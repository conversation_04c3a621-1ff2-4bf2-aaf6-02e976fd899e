"""
Generic Domain Implementation for New Industries

Provides a flexible, configurable domain implementation that can be
automatically instantiated for new industry applications without
requiring custom domain development.
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

from .interfaces import BaseDomain
from .feedback_types import ValidationResult, FeedbackType


class GenericDomain(BaseDomain):
    """
    Generic domain implementation that can be configured for any industry.
    
    Uses templates and configuration to provide domain-specific behavior
    without requiring custom implementation for each new industry.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize generic domain with industry-specific configuration.
        
        Args:
            config: Domain configuration including templates and thresholds
        """
        domain_name = config.get('domain_type', 'generic')
        super().__init__(domain_name)
        
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Extract configuration
        self.domain_name_display = config.get('domain_name', 'Generic Domain')
        self.description = config.get('description', 'Generic industry domain')
        self.feedback_categories = config.get('feedback_categories', ['correct', 'partial', 'incorrect'])
        self.confidence_thresholds = config.get('confidence_thresholds', {'high': 0.8, 'medium': 0.6, 'low': 0.4})
        self.safety_critical = config.get('safety_critical', False)
        self.time_sensitive = config.get('time_sensitive', False)
        self.regulatory_compliance = config.get('regulatory_compliance', [])
        
        # Processing statistics
        self.processing_stats = {
            'total_processed': 0,
            'by_category': {},
            'by_confidence_level': {'high': 0, 'medium': 0, 'low': 0},
            'average_processing_time': 0.0,
            'success_rate': 0.0
        }
        
        # Initialize components
        self.initialize_components()
    
    def initialize_components(self) -> None:
        """Initialize generic domain components."""
        try:
            # Create generic interpreter
            self.interpreter = GenericInterpreter(self.config)
            
            # Create generic matcher
            self.matcher = GenericMatcher(self.config)
            
            # Create generic validator
            self.validator = GenericValidator(self.config)
            
            self.logger.info(f"Initialized generic domain: {self.domain_name_display}")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize generic domain: {str(e)}")
            raise
    
    def process_output(self, raw_output: Any, context: Dict[str, Any]) -> ValidationResult:
        """
        Process output through generic domain pipeline.
        
        Args:
            raw_output: Raw input data
            context: Processing context
            
        Returns:
            ValidationResult with domain-specific analysis
        """
        try:
            start_time = datetime.utcnow()
            
            # Step 1: Interpret input data
            interpreted_output = self.interpreter.interpret(raw_output, context)
            
            # Step 2: Pattern matching
            match_results = self.matcher.match(interpreted_output, context)
            
            # Step 3: Validation
            validation_result = self.validator.validate(interpreted_output, match_results, context)
            
            # Add domain metadata
            validation_result.metadata.update({
                'domain': self.domain_name,
                'domain_display_name': self.domain_name_display,
                'domain_type': 'generic',
                'safety_critical': self.safety_critical,
                'time_sensitive': self.time_sensitive,
                'regulatory_compliance': self.regulatory_compliance,
                'processing_pipeline': ['interpret', 'match', 'validate']
            })
            
            # Update statistics
            processing_time = (datetime.utcnow() - start_time).total_seconds() * 1000
            self._update_processing_stats(validation_result, processing_time)
            
            return validation_result
            
        except Exception as e:
            self.logger.error(f"Error processing output in generic domain: {str(e)}")
            
            # Return error result
            error_result = ValidationResult()
            error_result.is_valid = False
            error_result.confidence_score = 0.0
            error_result.feedback_type = FeedbackType.INCORRECT
            error_result.issues = [{
                'type': 'domain_processing_error',
                'severity': 'critical',
                'message': f'Generic domain processing failed: {str(e)}',
                'details': {'error': str(e), 'domain': self.domain_name}
            }]
            error_result.metadata = {
                'domain': self.domain_name,
                'error': True,
                'error_message': str(e)
            }
            
            return error_result
    
    def _update_processing_stats(self, result: ValidationResult, processing_time: float) -> None:
        """Update processing statistics."""
        self.processing_stats['total_processed'] += 1
        
        # Update category stats
        category = result.feedback_type.value.lower()
        self.processing_stats['by_category'][category] = (
            self.processing_stats['by_category'].get(category, 0) + 1
        )
        
        # Update confidence level stats
        confidence = result.confidence_score
        if confidence >= self.confidence_thresholds['high']:
            level = 'high'
        elif confidence >= self.confidence_thresholds['medium']:
            level = 'medium'
        else:
            level = 'low'
        
        self.processing_stats['by_confidence_level'][level] += 1
        
        # Update average processing time
        total_time = self.processing_stats['average_processing_time'] * (self.processing_stats['total_processed'] - 1)
        self.processing_stats['average_processing_time'] = (total_time + processing_time) / self.processing_stats['total_processed']
        
        # Update success rate
        successful = sum(self.processing_stats['by_category'].get(cat, 0) 
                        for cat in ['correct', 'partially_correct'])
        self.processing_stats['success_rate'] = successful / self.processing_stats['total_processed']
    
    def get_domain_info(self) -> Dict[str, Any]:
        """Get comprehensive domain information."""
        base_info = super().get_domain_info()
        
        generic_info = {
            'domain_display_name': self.domain_name_display,
            'description': self.description,
            'feedback_categories': self.feedback_categories,
            'confidence_thresholds': self.confidence_thresholds,
            'safety_critical': self.safety_critical,
            'time_sensitive': self.time_sensitive,
            'regulatory_compliance': self.regulatory_compliance,
            'processing_stats': self.processing_stats.copy(),
            'capabilities': {
                'auto_generated': True,
                'configurable': True,
                'industry_agnostic': True,
                'template_based': True
            }
        }
        
        return {**base_info, **generic_info}


class GenericInterpreter:
    """Generic interpreter for any industry data."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
    
    def interpret(self, raw_output: Any, context: Dict[str, Any]) -> Dict[str, Any]:
        """Interpret raw output using generic patterns."""
        try:
            # Basic interpretation
            interpreted = {
                'timestamp': datetime.utcnow().isoformat(),
                'raw_data_type': type(raw_output).__name__,
                'context_keys': list(context.keys()) if isinstance(context, dict) else [],
                'classifications': [],
                'confidence_scores': {},
                'metadata': {
                    'interpreter': 'generic',
                    'domain_type': self.config.get('domain_type', 'unknown')
                }
            }
            
            # Extract classifications from raw data
            classifications = self._extract_classifications(raw_output, context)
            interpreted['classifications'] = classifications
            
            # Calculate confidence scores
            confidence_scores = self._calculate_confidence_scores(classifications, context)
            interpreted['confidence_scores'] = confidence_scores
            
            return interpreted
            
        except Exception as e:
            self.logger.error(f"Error in generic interpretation: {str(e)}")
            return {
                'error': True,
                'error_message': str(e),
                'timestamp': datetime.utcnow().isoformat()
            }
    
    def _extract_classifications(self, raw_output: Any, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract classifications from raw data."""
        classifications = []
        
        if isinstance(raw_output, dict):
            # Look for detection-like structures
            if 'detections' in raw_output:
                for i, detection in enumerate(raw_output['detections']):
                    classification = self._classify_detection(detection, i)
                    classifications.append(classification)
            else:
                # Treat entire dict as single classification
                classification = self._classify_detection(raw_output, 0)
                classifications.append(classification)
        
        elif isinstance(raw_output, list):
            for i, item in enumerate(raw_output):
                classification = self._classify_detection(item, i)
                classifications.append(classification)
        
        else:
            # Single item classification
            classification = self._classify_detection(raw_output, 0)
            classifications.append(classification)
        
        return classifications
    
    def _classify_detection(self, detection: Any, index: int) -> Dict[str, Any]:
        """Classify a single detection."""
        if isinstance(detection, dict):
            return {
                'index': index,
                'class': detection.get('class', detection.get('type', 'unknown')),
                'confidence': detection.get('confidence', detection.get('score', 0.5)),
                'category': detection.get('category', 'general'),
                'properties': detection.get('properties', {}),
                'meets_threshold': detection.get('confidence', 0.5) >= self.config.get('confidence_thresholds', {}).get('medium', 0.6)
            }
        else:
            return {
                'index': index,
                'class': str(detection),
                'confidence': 0.5,
                'category': 'general',
                'properties': {},
                'meets_threshold': False
            }
    
    def _calculate_confidence_scores(self, classifications: List[Dict[str, Any]], context: Dict[str, Any]) -> Dict[str, float]:
        """Calculate overall confidence scores."""
        if not classifications:
            return {'overall_confidence': 0.0}
        
        confidences = [c.get('confidence', 0.0) for c in classifications]
        overall_confidence = sum(confidences) / len(confidences)
        
        return {
            'overall_confidence': overall_confidence,
            'max_confidence': max(confidences),
            'min_confidence': min(confidences),
            'classification_count': len(classifications)
        }


class GenericMatcher:
    """Generic pattern matcher for any industry."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
    
    def match(self, interpreted_output: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Perform generic pattern matching."""
        try:
            match_results = {
                'overall_relevance': 0.0,
                'pattern_matches': [],
                'warnings': [],
                'metadata': {
                    'matcher': 'generic',
                    'match_timestamp': datetime.utcnow().isoformat()
                }
            }
            
            classifications = interpreted_output.get('classifications', [])
            
            # Basic pattern matching
            if classifications:
                # Check confidence patterns
                high_confidence_count = sum(1 for c in classifications 
                                          if c.get('confidence', 0) >= self.config.get('confidence_thresholds', {}).get('high', 0.8))
                
                if high_confidence_count > 0:
                    match_results['pattern_matches'].append({
                        'type': 'high_confidence_detections',
                        'count': high_confidence_count,
                        'confidence': 0.8,
                        'message': f'{high_confidence_count} high-confidence detections found'
                    })
                
                # Calculate overall relevance
                confidences = [c.get('confidence', 0) for c in classifications]
                match_results['overall_relevance'] = sum(confidences) / len(confidences)
            
            return match_results
            
        except Exception as e:
            self.logger.error(f"Error in generic pattern matching: {str(e)}")
            return {
                'overall_relevance': 0.0,
                'pattern_matches': [],
                'warnings': [{'type': 'matcher_error', 'message': str(e)}],
                'metadata': {'error': True}
            }


class GenericValidator:
    """Generic validator for any industry."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.feedback_categories = config.get('feedback_categories', ['correct', 'partial', 'incorrect'])
        self.confidence_thresholds = config.get('confidence_thresholds', {'high': 0.8, 'medium': 0.6, 'low': 0.4})
    
    def validate(self, interpreted_output: Dict[str, Any], 
                match_results: Dict[str, Any], 
                context: Dict[str, Any]) -> ValidationResult:
        """Perform generic validation."""
        try:
            result = ValidationResult()
            result.timestamp = datetime.utcnow()
            
            # Get overall confidence and relevance
            overall_confidence = interpreted_output.get('confidence_scores', {}).get('overall_confidence', 0.0)
            overall_relevance = match_results.get('overall_relevance', 0.0)
            
            # Determine feedback type based on thresholds
            if overall_confidence >= self.confidence_thresholds['high'] and overall_relevance >= 0.7:
                result.feedback_type = FeedbackType.CORRECT
                result.is_valid = True
                result.success_message = "High confidence validation successful"
            elif overall_confidence >= self.confidence_thresholds['medium'] or overall_relevance >= 0.5:
                result.feedback_type = FeedbackType.PARTIALLY_CORRECT
                result.is_valid = True
                result.success_message = "Partial validation successful"
            else:
                result.feedback_type = FeedbackType.INCORRECT
                result.is_valid = False
                result.success_message = "Validation unsuccessful"
            
            # Set confidence score
            result.confidence_score = (overall_confidence + overall_relevance) / 2
            
            # Add issues for low confidence
            if overall_confidence < self.confidence_thresholds['medium']:
                result.issues.append({
                    'type': 'low_confidence',
                    'severity': 'medium',
                    'message': f'Overall confidence below threshold: {overall_confidence:.2f}',
                    'details': {'confidence': overall_confidence, 'threshold': self.confidence_thresholds['medium']}
                })
            
            # Add safety-critical warnings
            if self.config.get('safety_critical', False) and result.confidence_score < 0.9:
                result.issues.append({
                    'type': 'safety_critical_low_confidence',
                    'severity': 'high',
                    'message': 'Low confidence in safety-critical domain',
                    'details': {'safety_critical': True, 'confidence': result.confidence_score}
                })
            
            # Add recommendations
            result.recommendations = self._generate_recommendations(result, interpreted_output, match_results)
            
            # Set metadata
            result.metadata = {
                'domain_type': self.config.get('domain_type', 'generic'),
                'validation_timestamp': datetime.utcnow().isoformat(),
                'validator': 'generic',
                'safety_critical': self.config.get('safety_critical', False),
                'regulatory_compliance': self.config.get('regulatory_compliance', [])
            }
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error in generic validation: {str(e)}")
            
            error_result = ValidationResult()
            error_result.is_valid = False
            error_result.confidence_score = 0.0
            error_result.feedback_type = FeedbackType.INCORRECT
            error_result.issues = [{
                'type': 'validation_error',
                'severity': 'critical',
                'message': f'Generic validation failed: {str(e)}',
                'details': {'error': str(e)}
            }]
            
            return error_result
    
    def _generate_recommendations(self, result: ValidationResult, 
                                interpreted_output: Dict[str, Any], 
                                match_results: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on validation results."""
        recommendations = []
        
        if result.feedback_type == FeedbackType.CORRECT:
            recommendations.append("Continue current approach - validation successful")
        elif result.feedback_type == FeedbackType.PARTIALLY_CORRECT:
            recommendations.append("Consider additional validation steps")
            if result.confidence_score < 0.7:
                recommendations.append("Increase data quality or collection parameters")
        else:
            recommendations.append("Review input data and processing parameters")
            recommendations.append("Consider alternative approaches or additional data sources")
        
        # Safety-critical recommendations
        if self.config.get('safety_critical', False):
            recommendations.append("Implement additional safety verification steps")
            recommendations.append("Consider human oversight for critical decisions")
        
        return recommendations

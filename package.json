{"name": "time-stamp-project-runner", "version": "1.0.0", "description": "Universal Project Runner & Validator for Time Stamp Project workspace", "main": "project-runner.js", "type": "module", "scripts": {"list": "node project-runner.js --list", "test": "node project-runner.js", "run": "node project-runner.js --run", "run:background": "node project-runner.js --run --background", "validate": "node project-runner.js --skip-tests", "ecostamp": "node project-runner.js --project ecostamp --run", "scanner": "node project-runner.js --project development-tools --run", "orchestrator": "node project-runner.js --project ai-orchestration --run", "help": "node project-runner.js --help", "setup": "npm install && node setup-workspace.js"}, "keywords": ["project-runner", "testing", "validation", "workspace", "automation"], "author": "<PERSON> (Solo Developer)", "license": "MIT", "dependencies": {"archiver": "^7.0.1", "chalk": "^5.3.0", "clsx": "^2.1.1", "concurrently": "^9.2.0", "fs-extra": "^11.2.0", "ora": "^8.0.1", "socket.io": "^4.8.1", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@types/jest": "^30.0.0", "@types/supertest": "^6.0.3", "jest": "^30.0.5", "nodemon": "^3.0.2", "supertest": "^7.1.4", "ts-jest": "^29.4.0"}, "engines": {"node": ">=16.0.0"}}
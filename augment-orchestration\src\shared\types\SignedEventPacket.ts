/**
 * Signed Event Packet Types and Interfaces
 * 
 * Core Gap 1: Signed Event Packets for code provenance and verification
 */

export enum EventType {
  CODE_GENERATION = 'CODE_GENERATION',
  CODE_MODIFICATION = 'CODE_MODIFICATION',
  TEST_EXECUTION = 'TEST_EXECUTION',
  MERGE_OPERATION = 'MERGE_OPERATION',
  DEPLOYMENT = 'DEPLOYMENT',
  ROLLBACK = 'ROLLBACK'
}

export interface TestMetrics {
  totalTests: number;
  passedTests: number;
  failedTests: number;
  coverage: number;
  executionTime: number;
  memoryUsage: number;
  cpuUsage: number;
  errorDetails?: string[];
  performanceMetrics?: {
    responseTime: number;
    throughput: number;
    errorRate: number;
  };
}

export interface AgentMetadata {
  agentId: string;
  agentName: string;
  vendor: string;
  version: string;
  capabilities: string[];
  roles: string[];
  fitnessScore: number;
  trustScore: number;
  contextWindow: number;
  modelParameters?: {
    temperature: number;
    maxTokens: number;
    topP: number;
  };
}

export interface CodeChange {
  filePath: string;
  changeType: 'CREATE' | 'MODIFY' | 'DELETE';
  linesAdded: number;
  linesRemoved: number;
  complexity: number;
  dependencies: string[];
}

export interface SignedEventPacketData {
  id: string;
  eventType: EventType;
  workflowId?: string;
  agentId?: string;
  codeHash: string;
  codeDiff?: string;
  testMetrics?: TestMetrics;
  agentMetadata: AgentMetadata;
  trustSignature: string;
  timestamp: Date;
  isVerified: boolean;
  verificationLog?: VerificationEntry[];
  codeChanges?: CodeChange[];
  environmentInfo?: {
    nodeVersion: string;
    platform: string;
    architecture: string;
    dependencies: Record<string, string>;
  };
}

export interface VerificationEntry {
  timestamp: Date;
  verifier: string;
  status: 'PENDING' | 'VERIFIED' | 'FAILED';
  signature: string;
  publicKey: string;
  algorithm: string;
  reason?: string;
}

export interface SignatureData {
  payload: string;
  signature: string;
  publicKey: string;
  algorithm: 'RSA-SHA256' | 'ECDSA-SHA256' | 'Ed25519';
  timestamp: Date;
}

export interface EventPacketQuery {
  eventType?: EventType;
  agentId?: string;
  workflowId?: string;
  dateFrom?: Date;
  dateTo?: Date;
  isVerified?: boolean;
  limit?: number;
  offset?: number;
}

export interface EventPacketStats {
  totalPackets: number;
  verifiedPackets: number;
  failedVerifications: number;
  byEventType: Record<EventType, number>;
  byAgent: Record<string, number>;
  averageVerificationTime: number;
  recentActivity: {
    last24Hours: number;
    lastWeek: number;
    lastMonth: number;
  };
}

// Cryptographic utilities interface
export interface CryptoService {
  generateKeyPair(): Promise<{ publicKey: string; privateKey: string }>;
  signData(data: string, privateKey: string): Promise<string>;
  verifySignature(data: string, signature: string, publicKey: string): Promise<boolean>;
  hashData(data: string): string;
  generateNonce(): string;
}

// Event packet creation request
export interface CreateEventPacketRequest {
  eventType: EventType;
  workflowId?: string;
  agentId?: string;
  codeContent: string;
  codeDiff?: string;
  testMetrics?: TestMetrics;
  agentMetadata: AgentMetadata;
  codeChanges?: CodeChange[];
  environmentInfo?: any;
}

// Event packet verification request
export interface VerifyEventPacketRequest {
  packetId: string;
  verifierPublicKey: string;
  expectedHash?: string;
  strictMode?: boolean;
}

export interface EventPacketResponse {
  success: boolean;
  packet?: SignedEventPacketData;
  error?: string;
  verificationStatus?: 'VERIFIED' | 'FAILED' | 'PENDING';
}

// Batch operations
export interface BatchEventPacketRequest {
  packets: CreateEventPacketRequest[];
  batchId: string;
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
}

export interface BatchEventPacketResponse {
  batchId: string;
  totalPackets: number;
  successfulPackets: number;
  failedPackets: number;
  packets: EventPacketResponse[];
  processingTime: number;
}

// Real-time event streaming
export interface EventPacketStream {
  subscribe(callback: (packet: SignedEventPacketData) => void): void;
  unsubscribe(): void;
  filter(criteria: EventPacketQuery): void;
}

// Integration with existing audit system
export interface AuditIntegration {
  logEventPacketCreation(packet: SignedEventPacketData): Promise<void>;
  logVerificationAttempt(packetId: string, result: boolean, verifier: string): Promise<void>;
  getAuditTrail(packetId: string): Promise<any[]>;
}

// Error types
export class EventPacketError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: any
  ) {
    super(message);
    this.name = 'EventPacketError';
  }
}

export class SignatureVerificationError extends EventPacketError {
  constructor(message: string, details?: any) {
    super(message, 'SIGNATURE_VERIFICATION_FAILED', details);
  }
}

export class HashMismatchError extends EventPacketError {
  constructor(message: string, details?: any) {
    super(message, 'HASH_MISMATCH', details);
  }
}

// Constants
export const EVENT_PACKET_CONSTANTS = {
  MAX_CODE_SIZE: 10 * 1024 * 1024, // 10MB
  MAX_DIFF_SIZE: 1 * 1024 * 1024,  // 1MB
  SIGNATURE_ALGORITHM: 'RSA-SHA256' as const,
  HASH_ALGORITHM: 'SHA256' as const,
  VERIFICATION_TIMEOUT: 30000, // 30 seconds
  BATCH_SIZE_LIMIT: 100,
  RETENTION_PERIOD_DAYS: 365
};

"""
Drone Validator for Drone AI Domain

Validates drone AI outputs and assigns feedback types including support
for 'Partially Correct' scenarios specific to drone operations.
"""

import logging
from typing import Dict, Any, List
from ...core.feedback_types import ValidationResult, FeedbackType, ValidationSeverity


class DroneValidator:
    """
    Validates drone AI outputs and determines appropriate feedback types.
    
    Handles various drone-specific scenarios including partial mission completion,
    sensor accuracy issues, and operational constraint violations.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.validation_criteria = self._initialize_criteria()
        
    def _initialize_criteria(self) -> Dict[str, Any]:
        """Initialize validation criteria for drone operations."""
        return {
            'correctness_thresholds': {
                'gps_accuracy': 5.0,        # meters
                'altitude_accuracy': 2.0,    # meters
                'heading_accuracy': 10.0,    # degrees
                'mission_completion': 0.95,  # 95% completion for "correct"
                'partial_completion': 0.70   # 70% completion for "partially correct"
            },
            'severity_mappings': {
                'critical_safety': ValidationSeverity.CRITICAL,
                'mission_failure': ValidationSeverity.HIGH,
                'accuracy_issue': ValidationSeverity.MEDIUM,
                'minor_deviation': ValidationSeverity.LOW,
                'informational': ValidationSeverity.INFO
            },
            'partial_correctness_rules': {
                'area_scanning': {
                    'min_coverage': 0.70,      # 70% area coverage for partial
                    'full_coverage': 0.95      # 95% area coverage for correct
                },
                'waypoint_navigation': {
                    'min_waypoints': 0.70,     # 70% waypoints reached for partial
                    'full_waypoints': 0.95     # 95% waypoints reached for correct
                },
                'sensor_data_quality': {
                    'min_quality': 0.60,       # 60% good readings for partial
                    'full_quality': 0.90       # 90% good readings for correct
                }
            }
        }
    
    def validate(self, 
                interpreted_output: Dict[str, Any], 
                match_results: Dict[str, Any], 
                context: Dict[str, Any]) -> ValidationResult:
        """
        Validate drone output and determine feedback type.
        
        Args:
            interpreted_output: Normalized output from interpreter
            match_results: Results from pattern matcher
            context: Additional validation context
            
        Returns:
            ValidationResult with feedback type and details
        """
        try:
            # Initialize validation result
            result = ValidationResult()
            result.metadata = {
                'validation_timestamp': match_results.get('metadata', {}).get('match_timestamp'),
                'validator_version': '1.0.0',
                'validation_type': 'drone_ai'
            }
            
            # Check for critical errors first
            if self._has_critical_errors(interpreted_output, match_results):
                result.feedback_type = FeedbackType.INCORRECT
                result.is_valid = False
                result.confidence_score = 0.0
                self._add_critical_error_issues(result, interpreted_output, match_results)
                return result
            
            # Determine feedback type based on validation criteria
            feedback_type, confidence = self._determine_feedback_type(
                interpreted_output, match_results, context
            )
            
            result.feedback_type = feedback_type
            result.confidence_score = confidence
            result.is_valid = feedback_type in [FeedbackType.CORRECT, FeedbackType.PARTIALLY_CORRECT]
            
            # Add detailed issues and recommendations
            self._analyze_issues(result, interpreted_output, match_results, context)
            self._generate_recommendations(result, interpreted_output, match_results, context)
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error in drone validation: {str(e)}")
            
            error_result = ValidationResult()
            error_result.feedback_type = FeedbackType.MISCELLANEOUS
            error_result.is_valid = False
            error_result.confidence_score = 0.0
            error_result.issues = [{
                'type': 'validation_error',
                'severity': ValidationSeverity.CRITICAL,
                'message': f'Validation failed: {str(e)}',
                'details': {'error': str(e)}
            }]
            
            return error_result
    
    def _has_critical_errors(self, interpreted_output: Dict[str, Any], match_results: Dict[str, Any]) -> bool:
        """Check for critical errors that immediately mark output as incorrect."""
        # Check for data interpretation errors
        if interpreted_output.get('error', False):
            return True
        
        # Check for critical anomalies
        for anomaly in match_results.get('anomalies', []):
            if anomaly.get('severity') == 'critical':
                return True
        
        # Check for safety violations
        safety_violations = [
            'altitude_out_of_bounds',
            'invalid_coordinates',
            'critical_system_failure'
        ]
        
        for anomaly in match_results.get('anomalies', []):
            if anomaly.get('type') in safety_violations:
                return True
        
        return False
    
    def _determine_feedback_type(self, 
                                interpreted_output: Dict[str, Any], 
                                match_results: Dict[str, Any], 
                                context: Dict[str, Any]) -> tuple[FeedbackType, float]:
        """Determine feedback type and confidence score."""
        
        # Start with base confidence from matcher
        base_confidence = match_results.get('overall_confidence', 0.5)
        
        # Check mission-specific completion criteria
        mission_type = context.get('mission_type', 'general')
        
        if mission_type == 'area_scanning':
            return self._validate_area_scanning(interpreted_output, match_results, context, base_confidence)
        elif mission_type == 'waypoint_navigation':
            return self._validate_waypoint_navigation(interpreted_output, match_results, context, base_confidence)
        elif mission_type == 'sensor_monitoring':
            return self._validate_sensor_monitoring(interpreted_output, match_results, context, base_confidence)
        else:
            return self._validate_general_operation(interpreted_output, match_results, context, base_confidence)
    
    def _validate_area_scanning(self, interpreted_output: Dict[str, Any], 
                               match_results: Dict[str, Any], 
                               context: Dict[str, Any], 
                               base_confidence: float) -> tuple[FeedbackType, float]:
        """Validate area scanning mission with partial correctness support."""
        rules = self.validation_criteria['partial_correctness_rules']['area_scanning']
        
        # Get area coverage from context or calculate from data
        area_coverage = context.get('area_coverage')
        if area_coverage is None:
            # Try to estimate from available data
            area_coverage = self._estimate_area_coverage(interpreted_output, context)
        
        if area_coverage is None:
            return FeedbackType.MISCELLANEOUS, base_confidence * 0.5
        
        # Determine feedback type based on coverage
        if area_coverage >= rules['full_coverage']:
            return FeedbackType.CORRECT, min(0.95, base_confidence + 0.1)
        elif area_coverage >= rules['min_coverage']:
            return FeedbackType.PARTIALLY_CORRECT, base_confidence * 0.8
        else:
            return FeedbackType.INCORRECT, base_confidence * 0.3
    
    def _validate_waypoint_navigation(self, interpreted_output: Dict[str, Any], 
                                     match_results: Dict[str, Any], 
                                     context: Dict[str, Any], 
                                     base_confidence: float) -> tuple[FeedbackType, float]:
        """Validate waypoint navigation with partial correctness support."""
        rules = self.validation_criteria['partial_correctness_rules']['waypoint_navigation']
        
        # Get waypoint completion from context
        waypoints_reached = context.get('waypoints_reached', 0)
        total_waypoints = context.get('total_waypoints', 1)
        
        completion_rate = waypoints_reached / total_waypoints if total_waypoints > 0 else 0
        
        # Check for navigation accuracy issues
        nav_warnings = [w for w in match_results.get('warnings', []) if w.get('type') == 'waypoint_deviation']
        accuracy_penalty = len(nav_warnings) * 0.05
        
        adjusted_confidence = base_confidence - accuracy_penalty
        
        if completion_rate >= rules['full_waypoints']:
            return FeedbackType.CORRECT, min(0.95, adjusted_confidence + 0.1)
        elif completion_rate >= rules['min_waypoints']:
            return FeedbackType.PARTIALLY_CORRECT, adjusted_confidence * 0.8
        else:
            return FeedbackType.INCORRECT, adjusted_confidence * 0.3
    
    def _validate_sensor_monitoring(self, interpreted_output: Dict[str, Any], 
                                   match_results: Dict[str, Any], 
                                   context: Dict[str, Any], 
                                   base_confidence: float) -> tuple[FeedbackType, float]:
        """Validate sensor monitoring with data quality assessment."""
        rules = self.validation_criteria['partial_correctness_rules']['sensor_data_quality']
        
        # Calculate data quality score
        quality_score = self._calculate_data_quality_score(interpreted_output, match_results)
        
        if quality_score >= rules['full_quality']:
            return FeedbackType.CORRECT, min(0.95, base_confidence + 0.1)
        elif quality_score >= rules['min_quality']:
            return FeedbackType.PARTIALLY_CORRECT, base_confidence * 0.8
        else:
            return FeedbackType.INCORRECT, base_confidence * 0.4
    
    def _validate_general_operation(self, interpreted_output: Dict[str, Any], 
                                   match_results: Dict[str, Any], 
                                   context: Dict[str, Any], 
                                   base_confidence: float) -> tuple[FeedbackType, float]:
        """Validate general drone operation."""
        # Count issues by severity
        high_severity_count = len([a for a in match_results.get('anomalies', []) if a.get('severity') == 'high'])
        warning_count = len(match_results.get('warnings', []))
        
        # Determine feedback type based on issue counts
        if high_severity_count == 0 and warning_count <= 1:
            return FeedbackType.CORRECT, base_confidence
        elif high_severity_count <= 1 and warning_count <= 3:
            return FeedbackType.PARTIALLY_CORRECT, base_confidence * 0.7
        else:
            return FeedbackType.INCORRECT, base_confidence * 0.4
    
    def _estimate_area_coverage(self, interpreted_output: Dict[str, Any], context: Dict[str, Any]) -> float:
        """Estimate area coverage from available GPS data."""
        # This is a simplified estimation - in practice, you'd use more sophisticated algorithms
        if 'latitude' in interpreted_output and 'longitude' in interpreted_output:
            # For now, return a placeholder based on GPS availability
            return 0.8  # 80% coverage estimate
        return None
    
    def _calculate_data_quality_score(self, interpreted_output: Dict[str, Any], match_results: Dict[str, Any]) -> float:
        """Calculate overall data quality score."""
        quality_score = 1.0
        
        # Penalize for missing critical data
        critical_fields = ['latitude', 'longitude', 'altitude']
        missing_fields = sum(1 for field in critical_fields if field not in interpreted_output)
        quality_score -= missing_fields * 0.2
        
        # Penalize for warnings and anomalies
        warning_count = len(match_results.get('warnings', []))
        anomaly_count = len(match_results.get('anomalies', []))
        
        quality_score -= warning_count * 0.05
        quality_score -= anomaly_count * 0.15
        
        return max(0.0, quality_score)
    
    def _add_critical_error_issues(self, result: ValidationResult, 
                                  interpreted_output: Dict[str, Any], 
                                  match_results: Dict[str, Any]) -> None:
        """Add critical error issues to validation result."""
        if interpreted_output.get('error', False):
            result.issues.append({
                'type': 'data_interpretation_error',
                'severity': ValidationSeverity.CRITICAL,
                'message': interpreted_output.get('error_message', 'Data interpretation failed'),
                'details': {'raw_error': interpreted_output.get('error_message')}
            })
        
        for anomaly in match_results.get('anomalies', []):
            if anomaly.get('severity') in ['critical', 'high']:
                result.issues.append({
                    'type': anomaly.get('type', 'unknown_anomaly'),
                    'severity': ValidationSeverity.CRITICAL if anomaly.get('severity') == 'critical' else ValidationSeverity.HIGH,
                    'message': anomaly.get('message', 'Critical anomaly detected'),
                    'details': anomaly
                })
    
    def _analyze_issues(self, result: ValidationResult, 
                       interpreted_output: Dict[str, Any], 
                       match_results: Dict[str, Any], 
                       context: Dict[str, Any]) -> None:
        """Analyze and categorize all issues found during validation."""
        # Add warnings as low-severity issues
        for warning in match_results.get('warnings', []):
            result.issues.append({
                'type': warning.get('type', 'unknown_warning'),
                'severity': ValidationSeverity.LOW,
                'message': warning.get('message', 'Warning detected'),
                'details': warning
            })
        
        # Add medium-severity anomalies
        for anomaly in match_results.get('anomalies', []):
            if anomaly.get('severity') == 'medium':
                result.issues.append({
                    'type': anomaly.get('type', 'unknown_anomaly'),
                    'severity': ValidationSeverity.MEDIUM,
                    'message': anomaly.get('message', 'Anomaly detected'),
                    'details': anomaly
                })
    
    def _generate_recommendations(self, result: ValidationResult, 
                                 interpreted_output: Dict[str, Any], 
                                 match_results: Dict[str, Any], 
                                 context: Dict[str, Any]) -> None:
        """Generate recommendations based on validation results."""
        if result.feedback_type == FeedbackType.INCORRECT:
            result.recommendations.append("Review mission parameters and retry operation")
            result.recommendations.append("Check sensor calibration and GPS accuracy")
        
        elif result.feedback_type == FeedbackType.PARTIALLY_CORRECT:
            result.recommendations.append("Mission partially completed - consider extending operation")
            result.recommendations.append("Review areas of concern identified in validation")
        
        # Add specific recommendations based on issues
        gps_issues = [i for i in result.issues if 'gps' in i.get('type', '').lower()]
        if gps_issues:
            result.recommendations.append("Improve GPS signal quality or wait for better conditions")
        
        wind_issues = [i for i in result.issues if 'wind' in i.get('type', '').lower()]
        if wind_issues:
            result.recommendations.append("Consider postponing mission due to adverse wind conditions")
    
    def supports_partial_correctness(self) -> bool:
        """Check if validator supports 'Partially Correct' feedback type."""
        return True
    
    def get_validation_criteria(self) -> Dict[str, Any]:
        """Get the validation criteria used by this validator."""
        return self.validation_criteria.copy()

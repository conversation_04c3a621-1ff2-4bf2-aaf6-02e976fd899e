/**
 * Provenance Ledger Types and Interfaces
 * 
 * Core Gap 2: Immutable Provenance Ledger for blockchain-style verification
 * of all code generation, modification, testing, and merging actions.
 */

export enum ProvenanceAction {
  CREATE = 'CREATE',
  MODIFY = 'MODIFY',
  TEST = 'TEST',
  MERGE = 'MERGE',
  DEPLOY = 'DEPLOY',
  ROLLBACK = 'ROLLBACK',
  DELETE = 'DELETE',
  CONFIGURE = 'CONFIGURE'
}

export enum EntityType {
  CODE = 'CODE',
  WORKFLOW = 'WORKFLOW',
  AGENT = 'AGENT',
  CONFIGURATION = 'CONFIGURATION',
  TEST_SUITE = 'TEST_SUITE',
  DEPLOYMENT = 'DEPLOYMENT',
  DATABASE = 'DATABASE',
  ENVIRONMENT = 'ENVIRONMENT'
}

export interface EntitySnapshot {
  id: string;
  type: EntityType;
  name: string;
  version: string;
  content?: string;
  metadata: Record<string, any>;
  dependencies: string[];
  checksum: string;
  timestamp: Date;
}

export interface ProvenanceEntryData {
  id: string;
  blockHash: string;
  previousHash: string | null;
  action: ProvenanceAction;
  entityType: EntityType;
  entityId: string;
  beforeState: EntitySnapshot | null;
  afterState: EntitySnapshot | null;
  changeMetadata: {
    reason: string;
    description: string;
    tags: string[];
    impact: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    reviewRequired: boolean;
    automatedChange: boolean;
    changeSize: number;
    affectedFiles: string[];
    testResults?: {
      passed: number;
      failed: number;
      coverage: number;
    };
  };
  performedBy: string;
  performedByType: 'USER' | 'AGENT' | 'SYSTEM';
  timestamp: Date;
  isImmutable: boolean;
  verificationHash: string;
  linkedEventPacketId?: string;
}

export interface ProvenanceChain {
  entries: ProvenanceEntryData[];
  isValid: boolean;
  totalEntries: number;
  firstEntry: Date;
  lastEntry: Date;
  integrityScore: number;
}

export interface ProvenanceQuery {
  entityId?: string;
  entityType?: EntityType;
  action?: ProvenanceAction;
  performedBy?: string;
  dateFrom?: Date;
  dateTo?: Date;
  includeSnapshots?: boolean;
  verifyIntegrity?: boolean;
  limit?: number;
  offset?: number;
}

export interface ProvenanceStats {
  totalEntries: number;
  entriesByAction: Record<ProvenanceAction, number>;
  entriesByEntityType: Record<EntityType, number>;
  entriesByPerformer: Record<string, number>;
  integrityStatus: {
    validEntries: number;
    invalidEntries: number;
    corruptedChains: number;
    lastVerification: Date;
  };
  recentActivity: {
    last24Hours: number;
    lastWeek: number;
    lastMonth: number;
  };
  chainMetrics: {
    longestChain: number;
    averageChainLength: number;
    totalChains: number;
  };
}

export interface CreateProvenanceEntryRequest {
  action: ProvenanceAction;
  entityType: EntityType;
  entityId: string;
  beforeState?: EntitySnapshot;
  afterState?: EntitySnapshot;
  changeMetadata: {
    reason: string;
    description: string;
    tags?: string[];
    impact?: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    reviewRequired?: boolean;
    automatedChange?: boolean;
    affectedFiles?: string[];
    testResults?: any;
  };
  performedBy: string;
  performedByType?: 'USER' | 'AGENT' | 'SYSTEM';
  linkedEventPacketId?: string;
}

export interface VerifyProvenanceChainRequest {
  entityId?: string;
  startDate?: Date;
  endDate?: Date;
  deepVerification?: boolean;
  repairCorruption?: boolean;
}

export interface ProvenanceChainVerificationResult {
  isValid: boolean;
  totalEntries: number;
  validEntries: number;
  invalidEntries: number;
  corruptedEntries: ProvenanceEntryData[];
  integrityScore: number;
  verificationTime: number;
  recommendations: string[];
}

export interface ProvenanceDiff {
  entryId: string;
  field: string;
  beforeValue: any;
  afterValue: any;
  changeType: 'ADDED' | 'MODIFIED' | 'REMOVED';
  impact: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
}

export interface ProvenanceAuditReport {
  reportId: string;
  generatedAt: Date;
  timeRange: {
    from: Date;
    to: Date;
  };
  summary: {
    totalChanges: number;
    criticalChanges: number;
    unauthorizedChanges: number;
    failedVerifications: number;
  };
  entries: ProvenanceEntryData[];
  integrityReport: ProvenanceChainVerificationResult;
  recommendations: string[];
  complianceStatus: 'COMPLIANT' | 'NON_COMPLIANT' | 'NEEDS_REVIEW';
}

// Blockchain-style verification interfaces
export interface BlockchainVerification {
  verifyChainIntegrity(entries: ProvenanceEntryData[]): Promise<boolean>;
  calculateBlockHash(entry: ProvenanceEntryData): string;
  validateHashChain(entries: ProvenanceEntryData[]): boolean;
  repairChainIntegrity(entries: ProvenanceEntryData[]): Promise<ProvenanceEntryData[]>;
}

// Immutability enforcement
export interface ImmutabilityGuard {
  enforceImmutability(entry: ProvenanceEntryData): boolean;
  detectTampering(entry: ProvenanceEntryData): boolean;
  createImmutableRecord(data: any): string;
  verifyImmutableRecord(data: any, hash: string): boolean;
}

// Export utilities
export interface ProvenanceExport {
  format: 'JSON' | 'CSV' | 'XML' | 'BLOCKCHAIN';
  includeSnapshots: boolean;
  includeMetadata: boolean;
  compressionEnabled: boolean;
  encryptionEnabled: boolean;
  signatureRequired: boolean;
}

export interface ProvenanceImport {
  format: 'JSON' | 'CSV' | 'XML' | 'BLOCKCHAIN';
  validateIntegrity: boolean;
  mergeStrategy: 'APPEND' | 'MERGE' | 'REPLACE';
  conflictResolution: 'SKIP' | 'OVERWRITE' | 'MANUAL';
}

// Real-time monitoring
export interface ProvenanceMonitor {
  subscribe(callback: (entry: ProvenanceEntryData) => void): void;
  unsubscribe(): void;
  filterByEntity(entityId: string): void;
  filterByAction(action: ProvenanceAction): void;
  alertOnCriticalChanges(enabled: boolean): void;
}

// Error types
export class ProvenanceError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: any
  ) {
    super(message);
    this.name = 'ProvenanceError';
  }
}

export class ChainIntegrityError extends ProvenanceError {
  constructor(message: string, details?: any) {
    super(message, 'CHAIN_INTEGRITY_ERROR', details);
  }
}

export class ImmutabilityViolationError extends ProvenanceError {
  constructor(message: string, details?: any) {
    super(message, 'IMMUTABILITY_VIOLATION', details);
  }
}

export class HashVerificationError extends ProvenanceError {
  constructor(message: string, details?: any) {
    super(message, 'HASH_VERIFICATION_ERROR', details);
  }
}

// Constants
export const PROVENANCE_CONSTANTS = {
  MAX_SNAPSHOT_SIZE: 50 * 1024 * 1024, // 50MB
  MAX_METADATA_SIZE: 1 * 1024 * 1024,  // 1MB
  HASH_ALGORITHM: 'SHA256' as const,
  BLOCK_SIZE_LIMIT: 1000, // entries per block
  VERIFICATION_INTERVAL: 24 * 60 * 60 * 1000, // 24 hours
  RETENTION_PERIOD_DAYS: 2555, // 7 years
  COMPRESSION_THRESHOLD: 10 * 1024, // 10KB
  BACKUP_FREQUENCY: 60 * 60 * 1000 // 1 hour
};

// Utility functions
export const ProvenanceUtils = {
  generateEntityId: (): string => {
    return `entity_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  },

  calculateChangeSize: (beforeState?: EntitySnapshot, afterState?: EntitySnapshot): number => {
    if (!beforeState && afterState) return afterState.content?.length || 0;
    if (beforeState && !afterState) return beforeState.content?.length || 0;
    if (!beforeState || !afterState) return 0;

    const beforeSize = beforeState.content?.length || 0;
    const afterSize = afterState.content?.length || 0;
    return Math.abs(afterSize - beforeSize);
  },

  determineImpact: (changeSize: number, entityType: EntityType): 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' => {
    if (entityType === EntityType.CONFIGURATION || entityType === EntityType.DATABASE) {
      return changeSize > 1000 ? 'CRITICAL' : changeSize > 100 ? 'HIGH' : 'MEDIUM';
    }
    
    if (changeSize > 10000) return 'CRITICAL';
    if (changeSize > 1000) return 'HIGH';
    if (changeSize > 100) return 'MEDIUM';
    return 'LOW';
  },

  validateEntitySnapshot: (snapshot: EntitySnapshot): boolean => {
    return !!(
      snapshot.id &&
      snapshot.type &&
      snapshot.name &&
      snapshot.version &&
      snapshot.checksum &&
      snapshot.timestamp
    );
  }
};

import React, { useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  LinearProgress,
} from '@mui/material'
import {
  AccountTree as TreeIcon,
  Psychology as AgentIcon,
  Timeline as WorkflowIcon,
  TunnelIcon,
  EvolutionIcon,
  AuditIcon,
} from '@mui/icons-material'
import { useAppDispatch, useAppSelector } from '../hooks/redux'
import { fetchOrchestrators } from '../store/slices/orchestratorSlice'
import { fetchAgents } from '../store/slices/agentSlice'

const Dashboard: React.FC = () => {
  const navigate = useNavigate()
  const dispatch = useAppDispatch()
  const { user } = useAppSelector((state) => state.auth)
  const { isConnected, connectionCount } = useAppSelector((state) => state.socket)
  const { orchestrators } = useAppSelector((state) => state.orchestrator)
  const { agents } = useAppSelector((state) => state.agent)

  useEffect(() => {
    dispatch(fetchOrchestrators())
    dispatch(fetchAgents())
  }, [dispatch])

  const dashboardCards = [
    {
      title: 'Family Tree',
      description: 'Visualize orchestrator hierarchy and agent relationships',
      icon: <TreeIcon sx={{ fontSize: 40 }} />,
      path: '/family-tree',
      color: '#667eea',
      stats: `${orchestrators.length} Orchestrators`,
    },
    {
      title: 'Agents',
      description: 'Manage AI agents and role assignments',
      icon: <AgentIcon sx={{ fontSize: 40 }} />,
      path: '/agents',
      color: '#764ba2',
      stats: `${agents.length} Active Agents`,
    },
    {
      title: 'Workflows',
      description: 'Create and execute multi-agent workflows',
      icon: <WorkflowIcon sx={{ fontSize: 40 }} />,
      path: '/workflows',
      color: '#f093fb',
      stats: 'Templates & Executions',
    },
    {
      title: 'Tunnels',
      description: 'Cross-domain communication channels',
      icon: <TunnelIcon sx={{ fontSize: 40 }} />,
      path: '/tunnels',
      color: '#4facfe',
      stats: 'Bidirectional Links',
    },
    {
      title: 'Evolution',
      description: 'Darwin Gödel Machine optimization',
      icon: <EvolutionIcon sx={{ fontSize: 40 }} />,
      path: '/evolution',
      color: '#43e97b',
      stats: 'Agent Mutations',
    },
    {
      title: 'Audit Logs',
      description: 'System activity and compliance tracking',
      icon: <AuditIcon sx={{ fontSize: 40 }} />,
      path: '/audit',
      color: '#fa709a',
      stats: 'Activity History',
    },
  ]

  return (
    <Box>
      {/* Header */}
      <Box mb={4}>
        <Typography variant="h4" gutterBottom>
          Welcome back, {user?.username}! 👋
        </Typography>
        <Typography variant="body1" color="text.secondary" gutterBottom>
          Unified AI Orchestration & Family Tree Visualization Platform
        </Typography>
        
        <Box display="flex" gap={2} mt={2}>
          <Chip
            label={isConnected ? 'Connected' : 'Disconnected'}
            color={isConnected ? 'success' : 'error'}
            variant="outlined"
          />
          <Chip
            label={`${connectionCount} Active Users`}
            color="info"
            variant="outlined"
          />
          <Chip
            label={`Role: ${user?.role}`}
            color="primary"
            variant="outlined"
          />
        </Box>
      </Box>

      {/* System Status */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            System Status
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <Typography variant="body2" color="text.secondary">
                Meta-Orchestrators
              </Typography>
              <Typography variant="h4">
                {orchestrators.length}
              </Typography>
            </Grid>
            <Grid item xs={12} md={4}>
              <Typography variant="body2" color="text.secondary">
                Active Agents
              </Typography>
              <Typography variant="h4">
                {agents.filter(a => a.isActive).length}
              </Typography>
            </Grid>
            <Grid item xs={12} md={4}>
              <Typography variant="body2" color="text.secondary">
                System Health
              </Typography>
              <Box display="flex" alignItems="center" gap={1}>
                <LinearProgress
                  variant="determinate"
                  value={85}
                  sx={{ flexGrow: 1, height: 8, borderRadius: 4 }}
                />
                <Typography variant="body2">85%</Typography>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Dashboard Cards */}
      <Grid container spacing={3}>
        {dashboardCards.map((card, index) => (
          <Grid item xs={12} sm={6} md={4} key={index}>
            <Card
              sx={{
                height: '100%',
                cursor: 'pointer',
                transition: 'transform 0.2s, box-shadow 0.2s',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: 4,
                },
              }}
              onClick={() => navigate(card.path)}
            >
              <CardContent sx={{ p: 3 }}>
                <Box
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                  sx={{
                    width: 80,
                    height: 80,
                    borderRadius: 2,
                    background: `linear-gradient(135deg, ${card.color}20, ${card.color}40)`,
                    color: card.color,
                    mb: 2,
                  }}
                >
                  {card.icon}
                </Box>
                <Typography variant="h6" gutterBottom>
                  {card.title}
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  {card.description}
                </Typography>
                <Chip
                  label={card.stats}
                  size="small"
                  sx={{
                    backgroundColor: `${card.color}20`,
                    color: card.color,
                  }}
                />
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Box>
  )
}

export default Dashboard

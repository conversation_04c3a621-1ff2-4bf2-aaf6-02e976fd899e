/**
 * Metrics Collector for DGM Evaluation
 * 
 * Collects and analyzes performance metrics for orchestration agents:
 * - Execution performance metrics
 * - Resource usage tracking
 * - Quality metrics
 * - Historical trend analysis
 */

const { performance } = require('perf_hooks');
const os = require('os');
const chalk = require('chalk');

class MetricsCollector {
  constructor(config) {
    this.config = config;
    this.metricsHistory = new Map();
    this.performanceBaselines = new Map();
    this.isCollecting = false;
  }

  /**
   * Collect comprehensive metrics for an agent
   */
  async collectMetrics(agent, benchmarkResults) {
    console.log(chalk.blue(`📊 Collecting metrics for agent ${agent.id}...`));
    
    const metrics = {
      agentId: agent.id,
      timestamp: new Date(),
      performance: await this.collectPerformanceMetrics(agent, benchmarkResults),
      reliability: await this.collectReliabilityMetrics(agent, benchmarkResults),
      functionality: await this.collectFunctionalityMetrics(agent, benchmarkResults),
      safety: await this.collectSafetyMetrics(agent, benchmarkResults),
      resource: await this.collectResourceMetrics(agent),
      quality: await this.collectQualityMetrics(agent),
      evolution: await this.collectEvolutionMetrics(agent)
    };

    // Store metrics in history
    if (!this.metricsHistory.has(agent.id)) {
      this.metricsHistory.set(agent.id, []);
    }
    this.metricsHistory.get(agent.id).push(metrics);

    // Calculate aggregate scores
    const aggregateMetrics = this.calculateAggregateMetrics(metrics);
    
    console.log(chalk.green(`✅ Metrics collected for agent ${agent.id}`));
    return aggregateMetrics;
  }

  /**
   * Collect performance metrics
   */
  async collectPerformanceMetrics(agent, benchmarkResults) {
    const performanceMetrics = {
      executionTime: 0,
      throughput: 0,
      latency: 0,
      efficiency: 0,
      benchmarkScore: 0,
      speedIndex: 0
    };

    if (benchmarkResults) {
      // Calculate execution time from benchmark results
      performanceMetrics.executionTime = benchmarkResults.executionTime || 0;
      performanceMetrics.benchmarkScore = benchmarkResults.averageScore || 0;
      
      // Calculate throughput (operations per second)
      if (benchmarkResults.executionTime > 0) {
        performanceMetrics.throughput = (benchmarkResults.totalTests / benchmarkResults.executionTime) * 1000;
      }
      
      // Calculate latency (average time per operation)
      if (benchmarkResults.totalTests > 0) {
        performanceMetrics.latency = benchmarkResults.executionTime / benchmarkResults.totalTests;
      }
      
      // Calculate efficiency (success rate weighted by speed)
      const successRate = benchmarkResults.totalTests > 0 ? 
        benchmarkResults.passedTests / benchmarkResults.totalTests : 0;
      const speedFactor = Math.max(0, 1 - (benchmarkResults.executionTime / 10000)); // Normalize to 10s
      performanceMetrics.efficiency = successRate * speedFactor;
      
      // Calculate speed index (lower is better, normalized to 0-1 scale)
      performanceMetrics.speedIndex = Math.max(0, 1 - (benchmarkResults.executionTime / 5000)); // Normalize to 5s
    }

    return performanceMetrics;
  }

  /**
   * Collect reliability metrics
   */
  async collectReliabilityMetrics(agent, benchmarkResults) {
    const reliabilityMetrics = {
      errorRate: 0,
      successRate: 0,
      stability: 0,
      robustness: 0,
      faultTolerance: 0,
      consistency: 0
    };

    if (benchmarkResults) {
      // Calculate error rate
      reliabilityMetrics.errorRate = benchmarkResults.totalTests > 0 ? 
        benchmarkResults.failedTests / benchmarkResults.totalTests : 0;
      
      // Calculate success rate
      reliabilityMetrics.successRate = 1 - reliabilityMetrics.errorRate;
      
      // Calculate stability (consistency across test runs)
      const testScores = Object.values(benchmarkResults.benchmarkResults || {})
        .map(result => result.score);
      if (testScores.length > 1) {
        const mean = testScores.reduce((sum, score) => sum + score, 0) / testScores.length;
        const variance = testScores.reduce((sum, score) => sum + Math.pow(score - mean, 2), 0) / testScores.length;
        reliabilityMetrics.stability = Math.max(0, 1 - Math.sqrt(variance));
      } else {
        reliabilityMetrics.stability = reliabilityMetrics.successRate;
      }
      
      // Calculate robustness (performance under stress)
      const errorTests = Object.values(benchmarkResults.benchmarkResults || {})
        .filter(result => result.name.includes('error') || result.name.includes('stress'));
      if (errorTests.length > 0) {
        reliabilityMetrics.robustness = errorTests.reduce((sum, test) => sum + test.score, 0) / errorTests.length;
      } else {
        reliabilityMetrics.robustness = reliabilityMetrics.successRate;
      }
      
      // Calculate fault tolerance
      reliabilityMetrics.faultTolerance = this.calculateFaultTolerance(benchmarkResults);
      
      // Calculate consistency
      reliabilityMetrics.consistency = reliabilityMetrics.stability;
    }

    return reliabilityMetrics;
  }

  /**
   * Collect functionality metrics
   */
  async collectFunctionalityMetrics(agent, benchmarkResults) {
    const functionalityMetrics = {
      featureCompleteness: 0,
      capabilityScore: 0,
      workflowCoverage: 0,
      toolIntegration: 0,
      adaptability: 0,
      extensibility: 0
    };

    if (benchmarkResults) {
      // Calculate feature completeness
      functionalityMetrics.featureCompleteness = benchmarkResults.featureCompleteness || 0;
      
      // Calculate capability score based on successful test categories
      const capabilityTests = Object.values(benchmarkResults.benchmarkResults || {});
      if (capabilityTests.length > 0) {
        functionalityMetrics.capabilityScore = capabilityTests.reduce((sum, test) => sum + test.score, 0) / capabilityTests.length;
      }
      
      // Calculate workflow coverage
      functionalityMetrics.workflowCoverage = this.calculateWorkflowCoverage(agent, benchmarkResults);
      
      // Calculate tool integration score
      functionalityMetrics.toolIntegration = this.calculateToolIntegration(agent, benchmarkResults);
      
      // Calculate adaptability (ability to handle different task types)
      functionalityMetrics.adaptability = this.calculateAdaptability(benchmarkResults);
      
      // Calculate extensibility (code structure quality)
      functionalityMetrics.extensibility = this.calculateExtensibility(agent);
    }

    return functionalityMetrics;
  }

  /**
   * Collect safety metrics
   */
  async collectSafetyMetrics(agent, benchmarkResults) {
    const safetyMetrics = {
      securityScore: 0,
      validationScore: 0,
      errorHandling: 0,
      inputSanitization: 0,
      resourceLimits: 0,
      codeQuality: 0
    };

    // Analyze code for safety patterns
    safetyMetrics.securityScore = this.analyzeSecurityPatterns(agent.code);
    safetyMetrics.validationScore = this.analyzeValidationPatterns(agent.code);
    safetyMetrics.errorHandling = this.analyzeErrorHandling(agent.code);
    safetyMetrics.inputSanitization = this.analyzeInputSanitization(agent.code);
    safetyMetrics.resourceLimits = this.analyzeResourceLimits(agent.code);
    safetyMetrics.codeQuality = this.analyzeCodeQuality(agent.code);

    return safetyMetrics;
  }

  /**
   * Collect resource usage metrics
   */
  async collectResourceMetrics(agent) {
    const resourceMetrics = {
      memoryUsage: 0,
      cpuUsage: 0,
      diskUsage: 0,
      networkUsage: 0,
      codeSize: 0,
      complexity: 0
    };

    // Get system resource usage
    const memUsage = process.memoryUsage();
    resourceMetrics.memoryUsage = memUsage.heapUsed / (1024 * 1024); // MB
    
    // Calculate code size
    resourceMetrics.codeSize = this.calculateCodeSize(agent.code);
    
    // Calculate code complexity
    resourceMetrics.complexity = this.calculateCodeComplexity(agent.code);
    
    // Get CPU usage (simplified)
    const cpuUsage = process.cpuUsage();
    resourceMetrics.cpuUsage = (cpuUsage.user + cpuUsage.system) / 1000000; // Convert to seconds

    return resourceMetrics;
  }

  /**
   * Collect code quality metrics
   */
  async collectQualityMetrics(agent) {
    const qualityMetrics = {
      maintainability: 0,
      readability: 0,
      testability: 0,
      modularity: 0,
      documentation: 0,
      bestPractices: 0
    };

    qualityMetrics.maintainability = this.analyzeMaintainability(agent.code);
    qualityMetrics.readability = this.analyzeReadability(agent.code);
    qualityMetrics.testability = this.analyzeTestability(agent.code);
    qualityMetrics.modularity = this.analyzeModularity(agent.code);
    qualityMetrics.documentation = this.analyzeDocumentation(agent.code);
    qualityMetrics.bestPractices = this.analyzeBestPractices(agent.code);

    return qualityMetrics;
  }

  /**
   * Collect evolution-specific metrics
   */
  async collectEvolutionMetrics(agent) {
    const evolutionMetrics = {
      generationProgress: 0,
      parentSimilarity: 0,
      novelty: 0,
      diversity: 0,
      fitnessImprovement: 0,
      lineageStrength: 0
    };

    evolutionMetrics.generationProgress = agent.generation / 100; // Normalize to 0-1
    evolutionMetrics.novelty = this.calculateNovelty(agent);
    evolutionMetrics.diversity = this.calculateDiversity(agent);
    evolutionMetrics.fitnessImprovement = this.calculateFitnessImprovement(agent);
    evolutionMetrics.lineageStrength = this.calculateLineageStrength(agent);

    return evolutionMetrics;
  }

  /**
   * Calculate aggregate metrics from all collected metrics
   */
  calculateAggregateMetrics(metrics) {
    const weights = this.config.get('evaluation.fitnessWeights', {
      performance: 0.4,
      reliability: 0.3,
      functionality: 0.2,
      safety: 0.1
    });

    // Calculate component scores
    const performanceScore = this.calculateComponentScore(metrics.performance);
    const reliabilityScore = this.calculateComponentScore(metrics.reliability);
    const functionalityScore = this.calculateComponentScore(metrics.functionality);
    const safetyScore = this.calculateComponentScore(metrics.safety);

    // Calculate weighted overall score
    const overallScore = (
      weights.performance * performanceScore +
      weights.reliability * reliabilityScore +
      weights.functionality * functionalityScore +
      weights.safety * safetyScore
    );

    return {
      performance: performanceScore,
      reliability: reliabilityScore,
      functionality: functionalityScore,
      safety: safetyScore,
      overall: overallScore,
      timestamp: metrics.timestamp,
      details: metrics
    };
  }

  /**
   * Calculate component score from metrics object
   */
  calculateComponentScore(componentMetrics) {
    const values = Object.values(componentMetrics).filter(val => typeof val === 'number');
    return values.length > 0 ? values.reduce((sum, val) => sum + val, 0) / values.length : 0;
  }

  /**
   * Helper methods for specific metric calculations
   */
  calculateFaultTolerance(benchmarkResults) {
    // Look for tests that specifically test error conditions
    const faultTests = Object.values(benchmarkResults.benchmarkResults || {})
      .filter(result => result.name.includes('fault') || result.name.includes('error'));
    
    if (faultTests.length > 0) {
      return faultTests.reduce((sum, test) => sum + test.score, 0) / faultTests.length;
    }
    
    // Fallback: use overall success rate
    return benchmarkResults.totalTests > 0 ? 
      benchmarkResults.passedTests / benchmarkResults.totalTests : 0;
  }

  calculateWorkflowCoverage(agent, benchmarkResults) {
    // Count how many different workflow types were successfully executed
    const workflowTypes = ['analysis', 'generation', 'refactoring', 'testing'];
    let coveredWorkflows = 0;
    
    for (const type of workflowTypes) {
      const typeTests = Object.values(benchmarkResults.benchmarkResults || {})
        .filter(result => result.name.toLowerCase().includes(type));
      
      if (typeTests.some(test => test.score > 0.5)) {
        coveredWorkflows++;
      }
    }
    
    return coveredWorkflows / workflowTypes.length;
  }

  calculateToolIntegration(agent, benchmarkResults) {
    // Analyze how well the agent integrates with different tools
    const expectedTools = ['augmentCode', 'cursor', 'windsurf', 'tabnine'];
    let toolScore = 0;
    
    // This would analyze the agent's code to see if it properly uses tools
    // For now, return a simplified score
    return 0.8; // Placeholder
  }

  calculateAdaptability(benchmarkResults) {
    // Measure performance consistency across different task types
    const testTypes = Object.values(benchmarkResults.benchmarkResults || {});
    if (testTypes.length < 2) return 1.0;
    
    const scores = testTypes.map(test => test.score);
    const mean = scores.reduce((sum, score) => sum + score, 0) / scores.length;
    const variance = scores.reduce((sum, score) => sum + Math.pow(score - mean, 2), 0) / scores.length;
    
    // Lower variance = higher adaptability
    return Math.max(0, 1 - Math.sqrt(variance));
  }

  calculateExtensibility(agent) {
    // Analyze code structure for extensibility patterns
    const code = agent.code.orchestrator + agent.code.crossFlow;
    
    let score = 0;
    
    // Check for modular patterns
    if (code.includes('class ') || code.includes('module.exports')) score += 0.2;
    if (code.includes('async ') || code.includes('await ')) score += 0.2;
    if (code.includes('try {') || code.includes('catch (')) score += 0.2;
    if (code.includes('/**') || code.includes('//')) score += 0.2;
    if (code.includes('config') || code.includes('options')) score += 0.2;
    
    return Math.min(1.0, score);
  }

  // Security and safety analysis methods
  analyzeSecurityPatterns(code) {
    const codeStr = JSON.stringify(code);
    let score = 1.0;
    
    // Deduct points for potential security issues
    if (codeStr.includes('eval(')) score -= 0.3;
    if (codeStr.includes('innerHTML')) score -= 0.2;
    if (codeStr.includes('document.write')) score -= 0.2;
    
    return Math.max(0, score);
  }

  analyzeValidationPatterns(code) {
    const codeStr = JSON.stringify(code);
    let score = 0;
    
    // Add points for validation patterns
    if (codeStr.includes('validate') || codeStr.includes('check')) score += 0.3;
    if (codeStr.includes('typeof') || codeStr.includes('instanceof')) score += 0.2;
    if (codeStr.includes('Array.isArray')) score += 0.2;
    if (codeStr.includes('throw new Error')) score += 0.3;
    
    return Math.min(1.0, score);
  }

  analyzeErrorHandling(code) {
    const codeStr = JSON.stringify(code);
    let score = 0;
    
    // Count error handling patterns
    const tryCount = (codeStr.match(/try\s*{/g) || []).length;
    const catchCount = (codeStr.match(/catch\s*\(/g) || []).length;
    const errorCount = (codeStr.match(/Error\(/g) || []).length;
    
    score = Math.min(1.0, (tryCount + catchCount + errorCount) / 10);
    
    return score;
  }

  analyzeInputSanitization(code) {
    const codeStr = JSON.stringify(code);
    let score = 0;
    
    // Look for sanitization patterns
    if (codeStr.includes('sanitize') || codeStr.includes('escape')) score += 0.4;
    if (codeStr.includes('trim()') || codeStr.includes('toLowerCase()')) score += 0.2;
    if (codeStr.includes('replace(') || codeStr.includes('filter(')) score += 0.2;
    if (codeStr.includes('JSON.parse') && codeStr.includes('try')) score += 0.2;
    
    return Math.min(1.0, score);
  }

  analyzeResourceLimits(code) {
    const codeStr = JSON.stringify(code);
    let score = 0;
    
    // Look for resource limit patterns
    if (codeStr.includes('timeout') || codeStr.includes('setTimeout')) score += 0.3;
    if (codeStr.includes('limit') || codeStr.includes('max')) score += 0.2;
    if (codeStr.includes('cache') || codeStr.includes('memory')) score += 0.3;
    if (codeStr.includes('abort') || codeStr.includes('cancel')) score += 0.2;
    
    return Math.min(1.0, score);
  }

  analyzeCodeQuality(code) {
    // Combine multiple quality metrics
    const maintainability = this.analyzeMaintainability(code);
    const readability = this.analyzeReadability(code);
    const modularity = this.analyzeModularity(code);
    
    return (maintainability + readability + modularity) / 3;
  }

  // Code quality analysis methods
  analyzeMaintainability(code) {
    const codeStr = JSON.stringify(code);
    let score = 0.5; // Base score
    
    // Add points for maintainable patterns
    if (codeStr.includes('const ') || codeStr.includes('let ')) score += 0.1;
    if (codeStr.includes('function ') || codeStr.includes('=>')) score += 0.1;
    if (codeStr.includes('class ')) score += 0.1;
    if (codeStr.includes('/**') || codeStr.includes('//')) score += 0.2;
    
    return Math.min(1.0, score);
  }

  analyzeReadability(code) {
    const codeStr = JSON.stringify(code);
    const lines = codeStr.split('\n');
    
    let score = 0.5;
    
    // Check for readable patterns
    const avgLineLength = lines.reduce((sum, line) => sum + line.length, 0) / lines.length;
    if (avgLineLength < 100) score += 0.2; // Reasonable line length
    
    const commentLines = lines.filter(line => line.trim().startsWith('//') || line.includes('/**')).length;
    const commentRatio = commentLines / lines.length;
    score += Math.min(0.3, commentRatio * 2); // Up to 30% for comments
    
    return Math.min(1.0, score);
  }

  analyzeTestability(code) {
    const codeStr = JSON.stringify(code);
    let score = 0;
    
    // Look for testable patterns
    if (codeStr.includes('module.exports') || codeStr.includes('export')) score += 0.3;
    if (codeStr.includes('function ') || codeStr.includes('class ')) score += 0.2;
    if (codeStr.includes('async ') || codeStr.includes('Promise')) score += 0.2;
    if (codeStr.includes('test') || codeStr.includes('spec')) score += 0.3;
    
    return Math.min(1.0, score);
  }

  analyzeModularity(code) {
    const codeStr = JSON.stringify(code);
    let score = 0;
    
    // Check for modular patterns
    if (codeStr.includes('require(') || codeStr.includes('import ')) score += 0.3;
    if (codeStr.includes('module.exports') || codeStr.includes('export')) score += 0.3;
    if (codeStr.includes('class ')) score += 0.2;
    if (codeStr.includes('function ')) score += 0.2;
    
    return Math.min(1.0, score);
  }

  analyzeDocumentation(code) {
    const codeStr = JSON.stringify(code);
    const lines = codeStr.split('\n');
    
    const docLines = lines.filter(line => 
      line.includes('/**') || line.includes('*/') || line.trim().startsWith('*')
    ).length;
    
    const docRatio = docLines / lines.length;
    return Math.min(1.0, docRatio * 5); // Scale up documentation ratio
  }

  analyzeBestPractices(code) {
    const codeStr = JSON.stringify(code);
    let score = 0;
    
    // Check for best practices
    if (codeStr.includes('use strict')) score += 0.1;
    if (codeStr.includes('const ') && !codeStr.includes('var ')) score += 0.2;
    if (codeStr.includes('async ') && codeStr.includes('await ')) score += 0.2;
    if (codeStr.includes('try {') && codeStr.includes('catch (')) score += 0.2;
    if (codeStr.includes('===') && !codeStr.includes('==')) score += 0.1;
    if (codeStr.includes('Array.isArray') || codeStr.includes('typeof')) score += 0.2;
    
    return Math.min(1.0, score);
  }

  // Evolution-specific calculations
  calculateCodeSize(code) {
    return JSON.stringify(code).length;
  }

  calculateCodeComplexity(code) {
    const codeStr = JSON.stringify(code);
    
    // Simple complexity calculation based on control structures
    const ifCount = (codeStr.match(/if\s*\(/g) || []).length;
    const forCount = (codeStr.match(/for\s*\(/g) || []).length;
    const whileCount = (codeStr.match(/while\s*\(/g) || []).length;
    const switchCount = (codeStr.match(/switch\s*\(/g) || []).length;
    const functionCount = (codeStr.match(/function\s+/g) || []).length;
    
    return ifCount + forCount + whileCount + switchCount + functionCount;
  }

  calculateNovelty(agent) {
    // Simplified novelty calculation
    return Math.min(1.0, agent.generation / 10);
  }

  calculateDiversity(agent) {
    // Simplified diversity calculation
    return Math.random() * 0.3 + 0.7; // Placeholder
  }

  calculateFitnessImprovement(agent) {
    const history = this.metricsHistory.get(agent.id);
    if (!history || history.length < 2) return 0;
    
    const current = history[history.length - 1];
    const previous = history[history.length - 2];
    
    return current.overall - previous.overall;
  }

  calculateLineageStrength(agent) {
    // Simplified lineage strength calculation
    return Math.min(1.0, agent.fitness);
  }

  /**
   * Get metrics history for an agent
   */
  getMetricsHistory(agentId) {
    return this.metricsHistory.get(agentId) || [];
  }

  /**
   * Get metrics statistics
   */
  getMetricsStats() {
    const stats = {
      totalAgents: this.metricsHistory.size,
      totalMetrics: 0,
      averagePerformance: 0,
      averageReliability: 0,
      averageFunctionality: 0,
      averageSafety: 0
    };

    let totalPerformance = 0;
    let totalReliability = 0;
    let totalFunctionality = 0;
    let totalSafety = 0;

    for (const history of this.metricsHistory.values()) {
      stats.totalMetrics += history.length;
      
      if (history.length > 0) {
        const latest = history[history.length - 1];
        totalPerformance += latest.performance || 0;
        totalReliability += latest.reliability || 0;
        totalFunctionality += latest.functionality || 0;
        totalSafety += latest.safety || 0;
      }
    }

    if (this.metricsHistory.size > 0) {
      stats.averagePerformance = totalPerformance / this.metricsHistory.size;
      stats.averageReliability = totalReliability / this.metricsHistory.size;
      stats.averageFunctionality = totalFunctionality / this.metricsHistory.size;
      stats.averageSafety = totalSafety / this.metricsHistory.size;
    }

    return stats;
  }
}

module.exports = MetricsCollector;

/**
 * Base Adapter Class
 * 
 * Abstract base class for all AI assistant adapters.
 * Provides standardized interface and common functionality.
 */

const EventEmitter = require('events');
const chalk = require('chalk');

class BaseAdapter extends EventEmitter {
  constructor(config, metaOrchestrator) {
    super();
    
    this.config = config;
    this.metaOrchestrator = metaOrchestrator;
    this.adapterId = this.constructor.name.toLowerCase().replace('adapter', '');
    
    // Adapter state
    this.state = {
      initialized: false,
      available: false,
      lastHealthCheck: null,
      lastResponseTime: null,
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0
    };
    
    // Capabilities that this adapter supports
    this.capabilities = new Set();
    
    // Roles that this adapter can perform
    this.supportedRoles = new Set();
    
    // Performance metrics
    this.metrics = {
      averageResponseTime: 0,
      successRate: 0,
      totalExecutionTime: 0,
      requestHistory: []
    };
  }
  
  /**
   * Initialize the adapter - must be implemented by subclasses
   */
  async initialize() {
    throw new Error('initialize() must be implemented by subclass');
  }
  
  /**
   * Check if the adapter is available - must be implemented by subclasses
   */
  async checkAvailability() {
    throw new Error('checkAvailability() must be implemented by subclass');
  }
  
  /**
   * Execute a task with this adapter - must be implemented by subclasses
   */
  async execute(task, context) {
    throw new Error('execute() must be implemented by subclass');
  }
  
  /**
   * Health check for the adapter - can be overridden by subclasses
   */
  async healthCheck() {
    try {
      const startTime = Date.now();
      const isAvailable = await this.checkAvailability();
      const responseTime = Date.now() - startTime;
      
      this.state.lastHealthCheck = Date.now();
      this.state.lastResponseTime = responseTime;
      this.state.available = isAvailable;
      
      return isAvailable;
      
    } catch (error) {
      this.state.lastHealthCheck = Date.now();
      this.state.available = false;
      throw error;
    }
  }
  
  /**
   * Shutdown the adapter - can be overridden by subclasses
   */
  async shutdown() {
    this.state.initialized = false;
    this.state.available = false;
    this.emit('shutdown');
  }
  
  /**
   * Check if adapter supports a specific role
   */
  supportsRole(role) {
    return this.supportedRoles.has(role);
  }
  
  /**
   * Check if adapter has a specific capability
   */
  hasCapability(capability) {
    return this.capabilities.has(capability);
  }
  
  /**
   * Add a capability to this adapter
   */
  addCapability(capability) {
    this.capabilities.add(capability);
  }
  
  /**
   * Add a supported role to this adapter
   */
  addSupportedRole(role) {
    this.supportedRoles.add(role);
  }
  
  /**
   * Record execution metrics
   */
  recordExecution(success, responseTime, error = null) {
    this.state.totalRequests++;
    
    if (success) {
      this.state.successfulRequests++;
    } else {
      this.state.failedRequests++;
    }
    
    // Update average response time
    const totalTime = this.metrics.averageResponseTime * (this.state.totalRequests - 1) + responseTime;
    this.metrics.averageResponseTime = totalTime / this.state.totalRequests;
    
    // Update success rate
    this.metrics.successRate = this.state.successfulRequests / this.state.totalRequests;
    
    // Update total execution time
    this.metrics.totalExecutionTime += responseTime;
    
    // Add to request history (keep last 100 requests)
    this.metrics.requestHistory.push({
      timestamp: Date.now(),
      success,
      responseTime,
      error: error?.message || null
    });
    
    if (this.metrics.requestHistory.length > 100) {
      this.metrics.requestHistory.shift();
    }
    
    // Emit metrics update event
    this.emit('metricsUpdated', {
      adapterId: this.adapterId,
      success,
      responseTime,
      totalRequests: this.state.totalRequests,
      successRate: this.metrics.successRate,
      averageResponseTime: this.metrics.averageResponseTime
    });
  }
  
  /**
   * Get adapter status and metrics
   */
  getStatus() {
    return {
      adapterId: this.adapterId,
      state: { ...this.state },
      capabilities: Array.from(this.capabilities),
      supportedRoles: Array.from(this.supportedRoles),
      metrics: { ...this.metrics },
      config: {
        enabled: this.config.enabled,
        timeout: this.config.timeout,
        retries: this.config.retries
      }
    };
  }
  
  /**
   * Validate task input - can be overridden by subclasses
   */
  validateTask(task) {
    if (!task) {
      throw new Error('Task is required');
    }
    
    if (!task.role) {
      throw new Error('Task role is required');
    }
    
    if (!this.supportsRole(task.role)) {
      throw new Error(`Adapter ${this.adapterId} does not support role: ${task.role}`);
    }
    
    return true;
  }
  
  /**
   * Validate context input - can be overridden by subclasses
   */
  validateContext(context) {
    // Base validation - subclasses can add more specific validation
    return true;
  }
  
  /**
   * Prepare request data - can be overridden by subclasses
   */
  prepareRequest(task, context) {
    return {
      task,
      context,
      timestamp: Date.now(),
      adapterId: this.adapterId
    };
  }
  
  /**
   * Process response data - can be overridden by subclasses
   */
  processResponse(response, task, context) {
    return {
      result: response,
      adapterId: this.adapterId,
      role: task.role,
      timestamp: Date.now(),
      success: true
    };
  }
  
  /**
   * Handle execution errors - can be overridden by subclasses
   */
  handleError(error, task, context) {
    console.error(chalk.red(`❌ ${this.adapterId} execution error:`, error.message));
    
    return {
      result: null,
      adapterId: this.adapterId,
      role: task?.role || 'unknown',
      timestamp: Date.now(),
      success: false,
      error: error.message
    };
  }
  
  /**
   * Execute with timeout and retry logic
   */
  async executeWithRetry(task, context) {
    const maxRetries = this.config.retries || 3;
    const timeout = this.config.timeout || 30000;
    
    let lastError = null;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(chalk.cyan(`🤖 ${this.adapterId} executing ${task.role} (attempt ${attempt}/${maxRetries})`));
        
        const startTime = Date.now();
        
        // Execute with timeout
        const result = await Promise.race([
          this.execute(task, context),
          new Promise((_, reject) => 
            setTimeout(() => reject(new Error('Execution timeout')), timeout)
          )
        ]);
        
        const responseTime = Date.now() - startTime;
        
        // Record successful execution
        this.recordExecution(true, responseTime);
        
        console.log(chalk.green(`✅ ${this.adapterId} completed ${task.role} in ${responseTime}ms`));
        
        return this.processResponse(result, task, context);
        
      } catch (error) {
        lastError = error;
        const responseTime = Date.now() - startTime;
        
        // Record failed execution
        this.recordExecution(false, responseTime, error);
        
        console.warn(chalk.yellow(`⚠️ ${this.adapterId} attempt ${attempt} failed: ${error.message}`));
        
        // If this is the last attempt, don't wait
        if (attempt < maxRetries) {
          // Wait before retry (exponential backoff)
          const delay = Math.min(1000 * Math.pow(2, attempt - 1), 10000);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }
    
    // All attempts failed
    throw lastError || new Error('All retry attempts failed');
  }
  
  /**
   * Get configuration value with fallback
   */
  getConfig(key, defaultValue = null) {
    return this.config[key] !== undefined ? this.config[key] : defaultValue;
  }
  
  /**
   * Update configuration
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    this.emit('configUpdated', { adapterId: this.adapterId, config: this.config });
  }
  
  /**
   * Log adapter activity
   */
  log(level, message, data = {}) {
    const logData = {
      adapterId: this.adapterId,
      timestamp: Date.now(),
      level,
      message,
      ...data
    };
    
    switch (level) {
      case 'error':
        console.error(chalk.red(`❌ [${this.adapterId}] ${message}`), data);
        break;
      case 'warn':
        console.warn(chalk.yellow(`⚠️ [${this.adapterId}] ${message}`), data);
        break;
      case 'info':
        console.log(chalk.blue(`ℹ️ [${this.adapterId}] ${message}`), data);
        break;
      case 'debug':
        console.log(chalk.gray(`🐛 [${this.adapterId}] ${message}`), data);
        break;
      default:
        console.log(`[${this.adapterId}] ${message}`, data);
    }
    
    this.emit('log', logData);
  }
  
  /**
   * Get recent performance metrics
   */
  getRecentMetrics(timeWindow = 300000) { // 5 minutes default
    const cutoff = Date.now() - timeWindow;
    const recentRequests = this.metrics.requestHistory.filter(req => req.timestamp >= cutoff);
    
    if (recentRequests.length === 0) {
      return {
        totalRequests: 0,
        successfulRequests: 0,
        successRate: 0,
        averageResponseTime: 0
      };
    }
    
    const successfulRequests = recentRequests.filter(req => req.success).length;
    const totalResponseTime = recentRequests.reduce((sum, req) => sum + req.responseTime, 0);
    
    return {
      totalRequests: recentRequests.length,
      successfulRequests,
      successRate: successfulRequests / recentRequests.length,
      averageResponseTime: totalResponseTime / recentRequests.length
    };
  }
}

module.exports = BaseAdapter;

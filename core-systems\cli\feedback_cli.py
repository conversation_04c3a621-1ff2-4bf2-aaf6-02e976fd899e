#!/usr/bin/env python3
"""
Feedback Loop CLI Tool

Command-line interface for managing and monitoring the Universal Dual-Purpose
Feedback Loop Framework.
"""

import argparse
import json
import sys
import yaml
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, Any, Optional

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from core.feedback_engine import FeedbackEngine
from domains.drone_ai import DroneAIDomain
from domains.timestamp_ai import TimeStampAIDomain
from domains.search_rescue import SearchRescueDomain
from components.confidence.adaptive_confidence_model import AdaptiveConfidenceModel
from components.trust.trust_score_calculator import TrustScoreCalculator
from components.memory.file_memory_store import FileMemoryStore


class FeedbackCLI:
    """Command-line interface for the feedback loop framework."""

    def __init__(self):
        self.config = None
        self.engine = None

    def load_config(self, config_path: str) -> Dict[str, Any]:
        """Load configuration from YAML file."""
        try:
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)
            self.config = config
            return config
        except Exception as e:
            print(f"Error loading config: {e}")
            sys.exit(1)

    def initialize_engine(self, config_path: str = None) -> FeedbackEngine:
        """Initialize the feedback engine from configuration."""
        if config_path:
            self.load_config(config_path)

        if not self.config:
            print("No configuration loaded. Use --config to specify config file.")
            sys.exit(1)

        try:
            # Initialize components
            confidence_model = AdaptiveConfidenceModel(self.config.get('confidence_model', {}))
            trust_calculator = TrustScoreCalculator(self.config.get('trust_calculator', {}))
            memory_store = FileMemoryStore(self.config.get('memory_store', {}))

            # Create engine
            self.engine = FeedbackEngine(
                confidence_model=confidence_model,
                trust_calculator=trust_calculator,
                memory_store=memory_store,
                config=self.config.get('feedback_engine', {})
            )

            # Register domains
            domains_config = self.config.get('domains', {})

            if domains_config.get('drone_ai', {}).get('enabled', True):
                drone_domain = DroneAIDomain(domains_config.get('drone_ai', {}))
                self.engine.register_domain('drone_ai', drone_domain)

            if domains_config.get('timestamp_ai', {}).get('enabled', True):
                timestamp_domain = TimeStampAIDomain(domains_config.get('timestamp_ai', {}))
                self.engine.register_domain('timestamp_ai', timestamp_domain)

            if domains_config.get('search_rescue', {}).get('enabled', True):
                sar_domain = SearchRescueDomain(domains_config.get('search_rescue', {}))
                self.engine.register_domain('search_rescue', sar_domain)

            return self.engine

        except Exception as e:
            print(f"Error initializing engine: {e}")
            sys.exit(1)

    def cmd_status(self, args) -> None:
        """Show system status."""
        if not self.engine:
            self.initialize_engine(args.config)

        print("=== Feedback Loop Framework Status ===")

        # Health check
        health = self.engine.health_check()
        print(f"Engine Status: {health['engine']}")
        print(f"Components: {health['components']}")
        print(f"Domains: {health['domains']}")

        # Statistics
        stats = self.engine.get_statistics()
        print(f"\nProcessing Statistics:")
        print(f"  Total Processed: {stats['total_processed']}")
        print(f"  Average Processing Time: {stats['average_processing_time']:.2f}ms")
        print(f"  By Domain: {stats['by_domain']}")
        print(f"  By Feedback Type: {stats['by_feedback_type']}")

        # Domain info
        domain_info = self.engine.get_domain_info()
        print(f"\nRegistered Domains: {list(domain_info.keys())}")

    def cmd_process(self, args) -> None:
        """Process a single input."""
        if not self.engine:
            self.initialize_engine(args.config)

        # Parse input data
        try:
            if args.input_file:
                with open(args.input_file, 'r') as f:
                    data = json.load(f)
            else:
                data = json.loads(args.input_data)
        except Exception as e:
            print(f"Error parsing input: {e}")
            sys.exit(1)

        # Parse context
        context = {}
        if args.context:
            try:
                context = json.loads(args.context)
            except Exception as e:
                print(f"Error parsing context: {e}")
                sys.exit(1)

        # Process
        try:
            result = self.engine.process_output(
                domain=args.domain,
                raw_output=data,
                context=context,
                agent_id=args.agent_id
            )

            # Output result
            output = {
                'feedback_type': result.feedback_type.value,
                'confidence_score': result.confidence_score,
                'trust_score': result.trust_score,
                'validation_passed': result.validation_passed,
                'issues': result.validation_issues,
                'recommendations': result.recommendations,
                'processing_time_ms': result.processing_time_ms
            }

            if args.output_file:
                with open(args.output_file, 'w') as f:
                    json.dump(output, f, indent=2)
                print(f"Result written to {args.output_file}")
            else:
                print(json.dumps(output, indent=2))

        except Exception as e:
            print(f"Error processing input: {e}")
            sys.exit(1)

    def cmd_analytics(self, args) -> None:
        """Generate analytics report."""
        if not self.engine:
            self.initialize_engine(args.config)

        if not self.engine.memory_store:
            print("No memory store configured for analytics")
            sys.exit(1)

        # Prepare time range filter
        time_range = None
        if args.days:
            end_time = datetime.utcnow()
            start_time = end_time - timedelta(days=args.days)
            time_range = {
                'start': start_time.isoformat(),
                'end': end_time.isoformat()
            }

        # Generate analytics
        try:
            analytics = self.engine.memory_store.get_analytics_data(
                domain=args.domain,
                time_range=time_range
            )

            # Output analytics
            if args.output_file:
                with open(args.output_file, 'w') as f:
                    json.dump(analytics, f, indent=2)
                print(f"Analytics written to {args.output_file}")
            else:
                print("=== Analytics Report ===")
                print(f"Total Entries: {analytics.get('total_entries', 0)}")
                print(f"Success Rate: {analytics.get('success_rate', 0):.2%}")
                print(f"Average Confidence: {analytics.get('average_confidence', 0):.3f}")
                print(f"Average Trust: {analytics.get('average_trust', 0):.3f}")
                print(f"Average Processing Time: {analytics.get('average_processing_time_ms', 0):.1f}ms")

                print(f"\nFeedback Distribution:")
                for feedback_type, count in analytics.get('feedback_distribution', {}).items():
                    print(f"  {feedback_type}: {count}")

                print(f"\nDomain Distribution:")
                for domain, count in analytics.get('domain_distribution', {}).items():
                    print(f"  {domain}: {count}")

                top_agents = analytics.get('top_agents', [])[:5]
                if top_agents:
                    print(f"\nTop 5 Agents:")
                    for agent in top_agents:
                        print(f"  {agent['agent_id']}: {agent['correct']} correct, {agent['partial']} partial")

        except Exception as e:
            print(f"Error generating analytics: {e}")
            sys.exit(1)

    def cmd_trust(self, args) -> None:
        """Show trust scores."""
        if not self.engine:
            self.initialize_engine(args.config)

        if not self.engine.trust_calculator:
            print("No trust calculator configured")
            sys.exit(1)

        try:
            if args.agent_id:
                # Show specific agent trust
                summary = self.engine.trust_calculator.get_agent_trust_summary(args.agent_id)
                print(f"=== Trust Summary for {args.agent_id} ===")
                print(f"Overall Trust: {summary['overall_trust']:.3f}")
                print(f"Domain Count: {summary['domain_count']}")

                for domain, data in summary['domains'].items():
                    print(f"\n{domain}:")
                    print(f"  Trust Score: {data['trust_score']:.3f}")
                    print(f"  Entry Count: {data['entry_count']}")
                    print(f"  Success Rate: {data['success_rate']:.2%}")

            elif args.domain:
                # Show domain trust summary
                summary = self.engine.trust_calculator.get_domain_trust_summary(args.domain)
                print(f"=== Trust Summary for {args.domain} ===")
                print(f"Agent Count: {summary['agent_count']}")
                print(f"Average Trust: {summary['average_trust']:.3f}")
                print(f"Min Trust: {summary['min_trust']:.3f}")
                print(f"Max Trust: {summary['max_trust']:.3f}")

            else:
                # Show top agents
                top_agents = self.engine.trust_calculator.get_top_agents(limit=args.limit or 10)
                print(f"=== Top {len(top_agents)} Agents by Trust Score ===")

                for i, agent in enumerate(top_agents, 1):
                    print(f"{i:2d}. {agent['agent_id']}: {agent['trust_score']:.3f} "
                          f"({agent['entry_count']} entries)")

        except Exception as e:
            print(f"Error retrieving trust data: {e}")
            sys.exit(1)

    def cmd_cleanup(self, args) -> None:
        """Clean up old data."""
        if not self.engine:
            self.initialize_engine(args.config)

        if not self.engine.memory_store:
            print("No memory store configured for cleanup")
            sys.exit(1)

        try:
            retention_days = args.days or self.config.get('memory_store', {}).get('retention_days', 30)

            if not args.force:
                response = input(f"Delete entries older than {retention_days} days? (y/N): ")
                if response.lower() != 'y':
                    print("Cleanup cancelled")
                    return

            cleaned_count = self.engine.memory_store.cleanup_old_entries(retention_days)
            print(f"Cleaned up {cleaned_count} old entries")

        except Exception as e:
            print(f"Error during cleanup: {e}")
            sys.exit(1)

    def cmd_config(self, args) -> None:
        """Show or validate configuration."""
        if args.validate:
            try:
                config = self.load_config(args.config)
                print("Configuration is valid")

                # Show summary
                print("\nConfiguration Summary:")
                print(f"  Version: {config.get('version', 'unknown')}")
                print(f"  Domains: {list(config.get('domains', {}).keys())}")
                print(f"  Memory Store: {config.get('memory_store', {}).get('type', 'unknown')}")
                print(f"  Analytics Enabled: {config.get('analytics', {}).get('enabled', False)}")

            except Exception as e:
                print(f"Configuration validation failed: {e}")
                sys.exit(1)
        else:
            # Show current config
            if self.config:
                print(yaml.dump(self.config, default_flow_style=False))
            else:
                print("No configuration loaded")

    def cmd_sar_mission(self, args) -> None:
        """Search and Rescue mission management."""
        if not self.engine:
            self.initialize_engine(args.config)

        # Check if SAR domain is registered
        domain_info = self.engine.get_domain_info()
        if 'search_rescue' not in domain_info:
            print("Search and Rescue domain not registered")
            sys.exit(1)

        sar_domain = self.engine.domains.get('search_rescue')

        if args.sar_action == 'summary':
            mission_summary = sar_domain.get_mission_summary(args.mission_id or 'current')
            print(f"=== Mission Summary: {args.mission_id or 'Current'} ===")
            print(f"Total Detections: {mission_summary['total_detections']}")
            print(f"Side Pocket Items: {mission_summary['side_pocket_items']}")

            print("\nMission Outcomes:")
            for outcome, count in mission_summary['outcomes'].items():
                print(f"  {outcome}: {count}")

        elif args.sar_action == 'outcomes':
            outcomes = sar_domain.get_mission_outcomes()
            print("=== Mission Outcomes Statistics ===")
            print(f"Total Missions: {outcomes['total_missions']}")
            print(f"Success Rate: {outcomes['success_rate']:.2%}")
            print(f"Target Found Rate: {outcomes['target_found_rate']:.2%}")
            print(f"Partial Success Rate: {outcomes['partial_success_rate']:.2%}")

            print("\nDetailed Outcomes:")
            for outcome, count in outcomes.items():
                if outcome not in ['total_missions', 'success_rate', 'target_found_rate', 'partial_success_rate']:
                    print(f"  {outcome}: {count}")

    def cmd_side_pocket(self, args) -> None:
        """Side pocket management for Search and Rescue."""
        if not self.engine:
            self.initialize_engine(args.config)

        # Check if SAR domain is registered
        domain_info = self.engine.get_domain_info()
        if 'search_rescue' not in domain_info:
            print("Search and Rescue domain not registered")
            sys.exit(1)

        sar_domain = self.engine.domains.get('search_rescue')

        if args.side_pocket_action == 'pending':
            pending_items = sar_domain.get_pending_reviews(limit=args.limit or 20)
            print(f"=== Pending Side Pocket Reviews ({len(pending_items)}) ===")

            for item in pending_items:
                print(f"\nItem ID: {item['item_id']}")
                print(f"Category: {item['category']}")
                print(f"Reason: {item['reason']}")
                print(f"Priority: {item.get('metadata', {}).get('priority', 'unknown')}")
                print(f"Added: {item['timestamp']}")

        elif args.side_pocket_action == 'analytics':
            analytics = sar_domain.get_side_pocket_analytics()
            print("=== Side Pocket Analytics ===")

            summary = analytics.get('summary', {})
            print(f"Total Items: {summary.get('total_items', 0)}")
            print(f"Review Completed: {summary.get('review_completed', 0)}")

            print("\nCategory Distribution:")
            for category, count in analytics.get('category_distribution', {}).items():
                print(f"  {category}: {count}")

            efficiency = analytics.get('review_efficiency', {})
            print(f"\nReview Efficiency: {efficiency.get('efficiency_rate', 0):.2%}")
            print(f"Pending Items: {efficiency.get('pending_items', 0)}")

        elif args.side_pocket_action == 'retraining':
            batch_result = sar_domain.create_retraining_batch()

            if batch_result.get('status') == 'success':
                print(f"=== Retraining Batch Created ===")
                print(f"Batch Name: {batch_result['batch_name']}")
                print(f"Item Count: {batch_result['item_count']}")
                print(f"Batch Path: {batch_result['batch_path']}")
            elif batch_result.get('status') == 'insufficient_items':
                print(f"Insufficient items for retraining batch: {batch_result['count']}")
            else:
                print(f"Error creating retraining batch: {batch_result.get('error', 'Unknown error')}")

        elif args.side_pocket_action == 'cleanup':
            retention_days = args.days or 90

            if not args.force:
                response = input(f"Delete side pocket items older than {retention_days} days? (y/N): ")
                if response.lower() != 'y':
                    print("Cleanup cancelled")
                    return

            cleaned_count = sar_domain.cleanup_side_pocket(retention_days)
            print(f"Cleaned up {cleaned_count} old side pocket items")


def main():
    """Main CLI entry point."""
    parser = argparse.ArgumentParser(
        description="Universal Dual-Purpose Feedback Loop Framework CLI",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )

    parser.add_argument(
        '--config', '-c',
        default='config/default_config.yaml',
        help='Configuration file path'
    )

    subparsers = parser.add_subparsers(dest='command', help='Available commands')

    # Status command
    status_parser = subparsers.add_parser('status', help='Show system status')

    # Process command
    process_parser = subparsers.add_parser('process', help='Process a single input')
    process_parser.add_argument('domain', choices=['drone_ai', 'timestamp_ai', 'search_rescue'], help='Domain to process')
    process_parser.add_argument('--input-data', help='JSON input data')
    process_parser.add_argument('--input-file', help='Input file path')
    process_parser.add_argument('--context', help='JSON context data')
    process_parser.add_argument('--agent-id', help='Agent identifier')
    process_parser.add_argument('--output-file', help='Output file path')

    # Analytics command
    analytics_parser = subparsers.add_parser('analytics', help='Generate analytics report')
    analytics_parser.add_argument('--domain', help='Filter by domain')
    analytics_parser.add_argument('--days', type=int, help='Number of days to analyze')
    analytics_parser.add_argument('--output-file', help='Output file path')

    # Trust command
    trust_parser = subparsers.add_parser('trust', help='Show trust scores')
    trust_parser.add_argument('--agent-id', help='Show trust for specific agent')
    trust_parser.add_argument('--domain', help='Show trust for specific domain')
    trust_parser.add_argument('--limit', type=int, help='Limit number of results')

    # Cleanup command
    cleanup_parser = subparsers.add_parser('cleanup', help='Clean up old data')
    cleanup_parser.add_argument('--days', type=int, help='Retention period in days')
    cleanup_parser.add_argument('--force', action='store_true', help='Skip confirmation')

    # Config command
    config_parser = subparsers.add_parser('config', help='Show or validate configuration')
    config_parser.add_argument('--validate', action='store_true', help='Validate configuration')

    # Search and Rescue mission command
    sar_parser = subparsers.add_parser('sar-mission', help='Search and Rescue mission management')
    sar_parser.add_argument('sar_action', choices=['summary', 'outcomes'], help='SAR action to perform')
    sar_parser.add_argument('--mission-id', help='Mission identifier')

    # Side pocket command
    side_pocket_parser = subparsers.add_parser('side-pocket', help='Side pocket management')
    side_pocket_parser.add_argument('side_pocket_action', choices=['pending', 'analytics', 'retraining', 'cleanup'], help='Side pocket action')
    side_pocket_parser.add_argument('--limit', type=int, help='Limit number of results')
    side_pocket_parser.add_argument('--days', type=int, help='Number of days for cleanup')
    side_pocket_parser.add_argument('--force', action='store_true', help='Skip confirmation')

    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        sys.exit(1)

    # Initialize CLI
    cli = FeedbackCLI()

    # Execute command
    command_map = {
        'status': cli.cmd_status,
        'process': cli.cmd_process,
        'analytics': cli.cmd_analytics,
        'trust': cli.cmd_trust,
        'cleanup': cli.cmd_cleanup,
        'config': cli.cmd_config,
        'sar-mission': cli.cmd_sar_mission,
        'side-pocket': cli.cmd_side_pocket
    }

    command_func = command_map.get(args.command)
    if command_func:
        command_func(args)
    else:
        print(f"Unknown command: {args.command}")
        sys.exit(1)


if __name__ == "__main__":
    main()

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EcoStamp - AI Environmental Impact Tracker</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🌱</text></svg>">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1rem 0;
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
        
        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 1.8rem;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
        }
        
        .nav-links a {
            text-decoration: none;
            color: #2c3e50;
            font-weight: 500;
            transition: color 0.3s;
        }
        
        .nav-links a:hover {
            color: #667eea;
        }
        
        main {
            margin-top: 80px;
        }
        
        .hero {
            text-align: center;
            padding: 4rem 0;
            color: white;
        }
        
        .hero h1 {
            font-size: 3.5rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .hero p {
            font-size: 1.3rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        
        .cta-button {
            display: inline-block;
            background: #27ae60;
            color: white;
            padding: 1rem 2rem;
            text-decoration: none;
            border-radius: 50px;
            font-weight: bold;
            font-size: 1.1rem;
            transition: all 0.3s;
            box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
        }
        
        .cta-button:hover {
            background: #2ecc71;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(39, 174, 96, 0.4);
        }
        
        .features {
            background: white;
            padding: 4rem 0;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }
        
        .feature-card {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 12px;
            text-align: center;
            transition: transform 0.3s;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
        }
        
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        .downloads {
            background: #2c3e50;
            color: white;
            padding: 4rem 0;
        }
        
        .downloads h2 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 3rem;
        }
        
        .download-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
        }
        
        .download-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 2rem;
            border-radius: 12px;
            text-align: center;
            transition: all 0.3s;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .download-card:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-5px);
        }
        
        .browser-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
        }
        
        .download-btn {
            display: inline-block;
            background: #27ae60;
            color: white;
            padding: 0.8rem 1.5rem;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            margin-top: 1rem;
            transition: all 0.3s;
            cursor: pointer;
            border: none;
            font-size: 1rem;
        }
        
        .download-btn:hover {
            background: #2ecc71;
            transform: scale(1.05);
        }
        
        .download-btn:disabled {
            background: #95a5a6;
            cursor: not-allowed;
            transform: none;
        }
        
        .status {
            background: #ecf0f1;
            padding: 3rem 0;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
            text-align: center;
        }
        
        .stat-card {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #27ae60;
            margin-bottom: 0.5rem;
        }
        
        footer {
            background: #2c3e50;
            color: white;
            text-align: center;
            padding: 2rem 0;
        }
        
        .footer-links {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-bottom: 1rem;
        }
        
        .footer-links a {
            color: white;
            text-decoration: none;
            transition: color 0.3s;
        }
        
        .footer-links a:hover {
            color: #27ae60;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            background-color: white;
            margin: 15% auto;
            padding: 2rem;
            border-radius: 12px;
            width: 80%;
            max-width: 500px;
            text-align: center;
        }
        
        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .close:hover {
            color: black;
        }
        
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2.5rem;
            }
            
            .nav-links {
                display: none;
            }
            
            .download-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header>
        <nav class="container">
            <div class="logo">🌱 EcoStamp</div>
            <ul class="nav-links">
                <li><a href="#home">Home</a></li>
                <li><a href="#features">Features</a></li>
                <li><a href="#downloads">Download</a></li>
                <li><a href="/verify.html">Verify</a></li>
                <li><a href="/dashboard.html">Dashboard</a></li>
            </ul>
        </nav>
    </header>

    <main>
        <section id="home" class="hero">
            <div class="container">
                <h1>🌱 EcoStamp</h1>
                <p>Track the environmental impact of your AI conversations in real-time</p>
                <a href="#downloads" class="cta-button">Download Now</a>
            </div>
        </section>

        <section id="features" class="features">
            <div class="container">
                <h2 style="text-align: center; font-size: 2.5rem; margin-bottom: 1rem;">Why EcoStamp?</h2>
                <p style="text-align: center; font-size: 1.2rem; color: #666; margin-bottom: 2rem;">
                    Make AI usage transparent and environmentally accountable
                </p>
                
                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">🌍</div>
                        <h3>Universal Compatibility</h3>
                        <p>Works with ChatGPT, Claude, Gemini, and all major AI platforms across all browsers</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">⚡</div>
                        <h3>Real-time Impact</h3>
                        <p>See energy and water usage instantly as you chat with AI systems</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">🔒</div>
                        <h3>Cryptographic Proof</h3>
                        <p>SHA-256 verification ensures your environmental data is tamper-proof</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">📊</div>
                        <h3>Detailed Analytics</h3>
                        <p>Track your AI usage patterns and environmental impact over time</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">🌱</div>
                        <h3>Eco-Level Rating</h3>
                        <p>Visual leaf system shows the environmental impact of each conversation</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">🔗</div>
                        <h3>GitHub Integration</h3>
                        <p>Direct links to open-source code for full transparency</p>
                    </div>
                </div>
            </div>
        </section>

        <section id="downloads" class="downloads">
            <div class="container">
                <h2>Download EcoStamp</h2>
                <p style="text-align: center; font-size: 1.2rem; margin-bottom: 2rem; opacity: 0.9;">
                    Available for all major browsers - Choose your platform
                </p>
                
                <div class="download-grid">
                    <div class="download-card">
                        <div class="browser-icon">🌐</div>
                        <h3>Chrome</h3>
                        <p>Most popular browser extension</p>
                        <button class="download-btn" onclick="downloadExtension('chrome')">
                            Download for Chrome
                        </button>
                    </div>
                    
                    <div class="download-card">
                        <div class="browser-icon">🦊</div>
                        <h3>Firefox</h3>
                        <p>Privacy-focused browser support</p>
                        <button class="download-btn" onclick="downloadExtension('firefox')">
                            Download for Firefox
                        </button>
                    </div>
                    
                    <div class="download-card">
                        <div class="browser-icon">🌊</div>
                        <h3>Edge</h3>
                        <p>Microsoft's modern browser</p>
                        <button class="download-btn" onclick="downloadExtension('edge')">
                            Download for Edge
                        </button>
                    </div>
                    
                    <div class="download-card">
                        <div class="browser-icon">🎭</div>
                        <h3>Opera</h3>
                        <p>Feature-rich browser experience</p>
                        <button class="download-btn" onclick="downloadExtension('opera')">
                            Download for Opera
                        </button>
                    </div>
                    
                    <div class="download-card">
                        <div class="browser-icon">🧭</div>
                        <h3>Safari</h3>
                        <p>Apple's browser for Mac</p>
                        <button class="download-btn" onclick="downloadExtension('safari')">
                            Download for Safari
                        </button>
                    </div>
                    
                    <div class="download-card">
                        <div class="browser-icon">📱</div>
                        <h3>Universal</h3>
                        <p>Works on any browser</p>
                        <button class="download-btn" onclick="downloadExtension('universal')">
                            Download Universal
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <section class="status">
            <div class="container">
                <h2 style="text-align: center; font-size: 2.5rem; margin-bottom: 2rem;">Live Statistics</h2>
                
                <div class="status-grid">
                    <div class="stat-card">
                        <div class="stat-number" id="totalHashes">0</div>
                        <div>Verified Conversations</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-number" id="totalEnergy">0</div>
                        <div>kWh Tracked</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-number" id="totalWater">0</div>
                        <div>Liters Water Tracked</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-number" id="activeUsers">0</div>
                        <div>Active Users</div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer>
        <div class="container">
            <div class="footer-links">
                <a href="/verify.html">Verify Hash</a>
                <a href="/dashboard.html">Analytics</a>
                <a href="https://github.com/chris-ai-dev/Time_Stamp_Project" target="_blank">GitHub</a>
                <a href="/api/docs" target="_blank">API Docs</a>
            </div>
            <p>&copy; 2025 EcoStamp. Making AI environmentally accountable.</p>
        </div>
    </footer>

    <!-- Download Modal -->
    <div id="downloadModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>🌱 Download Starting...</h2>
            <p id="downloadMessage">Your EcoStamp extension is being prepared...</p>
            <div style="margin-top: 1rem;">
                <button class="download-btn" onclick="closeModal()">Close</button>
            </div>
        </div>
    </div>

    <script>
        // Download functionality
        async function downloadExtension(browser) {
            const modal = document.getElementById('downloadModal');
            const message = document.getElementById('downloadMessage');
            
            modal.style.display = 'block';
            
            try {
                const response = await fetch(`/api/download/${browser}`);
                const data = await response.json();
                
                if (data.success) {
                    message.innerHTML = `
                        <strong>✅ Download Ready!</strong><br>
                        <a href="${data.downloadUrl}" download="${data.filename}" style="color: #27ae60; text-decoration: none;">
                            📥 Click here to download ${data.filename}
                        </a><br><br>
                        <small>Installation instructions will be included in the download.</small>
                    `;
                    
                    // Auto-download
                    const link = document.createElement('a');
                    link.href = data.downloadUrl;
                    link.download = data.filename;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                } else {
                    message.innerHTML = `
                        <strong>⚠️ Coming Soon!</strong><br>
                        ${browser.charAt(0).toUpperCase() + browser.slice(1)} extension is being prepared.<br>
                        <small>Check back soon or download the universal version.</small>
                    `;
                }
            } catch (error) {
                message.innerHTML = `
                    <strong>⚠️ Coming Soon!</strong><br>
                    ${browser.charAt(0).toUpperCase() + browser.slice(1)} extension is being prepared.<br>
                    <small>Check back soon or try the universal version.</small>
                `;
            }
        }
        
        function closeModal() {
            document.getElementById('downloadModal').style.display = 'none';
        }
        
        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('downloadModal');
            if (event.target == modal) {
                modal.style.display = 'none';
            }
        }
        
        // Close modal with X button
        document.querySelector('.close').onclick = closeModal;
        
        // Load live statistics
        async function loadStats() {
            try {
                const response = await fetch('/api/registry/stats');
                const stats = await response.json();
                
                document.getElementById('totalHashes').textContent = stats.totalHashes || 0;
                document.getElementById('totalEnergy').textContent = (stats.totalEnergy || 0).toFixed(2);
                document.getElementById('totalWater').textContent = (stats.totalWater || 0).toFixed(1);
                document.getElementById('activeUsers').textContent = stats.activeUsers || 0;
            } catch (error) {
                console.log('Stats loading...', error);
            }
        }
        
        // Load stats on page load and refresh every 30 seconds
        loadStats();
        setInterval(loadStats, 30000);
        
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.agentRoutes = void 0;
const express_1 = require("express");
const client_1 = require("@prisma/client");
const zod_1 = require("zod");
const errorHandler_1 = require("../middleware/errorHandler");
const types_1 = require("@/shared/types");
const EventBus_1 = require("../services/EventBus");
const logger_1 = require("../utils/logger");
const AgentAssignmentService_1 = require("../services/AgentAssignmentService");
const router = (0, express_1.Router)();
exports.agentRoutes = router;
const prisma = new client_1.PrismaClient();
const eventBus = new EventBus_1.EventBus();
const assignmentService = new AgentAssignmentService_1.AgentAssignmentService();
// Validation schemas
const createAgentSchema = zod_1.z.object({
    agentId: zod_1.z.string().min(1, 'Agent ID is required').max(50, 'Agent ID too long'),
    name: zod_1.z.string().min(1, 'Name is required').max(100, 'Name too long'),
    description: zod_1.z.string().optional(),
    vendor: zod_1.z.string().min(1, 'Vendor is required').max(50, 'Vendor too long'),
    capabilities: zod_1.z.array(zod_1.z.string()).min(1, 'At least one capability required'),
    roles: zod_1.z.array(zod_1.z.string()).min(1, 'At least one role required'),
    subOrchestratorId: zod_1.z.string().optional(),
    metadata: zod_1.z.record(zod_1.z.any()).optional(),
});
const updateAgentSchema = zod_1.z.object({
    name: zod_1.z.string().min(1, 'Name is required').max(100, 'Name too long').optional(),
    description: zod_1.z.string().optional(),
    capabilities: zod_1.z.array(zod_1.z.string()).optional(),
    roles: zod_1.z.array(zod_1.z.string()).optional(),
    isActive: zod_1.z.boolean().optional(),
    subOrchestratorId: zod_1.z.string().nullable().optional(),
    metadata: zod_1.z.record(zod_1.z.any()).optional(),
});
const assignRoleSchema = zod_1.z.object({
    agentIds: zod_1.z.array(zod_1.z.string()).min(1, 'At least one agent required'),
    role: zod_1.z.string().min(1, 'Role is required'),
    priority: zod_1.z.number().min(1).max(10).default(5),
});
// Get all agents with filtering and pagination
router.get('/', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const search = req.query.search;
    const vendor = req.query.vendor;
    const role = req.query.role;
    const isActive = req.query.isActive === 'true' ? true : req.query.isActive === 'false' ? false : undefined;
    const skip = (page - 1) * limit;
    const where = {};
    if (search) {
        where.OR = [
            { name: { contains: search, mode: 'insensitive' } },
            { agentId: { contains: search, mode: 'insensitive' } },
            { description: { contains: search, mode: 'insensitive' } },
        ];
    }
    if (vendor) {
        where.vendor = { contains: vendor, mode: 'insensitive' };
    }
    if (role) {
        where.roles = { has: role };
    }
    if (isActive !== undefined) {
        where.isActive = isActive;
    }
    const [agents, total] = await Promise.all([
        prisma.agent.findMany({
            where,
            skip,
            take: limit,
            include: {
                subOrchestrator: {
                    select: {
                        id: true,
                        name: true,
                        domain: true,
                    },
                },
                _count: {
                    select: {
                        workflowExecutions: true,
                        evolutionVariants: true,
                    },
                },
            },
            orderBy: [
                { fitnessScore: 'desc' },
                { name: 'asc' },
            ],
        }),
        prisma.agent.count({ where }),
    ]);
    res.json({
        success: true,
        data: agents,
        pagination: {
            page,
            limit,
            total,
            totalPages: Math.ceil(total / limit),
        },
    });
}));
// Get single agent with detailed information
router.get('/:id', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const agent = await prisma.agent.findUnique({
        where: { id },
        include: {
            subOrchestrator: {
                select: {
                    id: true,
                    name: true,
                    domain: true,
                    metaOrchestrator: {
                        select: {
                            id: true,
                            name: true,
                        },
                    },
                },
            },
            workflowExecutions: {
                select: {
                    id: true,
                    status: true,
                    startedAt: true,
                    endedAt: true,
                    template: {
                        select: {
                            id: true,
                            name: true,
                        },
                    },
                },
                orderBy: {
                    startedAt: 'desc',
                },
                take: 10,
            },
            evolutionVariants: {
                select: {
                    id: true,
                    generation: true,
                    fitnessScore: true,
                    isPromoted: true,
                    createdAt: true,
                },
                orderBy: {
                    generation: 'desc',
                },
                take: 5,
            },
            tunnelsFrom: {
                select: {
                    id: true,
                    name: true,
                    toAgent: {
                        select: {
                            id: true,
                            name: true,
                        },
                    },
                },
            },
            tunnelsTo: {
                select: {
                    id: true,
                    name: true,
                    fromAgent: {
                        select: {
                            id: true,
                            name: true,
                        },
                    },
                },
            },
        },
    });
    if (!agent) {
        throw new errorHandler_1.AppError('Agent not found', 404);
    }
    res.json({
        success: true,
        data: agent,
    });
}));
// Get agent roles directory
router.get('/roles', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    res.json({
        success: true,
        data: types_1.AGENT_ROLES,
    });
}));
// Get agent registry with role capabilities
router.get('/registry', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const agents = await prisma.agent.findMany({
        where: { isActive: true },
        select: {
            id: true,
            agentId: true,
            name: true,
            vendor: true,
            capabilities: true,
            roles: true,
            fitnessScore: true,
        },
    });
    // Build role capabilities mapping
    const roleCapabilities = {};
    types_1.AGENT_ROLES.forEach(role => {
        roleCapabilities[role.id] = agents
            .filter(agent => agent.roles.includes(role.id))
            .map(agent => agent.agentId);
    });
    res.json({
        success: true,
        data: {
            agents,
            roleCapabilities,
            roles: types_1.AGENT_ROLES,
        },
    });
}));
// Create new agent
router.post('/', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const validatedData = createAgentSchema.parse(req.body);
    // Check if agent ID already exists
    const existingAgent = await prisma.agent.findUnique({
        where: { agentId: validatedData.agentId },
    });
    if (existingAgent) {
        throw new errorHandler_1.AppError('Agent ID already exists', 400);
    }
    // Validate sub-orchestrator exists if provided
    if (validatedData.subOrchestratorId) {
        const subOrchestrator = await prisma.subOrchestrator.findUnique({
            where: { id: validatedData.subOrchestratorId },
        });
        if (!subOrchestrator) {
            throw new errorHandler_1.AppError('Sub-orchestrator not found', 404);
        }
    }
    // Validate roles exist
    const validRoles = types_1.AGENT_ROLES.map(r => r.id);
    const invalidRoles = validatedData.roles.filter(role => !validRoles.includes(role));
    if (invalidRoles.length > 0) {
        throw new errorHandler_1.AppError(`Invalid roles: ${invalidRoles.join(', ')}`, 400);
    }
    const agent = await prisma.agent.create({
        data: {
            ...validatedData,
            fitnessScore: 50, // Default starting fitness
            isActive: true,
            createdBy: req.user.id,
        },
        include: {
            subOrchestrator: {
                select: {
                    id: true,
                    name: true,
                    domain: true,
                },
            },
        },
    });
    // Emit event
    eventBus.emit(EventBus_1.EVENT_TYPES.AGENT_CREATED, {
        agentId: agent.id,
        userId: req.user.id,
        data: agent,
    });
    logger_1.logger.info(`Agent created: ${agent.name} (${agent.agentId})`, {
        agentId: agent.id,
        userId: req.user.id,
    });
    res.status(201).json({
        success: true,
        data: agent,
    });
}));
// Update agent
router.put('/:id', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const validatedData = updateAgentSchema.parse(req.body);
    const existingAgent = await prisma.agent.findUnique({
        where: { id },
    });
    if (!existingAgent) {
        throw new errorHandler_1.AppError('Agent not found', 404);
    }
    // Validate sub-orchestrator exists if provided
    if (validatedData.subOrchestratorId) {
        const subOrchestrator = await prisma.subOrchestrator.findUnique({
            where: { id: validatedData.subOrchestratorId },
        });
        if (!subOrchestrator) {
            throw new errorHandler_1.AppError('Sub-orchestrator not found', 404);
        }
    }
    // Validate roles exist if provided
    if (validatedData.roles) {
        const validRoles = types_1.AGENT_ROLES.map(r => r.id);
        const invalidRoles = validatedData.roles.filter(role => !validRoles.includes(role));
        if (invalidRoles.length > 0) {
            throw new errorHandler_1.AppError(`Invalid roles: ${invalidRoles.join(', ')}`, 400);
        }
    }
    const agent = await prisma.agent.update({
        where: { id },
        data: {
            ...validatedData,
            updatedAt: new Date(),
        },
        include: {
            subOrchestrator: {
                select: {
                    id: true,
                    name: true,
                    domain: true,
                },
            },
        },
    });
    // Emit event
    eventBus.emit(EventBus_1.EVENT_TYPES.AGENT_UPDATED, {
        agentId: agent.id,
        userId: req.user.id,
        data: agent,
    });
    logger_1.logger.info(`Agent updated: ${agent.name} (${agent.agentId})`, {
        agentId: agent.id,
        userId: req.user.id,
    });
    res.json({
        success: true,
        data: agent,
    });
}));
// Delete agent
router.delete('/:id', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const agent = await prisma.agent.findUnique({
        where: { id },
        include: {
            _count: {
                select: {
                    workflowExecutions: true,
                    tunnelsFrom: true,
                    tunnelsTo: true,
                },
            },
        },
    });
    if (!agent) {
        throw new errorHandler_1.AppError('Agent not found', 404);
    }
    // Check if agent has active dependencies
    if (agent._count.workflowExecutions > 0 || agent._count.tunnelsFrom > 0 || agent._count.tunnelsTo > 0) {
        throw new errorHandler_1.AppError('Cannot delete agent with active workflows or tunnels', 400);
    }
    await prisma.agent.delete({
        where: { id },
    });
    // Emit event
    eventBus.emit(EventBus_1.EVENT_TYPES.AGENT_DELETED, {
        agentId: id,
        userId: req.user.id,
        data: { name: agent.name, agentId: agent.agentId },
    });
    logger_1.logger.info(`Agent deleted: ${agent.name} (${agent.agentId})`, {
        agentId: id,
        userId: req.user.id,
    });
    res.json({
        success: true,
        message: 'Agent deleted successfully',
    });
}));
// Assign roles to multiple agents
router.post('/assign-roles', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const validatedData = assignRoleSchema.parse(req.body);
    // Validate role exists
    const validRoles = types_1.AGENT_ROLES.map(r => r.id);
    if (!validRoles.includes(validatedData.role)) {
        throw new errorHandler_1.AppError('Invalid role', 400);
    }
    // Validate agents exist
    const agents = await prisma.agent.findMany({
        where: {
            id: { in: validatedData.agentIds },
        },
    });
    if (agents.length !== validatedData.agentIds.length) {
        throw new errorHandler_1.AppError('One or more agents not found', 404);
    }
    // Update agents with new role
    const updatedAgents = await Promise.all(agents.map(async (agent) => {
        const updatedRoles = Array.from(new Set([...agent.roles, validatedData.role]));
        return prisma.agent.update({
            where: { id: agent.id },
            data: {
                roles: updatedRoles,
                updatedAt: new Date(),
            },
            include: {
                subOrchestrator: {
                    select: {
                        id: true,
                        name: true,
                        domain: true,
                    },
                },
            },
        });
    }));
    // Emit events
    updatedAgents.forEach(agent => {
        eventBus.emit(EventBus_1.EVENT_TYPES.AGENT_ROLE_ASSIGNED, {
            agentId: agent.id,
            userId: req.user.id,
            data: { role: validatedData.role, agent },
        });
    });
    logger_1.logger.info(`Role ${validatedData.role} assigned to ${updatedAgents.length} agents`, {
        role: validatedData.role,
        agentIds: validatedData.agentIds,
        userId: req.user.id,
    });
    res.json({
        success: true,
        data: updatedAgents,
        message: `Role assigned to ${updatedAgents.length} agents`,
    });
}));
// Intelligent agent assignment
router.post('/assign', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const assignmentCriteria = req.body;
    const result = await assignmentService.assignAgents(assignmentCriteria, req.user.id);
    res.json({
        success: true,
        data: result,
    });
}));
// Get role-based recommendations
router.get('/recommendations/:roleId', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { roleId } = req.params;
    const recommendations = await assignmentService.getRecommendations(roleId);
    res.json({
        success: true,
        data: recommendations,
    });
}));
// Get agent capabilities analysis
router.get('/capabilities-analysis', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const agents = await prisma.agent.findMany({
        where: { isActive: true },
        select: {
            id: true,
            name: true,
            vendor: true,
            capabilities: true,
            roles: true,
            fitnessScore: true,
        },
    });
    // Analyze capability coverage
    const allCapabilities = new Set();
    const capabilityAgents = {};
    const roleCapabilities = {};
    agents.forEach(agent => {
        agent.capabilities.forEach(cap => {
            allCapabilities.add(cap);
            if (!capabilityAgents[cap]) {
                capabilityAgents[cap] = [];
            }
            capabilityAgents[cap].push(agent.name);
        });
    });
    // Map role capabilities
    types_1.AGENT_ROLES.forEach(role => {
        roleCapabilities[role.id] = [
            ...role.requiredCapabilities,
            ...role.optionalCapabilities,
        ];
    });
    // Find capability gaps
    const capabilityGaps = types_1.AGENT_ROLES.flatMap(role => role.requiredCapabilities.filter(cap => !Array.from(allCapabilities).includes(cap)));
    res.json({
        success: true,
        data: {
            totalAgents: agents.length,
            totalCapabilities: allCapabilities.size,
            capabilityAgents,
            roleCapabilities,
            capabilityGaps: Array.from(new Set(capabilityGaps)),
            coverageAnalysis: {
                fullySupported: types_1.AGENT_ROLES.filter(role => role.requiredCapabilities.every(cap => Array.from(allCapabilities).includes(cap))).map(r => r.id),
                partiallySupportedRoles: types_1.AGENT_ROLES.filter(role => role.requiredCapabilities.some(cap => Array.from(allCapabilities).includes(cap)) && !role.requiredCapabilities.every(cap => Array.from(allCapabilities).includes(cap))).map(r => r.id),
                unsupportedRoles: types_1.AGENT_ROLES.filter(role => !role.requiredCapabilities.some(cap => Array.from(allCapabilities).includes(cap))).map(r => r.id),
            },
        },
    });
}));

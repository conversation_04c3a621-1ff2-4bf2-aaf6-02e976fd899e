import cors from 'cors';
import express from 'express';
import helmet from 'helmet';
import path from 'path';
import { fileURLToPath } from 'url';
import { config } from '../config/index.js';
import { logger } from '../utils/logger.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class WebServer {
  constructor(orchestrator) {
    this.orchestrator = orchestrator;
    this.app = express();
    this.setupMiddleware();
    this.setupRoutes();
  }

  setupMiddleware() {
    this.app.use(helmet());
    this.app.use(cors());
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true }));
    this.app.use(express.static(path.join(__dirname, 'public')));
  }

  setupRoutes() {
    // Health check
    this.app.get('/health', (req, res) => {
      res.json({ status: 'healthy', timestamp: new Date().toISOString() });
    });

    // Main orchestration endpoint
    this.app.post('/api/orchestrate', async (req, res) => {
      try {
        const { query, options = {} } = req.body;

        if (!query) {
          return res.status(400).json({ error: 'Query is required' });
        }

        logger.info('Web API orchestration request', { query, options });

        const result = await this.orchestrator.orchestrate(query, options);

        res.json({
          success: true,
          result: {
            id: result.id,
            query: result.query,
            duration: result.totalDuration,
            steps: result.steps,
            analysis: result.analysis,
            metadata: result.mergedThreads.metadata
          }
        });
      } catch (error) {
        logger.error('Web API orchestration failed', { error: error.message });
        res.status(500).json({
          success: false,
          error: error.message
        });
      }
    });

    // Search threads endpoint
    this.app.post('/api/search', async (req, res) => {
      try {
        const { query, options = {} } = req.body;

        if (!query) {
          return res.status(400).json({ error: 'Query is required' });
        }

        // Get threads
        let threads;
        if (options.useCache) {
          threads = await this.orchestrator.threadRetriever.loadCachedThreads();
          if (threads.length === 0) {
            threads = await this.orchestrator.threadRetriever.retrieveAllThreads();
          }
        } else {
          threads = await this.orchestrator.threadRetriever.retrieveAllThreads();
        }

        // Search
        const searchResults = await this.orchestrator.searchEngine.searchThreads(
          threads,
          query,
          options
        );

        res.json({
          success: true,
          results: searchResults.map(result => ({
            thread: {
              id: result.thread.id,
              source: result.thread.source,
              title: result.thread.title,
              created_at: result.thread.created_at,
              messageCount: result.thread.messages.length,
              highlights: result.thread.highlights
            },
            score: result.combinedScore,
            methods: result.methods
          }))
        });
      } catch (error) {
        logger.error('Web API search failed', { error: error.message });
        res.status(500).json({
          success: false,
          error: error.message
        });
      }
    });

    // Get orchestration history
    this.app.get('/api/history', async (req, res) => {
      try {
        const limit = parseInt(req.query.limit) || 10;
        const history = await this.orchestrator.getOrchestrationHistory(limit);

        res.json({
          success: true,
          history
        });
      } catch (error) {
        logger.error('Web API history failed', { error: error.message });
        res.status(500).json({
          success: false,
          error: error.message
        });
      }
    });

    // Get specific orchestration result
    this.app.get('/api/orchestration/:id', async (req, res) => {
      try {
        const { id } = req.params;
        const result = await this.orchestrator.getOrchestrationResult(id);

        res.json({
          success: true,
          result
        });
      } catch (error) {
        logger.error('Web API get orchestration failed', { error: error.message });
        res.status(500).json({
          success: false,
          error: error.message
        });
      }
    });

    // Get system statistics
    this.app.get('/api/stats', async (req, res) => {
      try {
        const stats = await this.orchestrator.getSystemStats();

        res.json({
          success: true,
          stats
        });
      } catch (error) {
        logger.error('Web API stats failed', { error: error.message });
        res.status(500).json({
          success: false,
          error: error.message
        });
      }
    });

    // Get available platforms
    this.app.get('/api/platforms', async (req, res) => {
      try {
        const platforms = this.orchestrator.universalClient.getClientInfo();
        const availablePlatforms = this.orchestrator.universalClient.getAvailablePlatforms();

        res.json({
          success: true,
          platforms,
          availablePlatforms
        });
      } catch (error) {
        logger.error('Web API platforms failed', { error: error.message });
        res.status(500).json({
          success: false,
          error: error.message
        });
      }
    });

    // Test platform connections
    this.app.post('/api/platforms/test', async (req, res) => {
      try {
        const { platform } = req.body;

        let results;
        if (platform) {
          results = [await this.orchestrator.universalClient.testConnection(platform)];
        } else {
          results = await this.orchestrator.universalClient.testAllConnections();
        }

        res.json({
          success: true,
          results
        });
      } catch (error) {
        logger.error('Web API platform test failed', { error: error.message });
        res.status(500).json({
          success: false,
          error: error.message
        });
      }
    });

    // Retrieve threads
    this.app.post('/api/retrieve', async (req, res) => {
      try {
        const { options = {} } = req.body;

        const threads = await this.orchestrator.threadRetriever.retrieveAllThreads(options);

        res.json({
          success: true,
          threads: threads.map(thread => ({
            id: thread.id,
            source: thread.source,
            title: thread.title,
            created_at: thread.created_at,
            messageCount: thread.messages.length
          })),
          total: threads.length
        });
      } catch (error) {
        logger.error('Web API retrieve failed', { error: error.message });
        res.status(500).json({
          success: false,
          error: error.message
        });
      }
    });

    // Serve the main web interface
    this.app.get('/', (req, res) => {
      res.sendFile(path.join(__dirname, 'public', 'index.html'));
    });

    // 404 handler
    this.app.use((req, res) => {
      res.status(404).json({ error: 'Not found' });
    });

    // Error handler
    this.app.use((error, req, res, next) => {
      logger.error('Web server error', { error: error.message, stack: error.stack });
      res.status(500).json({ error: 'Internal server error' });
    });
  }

  async start(port = config.web.port) {
    return new Promise((resolve, reject) => {
      this.server = this.app.listen(port, config.web.host, (error) => {
        if (error) {
          logger.error('Failed to start web server', { error: error.message });
          reject(error);
        } else {
          logger.info('Web server started', {
            host: config.web.host,
            port,
            url: `http://${config.web.host}:${port}`
          });
          console.log(`\n🌐 Web interface available at: http://${config.web.host}:${port}`);
          resolve();
        }
      });
    });
  }

  async stop() {
    if (this.server) {
      return new Promise((resolve) => {
        this.server.close(() => {
          logger.info('Web server stopped');
          resolve();
        });
      });
    }
  }
}

export default WebServer;

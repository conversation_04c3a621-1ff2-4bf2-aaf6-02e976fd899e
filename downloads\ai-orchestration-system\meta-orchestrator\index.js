#!/usr/bin/env node

/**
 * Meta-Orchestration System Entry Point
 * 
 * Main entry point for the comprehensive meta-orchestration system that merges
 * thread-merging orchestration, code orchestration, and mix-and-match workflows
 * with paid and free AI assistants.
 */

const { Command } = require('commander');
const chalk = require('chalk');
const ora = require('ora');
const inquirer = require('inquirer');

// Import the main meta-orchestrator
const MetaOrchestrator = require('./core/meta-orchestrator');

// Import utilities
const { logger } = require('./utils/logger');
const { validateEnvironment } = require('./utils/environment');

class MetaOrchestrationCLI {
  constructor() {
    this.metaOrchestrator = null;
    this.program = new Command();
    this.setupCommands();
  }
  
  setupCommands() {
    this.program
      .name('meta-orchestrator')
      .description('Meta-Orchestration System for AI Assistants')
      .version('1.0.0');
    
    // Initialize command
    this.program
      .command('init')
      .description('Initialize the meta-orchestration system')
      .option('--force', 'Force reinitialization')
      .option('--config <path>', 'Custom configuration path')
      .action(this.handleInit.bind(this));
    
    // Start command
    this.program
      .command('start')
      .description('Start the meta-orchestration system')
      .option('--daemon', 'Run as daemon')
      .option('--port <port>', 'API server port', '3000')
      .option('--ws-port <port>', 'WebSocket server port', '3001')
      .action(this.handleStart.bind(this));
    
    // Execute command
    this.program
      .command('execute')
      .description('Execute a request through the meta-orchestration system')
      .option('--type <type>', 'Request type (feature, bug-fix, review, etc.)')
      .option('--description <desc>', 'Request description')
      .option('--workflow <workflow>', 'Specific workflow to use')
      .option('--interactive', 'Interactive mode')
      .action(this.handleExecute.bind(this));
    
    // Status command
    this.program
      .command('status')
      .description('Show system status and metrics')
      .option('--detailed', 'Show detailed status')
      .option('--json', 'Output as JSON')
      .action(this.handleStatus.bind(this));
    
    // Config commands
    this.program
      .command('config')
      .description('Configuration management')
      .addCommand(this.createConfigCommands());
    
    // Role management commands
    this.program
      .command('roles')
      .description('Role assignment management')
      .addCommand(this.createRoleCommands());
    
    // Adapter management commands
    this.program
      .command('adapters')
      .description('Adapter management')
      .addCommand(this.createAdapterCommands());
    
    // Workflow management commands
    this.program
      .command('workflows')
      .description('Workflow management')
      .addCommand(this.createWorkflowCommands());
    
    // Dashboard command
    this.program
      .command('dashboard')
      .description('Launch web dashboard')
      .option('--port <port>', 'Dashboard port', '8080')
      .action(this.handleDashboard.bind(this));
    
    // Stop command
    this.program
      .command('stop')
      .description('Stop the meta-orchestration system')
      .action(this.handleStop.bind(this));
  }
  
  createConfigCommands() {
    const configCmd = new Command('config');
    
    configCmd
      .command('show')
      .description('Show current configuration')
      .option('--type <type>', 'Configuration type (main, roles, adapters, etc.)')
      .action(this.handleConfigShow.bind(this));
    
    configCmd
      .command('set')
      .description('Set configuration value')
      .argument('<path>', 'Configuration path (e.g., roles.analyzer.primary)')
      .argument('<value>', 'Configuration value')
      .action(this.handleConfigSet.bind(this));
    
    configCmd
      .command('export')
      .description('Export configuration')
      .option('--output <file>', 'Output file path')
      .action(this.handleConfigExport.bind(this));
    
    configCmd
      .command('import')
      .description('Import configuration')
      .argument('<file>', 'Configuration file path')
      .action(this.handleConfigImport.bind(this));
    
    return configCmd;
  }
  
  createRoleCommands() {
    const roleCmd = new Command('roles');
    
    roleCmd
      .command('list')
      .description('List all role assignments')
      .action(this.handleRolesList.bind(this));
    
    roleCmd
      .command('assign')
      .description('Assign assistant to role')
      .argument('<role>', 'Role name (analyzer, generator, etc.)')
      .argument('<assistant>', 'Assistant ID')
      .option('--fallback', 'Add as fallback instead of primary')
      .action(this.handleRoleAssign.bind(this));
    
    roleCmd
      .command('optimize')
      .description('Optimize role assignments based on performance')
      .action(this.handleRoleOptimize.bind(this));
    
    return roleCmd;
  }
  
  createAdapterCommands() {
    const adapterCmd = new Command('adapters');
    
    adapterCmd
      .command('list')
      .description('List all adapters and their status')
      .action(this.handleAdaptersList.bind(this));
    
    adapterCmd
      .command('test')
      .description('Test adapter availability')
      .argument('[adapter]', 'Specific adapter to test')
      .action(this.handleAdapterTest.bind(this));
    
    adapterCmd
      .command('reload')
      .description('Reload adapter')
      .argument('<adapter>', 'Adapter ID to reload')
      .action(this.handleAdapterReload.bind(this));
    
    return adapterCmd;
  }
  
  createWorkflowCommands() {
    const workflowCmd = new Command('workflows');
    
    workflowCmd
      .command('list')
      .description('List available workflows')
      .action(this.handleWorkflowsList.bind(this));
    
    workflowCmd
      .command('create')
      .description('Create custom workflow')
      .option('--interactive', 'Interactive workflow creation')
      .action(this.handleWorkflowCreate.bind(this));
    
    workflowCmd
      .command('execute')
      .description('Execute specific workflow')
      .argument('<workflow>', 'Workflow ID')
      .option('--description <desc>', 'Request description')
      .action(this.handleWorkflowExecute.bind(this));
    
    return workflowCmd;
  }
  
  async handleInit(options) {
    const spinner = ora('Initializing meta-orchestration system...').start();
    
    try {
      // Validate environment
      await validateEnvironment();
      
      // Initialize meta-orchestrator
      this.metaOrchestrator = new MetaOrchestrator(options);
      await this.metaOrchestrator.initialize();
      
      spinner.succeed('Meta-orchestration system initialized successfully');
      
      console.log(chalk.green('\n✅ System ready! Available commands:'));
      console.log(chalk.cyan('  meta-orchestrator start     - Start the system'));
      console.log(chalk.cyan('  meta-orchestrator execute   - Execute a request'));
      console.log(chalk.cyan('  meta-orchestrator status    - Check system status'));
      console.log(chalk.cyan('  meta-orchestrator dashboard - Launch web dashboard'));
      
    } catch (error) {
      spinner.fail('Failed to initialize meta-orchestration system');
      console.error(chalk.red('Error:', error.message));
      process.exit(1);
    }
  }
  
  async handleStart(options) {
    const spinner = ora('Starting meta-orchestration system...').start();
    
    try {
      if (!this.metaOrchestrator) {
        this.metaOrchestrator = new MetaOrchestrator();
        await this.metaOrchestrator.initialize();
      }
      
      // Start API server if requested
      if (options.daemon) {
        const { APIServer } = require('./api/api-server');
        const apiServer = new APIServer(this.metaOrchestrator);
        await apiServer.start(parseInt(options.port));
        
        const { WebSocketServer } = require('./api/websocket-server');
        const wsServer = new WebSocketServer(this.metaOrchestrator);
        await wsServer.start(parseInt(options.wsPort));
      }
      
      spinner.succeed('Meta-orchestration system started');
      
      if (options.daemon) {
        console.log(chalk.green(`🚀 API Server running on port ${options.port}`));
        console.log(chalk.green(`🔌 WebSocket Server running on port ${options.wsPort}`));
        console.log(chalk.blue('Press Ctrl+C to stop'));
        
        // Keep process alive
        process.on('SIGINT', async () => {
          console.log(chalk.yellow('\n🛑 Shutting down...'));
          await this.metaOrchestrator.shutdown();
          process.exit(0);
        });
      }
      
    } catch (error) {
      spinner.fail('Failed to start meta-orchestration system');
      console.error(chalk.red('Error:', error.message));
      process.exit(1);
    }
  }
  
  async handleExecute(options) {
    try {
      if (!this.metaOrchestrator) {
        this.metaOrchestrator = new MetaOrchestrator();
        await this.metaOrchestrator.initialize();
      }
      
      let request;
      
      if (options.interactive) {
        request = await this.interactiveRequestBuilder();
      } else {
        request = {
          type: options.type || 'feature',
          description: options.description || 'No description provided',
          workflow: options.workflow,
          timestamp: Date.now()
        };
      }
      
      const spinner = ora('Executing request...').start();
      
      const result = await this.metaOrchestrator.orchestrate(request);
      
      spinner.succeed('Request executed successfully');
      
      console.log(chalk.green('\n✅ Execution Results:'));
      console.log(JSON.stringify(result, null, 2));
      
    } catch (error) {
      console.error(chalk.red('❌ Execution failed:', error.message));
      process.exit(1);
    }
  }
  
  async interactiveRequestBuilder() {
    const answers = await inquirer.prompt([
      {
        type: 'list',
        name: 'type',
        message: 'What type of request is this?',
        choices: [
          { name: 'Feature Implementation', value: 'feature' },
          { name: 'Bug Fix', value: 'bug-fix' },
          { name: 'Code Review', value: 'review' },
          { name: 'Refactoring', value: 'refactoring' },
          { name: 'Testing', value: 'testing' }
        ]
      },
      {
        type: 'input',
        name: 'description',
        message: 'Describe your request:',
        validate: input => input.length > 0 || 'Description is required'
      },
      {
        type: 'confirm',
        name: 'includeTests',
        message: 'Include test generation?',
        default: true
      },
      {
        type: 'confirm',
        name: 'includeDocumentation',
        message: 'Include documentation generation?',
        default: true
      },
      {
        type: 'list',
        name: 'complexity',
        message: 'Expected complexity level:',
        choices: ['low', 'medium', 'high'],
        default: 'medium'
      }
    ]);
    
    return {
      ...answers,
      timestamp: Date.now()
    };
  }
  
  async handleStatus(options) {
    try {
      if (!this.metaOrchestrator) {
        console.log(chalk.yellow('⚠️ Meta-orchestration system not running'));
        return;
      }
      
      const status = this.metaOrchestrator.getSystemStatus();
      
      if (options.json) {
        console.log(JSON.stringify(status, null, 2));
      } else {
        this.displayStatus(status, options.detailed);
      }
      
    } catch (error) {
      console.error(chalk.red('❌ Failed to get status:', error.message));
    }
  }
  
  displayStatus(status, detailed = false) {
    console.log(chalk.blue('\n📊 Meta-Orchestration System Status\n'));
    
    console.log(chalk.green(`Status: ${status.status}`));
    console.log(chalk.green(`Uptime: ${Math.floor(status.uptime / 1000)}s`));
    console.log(chalk.green(`Active Workflows: ${status.activeWorkflows}`));
    
    console.log(chalk.blue('\n📈 Metrics:'));
    console.log(`  Total Requests: ${status.metrics.totalRequests}`);
    console.log(`  Successful: ${status.metrics.successfulRequests}`);
    console.log(`  Failed: ${status.metrics.failedRequests}`);
    console.log(`  Success Rate: ${((status.metrics.successfulRequests / status.metrics.totalRequests) * 100 || 0).toFixed(1)}%`);
    console.log(`  Avg Response Time: ${status.metrics.averageResponseTime.toFixed(0)}ms`);
    console.log(`  Fallback Activations: ${status.metrics.fallbackActivations}`);
    
    if (detailed) {
      console.log(chalk.blue('\n🤖 Assistant States:'));
      for (const [assistantId, state] of Object.entries(status.assistantStates)) {
        const statusIcon = state ? '✅' : '❌';
        console.log(`  ${statusIcon} ${assistantId}`);
      }
      
      console.log(chalk.blue('\n🎭 Role Assignments:'));
      for (const [role, assignment] of Object.entries(status.roleAssignments)) {
        console.log(`  ${role}: ${assignment.primary} (fallbacks: ${assignment.fallbacks.join(', ')})`);
      }
    }
  }
  
  async handleConfigShow(options) {
    try {
      if (!this.metaOrchestrator) {
        this.metaOrchestrator = new MetaOrchestrator();
        await this.metaOrchestrator.initialize();
      }
      
      const configManager = this.metaOrchestrator.configManager;
      
      if (options.type) {
        const config = configManager.getConfigValue(options.type);
        console.log(JSON.stringify(config, null, 2));
      } else {
        const allConfig = configManager.exportConfig();
        console.log(JSON.stringify(allConfig, null, 2));
      }
      
    } catch (error) {
      console.error(chalk.red('❌ Failed to show config:', error.message));
    }
  }
  
  async handleDashboard(options) {
    try {
      if (!this.metaOrchestrator) {
        this.metaOrchestrator = new MetaOrchestrator();
        await this.metaOrchestrator.initialize();
      }
      
      const { WebDashboard } = require('./dashboard/web-dashboard');
      const dashboard = new WebDashboard(this.metaOrchestrator);
      
      await dashboard.start(parseInt(options.port));
      
      console.log(chalk.green(`🌐 Dashboard running at http://localhost:${options.port}`));
      console.log(chalk.blue('Press Ctrl+C to stop'));
      
      process.on('SIGINT', async () => {
        console.log(chalk.yellow('\n🛑 Shutting down dashboard...'));
        await dashboard.stop();
        process.exit(0);
      });
      
    } catch (error) {
      console.error(chalk.red('❌ Failed to start dashboard:', error.message));
    }
  }
  
  async handleStop() {
    try {
      if (this.metaOrchestrator) {
        await this.metaOrchestrator.shutdown();
        console.log(chalk.green('✅ Meta-orchestration system stopped'));
      } else {
        console.log(chalk.yellow('⚠️ System not running'));
      }
    } catch (error) {
      console.error(chalk.red('❌ Failed to stop system:', error.message));
    }
  }
  
  async run() {
    try {
      await this.program.parseAsync(process.argv);
    } catch (error) {
      console.error(chalk.red('❌ CLI Error:', error.message));
      process.exit(1);
    }
  }
}

// Main execution
if (require.main === module) {
  const cli = new MetaOrchestrationCLI();
  cli.run().catch(error => {
    console.error(chalk.red('❌ Fatal error:', error.message));
    process.exit(1);
  });
}

module.exports = { MetaOrchestrationCLI, MetaOrchestrator };

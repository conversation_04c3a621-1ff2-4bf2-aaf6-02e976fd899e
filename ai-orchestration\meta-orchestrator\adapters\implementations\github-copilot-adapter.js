/**
 * GitHub Copilot Adapter
 * 
 * Adapter for GitHub Copilot - AI pair programmer
 * Specializes in: code generation, completion, documentation, GitHub integration
 */

const BaseAdapter = require('../base-adapter');
const axios = require('axios');
const { spawn } = require('child_process');
const fs = require('fs').promises;
const path = require('path');
const chalk = require('chalk');

class GitHubCopilotAdapter extends BaseAdapter {
  constructor(config, metaOrchestrator) {
    super(config, metaOrchestrator);
    
    // Set adapter-specific properties
    this.adapterId = 'github-copilot';
    
    // Define capabilities
    this.addCapability('code-generation');
    this.addCapability('completion');
    this.addCapability('documentation');
    this.addCapability('github-integration');
    this.addCapability('pair-programming');
    this.addCapability('suggestions');
    
    // Define supported roles
    this.addSupportedRole('generator');
    this.addSupportedRole('completer');
    this.addSupportedRole('documenter');
    
    // GitHub Copilot specific configuration
    this.apiKey = this.getConfig('apiKey', process.env.GITHUB_TOKEN);
    this.baseUrl = this.getConfig('baseUrl', 'https://api.github.com');
    
    // API client
    this.client = null;
    
    // Copilot CLI path
    this.copilotCliPath = null;
  }
  
  async initialize() {
    try {
      this.log('info', 'Initializing GitHub Copilot adapter...');
      
      // Setup API client
      this.client = axios.create({
        baseURL: this.baseUrl,
        timeout: this.getConfig('timeout', 30000),
        headers: {
          'Accept': 'application/vnd.github.v3+json',
          'User-Agent': 'meta-orchestrator/1.0.0',
          ...(this.apiKey && { 'Authorization': `token ${this.apiKey}` })
        }
      });
      
      // Try to find GitHub CLI and Copilot extension
      await this.findGitHubCLI();
      
      // Test connection
      await this.testConnection();
      
      this.state.initialized = true;
      this.log('info', 'GitHub Copilot adapter initialized successfully');
      
    } catch (error) {
      this.log('error', 'Failed to initialize GitHub Copilot adapter', { error: error.message });
      throw error;
    }
  }
  
  async findGitHubCLI() {
    try {
      // Check if GitHub CLI is available
      const result = await this.executeCommand('gh', ['--version']);
      
      if (result.success) {
        this.log('info', 'GitHub CLI found');
        
        // Check if Copilot extension is installed
        const extensionResult = await this.executeCommand('gh', ['extension', 'list']);
        
        if (extensionResult.success && extensionResult.stdout.includes('github/gh-copilot')) {
          this.copilotCliPath = 'gh';
          this.log('info', 'GitHub Copilot CLI extension found');
        } else {
          this.log('warn', 'GitHub Copilot CLI extension not found - install with: gh extension install github/gh-copilot');
        }
      }
      
    } catch (error) {
      this.log('warn', 'GitHub CLI not found - some features will be limited');
    }
  }
  
  async testConnection() {
    try {
      if (this.apiKey) {
        // Test GitHub API connection
        const response = await this.client.get('/user');
        if (response.status === 200) {
          this.log('info', 'GitHub API connection successful');
          return true;
        }
      }
      
      // If API not available, check CLI
      if (this.copilotCliPath) {
        this.log('info', 'Using GitHub Copilot CLI mode');
        return true;
      }
      
      throw new Error('Neither GitHub API nor Copilot CLI is available');
      
    } catch (error) {
      this.log('warn', 'GitHub Copilot connection test failed', { error: error.message });
      throw error;
    }
  }
  
  async checkAvailability() {
    try {
      // Check API availability
      if (this.apiKey && this.client) {
        try {
          const response = await this.client.get('/user', { timeout: 5000 });
          if (response.status === 200) {
            return true;
          }
        } catch (apiError) {
          this.log('debug', 'API check failed, trying CLI', { error: apiError.message });
        }
      }
      
      // Check CLI availability
      if (this.copilotCliPath) {
        try {
          const result = await this.executeCommand('gh', ['copilot', '--help'], { timeout: 5000 });
          return result.success;
        } catch (cliError) {
          this.log('debug', 'CLI check failed', { error: cliError.message });
        }
      }
      
      return false;
      
    } catch (error) {
      this.log('debug', 'Availability check failed', { error: error.message });
      return false;
    }
  }
  
  async execute(task, context) {
    this.validateTask(task);
    this.validateContext(context);
    
    const { role } = task;
    
    switch (role) {
      case 'generator':
        return await this.performGeneration(task, context);
      case 'completer':
        return await this.performCompletion(task, context);
      case 'documenter':
        return await this.performDocumentation(task, context);
      default:
        throw new Error(`Unsupported role for GitHub Copilot: ${role}`);
    }
  }
  
  async performGeneration(task, context) {
    try {
      this.log('info', 'Performing code generation', { task: task.type });
      
      const generationType = task.generationType || 'function';
      const requirements = task.requirements || task.description;
      
      if (this.copilotCliPath) {
        return await this.generateWithCLI(requirements, context, generationType);
      } else {
        // Fallback to API-based generation (limited)
        return await this.generateWithAPI(requirements, context, generationType);
      }
      
    } catch (error) {
      this.log('error', 'Code generation failed', { error: error.message });
      throw error;
    }
  }
  
  async generateWithCLI(requirements, context, generationType) {
    try {
      // Use GitHub Copilot CLI for code generation
      const prompt = this.buildGenerationPrompt(requirements, context, generationType);
      
      // Create temporary file with context
      const tempDir = path.join(process.cwd(), '.temp');
      await fs.mkdir(tempDir, { recursive: true });
      
      const contextFile = path.join(tempDir, `copilot-context-${Date.now()}.txt`);
      await fs.writeFile(contextFile, prompt);
      
      // Execute Copilot CLI
      const args = ['copilot', 'suggest', '-t', 'shell'];
      
      if (context.language) {
        args.push('--language', context.language);
      }
      
      args.push(prompt);
      
      const result = await this.executeCommand('gh', args);
      
      // Cleanup
      await fs.unlink(contextFile);
      
      if (result.success) {
        return {
          code: this.extractCodeFromCLIOutput(result.stdout),
          explanation: 'Generated using GitHub Copilot CLI',
          method: 'cli',
          suggestions: this.extractSuggestionsFromCLIOutput(result.stdout)
        };
      } else {
        throw new Error(`CLI generation failed: ${result.stderr}`);
      }
      
    } catch (error) {
      this.log('error', 'CLI generation failed', { error: error.message });
      throw error;
    }
  }
  
  async generateWithAPI(requirements, context, generationType) {
    try {
      // GitHub API doesn't have direct Copilot access, so we'll use a workaround
      // This is a simplified implementation - in practice, you'd need GitHub Copilot API access
      
      this.log('warn', 'Using limited API-based generation - consider using CLI for full features');
      
      return {
        code: `// Generated code placeholder for: ${requirements}\n// Use GitHub Copilot CLI for full generation capabilities`,
        explanation: 'Limited API-based generation - use CLI for full features',
        method: 'api-limited',
        suggestions: ['Install GitHub CLI and Copilot extension for full functionality']
      };
      
    } catch (error) {
      this.log('error', 'API generation failed', { error: error.message });
      throw error;
    }
  }
  
  buildGenerationPrompt(requirements, context, type) {
    let prompt = `Generate ${type} for: ${requirements}`;
    
    if (context.language) {
      prompt += ` in ${context.language}`;
    }
    
    if (context.framework) {
      prompt += ` using ${context.framework}`;
    }
    
    if (context.existingCode) {
      prompt += `\n\nExisting code context:\n${context.existingCode}`;
    }
    
    return prompt;
  }
  
  extractCodeFromCLIOutput(output) {
    // Extract code blocks from Copilot CLI output
    const codeBlockRegex = /```[\s\S]*?\n([\s\S]*?)```/g;
    const matches = output.match(codeBlockRegex);
    
    if (matches && matches.length > 0) {
      // Remove the ``` markers and return the code
      return matches[0].replace(/```[\s\S]*?\n/, '').replace(/```$/, '').trim();
    }
    
    // If no code blocks found, return the output as-is
    return output.trim();
  }
  
  extractSuggestionsFromCLIOutput(output) {
    // Extract suggestions from Copilot CLI output
    const suggestions = [];
    
    // Look for suggestion patterns in the output
    const lines = output.split('\n');
    for (const line of lines) {
      if (line.includes('Suggestion:') || line.includes('Alternative:')) {
        suggestions.push(line.trim());
      }
    }
    
    return suggestions;
  }
  
  async performCompletion(task, context) {
    try {
      this.log('info', 'Performing code completion', { task: task.type });
      
      const partialCode = task.partialCode || context.partialCode;
      const cursorPosition = task.cursorPosition || context.cursorPosition;
      
      if (this.copilotCliPath) {
        // Use Copilot CLI for completion
        const args = ['copilot', 'suggest', '-t', 'shell', partialCode];
        const result = await this.executeCommand('gh', args);
        
        if (result.success) {
          const completions = this.extractCompletionsFromCLIOutput(result.stdout);
          return {
            completions,
            method: 'cli'
          };
        } else {
          throw new Error(`CLI completion failed: ${result.stderr}`);
        }
      } else {
        // Limited API-based completion
        return {
          completions: ['// GitHub Copilot CLI required for completions'],
          method: 'api-limited'
        };
      }
      
    } catch (error) {
      this.log('error', 'Code completion failed', { error: error.message });
      throw error;
    }
  }
  
  extractCompletionsFromCLIOutput(output) {
    // Extract multiple completion suggestions from CLI output
    const completions = [];
    
    // Split output into potential completions
    const sections = output.split(/\n\s*\n/);
    
    for (const section of sections) {
      if (section.trim() && !section.includes('Suggestion:')) {
        completions.push(section.trim());
      }
    }
    
    return completions.slice(0, 5); // Return top 5 completions
  }
  
  async performDocumentation(task, context) {
    try {
      this.log('info', 'Performing documentation generation', { task: task.type });
      
      const sourceCode = task.sourceCode || context.sourceCode;
      const documentationType = task.documentationType || 'comprehensive';
      
      const prompt = `Generate ${documentationType} documentation for this code:\n\n${sourceCode}`;
      
      if (this.copilotCliPath) {
        const args = ['copilot', 'suggest', '-t', 'shell', prompt];
        const result = await this.executeCommand('gh', args);
        
        if (result.success) {
          return {
            documentation: this.extractCodeFromCLIOutput(result.stdout),
            method: 'cli'
          };
        } else {
          throw new Error(`CLI documentation failed: ${result.stderr}`);
        }
      } else {
        return {
          documentation: `// Documentation for the provided code\n// Use GitHub Copilot CLI for detailed documentation generation`,
          method: 'api-limited'
        };
      }
      
    } catch (error) {
      this.log('error', 'Documentation generation failed', { error: error.message });
      throw error;
    }
  }
  
  async executeCommand(command, args, options = {}) {
    return new Promise((resolve, reject) => {
      const timeout = options.timeout || 30000;
      
      const child = spawn(command, args, {
        stdio: ['pipe', 'pipe', 'pipe'],
        shell: true
      });
      
      let stdout = '';
      let stderr = '';
      
      child.stdout.on('data', (data) => {
        stdout += data.toString();
      });
      
      child.stderr.on('data', (data) => {
        stderr += data.toString();
      });
      
      const timeoutId = setTimeout(() => {
        child.kill();
        reject(new Error('Command timeout'));
      }, timeout);
      
      child.on('close', (code) => {
        clearTimeout(timeoutId);
        resolve({
          success: code === 0,
          stdout,
          stderr,
          exitCode: code
        });
      });
      
      child.on('error', (error) => {
        clearTimeout(timeoutId);
        reject(error);
      });
    });
  }
  
  validateContext(context) {
    super.validateContext(context);
    
    // GitHub Copilot specific context validation
    if (context.role === 'completer' && !context.partialCode) {
      throw new Error('partialCode is required for completion tasks');
    }
    
    if (context.role === 'documenter' && !context.sourceCode) {
      throw new Error('sourceCode is required for documentation tasks');
    }
    
    return true;
  }
  
  async healthCheck() {
    try {
      // Check if GitHub CLI and Copilot extension are working
      if (this.copilotCliPath) {
        const result = await this.executeCommand('gh', ['copilot', '--help'], { timeout: 5000 });
        return result.success;
      }
      
      // Fallback to API health check
      if (this.apiKey && this.client) {
        const response = await this.client.get('/user', { timeout: 5000 });
        return response.status === 200;
      }
      
      return false;
      
    } catch (error) {
      return false;
    }
  }
  
  async shutdown() {
    this.log('info', 'Shutting down GitHub Copilot adapter');
    
    if (this.client) {
      this.client = null;
    }
    
    await super.shutdown();
  }
}

module.exports = GitHubCopilotAdapter;

import React, { useState } from 'react';
import {
  Container,
  Typography,
  Box,
  Alert,
  CircularProgress,
} from '@mui/material';
import EvolutionDashboard from '../components/EvolutionDashboard';
import { evolutionApi } from '../services/api';

const Evolution: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleStartEvolution = async (parameters: any) => {
    try {
      setIsLoading(true);
      await evolutionApi.startEvolution(parameters);
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to start evolution');
    } finally {
      setIsLoading(false);
    }
  };

  const handleStopEvolution = async () => {
    try {
      setIsLoading(true);
      await evolutionApi.stopEvolution();
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to stop evolution');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = () => {
    // The dashboard handles its own refresh
    setError(null);
  };

  if (isLoading) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ mt: 2, mb: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        🧬 Darwin Gödel Machine
      </Typography>
      <Typography variant="body1" color="text.secondary" gutterBottom>
        Evolutionary algorithm engine for agent optimization and performance enhancement
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      <EvolutionDashboard
        onStartEvolution={handleStartEvolution}
        onStopEvolution={handleStopEvolution}
        onRefresh={handleRefresh}
      />
    </Container>
  );
};

export default Evolution;

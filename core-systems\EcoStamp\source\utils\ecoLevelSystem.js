/**
 * Enhanced Eco-Level Visualization System
 * 5-leaf system with visual indicators and contextual information
 */

// Eco-level thresholds and characteristics
const ECO_LEVELS = {
  1: {
    name: 'Excellent',
    emoji: '🌿',
    color: '#10b981',
    description: 'Minimal environmental impact',
    thresholds: { energy: 0.002, water: 0.02, combined: 2 },
    tips: 'This query was very efficient!'
  },
  2: {
    name: 'Good',
    emoji: '🍃',
    color: '#22c55e',
    description: 'Low environmental impact',
    thresholds: { energy: 0.005, water: 0.05, combined: 5 },
    tips: 'Good efficiency for this type of query.'
  },
  3: {
    name: 'Moderate',
    emoji: '🌱',
    color: '#eab308',
    description: 'Moderate environmental impact',
    thresholds: { energy: 0.01, water: 0.1, combined: 10 },
    tips: 'Average impact for complex queries.'
  },
  4: {
    name: 'Higher',
    emoji: '🍂',
    color: '#f97316',
    description: 'Higher environmental impact',
    thresholds: { energy: 0.02, water: 0.2, combined: 20 },
    tips: 'Consider shorter queries for better efficiency.'
  },
  5: {
    name: 'High',
    emoji: '🍁',
    color: '#ef4444',
    description: 'High environmental impact',
    thresholds: { energy: Infinity, water: Infinity, combined: Infinity },
    tips: 'This was a resource-intensive query.'
  }
};

// Model efficiency ratings (affects eco-level calculation)
const MODEL_EFFICIENCY = {
  'gpt-3.5-turbo': 1.0,
  'gpt-4': 1.3,
  'gpt-4-turbo': 1.5,
  'claude-instant': 0.8,
  'claude-3-haiku': 0.9,
  'claude-3-sonnet': 1.1,
  'claude-3-opus': 1.4,
  'gemini-nano': 0.7,
  'gemini-pro': 1.0,
  'gemini-ultra': 1.6
};

/**
 * Calculate comprehensive eco-level
 * @param {object} impact - Impact calculation data
 * @param {string} model - Detected model name
 * @param {object} context - Additional context (query complexity, etc.)
 * @returns {object} Eco-level information
 */
export function calculateEcoLevel(impact, model = null, context = {}) {
  const energy = impact.total?.energy?.value || 0;
  const water = impact.total?.water?.value || 0;
  const tokens = impact.tokens?.final?.totalTokens || 0;

  // Apply model efficiency multiplier
  const modelMultiplier = MODEL_EFFICIENCY[model] || 1.0;
  
  // Calculate normalized impact scores
  const energyScore = (energy * 1000) * modelMultiplier; // Convert to Wh
  const waterScore = (water * 10) * modelMultiplier; // Scale water impact
  const combinedScore = energyScore + waterScore;

  // Determine base eco-level
  let level = 5;
  for (let i = 1; i <= 5; i++) {
    if (combinedScore <= ECO_LEVELS[i].thresholds.combined) {
      level = i;
      break;
    }
  }

  // Adjust based on context
  if (context.complexity === 'high' && level > 1) {
    level = Math.max(1, level - 1); // Better rating for complex queries
  }
  if (context.hasMultimodal && level < 5) {
    level = Math.min(5, level + 1); // Worse rating for multimodal
  }

  const ecoData = ECO_LEVELS[level];
  
  return {
    level,
    name: ecoData.name,
    emoji: ecoData.emoji,
    color: ecoData.color,
    description: ecoData.description,
    tips: ecoData.tips,
    score: combinedScore,
    breakdown: {
      energy: energyScore,
      water: waterScore,
      tokens,
      modelMultiplier
    },
    visual: generateEcoVisual(level),
    comparison: generateComparison(combinedScore, tokens)
  };
}

/**
 * Generate visual representation of eco-level
 * @param {number} level - Eco-level (1-5)
 * @returns {object} Visual representation data
 */
function generateEcoVisual(level) {
  const activeLeaves = level;
  const inactiveLeaves = 5 - level;
  
  // Create leaf pattern
  const leaves = '🌿'.repeat(Math.max(0, 5 - level + 1)) + 
                 '🍂'.repeat(Math.max(0, level - 1));
  
  // Create progress bar
  const progressBar = '█'.repeat(activeLeaves) + '░'.repeat(inactiveLeaves);
  
  // Create star rating equivalent
  const stars = '★'.repeat(6 - level) + '☆'.repeat(level - 1);
  
  return {
    leaves,
    progressBar,
    stars,
    percentage: ((6 - level) / 5) * 100,
    gradient: generateGradient(level)
  };
}

/**
 * Generate CSS gradient for eco-level
 * @param {number} level - Eco-level
 * @returns {string} CSS gradient
 */
function generateGradient(level) {
  const colors = {
    1: 'linear-gradient(135deg, #10b981, #22c55e)',
    2: 'linear-gradient(135deg, #22c55e, #84cc16)',
    3: 'linear-gradient(135deg, #eab308, #f59e0b)',
    4: 'linear-gradient(135deg, #f97316, #ea580c)',
    5: 'linear-gradient(135deg, #ef4444, #dc2626)'
  };
  
  return colors[level] || colors[3];
}

/**
 * Generate comparison data
 * @param {number} score - Combined impact score
 * @param {number} tokens - Token count
 * @returns {object} Comparison information
 */
function generateComparison(score, tokens) {
  // Real-world equivalents
  const phoneCharges = (score / 1000) * 55.6; // Approximate phone charges
  const lightBulbMinutes = (score / 1000) * 60 / 0.01; // 10W LED bulb minutes
  const waterBottles = (score / 10) * 2; // 500ml bottles
  
  // Efficiency metrics
  const tokensPerWh = tokens / (score / 1000) || 0;
  const efficiency = tokensPerWh > 10000 ? 'Excellent' :
                    tokensPerWh > 5000 ? 'Good' :
                    tokensPerWh > 2000 ? 'Average' :
                    tokensPerWh > 1000 ? 'Below Average' : 'Poor';

  return {
    equivalents: {
      phoneCharges: Math.round(phoneCharges * 100) / 100,
      lightBulbMinutes: Math.round(lightBulbMinutes),
      waterBottles: Math.round(waterBottles * 10) / 10
    },
    efficiency: {
      tokensPerWh: Math.round(tokensPerWh),
      rating: efficiency
    },
    context: generateContextualMessage(score, tokens)
  };
}

/**
 * Generate contextual message based on impact
 * @param {number} score - Impact score
 * @param {number} tokens - Token count
 * @returns {string} Contextual message
 */
function generateContextualMessage(score, tokens) {
  if (score < 2) {
    return "🌟 Exceptionally efficient query!";
  } else if (score < 5) {
    return "✨ Well-optimized interaction.";
  } else if (score < 10) {
    return "📊 Standard efficiency for this complexity.";
  } else if (score < 20) {
    return "⚡ Consider breaking complex queries into parts.";
  } else {
    return "🔋 High-impact query - great for complex tasks!";
  }
}

/**
 * Get eco-level recommendations
 * @param {object} ecoLevel - Eco-level data
 * @param {string} model - Model name
 * @returns {array} Array of recommendations
 */
export function getEcoRecommendations(ecoLevel, model) {
  const recommendations = [];
  
  if (ecoLevel.level >= 4) {
    recommendations.push("💡 Try shorter, more specific queries");
    recommendations.push("🔄 Break complex requests into smaller parts");
    
    if (model && model.includes('gpt-4')) {
      recommendations.push("⚡ Consider using GPT-3.5 for simpler tasks");
    }
    
    if (model && model.includes('opus')) {
      recommendations.push("🚀 Try Claude Sonnet for balanced performance");
    }
  }
  
  if (ecoLevel.level <= 2) {
    recommendations.push("🌟 Great efficiency! This query style is eco-friendly");
    recommendations.push("♻️ Similar queries will have minimal impact");
  }
  
  return recommendations;
}

/**
 * Format eco-level for display
 * @param {object} ecoLevel - Eco-level data
 * @param {string} format - Display format ('compact', 'detailed', 'minimal')
 * @returns {string} Formatted eco-level string
 */
export function formatEcoLevel(ecoLevel, format = 'compact') {
  switch (format) {
    case 'minimal':
      return `${ecoLevel.visual.leaves} ${ecoLevel.level}/5`;
      
    case 'detailed':
      return `${ecoLevel.emoji} Eco-Level: ${ecoLevel.level}/5 (${ecoLevel.name}) - ${ecoLevel.description}`;
      
    case 'compact':
    default:
      return `🌿 Eco-Level: ${ecoLevel.level}/5 Leaves ${ecoLevel.visual.leaves}`;
  }
}

/**
 * Get eco-level color for styling
 * @param {number} level - Eco-level
 * @returns {object} Color information
 */
export function getEcoLevelColors(level) {
  const ecoData = ECO_LEVELS[level] || ECO_LEVELS[3];
  
  return {
    primary: ecoData.color,
    gradient: generateGradient(level),
    text: level <= 2 ? '#065f46' : level <= 3 ? '#92400e' : '#991b1b',
    background: level <= 2 ? '#d1fae5' : level <= 3 ? '#fef3c7' : '#fee2e2'
  };
}

#!/usr/bin/env node

/**
 * 🔒 Universal VS Code Security Scanner Setup
 * One-time setup for enterprise-grade security scanning
 */

import chalk from 'chalk';
import ora from 'ora';
import inquirer from 'inquirer';
import fs from 'fs-extra';
import path from 'path';
import { execSync } from 'child_process';

class SecurityScannerSetup {
  constructor() {
    this.scannerDir = path.dirname(new URL(import.meta.url).pathname);
  }

  async init() {
    console.log(chalk.blue.bold('🔒 Universal VS Code Security Scanner Setup'));
    console.log(chalk.gray('Enterprise-grade security for solo developers'));
    console.log(chalk.gray('=' .repeat(60)));
    console.log('');

    await this.welcomeMessage();
    await this.installDependencies();
    await this.createGlobalCommand();
    await this.setupVSCodeIntegration();
    await this.createDashboard();
    await this.finalInstructions();
  }

  async welcomeMessage() {
    console.log(chalk.cyan('👋 Welcome to the Universal Security Scanner!'));
    console.log('');
    console.log('This tool will set up enterprise-grade security scanning for ALL your VS Code projects:');
    console.log(chalk.green('✅ Vulnerability Detection (CVE Scanning)'));
    console.log(chalk.green('✅ License Compliance Management'));
    console.log(chalk.green('✅ Software Bill of Materials (SBOM)'));
    console.log(chalk.green('✅ Static Code Analysis'));
    console.log(chalk.green('✅ Dependency Analysis'));
    console.log(chalk.green('✅ Professional Security Reports'));
    console.log('');

    const { proceed } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'proceed',
        message: 'Ready to set up universal security scanning?',
        default: true
      }
    ]);

    if (!proceed) {
      console.log(chalk.yellow('Setup cancelled. Run again when ready!'));
      process.exit(0);
    }
  }

  async installDependencies() {
    const spinner = ora('📦 Installing security dependencies...').start();
    
    try {
      execSync('npm install', { 
        cwd: this.scannerDir,
        stdio: 'pipe'
      });
      
      spinner.succeed(chalk.green('✅ Dependencies installed successfully'));
    } catch (error) {
      spinner.fail(chalk.red('❌ Failed to install dependencies'));
      console.log(chalk.red(error.message));
      process.exit(1);
    }
  }

  async createGlobalCommand() {
    const spinner = ora('🌐 Setting up global command...').start();
    
    try {
      // Make scanner executable
      await fs.chmod(path.join(this.scannerDir, 'scanner.js'), '755');
      
      // Create a simple wrapper script for global access
      const wrapperScript = `#!/bin/bash
# Universal VS Code Security Scanner
node "${path.join(this.scannerDir, 'scanner.js')}" "$@"
`;

      const globalScriptPath = path.join(this.scannerDir, 'vscode-security-scan');
      await fs.writeFile(globalScriptPath, wrapperScript);
      await fs.chmod(globalScriptPath, '755');
      
      spinner.succeed(chalk.green('✅ Global command created'));
      
      console.log(chalk.cyan('💡 To use from anywhere, add this to your PATH:'));
      console.log(chalk.yellow(`   export PATH="${this.scannerDir}:$PATH"`));
      console.log(chalk.cyan('   Or add to your ~/.bashrc or ~/.zshrc'));
    } catch (error) {
      spinner.fail(chalk.red('❌ Failed to create global command'));
      console.log(chalk.yellow('⚠️  You can still use the scanner from this directory'));
    }
  }

  async setupVSCodeIntegration() {
    const spinner = ora('🔧 Setting up VS Code integration...').start();
    
    try {
      const vscodeSettings = {
        "tasks": {
          "version": "2.0.0",
          "tasks": [
            {
              "label": "🔒 Security Scan - Quick",
              "type": "shell",
              "command": "node",
              "args": [
                path.join(this.scannerDir, "scanner.js"),
                "--quick"
              ],
              "group": "build",
              "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "new"
              },
              "problemMatcher": []
            },
            {
              "label": "🔒 Security Scan - Full",
              "type": "shell",
              "command": "node",
              "args": [
                path.join(this.scannerDir, "scanner.js"),
                "--full"
              ],
              "group": "build",
              "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "new"
              },
              "problemMatcher": []
            },
            {
              "label": "🔒 Security Report",
              "type": "shell",
              "command": "node",
              "args": [
                path.join(this.scannerDir, "scanner.js"),
                "--report"
              ],
              "group": "build",
              "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "new"
              },
              "problemMatcher": []
            }
          ]
        }
      };

      const tasksPath = path.join(this.scannerDir, 'templates', 'vscode-tasks.json');
      await fs.writeFile(tasksPath, JSON.stringify(vscodeSettings, null, 2));
      
      spinner.succeed(chalk.green('✅ VS Code integration ready'));
      
      console.log(chalk.cyan('💡 To add to your projects:'));
      console.log(chalk.yellow('   Copy templates/vscode-tasks.json to your project\'s .vscode/tasks.json'));
      console.log(chalk.yellow('   Then use Ctrl+Shift+P > "Tasks: Run Task" > "🔒 Security Scan"'));
    } catch (error) {
      spinner.fail(chalk.red('❌ Failed to setup VS Code integration'));
    }
  }

  async createDashboard() {
    const spinner = ora('📊 Creating security dashboard...').start();
    
    try {
      const dashboardHTML = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔒 Universal Security Scanner Dashboard</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #2c5aa0 0%, #1e3c72 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 { font-size: 2.5em; margin-bottom: 10px; }
        .content { padding: 30px; }
        .command-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .command-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #2c5aa0;
            transition: transform 0.3s ease;
        }
        .command-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        .command-title {
            font-size: 1.2em;
            font-weight: bold;
            color: #2c5aa0;
            margin-bottom: 10px;
        }
        .command-code {
            background: #e9ecef;
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
        }
        .features {
            background: #d1ecf1;
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #17a2b8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔒 Universal Security Scanner</h1>
            <p>Enterprise-grade security for all your VS Code projects</p>
            <p><small>Solo Developer Security Solutions</small></p>
        </div>
        
        <div class="content">
            <h2>🚀 Available Commands</h2>
            <div class="command-grid">
                <div class="command-card">
                    <div class="command-title">⚡ Quick Scan</div>
                    <div class="command-code">node scanner.js --quick</div>
                    <p>Fast vulnerability and license check</p>
                </div>
                
                <div class="command-card">
                    <div class="command-title">🔍 Full Scan</div>
                    <div class="command-code">node scanner.js --full</div>
                    <p>Complete security analysis with reports</p>
                </div>
                
                <div class="command-card">
                    <div class="command-title">📊 Generate Report</div>
                    <div class="command-code">node scanner.js --report</div>
                    <p>Create detailed HTML/JSON security report</p>
                </div>
                
                <div class="command-card">
                    <div class="command-title">📦 SBOM Only</div>
                    <div class="command-code">node scanner.js --sbom</div>
                    <p>Generate Software Bill of Materials</p>
                </div>
                
                <div class="command-card">
                    <div class="command-title">📜 License Check</div>
                    <div class="command-code">node scanner.js --licenses</div>
                    <p>Check license compliance only</p>
                </div>
                
                <div class="command-card">
                    <div class="command-title">🛡️ Vulnerabilities</div>
                    <div class="command-code">node scanner.js --vulnerabilities</div>
                    <p>Check for security vulnerabilities only</p>
                </div>
            </div>
            
            <div class="features">
                <h3>🎯 Enterprise Features</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin-top: 15px;">
                    <div>✅ Multi-language Support</div>
                    <div>✅ CVE Detection</div>
                    <div>✅ License Compliance</div>
                    <div>✅ SBOM Generation</div>
                    <div>✅ Code Security Analysis</div>
                    <div>✅ Professional Reports</div>
                    <div>✅ VS Code Integration</div>
                    <div>✅ Project Auto-detection</div>
                </div>
            </div>
            
            <div style="margin-top: 30px; text-align: center; color: #6c757d;">
                <p>🌱 <strong>Universal Security Scanner</strong> - Protecting all your VS Code projects!</p>
                <p><small>Location: ${this.scannerDir}</small></p>
            </div>
        </div>
    </div>
</body>
</html>`;

      await fs.writeFile(path.join(this.scannerDir, 'dashboard.html'), dashboardHTML);
      
      spinner.succeed(chalk.green('✅ Security dashboard created'));
    } catch (error) {
      spinner.fail(chalk.red('❌ Failed to create dashboard'));
    }
  }

  async finalInstructions() {
    console.log('');
    console.log(chalk.green.bold('🎉 Setup Complete!'));
    console.log(chalk.gray('=' .repeat(60)));
    console.log('');
    
    console.log(chalk.cyan.bold('📋 How to Use:'));
    console.log('');
    
    console.log(chalk.yellow('1. Navigate to any VS Code project:'));
    console.log(chalk.gray('   cd /path/to/your/project'));
    console.log('');
    
    console.log(chalk.yellow('2. Run security scan:'));
    console.log(chalk.gray(`   node "${path.join(this.scannerDir, 'scanner.js')}" --quick`));
    console.log(chalk.gray(`   node "${path.join(this.scannerDir, 'scanner.js')}" --full`));
    console.log('');
    
    console.log(chalk.yellow('3. View dashboard:'));
    console.log(chalk.gray(`   Open: ${path.join(this.scannerDir, 'dashboard.html')}`));
    console.log('');
    
    console.log(chalk.cyan.bold('🔧 VS Code Integration:'));
    console.log(chalk.gray('   • Copy templates/vscode-tasks.json to your project\'s .vscode/tasks.json'));
    console.log(chalk.gray('   • Use Ctrl+Shift+P > "Tasks: Run Task" > "🔒 Security Scan"'));
    console.log('');
    
    console.log(chalk.cyan.bold('📚 Supported Project Types:'));
    console.log(chalk.gray('   • Node.js/JavaScript (full support)'));
    console.log(chalk.gray('   • Python (vulnerability scanning)'));
    console.log(chalk.gray('   • Java, Rust, Go, PHP (basic analysis)'));
    console.log('');
    
    console.log(chalk.green.bold('🌟 You now have enterprise-grade security for ALL your projects!'));
    
    // Open dashboard
    const { openDashboard } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'openDashboard',
        message: 'Open the security dashboard now?',
        default: true
      }
    ]);

    if (openDashboard) {
      const dashboardPath = path.join(this.scannerDir, 'dashboard.html');
      console.log(chalk.cyan(`Opening: ${dashboardPath}`));
      
      try {
        // Try to open in default browser
        const { default: open } = await import('open');
        await open(dashboardPath);
      } catch (error) {
        console.log(chalk.yellow(`Please open manually: ${dashboardPath}`));
      }
    }
  }
}

// Run setup
const setup = new SecurityScannerSetup();
setup.init().catch(console.error);

/**
 * MCP Capability Registry API Routes
 * 
 * RESTful API endpoints for Model Context Protocol and Capability Registry
 */

import { Router } from 'express';
import { body, query, param, validationResult } from 'express-validator';
import { PrismaClient } from '@prisma/client';
import { MCPCapabilityRegistryService } from '../services/MCPCapabilityRegistryService';
import { asyncHandler } from '../utils/asyncHandler';
import { authenticate, authorize } from '../middleware/auth';
import { logger } from '../utils/logger';
import {
  CapabilityType,
  ProficiencyLevel,
  AgentStatus,
  RequestPriority
} from '../../shared/types/MCPCapabilityRegistry';

const router = Router();
const prisma = new PrismaClient();
const mcpService = new MCPCapabilityRegistryService(prisma);

// Validation middleware
const validateAgentRegistration = [
  body('name').notEmpty().withMessage('Agent name is required'),
  body('description').optional().isString(),
  body('version').optional().isString(),
  body('capabilities').isArray({ min: 1 }).withMessage('At least one capability is required'),
  body('capabilities.*.type').isIn(Object.values(CapabilityType)).withMessage('Invalid capability type'),
  body('capabilities.*.proficiencyLevel').isIn(Object.values(ProficiencyLevel)).withMessage('Invalid proficiency level'),
  body('capabilities.*.confidence').isFloat({ min: 0, max: 1 }).withMessage('Confidence must be 0-1'),
  body('metadata.provider').optional().isString(),
  body('metadata.model').optional().isString(),
  body('configuration.maxConcurrentRequests').optional().isInt({ min: 1, max: 100 })
];

const validateRequestSubmission = [
  body('type').isIn(Object.values(CapabilityType)).withMessage('Invalid capability type'),
  body('priority').optional().isIn(Object.values(RequestPriority)),
  body('requirements.capabilities').isArray({ min: 1 }).withMessage('At least one capability requirement is required'),
  body('requirements.capabilities.*.type').isIn(Object.values(CapabilityType)),
  body('requirements.capabilities.*.minProficiencyLevel').isIn(Object.values(ProficiencyLevel)),
  body('requirements.capabilities.*.required').isBoolean(),
  body('requirements.capabilities.*.weight').isFloat({ min: 0, max: 1 }),
  body('payload.input').notEmpty().withMessage('Request payload input is required'),
  body('constraints.timeoutMs').optional().isInt({ min: 1000 })
];

const validateCapabilityUpdate = [
  body('capabilities').isArray({ min: 1 }).withMessage('At least one capability is required'),
  body('capabilities.*.type').isIn(Object.values(CapabilityType)),
  body('capabilities.*.proficiencyLevel').isIn(Object.values(ProficiencyLevel)),
  body('capabilities.*.confidence').isFloat({ min: 0, max: 1 })
];

/**
 * POST /api/mcp-capability-registry/agents
 * Register a new agent in the capability registry
 */
router.post('/agents',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR']),
  validateAgentRegistration,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const agentData = req.body;
    const agent = await mcpService.registerAgent(agentData);

    logger.info('Agent registered via API', {
      agentId: agent.id,
      name: agent.name,
      capabilitiesCount: agent.capabilities.length,
      userId: req.user?.id
    });

    res.status(201).json({
      success: true,
      agent: {
        id: agent.id,
        name: agent.name,
        description: agent.description,
        version: agent.version,
        status: agent.status,
        capabilities: agent.capabilities.map(cap => ({
          type: cap.type,
          proficiencyLevel: cap.proficiencyLevel,
          confidence: cap.confidence,
          specializations: cap.specializations
        })),
        trustScore: agent.trustScore,
        reputation: agent.reputation,
        metadata: agent.metadata,
        createdAt: agent.createdAt
      }
    });
  })
);

/**
 * GET /api/mcp-capability-registry/agents
 * Get all registered agents with optional filtering
 */
router.get('/agents',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR', 'AGENT']),
  query('status').optional().isIn(Object.values(AgentStatus)),
  query('capability').optional().isIn(Object.values(CapabilityType)),
  query('minTrustScore').optional().isFloat({ min: 0, max: 1 }),
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    let agents = mcpService.getAllAgents();

    // Apply filters
    if (req.query.status) {
      agents = agents.filter(a => a.status === req.query.status);
    }
    if (req.query.capability) {
      agents = agents.filter(a => 
        a.capabilities.some(cap => cap.type === req.query.capability)
      );
    }
    if (req.query.minTrustScore) {
      const minTrust = parseFloat(req.query.minTrustScore as string);
      agents = agents.filter(a => a.trustScore >= minTrust);
    }

    res.json({
      success: true,
      agents: agents.map(agent => ({
        id: agent.id,
        name: agent.name,
        description: agent.description,
        version: agent.version,
        status: agent.status,
        capabilities: agent.capabilities.map(cap => ({
          type: cap.type,
          proficiencyLevel: cap.proficiencyLevel,
          confidence: cap.confidence
        })),
        trustScore: agent.trustScore,
        reputation: agent.reputation,
        totalRequests: agent.totalRequests,
        successfulRequests: agent.successfulRequests,
        averageRating: agent.averageRating,
        lastActive: agent.lastActive,
        performance: {
          currentLoad: agent.performance.currentLoad,
          averageResponseTime: agent.performance.averageResponseTime,
          recentSuccessRate: agent.performance.recentSuccessRate,
          recentQualityScore: agent.performance.recentQualityScore
        },
        availability: {
          isAvailable: agent.availability.isAvailable,
          estimatedWaitTime: agent.availability.estimatedWaitTime,
          healthStatus: agent.availability.healthStatus.status
        }
      })),
      count: agents.length
    });
  })
);

/**
 * GET /api/mcp-capability-registry/agents/:agentId
 * Get specific agent details
 */
router.get('/agents/:agentId',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR', 'AGENT']),
  param('agentId').notEmpty().withMessage('Agent ID is required'),
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { agentId } = req.params;
    const agent = mcpService.getAgent(agentId);

    if (!agent) {
      return res.status(404).json({
        success: false,
        error: 'Agent not found'
      });
    }

    res.json({
      success: true,
      agent
    });
  })
);

/**
 * PUT /api/mcp-capability-registry/agents/:agentId/status
 * Update agent status
 */
router.put('/agents/:agentId/status',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR']),
  param('agentId').notEmpty().withMessage('Agent ID is required'),
  body('status').isIn(Object.values(AgentStatus)).withMessage('Invalid agent status'),
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { agentId } = req.params;
    const { status } = req.body;

    await mcpService.updateAgentStatus(agentId, status);

    logger.info('Agent status updated via API', {
      agentId,
      status,
      userId: req.user?.id
    });

    res.json({
      success: true,
      message: 'Agent status updated successfully',
      agentId,
      status
    });
  })
);

/**
 * PUT /api/mcp-capability-registry/agents/:agentId/capabilities
 * Update agent capabilities
 */
router.put('/agents/:agentId/capabilities',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR']),
  param('agentId').notEmpty().withMessage('Agent ID is required'),
  validateCapabilityUpdate,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { agentId } = req.params;
    const { capabilities } = req.body;

    // Add timestamps and validation
    const updatedCapabilities = capabilities.map((cap: any) => ({
      ...cap,
      lastUpdated: new Date(),
      certifications: cap.certifications || [],
      specializations: cap.specializations || [],
      limitations: cap.limitations || [],
      prerequisites: cap.prerequisites || [],
      estimatedPerformance: cap.estimatedPerformance || {
        averageExecutionTime: 5000,
        successRate: 0.9,
        qualityScore: 0.8,
        resourceEfficiency: 0.7,
        userSatisfactionScore: 0.8,
        errorRate: 0.1,
        throughput: 10,
        reliability: 0.9
      }
    }));

    await mcpService.updateAgentCapabilities(agentId, updatedCapabilities);

    logger.info('Agent capabilities updated via API', {
      agentId,
      capabilitiesCount: capabilities.length,
      userId: req.user?.id
    });

    res.json({
      success: true,
      message: 'Agent capabilities updated successfully',
      agentId,
      capabilities: updatedCapabilities
    });
  })
);

/**
 * POST /api/mcp-capability-registry/requests
 * Submit a new request for processing
 */
router.post('/requests',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR', 'AGENT', 'USER']),
  validateRequestSubmission,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const requestData = {
      ...req.body,
      requesterInfo: {
        id: req.user?.id || 'anonymous',
        name: req.user?.name || 'Anonymous User',
        role: req.user?.role || 'USER',
        contactInfo: {},
        preferences: req.body.requesterInfo?.preferences || {
          preferredAgents: [],
          excludedAgents: [],
          qualityOverSpeed: false,
          costSensitive: false,
          notificationSettings: {
            onAssignment: true,
            onProgress: false,
            onCompletion: true,
            onFailure: true,
            channels: []
          }
        }
      }
    };

    const request = await mcpService.submitRequest(requestData);

    logger.info('Request submitted via API', {
      requestId: request.id,
      type: request.type,
      priority: request.priority,
      selectedAgent: request.routing.selectedAgent,
      userId: req.user?.id
    });

    res.status(201).json({
      success: true,
      request: {
        id: request.id,
        type: request.type,
        priority: request.priority,
        status: request.status,
        selectedAgent: request.routing.selectedAgent,
        estimatedCompletionTime: new Date(Date.now() + 30000), // Mock estimation
        createdAt: request.createdAt
      }
    });
  })
);

/**
 * POST /api/mcp-capability-registry/requests/:requestId/process
 * Process a specific request
 */
router.post('/requests/:requestId/process',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR']),
  param('requestId').notEmpty().withMessage('Request ID is required'),
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { requestId } = req.params;

    const response = await mcpService.processRequest(requestId);

    logger.info('Request processed via API', {
      requestId,
      status: response.status,
      agentId: response.agentId,
      executionTime: response.performance.executionTime,
      userId: req.user?.id
    });

    res.json({
      success: true,
      response: {
        requestId: response.requestId,
        status: response.status,
        agentId: response.agentId,
        result: response.result,
        error: response.error,
        quality: response.quality,
        performance: response.performance,
        createdAt: response.createdAt
      }
    });
  })
);

/**
 * GET /api/mcp-capability-registry/capabilities
 * Get available capabilities and their distribution
 */
router.get('/capabilities',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR', 'AGENT']),
  asyncHandler(async (req, res) => {
    const statistics = mcpService.getRegistryStatistics();

    res.json({
      success: true,
      capabilities: Object.values(CapabilityType).map(type => ({
        type,
        agentCount: statistics.capabilityDistribution[type] || 0,
        proficiencyLevels: Object.values(ProficiencyLevel)
      })),
      distribution: statistics.capabilityDistribution
    });
  })
);

/**
 * GET /api/mcp-capability-registry/agents/by-capability/:capability
 * Get agents by specific capability
 */
router.get('/agents/by-capability/:capability',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR', 'AGENT']),
  param('capability').isIn(Object.values(CapabilityType)).withMessage('Invalid capability type'),
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { capability } = req.params;
    const agents = mcpService.getAgentsByCapability(capability as CapabilityType);

    res.json({
      success: true,
      capability,
      agents: agents.map(agent => ({
        id: agent.id,
        name: agent.name,
        status: agent.status,
        trustScore: agent.trustScore,
        reputation: agent.reputation,
        capabilities: agent.capabilities.filter(cap => cap.type === capability),
        performance: {
          currentLoad: agent.performance.currentLoad,
          averageResponseTime: agent.performance.averageResponseTime,
          recentSuccessRate: agent.performance.recentSuccessRate
        },
        availability: {
          isAvailable: agent.availability.isAvailable,
          estimatedWaitTime: agent.availability.estimatedWaitTime
        }
      })),
      count: agents.length
    });
  })
);

/**
 * GET /api/mcp-capability-registry/statistics
 * Get capability registry statistics
 */
router.get('/statistics',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR']),
  asyncHandler(async (req, res) => {
    const statistics = mcpService.getRegistryStatistics();

    res.json({
      success: true,
      statistics
    });
  })
);

/**
 * GET /api/mcp-capability-registry/health
 * Get MCP system health status
 */
router.get('/health',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR']),
  asyncHandler(async (req, res) => {
    const statistics = mcpService.getRegistryStatistics();

    const health = {
      status: 'healthy',
      timestamp: new Date(),
      metrics: {
        totalAgents: statistics.totalAgents,
        availableAgents: statistics.availableAgents,
        busyAgents: statistics.busyAgents,
        offlineAgents: statistics.offlineAgents,
        averageTrustScore: statistics.averageTrustScore,
        totalRequests: statistics.totalRequests,
        successfulRequests: statistics.successfulRequests,
        successRate: statistics.totalRequests > 0 ? 
          (statistics.successfulRequests / statistics.totalRequests) * 100 : 0,
        averageResponseTime: statistics.averageResponseTime,
        systemLoad: statistics.systemLoad
      },
      performance: {
        requestThroughput: 50, // Mock value
        agentUtilization: statistics.systemLoad,
        responseTimeP95: statistics.averageResponseTime * 1.5 // Mock value
      },
      issues: [] as string[],
      warnings: [] as string[]
    };

    // Add health warnings
    if (health.metrics.availableAgents < 3) {
      health.warnings.push('Low number of available agents');
    }

    if (health.metrics.averageTrustScore < 0.6) {
      health.warnings.push('Average trust score is below recommended level');
    }

    if (health.metrics.successRate < 80) {
      health.issues.push('Request success rate is below acceptable level');
    }

    if (health.metrics.systemLoad > 0.8) {
      health.issues.push('System load is high');
    }

    res.json({
      success: true,
      health
    });
  })
);

export default router;

{"evolution": {"populationSize": 15, "maxGenerations": 50, "targetFitness": 0.9, "offspringCount": 8, "mutationRate": 0.3, "crossoverRate": 0.7, "elitismRate": 0.1, "diversityThreshold": 0.8, "stagnationLimit": 10}, "genetics": {"selectionMethod": "tournament", "tournamentSize": 3, "mutationStrategies": ["error_handling", "performance_optimization", "new_feature", "algorithm_improvement", "code_refactoring"], "crossoverStrategies": ["single_point", "two_point", "uniform", "semantic"]}, "evaluation": {"benchmarkSuites": ["custom-orchestration", "performance-tests", "reliability"], "fitnessWeights": {"performance": 0.4, "reliability": 0.3, "functionality": 0.2, "safety": 0.1}, "validationCriteria": {"syntaxCheck": true, "securityScan": true, "performanceThreshold": 0.8, "reliabilityThreshold": 0.9, "safetyThreshold": 0.95}, "timeoutLimits": {"agentExecution": 30000, "benchmarkSuite": 300000, "validation": 10000}}, "archive": {"storageType": "git", "maxVersions": 500, "compressionEnabled": true, "backupInterval": 3600000, "genealogyDepth": 10, "performanceHistoryLength": 50}, "execution": {"sandboxDir": "./dgm-sandbox", "isolationLevel": "process", "resourceLimits": {"memory": "256MB", "cpu": "1", "timeout": 30000}, "allowedModules": ["fs", "path", "crypto", "util", "events", "stream"], "blockedModules": ["child_process", "cluster", "dgram", "net", "tls"]}, "safety": {"enabled": true, "humanApprovalRequired": true, "approvalThreshold": 0.8, "rollbackOnFailure": true, "maxConsecutiveFailures": 3, "quarantineEnabled": true, "auditLogging": true, "changeReviewRequired": true, "maxBackups": 20}, "monitoring": {"metricsCollection": true, "performanceTracking": true, "errorTracking": true, "evolutionHistory": true, "dashboardEnabled": true, "alerting": {"enabled": true, "channels": ["console", "file"], "thresholds": {"fitnessDecline": 0.1, "errorRate": 0.05, "performanceDegradation": 0.2}}}, "integration": {"mode": "hybrid", "migrationPhase": "testing", "augmentCode": {"enabled": true, "apiEndpoint": "http://localhost:3001", "timeout": 30000}, "orchestrationSystem": {"enabled": true, "backupBeforeModification": true, "testBeforeDeployment": true}, "externalBenchmarks": {"swebench": {"enabled": false, "endpoint": "https://api.swebench.com"}, "polyglot": {"enabled": false, "endpoint": "https://api.polyglot.com"}}}, "ui": {"webDashboard": {"enabled": true, "port": 3002, "autoRefresh": 5000}, "cli": {"enabled": true, "colorOutput": true, "verboseLogging": false}, "api": {"enabled": true, "port": 3003, "authentication": false}}}
"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.authRoutes = void 0;
const express_1 = require("express");
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const client_1 = require("@prisma/client");
const zod_1 = require("zod");
const config_1 = require("../config");
const errorHandler_1 = require("../middleware/errorHandler");
const logger_1 = require("../utils/logger");
const router = (0, express_1.Router)();
exports.authRoutes = router;
const prisma = new client_1.PrismaClient();
// Validation schemas
const registerSchema = zod_1.z.object({
    email: zod_1.z.string().email('Invalid email format'),
    username: zod_1.z.string().min(3, 'Username must be at least 3 characters').max(50, 'Username too long'),
    password: zod_1.z.string().min(8, 'Password must be at least 8 characters'),
});
const loginSchema = zod_1.z.object({
    email: zod_1.z.string().email('Invalid email format'),
    password: zod_1.z.string().min(1, 'Password is required'),
});
// Register new user
router.post('/register', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { email, username, password } = registerSchema.parse(req.body);
    // Check if user already exists
    const existingUser = await prisma.user.findFirst({
        where: {
            OR: [
                { email },
                { username },
            ],
        },
    });
    if (existingUser) {
        throw new errorHandler_1.AppError('User with this email or username already exists', 400);
    }
    // Hash password
    const hashedPassword = await bcryptjs_1.default.hash(password, 12);
    // Create user
    const user = await prisma.user.create({
        data: {
            email,
            username,
            password: hashedPassword,
            role: 'USER', // Default role
        },
        select: {
            id: true,
            email: true,
            username: true,
            role: true,
            createdAt: true,
        },
    });
    // Generate JWT token
    const token = jsonwebtoken_1.default.sign({ userId: user.id }, config_1.config.jwt.secret, { expiresIn: config_1.config.jwt.expiresIn });
    logger_1.logger.info(`New user registered: ${username} (${email})`);
    res.status(201).json({
        success: true,
        data: {
            user,
            token,
        },
        message: 'User registered successfully',
    });
}));
// Login user
router.post('/login', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { email, password } = loginSchema.parse(req.body);
    // Find user
    const user = await prisma.user.findUnique({
        where: { email },
    });
    if (!user) {
        throw new errorHandler_1.AppError('Invalid email or password', 401);
    }
    // Check password
    const isPasswordValid = await bcryptjs_1.default.compare(password, user.password);
    if (!isPasswordValid) {
        throw new errorHandler_1.AppError('Invalid email or password', 401);
    }
    // Generate JWT token
    const token = jsonwebtoken_1.default.sign({ userId: user.id }, config_1.config.jwt.secret, { expiresIn: config_1.config.jwt.expiresIn });
    logger_1.logger.info(`User logged in: ${user.username} (${email})`);
    res.json({
        success: true,
        data: {
            user: {
                id: user.id,
                email: user.email,
                username: user.username,
                role: user.role,
                createdAt: user.createdAt,
            },
            token,
        },
        message: 'Login successful',
    });
}));
// Get current user profile
router.get('/me', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        throw new errorHandler_1.AppError('Access token required', 401);
    }
    const token = authHeader.substring(7);
    try {
        const decoded = jsonwebtoken_1.default.verify(token, config_1.config.jwt.secret);
        const user = await prisma.user.findUnique({
            where: { id: decoded.userId },
            select: {
                id: true,
                email: true,
                username: true,
                role: true,
                createdAt: true,
                updatedAt: true,
            },
        });
        if (!user) {
            throw new errorHandler_1.AppError('User not found', 404);
        }
        res.json({
            success: true,
            data: user,
        });
    }
    catch (jwtError) {
        throw new errorHandler_1.AppError('Invalid or expired token', 401);
    }
}));
// Refresh token
router.post('/refresh', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        throw new errorHandler_1.AppError('Access token required', 401);
    }
    const token = authHeader.substring(7);
    try {
        const decoded = jsonwebtoken_1.default.verify(token, config_1.config.jwt.secret);
        // Generate new token
        const newToken = jsonwebtoken_1.default.sign({ userId: decoded.userId }, config_1.config.jwt.secret, { expiresIn: config_1.config.jwt.expiresIn });
        res.json({
            success: true,
            data: {
                token: newToken,
            },
            message: 'Token refreshed successfully',
        });
    }
    catch (jwtError) {
        throw new errorHandler_1.AppError('Invalid or expired token', 401);
    }
}));

"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const http_1 = require("http");
const socket_io_1 = require("socket.io");
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const client_1 = require("@prisma/client");
const config_1 = require("./config");
const logger_1 = require("./utils/logger");
const EventBus_1 = require("./services/EventBus");
const SocketService_1 = require("./services/SocketService");
const errorHandler_1 = require("./middleware/errorHandler");
const auth_1 = require("./middleware/auth");
// Import route handlers
const orchestrator_1 = require("./routes/orchestrator");
const agent_1 = require("./routes/agent");
const tunnel_1 = require("./routes/tunnel");
const workflow_1 = require("./routes/workflow");
const evolution_1 = require("./routes/evolution");
const audit_1 = require("./routes/audit");
const context_1 = require("./routes/context");
const auth_2 = require("./routes/auth");
class AugmentOrchestrationServer {
    constructor() {
        this.app = (0, express_1.default)();
        this.server = (0, http_1.createServer)(this.app);
        this.prisma = new client_1.PrismaClient();
        this.eventBus = new EventBus_1.EventBus();
        // Initialize Socket.IO first
        this.io = new socket_io_1.Server(this.server, {
            cors: {
                origin: process.env.CORS_ORIGIN || "http://localhost:3000",
                methods: ["GET", "POST"],
                credentials: true
            }
        });
        this.socketService = new SocketService_1.SocketService(this.io, this.eventBus);
        this.setupMiddleware();
        this.setupRoutes();
        this.setupErrorHandling();
    }
    setupMiddleware() {
        // Security middleware
        this.app.use((0, helmet_1.default)({
            contentSecurityPolicy: {
                directives: {
                    defaultSrc: ["'self'"],
                    styleSrc: ["'self'", "'unsafe-inline'"],
                    scriptSrc: ["'self'"],
                    imgSrc: ["'self'", "data:", "https:"],
                    connectSrc: ["'self'", "ws:", "wss:"],
                },
            },
        }));
        // CORS
        this.app.use((0, cors_1.default)(config_1.config.server.cors));
        // Rate limiting
        const limiter = (0, express_rate_limit_1.default)({
            windowMs: config_1.config.server.rateLimit.windowMs,
            max: config_1.config.server.rateLimit.max,
            message: 'Too many requests from this IP, please try again later.',
            standardHeaders: true,
            legacyHeaders: false,
        });
        this.app.use('/api/', limiter);
        // Body parsing
        this.app.use(express_1.default.json({ limit: '10mb' }));
        this.app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
        // Request logging
        this.app.use((req, res, next) => {
            logger_1.logger.info(`${req.method} ${req.path}`, {
                ip: req.ip,
                userAgent: req.get('User-Agent'),
            });
            next();
        });
        logger_1.logger.info('Middleware setup completed');
    }
    setupRoutes() {
        // Health check
        this.app.get('/health', (req, res) => {
            res.json({
                status: 'healthy',
                timestamp: new Date().toISOString(),
                version: process.env.npm_package_version || '1.0.0',
            });
        });
        // Authentication routes (no auth required)
        this.app.use('/api/auth', auth_2.authRoutes);
        // Protected API routes
        this.app.use('/api/orchestrators', auth_1.authMiddleware, orchestrator_1.orchestratorRoutes);
        this.app.use('/api/agents', auth_1.authMiddleware, agent_1.agentRoutes);
        this.app.use('/api/tunnels', auth_1.authMiddleware, tunnel_1.tunnelRoutes);
        this.app.use('/api/workflows', auth_1.authMiddleware, workflow_1.workflowRoutes);
        this.app.use('/api/evolution', auth_1.authMiddleware, evolution_1.evolutionRoutes);
        this.app.use('/api/audit', auth_1.authMiddleware, audit_1.auditRoutes);
        this.app.use('/api/context', auth_1.authMiddleware, context_1.contextRoutes);
        // Serve static files in production
        if (process.env.NODE_ENV === 'production') {
            this.app.use(express_1.default.static('client/dist'));
            this.app.get('*', (req, res) => {
                res.sendFile('index.html', { root: 'client/dist' });
            });
        }
        logger_1.logger.info('Routes setup completed');
    }
    setupErrorHandling() {
        this.app.use(errorHandler_1.errorHandler);
        process.on('unhandledRejection', (reason, promise) => {
            logger_1.logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
        });
        process.on('uncaughtException', (error) => {
            logger_1.logger.error('Uncaught Exception:', error);
            process.exit(1);
        });
        logger_1.logger.info('Error handling setup completed');
    }
    async start() {
        try {
            // Connect to database
            await this.prisma.$connect();
            logger_1.logger.info('Database connected successfully');
            // Start server
            this.server.listen(config_1.config.server.port, config_1.config.server.host, () => {
                logger_1.logger.info(`🚀 Augment Orchestration Server running at http://${config_1.config.server.host}:${config_1.config.server.port}`);
                logger_1.logger.info(`📊 Dashboard: http://${config_1.config.server.host}:${config_1.config.server.port}`);
                logger_1.logger.info(`🔌 WebSocket connections: 0`);
            });
            // Initialize services
            await this.eventBus.initialize();
            await this.socketService.initialize();
            logger_1.logger.info('Server started successfully');
        }
        catch (error) {
            logger_1.logger.error('Failed to start server:', error);
            process.exit(1);
        }
    }
    async stop() {
        try {
            await this.prisma.$disconnect();
            this.server.close();
            logger_1.logger.info('Server stopped successfully');
        }
        catch (error) {
            logger_1.logger.error('Error stopping server:', error);
        }
    }
}
// Start server
const server = new AugmentOrchestrationServer();
if (require.main === module) {
    server.start().catch((error) => {
        logger_1.logger.error('Failed to start server:', error);
        process.exit(1);
    });
}
exports.default = server;

# Darwin Gödel Machine (DGM) Documentation

## Overview

The Darwin Gödel Machine (DGM) is a revolutionary self-improving AI orchestration system that automatically evolves and optimizes its own code through evolutionary algorithms and machine learning. It represents the next generation of AI orchestration, capable of continuous self-improvement and adaptation.

## Table of Contents

1. [Quick Start](#quick-start)
2. [Architecture](#architecture)
3. [Installation](#installation)
4. [Configuration](#configuration)
5. [Usage Examples](#usage-examples)
6. [API Reference](#api-reference)
7. [Safety Features](#safety-features)
8. [Troubleshooting](#troubleshooting)
9. [Contributing](#contributing)

## Quick Start

### Basic Usage

```javascript
const { DarwinGodelMachine } = require('./dgm');

// Quick start with default settings
const dgm = await DarwinGodelMachine.quickStart();

// The system is now running with:
// - Web dashboard at http://localhost:3002
// - API server at http://localhost:3003
// - Evolution process started
```

### Integration with Existing Systems

```javascript
const { DarwinGodelMachine } = require('./dgm');
const existingOrchestrator = require('./orchestrator');
const threadMergingOrchestrator = require('./thread-merging-orchestrator');

// Create DGM instance with existing systems
const dgm = await DarwinGodelMachine.create(
  existingOrchestrator,
  threadMergingOrchestrator,
  {
    integrationMode: 'hybrid',
    migrationPhase: 'testing'
  }
);

// Start the system
await dgm.start();
```

## Architecture

The DGM system consists of several interconnected components:

### Core Components

1. **DGM Engine** (`core/dgm-engine.js`)
   - Main orchestration engine
   - Coordinates evolution cycles
   - Manages agent populations

2. **Agent Manager** (`core/agent-manager.js`)
   - Agent lifecycle management
   - Code generation and modification
   - Execution environment management

3. **Archive System** (`archive/`)
   - Git-based version control
   - Agent genealogy tracking
   - Performance history storage

4. **Evolution Framework** (`evolution/`)
   - Genetic algorithms
   - Mutation and crossover operations
   - Selection strategies

5. **Evaluation System** (`evaluation/`)
   - Comprehensive benchmarking
   - Performance metrics collection
   - Safety validation

### User Interfaces

1. **Web Dashboard** (`interface/web-dashboard.js`)
   - Real-time monitoring
   - Visual genealogy trees
   - Human oversight controls

2. **CLI Interface** (`interface/cli-interface.js`)
   - Command-line control
   - Batch operations
   - Scripting support

3. **REST API** (`interface/api-server.js`)
   - External integration
   - Programmatic access
   - Webhook support

### Safety Systems

1. **Safety Manager** (`safety/safety-manager.js`)
   - Human-in-the-loop oversight
   - Automatic rollback mechanisms
   - Risk assessment and quarantine

## Installation

### Prerequisites

- Node.js 16+ 
- Git (for version control)
- 4GB+ RAM (recommended)
- 10GB+ disk space (for archives)

### Setup

1. **Install Dependencies**
   ```bash
   cd ai-orchestration
   npm install
   ```

2. **Initialize DGM**
   ```bash
   npm run dgm:init
   ```

3. **Start DGM**
   ```bash
   npm run dgm:start
   ```

## Configuration

### Basic Configuration

Create `ai-orchestration/dgm/config/dgm-config.json`:

```json
{
  "evolution": {
    "populationSize": 20,
    "maxGenerations": 100,
    "targetFitness": 0.95,
    "mutationRate": 0.3,
    "crossoverRate": 0.7
  },
  "safety": {
    "humanApprovalRequired": true,
    "approvalThreshold": 0.8,
    "maxConsecutiveFailures": 3
  },
  "ui": {
    "webDashboard": {
      "enabled": true,
      "port": 3002
    },
    "api": {
      "enabled": true,
      "port": 3003
    }
  }
}
```

### Advanced Configuration

```json
{
  "genetics": {
    "selectionMethod": "tournament",
    "tournamentSize": 3,
    "mutationStrategies": [
      "error_handling",
      "performance_optimization",
      "new_feature",
      "algorithm_improvement"
    ]
  },
  "evaluation": {
    "benchmarkSuites": [
      "swe-bench",
      "polyglot",
      "custom-orchestration"
    ],
    "fitnessWeights": {
      "performance": 0.4,
      "reliability": 0.3,
      "functionality": 0.2,
      "safety": 0.1
    }
  },
  "integration": {
    "mode": "hybrid",
    "migrationPhase": "gradual",
    "augmentCode": {
      "enabled": true,
      "apiEndpoint": "http://localhost:3001"
    }
  }
}
```

## Usage Examples

### Example 1: Basic Evolution

```javascript
const { DarwinGodelMachine } = require('./dgm');

async function basicEvolution() {
  // Initialize DGM
  const dgm = new DarwinGodelMachine();
  await dgm.initialize();
  
  // Start evolution with custom parameters
  await dgm.startEvolution({
    maxGenerations: 50,
    targetFitness: 0.9
  });
  
  // Monitor progress
  dgm.integration.dgmEngine.on('generationCompleted', (generation) => {
    console.log(`Generation ${generation.number}: Best fitness ${generation.bestFitness}`);
  });
}
```

### Example 2: Custom Benchmarking

```javascript
async function customBenchmarking() {
  const dgm = await DarwinGodelMachine.quickStart();
  
  // Add custom benchmark
  dgm.integration.dgmEngine.benchmarkSuite.addBenchmark('custom-test', {
    name: 'Custom Performance Test',
    description: 'Tests specific functionality',
    testCases: [
      {
        name: 'Custom Test Case',
        description: 'Test custom functionality',
        input: { /* test input */ },
        expectedOutput: { /* expected output */ },
        validation: [
          { type: 'field_exists', field: 'result' },
          { type: 'execution_time', maxTime: 5000 }
        ]
      }
    ]
  });
}
```

### Example 3: Safety Management

```javascript
async function safetyManagement() {
  const dgm = await DarwinGodelMachine.create(null, null, {
    safety: {
      humanApprovalRequired: true,
      approvalThreshold: 0.8
    }
  });
  
  // Handle approval requests
  dgm.integration.dgmEngine.on('approvalRequired', async (request) => {
    console.log(`Approval required for agent ${request.agentId}`);
    console.log(`Risk level: ${request.riskAssessment.riskLevel}`);
    
    // Auto-approve low-risk agents
    if (request.riskAssessment.riskLevel === 'low') {
      await dgm.integration.dgmEngine.safetyManager.approveAgent(
        request.agentId, 
        'auto-approval'
      );
    }
  });
}
```

### Example 4: Data Export and Analysis

```javascript
async function dataAnalysis() {
  const dgm = await DarwinGodelMachine.quickStart();
  
  // Wait for some evolution
  await new Promise(resolve => setTimeout(resolve, 60000));
  
  // Export evolution data
  const evolutionData = await dgm.exportData('all');
  
  // Analyze best performing agents
  const bestAgents = evolutionData.agents
    .sort((a, b) => b.fitness - a.fitness)
    .slice(0, 5);
  
  console.log('Top 5 agents:');
  bestAgents.forEach((agent, index) => {
    console.log(`${index + 1}. ${agent.id}: ${agent.fitness.toFixed(3)}`);
  });
  
  // Get genealogy insights
  const genealogy = evolutionData.genealogy;
  console.log(`Total lineages: ${genealogy.lineages}`);
  console.log(`Average children per agent: ${genealogy.averageChildren}`);
}
```

## API Reference

### DarwinGodelMachine Class

#### Methods

- `initialize(existingOrchestrator?, threadMergingOrchestrator?)` - Initialize the DGM system
- `start(options?)` - Start the DGM system
- `stop()` - Stop the DGM system
- `startEvolution(options?)` - Start evolution process
- `stopEvolution()` - Stop evolution process
- `startCLI()` - Start CLI interface
- `getStatus()` - Get system status
- `getStats()` - Get performance statistics
- `getBestAgent()` - Get current best agent
- `exportData(type?)` - Export system data

#### Static Methods

- `create(existingOrchestrator?, threadMergingOrchestrator?, options?)` - Create DGM instance
- `quickStart(options?)` - Quick start with defaults

### REST API Endpoints

#### System Control
- `GET /api/v1/status` - Get system status
- `POST /api/v1/evolution/start` - Start evolution
- `POST /api/v1/evolution/stop` - Stop evolution

#### Agent Management
- `GET /api/v1/agents` - List agents
- `GET /api/v1/agents/:id` - Get agent details
- `POST /api/v1/agents/:id/approve` - Approve agent
- `POST /api/v1/agents/:id/reject` - Reject agent

#### Data Access
- `GET /api/v1/genealogy` - Get genealogy data
- `GET /api/v1/metrics` - Get performance metrics
- `GET /api/v1/evolution/history` - Get evolution history

### CLI Commands

- `help` - Show available commands
- `status` - Show system status
- `start [options]` - Start evolution
- `stop` - Stop evolution
- `agents [limit]` - List agents
- `agent <id>` - Show agent details
- `genealogy` - Show genealogy stats
- `metrics` - Show performance metrics
- `export <type> [file]` - Export data

## Safety Features

### Human-in-the-Loop Oversight

The DGM system includes comprehensive safety measures:

1. **Approval Workflows**
   - High-performing agents require human approval
   - Risk assessment for all new agents
   - Configurable approval thresholds

2. **Automatic Rollback**
   - Emergency rollback on system failures
   - Automatic backup creation
   - Restoration from stable checkpoints

3. **Agent Quarantine**
   - Automatic quarantine of risky agents
   - Manual quarantine capabilities
   - Quarantine reason tracking

4. **Performance Monitoring**
   - Continuous performance tracking
   - Degradation detection
   - Stagnation alerts

### Configuration

```json
{
  "safety": {
    "humanApprovalRequired": true,
    "approvalThreshold": 0.8,
    "rollbackOnFailure": true,
    "maxConsecutiveFailures": 3,
    "quarantineEnabled": true,
    "auditLogging": true
  }
}
```

## Troubleshooting

### Common Issues

1. **Evolution Not Starting**
   ```
   Error: No agents available in DGM population
   ```
   **Solution**: Initialize the system with `dgm.initialize()` first.

2. **High Memory Usage**
   ```
   Warning: Memory usage exceeding 2GB
   ```
   **Solution**: Reduce population size or enable archive compression.

3. **Permission Errors**
   ```
   Error: EACCES: permission denied
   ```
   **Solution**: Ensure write permissions for backup and archive directories.

### Debug Mode

Enable debug logging:

```javascript
const dgm = new DarwinGodelMachine({
  debug: true,
  verboseLogging: true
});
```

### Log Files

- System logs: `dgm-logs/system.log`
- Evolution logs: `dgm-logs/evolution.log`
- Safety logs: `dgm-logs/safety.log`
- Error logs: `dgm-logs/errors.log`

## Performance Optimization

### Recommended Settings

For different use cases:

#### Development/Testing
```json
{
  "evolution": {
    "populationSize": 10,
    "maxGenerations": 20
  }
}
```

#### Production
```json
{
  "evolution": {
    "populationSize": 50,
    "maxGenerations": 200
  },
  "archive": {
    "compressionEnabled": true,
    "maxVersions": 500
  }
}
```

#### High-Performance
```json
{
  "evolution": {
    "populationSize": 100,
    "maxGenerations": 500
  },
  "execution": {
    "resourceLimits": {
      "memory": "2GB",
      "cpu": "4"
    }
  }
}
```

## Contributing

### Development Setup

1. Fork the repository
2. Create a feature branch
3. Install dependencies: `npm install`
4. Run tests: `npm test`
5. Submit a pull request

### Code Style

- Use ESLint configuration
- Follow JSDoc commenting standards
- Include unit tests for new features
- Update documentation for API changes

### Testing

```bash
# Run all tests
npm test

# Run specific test suite
npm test -- --grep "DGM Engine"

# Run with coverage
npm run test:coverage
```

## License

MIT License - see LICENSE file for details.

## Support

- GitHub Issues: [Report bugs and feature requests](https://github.com/your-repo/issues)
- Documentation: [Full API documentation](https://docs.your-domain.com)
- Community: [Discord server](https://discord.gg/your-server)

---

*The Darwin Gödel Machine represents a new paradigm in AI orchestration - a system that not only executes tasks but continuously evolves to become better at executing them.*

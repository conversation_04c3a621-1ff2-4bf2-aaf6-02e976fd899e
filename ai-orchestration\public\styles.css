/* AI Assistant Orchestration Platform - Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f7fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-align: center;
}

.subtitle {
    font-size: 1.2rem;
    text-align: center;
    opacity: 0.9;
    margin-bottom: 1.5rem;
}

.status-bar {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #4ade80;
}

.status-dot.offline {
    background-color: #ef4444;
}

/* Main Content */
.main {
    padding: 2rem 0;
    min-height: calc(100vh - 200px);
}

/* Tabs */
.tabs {
    display: flex;
    background: white;
    border-radius: 8px 8px 0 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow-x: auto;
}

.tab-button {
    flex: 1;
    padding: 1rem 1.5rem;
    border: none;
    background: none;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    color: #6b7280;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.tab-button:hover {
    background-color: #f9fafb;
    color: #374151;
}

.tab-button.active {
    color: #667eea;
    border-bottom-color: #667eea;
    background-color: #f8fafc;
}

.tab-content {
    background: white;
    border-radius: 0 0 8px 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    min-height: 600px;
}

.tab-pane {
    display: none;
    padding: 2rem;
}

.tab-pane.active {
    display: block;
}

/* Cards */
.card {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    border: 1px solid #e5e7eb;
}

.card h2 {
    color: #1f2937;
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.card h3 {
    color: #374151;
    margin-bottom: 0.75rem;
    font-size: 1.2rem;
}

/* Metrics Grid */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1.5rem;
}

.metric-card {
    background: #f8fafc;
    padding: 1.5rem;
    border-radius: 8px;
    text-align: center;
    border: 1px solid #e2e8f0;
}

.metric-card h3 {
    font-size: 2rem;
    color: #667eea;
    margin-bottom: 0.5rem;
}

.metric-card p {
    color: #64748b;
    font-size: 0.9rem;
}

/* Buttons */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background-color: #667eea;
    color: white;
}

.btn-primary:hover {
    background-color: #5a67d8;
    transform: translateY(-1px);
}

.btn-secondary {
    background-color: #e2e8f0;
    color: #4a5568;
}

.btn-secondary:hover {
    background-color: #cbd5e0;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    margin-top: 1.5rem;
}

/* Agent Categories */
.agent-categories {
    margin-bottom: 2rem;
}

.category-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.category-card {
    background: #f8fafc;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.category-card h4 {
    margin-bottom: 1rem;
    color: #374151;
    font-size: 1rem;
}

.agent-badges {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.agent-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    color: white;
}

.agent-badge.openai { background-color: #10a37f; }
.agent-badge.anthropic { background-color: #d97706; }
.agent-badge.google { background-color: #4285f4; }
.agent-badge.perplexity { background-color: #6366f1; }
.agent-badge.github { background-color: #24292e; }
.agent-badge.cursor { background-color: #8b5cf6; }
.agent-badge.aws { background-color: #ff9900; }
.agent-badge.tabnine { background-color: #0ea5e9; }
.agent-badge.specialized { background-color: #64748b; }

/* Multi-Agent Role Assignment */
.multi-role-assignment {
    display: grid;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.role-selector-multi {
    background: #f8fafc;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.role-selector-multi label {
    display: block;
    font-weight: 600;
    color: #374151;
    margin-bottom: 1rem;
    font-size: 1rem;
}

.multi-select-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.selected-agents {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    min-height: 40px;
    padding: 0.5rem;
    background: white;
    border: 1px solid #d1d5db;
    border-radius: 6px;
}

.agent-tag {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    background: #667eea;
    color: white;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

.remove-agent {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    font-size: 1rem;
    line-height: 1;
    padding: 0;
    margin-left: 0.25rem;
}

.remove-agent:hover {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.agent-add-dropdown {
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    background: white;
    font-size: 0.9rem;
}

/* Role Assignment (Legacy) */
.role-assignment {
    display: grid;
    gap: 1rem;
    margin-bottom: 2rem;
}

.role-selector {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
}

.role-selector label {
    font-weight: 500;
    min-width: 200px;
    color: #374151;
}

.role-dropdown {
    flex: 1;
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    background: white;
    font-size: 0.9rem;
}

.role-summary {
    background: #f0f9ff;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #bae6fd;
    margin-bottom: 1.5rem;
}

.assignment-item {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem 0;
    border-bottom: 1px solid #e0f2fe;
}

.assignment-item:last-child {
    border-bottom: none;
}

.role {
    font-weight: 500;
    color: #0369a1;
}

/* Workflow Controls */
.workflow-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.workflow-selector {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.workflow-selector select {
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    background: white;
}

.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.status-item {
    display: flex;
    justify-content: space-between;
    padding: 0.75rem;
    background: #f8fafc;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
}

.label {
    font-weight: 500;
    color: #6b7280;
}

/* Progress Bar */
.progress-bar {
    width: 100%;
    height: 20px;
    background-color: #e5e7eb;
    border-radius: 10px;
    overflow: hidden;
    margin: 1rem 0;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    transition: width 0.3s ease;
}

#progress-text {
    text-align: center;
    color: #6b7280;
    font-size: 0.9rem;
}

/* Compliance Sections */
.compliance-section {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: #f8fafc;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.compliance-section ul {
    list-style: none;
    padding-left: 0;
}

.compliance-section li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #e2e8f0;
}

.compliance-section li:last-child {
    border-bottom: none;
}

/* Pruning Configuration */
.config-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.config-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.config-item label {
    font-weight: 500;
    color: #374151;
}

.config-item input,
.config-item select {
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    background: white;
}

/* Audit Logs */
.audit-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.filter-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.audit-logs {
    max-height: 400px;
    overflow-y: auto;
}

.log-entry {
    display: grid;
    grid-template-columns: 150px 150px 1fr;
    gap: 1rem;
    padding: 0.75rem;
    border-bottom: 1px solid #e5e7eb;
    font-size: 0.9rem;
}

.log-entry:hover {
    background-color: #f9fafb;
}

.timestamp {
    color: #6b7280;
    font-family: monospace;
}

.action {
    font-weight: 500;
    color: #374151;
}

.details {
    color: #6b7280;
}

/* Footer */
.footer {
    background-color: #1f2937;
    color: #9ca3af;
    text-align: center;
    padding: 1.5rem 0;
    margin-top: 2rem;
}

/* Code Execution Styles */
.execution-controls {
    margin-bottom: 2rem;
}

.code-input-section {
    margin-bottom: 1.5rem;
}

.code-input-section label {
    display: block;
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: #374151;
}

.code-textarea {
    width: 100%;
    height: 200px;
    padding: 1rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.9rem;
    background: #f8fafc;
    resize: vertical;
}

.execution-settings {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.setting-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.setting-group label {
    font-weight: 500;
    color: #374151;
}

.agent-checkboxes {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.agent-checkboxes label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: normal;
    cursor: pointer;
}

.results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.result-card {
    background: #f8fafc;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.agent-name {
    font-weight: 600;
    color: #374151;
}

.execution-status {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.execution-status.success {
    background: #dcfce7;
    color: #166534;
}

.execution-status.error {
    background: #fef2f2;
    color: #dc2626;
}

.result-output {
    background: white;
    padding: 1rem;
    border-radius: 4px;
    border: 1px solid #e5e7eb;
    font-family: monospace;
    font-size: 0.85rem;
    white-space: pre-wrap;
    max-height: 200px;
    overflow-y: auto;
}

/* Thread Merger Styles */
.thread-input-section {
    margin-bottom: 2rem;
}

.thread-inputs {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.thread-input {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.thread-input label {
    font-weight: 500;
    color: #374151;
}

.thread-textarea {
    height: 150px;
    padding: 1rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 0.9rem;
    resize: vertical;
}

.merger-settings {
    background: #f8fafc;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    margin-bottom: 1.5rem;
}

.merger-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.option-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.merged-content {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    min-height: 200px;
    margin-bottom: 1.5rem;
}

.merge-placeholder {
    color: #6b7280;
    font-style: italic;
    text-align: center;
    padding: 2rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    padding: 0.75rem;
    background: #f8fafc;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .title {
        font-size: 2rem;
    }

    .subtitle {
        font-size: 1rem;
    }

    .status-bar {
        gap: 1rem;
    }

    .tabs {
        flex-direction: column;
    }

    .tab-button {
        flex: none;
    }

    .metrics-grid {
        grid-template-columns: 1fr;
    }

    .action-buttons {
        flex-direction: column;
    }

    .btn {
        width: 100%;
        text-align: center;
    }

    .role-selector {
        flex-direction: column;
        align-items: stretch;
    }

    .role-selector label {
        min-width: auto;
    }

    .workflow-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .log-entry {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .category-grid {
        grid-template-columns: 1fr;
    }

    .execution-settings {
        grid-template-columns: 1fr;
    }

    .thread-inputs {
        grid-template-columns: 1fr;
    }

    .merger-options {
        grid-template-columns: 1fr;
    }

    .results-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.tab-pane.active {
    animation: fadeIn 0.3s ease;
}

.card {
    animation: fadeIn 0.5s ease;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #667eea;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

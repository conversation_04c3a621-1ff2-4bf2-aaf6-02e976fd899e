import { PrismaClient, SharedContext, Agent } from '@prisma/client';
import { EventBus, EVENT_TYPES } from './EventBus';
import { logger } from '../utils/logger';

export interface ContextLayer {
  id: string;
  name: string;
  type: 'GL<PERSON>BAL' | 'WORKFLOW' | 'AGENT' | 'SESSION' | 'TUNNEL';
  priority: number;
  data: Record<string, any>;
  metadata: {
    createdAt: Date;
    updatedAt: Date;
    version: number;
    tags: string[];
    accessLevel: 'PUBLIC' | 'PRIVATE' | 'RESTRICTED';
  };
}

export interface ContextQuery {
  layers?: string[];
  types?: string[];
  tags?: string[];
  agentIds?: string[];
  workflowIds?: string[];
  timeRange?: {
    start: Date;
    end: Date;
  };
  limit?: number;
  includeMetadata?: boolean;
}

export interface ContextMergeStrategy {
  type: 'OVERRIDE' | 'MERGE' | 'APPEND' | 'PRIORITY_BASED';
  conflictResolution: 'LATEST' | 'HIGHEST_PRIORITY' | 'MANUAL' | 'VERSIONED';
  preserveHistory: boolean;
  maxVersions: number;
}

export interface ContextSyncConfig {
  enabled: boolean;
  syncInterval: number; // milliseconds
  batchSize: number;
  retryAttempts: number;
  compressionEnabled: boolean;
  encryptionEnabled: boolean;
}

export class ModelContextProtocol {
  private prisma: PrismaClient;
  private eventBus: EventBus;
  private contextCache: Map<string, ContextLayer> = new Map();
  private syncConfig: ContextSyncConfig;
  private syncInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.prisma = new PrismaClient();
    this.eventBus = new EventBus();
    
    this.syncConfig = {
      enabled: true,
      syncInterval: 30000, // 30 seconds
      batchSize: 100,
      retryAttempts: 3,
      compressionEnabled: true,
      encryptionEnabled: false,
    };

    this.initializeContextProtocol();
  }

  /**
   * Initialize the Model Context Protocol
   */
  private async initializeContextProtocol(): Promise<void> {
    try {
      // Load existing context layers into cache
      await this.loadContextCache();
      
      // Start context synchronization
      if (this.syncConfig.enabled) {
        this.startContextSync();
      }

      // Set up event listeners
      this.setupEventListeners();

      logger.info('Model Context Protocol initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize Model Context Protocol', { error });
    }
  }

  /**
   * Load context layers into memory cache
   */
  private async loadContextCache(): Promise<void> {
    try {
      const contexts = await this.prisma.sharedContext.findMany({
        where: { isActive: true },
        orderBy: { priority: 'desc' },
      });

      for (const context of contexts) {
        const layer: ContextLayer = {
          id: context.id,
          name: context.name,
          type: context.type as ContextLayer['type'],
          priority: context.priority,
          data: context.data as Record<string, any>,
          metadata: {
            createdAt: context.createdAt,
            updatedAt: context.updatedAt,
            version: context.version,
            tags: context.tags,
            accessLevel: context.accessLevel as ContextLayer['metadata']['accessLevel'],
          },
        };

        this.contextCache.set(context.id, layer);
      }

      logger.debug(`Loaded ${contexts.length} context layers into cache`);
    } catch (error) {
      logger.error('Failed to load context cache', { error });
    }
  }

  /**
   * Start context synchronization process
   */
  private startContextSync(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }

    this.syncInterval = setInterval(async () => {
      await this.syncContextLayers();
    }, this.syncConfig.syncInterval);

    logger.debug('Context synchronization started');
  }

  /**
   * Set up event listeners for context updates
   */
  private setupEventListeners(): void {
    this.eventBus.on(EVENT_TYPES.AGENT_CREATED, this.handleAgentCreated.bind(this));
    this.eventBus.on(EVENT_TYPES.WORKFLOW_STARTED, this.handleWorkflowStarted.bind(this));
    this.eventBus.on(EVENT_TYPES.TUNNEL_DATA_SENT, this.handleTunnelDataSent.bind(this));
  }

  /**
   * Create a new context layer
   */
  async createContextLayer(
    name: string,
    type: ContextLayer['type'],
    data: Record<string, any>,
    options: {
      priority?: number;
      tags?: string[];
      accessLevel?: ContextLayer['metadata']['accessLevel'];
      agentId?: string;
      workflowId?: string;
    } = {}
  ): Promise<string> {
    try {
      const context = await this.prisma.sharedContext.create({
        data: {
          name,
          type,
          data,
          priority: options.priority || 0,
          tags: options.tags || [],
          accessLevel: options.accessLevel || 'PUBLIC',
          version: 1,
          isActive: true,
          agentId: options.agentId,
          workflowId: options.workflowId,
        },
      });

      // Add to cache
      const layer: ContextLayer = {
        id: context.id,
        name: context.name,
        type: context.type as ContextLayer['type'],
        priority: context.priority,
        data: context.data as Record<string, any>,
        metadata: {
          createdAt: context.createdAt,
          updatedAt: context.updatedAt,
          version: context.version,
          tags: context.tags,
          accessLevel: context.accessLevel as ContextLayer['metadata']['accessLevel'],
        },
      };

      this.contextCache.set(context.id, layer);

      // Emit event
      this.eventBus.emit(EVENT_TYPES.CONTEXT_LAYER_CREATED, {
        layerId: context.id,
        name,
        type,
      });

      logger.debug('Context layer created', { id: context.id, name, type });
      return context.id;
    } catch (error) {
      logger.error('Failed to create context layer', { error, name, type });
      throw error;
    }
  }

  /**
   * Update an existing context layer
   */
  async updateContextLayer(
    layerId: string,
    updates: {
      data?: Record<string, any>;
      priority?: number;
      tags?: string[];
      accessLevel?: ContextLayer['metadata']['accessLevel'];
    },
    mergeStrategy: ContextMergeStrategy = {
      type: 'MERGE',
      conflictResolution: 'LATEST',
      preserveHistory: true,
      maxVersions: 10,
    }
  ): Promise<void> {
    try {
      const existingContext = await this.prisma.sharedContext.findUnique({
        where: { id: layerId },
      });

      if (!existingContext) {
        throw new Error(`Context layer ${layerId} not found`);
      }

      // Merge data based on strategy
      let mergedData = existingContext.data as Record<string, any>;
      if (updates.data) {
        mergedData = this.mergeContextData(
          mergedData,
          updates.data,
          mergeStrategy
        );
      }

      // Update context
      const updatedContext = await this.prisma.sharedContext.update({
        where: { id: layerId },
        data: {
          data: mergedData,
          priority: updates.priority ?? existingContext.priority,
          tags: updates.tags ?? existingContext.tags,
          accessLevel: updates.accessLevel ?? existingContext.accessLevel,
          version: existingContext.version + 1,
          updatedAt: new Date(),
        },
      });

      // Update cache
      const cachedLayer = this.contextCache.get(layerId);
      if (cachedLayer) {
        cachedLayer.data = mergedData;
        cachedLayer.priority = updatedContext.priority;
        cachedLayer.metadata.tags = updatedContext.tags;
        cachedLayer.metadata.accessLevel = updatedContext.accessLevel as ContextLayer['metadata']['accessLevel'];
        cachedLayer.metadata.version = updatedContext.version;
        cachedLayer.metadata.updatedAt = updatedContext.updatedAt;
      }

      // Emit event
      this.eventBus.emit(EVENT_TYPES.CONTEXT_LAYER_UPDATED, {
        layerId,
        version: updatedContext.version,
      });

      logger.debug('Context layer updated', { id: layerId, version: updatedContext.version });
    } catch (error) {
      logger.error('Failed to update context layer', { error, layerId });
      throw error;
    }
  }

  /**
   * Query context layers based on criteria
   */
  async queryContext(query: ContextQuery): Promise<ContextLayer[]> {
    try {
      const layers: ContextLayer[] = [];

      // If specific layer IDs are requested, get them from cache first
      if (query.layers && query.layers.length > 0) {
        for (const layerId of query.layers) {
          const cachedLayer = this.contextCache.get(layerId);
          if (cachedLayer) {
            layers.push(cachedLayer);
          }
        }
      } else {
        // Query from database with filters
        const whereClause: any = { isActive: true };

        if (query.types && query.types.length > 0) {
          whereClause.type = { in: query.types };
        }

        if (query.agentIds && query.agentIds.length > 0) {
          whereClause.agentId = { in: query.agentIds };
        }

        if (query.workflowIds && query.workflowIds.length > 0) {
          whereClause.workflowId = { in: query.workflowIds };
        }

        if (query.timeRange) {
          whereClause.updatedAt = {
            gte: query.timeRange.start,
            lte: query.timeRange.end,
          };
        }

        if (query.tags && query.tags.length > 0) {
          whereClause.tags = {
            hasSome: query.tags,
          };
        }

        const contexts = await this.prisma.sharedContext.findMany({
          where: whereClause,
          orderBy: { priority: 'desc' },
          take: query.limit || 100,
        });

        for (const context of contexts) {
          layers.push({
            id: context.id,
            name: context.name,
            type: context.type as ContextLayer['type'],
            priority: context.priority,
            data: context.data as Record<string, any>,
            metadata: {
              createdAt: context.createdAt,
              updatedAt: context.updatedAt,
              version: context.version,
              tags: context.tags,
              accessLevel: context.accessLevel as ContextLayer['metadata']['accessLevel'],
            },
          });
        }
      }

      // Sort by priority
      layers.sort((a, b) => b.priority - a.priority);

      logger.debug('Context query executed', { 
        query, 
        resultCount: layers.length 
      });

      return layers;
    } catch (error) {
      logger.error('Failed to query context', { error, query });
      throw error;
    }
  }

  /**
   * Merge context data from multiple layers
   */
  async mergeContextLayers(
    layerIds: string[],
    strategy: ContextMergeStrategy = {
      type: 'PRIORITY_BASED',
      conflictResolution: 'HIGHEST_PRIORITY',
      preserveHistory: false,
      maxVersions: 1,
    }
  ): Promise<Record<string, any>> {
    try {
      const layers = await this.queryContext({ layers: layerIds });
      
      if (layers.length === 0) {
        return {};
      }

      // Sort by priority for priority-based merging
      if (strategy.type === 'PRIORITY_BASED') {
        layers.sort((a, b) => b.priority - a.priority);
      }

      let mergedData: Record<string, any> = {};

      for (const layer of layers) {
        mergedData = this.mergeContextData(mergedData, layer.data, strategy);
      }

      logger.debug('Context layers merged', { 
        layerIds, 
        strategy: strategy.type,
        resultKeys: Object.keys(mergedData).length 
      });

      return mergedData;
    } catch (error) {
      logger.error('Failed to merge context layers', { error, layerIds });
      throw error;
    }
  }

  /**
   * Merge two context data objects
   */
  private mergeContextData(
    existing: Record<string, any>,
    incoming: Record<string, any>,
    strategy: ContextMergeStrategy
  ): Record<string, any> {
    switch (strategy.type) {
      case 'OVERRIDE':
        return { ...incoming };
      
      case 'MERGE':
        return { ...existing, ...incoming };
      
      case 'APPEND':
        const result = { ...existing };
        for (const [key, value] of Object.entries(incoming)) {
          if (Array.isArray(value) && Array.isArray(result[key])) {
            result[key] = [...result[key], ...value];
          } else {
            result[key] = value;
          }
        }
        return result;
      
      case 'PRIORITY_BASED':
        // Keep existing values unless incoming has higher priority
        return { ...existing, ...incoming };
      
      default:
        return { ...existing, ...incoming };
    }
  }

  /**
   * Synchronize context layers across the system
   */
  private async syncContextLayers(): Promise<void> {
    try {
      // Get recently updated contexts from database
      const recentlyUpdated = await this.prisma.sharedContext.findMany({
        where: {
          isActive: true,
          updatedAt: {
            gte: new Date(Date.now() - this.syncConfig.syncInterval * 2),
          },
        },
        take: this.syncConfig.batchSize,
      });

      // Update cache with recent changes
      for (const context of recentlyUpdated) {
        const layer: ContextLayer = {
          id: context.id,
          name: context.name,
          type: context.type as ContextLayer['type'],
          priority: context.priority,
          data: context.data as Record<string, any>,
          metadata: {
            createdAt: context.createdAt,
            updatedAt: context.updatedAt,
            version: context.version,
            tags: context.tags,
            accessLevel: context.accessLevel as ContextLayer['metadata']['accessLevel'],
          },
        };

        this.contextCache.set(context.id, layer);
      }

      // Emit sync completed event
      this.eventBus.emit(EVENT_TYPES.CONTEXT_SYNC_COMPLETED, {
        syncedCount: recentlyUpdated.length,
        timestamp: new Date(),
      });

      logger.debug('Context synchronization completed', { 
        syncedCount: recentlyUpdated.length 
      });
    } catch (error) {
      logger.error('Context synchronization failed', { error });
    }
  }

  /**
   * Handle agent created event
   */
  private async handleAgentCreated(data: any): Promise<void> {
    try {
      await this.createContextLayer(
        `Agent Context: ${data.name}`,
        'AGENT',
        {
          agentId: data.id,
          capabilities: data.capabilities,
          role: data.role,
          createdAt: new Date(),
        },
        {
          priority: 5,
          tags: ['agent', data.role],
          agentId: data.id,
        }
      );
    } catch (error) {
      logger.error('Failed to create agent context layer', { error, data });
    }
  }

  /**
   * Handle workflow started event
   */
  private async handleWorkflowStarted(data: any): Promise<void> {
    try {
      await this.createContextLayer(
        `Workflow Context: ${data.workflowId}`,
        'WORKFLOW',
        {
          workflowId: data.workflowId,
          executionId: data.executionId,
          startedAt: new Date(),
          context: data.context || {},
        },
        {
          priority: 8,
          tags: ['workflow', 'execution'],
          workflowId: data.workflowId,
        }
      );
    } catch (error) {
      logger.error('Failed to create workflow context layer', { error, data });
    }
  }

  /**
   * Handle tunnel data sent event
   */
  private async handleTunnelDataSent(data: any): Promise<void> {
    try {
      await this.createContextLayer(
        `Tunnel Context: ${data.tunnelId}`,
        'TUNNEL',
        {
          tunnelId: data.tunnelId,
          dataFlowId: data.dataFlowId,
          timestamp: new Date(),
        },
        {
          priority: 3,
          tags: ['tunnel', 'data-flow'],
        }
      );
    } catch (error) {
      logger.error('Failed to create tunnel context layer', { error, data });
    }
  }

  /**
   * Get context statistics
   */
  async getContextStatistics(): Promise<any> {
    try {
      const stats = await this.prisma.sharedContext.groupBy({
        by: ['type'],
        where: { isActive: true },
        _count: { id: true },
        _avg: { priority: true },
      });

      const totalLayers = await this.prisma.sharedContext.count({
        where: { isActive: true },
      });

      return {
        totalLayers,
        cacheSize: this.contextCache.size,
        layersByType: stats.map(stat => ({
          type: stat.type,
          count: stat._count.id,
          averagePriority: stat._avg.priority,
        })),
        syncConfig: this.syncConfig,
      };
    } catch (error) {
      logger.error('Failed to get context statistics', { error });
      throw error;
    }
  }

  /**
   * Clean up old context layers
   */
  async cleanupOldContexts(olderThanDays: number = 30): Promise<number> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

      const result = await this.prisma.sharedContext.updateMany({
        where: {
          updatedAt: { lt: cutoffDate },
          isActive: true,
        },
        data: { isActive: false },
      });

      // Remove from cache
      for (const [id, layer] of this.contextCache.entries()) {
        if (layer.metadata.updatedAt < cutoffDate) {
          this.contextCache.delete(id);
        }
      }

      logger.info('Old context layers cleaned up', { 
        deactivatedCount: result.count,
        cutoffDate 
      });

      return result.count;
    } catch (error) {
      logger.error('Failed to cleanup old contexts', { error });
      throw error;
    }
  }

  /**
   * Shutdown the context protocol
   */
  async shutdown(): Promise<void> {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }

    this.contextCache.clear();
    logger.info('Model Context Protocol shutdown completed');
  }
}

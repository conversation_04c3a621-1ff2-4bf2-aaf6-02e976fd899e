#!/usr/bin/env node

/**
 * EcoStamp Standalone Test Runner
 * Tests all systems without requiring VS Code
 * Can be run directly from Terminal/Command Prompt
 */

import fs from 'fs';
import path from 'path';
import { spawn, exec } from 'child_process';
import os from 'os';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class StandaloneTestRunner {
  constructor() {
    this.workspaceRoot = process.cwd();
    this.results = {
      passed: 0,
      failed: 0,
      warnings: 0,
      tests: []
    };
    
    this.colors = {
      reset: '\x1b[0m',
      bright: '\x1b[1m',
      red: '\x1b[31m',
      green: '\x1b[32m',
      yellow: '\x1b[33m',
      blue: '\x1b[34m',
      magenta: '\x1b[35m',
      cyan: '\x1b[36m'
    };
  }
  
  log(message, color = 'reset') {
    console.log(`${this.colors[color]}${message}${this.colors.reset}`);
  }
  
  async run() {
    this.log('\n🌱 EcoStamp Standalone Test Runner', 'cyan');
    this.log('=' .repeat(50), 'cyan');
    this.log(`Workspace: ${this.workspaceRoot}`, 'blue');
    this.log(`Platform: ${os.platform()} ${os.arch()}`, 'blue');
    this.log(`Node.js: ${process.version}`, 'blue');
    this.log('');
    
    try {
      await this.runAllTests();
      this.displayResults();
    } catch (error) {
      this.log(`\n❌ Test runner failed: ${error.message}`, 'red');
      process.exit(1);
    }
  }
  
  async runAllTests() {
    const tests = [
      { name: 'System Requirements', fn: () => this.testSystemRequirements() },
      { name: 'Project Structure', fn: () => this.testProjectStructure() },
      { name: 'EcoStamp Backend', fn: () => this.testEcoStampBackend() },
      { name: 'EcoStamp Website', fn: () => this.testEcoStampWebsite() },
      { name: 'Browser Extensions', fn: () => this.testBrowserExtensions() },
      { name: 'Security Scanner', fn: () => this.testSecurityScanner() },
      { name: 'AI Orchestration', fn: () => this.testAIOrchestration() },
      { name: 'Core Systems', fn: () => this.testCoreSystems() },
      { name: 'API Endpoints', fn: () => this.testAPIEndpoints() },
      { name: 'Database Connectivity', fn: () => this.testDatabaseConnectivity() }
    ];
    
    for (const test of tests) {
      this.log(`\n🧪 Testing: ${test.name}`, 'yellow');
      try {
        const result = await test.fn();
        this.recordTest(test.name, result.status, result.message, result.details);
      } catch (error) {
        this.recordTest(test.name, 'failed', error.message);
      }
    }
  }
  
  async testSystemRequirements() {
    const checks = [];
    
    // Check Node.js version
    const nodeVersion = process.version;
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
    checks.push({
      name: 'Node.js Version',
      status: majorVersion >= 16 ? 'passed' : 'failed',
      message: `${nodeVersion} ${majorVersion >= 16 ? '✅' : '❌ (requires 16+)'}`
    });
    
    // Check npm
    try {
      await this.execCommand('npm --version');
      checks.push({ name: 'npm', status: 'passed', message: '✅ Available' });
    } catch (error) {
      checks.push({ name: 'npm', status: 'failed', message: '❌ Not found' });
    }
    
    // Check Python
    try {
      const pythonVersion = await this.execCommand('python --version');
      checks.push({ name: 'Python', status: 'passed', message: `✅ ${pythonVersion.trim()}` });
    } catch (error) {
      try {
        const python3Version = await this.execCommand('python3 --version');
        checks.push({ name: 'Python', status: 'passed', message: `✅ ${python3Version.trim()}` });
      } catch (error2) {
        checks.push({ name: 'Python', status: 'warning', message: '⚠️ Not found (optional)' });
      }
    }
    
    const failed = checks.filter(c => c.status === 'failed');
    return {
      status: failed.length === 0 ? 'passed' : 'failed',
      message: failed.length === 0 ? 'All requirements met' : `${failed.length} requirements missing`,
      details: checks
    };
  }
  
  async testProjectStructure() {
    const requiredPaths = [
      'core-systems/EcoStamp/source',
      'core-systems/EcoStamp/source/server.js',
      'core-systems/EcoStamp/source/package.json',
      'core-systems/EcoStamp/source/public/index.html',
      'extensions/universal',
      'development-tools/scanner.js',
      'ai-orchestration',
      'run-projects.js'
    ];
    
    const checks = [];
    for (const reqPath of requiredPaths) {
      const fullPath = path.join(this.workspaceRoot, reqPath);
      const exists = fs.existsSync(fullPath);
      checks.push({
        name: reqPath,
        status: exists ? 'passed' : 'failed',
        message: exists ? '✅ Found' : '❌ Missing'
      });
    }
    
    const failed = checks.filter(c => c.status === 'failed');
    return {
      status: failed.length === 0 ? 'passed' : 'failed',
      message: failed.length === 0 ? 'All required files found' : `${failed.length} files missing`,
      details: checks
    };
  }
  
  async testEcoStampBackend() {
    const ecoStampPath = path.join(this.workspaceRoot, 'core-systems/EcoStamp/source');
    
    if (!fs.existsSync(ecoStampPath)) {
      return { status: 'failed', message: 'EcoStamp directory not found' };
    }
    
    try {
      // Check package.json
      const packagePath = path.join(ecoStampPath, 'package.json');
      const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
      
      // Check if dependencies are installed
      const nodeModulesPath = path.join(ecoStampPath, 'node_modules');
      const depsInstalled = fs.existsSync(nodeModulesPath);
      
      // Try to start server (quick test)
      let serverTest = 'not tested';
      try {
        const serverProcess = spawn('node', ['server.js'], {
          cwd: ecoStampPath,
          stdio: 'pipe'
        });
        
        await new Promise((resolve) => {
          setTimeout(() => {
            serverProcess.kill();
            serverTest = 'started successfully';
            resolve();
          }, 3000);
          
          serverProcess.on('error', () => {
            serverTest = 'failed to start';
            resolve();
          });
        });
      } catch (error) {
        serverTest = 'failed to start';
      }
      
      return {
        status: depsInstalled ? 'passed' : 'warning',
        message: depsInstalled ? 'Backend ready' : 'Dependencies need installation',
        details: [
          { name: 'package.json', status: 'passed', message: '✅ Valid' },
          { name: 'Dependencies', status: depsInstalled ? 'passed' : 'warning', message: depsInstalled ? '✅ Installed' : '⚠️ Run npm install' },
          { name: 'Server Test', status: serverTest === 'started successfully' ? 'passed' : 'warning', message: serverTest }
        ]
      };
    } catch (error) {
      return { status: 'failed', message: `Backend test failed: ${error.message}` };
    }
  }
  
  async testEcoStampWebsite() {
    const websitePath = path.join(this.workspaceRoot, 'core-systems/EcoStamp/source/public');
    
    const checks = [];
    const requiredFiles = ['index.html', 'dashboard.html', 'verify.html'];
    
    for (const file of requiredFiles) {
      const filePath = path.join(websitePath, file);
      const exists = fs.existsSync(filePath);
      checks.push({
        name: file,
        status: exists ? 'passed' : 'failed',
        message: exists ? '✅ Found' : '❌ Missing'
      });
    }
    
    // Check if index.html has download functionality
    try {
      const indexPath = path.join(websitePath, 'index.html');
      const indexContent = fs.readFileSync(indexPath, 'utf8');
      const hasDownloads = indexContent.includes('downloadExtension') && indexContent.includes('download-btn');
      checks.push({
        name: 'Download Functionality',
        status: hasDownloads ? 'passed' : 'warning',
        message: hasDownloads ? '✅ Implemented' : '⚠️ Basic implementation'
      });
    } catch (error) {
      checks.push({
        name: 'Download Functionality',
        status: 'failed',
        message: '❌ Cannot verify'
      });
    }
    
    const failed = checks.filter(c => c.status === 'failed');
    return {
      status: failed.length === 0 ? 'passed' : 'failed',
      message: failed.length === 0 ? 'Website ready' : `${failed.length} issues found`,
      details: checks
    };
  }
  
  async testBrowserExtensions() {
    const extensionPath = path.join(this.workspaceRoot, 'extensions/universal');
    
    const checks = [];
    const requiredFiles = ['manifest.json', 'content.js', 'background.js', 'popup.html', 'popup.js', 'ecostamp.css'];
    
    for (const file of requiredFiles) {
      const filePath = path.join(extensionPath, file);
      const exists = fs.existsSync(filePath);
      checks.push({
        name: file,
        status: exists ? 'passed' : 'failed',
        message: exists ? '✅ Found' : '❌ Missing'
      });
    }
    
    // Validate manifest.json
    try {
      const manifestPath = path.join(extensionPath, 'manifest.json');
      const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
      const hasRequiredFields = manifest.name && manifest.version && manifest.permissions;
      checks.push({
        name: 'Manifest Validation',
        status: hasRequiredFields ? 'passed' : 'failed',
        message: hasRequiredFields ? '✅ Valid' : '❌ Invalid structure'
      });
    } catch (error) {
      checks.push({
        name: 'Manifest Validation',
        status: 'failed',
        message: '❌ Parse error'
      });
    }
    
    const failed = checks.filter(c => c.status === 'failed');
    return {
      status: failed.length === 0 ? 'passed' : 'failed',
      message: failed.length === 0 ? 'Extensions ready' : `${failed.length} files missing`,
      details: checks
    };
  }
  
  async testSecurityScanner() {
    const scannerPath = path.join(this.workspaceRoot, 'development-tools/scanner.js');
    
    if (!fs.existsSync(scannerPath)) {
      return { status: 'failed', message: 'Scanner not found' };
    }
    
    try {
      // Quick syntax check
      const scannerContent = fs.readFileSync(scannerPath, 'utf8');
      const hasMainFunctions = scannerContent.includes('scanProject') || scannerContent.includes('SecurityScanner');
      
      return {
        status: hasMainFunctions ? 'passed' : 'warning',
        message: hasMainFunctions ? 'Scanner ready' : 'Scanner needs verification',
        details: [
          { name: 'File exists', status: 'passed', message: '✅ Found' },
          { name: 'Main functions', status: hasMainFunctions ? 'passed' : 'warning', message: hasMainFunctions ? '✅ Present' : '⚠️ Verify implementation' }
        ]
      };
    } catch (error) {
      return { status: 'failed', message: `Scanner test failed: ${error.message}` };
    }
  }
  
  async testAIOrchestration() {
    const orchestrationPath = path.join(this.workspaceRoot, 'ai-orchestration');
    
    if (!fs.existsSync(orchestrationPath)) {
      return { status: 'failed', message: 'AI Orchestration directory not found' };
    }
    
    const checks = [];
    const requiredFiles = ['orchestrator.js', 'package.json'];
    
    for (const file of requiredFiles) {
      const filePath = path.join(orchestrationPath, file);
      const exists = fs.existsSync(filePath);
      checks.push({
        name: file,
        status: exists ? 'passed' : 'failed',
        message: exists ? '✅ Found' : '❌ Missing'
      });
    }
    
    const failed = checks.filter(c => c.status === 'failed');
    return {
      status: failed.length === 0 ? 'passed' : 'failed',
      message: failed.length === 0 ? 'AI Orchestration ready' : `${failed.length} files missing`,
      details: checks
    };
  }
  
  async testCoreSystems() {
    const coreSystemsPath = path.join(this.workspaceRoot, 'core-systems');
    
    if (!fs.existsSync(coreSystemsPath)) {
      return { status: 'failed', message: 'Core systems directory not found' };
    }
    
    const setupPyPath = path.join(coreSystemsPath, 'setup.py');
    const setupExists = fs.existsSync(setupPyPath);
    
    return {
      status: setupExists ? 'passed' : 'warning',
      message: setupExists ? 'Core systems ready' : 'Python setup needs verification',
      details: [
        { name: 'Directory', status: 'passed', message: '✅ Found' },
        { name: 'setup.py', status: setupExists ? 'passed' : 'warning', message: setupExists ? '✅ Found' : '⚠️ Missing' }
      ]
    };
  }
  
  async testAPIEndpoints() {
    // This would require the server to be running
    return {
      status: 'warning',
      message: 'API tests require running server',
      details: [
        { name: 'Server Required', status: 'warning', message: '⚠️ Start EcoStamp server to test APIs' }
      ]
    };
  }
  
  async testDatabaseConnectivity() {
    // Check for database configuration files
    const dbConfigPaths = [
      'core-systems/EcoStamp/source/config/database.js',
      'core-systems/EcoStamp/source/models',
      'core-systems/EcoStamp/source/data'
    ];
    
    const checks = [];
    for (const dbPath of dbConfigPaths) {
      const fullPath = path.join(this.workspaceRoot, dbPath);
      const exists = fs.existsSync(fullPath);
      checks.push({
        name: path.basename(dbPath),
        status: exists ? 'passed' : 'warning',
        message: exists ? '✅ Found' : '⚠️ Not configured'
      });
    }
    
    return {
      status: 'warning',
      message: 'Database configuration detected',
      details: checks
    };
  }
  
  recordTest(name, status, message, details = []) {
    this.results.tests.push({ name, status, message, details });
    
    if (status === 'passed') {
      this.results.passed++;
      this.log(`  ✅ ${message}`, 'green');
    } else if (status === 'warning') {
      this.results.warnings++;
      this.log(`  ⚠️  ${message}`, 'yellow');
    } else {
      this.results.failed++;
      this.log(`  ❌ ${message}`, 'red');
    }
    
    if (details.length > 0) {
      details.forEach(detail => {
        const icon = detail.status === 'passed' ? '✅' : detail.status === 'warning' ? '⚠️' : '❌';
        const color = detail.status === 'passed' ? 'green' : detail.status === 'warning' ? 'yellow' : 'red';
        this.log(`    ${icon} ${detail.name}: ${detail.message}`, color);
      });
    }
  }
  
  displayResults() {
    this.log('\n' + '='.repeat(50), 'cyan');
    this.log('🧪 TEST RESULTS SUMMARY', 'cyan');
    this.log('='.repeat(50), 'cyan');
    
    this.log(`✅ Passed: ${this.results.passed}`, 'green');
    this.log(`⚠️  Warnings: ${this.results.warnings}`, 'yellow');
    this.log(`❌ Failed: ${this.results.failed}`, 'red');
    this.log(`📊 Total Tests: ${this.results.tests.length}`, 'blue');
    
    const successRate = Math.round((this.results.passed / this.results.tests.length) * 100);
    this.log(`📈 Success Rate: ${successRate}%`, successRate >= 80 ? 'green' : successRate >= 60 ? 'yellow' : 'red');
    
    if (this.results.failed === 0) {
      this.log('\n🎉 All critical tests passed! System is ready.', 'green');
    } else if (this.results.failed <= 2) {
      this.log('\n⚠️  Some issues found, but system should work with limitations.', 'yellow');
    } else {
      this.log('\n❌ Multiple critical issues found. System may not work properly.', 'red');
    }
    
    this.log('\n📋 Next Steps:', 'blue');
    if (this.results.failed > 0) {
      this.log('  1. Fix failed tests before proceeding', 'yellow');
    }
    if (this.results.warnings > 0) {
      this.log('  2. Review warnings for optimal performance', 'yellow');
    }
    this.log('  3. Start EcoStamp server: npm start (in core-systems/EcoStamp/source)', 'blue');
    this.log('  4. Test website: http://localhost:3000', 'blue');
    this.log('  5. Install browser extension from extensions/universal/', 'blue');
    
    this.log('\n🌱 EcoStamp Test Complete!', 'cyan');
  }
  
  async execCommand(command) {
    return new Promise((resolve, reject) => {
      exec(command, (error, stdout, stderr) => {
        if (error) {
          reject(error);
        } else {
          resolve(stdout);
        }
      });
    });
  }
}

// Run the test runner
const isMainModule = import.meta.url === `file://${process.argv[1]}` ||
                     import.meta.url.endsWith('standalone-test-runner.js');

if (isMainModule) {
  const runner = new StandaloneTestRunner();
  runner.run().catch(error => {
    console.error('Test runner failed:', error);
    process.exit(1);
  });
}

export default StandaloneTestRunner;

import type { LinksFunction, LoaderFunctionArgs, MetaFunction } from "@remix-run/node";
import { json } from "@remix-run/node";
import {
  Links,
  LiveReload,
  Meta,
  Outlet,
  Scripts,
  ScrollRestoration,
  useLoaderData,
} from "@remix-run/react";
import { Toaster } from "react-hot-toast";

import stylesheet from "./tailwind.css";
import { getUser } from "./utils/auth.server";
import { PostHogProvider } from "./components/providers/PostHogProvider";

export const links: LinksFunction = () => [
  { rel: "stylesheet", href: stylesheet },
  { rel: "preconnect", href: "https://fonts.googleapis.com" },
  { rel: "preconnect", href: "https://fonts.gstatic.com", crossOrigin: "anonymous" },
  {
    rel: "stylesheet",
    href: "https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap",
  },
  {
    rel: "stylesheet",
    href: "https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@100;200;300;400;500;600;700;800&display=swap",
  },
  { rel: "icon", href: "/favicon.ico" },
  { rel: "apple-touch-icon", href: "/apple-touch-icon.png" },
];

export const meta: MetaFunction = () => [
  { title: "EcoStamp - Digital Trust & Provenance Platform" },
  { 
    name: "description", 
    content: "The universal platform for digital trust, content verification, and provenance tracking. Trusted by creators, professionals, and enterprises worldwide." 
  },
  { name: "viewport", content: "width=device-width,initial-scale=1" },
  { property: "og:title", content: "EcoStamp - Digital Trust & Provenance Platform" },
  { 
    property: "og:description", 
    content: "The universal platform for digital trust, content verification, and provenance tracking. Trusted by creators, professionals, and enterprises worldwide." 
  },
  { property: "og:type", content: "website" },
  { property: "og:url", content: "https://ecostamp.com" },
  { property: "og:image", content: "https://ecostamp.com/og-image.png" },
  { name: "twitter:card", content: "summary_large_image" },
  { name: "twitter:creator", content: "@ecostamp" },
  { name: "twitter:title", content: "EcoStamp - Digital Trust & Provenance Platform" },
  { 
    name: "twitter:description", 
    content: "The universal platform for digital trust, content verification, and provenance tracking." 
  },
  { name: "twitter:image", content: "https://ecostamp.com/og-image.png" },
];

export async function loader({ request }: LoaderFunctionArgs) {
  const user = await getUser(request);
  
  return json({
    user,
    ENV: {
      SUPABASE_URL: process.env.SUPABASE_URL,
      SUPABASE_ANON_KEY: process.env.SUPABASE_ANON_KEY,
      STRIPE_PUBLISHABLE_KEY: process.env.STRIPE_PUBLISHABLE_KEY,
      POSTHOG_KEY: process.env.POSTHOG_KEY,
      POSTHOG_HOST: process.env.POSTHOG_HOST,
    },
  });
}

export default function App() {
  const { user, ENV } = useLoaderData<typeof loader>();

  return (
    <html lang="en" className="h-full">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width,initial-scale=1" />
        <Meta />
        <Links />
      </head>
      <body className="h-full bg-white text-secondary-900 antialiased">
        <PostHogProvider apiKey={ENV.POSTHOG_KEY} host={ENV.POSTHOG_HOST}>
          <Outlet />
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: '#1e293b',
                color: '#f8fafc',
                borderRadius: '0.75rem',
                padding: '1rem',
                fontSize: '0.875rem',
                fontWeight: '500',
              },
              success: {
                iconTheme: {
                  primary: '#22c55e',
                  secondary: '#f8fafc',
                },
              },
              error: {
                iconTheme: {
                  primary: '#ef4444',
                  secondary: '#f8fafc',
                },
              },
            }}
          />
        </PostHogProvider>
        <ScrollRestoration />
        <Scripts />
        <LiveReload />
        <script
          dangerouslySetInnerHTML={{
            __html: `window.ENV = ${JSON.stringify(ENV)}`,
          }}
        />
      </body>
    </html>
  );
}

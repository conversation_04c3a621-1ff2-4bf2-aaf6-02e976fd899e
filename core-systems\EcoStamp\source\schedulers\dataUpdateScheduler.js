/**
 * Data Update Scheduler
 * Automatically scrapes and updates AI usage data on a schedule
 */

import { runScrapingProcess } from '../scrapers/usageDataScraper.js';
import { updateImpactRates } from '../models/enhancedImpactModel.js';

class DataUpdateScheduler {
    constructor() {
        this.intervals = new Map();
        this.isRunning = false;
    }

    /**
     * Start the scheduler with specified intervals
     * @param {object} config - Configuration object
     */
    start(config = {}) {
        const defaultConfig = {
            dailyUpdate: true,
            weeklyDeepScrape: true,
            hourlyRateCheck: false,
            dailyTime: '02:00', // 2 AM
            weeklyDay: 0, // Sunday
            weeklyTime: '03:00' // 3 AM
        };

        const finalConfig = { ...defaultConfig, ...config };
        
        if (this.isRunning) {
            console.log('Scheduler already running');
            return;
        }

        console.log('Starting data update scheduler...');
        this.isRunning = true;

        // Daily update
        if (finalConfig.dailyUpdate) {
            this.scheduleDailyUpdate(finalConfig.dailyTime);
        }

        // Weekly deep scrape
        if (finalConfig.weeklyDeepScrape) {
            this.scheduleWeeklyUpdate(finalConfig.weeklyDay, finalConfig.weeklyTime);
        }

        // Hourly rate check (optional, for high-frequency updates)
        if (finalConfig.hourlyRateCheck) {
            this.scheduleHourlyCheck();
        }

        // Run initial update
        this.runInitialUpdate();
    }

    /**
     * Stop all scheduled tasks
     */
    stop() {
        console.log('Stopping data update scheduler...');
        
        for (const [name, intervalId] of this.intervals) {
            clearInterval(intervalId);
            console.log(`Stopped ${name} scheduler`);
        }
        
        this.intervals.clear();
        this.isRunning = false;
    }

    /**
     * Schedule daily updates
     * @param {string} time - Time in HH:MM format
     */
    scheduleDailyUpdate(time) {
        const [hours, minutes] = time.split(':').map(Number);
        const now = new Date();
        const scheduledTime = new Date();
        scheduledTime.setHours(hours, minutes, 0, 0);

        // If scheduled time has passed today, schedule for tomorrow
        if (scheduledTime <= now) {
            scheduledTime.setDate(scheduledTime.getDate() + 1);
        }

        const msUntilFirst = scheduledTime.getTime() - now.getTime();
        const msPerDay = 24 * 60 * 60 * 1000;

        // Set timeout for first run, then interval for subsequent runs
        setTimeout(() => {
            this.runDailyUpdate();
            
            const intervalId = setInterval(() => {
                this.runDailyUpdate();
            }, msPerDay);
            
            this.intervals.set('daily', intervalId);
        }, msUntilFirst);

        console.log(`Daily update scheduled for ${time} (next run: ${scheduledTime.toLocaleString()})`);
    }

    /**
     * Schedule weekly deep scraping
     * @param {number} dayOfWeek - Day of week (0 = Sunday)
     * @param {string} time - Time in HH:MM format
     */
    scheduleWeeklyUpdate(dayOfWeek, time) {
        const [hours, minutes] = time.split(':').map(Number);
        const now = new Date();
        const scheduledTime = new Date();
        
        // Calculate next occurrence of the specified day and time
        const daysUntilTarget = (dayOfWeek - now.getDay() + 7) % 7;
        scheduledTime.setDate(now.getDate() + daysUntilTarget);
        scheduledTime.setHours(hours, minutes, 0, 0);

        // If the time has passed today and it's the target day, schedule for next week
        if (daysUntilTarget === 0 && scheduledTime <= now) {
            scheduledTime.setDate(scheduledTime.getDate() + 7);
        }

        const msUntilFirst = scheduledTime.getTime() - now.getTime();
        const msPerWeek = 7 * 24 * 60 * 60 * 1000;

        setTimeout(() => {
            this.runWeeklyUpdate();
            
            const intervalId = setInterval(() => {
                this.runWeeklyUpdate();
            }, msPerWeek);
            
            this.intervals.set('weekly', intervalId);
        }, msUntilFirst);

        const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
        console.log(`Weekly update scheduled for ${dayNames[dayOfWeek]} at ${time} (next run: ${scheduledTime.toLocaleString()})`);
    }

    /**
     * Schedule hourly rate checks
     */
    scheduleHourlyCheck() {
        const intervalId = setInterval(() => {
            this.runHourlyCheck();
        }, 60 * 60 * 1000); // Every hour

        this.intervals.set('hourly', intervalId);
        console.log('Hourly rate check scheduled');
    }

    /**
     * Run initial update on startup
     */
    async runInitialUpdate() {
        console.log('Running initial data update...');
        try {
            await this.runDailyUpdate();
            console.log('Initial update completed');
        } catch (error) {
            console.error('Initial update failed:', error);
        }
    }

    /**
     * Execute daily update
     */
    async runDailyUpdate() {
        console.log('Running daily data update...');
        try {
            const scrapedData = await runScrapingProcess();
            const updated = updateImpactRates(scrapedData);
            
            if (updated) {
                console.log('Impact rates updated with new data');
            } else {
                console.log('No new impact rate data available');
            }
            
            // Log summary
            this.logUpdateSummary('daily', scrapedData);
            
        } catch (error) {
            console.error('Daily update failed:', error);
        }
    }

    /**
     * Execute weekly deep update
     */
    async runWeeklyUpdate() {
        console.log('Running weekly deep data update...');
        try {
            // More comprehensive scraping for weekly updates
            const scrapedData = await runScrapingProcess();
            updateImpactRates(scrapedData);
            
            // Additional weekly tasks could go here
            // - Clean up old data files
            // - Generate weekly reports
            // - Update model parameters
            
            this.logUpdateSummary('weekly', scrapedData);
            
        } catch (error) {
            console.error('Weekly update failed:', error);
        }
    }

    /**
     * Execute hourly rate check
     */
    async runHourlyCheck() {
        console.log('Running hourly rate check...');
        try {
            // Quick check for any immediate updates
            // This could be a lighter version of the scraping process
            console.log('Hourly check completed');
        } catch (error) {
            console.error('Hourly check failed:', error);
        }
    }

    /**
     * Log update summary
     * @param {string} type - Update type
     * @param {object} data - Scraped data
     */
    logUpdateSummary(type, data) {
        const timestamp = new Date().toISOString();
        const summary = {
            type,
            timestamp,
            sources: Object.keys(data.scraped || {}).length,
            estimates: Object.keys(data.estimates || {}).length,
            success: true
        };
        
        console.log(`${type.toUpperCase()} UPDATE SUMMARY:`, summary);
    }

    /**
     * Get scheduler status
     * @returns {object} Current status
     */
    getStatus() {
        return {
            isRunning: this.isRunning,
            activeSchedules: Array.from(this.intervals.keys()),
            nextRuns: {
                // This could be enhanced to show actual next run times
                daily: 'Scheduled',
                weekly: 'Scheduled'
            }
        };
    }
}

// Export singleton instance
export const dataScheduler = new DataUpdateScheduler();

// Auto-start scheduler if running as main module
if (process.env.AUTO_START_SCHEDULER === 'true') {
    dataScheduler.start();
}

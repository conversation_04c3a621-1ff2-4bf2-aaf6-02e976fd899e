import _ from 'lodash';
import moment from 'moment';
import MarkdownIt from 'markdown-it';
import TurndownService from 'turndown';
import { config } from '../config/index.js';
import { logger } from '../utils/logger.js';

class ThreadMerger {
  constructor() {
    this.markdown = new MarkdownIt();
    this.turndown = new TurndownService();
    this.maxContextLength = config.search.maxContextLength;
  }

  async mergeThreads(searchResults, options = {}) {
    const startTime = Date.now();
    logger.info('Starting thread merging', { 
      threadCount: searchResults.length,
      options 
    });

    try {
      // Extract threads from search results
      const threads = searchResults.map(result => result.thread);
      
      // Sort threads by relevance and date
      const sortedThreads = this.sortThreads(threads, options);
      
      // Deduplicate content
      const deduplicatedThreads = this.deduplicateContent(sortedThreads, options);
      
      // Merge and format
      const mergedContent = this.formatMergedThreads(deduplicatedThreads, options);
      
      // Apply context length limits
      const chunkedContent = this.chunkContent(mergedContent, options);

      const duration = Date.now() - startTime;
      logger.info('Thread merging completed', {
        originalThreads: threads.length,
        deduplicatedThreads: deduplicatedThreads.length,
        chunks: chunkedContent.length,
        duration: `${duration}ms`
      });

      return {
        threads: deduplicatedThreads,
        mergedContent,
        chunks: chunkedContent,
        metadata: {
          originalCount: threads.length,
          deduplicatedCount: deduplicatedThreads.length,
          totalMessages: deduplicatedThreads.reduce((sum, t) => sum + t.messages.length, 0),
          sources: [...new Set(threads.map(t => t.source))],
          dateRange: this.getDateRange(threads),
          mergedAt: new Date().toISOString()
        }
      };
    } catch (error) {
      logger.error('Thread merging failed', { error: error.message });
      throw error;
    }
  }

  sortThreads(threads, options = {}) {
    const sortBy = options.sortBy || 'relevance_date';
    
    switch (sortBy) {
      case 'date':
        return threads.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
      
      case 'relevance':
        // Assume threads are already sorted by relevance from search
        return threads;
      
      case 'relevance_date':
      default:
        // Combine relevance and recency
        return threads.sort((a, b) => {
          const aDate = moment(a.created_at);
          const bDate = moment(b.created_at);
          const daysDiff = Math.abs(aDate.diff(bDate, 'days'));
          
          // If dates are close (within 7 days), maintain relevance order
          if (daysDiff <= 7) {
            return 0; // Keep original order (relevance-based)
          }
          
          // Otherwise, prefer more recent
          return bDate - aDate;
        });
    }
  }

  deduplicateContent(threads, options = {}) {
    const similarityThreshold = options.deduplicationThreshold || 0.8;
    const deduplicatedThreads = [];
    
    logger.info('Deduplicating thread content', { 
      threshold: similarityThreshold 
    });

    for (const thread of threads) {
      let isDuplicate = false;
      
      for (const existingThread of deduplicatedThreads) {
        const similarity = this.calculateThreadSimilarity(thread, existingThread);
        
        if (similarity > similarityThreshold) {
          logger.debug(`Thread ${thread.id} is similar to ${existingThread.id} (${similarity.toFixed(2)})`);
          
          // Merge highlights and metadata instead of discarding
          existingThread.highlights = [
            ...(existingThread.highlights || []),
            ...(thread.highlights || [])
          ];
          
          existingThread.metadata = {
            ...existingThread.metadata,
            duplicateOf: existingThread.id,
            mergedThreads: [
              ...(existingThread.metadata.mergedThreads || []),
              thread.id
            ]
          };
          
          isDuplicate = true;
          break;
        }
      }
      
      if (!isDuplicate) {
        deduplicatedThreads.push(_.cloneDeep(thread));
      }
    }

    logger.info(`Deduplicated ${threads.length} threads to ${deduplicatedThreads.length}`);
    return deduplicatedThreads;
  }

  calculateThreadSimilarity(threadA, threadB) {
    // Calculate similarity based on content overlap
    const contentA = this.extractThreadText(threadA).toLowerCase();
    const contentB = this.extractThreadText(threadB).toLowerCase();
    
    // Simple Jaccard similarity for now
    const wordsA = new Set(contentA.split(/\s+/));
    const wordsB = new Set(contentB.split(/\s+/));
    
    const intersection = new Set([...wordsA].filter(x => wordsB.has(x)));
    const union = new Set([...wordsA, ...wordsB]);
    
    return intersection.size / union.size;
  }

  formatMergedThreads(threads, options = {}) {
    const format = options.format || 'markdown';
    
    switch (format) {
      case 'json':
        return this.formatAsJSON(threads, options);
      case 'markdown':
      default:
        return this.formatAsMarkdown(threads, options);
    }
  }

  formatAsMarkdown(threads, options = {}) {
    let markdown = `# Merged Thread Analysis\n\n`;
    markdown += `**Generated**: ${new Date().toISOString()}\n`;
    markdown += `**Threads**: ${threads.length}\n`;
    markdown += `**Sources**: ${[...new Set(threads.map(t => t.source))].join(', ')}\n\n`;

    // Add summary section
    if (options.includeSummary !== false) {
      markdown += `## Summary\n\n`;
      markdown += this.generateThreadSummary(threads);
      markdown += `\n\n`;
    }

    // Add individual threads
    threads.forEach((thread, index) => {
      markdown += `## Thread ${index + 1}: ${thread.title || 'Untitled'}\n\n`;
      markdown += `**Source**: ${thread.source}\n`;
      markdown += `**ID**: ${thread.id}\n`;
      markdown += `**Created**: ${thread.created_at}\n`;
      markdown += `**Messages**: ${thread.messages.length}\n\n`;

      // Add highlights if available
      if (thread.highlights && thread.highlights.length > 0) {
        markdown += `### Key Highlights\n\n`;
        thread.highlights.forEach(highlight => {
          markdown += `> ${highlight.text}\n\n`;
        });
      }

      // Add conversation
      markdown += `### Conversation\n\n`;
      thread.messages.forEach((message, msgIndex) => {
        const role = message.role === 'user' ? '👤 User' : '🤖 Assistant';
        markdown += `**${role}** (Message ${msgIndex + 1}):\n\n`;
        markdown += `${message.content}\n\n`;
        markdown += `---\n\n`;
      });

      markdown += `\n`;
    });

    return markdown;
  }

  formatAsJSON(threads, options = {}) {
    return JSON.stringify({
      metadata: {
        generatedAt: new Date().toISOString(),
        threadCount: threads.length,
        sources: [...new Set(threads.map(t => t.source))],
        totalMessages: threads.reduce((sum, t) => sum + t.messages.length, 0)
      },
      threads: threads.map(thread => ({
        id: thread.id,
        source: thread.source,
        title: thread.title,
        created_at: thread.created_at,
        messages: thread.messages,
        highlights: thread.highlights || [],
        metadata: thread.metadata || {}
      }))
    }, null, 2);
  }

  generateThreadSummary(threads) {
    const totalMessages = threads.reduce((sum, t) => sum + t.messages.length, 0);
    const sources = [...new Set(threads.map(t => t.source))];
    const dateRange = this.getDateRange(threads);
    
    let summary = `This analysis combines ${threads.length} conversation threads `;
    summary += `from ${sources.join(' and ')} containing ${totalMessages} total messages. `;
    summary += `The conversations span from ${dateRange.earliest} to ${dateRange.latest}.\n\n`;
    
    // Extract common topics/themes
    const commonTerms = this.extractCommonTerms(threads);
    if (commonTerms.length > 0) {
      summary += `**Common topics**: ${commonTerms.slice(0, 10).join(', ')}\n\n`;
    }
    
    return summary;
  }

  extractCommonTerms(threads) {
    const termCounts = {};
    
    threads.forEach(thread => {
      const text = this.extractThreadText(thread).toLowerCase();
      const words = text.match(/\b\w{4,}\b/g) || [];
      
      words.forEach(word => {
        termCounts[word] = (termCounts[word] || 0) + 1;
      });
    });
    
    return Object.entries(termCounts)
      .filter(([term, count]) => count >= 2)
      .sort((a, b) => b[1] - a[1])
      .map(([term]) => term);
  }

  chunkContent(content, options = {}) {
    const maxChunkSize = options.maxChunkSize || this.maxContextLength;
    const overlap = options.chunkOverlap || 200;
    
    if (content.length <= maxChunkSize) {
      return [content];
    }
    
    const chunks = [];
    let start = 0;
    
    while (start < content.length) {
      const end = Math.min(start + maxChunkSize, content.length);
      let chunk = content.substring(start, end);
      
      // Try to break at a natural boundary (paragraph, sentence)
      if (end < content.length) {
        const lastParagraph = chunk.lastIndexOf('\n\n');
        const lastSentence = chunk.lastIndexOf('. ');
        
        if (lastParagraph > start + maxChunkSize * 0.7) {
          chunk = chunk.substring(0, lastParagraph + 2);
        } else if (lastSentence > start + maxChunkSize * 0.7) {
          chunk = chunk.substring(0, lastSentence + 2);
        }
      }
      
      chunks.push(chunk);
      start = chunk.length - overlap;
      
      if (start >= content.length) break;
    }
    
    logger.info(`Content chunked into ${chunks.length} pieces`);
    return chunks;
  }

  extractThreadText(thread) {
    return thread.messages.map(m => m.content).join(' ');
  }

  getDateRange(threads) {
    const dates = threads.map(t => new Date(t.created_at)).filter(d => !isNaN(d));
    
    if (dates.length === 0) {
      return { earliest: 'Unknown', latest: 'Unknown' };
    }
    
    const earliest = new Date(Math.min(...dates));
    const latest = new Date(Math.max(...dates));
    
    return {
      earliest: earliest.toISOString().split('T')[0],
      latest: latest.toISOString().split('T')[0]
    };
  }
}

export default ThreadMerger;

"""
Sensor Log Matcher for Drone AI

Pattern matching component that analyzes sensor data against expected patterns,
flight plans, and operational parameters to identify potential issues.
"""

import math
import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta


class SensorLogMatcher:
    """
    Matches sensor data against expected patterns and operational parameters.
    
    Identifies anomalies, deviations from flight plans, and potential issues
    in drone sensor data and AI outputs.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.patterns = {}
        self.thresholds = self._initialize_default_thresholds()
        
    def _initialize_default_thresholds(self) -> Dict[str, Any]:
        """Initialize default thresholds for various sensor parameters."""
        return {
            'gps': {
                'accuracy_threshold': 5.0,  # meters
                'altitude_min': -100.0,     # meters
                'altitude_max': 10000.0,    # meters
                'speed_max': 50.0           # m/s
            },
            'imu': {
                'acceleration_max': 50.0,   # m/s²
                'gyro_max': 10.0,          # rad/s
                'vibration_threshold': 5.0  # m/s²
            },
            'environmental': {
                'temperature_min': -40.0,   # °C
                'temperature_max': 60.0,    # °C
                'humidity_max': 100.0,      # %
                'pressure_min': 300.0,      # hPa
                'pressure_max': 1100.0,     # hPa
                'wind_speed_max': 25.0      # m/s
            },
            'mission': {
                'waypoint_tolerance': 10.0,  # meters
                'altitude_tolerance': 5.0,   # meters
                'heading_tolerance': 15.0,   # degrees
                'time_tolerance': 30.0       # seconds
            }
        }
    
    def match(self, interpreted_output: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Match interpreted sensor data against expected patterns.
        
        Args:
            interpreted_output: Normalized sensor data from interpreter
            context: Additional context including mission parameters
            
        Returns:
            Match results with confidence scores and identified patterns
        """
        try:
            match_results = {
                'overall_confidence': 1.0,
                'pattern_matches': [],
                'anomalies': [],
                'warnings': [],
                'metadata': {
                    'match_timestamp': datetime.utcnow().isoformat(),
                    'matcher_version': '1.0.0'
                }
            }
            
            # Check for data quality issues
            self._check_data_quality(interpreted_output, match_results)
            
            # Check sensor-specific patterns
            sensor_type = context.get('sensor_type', 'mixed')
            if sensor_type == 'gps' or 'latitude' in interpreted_output:
                self._check_gps_patterns(interpreted_output, context, match_results)
            
            if sensor_type == 'imu' or any(key.startswith('acceleration') for key in interpreted_output):
                self._check_imu_patterns(interpreted_output, context, match_results)
            
            if sensor_type == 'environmental' or 'temperature' in interpreted_output:
                self._check_environmental_patterns(interpreted_output, context, match_results)
            
            # Check mission-specific patterns
            if 'mission_plan' in context:
                self._check_mission_patterns(interpreted_output, context, match_results)
            
            # Calculate overall confidence
            self._calculate_overall_confidence(match_results)
            
            return match_results
            
        except Exception as e:
            self.logger.error(f"Error in pattern matching: {str(e)}")
            return {
                'overall_confidence': 0.0,
                'pattern_matches': [],
                'anomalies': [{'type': 'matcher_error', 'message': str(e)}],
                'warnings': [],
                'metadata': {'error': True, 'error_message': str(e)}
            }
    
    def _check_data_quality(self, data: Dict[str, Any], results: Dict[str, Any]) -> None:
        """Check basic data quality issues."""
        # Check for missing critical fields
        if data.get('error', False):
            results['anomalies'].append({
                'type': 'data_error',
                'severity': 'high',
                'message': data.get('error_message', 'Unknown data error'),
                'confidence_impact': -0.5
            })
            return
        
        # Check for null/invalid values in critical fields
        critical_fields = ['latitude', 'longitude', 'altitude', 'temperature']
        for field in critical_fields:
            if field in data:
                value = data[field]
                if value is None or (isinstance(value, str) and value.lower() in ['null', 'nan', 'none']):
                    results['warnings'].append({
                        'type': 'null_value',
                        'field': field,
                        'message': f'Null value detected in critical field: {field}',
                        'confidence_impact': -0.1
                    })
    
    def _check_gps_patterns(self, data: Dict[str, Any], context: Dict[str, Any], results: Dict[str, Any]) -> None:
        """Check GPS-specific patterns and anomalies."""
        gps_thresholds = self.thresholds['gps']
        
        # Check GPS accuracy
        accuracy = data.get('accuracy')
        if accuracy is not None and accuracy > gps_thresholds['accuracy_threshold']:
            results['warnings'].append({
                'type': 'poor_gps_accuracy',
                'value': accuracy,
                'threshold': gps_thresholds['accuracy_threshold'],
                'message': f'GPS accuracy ({accuracy}m) exceeds threshold',
                'confidence_impact': -0.2
            })
        
        # Check altitude bounds
        altitude = data.get('altitude')
        if altitude is not None:
            if altitude < gps_thresholds['altitude_min'] or altitude > gps_thresholds['altitude_max']:
                results['anomalies'].append({
                    'type': 'altitude_out_of_bounds',
                    'value': altitude,
                    'bounds': [gps_thresholds['altitude_min'], gps_thresholds['altitude_max']],
                    'severity': 'high',
                    'message': f'Altitude ({altitude}m) outside operational bounds',
                    'confidence_impact': -0.4
                })
        
        # Check coordinate validity
        lat = data.get('latitude')
        lon = data.get('longitude')
        if lat is not None and lon is not None:
            if not (-90 <= lat <= 90) or not (-180 <= lon <= 180):
                results['anomalies'].append({
                    'type': 'invalid_coordinates',
                    'latitude': lat,
                    'longitude': lon,
                    'severity': 'critical',
                    'message': 'GPS coordinates outside valid range',
                    'confidence_impact': -0.6
                })
            else:
                results['pattern_matches'].append({
                    'type': 'valid_coordinates',
                    'confidence': 0.9,
                    'message': 'GPS coordinates within valid range'
                })
    
    def _check_imu_patterns(self, data: Dict[str, Any], context: Dict[str, Any], results: Dict[str, Any]) -> None:
        """Check IMU-specific patterns and anomalies."""
        imu_thresholds = self.thresholds['imu']
        
        # Check acceleration values
        accel_fields = ['acceleration_x', 'acceleration_y', 'acceleration_z']
        accelerations = []
        
        for field in accel_fields:
            if field in data:
                accel = data[field]
                if accel is not None:
                    accelerations.append(abs(accel))
                    
                    if abs(accel) > imu_thresholds['acceleration_max']:
                        results['warnings'].append({
                            'type': 'high_acceleration',
                            'field': field,
                            'value': accel,
                            'threshold': imu_thresholds['acceleration_max'],
                            'message': f'High acceleration detected: {field}={accel}',
                            'confidence_impact': -0.15
                        })
        
        # Check for excessive vibration (high frequency acceleration changes)
        if len(accelerations) >= 3:
            total_accel = math.sqrt(sum(a**2 for a in accelerations))
            if total_accel > imu_thresholds['vibration_threshold']:
                results['warnings'].append({
                    'type': 'excessive_vibration',
                    'total_acceleration': total_accel,
                    'threshold': imu_thresholds['vibration_threshold'],
                    'message': f'Excessive vibration detected: {total_accel:.2f}',
                    'confidence_impact': -0.2
                })
        
        # Check gyroscope values
        gyro_fields = ['gyro_x', 'gyro_y', 'gyro_z']
        for field in gyro_fields:
            if field in data:
                gyro = data[field]
                if gyro is not None and abs(gyro) > imu_thresholds['gyro_max']:
                    results['warnings'].append({
                        'type': 'high_angular_velocity',
                        'field': field,
                        'value': gyro,
                        'threshold': imu_thresholds['gyro_max'],
                        'message': f'High angular velocity: {field}={gyro}',
                        'confidence_impact': -0.15
                    })
    
    def _check_environmental_patterns(self, data: Dict[str, Any], context: Dict[str, Any], results: Dict[str, Any]) -> None:
        """Check environmental sensor patterns."""
        env_thresholds = self.thresholds['environmental']
        
        # Check temperature
        temp = data.get('temperature')
        if temp is not None:
            if temp < env_thresholds['temperature_min'] or temp > env_thresholds['temperature_max']:
                results['warnings'].append({
                    'type': 'extreme_temperature',
                    'value': temp,
                    'bounds': [env_thresholds['temperature_min'], env_thresholds['temperature_max']],
                    'message': f'Extreme temperature: {temp}°C',
                    'confidence_impact': -0.1
                })
        
        # Check wind conditions
        wind_speed = data.get('wind_speed')
        if wind_speed is not None and wind_speed > env_thresholds['wind_speed_max']:
            results['warnings'].append({
                'type': 'high_wind_speed',
                'value': wind_speed,
                'threshold': env_thresholds['wind_speed_max'],
                'message': f'High wind speed: {wind_speed} m/s',
                'confidence_impact': -0.25
            })
    
    def _check_mission_patterns(self, data: Dict[str, Any], context: Dict[str, Any], results: Dict[str, Any]) -> None:
        """Check mission-specific patterns and waypoint adherence."""
        mission_plan = context.get('mission_plan', {})
        mission_thresholds = self.thresholds['mission']
        
        # Check waypoint adherence
        current_waypoint = mission_plan.get('current_waypoint')
        if current_waypoint and 'latitude' in data and 'longitude' in data:
            distance = self._calculate_distance(
                data['latitude'], data['longitude'],
                current_waypoint.get('latitude'), current_waypoint.get('longitude')
            )
            
            if distance > mission_thresholds['waypoint_tolerance']:
                results['warnings'].append({
                    'type': 'waypoint_deviation',
                    'distance': distance,
                    'tolerance': mission_thresholds['waypoint_tolerance'],
                    'message': f'Deviation from waypoint: {distance:.1f}m',
                    'confidence_impact': -0.2
                })
            else:
                results['pattern_matches'].append({
                    'type': 'waypoint_adherence',
                    'confidence': 0.8,
                    'distance': distance,
                    'message': f'Within waypoint tolerance: {distance:.1f}m'
                })
    
    def _calculate_distance(self, lat1: float, lon1: float, lat2: float, lon2: float) -> float:
        """Calculate distance between two GPS coordinates using Haversine formula."""
        if None in [lat1, lon1, lat2, lon2]:
            return float('inf')
        
        R = 6371000  # Earth's radius in meters
        
        lat1_rad = math.radians(lat1)
        lat2_rad = math.radians(lat2)
        delta_lat = math.radians(lat2 - lat1)
        delta_lon = math.radians(lon2 - lon1)
        
        a = (math.sin(delta_lat/2)**2 + 
             math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(delta_lon/2)**2)
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
        
        return R * c
    
    def _calculate_overall_confidence(self, results: Dict[str, Any]) -> None:
        """Calculate overall confidence based on all findings."""
        confidence = 1.0
        
        # Apply confidence impacts from anomalies and warnings
        for anomaly in results['anomalies']:
            impact = anomaly.get('confidence_impact', -0.3)
            confidence += impact
        
        for warning in results['warnings']:
            impact = warning.get('confidence_impact', -0.1)
            confidence += impact
        
        # Boost confidence for positive pattern matches
        for match in results['pattern_matches']:
            boost = match.get('confidence', 0.1) * 0.1
            confidence += boost
        
        # Ensure confidence stays within bounds
        results['overall_confidence'] = max(0.0, min(1.0, confidence))
    
    def add_pattern(self, pattern_id: str, pattern_data: Dict[str, Any]) -> None:
        """Add a new pattern to the matcher."""
        self.patterns[pattern_id] = pattern_data
        self.logger.info(f"Added pattern: {pattern_id}")
    
    def remove_pattern(self, pattern_id: str) -> bool:
        """Remove a pattern from the matcher."""
        if pattern_id in self.patterns:
            del self.patterns[pattern_id]
            self.logger.info(f"Removed pattern: {pattern_id}")
            return True
        return False
    
    def update_thresholds(self, category: str, thresholds: Dict[str, Any]) -> None:
        """Update thresholds for a specific category."""
        if category in self.thresholds:
            self.thresholds[category].update(thresholds)
        else:
            self.thresholds[category] = thresholds
        self.logger.info(f"Updated thresholds for category: {category}")

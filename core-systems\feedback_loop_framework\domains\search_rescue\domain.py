"""
Search and Rescue Domain Implementation

Complete domain implementation that coordinates visual detection, reference matching,
mission validation, and side pocket management for Search and Rescue drone operations.
"""

import logging
from typing import Dict, Any, List
from ...core.interfaces import BaseDomain
from ...core.feedback_types import ValidationR<PERSON>ult, FeedbackType
from .visual_detector import VisualDetectionInterpreter
from .reference_matcher import ReferenceItemMatcher
from .mission_validator import SearchRescueValidator
from .side_pocket_manager import SidePocketManager


class SearchRescueDomain(BaseDomain):
    """
    Complete domain implementation for Search and Rescue drone operations.
    
    Coordinates visual detection interpretation, reference item matching,
    mission validation, and side pocket management for continuous improvement
    of Search and Rescue AI systems.
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize Search and Rescue domain.
        
        Args:
            config: Optional configuration dictionary for domain customization
        """
        super().__init__("search_rescue")
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # Domain-specific settings
        self.detection_categories = ['target_person', 'clothing', 'personal_items', 'broken_environment']
        self.mission_types = ['lost_child', 'missing_person', 'disaster_response', 'wilderness_search']
        self.search_patterns = ['grid', 'spiral', 'sector', 'contour', 'random']
        
        # Side pocket configuration
        self.side_pocket_config = {
            'base_path': self.config.get('side_pocket_path', './sar_side_pocket'),
            'retention_days': self.config.get('side_pocket_retention', 90),
            'auto_review_enabled': self.config.get('auto_review', True),
            'retraining_threshold': self.config.get('retraining_threshold', 50)
        }
        
        # Performance tracking
        self.processing_stats = {
            'total_processed': 0,
            'by_mission_type': {},
            'by_detection_category': {},
            'by_outcome': {},
            'side_pocket_usage': 0,
            'retraining_batches_created': 0,
            'average_processing_time': 0.0
        }
        
        # Mission outcome tracking
        self.mission_outcomes = {
            'target_found': 0,
            'target_likely': 0,
            'reference_items_found': 0,
            'search_incomplete': 0,
            'no_findings': 0,
            'mission_failed': 0
        }
    
    def initialize_components(self) -> None:
        """Initialize domain-specific components."""
        try:
            # Initialize visual detection interpreter
            self.interpreter = VisualDetectionInterpreter()
            
            # Add custom detection categories if provided
            if 'custom_categories' in self.config:
                for category, config in self.config['custom_categories'].items():
                    self.interpreter.add_custom_category(category, config)
            
            # Initialize reference item matcher
            self.matcher = ReferenceItemMatcher()
            
            # Update reference criteria if provided
            if 'reference_criteria' in self.config:
                for category, criteria in self.config['reference_criteria'].items():
                    self.matcher.update_reference_criteria(category, criteria)
            
            # Initialize mission validator
            self.validator = SearchRescueValidator()
            
            # Update mission thresholds if provided
            if 'mission_thresholds' in self.config:
                for category, thresholds in self.config['mission_thresholds'].items():
                    self.validator.update_mission_thresholds(category, thresholds)
            
            # Initialize side pocket manager
            self.side_pocket_manager = SidePocketManager(self.side_pocket_config)
            
            self.logger.info("Search and Rescue domain components initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Search and Rescue domain components: {str(e)}")
            raise
    
    def process_output(self, raw_output: Any, context: Dict[str, Any]) -> ValidationResult:
        """
        Process Search and Rescue output through the complete domain pipeline.
        
        Args:
            raw_output: Raw detection data from drone sensors/cameras
            context: Mission context including objectives, target info, search parameters
            
        Returns:
            ValidationResult with complete mission analysis and side pocket management
        """
        try:
            # Enhance context with domain-specific information
            enhanced_context = self._enhance_context(context)
            
            # Step 1: Visual detection interpretation
            self.logger.debug("Starting visual detection interpretation")
            interpreted_output = self.interpreter.interpret(raw_output, enhanced_context)
            
            # Step 2: Reference item matching
            self.logger.debug("Starting reference item matching")
            match_results = self.matcher.match(interpreted_output, enhanced_context)
            
            # Step 3: Mission validation
            self.logger.debug("Starting mission validation")
            validation_result = self.validator.validate(
                interpreted_output, match_results, enhanced_context
            )
            
            # Step 4: Side pocket management
            self._handle_side_pocket_items(interpreted_output, match_results, validation_result, enhanced_context)
            
            # Add domain-specific metadata
            validation_result.metadata.update({
                'domain': self.domain_name,
                'mission_type': enhanced_context.get('mission_type', 'unknown'),
                'detection_categories_found': self._extract_detection_categories(interpreted_output),
                'mission_outcome': self._extract_mission_outcome(validation_result),
                'side_pocket_items': self._count_side_pocket_items(interpreted_output, match_results),
                'processing_pipeline': ['interpret', 'match', 'validate', 'side_pocket']
            })
            
            # Update statistics
            self._update_processing_stats(enhanced_context, validation_result)
            
            self.logger.info(
                f"Processed Search and Rescue output: "
                f"mission_type={enhanced_context.get('mission_type')}, "
                f"outcome={validation_result.metadata.get('mission_outcome')}, "
                f"feedback_type={validation_result.feedback_type.value}, "
                f"confidence={validation_result.confidence_score:.3f}"
            )
            
            return validation_result
            
        except Exception as e:
            self.logger.error(f"Error processing Search and Rescue output: {str(e)}")
            
            # Return error validation result
            error_result = ValidationResult()
            error_result.is_valid = False
            error_result.confidence_score = 0.0
            error_result.issues = [{
                'type': 'domain_processing_error',
                'severity': 'critical',
                'message': f'Search and Rescue processing failed: {str(e)}',
                'details': {'error': str(e), 'domain': self.domain_name}
            }]
            error_result.metadata = {
                'domain': self.domain_name,
                'error': True,
                'error_message': str(e)
            }
            
            return error_result
    
    def _enhance_context(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Enhance context with Search and Rescue specific information."""
        enhanced = context.copy()
        
        # Set default mission type if not provided
        if 'mission_type' not in enhanced:
            enhanced['mission_type'] = self._detect_mission_type(context)
        
        # Set default search pattern if not provided
        if 'search_pattern' not in enhanced:
            enhanced['search_pattern'] = context.get('search_pattern', 'grid')
        
        # Add target information if available
        if 'target_info' not in enhanced:
            enhanced['target_info'] = self._extract_target_info(context)
        
        # Add mission parameters
        enhanced['mission_parameters'] = {
            'search_area_km2': context.get('search_area_km2', 1.0),
            'target_age': context.get('target_age', 'unknown'),
            'target_description': context.get('target_description', {}),
            'last_known_location': context.get('last_known_location', {}),
            'time_missing_hours': context.get('time_missing_hours', 0)
        }
        
        # Add environmental conditions
        enhanced.update({
            'weather_conditions': context.get('weather', 'clear'),
            'lighting_conditions': context.get('lighting', 'daylight'),
            'terrain_type': context.get('terrain', 'mixed'),
            'visibility_km': context.get('visibility', 10.0),
            'wind_speed_mps': context.get('wind_speed', 0.0)
        })
        
        # Add domain capabilities
        enhanced['domain_capabilities'] = {
            'supported_detection_categories': self.detection_categories,
            'supported_mission_types': self.mission_types,
            'supported_search_patterns': self.search_patterns,
            'supports_partial_correctness': True,
            'supports_side_pocket': True,
            'supports_retraining': True,
            'supports_real_time': True
        }
        
        # Add processing timestamp
        from datetime import datetime
        enhanced['domain_processing_timestamp'] = datetime.utcnow().isoformat()
        
        return enhanced
    
    def _detect_mission_type(self, context: Dict[str, Any]) -> str:
        """Detect mission type from context clues."""
        context_str = str(context).lower()
        
        # Check for specific mission type indicators
        if any(keyword in context_str for keyword in ['child', 'kid', 'minor']):
            return 'lost_child'
        
        if any(keyword in context_str for keyword in ['disaster', 'emergency', 'rescue']):
            return 'disaster_response'
        
        if any(keyword in context_str for keyword in ['wilderness', 'forest', 'mountain']):
            return 'wilderness_search'
        
        return 'missing_person'  # Default mission type
    
    def _extract_target_info(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Extract target information from context."""
        return {
            'age': context.get('target_age', 'unknown'),
            'description': context.get('target_description', {}),
            'clothing': context.get('target_clothing', {}),
            'last_seen': context.get('last_known_location', {}),
            'medical_conditions': context.get('medical_conditions', []),
            'mobility': context.get('mobility_status', 'unknown')
        }
    
    def _handle_side_pocket_items(self, interpreted_output: Dict[str, Any], 
                                 match_results: Dict[str, Any], 
                                 validation_result: ValidationResult,
                                 context: Dict[str, Any]) -> None:
        """Handle items that should go to the side pocket."""
        classifications = interpreted_output.get('classifications', [])
        
        # Check for uncertain detections
        for classification in classifications:
            confidence = classification.get('confidence', 0.0)
            category = classification.get('sar_category', 'unknown')
            
            # Low confidence detections go to side pocket
            if confidence < 0.5:
                self.side_pocket_manager.add_to_side_pocket(
                    detection_data=classification,
                    reason=f"Low confidence detection: {confidence:.2f}",
                    category='uncertain',
                    mission_context=context
                )
                self.processing_stats['side_pocket_usage'] += 1
        
        # Check for potential misclassifications based on warnings
        for warning in match_results.get('warnings', []):
            if warning.get('type') == 'potential_misidentification':
                classification = warning.get('classification', {})
                self.side_pocket_manager.add_to_side_pocket(
                    detection_data=classification,
                    reason=f"Potential misidentification: {warning.get('misidentification_type')}",
                    category='misclassified',
                    mission_context=context
                )
                self.processing_stats['side_pocket_usage'] += 1
        
        # Check for environmental artifacts
        for warning in match_results.get('warnings', []):
            if warning.get('type') == 'poor_environmental_conditions':
                # Add environmental context to side pocket for training
                env_data = {
                    'environmental_factors': warning.get('environmental_factor'),
                    'weather': warning.get('weather'),
                    'lighting': warning.get('lighting'),
                    'terrain': warning.get('terrain')
                }
                self.side_pocket_manager.add_to_side_pocket(
                    detection_data=env_data,
                    reason="Poor environmental conditions affecting detection",
                    category='environmental',
                    mission_context=context
                )
    
    def _extract_detection_categories(self, interpreted_output: Dict[str, Any]) -> List[str]:
        """Extract unique detection categories found."""
        classifications = interpreted_output.get('classifications', [])
        categories = set()
        
        for classification in classifications:
            category = classification.get('sar_category', 'unknown')
            if category != 'unknown':
                categories.add(category)
        
        return list(categories)
    
    def _extract_mission_outcome(self, validation_result: ValidationResult) -> str:
        """Extract mission outcome from validation result."""
        feedback_type = validation_result.feedback_type
        
        # Map feedback types to mission outcomes
        if feedback_type == FeedbackType.CORRECT:
            return 'target_found'
        elif feedback_type == FeedbackType.PARTIALLY_CORRECT:
            # Determine specific partial outcome from issues/recommendations
            for issue in validation_result.issues:
                if 'reference' in issue.get('type', '').lower():
                    return 'reference_items_found'
                elif 'incomplete' in issue.get('type', '').lower():
                    return 'search_incomplete'
            return 'target_likely'
        elif feedback_type == FeedbackType.INCORRECT:
            return 'no_findings'
        else:
            return 'mission_failed'
    
    def _count_side_pocket_items(self, interpreted_output: Dict[str, Any], match_results: Dict[str, Any]) -> int:
        """Count items that were sent to side pocket."""
        count = 0
        
        # Count low confidence detections
        classifications = interpreted_output.get('classifications', [])
        count += sum(1 for c in classifications if c.get('confidence', 0.0) < 0.5)
        
        # Count potential misclassifications
        warnings = match_results.get('warnings', [])
        count += sum(1 for w in warnings if w.get('type') == 'potential_misidentification')
        
        return count
    
    def _update_processing_stats(self, context: Dict[str, Any], validation_result: ValidationResult) -> None:
        """Update domain processing statistics."""
        self.processing_stats['total_processed'] += 1
        
        mission_type = context.get('mission_type', 'unknown')
        self.processing_stats['by_mission_type'][mission_type] = (
            self.processing_stats['by_mission_type'].get(mission_type, 0) + 1
        )
        
        # Update detection category stats
        categories = validation_result.metadata.get('detection_categories_found', [])
        for category in categories:
            self.processing_stats['by_detection_category'][category] = (
                self.processing_stats['by_detection_category'].get(category, 0) + 1
            )
        
        # Update outcome stats
        outcome = validation_result.metadata.get('mission_outcome', 'unknown')
        self.processing_stats['by_outcome'][outcome] = (
            self.processing_stats['by_outcome'].get(outcome, 0) + 1
        )
        
        if outcome in self.mission_outcomes:
            self.mission_outcomes[outcome] += 1
    
    def get_domain_info(self) -> Dict[str, Any]:
        """Get comprehensive information about this domain."""
        base_info = super().get_domain_info()
        
        domain_specific_info = {
            'supported_detection_categories': self.detection_categories,
            'supported_mission_types': self.mission_types,
            'supported_search_patterns': self.search_patterns,
            'processing_stats': self.processing_stats.copy(),
            'mission_outcomes': self.mission_outcomes.copy(),
            'side_pocket_stats': self.side_pocket_manager.get_stats() if hasattr(self, 'side_pocket_manager') else {},
            'configuration': {
                'has_custom_categories': 'custom_categories' in self.config,
                'has_custom_criteria': 'reference_criteria' in self.config,
                'has_custom_thresholds': 'mission_thresholds' in self.config,
                'side_pocket_enabled': True
            },
            'capabilities': {
                'partial_correctness': True,
                'real_time_processing': True,
                'visual_detection': True,
                'reference_matching': True,
                'mission_validation': True,
                'side_pocket_management': True,
                'continuous_learning': True,
                'retraining_support': True
            }
        }
        
        return {**base_info, **domain_specific_info}
    
    def get_mission_summary(self, mission_id: str) -> Dict[str, Any]:
        """Get summary for a specific mission."""
        # This would retrieve mission-specific data
        # For now, return current stats
        return {
            'mission_id': mission_id,
            'total_detections': self.processing_stats['total_processed'],
            'outcomes': self.mission_outcomes.copy(),
            'side_pocket_items': self.processing_stats['side_pocket_usage']
        }
    
    def create_retraining_batch(self) -> Dict[str, Any]:
        """Create a retraining batch from side pocket data."""
        if hasattr(self, 'side_pocket_manager'):
            batch_result = self.side_pocket_manager.create_retraining_batch()
            if batch_result.get('status') == 'success':
                self.processing_stats['retraining_batches_created'] += 1
            return batch_result
        else:
            return {'status': 'error', 'message': 'Side pocket manager not initialized'}
    
    def get_pending_reviews(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get pending side pocket items for review."""
        if hasattr(self, 'side_pocket_manager'):
            return self.side_pocket_manager.get_pending_reviews(limit=limit)
        else:
            return []
    
    def review_side_pocket_item(self, item_id: str, review_result: Dict[str, Any]) -> bool:
        """Review a side pocket item."""
        if hasattr(self, 'side_pocket_manager'):
            return self.side_pocket_manager.review_item(item_id, review_result)
        else:
            return False
    
    def get_side_pocket_analytics(self) -> Dict[str, Any]:
        """Get side pocket analytics."""
        if hasattr(self, 'side_pocket_manager'):
            return self.side_pocket_manager.get_analytics()
        else:
            return {}
    
    def cleanup_side_pocket(self, retention_days: int = None) -> int:
        """Clean up old side pocket items."""
        if hasattr(self, 'side_pocket_manager'):
            return self.side_pocket_manager.cleanup_old_items(retention_days)
        else:
            return 0
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """Get detailed processing statistics for this domain."""
        return self.processing_stats.copy()
    
    def get_mission_outcomes(self) -> Dict[str, Any]:
        """Get mission outcome statistics."""
        total_missions = sum(self.mission_outcomes.values())
        if total_missions == 0:
            return self.mission_outcomes.copy()
        
        # Add success rates
        success_outcomes = ['target_found', 'target_likely', 'reference_items_found']
        success_count = sum(self.mission_outcomes[outcome] for outcome in success_outcomes)
        
        return {
            **self.mission_outcomes,
            'total_missions': total_missions,
            'success_rate': success_count / total_missions,
            'target_found_rate': self.mission_outcomes['target_found'] / total_missions,
            'partial_success_rate': (self.mission_outcomes['target_likely'] + self.mission_outcomes['reference_items_found']) / total_missions
        }
    
    def reset_stats(self) -> None:
        """Reset processing statistics."""
        self.processing_stats = {
            'total_processed': 0,
            'by_mission_type': {},
            'by_detection_category': {},
            'by_outcome': {},
            'side_pocket_usage': 0,
            'retraining_batches_created': 0,
            'average_processing_time': 0.0
        }
        
        self.mission_outcomes = {
            'target_found': 0,
            'target_likely': 0,
            'reference_items_found': 0,
            'search_incomplete': 0,
            'no_findings': 0,
            'mission_failed': 0
        }
        
        self.logger.info("Reset processing statistics for Search and Rescue domain")

    def get_domain_info(self) -> Dict[str, Any]:
        """Get domain information and capabilities."""
        return {
            'domain_name': 'search_rescue',
            'supported_detection_categories': [
                'target_person', 'clothing', 'personal_items', 'equipment',
                'shelter', 'vehicle', 'debris', 'terrain_feature'
            ],
            'supported_mission_types': [
                'person_search', 'evidence_collection', 'area_survey',
                'reference_tracking', 'emergency_response'
            ],
            'capabilities': {
                'partial_correctness': True,
                'visual_detection': True,
                'reference_matching': True,
                'mission_validation': True,
                'side_pocket_management': True,
                'confidence_assessment': True,
                'trust_scoring': True,
                'continuous_learning': True
            },
            'version': '1.0.0',
            'description': 'Complete domain implementation for Search and Rescue drone operations'
        }

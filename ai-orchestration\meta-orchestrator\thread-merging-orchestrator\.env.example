# API Keys - Required for the system to function
OPENAI_API_KEY=your_openai_api_key_here
PERPLEXITY_API_KEY=your_perplexity_api_key_here
CLAUDE_API_KEY=your_claude_api_key_here
GEMINI_API_KEY=your_gemini_api_key_here
MISTRAL_API_KEY=your_mistral_api_key_here
COHERE_API_KEY=your_cohere_api_key_here
HUGGINGFACE_API_KEY=your_huggingface_api_key_here
REPLICATE_API_KEY=your_replicate_api_key_here
TOGETHER_API_KEY=your_together_api_key_here
GROQ_API_KEY=your_groq_api_key_here
FIREWORKS_API_KEY=your_fireworks_api_key_here

# Microsoft/Azure APIs
AZURE_OPENAI_API_KEY=your_azure_openai_key_here
AZURE_OPENAI_ENDPOINT=your_azure_endpoint_here
BING_CHAT_API_KEY=your_bing_chat_key_here

# API Base URLs (optional - uses defaults if not specified)
OPENAI_BASE_URL=https://api.openai.com/v1
PERPLEXITY_BASE_URL=https://api.perplexity.ai
CLAUDE_BASE_URL=https://api.anthropic.com
GEMINI_BASE_URL=https://generativelanguage.googleapis.com
MISTRAL_BASE_URL=https://api.mistral.ai/v1
COHERE_BASE_URL=https://api.cohere.ai/v1
HUGGINGFACE_BASE_URL=https://api-inference.huggingface.co
REPLICATE_BASE_URL=https://api.replicate.com/v1
TOGETHER_BASE_URL=https://api.together.xyz/v1
GROQ_BASE_URL=https://api.groq.com/openai/v1
FIREWORKS_BASE_URL=https://api.fireworks.ai/inference/v1

# Rate Limiting Configuration
RATE_LIMIT_REQUESTS_PER_MINUTE=60
RATE_LIMIT_BURST_SIZE=10

# Search Configuration
SEARCH_SIMILARITY_THRESHOLD=0.7
MAX_THREADS_TO_RETRIEVE=50
MAX_CONTEXT_LENGTH=100000

# Logging Configuration
LOG_LEVEL=info
LOG_FILE_PATH=./logs/orchestrator.log

# Web Interface Configuration
WEB_PORT=3000
WEB_HOST=localhost

# Storage Configuration
THREADS_STORAGE_PATH=./data/threads
RESULTS_STORAGE_PATH=./data/results
CACHE_STORAGE_PATH=./data/cache

# Automation Configuration
AUTO_SCHEDULE_ENABLED=false
AUTO_SCHEDULE_CRON=0 */6 * * *

# Browser Automation (for Perplexity if API unavailable)
BROWSER_HEADLESS=true
BROWSER_TIMEOUT=30000

# Default LLM for Code Generation
DEFAULT_TARGET_LLM=claude

# Platform-specific settings
CHATGPT_MODEL=gpt-4-turbo-preview
CLAUDE_MODEL=claude-3-sonnet-20240229
GEMINI_MODEL=gemini-pro
MISTRAL_MODEL=mistral-large-latest
COHERE_MODEL=command-r-plus
LLAMA_MODEL=meta-llama/Llama-2-70b-chat-hf
GROQ_MODEL=llama2-70b-4096

# Thread Sources Configuration
ENABLED_SOURCES=chatgpt,perplexity,claude,gemini,mistral,cohere
THREAD_RETRIEVAL_METHODS=api,browser,export

import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Card,
  CardContent,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  LinearProgress,
  Alert,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  PlayArrow,
  Stop,
  Refresh,
  Timeline,
  TrendingUp,
  Science,
  Visibility,
  AutoFixHigh,
  Speed,
  Psychology,
} from '@mui/icons-material';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, ResponsiveContainer, Bar<PERSON>hart, Bar } from 'recharts';

interface EvolutionDashboardProps {
  onStartEvolution?: (parameters: any) => void;
  onStopEvolution?: () => void;
  onRefresh?: () => void;
}

interface EvolutionStatus {
  isRunning: boolean;
  currentGeneration: number;
  parameters: any;
  activeSession: any;
  populationSize: number;
}

interface EvolutionVariant {
  id: string;
  generation: number;
  mutationType: string;
  fitnessScore: number;
  status: string;
  createdAt: string;
  parent?: {
    id: string;
    name: string;
    performanceScore: number;
  };
}

export const EvolutionDashboard: React.FC<EvolutionDashboardProps> = ({
  onStartEvolution,
  onStopEvolution,
  onRefresh,
}) => {
  const [status, setStatus] = useState<EvolutionStatus | null>(null);
  const [history, setHistory] = useState<EvolutionVariant[]>([]);
  const [startDialog, setStartDialog] = useState(false);
  const [parameters, setParameters] = useState({
    populationSize: 50,
    mutationRate: 0.1,
    crossoverRate: 0.7,
    selectionPressure: 0.8,
    elitismRate: 0.1,
    generationLimit: 100,
    fitnessThreshold: 0.95,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadData();
    const interval = setInterval(loadData, 10000); // Refresh every 10 seconds
    return () => clearInterval(interval);
  }, []);

  const loadData = async () => {
    try {
      setIsLoading(true);
      
      // Load evolution status and history
      const [statusResponse, historyResponse] = await Promise.all([
        fetch('/api/evolution/status'),
        fetch('/api/evolution/history'),
      ]);

      if (statusResponse.ok && historyResponse.ok) {
        const statusData = await statusResponse.json();
        const historyData = await historyResponse.json();
        
        setStatus(statusData.data);
        setHistory(historyData.data);
        setError(null);
      } else {
        throw new Error('Failed to load evolution data');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to load data');
    } finally {
      setIsLoading(false);
    }
  };

  const handleStartEvolution = async () => {
    try {
      await onStartEvolution?.(parameters);
      setStartDialog(false);
      loadData();
    } catch (err: any) {
      setError(err.message || 'Failed to start evolution');
    }
  };

  const handleStopEvolution = async () => {
    try {
      await onStopEvolution?.();
      loadData();
    } catch (err: any) {
      setError(err.message || 'Failed to stop evolution');
    }
  };

  const getMutationTypeIcon = (type: string) => {
    switch (type) {
      case 'PARAMETER_ADJUSTMENT': return <Speed />;
      case 'CAPABILITY_ENHANCEMENT': return <AutoFixHigh />;
      case 'ROLE_SPECIALIZATION': return <Psychology />;
      case 'HYBRID_CREATION': return <Science />;
      default: return <TrendingUp />;
    }
  };

  const getMutationTypeColor = (type: string) => {
    switch (type) {
      case 'PARAMETER_ADJUSTMENT': return 'primary';
      case 'CAPABILITY_ENHANCEMENT': return 'success';
      case 'ROLE_SPECIALIZATION': return 'warning';
      case 'HYBRID_CREATION': return 'secondary';
      default: return 'default';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED': return 'success';
      case 'RUNNING': return 'primary';
      case 'FAILED': return 'error';
      case 'CREATED': return 'info';
      default: return 'default';
    }
  };

  // Prepare chart data
  const chartData = history
    .slice(0, 20)
    .reverse()
    .map((variant, index) => ({
      generation: variant.generation,
      fitness: variant.fitnessScore,
      name: `Gen ${variant.generation}`,
    }));

  const mutationTypeData = history.reduce((acc, variant) => {
    acc[variant.mutationType] = (acc[variant.mutationType] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const mutationChartData = Object.entries(mutationTypeData).map(([type, count]) => ({
    type: type.replace('_', ' '),
    count,
  }));

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h5">Darwin Gödel Machine Evolution</Typography>
        <Box>
          <Button
            startIcon={<Refresh />}
            onClick={onRefresh}
            sx={{ mr: 1 }}
          >
            Refresh
          </Button>
          {status?.isRunning ? (
            <Button
              startIcon={<Stop />}
              variant="contained"
              color="error"
              onClick={handleStopEvolution}
            >
              Stop Evolution
            </Button>
          ) : (
            <Button
              startIcon={<PlayArrow />}
              variant="contained"
              onClick={() => setStartDialog(true)}
            >
              Start Evolution
            </Button>
          )}
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Evolution Status */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" gap={1} mb={1}>
                <Timeline color="primary" />
                <Typography variant="h6">Status</Typography>
              </Box>
              <Chip
                label={status?.isRunning ? 'Running' : 'Stopped'}
                color={status?.isRunning ? 'success' : 'default'}
                size="small"
              />
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6">Generation</Typography>
              <Typography variant="h4" color="primary">
                {status?.currentGeneration || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6">Population</Typography>
              <Typography variant="h4" color="primary">
                {status?.populationSize || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6">Best Fitness</Typography>
              <Typography variant="h4" color="primary">
                {history.length > 0 ? (Math.max(...history.map(h => h.fitnessScore)) * 100).toFixed(1) + '%' : '0%'}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Evolution Progress */}
      {status?.isRunning && (
        <Paper sx={{ p: 3, mb: 3 }}>
          <Typography variant="h6" gutterBottom>Evolution Progress</Typography>
          <LinearProgress
            variant="determinate"
            value={(status.currentGeneration / status.parameters.generationLimit) * 100}
            sx={{ mb: 1 }}
          />
          <Typography variant="body2" color="text.secondary">
            Generation {status.currentGeneration} of {status.parameters.generationLimit}
          </Typography>
        </Paper>
      )}

      {/* Charts */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>Fitness Evolution</Typography>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="generation" />
                <YAxis />
                <RechartsTooltip />
                <Line type="monotone" dataKey="fitness" stroke="#1976d2" strokeWidth={2} />
              </LineChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>Mutation Types</Typography>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={mutationChartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="type" angle={-45} textAnchor="end" height={80} />
                <YAxis />
                <RechartsTooltip />
                <Bar dataKey="count" fill="#1976d2" />
              </BarChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>
      </Grid>

      {/* Evolution History */}
      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>Evolution History</Typography>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Generation</TableCell>
                <TableCell>Mutation Type</TableCell>
                <TableCell>Fitness Score</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Parent</TableCell>
                <TableCell>Created</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {history.slice(0, 10).map((variant) => (
                <TableRow key={variant.id}>
                  <TableCell>{variant.generation}</TableCell>
                  <TableCell>
                    <Box display="flex" alignItems="center" gap={1}>
                      {getMutationTypeIcon(variant.mutationType)}
                      <Chip
                        label={variant.mutationType.replace('_', ' ')}
                        color={getMutationTypeColor(variant.mutationType)}
                        size="small"
                      />
                    </Box>
                  </TableCell>
                  <TableCell>{(variant.fitnessScore * 100).toFixed(1)}%</TableCell>
                  <TableCell>
                    <Chip
                      label={variant.status}
                      color={getStatusColor(variant.status)}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    {variant.parent ? (
                      <Typography variant="body2">
                        {variant.parent.name} ({(variant.parent.performanceScore * 100).toFixed(1)}%)
                      </Typography>
                    ) : (
                      'None'
                    )}
                  </TableCell>
                  <TableCell>
                    {new Date(variant.createdAt).toLocaleString()}
                  </TableCell>
                  <TableCell>
                    <Tooltip title="View Details">
                      <IconButton size="small">
                        <Visibility />
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>

      {/* Start Evolution Dialog */}
      <Dialog open={startDialog} onClose={() => setStartDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Start Evolution Process</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Population Size"
                type="number"
                value={parameters.populationSize}
                onChange={(e) => setParameters(prev => ({ ...prev, populationSize: parseInt(e.target.value) }))}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Generation Limit"
                type="number"
                value={parameters.generationLimit}
                onChange={(e) => setParameters(prev => ({ ...prev, generationLimit: parseInt(e.target.value) }))}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Mutation Rate"
                type="number"
                inputProps={{ step: 0.01, min: 0, max: 1 }}
                value={parameters.mutationRate}
                onChange={(e) => setParameters(prev => ({ ...prev, mutationRate: parseFloat(e.target.value) }))}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Crossover Rate"
                type="number"
                inputProps={{ step: 0.01, min: 0, max: 1 }}
                value={parameters.crossoverRate}
                onChange={(e) => setParameters(prev => ({ ...prev, crossoverRate: parseFloat(e.target.value) }))}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Selection Pressure"
                type="number"
                inputProps={{ step: 0.01, min: 0, max: 1 }}
                value={parameters.selectionPressure}
                onChange={(e) => setParameters(prev => ({ ...prev, selectionPressure: parseFloat(e.target.value) }))}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Fitness Threshold"
                type="number"
                inputProps={{ step: 0.01, min: 0, max: 1 }}
                value={parameters.fitnessThreshold}
                onChange={(e) => setParameters(prev => ({ ...prev, fitnessThreshold: parseFloat(e.target.value) }))}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setStartDialog(false)}>Cancel</Button>
          <Button onClick={handleStartEvolution} variant="contained">Start Evolution</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default EvolutionDashboard;

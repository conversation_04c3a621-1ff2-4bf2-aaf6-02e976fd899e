import { PrismaClient } from '@prisma/client';
import { EventBus, EVENT_TYPES } from './EventBus';
import { logger } from '../utils/logger';
import { Server as SocketIOServer } from 'socket.io';

export interface UserPresence {
  userId: string;
  username: string;
  status: 'online' | 'away' | 'busy' | 'offline';
  currentPage?: string;
  currentResource?: string;
  lastSeen: Date;
  socketId?: string;
}

export interface Comment {
  id: string;
  userId: string;
  username: string;
  resourceType: string;
  resourceId: string;
  content: string;
  parentId?: string; // For threaded comments
  mentions: string[]; // User IDs mentioned in comment
  attachments?: string[];
  createdAt: Date;
  updatedAt: Date;
  isResolved: boolean;
  reactions: CommentReaction[];
}

export interface CommentReaction {
  userId: string;
  username: string;
  emoji: string;
  createdAt: Date;
}

export interface Notification {
  id: string;
  userId: string;
  type: 'mention' | 'comment' | 'assignment' | 'workflow' | 'system';
  title: string;
  message: string;
  resourceType?: string;
  resourceId?: string;
  isRead: boolean;
  createdAt: Date;
  metadata?: Record<string, any>;
}

export interface ActivityFeed {
  id: string;
  userId: string;
  username: string;
  action: string;
  resourceType: string;
  resourceId: string;
  resourceName: string;
  description: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

export class CollaborationService {
  private prisma: PrismaClient;
  private eventBus: EventBus;
  private io: SocketIOServer;
  private userPresence: Map<string, UserPresence> = new Map();
  private socketToUser: Map<string, string> = new Map();

  constructor(io: SocketIOServer) {
    this.prisma = new PrismaClient();
    this.eventBus = new EventBus();
    this.io = io;
    this.setupSocketHandlers();
    this.setupEventListeners();
  }

  /**
   * Setup Socket.IO event handlers
   */
  private setupSocketHandlers(): void {
    this.io.on('connection', (socket) => {
      socket.on('user:join', async (data: { userId: string; username: string }) => {
        await this.handleUserJoin(socket.id, data.userId, data.username);
      });

      socket.on('user:presence', async (data: { status: string; currentPage?: string; currentResource?: string }) => {
        await this.updateUserPresence(socket.id, data);
      });

      socket.on('comment:create', async (data: any) => {
        await this.handleCommentCreate(socket.id, data);
      });

      socket.on('comment:react', async (data: { commentId: string; emoji: string }) => {
        await this.handleCommentReaction(socket.id, data);
      });

      socket.on('disconnect', async () => {
        await this.handleUserDisconnect(socket.id);
      });
    });
  }

  /**
   * Setup event listeners for system events
   */
  private setupEventListeners(): void {
    this.eventBus.on(EVENT_TYPES.WORKFLOW_STARTED, this.handleWorkflowStarted.bind(this));
    this.eventBus.on(EVENT_TYPES.AGENT_CREATED, this.handleAgentCreated.bind(this));
    this.eventBus.on(EVENT_TYPES.ROLE_ASSIGNED, this.handleRoleAssigned.bind(this));
  }

  /**
   * Handle user joining the collaboration session
   */
  private async handleUserJoin(socketId: string, userId: string, username: string): Promise<void> {
    try {
      const presence: UserPresence = {
        userId,
        username,
        status: 'online',
        lastSeen: new Date(),
        socketId,
      };

      this.userPresence.set(userId, presence);
      this.socketToUser.set(socketId, userId);

      // Broadcast user joined to all clients
      this.io.emit('user:joined', {
        userId,
        username,
        status: 'online',
      });

      // Send current online users to the new user
      const onlineUsers = Array.from(this.userPresence.values())
        .filter(user => user.status !== 'offline');

      this.io.to(socketId).emit('users:online', onlineUsers);

      logger.debug('User joined collaboration session', { userId, username, socketId });
    } catch (error) {
      logger.error('Failed to handle user join', { error, socketId, userId });
    }
  }

  /**
   * Update user presence information
   */
  private async updateUserPresence(
    socketId: string,
    data: { status: string; currentPage?: string; currentResource?: string }
  ): Promise<void> {
    try {
      const userId = this.socketToUser.get(socketId);
      if (!userId) return;

      const presence = this.userPresence.get(userId);
      if (!presence) return;

      presence.status = data.status as UserPresence['status'];
      presence.currentPage = data.currentPage;
      presence.currentResource = data.currentResource;
      presence.lastSeen = new Date();

      this.userPresence.set(userId, presence);

      // Broadcast presence update
      this.io.emit('user:presence', {
        userId,
        status: presence.status,
        currentPage: presence.currentPage,
        currentResource: presence.currentResource,
      });

      logger.debug('User presence updated', { userId, status: data.status });
    } catch (error) {
      logger.error('Failed to update user presence', { error, socketId });
    }
  }

  /**
   * Handle user disconnect
   */
  private async handleUserDisconnect(socketId: string): Promise<void> {
    try {
      const userId = this.socketToUser.get(socketId);
      if (!userId) return;

      const presence = this.userPresence.get(userId);
      if (presence) {
        presence.status = 'offline';
        presence.lastSeen = new Date();
        presence.socketId = undefined;
      }

      this.socketToUser.delete(socketId);

      // Broadcast user left
      this.io.emit('user:left', { userId });

      logger.debug('User disconnected from collaboration session', { userId, socketId });
    } catch (error) {
      logger.error('Failed to handle user disconnect', { error, socketId });
    }
  }

  /**
   * Create a new comment
   */
  async createComment(
    userId: string,
    resourceType: string,
    resourceId: string,
    content: string,
    parentId?: string,
    mentions: string[] = []
  ): Promise<Comment> {
    try {
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
        select: { id: true, username: true },
      });

      if (!user) {
        throw new Error('User not found');
      }

      const comment = await this.prisma.comment.create({
        data: {
          userId,
          resourceType,
          resourceId,
          content,
          parentId,
          mentions,
          isResolved: false,
        },
      });

      const commentObj: Comment = {
        id: comment.id,
        userId: comment.userId,
        username: user.username,
        resourceType: comment.resourceType,
        resourceId: comment.resourceId,
        content: comment.content,
        parentId: comment.parentId,
        mentions: comment.mentions,
        createdAt: comment.createdAt,
        updatedAt: comment.updatedAt,
        isResolved: comment.isResolved,
        reactions: [],
      };

      // Create notifications for mentioned users
      for (const mentionedUserId of mentions) {
        await this.createNotification(
          mentionedUserId,
          'mention',
          'You were mentioned in a comment',
          `${user.username} mentioned you in a comment on ${resourceType}`,
          resourceType,
          resourceId
        );
      }

      // Broadcast new comment
      this.io.emit('comment:created', commentObj);

      // Log activity
      await this.logActivity(
        userId,
        user.username,
        'commented',
        resourceType,
        resourceId,
        `Added a comment`,
        { commentId: comment.id }
      );

      logger.debug('Comment created', { commentId: comment.id, userId, resourceType, resourceId });
      return commentObj;
    } catch (error) {
      logger.error('Failed to create comment', { error, userId, resourceType, resourceId });
      throw error;
    }
  }

  /**
   * Handle comment creation from socket
   */
  private async handleCommentCreate(socketId: string, data: any): Promise<void> {
    try {
      const userId = this.socketToUser.get(socketId);
      if (!userId) return;

      await this.createComment(
        userId,
        data.resourceType,
        data.resourceId,
        data.content,
        data.parentId,
        data.mentions
      );
    } catch (error) {
      logger.error('Failed to handle comment creation', { error, socketId, data });
    }
  }

  /**
   * Add reaction to comment
   */
  async addCommentReaction(userId: string, commentId: string, emoji: string): Promise<void> {
    try {
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
        select: { id: true, username: true },
      });

      if (!user) {
        throw new Error('User not found');
      }

      // Check if user already reacted with this emoji
      const existingReaction = await this.prisma.commentReaction.findFirst({
        where: { commentId, userId, emoji },
      });

      if (existingReaction) {
        // Remove existing reaction
        await this.prisma.commentReaction.delete({
          where: { id: existingReaction.id },
        });
      } else {
        // Add new reaction
        await this.prisma.commentReaction.create({
          data: { commentId, userId, emoji },
        });
      }

      // Get updated reactions
      const reactions = await this.prisma.commentReaction.findMany({
        where: { commentId },
        include: { user: { select: { username: true } } },
      });

      const reactionData = reactions.map(r => ({
        userId: r.userId,
        username: r.user.username,
        emoji: r.emoji,
        createdAt: r.createdAt,
      }));

      // Broadcast reaction update
      this.io.emit('comment:reaction', {
        commentId,
        reactions: reactionData,
      });

      logger.debug('Comment reaction updated', { commentId, userId, emoji });
    } catch (error) {
      logger.error('Failed to add comment reaction', { error, userId, commentId, emoji });
      throw error;
    }
  }

  /**
   * Handle comment reaction from socket
   */
  private async handleCommentReaction(socketId: string, data: { commentId: string; emoji: string }): Promise<void> {
    try {
      const userId = this.socketToUser.get(socketId);
      if (!userId) return;

      await this.addCommentReaction(userId, data.commentId, data.emoji);
    } catch (error) {
      logger.error('Failed to handle comment reaction', { error, socketId, data });
    }
  }

  /**
   * Get comments for a resource
   */
  async getComments(resourceType: string, resourceId: string): Promise<Comment[]> {
    try {
      const comments = await this.prisma.comment.findMany({
        where: { resourceType, resourceId },
        include: {
          user: { select: { username: true } },
          reactions: {
            include: { user: { select: { username: true } } },
          },
        },
        orderBy: { createdAt: 'asc' },
      });

      return comments.map(comment => ({
        id: comment.id,
        userId: comment.userId,
        username: comment.user.username,
        resourceType: comment.resourceType,
        resourceId: comment.resourceId,
        content: comment.content,
        parentId: comment.parentId,
        mentions: comment.mentions,
        createdAt: comment.createdAt,
        updatedAt: comment.updatedAt,
        isResolved: comment.isResolved,
        reactions: comment.reactions.map(r => ({
          userId: r.userId,
          username: r.user.username,
          emoji: r.emoji,
          createdAt: r.createdAt,
        })),
      }));
    } catch (error) {
      logger.error('Failed to get comments', { error, resourceType, resourceId });
      throw error;
    }
  }

  /**
   * Create notification
   */
  async createNotification(
    userId: string,
    type: Notification['type'],
    title: string,
    message: string,
    resourceType?: string,
    resourceId?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    try {
      const notification = await this.prisma.notification.create({
        data: {
          userId,
          type,
          title,
          message,
          resourceType,
          resourceId,
          metadata,
          isRead: false,
        },
      });

      // Send real-time notification
      const userSocket = Array.from(this.userPresence.values())
        .find(presence => presence.userId === userId)?.socketId;

      if (userSocket) {
        this.io.to(userSocket).emit('notification:new', {
          id: notification.id,
          type: notification.type,
          title: notification.title,
          message: notification.message,
          resourceType: notification.resourceType,
          resourceId: notification.resourceId,
          createdAt: notification.createdAt,
          isRead: notification.isRead,
        });
      }

      logger.debug('Notification created', { notificationId: notification.id, userId, type });
    } catch (error) {
      logger.error('Failed to create notification', { error, userId, type });
    }
  }

  /**
   * Get user notifications
   */
  async getUserNotifications(userId: string, limit: number = 50): Promise<Notification[]> {
    try {
      const notifications = await this.prisma.notification.findMany({
        where: { userId },
        orderBy: { createdAt: 'desc' },
        take: limit,
      });

      return notifications.map(n => ({
        id: n.id,
        userId: n.userId,
        type: n.type as Notification['type'],
        title: n.title,
        message: n.message,
        resourceType: n.resourceType,
        resourceId: n.resourceId,
        isRead: n.isRead,
        createdAt: n.createdAt,
        metadata: n.metadata as Record<string, any>,
      }));
    } catch (error) {
      logger.error('Failed to get user notifications', { error, userId });
      throw error;
    }
  }

  /**
   * Mark notification as read
   */
  async markNotificationRead(userId: string, notificationId: string): Promise<void> {
    try {
      await this.prisma.notification.updateMany({
        where: { id: notificationId, userId },
        data: { isRead: true },
      });

      logger.debug('Notification marked as read', { notificationId, userId });
    } catch (error) {
      logger.error('Failed to mark notification as read', { error, notificationId, userId });
    }
  }

  /**
   * Log user activity
   */
  async logActivity(
    userId: string,
    username: string,
    action: string,
    resourceType: string,
    resourceId: string,
    description: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    try {
      await this.prisma.activityLog.create({
        data: {
          userId,
          username,
          action,
          resourceType,
          resourceId,
          description,
          metadata,
          timestamp: new Date(),
        },
      });

      // Broadcast activity to interested users
      this.io.emit('activity:new', {
        userId,
        username,
        action,
        resourceType,
        resourceId,
        description,
        timestamp: new Date(),
      });

      logger.debug('Activity logged', { userId, action, resourceType, resourceId });
    } catch (error) {
      logger.error('Failed to log activity', { error, userId, action });
    }
  }

  /**
   * Get activity feed
   */
  async getActivityFeed(limit: number = 100, resourceType?: string): Promise<ActivityFeed[]> {
    try {
      const where: any = {};
      if (resourceType) where.resourceType = resourceType;

      const activities = await this.prisma.activityLog.findMany({
        where,
        orderBy: { timestamp: 'desc' },
        take: limit,
      });

      return activities.map(activity => ({
        id: activity.id,
        userId: activity.userId,
        username: activity.username,
        action: activity.action,
        resourceType: activity.resourceType,
        resourceId: activity.resourceId,
        resourceName: activity.resourceName || '',
        description: activity.description,
        timestamp: activity.timestamp,
        metadata: activity.metadata as Record<string, any>,
      }));
    } catch (error) {
      logger.error('Failed to get activity feed', { error, resourceType });
      throw error;
    }
  }

  /**
   * Get current online users
   */
  getOnlineUsers(): UserPresence[] {
    return Array.from(this.userPresence.values())
      .filter(user => user.status !== 'offline');
  }

  /**
   * Handle workflow started event
   */
  private async handleWorkflowStarted(data: any): Promise<void> {
    // Create notifications for relevant users
    // This is a simplified example - in practice, you'd determine who should be notified
    const message = `Workflow execution started: ${data.workflowId}`;
    
    // Broadcast to all online users for now
    this.io.emit('workflow:started', {
      workflowId: data.workflowId,
      executionId: data.executionId,
      message,
    });
  }

  /**
   * Handle agent created event
   */
  private async handleAgentCreated(data: any): Promise<void> {
    const message = `New agent created: ${data.name}`;
    
    this.io.emit('agent:created', {
      agentId: data.id,
      name: data.name,
      message,
    });
  }

  /**
   * Handle role assigned event
   */
  private async handleRoleAssigned(data: any): Promise<void> {
    await this.createNotification(
      data.userId,
      'assignment',
      'Role Assigned',
      `You have been assigned a new role`,
      'role',
      data.roleId
    );
  }
}

import os

EXCLUDES = {"__pycache__", ".git", "node_modules"}

with open("project_structure.txt", "w", encoding="utf-8") as f:
    for root, dirs, files in os.walk("."):
        # skip excluded dirs
        dirs[:] = [d for d in dirs if d not in EXCLUDES]

        level = root.replace(os.getcwd(), "").count(os.sep)
        indent = " " * 4 * level
        f.write(f"{indent}{os.path.basename(root)}/\n")
        subindent = " " * 4 * (level + 1)
        for file in files:
            f.write(f"{subindent}{file}\n")

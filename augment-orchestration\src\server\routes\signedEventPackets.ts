/**
 * Signed Event Packet API Routes
 * 
 * RESTful API endpoints for managing signed event packets
 * Core Gap 1: Signed Event Packets implementation
 */

import { Router } from 'express';
import { body, query, param, validationResult } from 'express-validator';
import { PrismaClient } from '@prisma/client';
import { SignedEventPacketService } from '../services/SignedEventPacketService';
import { asyncHandler } from '../utils/asyncHandler';
import { authenticate, authorize } from '../middleware/auth';
import { logger } from '../utils/logger';
import {
  CreateEventPacketRequest,
  VerifyEventPacketRequest,
  EventPacketQuery,
  EventType,
  BatchEventPacketRequest
} from '../../shared/types/SignedEventPacket';

const router = Router();
const prisma = new PrismaClient();
const eventPacketService = new SignedEventPacketService(prisma);

// Validation middleware
const validateCreatePacket = [
  body('eventType').isIn(Object.values(EventType)).withMessage('Invalid event type'),
  body('codeContent').notEmpty().withMessage('Code content is required'),
  body('agentMetadata').isObject().withMessage('Agent metadata must be an object'),
  body('agentMetadata.agentId').notEmpty().withMessage('Agent ID is required'),
  body('agentMetadata.agentName').notEmpty().withMessage('Agent name is required'),
  body('agentMetadata.vendor').notEmpty().withMessage('Agent vendor is required'),
  body('workflowId').optional().isString(),
  body('agentId').optional().isString(),
  body('codeDiff').optional().isString(),
  body('testMetrics').optional().isObject(),
  body('codeChanges').optional().isArray(),
  body('environmentInfo').optional().isObject()
];

const validateVerifyPacket = [
  body('packetId').notEmpty().withMessage('Packet ID is required'),
  body('verifierPublicKey').notEmpty().withMessage('Verifier public key is required'),
  body('expectedHash').optional().isString(),
  body('strictMode').optional().isBoolean()
];

const validateQuery = [
  query('eventType').optional().isIn(Object.values(EventType)),
  query('agentId').optional().isString(),
  query('workflowId').optional().isString(),
  query('isVerified').optional().isBoolean(),
  query('dateFrom').optional().isISO8601(),
  query('dateTo').optional().isISO8601(),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('offset').optional().isInt({ min: 0 })
];

/**
 * POST /api/signed-event-packets
 * Create a new signed event packet
 */
router.post('/', 
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR']),
  validateCreatePacket,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const request: CreateEventPacketRequest = {
      eventType: req.body.eventType,
      workflowId: req.body.workflowId,
      agentId: req.body.agentId,
      codeContent: req.body.codeContent,
      codeDiff: req.body.codeDiff,
      testMetrics: req.body.testMetrics,
      agentMetadata: req.body.agentMetadata,
      codeChanges: req.body.codeChanges,
      environmentInfo: req.body.environmentInfo
    };

    const result = await eventPacketService.createEventPacket(request);

    if (result.success) {
      logger.info('Created signed event packet via API', { 
        packetId: result.packet?.id,
        eventType: request.eventType,
        userId: req.user?.id
      });

      res.status(201).json(result);
    } else {
      res.status(400).json(result);
    }
  })
);

/**
 * POST /api/signed-event-packets/verify
 * Verify an existing signed event packet
 */
router.post('/verify',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR', 'VIEWER']),
  validateVerifyPacket,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const request: VerifyEventPacketRequest = {
      packetId: req.body.packetId,
      verifierPublicKey: req.body.verifierPublicKey,
      expectedHash: req.body.expectedHash,
      strictMode: req.body.strictMode
    };

    const result = await eventPacketService.verifyEventPacket(request);

    logger.info('Verified signed event packet via API', {
      packetId: request.packetId,
      success: result.success,
      userId: req.user?.id
    });

    res.json(result);
  })
);

/**
 * GET /api/signed-event-packets
 * Query signed event packets with filters
 */
router.get('/',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR', 'VIEWER']),
  validateQuery,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const query: EventPacketQuery = {
      eventType: req.query.eventType as EventType,
      agentId: req.query.agentId as string,
      workflowId: req.query.workflowId as string,
      isVerified: req.query.isVerified === 'true' ? true : req.query.isVerified === 'false' ? false : undefined,
      dateFrom: req.query.dateFrom ? new Date(req.query.dateFrom as string) : undefined,
      dateTo: req.query.dateTo ? new Date(req.query.dateTo as string) : undefined,
      limit: req.query.limit ? parseInt(req.query.limit as string) : undefined,
      offset: req.query.offset ? parseInt(req.query.offset as string) : undefined
    };

    const packets = await eventPacketService.queryEventPackets(query);

    res.json({
      success: true,
      packets,
      count: packets.length
    });
  })
);

/**
 * GET /api/signed-event-packets/:id
 * Get a specific signed event packet by ID
 */
router.get('/:id',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR', 'VIEWER']),
  param('id').notEmpty().withMessage('Packet ID is required'),
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const packets = await eventPacketService.queryEventPackets({
      limit: 1,
      offset: 0
    });

    const packet = packets.find(p => p.id === req.params.id);

    if (!packet) {
      return res.status(404).json({
        success: false,
        error: 'Event packet not found'
      });
    }

    res.json({
      success: true,
      packet
    });
  })
);

/**
 * GET /api/signed-event-packets/stats
 * Get event packet statistics
 */
router.get('/stats',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR']),
  asyncHandler(async (req, res) => {
    const stats = await eventPacketService.getEventPacketStats();

    res.json({
      success: true,
      stats
    });
  })
);

/**
 * POST /api/signed-event-packets/batch
 * Create multiple signed event packets in batch
 */
router.post('/batch',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR']),
  body('packets').isArray().withMessage('Packets must be an array'),
  body('batchId').notEmpty().withMessage('Batch ID is required'),
  body('priority').optional().isIn(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']),
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const batchRequest: BatchEventPacketRequest = {
      packets: req.body.packets,
      batchId: req.body.batchId,
      priority: req.body.priority || 'MEDIUM'
    };

    const startTime = Date.now();
    const results = [];
    let successCount = 0;
    let failCount = 0;

    // Process each packet in the batch
    for (const packetRequest of batchRequest.packets) {
      try {
        const result = await eventPacketService.createEventPacket(packetRequest);
        results.push(result);
        
        if (result.success) {
          successCount++;
        } else {
          failCount++;
        }
      } catch (error) {
        results.push({
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
        failCount++;
      }
    }

    const processingTime = Date.now() - startTime;

    const batchResponse = {
      batchId: batchRequest.batchId,
      totalPackets: batchRequest.packets.length,
      successfulPackets: successCount,
      failedPackets: failCount,
      packets: results,
      processingTime
    };

    logger.info('Processed batch event packets', {
      batchId: batchRequest.batchId,
      totalPackets: batchResponse.totalPackets,
      successfulPackets: successCount,
      failedPackets: failCount,
      processingTime,
      userId: req.user?.id
    });

    res.status(201).json({
      success: true,
      batch: batchResponse
    });
  })
);

/**
 * DELETE /api/signed-event-packets/:id
 * Delete a signed event packet (admin only)
 */
router.delete('/:id',
  authenticate,
  authorize(['ADMIN']),
  param('id').notEmpty().withMessage('Packet ID is required'),
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    try {
      await prisma.signedEventPacket.delete({
        where: { id: req.params.id }
      });

      logger.info('Deleted signed event packet', {
        packetId: req.params.id,
        userId: req.user?.id
      });

      res.json({
        success: true,
        message: 'Event packet deleted successfully'
      });
    } catch (error) {
      logger.error('Failed to delete event packet:', error);
      res.status(404).json({
        success: false,
        error: 'Event packet not found or could not be deleted'
      });
    }
  })
);

export default router;

/**
 * Provenance Ledger API Routes
 * 
 * RESTful API endpoints for managing the immutable provenance ledger
 * Core Gap 2: Immutable Provenance Ledger implementation
 */

import { Router } from 'express';
import { body, query, param, validationResult } from 'express-validator';
import { PrismaClient } from '@prisma/client';
import { ProvenanceLedgerService } from '../services/ProvenanceLedgerService';
import { asyncHandler } from '../utils/asyncHandler';
import { authenticate, authorize } from '../middleware/auth';
import { logger } from '../utils/logger';
import {
  CreateProvenanceEntryRequest,
  ProvenanceQuery,
  VerifyProvenanceChainRequest,
  ProvenanceAction,
  EntityType
} from '../../shared/types/ProvenanceLedger';

const router = Router();
const prisma = new PrismaClient();
const provenanceService = new ProvenanceLedgerService(prisma);

// Validation middleware
const validateCreateEntry = [
  body('action').isIn(Object.values(ProvenanceAction)).withMessage('Invalid provenance action'),
  body('entityType').isIn(Object.values(EntityType)).withMessage('Invalid entity type'),
  body('entityId').notEmpty().withMessage('Entity ID is required'),
  body('performedBy').notEmpty().withMessage('Performer ID is required'),
  body('changeMetadata').isObject().withMessage('Change metadata must be an object'),
  body('changeMetadata.reason').notEmpty().withMessage('Change reason is required'),
  body('changeMetadata.description').notEmpty().withMessage('Change description is required'),
  body('beforeState').optional().isObject(),
  body('afterState').optional().isObject(),
  body('performedByType').optional().isIn(['USER', 'AGENT', 'SYSTEM']),
  body('linkedEventPacketId').optional().isString()
];

const validateQuery = [
  query('entityId').optional().isString(),
  query('entityType').optional().isIn(Object.values(EntityType)),
  query('action').optional().isIn(Object.values(ProvenanceAction)),
  query('performedBy').optional().isString(),
  query('dateFrom').optional().isISO8601(),
  query('dateTo').optional().isISO8601(),
  query('includeSnapshots').optional().isBoolean(),
  query('verifyIntegrity').optional().isBoolean(),
  query('limit').optional().isInt({ min: 1, max: 1000 }),
  query('offset').optional().isInt({ min: 0 })
];

const validateChainVerification = [
  body('entityId').optional().isString(),
  body('startDate').optional().isISO8601(),
  body('endDate').optional().isISO8601(),
  body('deepVerification').optional().isBoolean(),
  body('repairCorruption').optional().isBoolean()
];

/**
 * POST /api/provenance-ledger/entries
 * Create a new provenance entry
 */
router.post('/entries',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR']),
  validateCreateEntry,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const request: CreateProvenanceEntryRequest = {
      action: req.body.action,
      entityType: req.body.entityType,
      entityId: req.body.entityId,
      beforeState: req.body.beforeState,
      afterState: req.body.afterState,
      changeMetadata: req.body.changeMetadata,
      performedBy: req.body.performedBy,
      performedByType: req.body.performedByType,
      linkedEventPacketId: req.body.linkedEventPacketId
    };

    const entry = await provenanceService.createProvenanceEntry(request);

    logger.info('Created provenance entry via API', {
      entryId: entry.id,
      action: request.action,
      entityType: request.entityType,
      entityId: request.entityId,
      userId: req.user?.id
    });

    res.status(201).json({
      success: true,
      entry
    });
  })
);

/**
 * GET /api/provenance-ledger/entries
 * Query provenance entries with filters
 */
router.get('/entries',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR', 'VIEWER']),
  validateQuery,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const query: ProvenanceQuery = {
      entityId: req.query.entityId as string,
      entityType: req.query.entityType as EntityType,
      action: req.query.action as ProvenanceAction,
      performedBy: req.query.performedBy as string,
      dateFrom: req.query.dateFrom ? new Date(req.query.dateFrom as string) : undefined,
      dateTo: req.query.dateTo ? new Date(req.query.dateTo as string) : undefined,
      includeSnapshots: req.query.includeSnapshots === 'true',
      verifyIntegrity: req.query.verifyIntegrity === 'true',
      limit: req.query.limit ? parseInt(req.query.limit as string) : undefined,
      offset: req.query.offset ? parseInt(req.query.offset as string) : undefined
    };

    const entries = await provenanceService.queryProvenanceEntries(query);

    res.json({
      success: true,
      entries,
      count: entries.length
    });
  })
);

/**
 * GET /api/provenance-ledger/chains/:entityId
 * Get complete provenance chain for an entity
 */
router.get('/chains/:entityId',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR', 'VIEWER']),
  param('entityId').notEmpty().withMessage('Entity ID is required'),
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const chain = await provenanceService.getProvenanceChain(req.params.entityId);

    res.json({
      success: true,
      chain
    });
  })
);

/**
 * POST /api/provenance-ledger/verify-chain
 * Verify provenance chain integrity
 */
router.post('/verify-chain',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR']),
  validateChainVerification,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const request: VerifyProvenanceChainRequest = {
      entityId: req.body.entityId,
      startDate: req.body.startDate ? new Date(req.body.startDate) : undefined,
      endDate: req.body.endDate ? new Date(req.body.endDate) : undefined,
      deepVerification: req.body.deepVerification || false,
      repairCorruption: req.body.repairCorruption || false
    };

    const result = await provenanceService.verifyProvenanceChain(request);

    logger.info('Verified provenance chain via API', {
      entityId: request.entityId,
      isValid: result.isValid,
      totalEntries: result.totalEntries,
      validEntries: result.validEntries,
      invalidEntries: result.invalidEntries,
      userId: req.user?.id
    });

    res.json({
      success: true,
      verification: result
    });
  })
);

/**
 * GET /api/provenance-ledger/stats
 * Get provenance ledger statistics
 */
router.get('/stats',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR']),
  asyncHandler(async (req, res) => {
    const stats = await provenanceService.getProvenanceStats();

    res.json({
      success: true,
      stats
    });
  })
);

/**
 * GET /api/provenance-ledger/integrity-check
 * Perform system-wide integrity check
 */
router.get('/integrity-check',
  authenticate,
  authorize(['ADMIN']),
  asyncHandler(async (req, res) => {
    const result = await provenanceService.verifyProvenanceChain({
      deepVerification: true,
      repairCorruption: false
    });

    logger.info('Performed system-wide integrity check', {
      isValid: result.isValid,
      totalEntries: result.totalEntries,
      integrityScore: result.integrityScore,
      userId: req.user?.id
    });

    res.json({
      success: true,
      integrityCheck: result
    });
  })
);

/**
 * POST /api/provenance-ledger/repair-chain
 * Repair chain integrity issues (admin only)
 */
router.post('/repair-chain',
  authenticate,
  authorize(['ADMIN']),
  body('entityId').optional().isString(),
  body('forceRepair').optional().isBoolean(),
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const request: VerifyProvenanceChainRequest = {
      entityId: req.body.entityId,
      deepVerification: true,
      repairCorruption: true
    };

    const result = await provenanceService.verifyProvenanceChain(request);

    logger.warn('Performed chain repair operation', {
      entityId: request.entityId,
      wasValid: result.isValid,
      totalEntries: result.totalEntries,
      repairedEntries: result.invalidEntries,
      userId: req.user?.id
    });

    res.json({
      success: true,
      repair: result,
      message: 'Chain repair completed'
    });
  })
);

/**
 * GET /api/provenance-ledger/audit-report
 * Generate comprehensive audit report
 */
router.get('/audit-report',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR']),
  query('dateFrom').optional().isISO8601(),
  query('dateTo').optional().isISO8601(),
  query('entityType').optional().isIn(Object.values(EntityType)),
  query('includeSnapshots').optional().isBoolean(),
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const dateFrom = req.query.dateFrom ? new Date(req.query.dateFrom as string) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const dateTo = req.query.dateTo ? new Date(req.query.dateTo as string) : new Date();

    // Get entries for the report
    const entries = await provenanceService.queryProvenanceEntries({
      entityType: req.query.entityType as EntityType,
      dateFrom,
      dateTo,
      includeSnapshots: req.query.includeSnapshots === 'true',
      verifyIntegrity: true
    });

    // Get integrity report
    const integrityReport = await provenanceService.verifyProvenanceChain({
      startDate: dateFrom,
      endDate: dateTo,
      deepVerification: true
    });

    // Generate audit report
    const auditReport = {
      reportId: `audit_${Date.now()}`,
      generatedAt: new Date(),
      timeRange: { from: dateFrom, to: dateTo },
      summary: {
        totalChanges: entries.length,
        criticalChanges: entries.filter(e => e.changeMetadata.impact === 'CRITICAL').length,
        unauthorizedChanges: 0, // TODO: Implement unauthorized change detection
        failedVerifications: integrityReport.invalidEntries
      },
      entries: entries.slice(0, 1000), // Limit for performance
      integrityReport,
      recommendations: integrityReport.recommendations,
      complianceStatus: integrityReport.isValid ? 'COMPLIANT' : 'NEEDS_REVIEW'
    };

    logger.info('Generated audit report', {
      reportId: auditReport.reportId,
      timeRange: auditReport.timeRange,
      totalChanges: auditReport.summary.totalChanges,
      complianceStatus: auditReport.complianceStatus,
      userId: req.user?.id
    });

    res.json({
      success: true,
      auditReport
    });
  })
);

export default router;

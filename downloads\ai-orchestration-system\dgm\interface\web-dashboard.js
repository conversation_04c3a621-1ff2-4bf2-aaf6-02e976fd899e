/**
 * Web Dashboard for DGM Monitoring
 * 
 * Provides a web-based interface for monitoring and controlling the DGM system:
 * - Real-time evolution monitoring
 * - Agent genealogy visualization
 * - Performance metrics dashboard
 * - Human oversight controls
 */

const express = require('express');
const http = require('http');
const path = require('path');
const chalk = require('chalk');
// Note: socket.io would be added as dependency in package.json

class WebDashboard {
  constructor(dgmEngine, config) {
    this.dgmEngine = dgmEngine;
    this.config = config;
    this.app = express();
    this.server = http.createServer(this.app);
    // this.io = socketIo(this.server); // Would be initialized with socket.io
    this.port = config.get('ui.webDashboard.port', 3002);
    this.autoRefresh = config.get('ui.webDashboard.autoRefresh', 5000);
    this.isRunning = false;
    this.connectedClients = new Set();
    
    this.setupRoutes();
    this.setupSocketHandlers();
    this.setupDGMEventHandlers();
  }

  /**
   * Start the web dashboard server
   */
  async start() {
    return new Promise((resolve, reject) => {
      this.server.listen(this.port, (error) => {
        if (error) {
          reject(error);
        } else {
          this.isRunning = true;
          console.log(chalk.green(`🌐 DGM Web Dashboard running on http://localhost:${this.port}`));
          resolve();
        }
      });
    });
  }

  /**
   * Stop the web dashboard server
   */
  async stop() {
    return new Promise((resolve) => {
      this.server.close(() => {
        this.isRunning = false;
        console.log(chalk.yellow('🌐 DGM Web Dashboard stopped'));
        resolve();
      });
    });
  }

  /**
   * Setup Express routes
   */
  setupRoutes() {
    // Serve static files
    this.app.use(express.static(path.join(__dirname, 'public')));
    this.app.use(express.json());

    // Main dashboard page
    this.app.get('/', (req, res) => {
      res.send(this.generateDashboardHTML());
    });

    // API endpoints
    this.app.get('/api/status', async (req, res) => {
      try {
        const status = await this.getDGMStatus();
        res.json(status);
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    this.app.get('/api/agents', async (req, res) => {
      try {
        const agents = await this.getAgentsList();
        res.json(agents);
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    this.app.get('/api/agents/:id', async (req, res) => {
      try {
        const agent = await this.getAgentDetails(req.params.id);
        res.json(agent);
      } catch (error) {
        res.status(404).json({ error: 'Agent not found' });
      }
    });

    this.app.get('/api/genealogy', async (req, res) => {
      try {
        const genealogy = await this.getGenealogyData();
        res.json(genealogy);
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    this.app.get('/api/metrics', async (req, res) => {
      try {
        const metrics = await this.getMetricsData();
        res.json(metrics);
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    this.app.get('/api/evolution-history', async (req, res) => {
      try {
        const history = await this.getEvolutionHistory();
        res.json(history);
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    // Control endpoints
    this.app.post('/api/control/start', async (req, res) => {
      try {
        await this.dgmEngine.startEvolution(req.body);
        res.json({ success: true, message: 'Evolution started' });
      } catch (error) {
        res.status(400).json({ error: error.message });
      }
    });

    this.app.post('/api/control/stop', async (req, res) => {
      try {
        this.dgmEngine.isRunning = false;
        res.json({ success: true, message: 'Evolution stopped' });
      } catch (error) {
        res.status(400).json({ error: error.message });
      }
    });

    this.app.post('/api/control/pause', async (req, res) => {
      try {
        // Implement pause functionality
        res.json({ success: true, message: 'Evolution paused' });
      } catch (error) {
        res.status(400).json({ error: error.message });
      }
    });

    this.app.post('/api/agents/:id/approve', async (req, res) => {
      try {
        await this.approveAgent(req.params.id);
        res.json({ success: true, message: 'Agent approved' });
      } catch (error) {
        res.status(400).json({ error: error.message });
      }
    });

    this.app.post('/api/agents/:id/reject', async (req, res) => {
      try {
        await this.rejectAgent(req.params.id);
        res.json({ success: true, message: 'Agent rejected' });
      } catch (error) {
        res.status(400).json({ error: error.message });
      }
    });
  }

  /**
   * Setup Socket.IO handlers for real-time updates
   */
  setupSocketHandlers() {
    this.io.on('connection', (socket) => {
      console.log(chalk.blue(`📱 Client connected to dashboard: ${socket.id}`));
      this.connectedClients.add(socket.id);

      // Send initial data
      this.sendInitialData(socket);

      // Handle client requests
      socket.on('request-update', async (type) => {
        await this.sendUpdate(socket, type);
      });

      socket.on('disconnect', () => {
        console.log(chalk.gray(`📱 Client disconnected: ${socket.id}`));
        this.connectedClients.delete(socket.id);
      });
    });

    // Auto-refresh timer
    if (this.autoRefresh > 0) {
      setInterval(() => {
        this.broadcastUpdates();
      }, this.autoRefresh);
    }
  }

  /**
   * Setup DGM event handlers for real-time updates
   */
  setupDGMEventHandlers() {
    this.dgmEngine.on('generationCompleted', (generation) => {
      this.broadcast('generation-completed', generation);
    });

    this.dgmEngine.on('agentCreated', (agent) => {
      this.broadcast('agent-created', agent);
    });

    this.dgmEngine.on('agentEvaluated', (agent) => {
      this.broadcast('agent-evaluated', agent);
    });

    this.dgmEngine.on('evolutionStarted', (data) => {
      this.broadcast('evolution-started', data);
    });

    this.dgmEngine.on('evolutionStopped', (data) => {
      this.broadcast('evolution-stopped', data);
    });
  }

  /**
   * Send initial data to connected client
   */
  async sendInitialData(socket) {
    try {
      const data = {
        status: await this.getDGMStatus(),
        agents: await this.getAgentsList(),
        metrics: await this.getMetricsData(),
        genealogy: await this.getGenealogyData()
      };
      
      socket.emit('initial-data', data);
    } catch (error) {
      console.error(chalk.red(`Error sending initial data: ${error.message}`));
    }
  }

  /**
   * Send specific update to client
   */
  async sendUpdate(socket, type) {
    try {
      let data;
      
      switch (type) {
        case 'status':
          data = await this.getDGMStatus();
          break;
        case 'agents':
          data = await this.getAgentsList();
          break;
        case 'metrics':
          data = await this.getMetricsData();
          break;
        case 'genealogy':
          data = await this.getGenealogyData();
          break;
        case 'evolution-history':
          data = await this.getEvolutionHistory();
          break;
        default:
          return;
      }
      
      socket.emit('update', { type, data });
    } catch (error) {
      socket.emit('error', { message: error.message });
    }
  }

  /**
   * Broadcast updates to all connected clients
   */
  async broadcastUpdates() {
    if (this.connectedClients.size === 0) return;

    try {
      const updates = {
        status: await this.getDGMStatus(),
        metrics: await this.getMetricsData()
      };
      
      this.broadcast('auto-update', updates);
    } catch (error) {
      console.error(chalk.red(`Error broadcasting updates: ${error.message}`));
    }
  }

  /**
   * Broadcast message to all connected clients
   */
  broadcast(event, data) {
    this.io.emit(event, data);
  }

  /**
   * Get DGM system status
   */
  async getDGMStatus() {
    return {
      isRunning: this.dgmEngine.isRunning,
      currentGeneration: this.dgmEngine.currentGeneration,
      populationSize: this.dgmEngine.agentManager.currentPopulation.length,
      evolutionHistory: this.dgmEngine.evolutionHistory.length,
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
      timestamp: new Date()
    };
  }

  /**
   * Get agents list
   */
  async getAgentsList() {
    const population = await this.dgmEngine.agentManager.getCurrentPopulation();
    
    return population.map(agent => ({
      id: agent.id,
      generation: agent.generation,
      type: agent.type,
      fitness: agent.fitness,
      created: agent.created,
      parentIds: agent.parentIds,
      metrics: agent.metrics
    }));
  }

  /**
   * Get agent details
   */
  async getAgentDetails(agentId) {
    const agent = await this.dgmEngine.archive.retrieveAgent(agentId);
    const lineage = await this.dgmEngine.archive.getAgentLineage(agentId);
    
    return {
      ...agent,
      lineage
    };
  }

  /**
   * Get genealogy data for visualization
   */
  async getGenealogyData() {
    const genealogyStats = this.dgmEngine.archive.genealogy.getGenealogyStats();
    const familyTree = this.dgmEngine.archive.genealogy.generateFamilyTreeData();
    
    return {
      stats: genealogyStats,
      familyTree
    };
  }

  /**
   * Get metrics data
   */
  async getMetricsData() {
    const metricsStats = this.dgmEngine.metricsCollector.getMetricsStats();
    const archiveStats = await this.dgmEngine.archive.getArchiveStats();
    
    return {
      metrics: metricsStats,
      archive: archiveStats
    };
  }

  /**
   * Get evolution history
   */
  async getEvolutionHistory() {
    return this.dgmEngine.evolutionHistory.map(generation => ({
      number: generation.number,
      timestamp: generation.timestamp,
      bestFitness: generation.bestFitness,
      averageFitness: generation.averageFitness,
      survivors: generation.survivors,
      duration: generation.duration
    }));
  }

  /**
   * Approve an agent for deployment
   */
  async approveAgent(agentId) {
    // Implementation for agent approval
    console.log(chalk.green(`✅ Agent ${agentId} approved for deployment`));
    this.broadcast('agent-approved', { agentId, timestamp: new Date() });
  }

  /**
   * Reject an agent
   */
  async rejectAgent(agentId) {
    // Implementation for agent rejection
    console.log(chalk.red(`❌ Agent ${agentId} rejected`));
    this.broadcast('agent-rejected', { agentId, timestamp: new Date() });
  }

  /**
   * Generate dashboard HTML
   */
  generateDashboardHTML() {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DGM Dashboard</title>
    <script src="/socket.io/socket.io.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .header { background: #2c3e50; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .metric { display: flex; justify-content: space-between; margin: 10px 0; }
        .status-running { color: #27ae60; }
        .status-stopped { color: #e74c3c; }
        .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background: #3498db; color: white; }
        .btn-success { background: #27ae60; color: white; }
        .btn-danger { background: #e74c3c; color: white; }
        .btn-warning { background: #f39c12; color: white; }
        .agent-list { max-height: 400px; overflow-y: auto; }
        .agent-item { padding: 10px; border-bottom: 1px solid #eee; }
        .fitness-bar { background: #ecf0f1; height: 20px; border-radius: 10px; overflow: hidden; }
        .fitness-fill { background: #3498db; height: 100%; transition: width 0.3s; }
        #genealogy-chart { width: 100%; height: 400px; border: 1px solid #ddd; }
        .log { background: #2c3e50; color: #ecf0f1; padding: 15px; border-radius: 4px; font-family: monospace; max-height: 300px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧬 Darwin Gödel Machine Dashboard</h1>
        <p>Real-time monitoring and control of the self-improving AI orchestration system</p>
    </div>

    <div class="grid">
        <div class="card">
            <h3>System Status</h3>
            <div class="metric">
                <span>Status:</span>
                <span id="system-status" class="status-stopped">Stopped</span>
            </div>
            <div class="metric">
                <span>Generation:</span>
                <span id="current-generation">0</span>
            </div>
            <div class="metric">
                <span>Population:</span>
                <span id="population-size">0</span>
            </div>
            <div class="metric">
                <span>Uptime:</span>
                <span id="uptime">0s</span>
            </div>
            <div style="margin-top: 20px;">
                <button class="btn btn-success" onclick="startEvolution()">Start Evolution</button>
                <button class="btn btn-warning" onclick="pauseEvolution()">Pause</button>
                <button class="btn btn-danger" onclick="stopEvolution()">Stop</button>
            </div>
        </div>

        <div class="card">
            <h3>Performance Metrics</h3>
            <div class="metric">
                <span>Best Fitness:</span>
                <span id="best-fitness">0.000</span>
            </div>
            <div class="metric">
                <span>Average Fitness:</span>
                <span id="avg-fitness">0.000</span>
            </div>
            <div class="metric">
                <span>Total Agents:</span>
                <span id="total-agents">0</span>
            </div>
            <div class="metric">
                <span>Generations:</span>
                <span id="total-generations">0</span>
            </div>
        </div>

        <div class="card">
            <h3>Current Population</h3>
            <div id="agent-list" class="agent-list">
                <p>No agents in population</p>
            </div>
        </div>

        <div class="card">
            <h3>Evolution History</h3>
            <canvas id="evolution-chart" width="400" height="200"></canvas>
        </div>

        <div class="card">
            <h3>Agent Genealogy</h3>
            <div id="genealogy-chart">
                <p>Genealogy visualization will appear here</p>
            </div>
        </div>

        <div class="card">
            <h3>System Log</h3>
            <div id="system-log" class="log">
                <div>DGM Dashboard initialized</div>
            </div>
        </div>
    </div>

    <script>
        const socket = io();
        
        // Socket event handlers
        socket.on('initial-data', (data) => {
            updateDashboard(data);
        });
        
        socket.on('auto-update', (data) => {
            updateStatus(data.status);
            updateMetrics(data.metrics);
        });
        
        socket.on('generation-completed', (generation) => {
            addLogEntry(\`Generation \${generation.number} completed - Best fitness: \${generation.bestFitness.toFixed(3)}\`);
            updateEvolutionChart();
        });
        
        socket.on('agent-created', (agent) => {
            addLogEntry(\`New agent created: \${agent.id} (fitness: \${agent.fitness.toFixed(3)})\`);
        });
        
        // Control functions
        function startEvolution() {
            fetch('/api/control/start', { method: 'POST' })
                .then(response => response.json())
                .then(data => addLogEntry(data.message));
        }
        
        function pauseEvolution() {
            fetch('/api/control/pause', { method: 'POST' })
                .then(response => response.json())
                .then(data => addLogEntry(data.message));
        }
        
        function stopEvolution() {
            fetch('/api/control/stop', { method: 'POST' })
                .then(response => response.json())
                .then(data => addLogEntry(data.message));
        }
        
        // Update functions
        function updateDashboard(data) {
            updateStatus(data.status);
            updateAgentList(data.agents);
            updateMetrics(data.metrics);
        }
        
        function updateStatus(status) {
            document.getElementById('system-status').textContent = status.isRunning ? 'Running' : 'Stopped';
            document.getElementById('system-status').className = status.isRunning ? 'status-running' : 'status-stopped';
            document.getElementById('current-generation').textContent = status.currentGeneration;
            document.getElementById('population-size').textContent = status.populationSize;
            document.getElementById('uptime').textContent = Math.floor(status.uptime) + 's';
        }
        
        function updateAgentList(agents) {
            const listElement = document.getElementById('agent-list');
            if (agents.length === 0) {
                listElement.innerHTML = '<p>No agents in population</p>';
                return;
            }
            
            listElement.innerHTML = agents.map(agent => \`
                <div class="agent-item">
                    <div><strong>\${agent.id.substring(0, 8)}...</strong> (Gen \${agent.generation})</div>
                    <div>Type: \${agent.type} | Fitness: \${agent.fitness.toFixed(3)}</div>
                    <div class="fitness-bar">
                        <div class="fitness-fill" style="width: \${agent.fitness * 100}%"></div>
                    </div>
                </div>
            \`).join('');
        }
        
        function updateMetrics(metrics) {
            if (metrics.archive) {
                document.getElementById('total-agents').textContent = metrics.archive.totalAgents;
                document.getElementById('total-generations').textContent = metrics.archive.generations;
                document.getElementById('best-fitness').textContent = metrics.archive.fitnessDistribution.max.toFixed(3);
                document.getElementById('avg-fitness').textContent = metrics.archive.fitnessDistribution.average.toFixed(3);
            }
        }
        
        function updateEvolutionChart() {
            // Placeholder for evolution chart update
            // In a real implementation, this would use Chart.js or similar
        }
        
        function addLogEntry(message) {
            const logElement = document.getElementById('system-log');
            const timestamp = new Date().toLocaleTimeString();
            const entry = document.createElement('div');
            entry.textContent = \`[\${timestamp}] \${message}\`;
            logElement.appendChild(entry);
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        // Request initial data
        socket.emit('request-update', 'all');
    </script>
</body>
</html>
    `;
  }

  /**
   * Get dashboard statistics
   */
  getDashboardStats() {
    return {
      isRunning: this.isRunning,
      connectedClients: this.connectedClients.size,
      port: this.port,
      autoRefresh: this.autoRefresh
    };
  }
}

module.exports = WebDashboard;

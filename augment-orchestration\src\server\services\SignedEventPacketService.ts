/**
 * Signed Event Packet Service
 * 
 * Core Gap 1: Implementation of signed event packets for code provenance
 * and verification across all AI orchestration operations.
 */

import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger';
import { cryptographicService } from './CryptographicService';
import {
  SignedEventPacketData,
  CreateEventPacketRequest,
  VerifyEventPacketRequest,
  EventPacketResponse,
  EventPacketQuery,
  EventPacketStats,
  EventType,
  VerificationEntry,
  BatchEventPacketRequest,
  BatchEventPacketResponse,
  EventPacketError,
  SignatureVerificationError,
  HashMismatchError,
  EVENT_PACKET_CONSTANTS
} from '../../shared/types/SignedEventPacket';

export class SignedEventPacketService {
  private prisma: PrismaClient;
  private systemPrivateKey: string | null = null;
  private systemPublicKey: string | null = null;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
    this.initializeSystemKeys();
  }

  /**
   * Initialize system cryptographic keys
   */
  private async initializeSystemKeys(): Promise<void> {
    try {
      // In production, these should be loaded from secure storage
      const keyPair = await cryptographicService.generateKeyPair();
      this.systemPrivateKey = keyPair.privateKey;
      this.systemPublicKey = keyPair.publicKey;
      
      logger.info('System cryptographic keys initialized');
    } catch (error) {
      logger.error('Failed to initialize system keys:', error);
      throw new Error('System key initialization failed');
    }
  }

  /**
   * Create a new signed event packet
   */
  async createEventPacket(request: CreateEventPacketRequest): Promise<EventPacketResponse> {
    try {
      // Validate request
      this.validateCreateRequest(request);

      // Generate code hash
      const codeHash = cryptographicService.generateCodeHash(
        request.codeContent,
        { eventType: request.eventType, timestamp: new Date() }
      );

      // Create payload for signing
      const payload = this.createSigningPayload(request, codeHash);

      // Sign the payload
      if (!this.systemPrivateKey) {
        throw new EventPacketError('System private key not available', 'MISSING_PRIVATE_KEY');
      }

      const trustSignature = await cryptographicService.signData(payload, this.systemPrivateKey);

      // Create verification entry
      const verificationEntry: VerificationEntry = {
        timestamp: new Date(),
        verifier: 'SYSTEM',
        status: 'VERIFIED',
        signature: trustSignature,
        publicKey: this.systemPublicKey!,
        algorithm: 'RSA-SHA256'
      };

      // Store in database
      const packet = await this.prisma.signedEventPacket.create({
        data: {
          eventType: request.eventType,
          workflowId: request.workflowId,
          agentId: request.agentId,
          codeHash,
          codeDiff: request.codeDiff,
          testMetrics: request.testMetrics ? JSON.stringify(request.testMetrics) : null,
          agentMetadata: JSON.stringify(request.agentMetadata),
          trustSignature,
          isVerified: true,
          verificationLog: JSON.stringify([verificationEntry])
        },
        include: {
          agent: true,
          workflowExecution: true
        }
      });

      // Convert to response format
      const packetData = this.convertToPacketData(packet);

      logger.info('Created signed event packet', { 
        id: packet.id, 
        eventType: request.eventType,
        codeHash 
      });

      return {
        success: true,
        packet: packetData,
        verificationStatus: 'VERIFIED'
      };

    } catch (error) {
      logger.error('Failed to create event packet:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Verify an existing event packet
   */
  async verifyEventPacket(request: VerifyEventPacketRequest): Promise<EventPacketResponse> {
    try {
      // Retrieve packet from database
      const packet = await this.prisma.signedEventPacket.findUnique({
        where: { id: request.packetId },
        include: {
          agent: true,
          workflowExecution: true
        }
      });

      if (!packet) {
        throw new EventPacketError('Event packet not found', 'PACKET_NOT_FOUND');
      }

      // Reconstruct payload for verification
      const agentMetadata = JSON.parse(packet.agentMetadata);
      const testMetrics = packet.testMetrics ? JSON.parse(packet.testMetrics) : undefined;
      
      const payload = this.createSigningPayload({
        eventType: packet.eventType as EventType,
        workflowId: packet.workflowId || undefined,
        agentId: packet.agentId || undefined,
        codeContent: '', // We only have the hash, not the original content
        codeDiff: packet.codeDiff || undefined,
        testMetrics,
        agentMetadata
      }, packet.codeHash);

      // Verify signature
      const isSignatureValid = await cryptographicService.verifySignature(
        payload,
        packet.trustSignature,
        this.systemPublicKey!
      );

      if (!isSignatureValid) {
        throw new SignatureVerificationError('Signature verification failed');
      }

      // Verify hash if provided
      if (request.expectedHash && request.expectedHash !== packet.codeHash) {
        throw new HashMismatchError('Code hash mismatch');
      }

      // Update verification log
      const verificationLog = packet.verificationLog 
        ? JSON.parse(packet.verificationLog) 
        : [];

      const newVerificationEntry: VerificationEntry = {
        timestamp: new Date(),
        verifier: 'EXTERNAL',
        status: 'VERIFIED',
        signature: packet.trustSignature,
        publicKey: request.verifierPublicKey,
        algorithm: 'RSA-SHA256'
      };

      verificationLog.push(newVerificationEntry);

      // Update packet
      await this.prisma.signedEventPacket.update({
        where: { id: request.packetId },
        data: {
          isVerified: true,
          verificationLog: JSON.stringify(verificationLog)
        }
      });

      const packetData = this.convertToPacketData(packet);

      logger.info('Verified event packet', { id: request.packetId });

      return {
        success: true,
        packet: packetData,
        verificationStatus: 'VERIFIED'
      };

    } catch (error) {
      logger.error('Failed to verify event packet:', error);
      
      if (error instanceof EventPacketError) {
        return {
          success: false,
          error: error.message,
          verificationStatus: 'FAILED'
        };
      }

      return {
        success: false,
        error: 'Verification failed',
        verificationStatus: 'FAILED'
      };
    }
  }

  /**
   * Query event packets with filters
   */
  async queryEventPackets(query: EventPacketQuery): Promise<SignedEventPacketData[]> {
    try {
      const where: any = {};

      if (query.eventType) where.eventType = query.eventType;
      if (query.agentId) where.agentId = query.agentId;
      if (query.workflowId) where.workflowId = query.workflowId;
      if (query.isVerified !== undefined) where.isVerified = query.isVerified;
      
      if (query.dateFrom || query.dateTo) {
        where.timestamp = {};
        if (query.dateFrom) where.timestamp.gte = query.dateFrom;
        if (query.dateTo) where.timestamp.lte = query.dateTo;
      }

      const packets = await this.prisma.signedEventPacket.findMany({
        where,
        include: {
          agent: true,
          workflowExecution: true
        },
        orderBy: { timestamp: 'desc' },
        take: query.limit || 50,
        skip: query.offset || 0
      });

      return packets.map(packet => this.convertToPacketData(packet));

    } catch (error) {
      logger.error('Failed to query event packets:', error);
      throw new EventPacketError('Query failed', 'QUERY_ERROR');
    }
  }

  /**
   * Get event packet statistics
   */
  async getEventPacketStats(): Promise<EventPacketStats> {
    try {
      const [
        totalPackets,
        verifiedPackets,
        failedVerifications,
        recentActivity
      ] = await Promise.all([
        this.prisma.signedEventPacket.count(),
        this.prisma.signedEventPacket.count({ where: { isVerified: true } }),
        this.prisma.signedEventPacket.count({ where: { isVerified: false } }),
        this.getRecentActivityStats()
      ]);

      const byEventType = await this.getEventTypeStats();
      const byAgent = await this.getAgentStats();

      return {
        totalPackets,
        verifiedPackets,
        failedVerifications,
        byEventType,
        byAgent,
        averageVerificationTime: 0, // TODO: Calculate from verification logs
        recentActivity
      };

    } catch (error) {
      logger.error('Failed to get event packet stats:', error);
      throw new EventPacketError('Stats retrieval failed', 'STATS_ERROR');
    }
  }

  /**
   * Validate create request
   */
  private validateCreateRequest(request: CreateEventPacketRequest): void {
    if (!request.codeContent) {
      throw new EventPacketError('Code content is required', 'MISSING_CODE_CONTENT');
    }

    if (request.codeContent.length > EVENT_PACKET_CONSTANTS.MAX_CODE_SIZE) {
      throw new EventPacketError('Code content exceeds maximum size', 'CODE_TOO_LARGE');
    }

    if (request.codeDiff && request.codeDiff.length > EVENT_PACKET_CONSTANTS.MAX_DIFF_SIZE) {
      throw new EventPacketError('Code diff exceeds maximum size', 'DIFF_TOO_LARGE');
    }

    if (!request.agentMetadata) {
      throw new EventPacketError('Agent metadata is required', 'MISSING_AGENT_METADATA');
    }
  }

  /**
   * Create signing payload
   */
  private createSigningPayload(request: CreateEventPacketRequest, codeHash: string): string {
    const payload = {
      eventType: request.eventType,
      workflowId: request.workflowId,
      agentId: request.agentId,
      codeHash,
      codeDiff: request.codeDiff,
      testMetrics: request.testMetrics,
      agentMetadata: request.agentMetadata,
      timestamp: new Date().toISOString()
    };

    return JSON.stringify(payload, null, 0);
  }

  /**
   * Convert database packet to response format
   */
  private convertToPacketData(packet: any): SignedEventPacketData {
    return {
      id: packet.id,
      eventType: packet.eventType as EventType,
      workflowId: packet.workflowId,
      agentId: packet.agentId,
      codeHash: packet.codeHash,
      codeDiff: packet.codeDiff,
      testMetrics: packet.testMetrics ? JSON.parse(packet.testMetrics) : undefined,
      agentMetadata: JSON.parse(packet.agentMetadata),
      trustSignature: packet.trustSignature,
      timestamp: packet.timestamp,
      isVerified: packet.isVerified,
      verificationLog: packet.verificationLog ? JSON.parse(packet.verificationLog) : undefined
    };
  }

  /**
   * Get recent activity statistics
   */
  private async getRecentActivityStats() {
    const now = new Date();
    const last24Hours = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const lastWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const lastMonth = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    const [day, week, month] = await Promise.all([
      this.prisma.signedEventPacket.count({ where: { timestamp: { gte: last24Hours } } }),
      this.prisma.signedEventPacket.count({ where: { timestamp: { gte: lastWeek } } }),
      this.prisma.signedEventPacket.count({ where: { timestamp: { gte: lastMonth } } })
    ]);

    return {
      last24Hours: day,
      lastWeek: week,
      lastMonth: month
    };
  }

  /**
   * Get event type statistics
   */
  private async getEventTypeStats(): Promise<Record<EventType, number>> {
    const stats = await this.prisma.signedEventPacket.groupBy({
      by: ['eventType'],
      _count: { eventType: true }
    });

    const result: Record<EventType, number> = {} as any;
    for (const eventType of Object.values(EventType)) {
      result[eventType] = 0;
    }

    stats.forEach(stat => {
      result[stat.eventType as EventType] = stat._count.eventType;
    });

    return result;
  }

  /**
   * Get agent statistics
   */
  private async getAgentStats(): Promise<Record<string, number>> {
    const stats = await this.prisma.signedEventPacket.groupBy({
      by: ['agentId'],
      _count: { agentId: true },
      where: { agentId: { not: null } }
    });

    const result: Record<string, number> = {};
    stats.forEach(stat => {
      if (stat.agentId) {
        result[stat.agentId] = stat._count.agentId;
      }
    });

    return result;
  }
}

// Export singleton instance
export const signedEventPacketService = (prisma: PrismaClient) => new SignedEventPacketService(prisma);

import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Card,
  CardContent,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  Alert,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Tooltip,
  Tabs,
  Tab,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  Refresh,
  Search,
  Merge,
  Layers,
  Public,
  Lock,
  Security,
  ExpandMore,
  Visibility,
  Code,
  Timeline,
} from '@mui/icons-material';
import { TreeView, TreeItem } from '@mui/x-tree-view';

interface ContextManagementProps {
  onCreateContext?: (data: any) => void;
  onUpdateContext?: (id: string, data: any) => void;
  onDeleteContext?: (id: string) => void;
  onMergeContexts?: (layerIds: string[], strategy: any) => void;
  onRefresh?: () => void;
}

interface ContextLayer {
  id: string;
  name: string;
  type: 'GLOBAL' | 'WORKFLOW' | 'AGENT' | 'SESSION' | 'TUNNEL';
  priority: number;
  data: Record<string, any>;
  metadata: {
    createdAt: string;
    updatedAt: string;
    version: number;
    tags: string[];
    accessLevel: 'PUBLIC' | 'PRIVATE' | 'RESTRICTED';
  };
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`context-tabpanel-${index}`}
      aria-labelledby={`context-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

export const ContextManagement: React.FC<ContextManagementProps> = ({
  onCreateContext,
  onUpdateContext,
  onDeleteContext,
  onMergeContexts,
  onRefresh,
}) => {
  const [contexts, setContexts] = useState<ContextLayer[]>([]);
  const [statistics, setStatistics] = useState<any>(null);
  const [createDialog, setCreateDialog] = useState(false);
  const [editDialog, setEditDialog] = useState(false);
  const [mergeDialog, setMergeDialog] = useState(false);
  const [selectedContext, setSelectedContext] = useState<ContextLayer | null>(null);
  const [selectedContexts, setSelectedContexts] = useState<string[]>([]);
  const [tabValue, setTabValue] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const [newContext, setNewContext] = useState({
    name: '',
    type: 'GLOBAL' as ContextLayer['type'],
    data: '{}',
    priority: 5,
    tags: '',
    accessLevel: 'PUBLIC' as ContextLayer['metadata']['accessLevel'],
  });

  const [mergeStrategy, setMergeStrategy] = useState({
    type: 'PRIORITY_BASED',
    conflictResolution: 'HIGHEST_PRIORITY',
    preserveHistory: false,
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setIsLoading(true);
      
      const [contextsResponse, statsResponse] = await Promise.all([
        fetch('/api/context'),
        fetch('/api/context/stats/overview'),
      ]);

      if (contextsResponse.ok && statsResponse.ok) {
        const contextsData = await contextsResponse.json();
        const statsData = await statsResponse.json();
        
        setContexts(contextsData.data);
        setStatistics(statsData.data);
        setError(null);
      } else {
        throw new Error('Failed to load context data');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to load data');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateContext = async () => {
    try {
      let parsedData;
      try {
        parsedData = JSON.parse(newContext.data);
      } catch {
        throw new Error('Invalid JSON data');
      }

      const contextData = {
        name: newContext.name,
        type: newContext.type,
        data: parsedData,
        priority: newContext.priority,
        tags: newContext.tags.split(',').map(tag => tag.trim()).filter(Boolean),
        accessLevel: newContext.accessLevel,
      };

      await onCreateContext?.(contextData);
      setCreateDialog(false);
      setNewContext({
        name: '',
        type: 'GLOBAL',
        data: '{}',
        priority: 5,
        tags: '',
        accessLevel: 'PUBLIC',
      });
      loadData();
    } catch (err: any) {
      setError(err.message || 'Failed to create context');
    }
  };

  const handleUpdateContext = async () => {
    if (!selectedContext) return;

    try {
      let parsedData;
      try {
        parsedData = JSON.parse(newContext.data);
      } catch {
        throw new Error('Invalid JSON data');
      }

      const updateData = {
        data: parsedData,
        priority: newContext.priority,
        tags: newContext.tags.split(',').map(tag => tag.trim()).filter(Boolean),
        accessLevel: newContext.accessLevel,
      };

      await onUpdateContext?.(selectedContext.id, updateData);
      setEditDialog(false);
      setSelectedContext(null);
      loadData();
    } catch (err: any) {
      setError(err.message || 'Failed to update context');
    }
  };

  const handleDeleteContext = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this context layer?')) {
      try {
        await onDeleteContext?.(id);
        loadData();
      } catch (err: any) {
        setError(err.message || 'Failed to delete context');
      }
    }
  };

  const handleMergeContexts = async () => {
    if (selectedContexts.length < 2) {
      setError('Please select at least 2 contexts to merge');
      return;
    }

    try {
      await onMergeContexts?.(selectedContexts, mergeStrategy);
      setMergeDialog(false);
      setSelectedContexts([]);
      loadData();
    } catch (err: any) {
      setError(err.message || 'Failed to merge contexts');
    }
  };

  const openEditDialog = (context: ContextLayer) => {
    setSelectedContext(context);
    setNewContext({
      name: context.name,
      type: context.type,
      data: JSON.stringify(context.data, null, 2),
      priority: context.priority,
      tags: context.metadata.tags.join(', '),
      accessLevel: context.metadata.accessLevel,
    });
    setEditDialog(true);
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'GLOBAL': return <Public />;
      case 'WORKFLOW': return <Timeline />;
      case 'AGENT': return <Security />;
      case 'SESSION': return <Code />;
      case 'TUNNEL': return <Layers />;
      default: return <Layers />;
    }
  };

  const getAccessLevelColor = (level: string) => {
    switch (level) {
      case 'PUBLIC': return 'success';
      case 'PRIVATE': return 'warning';
      case 'RESTRICTED': return 'error';
      default: return 'default';
    }
  };

  const filteredContexts = contexts.filter(context => {
    const matchesSearch = !searchTerm || 
      context.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      context.metadata.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesType = !filterType || context.type === filterType;
    
    return matchesSearch && matchesType;
  });

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h5">Model Context Protocol</Typography>
        <Box>
          <Button
            startIcon={<Refresh />}
            onClick={onRefresh}
            sx={{ mr: 1 }}
          >
            Refresh
          </Button>
          <Button
            startIcon={<Merge />}
            variant="outlined"
            onClick={() => setMergeDialog(true)}
            disabled={selectedContexts.length < 2}
            sx={{ mr: 1 }}
          >
            Merge ({selectedContexts.length})
          </Button>
          <Button
            startIcon={<Add />}
            variant="contained"
            onClick={() => setCreateDialog(true)}
          >
            Create Context
          </Button>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Statistics Cards */}
      {statistics && (
        <Grid container spacing={3} mb={3}>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Typography variant="h6">Total Layers</Typography>
                <Typography variant="h4" color="primary">
                  {statistics.totalLayers}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Typography variant="h6">Cache Size</Typography>
                <Typography variant="h4" color="primary">
                  {statistics.cacheSize}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6">Layers by Type</Typography>
                <Box display="flex" gap={1} flexWrap="wrap" mt={1}>
                  {statistics.layersByType?.map((type: any) => (
                    <Chip
                      key={type.type}
                      label={`${type.type}: ${type.count}`}
                      size="small"
                      variant="outlined"
                    />
                  ))}
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Search and Filter */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              placeholder="Search contexts..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: <Search sx={{ mr: 1, color: 'text.secondary' }} />,
              }}
            />
          </Grid>
          <Grid item xs={12} md={3}>
            <FormControl fullWidth>
              <InputLabel>Filter by Type</InputLabel>
              <Select
                value={filterType}
                label="Filter by Type"
                onChange={(e) => setFilterType(e.target.value)}
              >
                <MenuItem value="">All Types</MenuItem>
                <MenuItem value="GLOBAL">Global</MenuItem>
                <MenuItem value="WORKFLOW">Workflow</MenuItem>
                <MenuItem value="AGENT">Agent</MenuItem>
                <MenuItem value="SESSION">Session</MenuItem>
                <MenuItem value="TUNNEL">Tunnel</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </Paper>

      {/* Context Layers Table */}
      <Paper>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell padding="checkbox">
                  {/* Checkbox for select all would go here */}
                </TableCell>
                <TableCell>Name</TableCell>
                <TableCell>Type</TableCell>
                <TableCell>Priority</TableCell>
                <TableCell>Access Level</TableCell>
                <TableCell>Tags</TableCell>
                <TableCell>Version</TableCell>
                <TableCell>Updated</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredContexts.map((context) => (
                <TableRow key={context.id}>
                  <TableCell padding="checkbox">
                    <input
                      type="checkbox"
                      checked={selectedContexts.includes(context.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedContexts(prev => [...prev, context.id]);
                        } else {
                          setSelectedContexts(prev => prev.filter(id => id !== context.id));
                        }
                      }}
                    />
                  </TableCell>
                  <TableCell>
                    <Box display="flex" alignItems="center" gap={1}>
                      {getTypeIcon(context.type)}
                      <Typography variant="body2">{context.name}</Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Chip label={context.type} size="small" variant="outlined" />
                  </TableCell>
                  <TableCell>{context.priority}</TableCell>
                  <TableCell>
                    <Chip
                      label={context.metadata.accessLevel}
                      size="small"
                      color={getAccessLevelColor(context.metadata.accessLevel)}
                    />
                  </TableCell>
                  <TableCell>
                    <Box display="flex" gap={0.5} flexWrap="wrap">
                      {context.metadata.tags.slice(0, 3).map((tag) => (
                        <Chip key={tag} label={tag} size="small" />
                      ))}
                      {context.metadata.tags.length > 3 && (
                        <Chip label={`+${context.metadata.tags.length - 3}`} size="small" />
                      )}
                    </Box>
                  </TableCell>
                  <TableCell>v{context.metadata.version}</TableCell>
                  <TableCell>
                    {new Date(context.metadata.updatedAt).toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    <Tooltip title="View Details">
                      <IconButton size="small">
                        <Visibility />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Edit">
                      <IconButton size="small" onClick={() => openEditDialog(context)}>
                        <Edit />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Delete">
                      <IconButton size="small" onClick={() => handleDeleteContext(context.id)}>
                        <Delete />
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>

      {/* Create Context Dialog */}
      <Dialog open={createDialog} onClose={() => setCreateDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Create Context Layer</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Name"
                value={newContext.name}
                onChange={(e) => setNewContext(prev => ({ ...prev, name: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Type</InputLabel>
                <Select
                  value={newContext.type}
                  label="Type"
                  onChange={(e) => setNewContext(prev => ({ ...prev, type: e.target.value as ContextLayer['type'] }))}
                >
                  <MenuItem value="GLOBAL">Global</MenuItem>
                  <MenuItem value="WORKFLOW">Workflow</MenuItem>
                  <MenuItem value="AGENT">Agent</MenuItem>
                  <MenuItem value="SESSION">Session</MenuItem>
                  <MenuItem value="TUNNEL">Tunnel</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Priority"
                type="number"
                value={newContext.priority}
                onChange={(e) => setNewContext(prev => ({ ...prev, priority: parseInt(e.target.value) }))}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <FormControl fullWidth>
                <InputLabel>Access Level</InputLabel>
                <Select
                  value={newContext.accessLevel}
                  label="Access Level"
                  onChange={(e) => setNewContext(prev => ({ ...prev, accessLevel: e.target.value as ContextLayer['metadata']['accessLevel'] }))}
                >
                  <MenuItem value="PUBLIC">Public</MenuItem>
                  <MenuItem value="PRIVATE">Private</MenuItem>
                  <MenuItem value="RESTRICTED">Restricted</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Tags (comma-separated)"
                value={newContext.tags}
                onChange={(e) => setNewContext(prev => ({ ...prev, tags: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Data (JSON)"
                multiline
                rows={8}
                value={newContext.data}
                onChange={(e) => setNewContext(prev => ({ ...prev, data: e.target.value }))}
                helperText="Enter valid JSON data"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateDialog(false)}>Cancel</Button>
          <Button onClick={handleCreateContext} variant="contained">Create</Button>
        </DialogActions>
      </Dialog>

      {/* Edit Context Dialog */}
      <Dialog open={editDialog} onClose={() => setEditDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Edit Context Layer</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Priority"
                type="number"
                value={newContext.priority}
                onChange={(e) => setNewContext(prev => ({ ...prev, priority: parseInt(e.target.value) }))}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <FormControl fullWidth>
                <InputLabel>Access Level</InputLabel>
                <Select
                  value={newContext.accessLevel}
                  label="Access Level"
                  onChange={(e) => setNewContext(prev => ({ ...prev, accessLevel: e.target.value as ContextLayer['metadata']['accessLevel'] }))}
                >
                  <MenuItem value="PUBLIC">Public</MenuItem>
                  <MenuItem value="PRIVATE">Private</MenuItem>
                  <MenuItem value="RESTRICTED">Restricted</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Tags (comma-separated)"
                value={newContext.tags}
                onChange={(e) => setNewContext(prev => ({ ...prev, tags: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Data (JSON)"
                multiline
                rows={8}
                value={newContext.data}
                onChange={(e) => setNewContext(prev => ({ ...prev, data: e.target.value }))}
                helperText="Enter valid JSON data"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialog(false)}>Cancel</Button>
          <Button onClick={handleUpdateContext} variant="contained">Update</Button>
        </DialogActions>
      </Dialog>

      {/* Merge Contexts Dialog */}
      <Dialog open={mergeDialog} onClose={() => setMergeDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Merge Context Layers</DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            Selected {selectedContexts.length} context layers for merging
          </Typography>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>Merge Strategy</InputLabel>
                <Select
                  value={mergeStrategy.type}
                  label="Merge Strategy"
                  onChange={(e) => setMergeStrategy(prev => ({ ...prev, type: e.target.value }))}
                >
                  <MenuItem value="OVERRIDE">Override</MenuItem>
                  <MenuItem value="MERGE">Merge</MenuItem>
                  <MenuItem value="APPEND">Append</MenuItem>
                  <MenuItem value="PRIORITY_BASED">Priority Based</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>Conflict Resolution</InputLabel>
                <Select
                  value={mergeStrategy.conflictResolution}
                  label="Conflict Resolution"
                  onChange={(e) => setMergeStrategy(prev => ({ ...prev, conflictResolution: e.target.value }))}
                >
                  <MenuItem value="LATEST">Latest</MenuItem>
                  <MenuItem value="HIGHEST_PRIORITY">Highest Priority</MenuItem>
                  <MenuItem value="MANUAL">Manual</MenuItem>
                  <MenuItem value="VERSIONED">Versioned</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setMergeDialog(false)}>Cancel</Button>
          <Button onClick={handleMergeContexts} variant="contained">Merge</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ContextManagement;

# Universal Dual-Purpose Feedback Loop Framework Configuration

# Framework Version
version: "1.0.0"

# Global Settings
global:
  log_level: "INFO"
  log_file: "feedback_loop.log"
  enable_real_time: true
  enable_batch: true
  max_processing_time: 30.0
  developer_only: true

# Feedback Engine Configuration
feedback_engine:
  # Performance settings
  max_concurrent_processes: 10
  timeout_seconds: 30
  retry_attempts: 3

  # Quality settings
  min_confidence_threshold: 0.3
  min_trust_threshold: 0.4

# Confidence Model Configuration
confidence_model:
  type: "adaptive"

  # Adjustment parameters
  correct_bonus: 0.05
  partially_correct_penalty: 0.02
  incorrect_penalty: 0.10
  miscellaneous_neutral: 0.0

  # Learning parameters
  learning_rate: 0.1
  decay_factor: 0.95
  history_window: 100

  # Bounds
  min_confidence: 0.0
  max_confidence: 1.0

# Trust Score Calculator Configuration
trust_calculator:
  # Feedback type weights
  correct_weight: 1.0
  partially_correct_weight: 0.7
  incorrect_weight: 0.0
  miscellaneous_weight: 0.5

  # Learning parameters
  decay_rate: 0.95
  learning_rate: 0.1
  history_window: 100
  min_entries_for_trust: 5

# Memory Store Configuration
memory_store:
  type: "file"

  # Storage settings
  base_path: "./feedback_data"
  compression_enabled: true
  max_file_size_mb: 10
  retention_days: 30
  backup_enabled: true

  # Performance settings
  cache_size: 1000
  cache_expiry_minutes: 15

# Domain Configurations
domains:
  # Drone AI Domain
  drone_ai:
    enabled: true

    # Sensor thresholds
    thresholds:
      gps:
        accuracy_threshold: 5.0      # meters
        altitude_min: -100.0         # meters
        altitude_max: 10000.0        # meters
        speed_max: 50.0              # m/s

      imu:
        acceleration_max: 50.0       # m/s²
        gyro_max: 10.0              # rad/s
        vibration_threshold: 5.0     # m/s²

      environmental:
        temperature_min: -40.0       # °C
        temperature_max: 60.0        # °C
        humidity_max: 100.0          # %
        pressure_min: 300.0          # hPa
        pressure_max: 1100.0         # hPa
        wind_speed_max: 25.0         # m/s

      mission:
        waypoint_tolerance: 10.0     # meters
        altitude_tolerance: 5.0      # meters
        heading_tolerance: 15.0      # degrees
        time_tolerance: 30.0         # seconds

    # Validation criteria
    validation_criteria:
      correctness_thresholds:
        gps_accuracy: 5.0
        altitude_accuracy: 2.0
        heading_accuracy: 10.0
        mission_completion: 0.95
        partial_completion: 0.70

      partial_correctness_rules:
        area_scanning:
          min_coverage: 0.70
          full_coverage: 0.95
        waypoint_navigation:
          min_waypoints: 0.70
          full_waypoints: 0.95
        sensor_data_quality:
          min_quality: 0.60
          full_quality: 0.90

  # TimeStamp AI Domain
  timestamp_ai:
    enabled: true

    # Accuracy thresholds
    thresholds:
      timestamp_accuracy:
        max_time_drift: 300.0        # seconds (5 minutes)
        future_tolerance: 60.0       # seconds
        past_tolerance: 86400.0      # seconds (24 hours)
        precision_threshold: 1.0     # seconds

      environmental_impact:
        water_usage_max: 1000.0      # ml per query
        electricity_max: 100.0       # Wh per query
        carbon_footprint_max: 50.0   # g CO2 per query
        efficiency_threshold: 0.8    # 80% efficiency minimum

      hash_verification:
        hash_length: 64              # SHA-256 length
        signature_min_length: 128    # Minimum signature length
        verification_timeout: 30.0   # seconds

      llm_response:
        min_confidence: 0.7          # 70% minimum confidence
        max_response_time: 30.0      # seconds
        min_content_length: 10       # characters
        max_token_usage: 4000        # tokens

    # Validation criteria
    validation_criteria:
      correctness_thresholds:
        timestamp_accuracy: 60.0
        partial_accuracy: 300.0
        hash_verification: true
        environmental_efficiency: 0.8
        partial_efficiency: 0.6

      partial_correctness_rules:
        timestamp_accuracy:
          perfect_threshold: 10.0
          good_threshold: 60.0
          acceptable_threshold: 300.0
        environmental_impact:
          excellent_efficiency: 0.9
          good_efficiency: 0.8
          acceptable_efficiency: 0.6

  # Search and Rescue Domain
  search_rescue:
    enabled: true

    # Detection categories and thresholds
    detection_categories:
      target_person:
        confidence_threshold: 0.85
        priority: critical
        size_range_m: [0.3, 2.0]

      clothing:
        confidence_threshold: 0.75
        priority: high
        size_range_cm: [5, 100]
        color_matching: true

      personal_items:
        confidence_threshold: 0.80
        priority: high
        size_range_cm: [2, 50]
        age_appropriate: true

      broken_environment:
        confidence_threshold: 0.65
        priority: medium
        size_range_cm: [10, 200]

    # Mission success thresholds
    mission_thresholds:
      target_found_confidence: 0.85
      target_partial_confidence: 0.70
      reference_item_relevance: 0.65
      area_coverage_complete: 0.95
      area_coverage_partial: 0.70

    # Side pocket configuration
    side_pocket:
      base_path: "./sar_side_pocket"
      retention_days: 120
      auto_review_enabled: true
      retraining_threshold: 30
      uncertain_confidence: 0.50

    # Reference criteria
    reference_criteria:
      target_person:
        size_range_m: [0.3, 2.0]
        priority_multiplier: 10.0
      clothing:
        color_matching: true
        size_range_cm: [5, 100]
        priority_multiplier: 3.0
      personal_items:
        age_appropriate: true
        size_range_cm: [2, 50]
        priority_multiplier: 2.5
      broken_environment:
        pattern_consistency: true
        size_range_cm: [10, 200]
        priority_multiplier: 1.5

# Analytics Configuration
analytics:
  enabled: true

  # Dashboard settings
  dashboard:
    enabled: false
    port: 8080
    host: "localhost"
    refresh_interval: 30  # seconds

  # Reporting settings
  reports:
    enabled: true
    output_dir: "./reports"
    formats: ["json", "csv", "html"]
    schedule: "daily"  # daily, weekly, monthly

  # Metrics to track
  metrics:
    - "feedback_distribution"
    - "domain_performance"
    - "agent_trust_scores"
    - "processing_times"
    - "error_rates"
    - "trend_analysis"

# Integration Settings
integrations:
  # Existing AI Orchestration Hub
  ai_orchestration:
    enabled: false
    endpoint: "http://localhost:3000"
    api_key: "${AI_ORCHESTRATION_API_KEY}"

  # Darwin Gödel Machine
  dgm:
    enabled: false
    endpoint: "http://localhost:3003"
    api_key: "${DGM_API_KEY}"

  # Thread-Merging Orchestrator
  thread_merging:
    enabled: false
    endpoint: "http://localhost:3001"
    api_key: "${THREAD_MERGING_API_KEY}"

  # EcoStamp
  ecostamp:
    enabled: false
    endpoint: "http://localhost:3004"
    api_key: "${ECOSTAMP_API_KEY}"

# Security Settings
security:
  # Access control
  developer_only: true
  require_authentication: false

  # Data protection
  encrypt_storage: false
  anonymize_data: true

  # Audit logging
  audit_enabled: true
  audit_file: "feedback_audit.log"

# Performance Tuning
performance:
  # Processing limits
  max_batch_size: 1000
  max_memory_usage_mb: 512

  # Optimization settings
  enable_caching: true
  cache_ttl_minutes: 15

  # Monitoring
  enable_metrics: true
  metrics_interval: 60  # seconds

# Development Settings
development:
  # Debug options
  debug_mode: false
  verbose_logging: false

  # Testing
  enable_test_mode: false
  mock_external_services: false

  # Profiling
  enable_profiling: false
  profile_output_dir: "./profiles"

# Environment-specific overrides
environments:
  development:
    global:
      log_level: "DEBUG"
    development:
      debug_mode: true
      verbose_logging: true

  testing:
    memory_store:
      base_path: "./test_feedback_data"
      retention_days: 1
    development:
      enable_test_mode: true
      mock_external_services: true

  production:
    global:
      log_level: "WARNING"
    security:
      require_authentication: true
      encrypt_storage: true
    performance:
      max_memory_usage_mb: 1024

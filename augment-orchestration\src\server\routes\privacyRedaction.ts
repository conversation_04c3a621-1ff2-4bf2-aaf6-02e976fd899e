/**
 * Privacy/Redaction API Routes
 * 
 * RESTful API endpoints for intelligent redaction system
 * Core Gap 3: Privacy/Redaction Layer implementation
 */

import { Router } from 'express';
import { body, query, param, validationResult } from 'express-validator';
import { PrismaClient } from '@prisma/client';
import { PrivacyRedactionService } from '../services/PrivacyRedactionService';
import { asyncHandler } from '../utils/asyncHandler';
import { authenticate, authorize } from '../middleware/auth';
import { logger } from '../utils/logger';
import {
  RedactionRequest,
  UnredactionRequest,
  BatchRedactionRequest,
  RedactionLevel,
  SensitiveDataType,
  EntityContext
} from '../../shared/types/PrivacyRedaction';

const router = Router();
const prisma = new PrismaClient();
const redactionService = new PrivacyRedactionService(prisma);

// Validation middleware
const validateRedactionRequest = [
  body('text').notEmpty().withMessage('Text is required'),
  body('entityType').isIn(Object.values(EntityContext)).withMessage('Invalid entity type'),
  body('redactionLevel').isIn(Object.values(RedactionLevel)).withMessage('Invalid redaction level'),
  body('customRules').optional().isArray(),
  body('preserveStructure').optional().isBoolean(),
  body('allowReversible').optional().isBoolean(),
  body('contextHints').optional().isObject()
];

const validateUnredactionRequest = [
  body('redactedText').notEmpty().withMessage('Redacted text is required'),
  body('reversibilityKey').notEmpty().withMessage('Reversibility key is required'),
  body('integrityHash').notEmpty().withMessage('Integrity hash is required'),
  body('authorizedBy').notEmpty().withMessage('Authorization is required'),
  body('reason').notEmpty().withMessage('Reason is required')
];

const validateBatchRequest = [
  body('items').isArray({ min: 1, max: 1000 }).withMessage('Items array required (1-1000 items)'),
  body('items.*.text').notEmpty().withMessage('Each item must have text'),
  body('items.*.entityType').isIn(Object.values(EntityContext)).withMessage('Invalid entity type'),
  body('items.*.redactionLevel').isIn(Object.values(RedactionLevel)).withMessage('Invalid redaction level'),
  body('batchId').notEmpty().withMessage('Batch ID is required'),
  body('priority').optional().isIn(['LOW', 'MEDIUM', 'HIGH', 'URGENT']),
  body('notifyOnComplete').optional().isBoolean()
];

const validateRuleCreation = [
  body('name').notEmpty().withMessage('Rule name is required'),
  body('description').notEmpty().withMessage('Rule description is required'),
  body('pattern').notEmpty().withMessage('Pattern is required'),
  body('replacement').notEmpty().withMessage('Replacement text is required'),
  body('dataType').isIn(Object.values(SensitiveDataType)).withMessage('Invalid data type'),
  body('entityTypes').isArray().withMessage('Entity types array is required'),
  body('priority').isInt({ min: 1, max: 1000 }).withMessage('Priority must be 1-1000'),
  body('preserveFormat').optional().isBoolean(),
  body('contextAware').optional().isBoolean(),
  body('reversible').optional().isBoolean()
];

/**
 * POST /api/privacy-redaction/redact
 * Redact sensitive data from text
 */
router.post('/redact',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR', 'AGENT']),
  validateRedactionRequest,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const request: RedactionRequest = {
      text: req.body.text,
      entityType: req.body.entityType,
      redactionLevel: req.body.redactionLevel,
      customRules: req.body.customRules,
      preserveStructure: req.body.preserveStructure,
      allowReversible: req.body.allowReversible,
      contextHints: req.body.contextHints
    };

    const result = await redactionService.redactText(request);

    logger.info('Text redacted via API', {
      entityType: request.entityType,
      redactionLevel: request.redactionLevel,
      redactionCount: result.metadata.redactionCount,
      processingTime: result.metadata.processingTime,
      userId: req.user?.id
    });

    res.json({
      success: true,
      result: {
        redactedText: result.redactedText,
        redactionCount: result.metadata.redactionCount,
        sensitiveDataFound: result.metadata.sensitiveDataFound,
        integrityHash: result.integrityHash,
        reversibilityKey: result.reversibilityKey,
        processingTime: result.metadata.processingTime
      }
    });
  })
);

/**
 * POST /api/privacy-redaction/unredact
 * Unredact text with proper authorization
 */
router.post('/unredact',
  authenticate,
  authorize(['ADMIN']),
  validateUnredactionRequest,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const request: UnredactionRequest = {
      redactedText: req.body.redactedText,
      reversibilityKey: req.body.reversibilityKey,
      integrityHash: req.body.integrityHash,
      authorizedBy: req.user?.id || req.body.authorizedBy,
      reason: req.body.reason
    };

    const originalText = await redactionService.unredactText(request);

    logger.warn('Text unredacted via API', {
      authorizedBy: request.authorizedBy,
      reason: request.reason,
      userId: req.user?.id
    });

    res.json({
      success: true,
      originalText,
      message: 'Text successfully unredacted'
    });
  })
);

/**
 * POST /api/privacy-redaction/batch-redact
 * Process batch redaction requests
 */
router.post('/batch-redact',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR']),
  validateBatchRequest,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const request: BatchRedactionRequest = {
      items: req.body.items,
      batchId: req.body.batchId,
      priority: req.body.priority || 'MEDIUM',
      notifyOnComplete: req.body.notifyOnComplete || false
    };

    const response = await redactionService.processBatchRedaction(request);

    logger.info('Batch redaction completed via API', {
      batchId: request.batchId,
      totalItems: response.totalItems,
      successfulItems: response.successfulItems,
      failedItems: response.failedItems,
      processingTime: response.processingTime,
      userId: req.user?.id
    });

    res.json({
      success: true,
      batch: response
    });
  })
);

/**
 * GET /api/privacy-redaction/stats
 * Get redaction statistics
 */
router.get('/stats',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR']),
  asyncHandler(async (req, res) => {
    const stats = await redactionService.getRedactionStats();

    res.json({
      success: true,
      stats
    });
  })
);

/**
 * POST /api/privacy-redaction/rules
 * Create new redaction rule
 */
router.post('/rules',
  authenticate,
  authorize(['ADMIN']),
  validateRuleCreation,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const ruleData = {
      name: req.body.name,
      description: req.body.description,
      pattern: req.body.pattern,
      replacement: req.body.replacement,
      dataType: req.body.dataType,
      entityTypes: req.body.entityTypes,
      isActive: req.body.isActive !== false,
      priority: req.body.priority,
      preserveFormat: req.body.preserveFormat || false,
      contextAware: req.body.contextAware || true,
      reversible: req.body.reversible || true,
      createdBy: req.user?.id || 'unknown'
    };

    const rule = await redactionService.createRedactionRule(ruleData);

    logger.info('Created redaction rule via API', {
      ruleId: rule.id,
      name: rule.name,
      dataType: rule.dataType,
      userId: req.user?.id
    });

    res.status(201).json({
      success: true,
      rule
    });
  })
);

/**
 * GET /api/privacy-redaction/test-pattern
 * Test a redaction pattern against sample text
 */
router.get('/test-pattern',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR']),
  query('pattern').notEmpty().withMessage('Pattern is required'),
  query('testText').notEmpty().withMessage('Test text is required'),
  query('dataType').optional().isIn(Object.values(SensitiveDataType)),
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const pattern = req.query.pattern as string;
    const testText = req.query.testText as string;
    const dataType = req.query.dataType as SensitiveDataType || SensitiveDataType.CUSTOM;

    try {
      const regex = new RegExp(pattern, 'g');
      const matches = [];
      let match;

      while ((match = regex.exec(testText)) !== null) {
        matches.push({
          match: match[0],
          startIndex: match.index,
          endIndex: match.index + match[0].length,
          groups: match.slice(1)
        });
      }

      res.json({
        success: true,
        pattern,
        testText,
        matches,
        matchCount: matches.length,
        isValid: true
      });

    } catch (error) {
      res.json({
        success: false,
        pattern,
        testText,
        error: error.message,
        isValid: false,
        matches: []
      });
    }
  })
);

/**
 * POST /api/privacy-redaction/analyze-text
 * Analyze text for potential sensitive data without redacting
 */
router.post('/analyze-text',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR', 'AGENT']),
  body('text').notEmpty().withMessage('Text is required'),
  body('entityType').isIn(Object.values(EntityContext)).withMessage('Invalid entity type'),
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    // Perform analysis without actual redaction
    const analysisRequest: RedactionRequest = {
      text: req.body.text,
      entityType: req.body.entityType,
      redactionLevel: RedactionLevel.BASIC,
      allowReversible: false
    };

    const result = await redactionService.redactText(analysisRequest);

    // Return analysis without the redacted text
    res.json({
      success: true,
      analysis: {
        sensitiveDataFound: result.metadata.sensitiveDataFound,
        redactionCount: result.metadata.redactionCount,
        riskLevel: result.metadata.contextAnalysis?.riskLevel || 'LOW',
        detectedPatterns: result.redactionMap.map(match => ({
          dataType: match.dataType,
          confidence: match.confidence,
          context: match.context.substring(0, 50) + '...',
          position: { start: match.startIndex, end: match.endIndex }
        })),
        recommendations: result.metadata.redactionCount > 0 
          ? ['Consider applying redaction before sharing this content']
          : ['No sensitive data detected - safe to share']
      }
    });
  })
);

/**
 * GET /api/privacy-redaction/compliance-report
 * Generate compliance report for redaction activities
 */
router.get('/compliance-report',
  authenticate,
  authorize(['ADMIN']),
  query('dateFrom').optional().isISO8601(),
  query('dateTo').optional().isISO8601(),
  query('regulation').optional().isIn(['GDPR', 'HIPAA', 'PCI_DSS', 'SOX', 'CUSTOM']),
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const dateFrom = req.query.dateFrom ? new Date(req.query.dateFrom as string) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const dateTo = req.query.dateTo ? new Date(req.query.dateTo as string) : new Date();
    const regulation = req.query.regulation as string || 'GDPR';

    // Get redaction statistics for the period
    const stats = await redactionService.getRedactionStats();

    // Generate compliance report
    const complianceReport = {
      reportId: `compliance_${Date.now()}`,
      generatedAt: new Date(),
      timeRange: { from: dateFrom, to: dateTo },
      regulation,
      summary: {
        totalRedactions: stats.totalRedactions,
        reversibleRedactions: stats.reversibleRedactions,
        averageProcessingTime: stats.averageProcessingTime,
        complianceScore: 95 // Mock score
      },
      findings: [
        {
          severity: 'LOW' as const,
          description: 'All redaction activities properly audited',
          recommendation: 'Continue current practices',
          affectedData: [],
          remediation: 'None required'
        }
      ],
      complianceLevel: 'COMPLIANT' as const,
      nextReviewDate: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000)
    };

    logger.info('Generated compliance report', {
      reportId: complianceReport.reportId,
      regulation,
      complianceLevel: complianceReport.complianceLevel,
      userId: req.user?.id
    });

    res.json({
      success: true,
      complianceReport
    });
  })
);

export default router;

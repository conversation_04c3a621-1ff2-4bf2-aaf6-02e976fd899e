/**
 * Cursor Adapter
 * 
 * Adapter for Cursor - AI-powered code generation with Claude 3.5 Sonnet
 * Specializes in: code generation, smart rewrites, single-file editing, completion
 */

const BaseAdapter = require('../base-adapter');
const axios = require('axios');
const { spawn } = require('child_process');
const fs = require('fs').promises;
const path = require('path');
const chalk = require('chalk');

class CursorAdapter extends BaseAdapter {
  constructor(config, metaOrchestrator) {
    super(config, metaOrchestrator);
    
    // Set adapter-specific properties
    this.adapterId = 'cursor';
    
    // Define capabilities
    this.addCapability('code-generation');
    this.addCapability('smart-rewrites');
    this.addCapability('single-file-editing');
    this.addCapability('completion');
    this.addCapability('refactoring');
    this.addCapability('chat');
    this.addCapability('documentation');
    
    // Define supported roles
    this.addSupportedRole('generator');
    this.addSupportedRole('completer');
    this.addSupportedRole('documenter');
    
    // Cursor specific configuration
    this.apiKey = this.getConfig('apiKey', process.env.CURSOR_API_KEY);
    this.baseUrl = this.getConfig('baseUrl', 'https://api.cursor.sh');
    this.model = this.getConfig('model', 'claude-3.5-sonnet');
    
    // API client
    this.client = null;
    
    // Cursor CLI path (if available)
    this.cursorCliPath = null;
  }
  
  async initialize() {
    try {
      this.log('info', 'Initializing Cursor adapter...');
      
      // Setup API client
      this.client = axios.create({
        baseURL: this.baseUrl,
        timeout: this.getConfig('timeout', 60000),
        headers: {
          'Content-Type': 'application/json',
          ...(this.apiKey && { 'Authorization': `Bearer ${this.apiKey}` })
        }
      });
      
      // Try to find Cursor CLI
      await this.findCursorCli();
      
      // Test connection
      await this.testConnection();
      
      this.state.initialized = true;
      this.log('info', 'Cursor adapter initialized successfully');
      
    } catch (error) {
      this.log('error', 'Failed to initialize Cursor adapter', { error: error.message });
      throw error;
    }
  }
  
  async findCursorCli() {
    try {
      // Common Cursor CLI paths
      const possiblePaths = [
        'cursor',
        '/usr/local/bin/cursor',
        '/Applications/Cursor.app/Contents/Resources/app/bin/cursor',
        'C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\cursor.exe'
      ];
      
      for (const cliPath of possiblePaths) {
        try {
          const result = await this.executeCommand(cliPath, ['--version']);
          if (result.success) {
            this.cursorCliPath = cliPath;
            this.log('info', `Found Cursor CLI at: ${cliPath}`);
            break;
          }
        } catch (error) {
          // Continue to next path
        }
      }
      
      if (!this.cursorCliPath) {
        this.log('warn', 'Cursor CLI not found - using API only mode');
      }
      
    } catch (error) {
      this.log('warn', 'Failed to find Cursor CLI', { error: error.message });
    }
  }
  
  async testConnection() {
    try {
      if (this.apiKey) {
        // Test API connection
        const response = await this.client.get('/health');
        if (response.status === 200) {
          this.log('info', 'Cursor API connection successful');
          return true;
        }
      }
      
      // If API not available, check CLI
      if (this.cursorCliPath) {
        this.log('info', 'Using Cursor CLI mode');
        return true;
      }
      
      throw new Error('Neither Cursor API nor CLI is available');
      
    } catch (error) {
      this.log('warn', 'Cursor connection test failed', { error: error.message });
      throw error;
    }
  }
  
  async checkAvailability() {
    try {
      // Check API availability
      if (this.apiKey && this.client) {
        try {
          const response = await this.client.get('/health', { timeout: 5000 });
          if (response.status === 200) {
            return true;
          }
        } catch (apiError) {
          this.log('debug', 'API check failed, trying CLI', { error: apiError.message });
        }
      }
      
      // Check CLI availability
      if (this.cursorCliPath) {
        try {
          const result = await this.executeCommand(this.cursorCliPath, ['--version'], { timeout: 5000 });
          return result.success;
        } catch (cliError) {
          this.log('debug', 'CLI check failed', { error: cliError.message });
        }
      }
      
      return false;
      
    } catch (error) {
      this.log('debug', 'Availability check failed', { error: error.message });
      return false;
    }
  }
  
  async execute(task, context) {
    this.validateTask(task);
    this.validateContext(context);
    
    const { role } = task;
    
    switch (role) {
      case 'generator':
        return await this.performGeneration(task, context);
      case 'completer':
        return await this.performCompletion(task, context);
      case 'documenter':
        return await this.performDocumentation(task, context);
      default:
        throw new Error(`Unsupported role for Cursor: ${role}`);
    }
  }
  
  async performGeneration(task, context) {
    try {
      this.log('info', 'Performing code generation', { task: task.type });
      
      const generationType = task.generationType || 'component';
      const requirements = task.requirements || task.description;
      
      let result;
      
      switch (generationType) {
        case 'component':
          result = await this.generateComponent(requirements, context);
          break;
        case 'function':
          result = await this.generateFunction(requirements, context);
          break;
        case 'class':
          result = await this.generateClass(requirements, context);
          break;
        case 'api':
          result = await this.generateAPI(requirements, context);
          break;
        case 'test':
          result = await this.generateTests(requirements, context);
          break;
        case 'refactor':
          result = await this.performRefactoring(requirements, context);
          break;
        default:
          result = await this.generateGeneric(requirements, context);
      }
      
      return {
        type: 'generation',
        generationType,
        result,
        metadata: {
          requirements,
          timestamp: Date.now(),
          adapterId: this.adapterId
        }
      };
      
    } catch (error) {
      this.log('error', 'Code generation failed', { error: error.message });
      throw error;
    }
  }
  
  async generateComponent(requirements, context) {
    const prompt = this.buildGenerationPrompt(requirements, context, 'component');
    
    if (this.apiKey) {
      return await this.generateWithAPI(prompt, context);
    } else if (this.cursorCliPath) {
      return await this.generateWithCLI(prompt, context);
    } else {
      throw new Error('No Cursor interface available');
    }
  }
  
  async generateFunction(requirements, context) {
    const prompt = this.buildGenerationPrompt(requirements, context, 'function');
    
    if (this.apiKey) {
      return await this.generateWithAPI(prompt, context);
    } else if (this.cursorCliPath) {
      return await this.generateWithCLI(prompt, context);
    } else {
      throw new Error('No Cursor interface available');
    }
  }
  
  async generateClass(requirements, context) {
    const prompt = this.buildGenerationPrompt(requirements, context, 'class');
    
    if (this.apiKey) {
      return await this.generateWithAPI(prompt, context);
    } else if (this.cursorCliPath) {
      return await this.generateWithCLI(prompt, context);
    } else {
      throw new Error('No Cursor interface available');
    }
  }
  
  async generateAPI(requirements, context) {
    const prompt = this.buildGenerationPrompt(requirements, context, 'api');
    
    if (this.apiKey) {
      return await this.generateWithAPI(prompt, context);
    } else if (this.cursorCliPath) {
      return await this.generateWithCLI(prompt, context);
    } else {
      throw new Error('No Cursor interface available');
    }
  }
  
  async generateTests(requirements, context) {
    const prompt = this.buildGenerationPrompt(requirements, context, 'test');
    
    if (this.apiKey) {
      return await this.generateWithAPI(prompt, context);
    } else if (this.cursorCliPath) {
      return await this.generateWithCLI(prompt, context);
    } else {
      throw new Error('No Cursor interface available');
    }
  }
  
  async performRefactoring(requirements, context) {
    const prompt = this.buildRefactoringPrompt(requirements, context);
    
    if (this.apiKey) {
      return await this.generateWithAPI(prompt, context);
    } else if (this.cursorCliPath) {
      return await this.generateWithCLI(prompt, context);
    } else {
      throw new Error('No Cursor interface available');
    }
  }
  
  async generateGeneric(requirements, context) {
    const prompt = this.buildGenerationPrompt(requirements, context, 'generic');
    
    if (this.apiKey) {
      return await this.generateWithAPI(prompt, context);
    } else if (this.cursorCliPath) {
      return await this.generateWithCLI(prompt, context);
    } else {
      throw new Error('No Cursor interface available');
    }
  }
  
  buildGenerationPrompt(requirements, context, type) {
    let prompt = `Generate ${type} based on the following requirements:\n\n${requirements}\n\n`;
    
    if (context.language) {
      prompt += `Language: ${context.language}\n`;
    }
    
    if (context.framework) {
      prompt += `Framework: ${context.framework}\n`;
    }
    
    if (context.style) {
      prompt += `Code style: ${context.style}\n`;
    }
    
    if (context.existingCode) {
      prompt += `\nExisting code context:\n\`\`\`\n${context.existingCode}\n\`\`\`\n`;
    }
    
    if (context.dependencies) {
      prompt += `\nAvailable dependencies: ${context.dependencies.join(', ')}\n`;
    }
    
    prompt += '\nPlease provide clean, well-documented code that follows best practices.';
    
    return prompt;
  }
  
  buildRefactoringPrompt(requirements, context) {
    let prompt = `Refactor the following code based on these requirements:\n\n${requirements}\n\n`;
    
    if (context.sourceCode) {
      prompt += `Source code to refactor:\n\`\`\`\n${context.sourceCode}\n\`\`\`\n`;
    }
    
    if (context.refactoringGoals) {
      prompt += `\nRefactoring goals: ${context.refactoringGoals.join(', ')}\n`;
    }
    
    prompt += '\nPlease provide the refactored code with explanations of the changes made.';
    
    return prompt;
  }
  
  async generateWithAPI(prompt, context) {
    try {
      const response = await this.client.post('/api/generate', {
        model: this.model,
        prompt,
        max_tokens: context.maxTokens || 4000,
        temperature: context.temperature || 0.7,
        context: {
          projectPath: context.projectPath,
          language: context.language,
          framework: context.framework
        }
      });
      
      return {
        code: response.data.generated_code,
        explanation: response.data.explanation,
        suggestions: response.data.suggestions,
        method: 'api'
      };
      
    } catch (error) {
      this.log('error', 'API generation failed', { error: error.message });
      throw error;
    }
  }
  
  async generateWithCLI(prompt, context) {
    try {
      // Create temporary file with prompt
      const tempDir = path.join(process.cwd(), '.temp');
      await fs.mkdir(tempDir, { recursive: true });
      
      const promptFile = path.join(tempDir, `cursor-prompt-${Date.now()}.txt`);
      await fs.writeFile(promptFile, prompt);
      
      // Execute Cursor CLI
      const args = [
        'generate',
        '--prompt-file', promptFile,
        '--model', this.model
      ];
      
      if (context.projectPath) {
        args.push('--project', context.projectPath);
      }
      
      const result = await this.executeCommand(this.cursorCliPath, args);
      
      // Cleanup
      await fs.unlink(promptFile);
      
      if (result.success) {
        return {
          code: result.stdout,
          explanation: 'Generated using Cursor CLI',
          method: 'cli'
        };
      } else {
        throw new Error(`CLI generation failed: ${result.stderr}`);
      }
      
    } catch (error) {
      this.log('error', 'CLI generation failed', { error: error.message });
      throw error;
    }
  }
  
  async performCompletion(task, context) {
    try {
      this.log('info', 'Performing code completion', { task: task.type });
      
      const partialCode = task.partialCode || context.partialCode;
      const cursorPosition = task.cursorPosition || context.cursorPosition;
      
      if (this.apiKey) {
        const response = await this.client.post('/api/complete', {
          model: this.model,
          code: partialCode,
          position: cursorPosition,
          max_completions: context.maxCompletions || 5,
          context: {
            language: context.language,
            framework: context.framework
          }
        });
        
        return {
          completions: response.data.completions,
          method: 'api'
        };
      } else {
        // CLI completion is more limited
        return {
          completions: ['// Cursor CLI completion not available'],
          method: 'fallback'
        };
      }
      
    } catch (error) {
      this.log('error', 'Code completion failed', { error: error.message });
      throw error;
    }
  }
  
  async performDocumentation(task, context) {
    try {
      this.log('info', 'Performing documentation generation', { task: task.type });
      
      const sourceCode = task.sourceCode || context.sourceCode;
      const documentationType = task.documentationType || 'comprehensive';
      
      const prompt = `Generate ${documentationType} documentation for the following code:\n\n\`\`\`\n${sourceCode}\n\`\`\`\n\nInclude function descriptions, parameter explanations, return values, and usage examples.`;
      
      if (this.apiKey) {
        return await this.generateWithAPI(prompt, context);
      } else if (this.cursorCliPath) {
        return await this.generateWithCLI(prompt, context);
      } else {
        throw new Error('No Cursor interface available');
      }
      
    } catch (error) {
      this.log('error', 'Documentation generation failed', { error: error.message });
      throw error;
    }
  }
  
  async executeCommand(command, args, options = {}) {
    return new Promise((resolve, reject) => {
      const timeout = options.timeout || 30000;
      
      const child = spawn(command, args, {
        stdio: ['pipe', 'pipe', 'pipe'],
        shell: true
      });
      
      let stdout = '';
      let stderr = '';
      
      child.stdout.on('data', (data) => {
        stdout += data.toString();
      });
      
      child.stderr.on('data', (data) => {
        stderr += data.toString();
      });
      
      const timeoutId = setTimeout(() => {
        child.kill();
        reject(new Error('Command timeout'));
      }, timeout);
      
      child.on('close', (code) => {
        clearTimeout(timeoutId);
        resolve({
          success: code === 0,
          stdout,
          stderr,
          exitCode: code
        });
      });
      
      child.on('error', (error) => {
        clearTimeout(timeoutId);
        reject(error);
      });
    });
  }
  
  validateContext(context) {
    super.validateContext(context);
    
    // Cursor specific context validation
    if (context.role === 'completer' && !context.partialCode) {
      throw new Error('partialCode is required for completion tasks');
    }
    
    if (context.role === 'documenter' && !context.sourceCode) {
      throw new Error('sourceCode is required for documentation tasks');
    }
    
    return true;
  }
  
  async shutdown() {
    this.log('info', 'Shutting down Cursor adapter');
    
    if (this.client) {
      this.client = null;
    }
    
    await super.shutdown();
  }
}

module.exports = CursorAdapter;

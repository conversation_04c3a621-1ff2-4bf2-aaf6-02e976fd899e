import express from "express";
import crypto from "crypto";
import rateLimit from "express-rate-limit";
import path from "path";
import cors from "cors";
import { IMPACT_MODELS, COMPLEXITY_LEVELS } from "./config.js";
import { calculateConversationImpact, calculateTokenBasedImpact, getCurrentImpactRates } from "./models/enhancedImpactModel.js";
import { getTokenAnalysis } from "./utils/tokenCounter.js";
import { runScrapingProcess } from "./scrapers/usageDataScraper.js";
import { dataScheduler } from "./schedulers/dataUpdateScheduler.js";
import {
  initializeHashRegistry,
  storeHash,
  searchHash,
  verifyHash,
  getRegistryStats,
  searchHashesByCriteria
} from "./models/hashRegistry.js";
import { uploadSingle, uploadMultiple, processUploadedFile } from "./middleware/fileUpload.js";
import { benchmarkUpdater } from "./schedulers/benchmarkUpdater.js";

const router = express.Router();

// Public verification routes (no rate limiting for public access)
router.get('/', (req, res) => {
  res.redirect('/index.html');
});

// Public hash verification API (CORS enabled for external access)
router.get('/public/verify/:hash', cors(), async (req, res) => {
  try {
    const { hash } = req.params;
    const result = searchHash(hash);

    if (result) {
      // Return public-safe information (no sensitive data like IP hashes)
      res.json({
        found: true,
        verified: true,
        timestamp: result.timestamp,
        platform: result.platform,
        model: result.model,
        ecoLevel: result.ecoLevel,
        energy: result.energy,
        water: result.water,
        hash: result.hash,
        publicUrl: `${req.protocol}://${req.get('host')}/public/verify/${hash}`,
        verifyUrl: `${req.protocol}://${req.get('host')}/verify.html?hash=${hash}`
      });
    } else {
      res.json({
        found: false,
        verified: false,
        message: 'Hash not found in public registry',
        searchUrl: `${req.protocol}://${req.get('host')}/verify.html`
      });
    }
  } catch (error) {
    res.status(500).json({
      found: false,
      verified: false,
      error: 'Verification failed',
      message: error.message
    });
  }
});

// Public file verification endpoint
router.post('/public/verify-file', cors(), uploadSingle, async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        verified: false,
        error: 'No file uploaded'
      });
    }

    const result = await processUploadedFile(req.file);

    if (!result.success) {
      return res.status(400).json({
        verified: false,
        error: result.error || 'File processing failed'
      });
    }

    // Check if hash exists in registry
    const registryEntry = searchHash(result.hash);

    res.json({
      verified: registryEntry ? true : false,
      hash: result.hash,
      file: {
        name: req.file.originalname,
        size: req.file.size,
        type: req.file.mimetype
      },
      registry: registryEntry ? {
        timestamp: registryEntry.timestamp,
        platform: registryEntry.platform,
        model: registryEntry.model,
        ecoLevel: registryEntry.ecoLevel,
        energy: registryEntry.energy,
        water: registryEntry.water
      } : null,
      publicUrl: registryEntry ?
        `${req.protocol}://${req.get('host')}/public/verify/${result.hash}` : null
    });

  } catch (error) {
    console.error('Public file verification error:', error);
    res.status(500).json({
      verified: false,
      error: 'File verification failed',
      message: error.message
    });
  }
});

// Rate limiting middleware for API endpoints
const apiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100
});
router.use('/api/', apiLimiter);

// Test endpoint
router.get('/test', (req, res) => {
  res.json({ status: 'active', message: 'API operational' });
});

// Legacy eco-impact calculation endpoint (kept for backward compatibility)
router.post('/api/calculate-impact', (req, res) => {
  const { platform, complexity = 'medium' } = req.body;

  // Validate platform
  if (!platform || !IMPACT_MODELS[platform]) {
    return res.status(400).json({ error: 'Invalid or missing platform' });
  }

  // Validate complexity
  if (!COMPLEXITY_LEVELS.includes(complexity)) {
    return res.status(400).json({ error: 'Invalid complexity level' });
  }

  const model = IMPACT_MODELS[platform];
  const multiplier = model.complexityMultiplier[complexity];
  const water = parseFloat((model.baseWater * multiplier).toFixed(4));
  const electricity = parseFloat((model.baseElectricity * multiplier).toFixed(5));

  res.json({
    water,
    electricity,
    units: { water: 'L', electricity: 'kWh' },
    method: 'legacy',
    note: 'Use /api/calculate-token-impact for more accurate calculations'
  });
});

// Enhanced token-based impact calculation
router.post('/api/calculate-token-impact', async (req, res) => {
  try {
    const { input, output, platform, model, tokenCount } = req.body;

    if (!platform) {
      return res.status(400).json({ error: 'Platform is required' });
    }

    let result;

    if (input && output) {
      // Calculate based on conversation text
      result = await calculateConversationImpact(input, output, platform, model);
    } else if (tokenCount) {
      // Calculate based on provided token count
      result = calculateTokenBasedImpact(tokenCount, platform, model);
    } else {
      return res.status(400).json({
        error: 'Either provide input/output text or tokenCount'
      });
    }

    res.json(result);

  } catch (error) {
    console.error('Token impact calculation error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get current impact rates
router.get('/api/impact-rates', (req, res) => {
  const rates = getCurrentImpactRates();
  res.json(rates);
});

// Token analysis endpoint
router.post('/api/analyze-tokens', (req, res) => {
  try {
    const { input, output, platform } = req.body;

    if (!input || !output || !platform) {
      return res.status(400).json({
        error: 'input, output, and platform are required'
      });
    }

    const analysis = getTokenAnalysis(input, output, platform);
    res.json(analysis);

  } catch (error) {
    console.error('Token analysis error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Enhanced timestamp endpoint with hash storage
router.post('/api/timestamps', async (req, res) => {
  const { hash, metadata = {} } = req.body;

  if (!hash || typeof hash !== 'string') {
    return res.status(400).json({ error: 'Invalid content hash' });
  }

  if (!process.env.TSA_SECRET) {
    return res.status(500).json({ error: 'TSA_SECRET environment variable not configured' });
  }

  const timestamp = new Date().toISOString();
  const signer = crypto.createHmac('sha256', process.env.TSA_SECRET);
  signer.update(`${hash}|${timestamp}`);
  const signature = signer.digest('hex');

  // Store hash in registry for searchability
  try {
    const hashMetadata = {
      ...metadata,
      signature,
      userAgent: req.headers['user-agent'],
      ipHash: crypto.createHash('sha256').update(req.ip || 'unknown').digest('hex').substring(0, 16)
    };

    await storeHash(hash, hashMetadata);
  } catch (error) {
    console.warn('Failed to store hash in registry:', error);
    // Continue with response even if storage fails
  }

  res.json({
    timestamp,
    signature,
    algorithm: 'HMAC-SHA256',
    searchable: true,
    verifyUrl: `${req.protocol}://${req.get('host')}/api/verify/${hash}`
  });
});

// Manual data update trigger
router.post('/api/update-data', async (req, res) => {
  try {
    console.log('Manual data update triggered');
    const scrapedData = await runScrapingProcess();
    res.json({
      success: true,
      message: 'Data update completed',
      data: scrapedData,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Manual data update failed:', error);
    res.status(500).json({
      success: false,
      error: 'Data update failed',
      message: error.message
    });
  }
});

// Scheduler status and control
router.get('/api/scheduler/status', (req, res) => {
  const status = dataScheduler.getStatus();
  res.json(status);
});

router.post('/api/scheduler/start', (req, res) => {
  try {
    dataScheduler.start(req.body);
    res.json({ success: true, message: 'Scheduler started' });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

router.post('/api/scheduler/stop', (req, res) => {
  try {
    dataScheduler.stop();
    res.json({ success: true, message: 'Scheduler stopped' });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// Hash verification and search endpoints
router.get('/api/verify/:hash', async (req, res) => {
  try {
    const { hash } = req.params;
    const { content } = req.query;

    const verification = await verifyHash(hash, content);
    res.json(verification);
  } catch (error) {
    res.status(500).json({
      valid: false,
      error: 'Verification failed',
      message: error.message
    });
  }
});

router.get('/api/search/:hash', (req, res) => {
  try {
    const { hash } = req.params;
    const result = searchHash(hash);

    if (result) {
      res.json({ found: true, entry: result });
    } else {
      res.json({ found: false, message: 'Hash not found' });
    }
  } catch (error) {
    res.status(500).json({
      found: false,
      error: 'Search failed',
      message: error.message
    });
  }
});

router.get('/api/registry/stats', (req, res) => {
  try {
    const stats = getRegistryStats();
    res.json(stats);
  } catch (error) {
    res.status(500).json({ error: 'Failed to get registry stats' });
  }
});

router.post('/api/registry/search', (req, res) => {
  try {
    const criteria = req.body;
    const results = searchHashesByCriteria(criteria);
    res.json({
      results,
      count: results.length,
      criteria
    });
  } catch (error) {
    res.status(500).json({
      error: 'Search failed',
      message: error.message
    });
  }
});

// File upload endpoints
router.post('/api/upload/single', uploadSingle, async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    const result = await processUploadedFile(req.file);

    if (!result.success) {
      return res.status(400).json(result);
    }

    // Calculate environmental impact for file processing
    const impact = await calculateTokenBasedImpact(
      `File: ${req.file.originalname}`,
      result.textContent,
      'universal',
      'file-processor'
    );

    // Store in hash registry
    await storeHash(result.hash, {
      platform: 'file-upload',
      model: 'file-processor',
      inputLength: req.file.originalname.length,
      outputLength: result.textContent.length,
      ecoLevel: impact.ecoLevel || 3,
      energy: impact.total?.energy?.value || 0,
      water: impact.total?.water?.value || 0,
      fileType: req.file.mimetype,
      fileName: req.file.originalname
    });

    res.json({
      success: true,
      file: result.file,
      impact,
      hash: result.hash,
      textPreview: result.textContent.substring(0, 500) + (result.textContent.length > 500 ? '...' : '')
    });

  } catch (error) {
    console.error('File upload error:', error);
    res.status(500).json({
      error: 'File upload failed',
      message: error.message
    });
  }
});

router.post('/api/upload/multiple', uploadMultiple, async (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({ error: 'No files uploaded' });
    }

    const results = [];
    let totalImpact = { energy: 0, water: 0 };

    for (const file of req.files) {
      const result = await processUploadedFile(file);

      if (result.success) {
        // Calculate impact for each file
        const impact = await calculateTokenBasedImpact(
          `File: ${file.originalname}`,
          result.textContent,
          'universal',
          'file-processor'
        );

        totalImpact.energy += impact.total?.energy?.value || 0;
        totalImpact.water += impact.total?.water?.value || 0;

        // Store in registry
        await storeHash(result.hash, {
          platform: 'file-upload',
          model: 'file-processor',
          inputLength: file.originalname.length,
          outputLength: result.textContent.length,
          ecoLevel: impact.ecoLevel || 3,
          energy: impact.total?.energy?.value || 0,
          water: impact.total?.water?.value || 0,
          fileType: file.mimetype,
          fileName: file.originalname
        });

        results.push({
          file: result.file,
          impact,
          hash: result.hash,
          textPreview: result.textContent.substring(0, 200) + '...'
        });
      } else {
        results.push({
          file: { originalName: file.originalname },
          error: result.error || 'Processing failed'
        });
      }
    }

    res.json({
      success: true,
      files: results,
      totalImpact,
      processedCount: results.filter(r => !r.error).length,
      totalCount: req.files.length
    });

  } catch (error) {
    console.error('Multiple file upload error:', error);
    res.status(500).json({
      error: 'File upload failed',
      message: error.message
    });
  }
});

// Benchmark endpoints
router.get('/api/benchmarks', (req, res) => {
  try {
    // Serve the latest benchmarks data
    res.sendFile(path.join(process.cwd(), 'source', 'public', 'data', 'benchmarks.json'));
  } catch (error) {
    res.status(500).json({ error: 'Failed to load benchmarks' });
  }
});

router.get('/api/benchmarks/status', (req, res) => {
  try {
    const status = benchmarkUpdater.getStatus();
    res.json(status);
  } catch (error) {
    res.status(500).json({ error: 'Failed to get benchmark status' });
  }
});

router.post('/api/benchmarks/update', (req, res) => {
  try {
    benchmarkUpdater.updateBenchmarks();
    res.json({ success: true, message: 'Benchmark update initiated' });
  } catch (error) {
    res.status(500).json({ error: 'Failed to initiate benchmark update' });
  }
});

// Download API routes for browser extensions
router.get('/api/download/:browser', async (req, res) => {
  try {
    const { browser } = req.params;
    const downloadsPath = path.join(process.cwd(), '..', '..', '..', 'downloads');

    // Browser-specific extension files
    const extensions = {
      chrome: {
        filename: 'ecostamp-chrome-extension.zip',
        path: path.join(downloadsPath, 'ecostamp-chrome-extension.zip'),
        contentType: 'application/zip'
      },
      firefox: {
        filename: 'ecostamp-firefox-extension.xpi',
        path: path.join(downloadsPath, 'ecostamp-firefox-extension.xpi'),
        contentType: 'application/x-xpinstall'
      },
      edge: {
        filename: 'ecostamp-edge-extension.zip',
        path: path.join(downloadsPath, 'ecostamp-edge-extension.zip'),
        contentType: 'application/zip'
      },
      opera: {
        filename: 'ecostamp-opera-extension.zip',
        path: path.join(downloadsPath, 'ecostamp-opera-extension.zip'),
        contentType: 'application/zip'
      },
      safari: {
        filename: 'ecostamp-safari-extension.zip',
        path: path.join(downloadsPath, 'ecostamp-safari-extension.zip'),
        contentType: 'application/zip'
      },
      universal: {
        filename: 'ecostamp-universal-extension.zip',
        path: path.join(downloadsPath, 'ecostamp-universal-extension.zip'),
        contentType: 'application/zip'
      }
    };

    const extension = extensions[browser];
    if (!extension) {
      return res.status(400).json({
        success: false,
        error: 'Unsupported browser'
      });
    }

    // Check if extension file exists
    const fs = await import('fs-extra');
    if (await fs.pathExists(extension.path)) {
      res.json({
        success: true,
        downloadUrl: `/downloads/${browser}/${extension.filename}`,
        filename: extension.filename,
        browser: browser,
        size: (await fs.stat(extension.path)).size
      });
    } else {
      // Try to create downloads if they don't exist
      try {
        const { spawn } = require('child_process');
        const createDownloads = spawn('node', ['../../../create-downloads.js'], {
          cwd: process.cwd(),
          stdio: 'pipe'
        });

        // Don't wait for completion, just indicate it's being prepared
        res.json({
          success: false,
          message: `${browser.charAt(0).toUpperCase() + browser.slice(1)} extension is being prepared`,
          comingSoon: true,
          preparing: true
        });
      } catch (error) {
        res.json({
          success: false,
          message: `${browser.charAt(0).toUpperCase() + browser.slice(1)} extension is being prepared`,
          comingSoon: true
        });
      }
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Download service temporarily unavailable'
    });
  }
});

// Serve extension files for download
router.get('/downloads/:browser/:filename', async (req, res) => {
  try {
    const { browser, filename } = req.params;
    const downloadsPath = path.join(process.cwd(), '..', '..', '..', 'downloads');
    const filePath = path.join(downloadsPath, filename);

    const fs = await import('fs-extra');
    if (await fs.pathExists(filePath)) {
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      res.setHeader('Content-Type', 'application/octet-stream');
      res.sendFile(path.resolve(filePath));
    } else {
      res.status(404).json({ error: 'Extension file not found' });
    }
  } catch (error) {
    res.status(500).json({ error: 'Download failed' });
  }
});

export default router;

/**
 * Model Context Protocol (MCP) + Capability Registry Service
 * 
 * Enhanced MCP integration with dynamic agent selection based on capabilities
 * and trust scores. Standardized request/response system for AI orchestration.
 */

import { EventEmitter } from 'events';
import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger';
import {
  AgentProfile,
  AgentCapability,
  CapabilityType,
  ProficiencyLevel,
  AgentStatus,
  MCPRequest,
  MCPResponse,
  RequestStatus,
  RequestPriority,
  CapabilityRegistry,
  TrustScore,
  PerformanceHistory,
  AvailabilityMatrix,
  SelectionCriteria,
  RoutingRule,
  MCPError,
  AgentSelectionError,
  CapabilityMismatchError,
  MCP_CONSTANTS,
  MCPUtils
} from '../../shared/types/MCPCapabilityRegistry';

export class MCPCapabilityRegistryService extends EventEmitter {
  private prisma: PrismaClient;
  private registry: CapabilityRegistry;
  private agentSelector: AgentSelector;
  private requestRouter: RequestRouter;
  private performanceTracker: PerformanceTracker;
  private trustManager: TrustManager;
  private availabilityMonitor: AvailabilityMonitor;
  private qualityAssessor: QualityAssessor;

  constructor(prisma: PrismaClient) {
    super();
    this.prisma = prisma;
    this.registry = this.initializeRegistry();
    this.agentSelector = new AgentSelector(this.registry);
    this.requestRouter = new RequestRouter(this.registry);
    this.performanceTracker = new PerformanceTracker();
    this.trustManager = new TrustManager();
    this.availabilityMonitor = new AvailabilityMonitor();
    this.qualityAssessor = new QualityAssessor();

    this.setupEventHandlers();
    this.startMonitoring();
  }

  /**
   * Register a new agent in the capability registry
   */
  async registerAgent(agentData: Partial<AgentProfile>): Promise<AgentProfile> {
    const agentId = agentData.id || `agent_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const agent: AgentProfile = {
      id: agentId,
      name: agentData.name || 'Unnamed Agent',
      description: agentData.description || '',
      version: agentData.version || '1.0.0',
      status: AgentStatus.AVAILABLE,
      capabilities: agentData.capabilities || [],
      trustScore: 0.7, // Default trust score
      reputation: 5.0, // Default reputation (0-10 scale)
      totalRequests: 0,
      successfulRequests: 0,
      averageRating: 0,
      lastActive: new Date(),
      createdAt: new Date(),
      updatedAt: new Date(),
      metadata: {
        provider: agentData.metadata?.provider || 'unknown',
        model: agentData.metadata?.model || 'unknown',
        version: agentData.metadata?.version || '1.0.0',
        architecture: agentData.metadata?.architecture || 'unknown',
        trainingData: agentData.metadata?.trainingData || 'unknown',
        knowledgeCutoff: agentData.metadata?.knowledgeCutoff,
        languages: agentData.metadata?.languages || ['en'],
        frameworks: agentData.metadata?.frameworks || [],
        domains: agentData.metadata?.domains || [],
        tags: agentData.metadata?.tags || [],
        customFields: agentData.metadata?.customFields || {}
      },
      configuration: {
        maxConcurrentRequests: agentData.configuration?.maxConcurrentRequests || 10,
        timeoutMs: agentData.configuration?.timeoutMs || MCP_CONSTANTS.DEFAULT_TIMEOUT,
        retryPolicy: agentData.configuration?.retryPolicy || {
          maxRetries: 3,
          retryDelay: 5000,
          backoffMultiplier: 2,
          retryableErrors: ['TIMEOUT', 'NETWORK_ERROR'],
          nonRetryableErrors: ['VALIDATION_ERROR', 'PERMISSION_DENIED']
        },
        resourceLimits: agentData.configuration?.resourceLimits || {
          maxMemoryMB: 1024,
          maxCpuPercent: 80,
          maxDiskMB: 5120,
          maxNetworkMBps: 100,
          maxExecutionTime: 300000,
          maxTokens: 4096,
          maxRequestSize: MCP_CONSTANTS.MAX_REQUEST_SIZE,
          maxResponseSize: MCP_CONSTANTS.MAX_RESPONSE_SIZE
        },
        securitySettings: agentData.configuration?.securitySettings || {
          requiresAuthentication: true,
          allowedOrigins: ['*'],
          encryptionRequired: false,
          auditLogging: true,
          dataRetention: 2592000000, // 30 days
          privacyLevel: 'INTERNAL',
          complianceFrameworks: []
        },
        qualityThresholds: agentData.configuration?.qualityThresholds || {
          minSuccessRate: 0.8,
          minQualityScore: 0.7,
          maxErrorRate: 0.1,
          minUserSatisfaction: 0.7,
          maxResponseTime: 30000,
          minReliability: 0.8
        },
        costSettings: agentData.configuration?.costSettings || {
          costPerRequest: 0.01,
          costPerToken: 0.0001,
          costPerMinute: 0.1,
          billingModel: 'PAY_PER_USE',
          budgetLimits: {
            dailyLimit: 100,
            monthlyLimit: 3000,
            perRequestLimit: 10,
            alertThresholds: [50, 80, 95]
          }
        },
        preferences: agentData.configuration?.preferences || {
          preferredRequestTypes: [],
          workingHours: {
            timezone: 'UTC',
            schedule: [],
            holidays: [],
            maintenanceWindows: []
          },
          loadBalancing: 'LEAST_LOADED',
          failoverStrategy: 'IMMEDIATE',
          scalingPolicy: {
            minInstances: 1,
            maxInstances: 10,
            targetUtilization: 0.7,
            scaleUpThreshold: 0.8,
            scaleDownThreshold: 0.3,
            cooldownPeriod: 300000
          }
        }
      },
      performance: {
        currentLoad: 0,
        queueLength: 0,
        averageResponseTime: 5000,
        recentSuccessRate: 1.0,
        recentQualityScore: 0.8,
        resourceUtilization: {
          cpu: 0,
          memory: 0,
          disk: 0,
          network: 0,
          tokens: 0
        },
        trends: [],
        benchmarks: []
      },
      availability: {
        isAvailable: true,
        estimatedWaitTime: 0,
        maintenanceSchedule: [],
        capacity: {
          current: 0,
          maximum: agentData.configuration?.maxConcurrentRequests || 10,
          reserved: 0,
          utilization: 0,
          projectedCapacity: []
        },
        healthStatus: {
          status: 'HEALTHY',
          lastHealthCheck: new Date(),
          healthScore: 1.0,
          issues: [],
          diagnostics: []
        }
      }
    };

    // Store agent in registry
    this.registry.agents.set(agentId, agent);

    // Update capability mappings
    this.updateCapabilityMappings(agent);

    // Initialize trust score
    await this.trustManager.initializeTrustScore(agentId);

    // Emit registration event
    this.emit('agentRegistered', {
      agentId,
      name: agent.name,
      capabilities: agent.capabilities.map(c => c.type)
    });

    logger.info('Agent registered', {
      agentId,
      name: agent.name,
      capabilitiesCount: agent.capabilities.length
    });

    return agent;
  }

  /**
   * Submit a request for processing
   */
  async submitRequest(requestData: Partial<MCPRequest>): Promise<MCPRequest> {
    const requestId = MCPUtils.generateRequestId();

    const request: MCPRequest = {
      id: requestId,
      type: requestData.type || CapabilityType.CODE_GENERATION,
      priority: requestData.priority || RequestPriority.MEDIUM,
      status: RequestStatus.PENDING,
      requesterInfo: requestData.requesterInfo || {
        id: 'anonymous',
        name: 'Anonymous User',
        role: 'USER',
        contactInfo: {},
        preferences: {
          preferredAgents: [],
          excludedAgents: [],
          qualityOverSpeed: false,
          costSensitive: false,
          notificationSettings: {
            onAssignment: true,
            onProgress: false,
            onCompletion: true,
            onFailure: true,
            channels: []
          }
        }
      },
      requirements: requestData.requirements || {
        capabilities: [{
          type: requestData.type || CapabilityType.CODE_GENERATION,
          minProficiencyLevel: ProficiencyLevel.INTERMEDIATE,
          required: true,
          weight: 1.0
        }],
        qualityThresholds: {
          minSuccessRate: 0.8,
          minQualityScore: 0.7,
          maxErrorRate: 0.1,
          minUserSatisfaction: 0.7,
          maxResponseTime: 30000,
          minReliability: 0.8
        },
        performanceRequirements: {
          maxResponseTime: 30000,
          minSuccessRate: 0.8,
          maxErrorRate: 0.1
        },
        securityRequirements: {
          encryptionRequired: false,
          auditLogging: true,
          dataClassification: 'INTERNAL',
          accessControls: [],
          complianceFrameworks: []
        },
        complianceRequirements: [],
        customRequirements: {}
      },
      constraints: requestData.constraints || {
        timeoutMs: MCP_CONSTANTS.DEFAULT_TIMEOUT,
        maxRetries: MCP_CONSTANTS.MAX_RETRIES,
        resourceLimits: {
          maxMemoryMB: 1024,
          maxCpuPercent: 80,
          maxDiskMB: 5120,
          maxNetworkMBps: 100,
          maxExecutionTime: 300000,
          maxTokens: 4096,
          maxRequestSize: MCP_CONSTANTS.MAX_REQUEST_SIZE,
          maxResponseSize: MCP_CONSTANTS.MAX_RESPONSE_SIZE
        }
      },
      context: requestData.context || {
        projectId: 'default',
        environment: 'development',
        relatedRequests: [],
        metadata: {}
      },
      payload: requestData.payload || {
        input: {},
        format: 'json'
      },
      routing: {
        strategy: 'BEST_FIT',
        candidateAgents: [],
        fallbackAgents: [],
        routingHistory: []
      },
      tracking: {
        progress: 0,
        milestones: [],
        events: [],
        metrics: {
          executionTime: 0,
          queueTime: 0,
          processingTime: 0,
          resourceUsage: {
            cpu: 0,
            memory: 0,
            disk: 0,
            network: 0,
            tokens: 0
          },
          qualityScore: 0,
          cost: 0,
          retryCount: 0,
          errorCount: 0
        }
      },
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // Validate request
    const validationErrors = MCPUtils.validateRequest(request);
    if (validationErrors.length > 0) {
      throw new MCPError('Request validation failed', 'VALIDATION_FAILED', requestId, validationErrors);
    }

    // Route request to appropriate agent
    const selectedAgent = await this.routeRequest(request);
    
    if (!selectedAgent) {
      throw new AgentSelectionError('No suitable agent found for request', requestId);
    }

    request.routing.selectedAgent = selectedAgent.id;
    request.status = RequestStatus.ASSIGNED;
    request.tracking.assignedAt = new Date();

    // Emit request submitted event
    this.emit('requestSubmitted', {
      requestId,
      type: request.type,
      priority: request.priority,
      selectedAgent: selectedAgent.id
    });

    logger.info('Request submitted', {
      requestId,
      type: request.type,
      priority: request.priority,
      selectedAgent: selectedAgent.id
    });

    return request;
  }

  /**
   * Process a request and generate response
   */
  async processRequest(requestId: string): Promise<MCPResponse> {
    // Mock implementation - would integrate with actual agent execution
    const startTime = Date.now();

    try {
      // Simulate processing
      await new Promise(resolve => setTimeout(resolve, Math.random() * 5000 + 1000));

      const response: MCPResponse = {
        requestId,
        status: RequestStatus.COMPLETED,
        agentId: 'mock_agent',
        result: {
          output: 'Mock response result',
          confidence: 0.85,
          metadata: {
            processingTime: Date.now() - startTime,
            tokensUsed: 150
          }
        },
        metadata: {
          agentVersion: '1.0.0',
          processingTime: Date.now() - startTime,
          resourcesUsed: {
            cpu: 25,
            memory: 128,
            disk: 10,
            network: 5,
            tokens: 150
          },
          confidence: 0.85,
          warnings: [],
          recommendations: []
        },
        quality: {
          overallScore: 0.85,
          dimensions: [
            { name: 'accuracy', score: 0.9, weight: 0.4, description: 'Result accuracy' },
            { name: 'completeness', score: 0.8, weight: 0.3, description: 'Response completeness' },
            { name: 'clarity', score: 0.85, weight: 0.3, description: 'Response clarity' }
          ],
          feedback: [],
          validation: []
        },
        performance: {
          executionTime: Date.now() - startTime,
          throughput: 1,
          resourceEfficiency: 0.8,
          scalability: 0.9,
          reliability: 0.95,
          benchmarkComparison: []
        },
        createdAt: new Date()
      };

      this.emit('requestCompleted', {
        requestId,
        agentId: response.agentId,
        executionTime: response.performance.executionTime,
        qualityScore: response.quality.overallScore
      });

      return response;

    } catch (error) {
      const response: MCPResponse = {
        requestId,
        status: RequestStatus.FAILED,
        agentId: 'mock_agent',
        error: {
          code: 'PROCESSING_ERROR',
          message: error.message,
          retryable: true,
          category: 'EXECUTION'
        },
        metadata: {
          agentVersion: '1.0.0',
          processingTime: Date.now() - startTime,
          resourcesUsed: {
            cpu: 0,
            memory: 0,
            disk: 0,
            network: 0,
            tokens: 0
          },
          confidence: 0,
          warnings: [error.message],
          recommendations: ['Retry with different parameters']
        },
        quality: {
          overallScore: 0,
          dimensions: [],
          feedback: [],
          validation: []
        },
        performance: {
          executionTime: Date.now() - startTime,
          throughput: 0,
          resourceEfficiency: 0,
          scalability: 0,
          reliability: 0,
          benchmarkComparison: []
        },
        createdAt: new Date()
      };

      this.emit('requestFailed', {
        requestId,
        agentId: response.agentId,
        error: response.error
      });

      return response;
    }
  }

  /**
   * Get agent by ID
   */
  getAgent(agentId: string): AgentProfile | undefined {
    return this.registry.agents.get(agentId);
  }

  /**
   * Get all agents
   */
  getAllAgents(): AgentProfile[] {
    return Array.from(this.registry.agents.values());
  }

  /**
   * Get agents by capability
   */
  getAgentsByCapability(capability: CapabilityType): AgentProfile[] {
    return Array.from(this.registry.agents.values()).filter(agent =>
      agent.capabilities.some(cap => cap.type === capability)
    );
  }

  /**
   * Update agent status
   */
  async updateAgentStatus(agentId: string, status: AgentStatus): Promise<void> {
    const agent = this.registry.agents.get(agentId);
    
    if (!agent) {
      throw new MCPError('Agent not found', 'AGENT_NOT_FOUND', undefined, { agentId });
    }

    agent.status = status;
    agent.updatedAt = new Date();

    this.emit('agentStatusChanged', { agentId, status });

    logger.info('Agent status updated', { agentId, status });
  }

  /**
   * Update agent capabilities
   */
  async updateAgentCapabilities(agentId: string, capabilities: AgentCapability[]): Promise<void> {
    const agent = this.registry.agents.get(agentId);
    
    if (!agent) {
      throw new MCPError('Agent not found', 'AGENT_NOT_FOUND', undefined, { agentId });
    }

    agent.capabilities = capabilities;
    agent.updatedAt = new Date();

    // Update capability mappings
    this.updateCapabilityMappings(agent);

    this.emit('agentCapabilitiesUpdated', { agentId, capabilities });

    logger.info('Agent capabilities updated', {
      agentId,
      capabilitiesCount: capabilities.length
    });
  }

  /**
   * Get capability registry statistics
   */
  getRegistryStatistics(): any {
    const agents = Array.from(this.registry.agents.values());

    return {
      totalAgents: agents.length,
      availableAgents: agents.filter(a => a.status === AgentStatus.AVAILABLE).length,
      busyAgents: agents.filter(a => a.status === AgentStatus.BUSY).length,
      offlineAgents: agents.filter(a => a.status === AgentStatus.OFFLINE).length,
      capabilityDistribution: this.getCapabilityDistribution(),
      averageTrustScore: this.calculateAverageTrustScore(),
      totalRequests: agents.reduce((sum, a) => sum + a.totalRequests, 0),
      successfulRequests: agents.reduce((sum, a) => sum + a.successfulRequests, 0),
      averageResponseTime: this.calculateAverageResponseTime(),
      systemLoad: this.calculateSystemLoad()
    };
  }

  /**
   * Private helper methods
   */
  private async routeRequest(request: MCPRequest): Promise<AgentProfile | null> {
    return await this.agentSelector.selectBestAgent(request);
  }

  private updateCapabilityMappings(agent: AgentProfile): void {
    for (const capability of agent.capabilities) {
      if (!this.registry.capabilities.has(capability.type)) {
        this.registry.capabilities.set(capability.type, []);
      }
      
      const capabilities = this.registry.capabilities.get(capability.type)!;
      const existingIndex = capabilities.findIndex(c => 
        c.type === capability.type && 
        capabilities === agent.capabilities
      );
      
      if (existingIndex >= 0) {
        capabilities[existingIndex] = capability;
      } else {
        capabilities.push(capability);
      }
    }
  }

  private getCapabilityDistribution(): Record<CapabilityType, number> {
    const distribution = {} as Record<CapabilityType, number>;
    
    for (const agent of this.registry.agents.values()) {
      for (const capability of agent.capabilities) {
        distribution[capability.type] = (distribution[capability.type] || 0) + 1;
      }
    }
    
    return distribution;
  }

  private calculateAverageTrustScore(): number {
    const agents = Array.from(this.registry.agents.values());
    if (agents.length === 0) return 0;
    
    const totalTrust = agents.reduce((sum, agent) => sum + agent.trustScore, 0);
    return totalTrust / agents.length;
  }

  private calculateAverageResponseTime(): number {
    const agents = Array.from(this.registry.agents.values());
    if (agents.length === 0) return 0;
    
    const totalTime = agents.reduce((sum, agent) => sum + agent.performance.averageResponseTime, 0);
    return totalTime / agents.length;
  }

  private calculateSystemLoad(): number {
    const agents = Array.from(this.registry.agents.values());
    if (agents.length === 0) return 0;
    
    const totalLoad = agents.reduce((sum, agent) => sum + agent.performance.currentLoad, 0);
    return totalLoad / agents.length;
  }

  private initializeRegistry(): CapabilityRegistry {
    return {
      agents: new Map(),
      capabilities: new Map(),
      trustScores: new Map(),
      performanceHistory: new Map(),
      availabilityMatrix: {
        agents: [],
        capabilities: [],
        matrix: [],
        lastUpdated: new Date()
      },
      routingRules: [],
      selectionCriteria: {
        weights: {
          trustScore: 0.25,
          performanceScore: 0.25,
          availabilityScore: 0.2,
          costScore: 0.1,
          capabilityMatch: 0.15,
          reputationScore: 0.05,
          loadBalance: 0.1
        },
        thresholds: {
          minTrustScore: 0.5,
          minPerformanceScore: 0.6,
          maxCost: 10,
          maxWaitTime: 60000,
          minCapabilityMatch: 0.7,
          minAvailability: 0.8
        },
        preferences: {
          preferHighTrust: true,
          preferLowCost: false,
          preferFastResponse: true,
          preferHighQuality: true,
          balanceLoad: true,
          diversifySelection: false
        },
        constraints: {
          excludedAgents: [],
          requiredCapabilities: [],
          geographicRestrictions: [],
          complianceRequirements: [],
          budgetLimits: {
            dailyLimit: 1000,
            monthlyLimit: 30000,
            perRequestLimit: 100,
            alertThresholds: [50, 80, 95]
          },
          timeConstraints: {
            maxWaitTime: 60000,
            maxExecutionTime: 300000,
            preferredTimeSlots: []
          }
        }
      }
    };
  }

  private setupEventHandlers(): void {
    this.on('requestCompleted', (event) => {
      this.performanceTracker.recordCompletion(event);
      this.trustManager.updateTrustScore(event.agentId, 'SUCCESS', event.qualityScore);
    });

    this.on('requestFailed', (event) => {
      this.performanceTracker.recordFailure(event);
      this.trustManager.updateTrustScore(event.agentId, 'FAILURE', 0);
    });
  }

  private startMonitoring(): void {
    // Start health monitoring
    setInterval(() => {
      this.availabilityMonitor.checkAgentHealth(Array.from(this.registry.agents.values()));
    }, MCP_CONSTANTS.HEALTH_CHECK_INTERVAL);

    // Start performance monitoring
    setInterval(() => {
      this.performanceTracker.collectMetrics(Array.from(this.registry.agents.values()));
    }, 60000); // Every minute
  }
}

// Helper classes
class AgentSelector {
  constructor(private registry: CapabilityRegistry) {}

  async selectBestAgent(request: MCPRequest): Promise<AgentProfile | null> {
    const candidates = this.findCandidateAgents(request);
    
    if (candidates.length === 0) {
      return null;
    }

    // Score and rank candidates
    const scoredCandidates = candidates.map(agent => ({
      agent,
      score: MCPUtils.calculateSelectionScore(agent, request, this.registry.selectionCriteria)
    }));

    // Sort by score (highest first)
    scoredCandidates.sort((a, b) => b.score - a.score);

    return scoredCandidates[0].agent;
  }

  private findCandidateAgents(request: MCPRequest): AgentProfile[] {
    return Array.from(this.registry.agents.values()).filter(agent => {
      // Check availability
      if (!agent.availability.isAvailable || agent.status !== AgentStatus.AVAILABLE) {
        return false;
      }

      // Check capability match
      const capabilityMatch = MCPUtils.calculateCapabilityMatch(
        request.requirements.capabilities,
        agent.capabilities
      );

      return capabilityMatch >= this.registry.selectionCriteria.thresholds.minCapabilityMatch;
    });
  }
}

class RequestRouter {
  constructor(private registry: CapabilityRegistry) {}

  async route(request: MCPRequest): Promise<string | null> {
    // Mock implementation
    return null;
  }
}

class PerformanceTracker {
  recordCompletion(event: any): void {
    // Mock implementation
    logger.debug('Recording completion', event);
  }

  recordFailure(event: any): void {
    // Mock implementation
    logger.debug('Recording failure', event);
  }

  collectMetrics(agents: AgentProfile[]): void {
    // Mock implementation
    logger.debug('Collecting metrics', { agentCount: agents.length });
  }
}

class TrustManager {
  async initializeTrustScore(agentId: string): Promise<void> {
    // Mock implementation
    logger.debug('Initializing trust score', { agentId });
  }

  updateTrustScore(agentId: string, event: string, score: number): void {
    // Mock implementation
    logger.debug('Updating trust score', { agentId, event, score });
  }
}

class AvailabilityMonitor {
  checkAgentHealth(agents: AgentProfile[]): void {
    // Mock implementation
    logger.debug('Checking agent health', { agentCount: agents.length });
  }
}

class QualityAssessor {
  assess(response: MCPResponse): void {
    // Mock implementation
    logger.debug('Assessing quality', { requestId: response.requestId });
  }
}

// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  username  String   @unique
  password  String
  role      String   @default("USER")
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  createdOrchestrators MetaOrchestrator[]
  auditLogs           AuditLog[]
  workflowExecutions  WorkflowExecution[]
  provenanceEntries   ProvenanceEntry[]

  @@map("users")
}

// SQLite doesn't support enums, using String instead
// UserRole: ADMIN, USER, VIEWER

model MetaOrchestrator {
  id          String   @id @default(cuid())
  name        String
  description String?
  version     String   @default("1.0.0")
  isActive    Boolean  @default(true)
  metadata    String?  // JSON as string for SQLite
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  creator           User                @relation(fields: [creatorId], references: [id])
  creatorId         String
  subOrchestrators  SubOrchestrator[]
  workflowTemplates WorkflowTemplate[]
  tunnels           Tunnel[]

  @@map("meta_orchestrators")
}

model SubOrchestrator {
  id          String   @id @default(cuid())
  name        String
  description String?
  domain      String
  isActive    Boolean  @default(true)
  metadata    String?  // JSON as string for SQLite
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  metaOrchestrator   MetaOrchestrator @relation(fields: [metaOrchestratorId], references: [id], onDelete: Cascade)
  metaOrchestratorId String
  agents             Agent[]
  tunnelsFrom        Tunnel[]         @relation("TunnelFrom")
  tunnelsTo          Tunnel[]         @relation("TunnelTo")

  @@map("sub_orchestrators")
}

model Agent {
  id           String    @id @default(cuid())
  agentId      String    @unique // e.g., "chatgpt-4o", "claude-opus"
  name         String
  description  String?
  vendor       String    // e.g., "OpenAI", "Anthropic", "Google"
  capabilities String    // JSON array as string for SQLite
  roles        String    // JSON array as string for SQLite
  isActive     Boolean   @default(true)
  metadata     String?   // JSON as string for SQLite
  fitnessScore Float     @default(0.0)
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt

  // Relations
  subOrchestrator     SubOrchestrator?   @relation(fields: [subOrchestratorId], references: [id])
  subOrchestratorId   String?
  workflowExecutions  WorkflowExecution[]
  evolutionVariants   EvolutionVariant[]
  tunnelsFrom         Tunnel[]           @relation("AgentTunnelFrom")
  tunnelsTo           Tunnel[]           @relation("AgentTunnelTo")
  signedEventPackets  SignedEventPacket[]

  @@map("agents")
}

model Tunnel {
  id          String     @id @default(cuid())
  name        String
  description String?
  tags        String     // JSON array as string for SQLite
  isActive    Boolean    @default(true)
  metadata    String?    // JSON as string for SQLite
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt

  // Relations - Flexible tunnel connections
  metaOrchestrator   MetaOrchestrator? @relation(fields: [metaOrchestratorId], references: [id])
  metaOrchestratorId String?

  fromSubOrchestrator   SubOrchestrator? @relation("TunnelFrom", fields: [fromSubOrchestratorId], references: [id])
  fromSubOrchestratorId String?
  toSubOrchestrator     SubOrchestrator? @relation("TunnelTo", fields: [toSubOrchestratorId], references: [id])
  toSubOrchestratorId   String?

  fromAgent   Agent?  @relation("AgentTunnelFrom", fields: [fromAgentId], references: [id])
  fromAgentId String?
  toAgent     Agent?  @relation("AgentTunnelTo", fields: [toAgentId], references: [id])
  toAgentId   String?

  @@map("tunnels")
}

model WorkflowTemplate {
  id          String   @id @default(cuid())
  name        String
  description String?
  stages      String   // JSON array as string for SQLite
  metadata    String?  // JSON as string for SQLite
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  metaOrchestrator   MetaOrchestrator    @relation(fields: [metaOrchestratorId], references: [id], onDelete: Cascade)
  metaOrchestratorId String
  executions         WorkflowExecution[]

  @@map("workflow_templates")
}

model WorkflowExecution {
  id        String            @id @default(cuid())
  status    String            @default("PENDING")
  startedAt DateTime          @default(now())
  endedAt   DateTime?
  result    String?           // JSON as string for SQLite
  metadata  String?           // JSON as string for SQLite

  // Relations
  template   WorkflowTemplate @relation(fields: [templateId], references: [id])
  templateId String
  executor   User             @relation(fields: [executorId], references: [id])
  executorId String
  agents     Agent[]
  signedEventPackets SignedEventPacket[]
  conflictDetections ConflictDetection[]
  workflowVersions   WorkflowVersion[]

  @@map("workflow_executions")
}

// SQLite doesn't support enums, using String instead
// WorkflowStatus: PENDING, RUNNING, COMPLETED, FAILED, CANCELLED

model EvolutionVariant {
  id           String   @id @default(cuid())
  generation   Int
  mutations    String   // JSON array as string for SQLite
  fitnessScore Float
  isPromoted   Boolean  @default(false)
  createdAt    DateTime @default(now())

  // Relations
  parentAgent Agent  @relation(fields: [agentId], references: [id], onDelete: Cascade)
  agentId     String

  @@map("evolution_variants")
}

model AuditLog {
  id        String   @id @default(cuid())
  action    String
  entityId  String
  entityType String
  oldValue  String?  // JSON as string for SQLite
  newValue  String?  // JSON as string for SQLite
  metadata  String?  // JSON as string for SQLite
  timestamp DateTime @default(now())

  // Relations
  user   User   @relation(fields: [userId], references: [id])
  userId String

  @@map("audit_logs")
}

model SharedContext {
  id        String   @id @default(cuid())
  key       String   @unique
  value     String   // JSON as string for SQLite
  tags      String   // JSON array as string for SQLite
  expiresAt DateTime?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("shared_contexts")
}

// Core Gap 1: Signed Event Packets
model SignedEventPacket {
  id              String   @id @default(cuid())
  eventType       String   // CODE_GENERATION, CODE_MODIFICATION, TEST_EXECUTION, MERGE_OPERATION
  workflowId      String?  // Links to workflow execution
  agentId         String?  // Links to agent
  codeHash        String   // SHA-256 hash of code content
  codeDiff        String?  // Git-style diff for modifications
  testMetrics     String?  // JSON test results and performance metrics
  agentMetadata   String   // JSON agent information and capabilities
  trustSignature  String   // Cryptographic signature for verification
  timestamp       DateTime @default(now())
  isVerified      Boolean  @default(false)
  verificationLog String?  // JSON verification audit trail

  // Relations
  agent           Agent?   @relation(fields: [agentId], references: [id])
  workflowExecution WorkflowExecution? @relation(fields: [workflowId], references: [id])
  provenanceEntries ProvenanceEntry[]

  @@map("signed_event_packets")
}

// Core Gap 2: Immutable Provenance Ledger
model ProvenanceEntry {
  id                String   @id @default(cuid())
  blockHash         String   @unique // Blockchain-style hash linking to previous entry
  previousHash      String?  // Hash of previous provenance entry
  action            String   // CREATE, MODIFY, TEST, MERGE, DEPLOY
  entityType        String   // CODE, WORKFLOW, AGENT, CONFIGURATION
  entityId          String   // ID of the entity being tracked
  beforeState       String?  // JSON snapshot before change
  afterState        String?  // JSON snapshot after change
  changeMetadata    String   // JSON metadata about the change
  performedBy       String   // User or agent ID
  timestamp         DateTime @default(now())
  isImmutable       Boolean  @default(true)

  // Relations
  signedEventPacket SignedEventPacket? @relation(fields: [eventPacketId], references: [id])
  eventPacketId     String?
  user              User?              @relation(fields: [performedBy], references: [id])

  @@map("provenance_entries")
}

// Core Gap 3: Privacy/Redaction Layer
model RedactionRule {
  id          String   @id @default(cuid())
  name        String
  pattern     String   // Regex pattern for sensitive data
  replacement String   // Replacement text (e.g., "[REDACTED]")
  entityTypes String   // JSON array of entity types to apply to
  isActive    Boolean  @default(true)
  priority    Int      @default(0) // Higher priority rules applied first
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("redaction_rules")
}

// Core Gap 4: Real-Time Conflict Detection
model ConflictDetection {
  id              String   @id @default(cuid())
  conflictType    String   // FUNCTION_OVERLAP, CLASS_CONFLICT, DEPENDENCY_CONFLICT
  severity        String   // LOW, MEDIUM, HIGH, CRITICAL
  status          String   // DETECTED, ANALYZING, RESOLVED, IGNORED
  description     String
  affectedEntities String  // JSON array of affected code entities
  resolutionStrategy String? // AUTO_MERGE, MANUAL_REVIEW, PRIORITY_BASED
  resolvedBy      String?  // User or agent ID that resolved conflict
  resolvedAt      DateTime?
  metadata        String?  // JSON additional conflict data
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  workflowExecution WorkflowExecution? @relation(fields: [workflowId], references: [id])
  workflowId        String?

  @@map("conflict_detections")
}

// Core Gap 5: Versioned Workflow Management
model WorkflowVersion {
  id              String   @id @default(cuid())
  workflowId      String   // Links to WorkflowExecution
  versionNumber   String   // Semantic versioning (e.g., "1.2.3")
  versionHash     String   @unique // Hash of workflow configuration
  configuration   String   // JSON workflow configuration
  agentAssignments String  // JSON agent assignments and roles
  testSuites      String   // JSON test suite configurations
  outcomeMetrics  String?  // JSON performance and outcome metrics
  isActive        Boolean  @default(true)
  parentVersionId String?  // For branching/forking workflows
  createdAt       DateTime @default(now())

  // Relations
  workflowExecution WorkflowExecution @relation(fields: [workflowId], references: [id])
  parentVersion     WorkflowVersion?  @relation("WorkflowVersionHierarchy", fields: [parentVersionId], references: [id])
  childVersions     WorkflowVersion[] @relation("WorkflowVersionHierarchy")
  abTestResults     ABTestResult[]

  @@map("workflow_versions")
}

// Core Gap 6: A/B Testing and Rollback Support
model ABTestResult {
  id              String   @id @default(cuid())
  testName        String
  versionA        String   // Version ID for variant A
  versionB        String   // Version ID for variant B
  testMetrics     String   // JSON test results comparison
  winnerVersion   String?  // ID of winning version
  confidenceLevel Float?   // Statistical confidence in results
  testDuration    Int      // Duration in minutes
  sampleSize      Int      // Number of test runs
  status          String   // RUNNING, COMPLETED, CANCELLED
  startedAt       DateTime @default(now())
  completedAt     DateTime?

  // Relations
  workflowVersion WorkflowVersion @relation(fields: [versionId], references: [id])
  versionId       String

  @@map("ab_test_results")
}

// Enhanced Conflict Resolution Models
model ConflictResolution {
  id                String   @id @default(cuid())
  conflictType      String   // FUNCTION_OVERLAP, CLASS_OVERLAP, VARIABLE_CONFLICT, etc.
  severity          String   // LOW, MEDIUM, HIGH, CRITICAL
  status            String   // DETECTED, ANALYZING, RESOLVING, RESOLVED, FAILED
  detectedAt        DateTime @default(now())
  resolvedAt        DateTime?
  involvedAgents    String   // JSON array of agent IDs
  conflictingElements String // JSON array of conflicting code elements
  contextInfo       String   // JSON context information
  resolutionStrategy String? // TRUST_BASED, MERGE_INTELLIGENT, PERFORMANCE_BASED, etc.
  resolutionResult  String?  // JSON resolution result
  metadata          String   // JSON additional metadata

  @@index([conflictType])
  @@index([severity])
  @@index([status])
  @@index([detectedAt])
  @@map("conflict_resolutions")
}

// Enhanced Versioned Workflow Models
model VersionedWorkflowManagement {
  id              String   @id @default(cuid())
  name            String
  description     String
  type            String   // CODE_GENERATION, CODE_REVIEW, TESTING, etc.
  version         String
  status          String   // DRAFT, ACTIVE, PAUSED, COMPLETED, FAILED, etc.
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  createdBy       String
  ownerId         String
  parentWorkflowId String?
  branchName      String?
  tags            String   // JSON array
  metadata        String   // JSON metadata
  configuration   String   // JSON configuration
  tasks           String   // JSON array of tasks
  dependencies    String   // JSON array of dependencies
  testSuites      String   // JSON array of test suites
  experiments     String   // JSON array of experiments
  metrics         String   // JSON metrics
  rollbackPlan    String   // JSON rollback plan
  approvals       String   // JSON array of approvals

  @@index([type])
  @@index([status])
  @@index([createdBy])
  @@index([ownerId])
  @@index([createdAt])
  @@map("versioned_workflow_management")
}

// MCP Capability Registry Models
model MCPAgentProfile {
  id                String   @id @default(cuid())
  name              String
  description       String
  version           String
  status            String   // AVAILABLE, BUSY, OFFLINE, MAINTENANCE, ERROR
  capabilities      String   // JSON array of capabilities
  trustScore        Float
  reputation        Float
  totalRequests     Int      @default(0)
  successfulRequests Int     @default(0)
  averageRating     Float    @default(0)
  lastActive        DateTime @default(now())
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  metadata          String   // JSON metadata
  configuration     String   // JSON configuration
  performance       String   // JSON performance metrics
  availability      String   // JSON availability information

  @@index([status])
  @@index([trustScore])
  @@index([reputation])
  @@index([lastActive])
  @@map("mcp_agent_profiles")
}

model MCPRequestManagement {
  id              String   @id @default(cuid())
  type            String   // Capability type
  priority        String   // LOW, MEDIUM, HIGH, URGENT, CRITICAL
  status          String   // PENDING, ASSIGNED, IN_PROGRESS, COMPLETED, FAILED, etc.
  requesterInfo   String   // JSON requester information
  requirements    String   // JSON requirements
  constraints     String   // JSON constraints
  context         String   // JSON context
  payload         String   // JSON payload
  routing         String   // JSON routing information
  tracking        String   // JSON tracking information
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@index([type])
  @@index([priority])
  @@index([status])
  @@index([createdAt])
  @@map("mcp_request_management")
}

model MCPResponseManagement {
  id          String   @id @default(cuid())
  requestId   String
  status      String
  agentId     String
  result      String?  // JSON result
  error       String?  // JSON error information
  metadata    String   // JSON metadata
  quality     String   // JSON quality assessment
  performance String   // JSON performance metrics
  createdAt   DateTime @default(now())

  @@index([requestId])
  @@index([agentId])
  @@index([status])
  @@index([createdAt])
  @@map("mcp_response_management")
}

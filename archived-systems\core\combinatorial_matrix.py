"""
Combinatorial Agent Assignment Matrix System

Implements systematic generation and testing of all possible agent-role-orchestrator
combinations to discover optimal workflows and performance patterns.

Mathematical Combinations:
- With repetition: n^r (agents can fill multiple roles)
- Without repetition: n!/(n-r)! (unique agent per role)
- Including orchestrators: o × n^r (orchestrator × agent combinations)

Key Features:
- Exhaustive combination generation
- Empirical performance testing
- Learning and optimization algorithms
- Dynamic workflow adaptation
"""

import logging
import itertools
import asyncio
from typing import Dict, Any, List, Optional, Tuple, Iterator
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import json
import math

from .agent_registry import AgentRegistry, AgentRole, AgentStatus
from .workflow_executor import WorkflowExecutor, TaskResult


class CombinationType(Enum):
    """Types of combinatorial assignments."""
    WITH_REPETITION = "with_repetition"      # Agents can fill multiple roles
    WITHOUT_REPETITION = "without_repetition"  # Unique agent per role
    HYBRID = "hybrid"                        # Mix of both approaches


class OptimizationGoal(Enum):
    """Optimization goals for combination selection."""
    PERFORMANCE = "performance"              # Maximize task success rate
    SPEED = "speed"                         # Minimize execution time
    QUALITY = "quality"                     # Maximize output quality
    COST = "cost"                          # Minimize resource usage
    BALANCED = "balanced"                   # Balance all factors


@dataclass
class AgentRoleCombination:
    """Represents a specific agent-role combination."""
    combination_id: str
    agents: Dict[AgentRole, str]  # role -> agent_id mapping
    orchestrator: str
    combination_type: CombinationType
    created_at: datetime = field(default_factory=datetime.utcnow)
    
    def __hash__(self):
        """Make combination hashable for set operations."""
        return hash((
            tuple(sorted(self.agents.items())),
            self.orchestrator,
            self.combination_type.value
        ))


@dataclass
class CombinationPerformance:
    """Performance metrics for a specific combination."""
    combination_id: str
    total_tests: int = 0
    successful_tests: int = 0
    failed_tests: int = 0
    average_execution_time: float = 0.0
    average_quality_score: float = 0.0
    average_cost: float = 0.0
    success_rate: float = 0.0
    performance_trend: List[float] = field(default_factory=list)
    task_type_performance: Dict[str, Dict[str, float]] = field(default_factory=dict)
    last_tested: Optional[datetime] = None
    confidence_score: float = 0.0  # Confidence in performance metrics


class CombinatorialMatrix:
    """
    Systematic generation and testing of agent-role-orchestrator combinations.
    
    Generates all possible combinations, tests them empirically, and learns
    optimal patterns for different types of tasks and contexts.
    """
    
    def __init__(self, agent_registry: AgentRegistry, workflow_executor: WorkflowExecutor, 
                 config: Dict[str, Any] = None):
        self.agent_registry = agent_registry
        self.workflow_executor = workflow_executor
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # Combination storage
        self.combinations: Dict[str, AgentRoleCombination] = {}
        self.performance_data: Dict[str, CombinationPerformance] = {}
        self.optimal_combinations: Dict[str, List[str]] = {}  # task_type -> combination_ids
        
        # Configuration
        self.max_combinations_to_test = self.config.get('max_combinations_to_test', 1000)
        self.min_tests_per_combination = self.config.get('min_tests_per_combination', 3)
        self.max_tests_per_combination = self.config.get('max_tests_per_combination', 20)
        self.learning_enabled = self.config.get('learning_enabled', True)
        self.parallel_testing = self.config.get('parallel_testing', True)
        self.max_parallel_tests = self.config.get('max_parallel_tests', 5)
        
        # Available orchestrators
        self.orchestrators = self.config.get('orchestrators', ['default', 'airflow', 'jenkins'])
        
        # Performance tracking
        self.matrix_stats = {
            'total_combinations_generated': 0,
            'total_combinations_tested': 0,
            'total_tests_executed': 0,
            'optimal_combinations_found': 0,
            'learning_iterations': 0,
            'last_optimization': None
        }
        
        # Generate initial combinations
        self._generate_initial_combinations()
    
    def _generate_initial_combinations(self) -> None:
        """Generate initial set of combinations to test."""
        try:
            self.logger.info("Generating initial agent-role combinations...")
            
            # Get available agents and roles
            available_agents = [agent['agent_id'] for agent in self.agent_registry.get_available_agents()]
            available_roles = list(AgentRole)
            
            if not available_agents:
                self.logger.warning("No available agents found for combination generation")
                return
            
            # Generate combinations for each orchestrator
            for orchestrator in self.orchestrators:
                # Generate with repetition (agents can fill multiple roles)
                with_rep_combinations = self._generate_with_repetition(
                    available_agents, available_roles, orchestrator
                )
                
                # Generate without repetition (unique agent per role)
                without_rep_combinations = self._generate_without_repetition(
                    available_agents, available_roles, orchestrator
                )
                
                # Store combinations
                for combo in with_rep_combinations:
                    self.combinations[combo.combination_id] = combo
                    self.performance_data[combo.combination_id] = CombinationPerformance(
                        combination_id=combo.combination_id
                    )
                
                for combo in without_rep_combinations:
                    self.combinations[combo.combination_id] = combo
                    self.performance_data[combo.combination_id] = CombinationPerformance(
                        combination_id=combo.combination_id
                    )
            
            self.matrix_stats['total_combinations_generated'] = len(self.combinations)
            self.logger.info(f"Generated {len(self.combinations)} total combinations")
            
        except Exception as e:
            self.logger.error(f"Error generating initial combinations: {str(e)}")
    
    def _generate_with_repetition(self, agents: List[str], roles: List[AgentRole], 
                                 orchestrator: str) -> List[AgentRoleCombination]:
        """Generate combinations with repetition (n^r)."""
        combinations = []
        
        # Limit to reasonable subset for testing
        max_role_combinations = min(len(roles), 4)  # Test up to 4 roles at once
        
        for num_roles in range(1, max_role_combinations + 1):
            for role_subset in itertools.combinations(roles, num_roles):
                # For each role, any agent can be assigned (with repetition)
                for agent_assignment in itertools.product(agents, repeat=len(role_subset)):
                    role_agent_mapping = dict(zip(role_subset, agent_assignment))
                    
                    # Validate that agents support their assigned roles
                    if self._validate_role_assignments(role_agent_mapping):
                        combination_id = self._generate_combination_id(
                            role_agent_mapping, orchestrator, CombinationType.WITH_REPETITION
                        )
                        
                        combination = AgentRoleCombination(
                            combination_id=combination_id,
                            agents=role_agent_mapping,
                            orchestrator=orchestrator,
                            combination_type=CombinationType.WITH_REPETITION
                        )
                        
                        combinations.append(combination)
                        
                        # Limit total combinations to prevent explosion
                        if len(combinations) >= self.max_combinations_to_test // len(self.orchestrators):
                            return combinations
        
        return combinations
    
    def _generate_without_repetition(self, agents: List[str], roles: List[AgentRole], 
                                   orchestrator: str) -> List[AgentRoleCombination]:
        """Generate combinations without repetition (n!/(n-r)!)."""
        combinations = []
        
        # Limit to reasonable subset
        max_role_combinations = min(len(roles), min(len(agents), 4))
        
        for num_roles in range(1, max_role_combinations + 1):
            for role_subset in itertools.combinations(roles, num_roles):
                # Each role gets a unique agent (no repetition)
                for agent_subset in itertools.permutations(agents, len(role_subset)):
                    role_agent_mapping = dict(zip(role_subset, agent_subset))
                    
                    # Validate that agents support their assigned roles
                    if self._validate_role_assignments(role_agent_mapping):
                        combination_id = self._generate_combination_id(
                            role_agent_mapping, orchestrator, CombinationType.WITHOUT_REPETITION
                        )
                        
                        combination = AgentRoleCombination(
                            combination_id=combination_id,
                            agents=role_agent_mapping,
                            orchestrator=orchestrator,
                            combination_type=CombinationType.WITHOUT_REPETITION
                        )
                        
                        combinations.append(combination)
                        
                        # Limit total combinations
                        if len(combinations) >= self.max_combinations_to_test // len(self.orchestrators):
                            return combinations
        
        return combinations
    
    def _validate_role_assignments(self, role_agent_mapping: Dict[AgentRole, str]) -> bool:
        """Validate that agents can fulfill their assigned roles."""
        for role, agent_id in role_agent_mapping.items():
            agent_info = self.agent_registry.get_agent_info(agent_id)
            if not agent_info:
                return False
            
            supported_roles = [AgentRole(role_str) for role_str in agent_info['supported_roles']]
            if role not in supported_roles:
                return False
        
        return True
    
    def _generate_combination_id(self, role_agent_mapping: Dict[AgentRole, str], 
                                orchestrator: str, combination_type: CombinationType) -> str:
        """Generate unique ID for a combination."""
        # Sort roles for consistent ID generation
        sorted_mapping = sorted(role_agent_mapping.items(), key=lambda x: x[0].value)
        mapping_str = "_".join([f"{role.value}:{agent}" for role, agent in sorted_mapping])
        
        return f"{orchestrator}_{combination_type.value}_{hash(mapping_str) % 1000000}"
    
    async def test_combination(self, combination_id: str, task_description: str, 
                             task_type: str, context: Dict[str, Any] = None) -> TaskResult:
        """Test a specific combination with a given task."""
        if combination_id not in self.combinations:
            raise ValueError(f"Combination {combination_id} not found")
        
        combination = self.combinations[combination_id]
        performance = self.performance_data[combination_id]
        
        try:
            start_time = datetime.utcnow()
            
            # Execute task with this combination
            result = await self.workflow_executor.execute_with_combination(
                combination=combination,
                task_description=task_description,
                task_type=task_type,
                context=context or {}
            )
            
            execution_time = (datetime.utcnow() - start_time).total_seconds()
            
            # Update performance metrics
            self._update_combination_performance(
                combination_id, result, execution_time, task_type
            )
            
            self.matrix_stats['total_tests_executed'] += 1
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error testing combination {combination_id}: {str(e)}")
            
            # Record failed test
            performance.total_tests += 1
            performance.failed_tests += 1
            performance.last_tested = datetime.utcnow()
            
            return TaskResult(
                task_id=f"test_{combination_id}",
                success=False,
                error_message=str(e),
                metadata={'combination_id': combination_id, 'test_error': True}
            )
    
    def _update_combination_performance(self, combination_id: str, result: TaskResult, 
                                      execution_time: float, task_type: str) -> None:
        """Update performance metrics for a combination."""
        performance = self.performance_data[combination_id]
        
        performance.total_tests += 1
        performance.last_tested = datetime.utcnow()
        
        if result.success:
            performance.successful_tests += 1
        else:
            performance.failed_tests += 1
        
        # Update success rate
        performance.success_rate = performance.successful_tests / performance.total_tests
        
        # Update average execution time
        total_time = (performance.average_execution_time * (performance.total_tests - 1) + execution_time)
        performance.average_execution_time = total_time / performance.total_tests
        
        # Update quality score if available
        quality_score = result.metadata.get('quality_score', 0.5 if result.success else 0.0)
        total_quality = (performance.average_quality_score * (performance.total_tests - 1) + quality_score)
        performance.average_quality_score = total_quality / performance.total_tests
        
        # Update performance trend (keep last 10 results)
        performance.performance_trend.append(quality_score)
        if len(performance.performance_trend) > 10:
            performance.performance_trend = performance.performance_trend[-10:]
        
        # Update task-specific performance
        if task_type not in performance.task_type_performance:
            performance.task_type_performance[task_type] = {
                'tests': 0, 'success_rate': 0.0, 'avg_quality': 0.0
            }
        
        task_perf = performance.task_type_performance[task_type]
        task_perf['tests'] += 1
        
        if result.success:
            task_success_count = task_perf['success_rate'] * (task_perf['tests'] - 1) + 1
        else:
            task_success_count = task_perf['success_rate'] * (task_perf['tests'] - 1)
        
        task_perf['success_rate'] = task_success_count / task_perf['tests']
        
        task_quality_total = task_perf['avg_quality'] * (task_perf['tests'] - 1) + quality_score
        task_perf['avg_quality'] = task_quality_total / task_perf['tests']
        
        # Update confidence score based on number of tests
        performance.confidence_score = min(1.0, performance.total_tests / self.min_tests_per_combination)
    
    async def find_optimal_combinations(self, task_type: str, 
                                      optimization_goal: OptimizationGoal = OptimizationGoal.BALANCED,
                                      min_confidence: float = 0.7) -> List[str]:
        """Find optimal combinations for a specific task type."""
        try:
            # Filter combinations with sufficient confidence
            candidate_combinations = [
                combo_id for combo_id, performance in self.performance_data.items()
                if (performance.confidence_score >= min_confidence and
                    task_type in performance.task_type_performance)
            ]
            
            if not candidate_combinations:
                self.logger.warning(f"No combinations with sufficient confidence for task type: {task_type}")
                return []
            
            # Score combinations based on optimization goal
            scored_combinations = []
            
            for combo_id in candidate_combinations:
                performance = self.performance_data[combo_id]
                task_perf = performance.task_type_performance[task_type]
                
                score = self._calculate_optimization_score(
                    performance, task_perf, optimization_goal
                )
                
                scored_combinations.append((combo_id, score))
            
            # Sort by score and return top combinations
            scored_combinations.sort(key=lambda x: x[1], reverse=True)
            
            # Return top 5 combinations
            optimal_combo_ids = [combo_id for combo_id, _ in scored_combinations[:5]]
            
            # Store optimal combinations
            self.optimal_combinations[task_type] = optimal_combo_ids
            self.matrix_stats['optimal_combinations_found'] += 1
            
            return optimal_combo_ids
            
        except Exception as e:
            self.logger.error(f"Error finding optimal combinations: {str(e)}")
            return []
    
    def _calculate_optimization_score(self, performance: CombinationPerformance, 
                                    task_performance: Dict[str, float], 
                                    goal: OptimizationGoal) -> float:
        """Calculate optimization score based on goal."""
        if goal == OptimizationGoal.PERFORMANCE:
            return task_performance['success_rate']
        
        elif goal == OptimizationGoal.SPEED:
            # Lower execution time is better
            return 1.0 / (1.0 + performance.average_execution_time)
        
        elif goal == OptimizationGoal.QUALITY:
            return task_performance['avg_quality']
        
        elif goal == OptimizationGoal.COST:
            # Lower cost is better (simplified cost model)
            estimated_cost = performance.average_execution_time * 0.1  # Simple cost estimate
            return 1.0 / (1.0 + estimated_cost)
        
        else:  # BALANCED
            # Weighted combination of all factors
            success_weight = 0.4
            quality_weight = 0.3
            speed_weight = 0.2
            cost_weight = 0.1
            
            success_score = task_performance['success_rate']
            quality_score = task_performance['avg_quality']
            speed_score = 1.0 / (1.0 + performance.average_execution_time)
            cost_score = 1.0 / (1.0 + performance.average_execution_time * 0.1)
            
            return (success_score * success_weight +
                   quality_score * quality_weight +
                   speed_score * speed_weight +
                   cost_score * cost_weight)
    
    async def run_comprehensive_testing(self, test_tasks: List[Dict[str, Any]], 
                                      max_combinations: int = None) -> Dict[str, Any]:
        """Run comprehensive testing across multiple combinations and tasks."""
        max_combinations = max_combinations or min(50, len(self.combinations))
        
        # Select combinations to test (prioritize untested or low-confidence ones)
        combinations_to_test = self._select_combinations_for_testing(max_combinations)
        
        test_results = {
            'total_combinations_tested': 0,
            'total_tests_executed': 0,
            'successful_tests': 0,
            'failed_tests': 0,
            'task_type_results': {},
            'optimal_combinations_by_task': {},
            'testing_duration': 0.0
        }
        
        start_time = datetime.utcnow()
        
        try:
            if self.parallel_testing:
                # Run tests in parallel
                test_results = await self._run_parallel_testing(
                    combinations_to_test, test_tasks, test_results
                )
            else:
                # Run tests sequentially
                test_results = await self._run_sequential_testing(
                    combinations_to_test, test_tasks, test_results
                )
            
            # Find optimal combinations for each task type
            task_types = set(task['task_type'] for task in test_tasks)
            for task_type in task_types:
                optimal_combos = await self.find_optimal_combinations(task_type)
                test_results['optimal_combinations_by_task'][task_type] = optimal_combos
            
            test_results['testing_duration'] = (datetime.utcnow() - start_time).total_seconds()
            
            self.matrix_stats['total_combinations_tested'] = len(combinations_to_test)
            self.matrix_stats['last_optimization'] = datetime.utcnow().isoformat()
            
            return test_results
            
        except Exception as e:
            self.logger.error(f"Error in comprehensive testing: {str(e)}")
            test_results['error'] = str(e)
            return test_results
    
    def _select_combinations_for_testing(self, max_combinations: int) -> List[str]:
        """Select combinations for testing based on priority."""
        # Priority: untested > low confidence > random selection
        untested = [
            combo_id for combo_id, perf in self.performance_data.items()
            if perf.total_tests == 0
        ]
        
        low_confidence = [
            combo_id for combo_id, perf in self.performance_data.items()
            if 0 < perf.total_tests < self.min_tests_per_combination
        ]
        
        tested = [
            combo_id for combo_id, perf in self.performance_data.items()
            if perf.total_tests >= self.min_tests_per_combination
        ]
        
        # Select combinations in priority order
        selected = []
        
        # Add untested combinations first
        selected.extend(untested[:max_combinations])
        
        # Add low confidence combinations
        remaining = max_combinations - len(selected)
        if remaining > 0:
            selected.extend(low_confidence[:remaining])
        
        # Add some tested combinations for comparison
        remaining = max_combinations - len(selected)
        if remaining > 0:
            selected.extend(tested[:remaining])
        
        return selected[:max_combinations]
    
    async def _run_parallel_testing(self, combinations_to_test: List[str], 
                                   test_tasks: List[Dict[str, Any]], 
                                   test_results: Dict[str, Any]) -> Dict[str, Any]:
        """Run tests in parallel for faster execution."""
        semaphore = asyncio.Semaphore(self.max_parallel_tests)
        
        async def test_combination_with_semaphore(combo_id: str, task: Dict[str, Any]):
            async with semaphore:
                return await self.test_combination(
                    combo_id, task['description'], task['task_type'], task.get('context', {})
                )
        
        # Create all test tasks
        all_tests = []
        for combo_id in combinations_to_test:
            for task in test_tasks:
                all_tests.append((combo_id, task))
        
        # Execute tests in parallel
        test_coroutines = [
            test_combination_with_semaphore(combo_id, task)
            for combo_id, task in all_tests
        ]
        
        results = await asyncio.gather(*test_coroutines, return_exceptions=True)
        
        # Process results
        for i, result in enumerate(results):
            combo_id, task = all_tests[i]
            
            if isinstance(result, Exception):
                test_results['failed_tests'] += 1
                self.logger.error(f"Test failed for {combo_id}: {str(result)}")
            elif result.success:
                test_results['successful_tests'] += 1
            else:
                test_results['failed_tests'] += 1
            
            test_results['total_tests_executed'] += 1
        
        test_results['total_combinations_tested'] = len(combinations_to_test)
        
        return test_results
    
    async def _run_sequential_testing(self, combinations_to_test: List[str], 
                                     test_tasks: List[Dict[str, Any]], 
                                     test_results: Dict[str, Any]) -> Dict[str, Any]:
        """Run tests sequentially."""
        for combo_id in combinations_to_test:
            for task in test_tasks:
                try:
                    result = await self.test_combination(
                        combo_id, task['description'], task['task_type'], task.get('context', {})
                    )
                    
                    if result.success:
                        test_results['successful_tests'] += 1
                    else:
                        test_results['failed_tests'] += 1
                    
                    test_results['total_tests_executed'] += 1
                    
                except Exception as e:
                    self.logger.error(f"Test failed for {combo_id}: {str(e)}")
                    test_results['failed_tests'] += 1
                    test_results['total_tests_executed'] += 1
        
        test_results['total_combinations_tested'] = len(combinations_to_test)
        
        return test_results
    
    def get_combination_info(self, combination_id: str) -> Optional[Dict[str, Any]]:
        """Get detailed information about a specific combination."""
        if combination_id not in self.combinations:
            return None
        
        combination = self.combinations[combination_id]
        performance = self.performance_data[combination_id]
        
        return {
            'combination_id': combination_id,
            'agents': {role.value: agent_id for role, agent_id in combination.agents.items()},
            'orchestrator': combination.orchestrator,
            'combination_type': combination.combination_type.value,
            'created_at': combination.created_at.isoformat(),
            'performance': {
                'total_tests': performance.total_tests,
                'success_rate': performance.success_rate,
                'average_execution_time': performance.average_execution_time,
                'average_quality_score': performance.average_quality_score,
                'confidence_score': performance.confidence_score,
                'performance_trend': performance.performance_trend,
                'task_type_performance': performance.task_type_performance,
                'last_tested': performance.last_tested.isoformat() if performance.last_tested else None
            }
        }
    
    def get_matrix_statistics(self) -> Dict[str, Any]:
        """Get comprehensive matrix statistics."""
        total_combinations = len(self.combinations)
        tested_combinations = sum(1 for perf in self.performance_data.values() if perf.total_tests > 0)
        
        confidence_distribution = {
            'high_confidence': sum(1 for perf in self.performance_data.values() if perf.confidence_score >= 0.8),
            'medium_confidence': sum(1 for perf in self.performance_data.values() if 0.5 <= perf.confidence_score < 0.8),
            'low_confidence': sum(1 for perf in self.performance_data.values() if 0 < perf.confidence_score < 0.5),
            'untested': sum(1 for perf in self.performance_data.values() if perf.confidence_score == 0)
        }
        
        return {
            'matrix_stats': self.matrix_stats.copy(),
            'total_combinations': total_combinations,
            'tested_combinations': tested_combinations,
            'confidence_distribution': confidence_distribution,
            'optimal_combinations_by_task': self.optimal_combinations.copy(),
            'average_success_rate': sum(perf.success_rate for perf in self.performance_data.values()) / total_combinations if total_combinations > 0 else 0.0,
            'configuration': {
                'max_combinations_to_test': self.max_combinations_to_test,
                'min_tests_per_combination': self.min_tests_per_combination,
                'learning_enabled': self.learning_enabled,
                'parallel_testing': self.parallel_testing
            },
            'last_updated': datetime.utcnow().isoformat()
        }

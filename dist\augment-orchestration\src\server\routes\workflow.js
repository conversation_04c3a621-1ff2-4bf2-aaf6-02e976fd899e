"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.workflowRoutes = void 0;
const express_1 = require("express");
const client_1 = require("@prisma/client");
const zod_1 = require("zod");
const errorHandler_1 = require("../middleware/errorHandler");
const EventBus_1 = require("../services/EventBus");
const WorkflowExecutionEngine_1 = require("../services/WorkflowExecutionEngine");
const router = (0, express_1.Router)();
exports.workflowRoutes = router;
const prisma = new client_1.PrismaClient();
const eventBus = new EventBus_1.EventBus();
const executionEngine = new WorkflowExecutionEngine_1.WorkflowExecutionEngine();
// Validation schemas
const createTemplateSchema = zod_1.z.object({
    name: zod_1.z.string().min(1, 'Name is required').max(100, 'Name too long'),
    description: zod_1.z.string().optional(),
    stages: zod_1.z.array(zod_1.z.object({
        id: zod_1.z.string(),
        name: zod_1.z.string(),
        description: zod_1.z.string().optional(),
        requiredRoles: zod_1.z.array(zod_1.z.string()),
        dependencies: zod_1.z.array(zod_1.z.string()).default([]),
        timeout: zod_1.z.number().min(1).max(3600).default(300), // 5 minutes default
        retryCount: zod_1.z.number().min(0).max(5).default(0),
        metadata: zod_1.z.record(zod_1.z.any()).optional(),
    })).min(1, 'At least one stage required'),
    metadata: zod_1.z.record(zod_1.z.any()).optional(),
    isActive: zod_1.z.boolean().default(true),
});
const executeWorkflowSchema = zod_1.z.object({
    templateId: zod_1.z.string().min(1, 'Template ID is required'),
    input: zod_1.z.record(zod_1.z.any()).optional(),
    priority: zod_1.z.number().min(1).max(10).default(5),
    metadata: zod_1.z.record(zod_1.z.any()).optional(),
});
const updateExecutionSchema = zod_1.z.object({
    status: zod_1.z.enum(['PENDING', 'RUNNING', 'COMPLETED', 'FAILED', 'CANCELLED']).optional(),
    currentStage: zod_1.z.string().optional(),
    output: zod_1.z.record(zod_1.z.any()).optional(),
    error: zod_1.z.string().optional(),
    metadata: zod_1.z.record(zod_1.z.any()).optional(),
});
// Get workflow templates with filtering
router.get('/templates', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const search = req.query.search;
    const isActive = req.query.isActive === 'true' ? true : req.query.isActive === 'false' ? false : undefined;
    const skip = (page - 1) * limit;
    const where = {};
    if (search) {
        where.OR = [
            { name: { contains: search, mode: 'insensitive' } },
            { description: { contains: search, mode: 'insensitive' } },
        ];
    }
    if (isActive !== undefined) {
        where.isActive = isActive;
    }
    const [templates, total] = await Promise.all([
        prisma.workflowTemplate.findMany({
            where,
            skip,
            take: limit,
            include: {
                _count: {
                    select: {
                        executions: true,
                    },
                },
                createdByUser: {
                    select: {
                        id: true,
                        username: true,
                    },
                },
            },
            orderBy: {
                createdAt: 'desc',
            },
        }),
        prisma.workflowTemplate.count({ where }),
    ]);
    res.json({
        success: true,
        data: templates,
        pagination: {
            page,
            limit,
            total,
            totalPages: Math.ceil(total / limit),
        },
    });
}));
// Get single workflow template
router.get('/templates/:id', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const template = await prisma.workflowTemplate.findUnique({
        where: { id },
        include: {
            executions: {
                select: {
                    id: true,
                    status: true,
                    startedAt: true,
                    endedAt: true,
                    currentStage: true,
                    priority: true,
                },
                orderBy: {
                    startedAt: 'desc',
                },
                take: 10,
            },
            createdByUser: {
                select: {
                    id: true,
                    username: true,
                },
            },
        },
    });
    if (!template) {
        throw new errorHandler_1.AppError('Workflow template not found', 404);
    }
    res.json({
        success: true,
        data: template,
    });
}));
// Get all workflow executions
router.get('/executions', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const executions = await prisma.workflowExecution.findMany({
        include: {
            template: {
                select: {
                    id: true,
                    name: true,
                },
            },
            executor: {
                select: {
                    id: true,
                    username: true,
                },
            },
            agents: {
                select: {
                    id: true,
                    agentId: true,
                    name: true,
                },
            },
        },
        orderBy: {
            startedAt: 'desc',
        },
    });
    res.json({
        success: true,
        data: executions,
    });
}));
// Start workflow execution
router.post('/:templateId/execute', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { templateId } = req.params;
    const { context = {} } = req.body;
    const executionId = await executionEngine.startExecution(templateId, req.user.id, context);
    res.status(201).json({
        success: true,
        data: {
            executionId,
            message: 'Workflow execution started',
        },
    });
}));
// Get execution status
router.get('/executions/:executionId', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { executionId } = req.params;
    const status = await executionEngine.getExecutionStatus(executionId);
    res.json({
        success: true,
        data: status,
    });
}));
// Cancel workflow execution
router.post('/executions/:executionId/cancel', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { executionId } = req.params;
    await executionEngine.cancelExecution(executionId, req.user.id);
    res.json({
        success: true,
        message: 'Workflow execution cancelled',
    });
}));
// Get all executions with filtering
router.get('/executions', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const status = req.query.status;
    const templateId = req.query.templateId;
    const skip = (page - 1) * limit;
    const where = {};
    if (status) {
        where.status = status;
    }
    if (templateId) {
        where.templateId = templateId;
    }
    const [executions, total] = await Promise.all([
        prisma.workflowExecution.findMany({
            where,
            skip,
            take: limit,
            include: {
                template: {
                    select: {
                        id: true,
                        name: true,
                        description: true,
                    },
                },
                createdByUser: {
                    select: {
                        id: true,
                        email: true,
                        name: true,
                    },
                },
            },
            orderBy: {
                createdAt: 'desc',
            },
        }),
        prisma.workflowExecution.count({ where }),
    ]);
    res.json({
        success: true,
        data: executions,
        pagination: {
            page,
            limit,
            total,
            totalPages: Math.ceil(total / limit),
        },
    });
}));

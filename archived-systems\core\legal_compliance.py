"""
Legal Compliance Framework for Branded AI Assistant Orchestration

Ensures full compliance with licensing, branding, and patent requirements
for all branded AI coding assistants including Copilot, Tabnine, Amazon Q, etc.

Key Compliance Requirements:
- User-provided credentials for all branded tools
- No hosting, reselling, or modification of branded tools
- Respect all branding, copyright, and patent restrictions
- Official API usage only
- Proper attribution and vendor guideline compliance
"""

import logging
import hashlib
import json
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum


class LicenseStatus(Enum):
    """License validation status."""
    VALID = "valid"
    INVALID = "invalid"
    EXPIRED = "expired"
    PENDING = "pending"
    UNKNOWN = "unknown"


class BrandingRequirement(Enum):
    """Branding requirement types."""
    ATTRIBUTION = "attribution"
    LOGO_DISPLAY = "logo_display"
    TRADEMARK_NOTICE = "trademark_notice"
    COPYRIGHT_NOTICE = "copyright_notice"
    VENDOR_GUIDELINES = "vendor_guidelines"


@dataclass
class VendorCompliance:
    """Vendor-specific compliance requirements."""
    vendor_name: str
    api_endpoint: str
    required_attribution: str
    trademark_notice: str
    copyright_notice: str
    branding_guidelines: Dict[str, Any]
    rate_limits: Dict[str, int]
    terms_of_service_url: str
    privacy_policy_url: str
    acceptable_use_policy: str
    patent_restrictions: List[str]
    api_version: str
    last_updated: datetime


class ComplianceManager:
    """
    Central compliance manager for all branded AI assistants.
    
    Ensures legal compliance, validates licenses, and enforces
    branding requirements for all integrated AI assistants.
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # Vendor compliance database
        self.vendor_compliance = {}
        self.user_licenses = {}
        self.compliance_violations = []
        
        # Initialize vendor compliance requirements
        self._initialize_vendor_compliance()
        
        # Compliance monitoring
        self.compliance_checks_enabled = self.config.get('compliance_checks_enabled', True)
        self.strict_mode = self.config.get('strict_mode', True)
        
    def _initialize_vendor_compliance(self) -> None:
        """Initialize compliance requirements for all supported vendors."""
        
        # GitHub Copilot Compliance
        self.vendor_compliance['github_copilot'] = VendorCompliance(
            vendor_name="GitHub Copilot",
            api_endpoint="https://api.github.com/copilot",
            required_attribution="Powered by GitHub Copilot®",
            trademark_notice="GitHub Copilot® is a trademark of GitHub, Inc.",
            copyright_notice="© GitHub, Inc. All rights reserved.",
            branding_guidelines={
                "logo_required": True,
                "attribution_placement": "visible",
                "trademark_symbol": "®",
                "color_requirements": {"primary": "#24292e", "secondary": "#586069"}
            },
            rate_limits={"requests_per_minute": 60, "requests_per_hour": 5000},
            terms_of_service_url="https://docs.github.com/en/site-policy/github-terms/github-terms-of-service",
            privacy_policy_url="https://docs.github.com/en/site-policy/privacy-policies/github-privacy-statement",
            acceptable_use_policy="https://docs.github.com/en/site-policy/acceptable-use-policies/github-acceptable-use-policies",
            patent_restrictions=["No reverse engineering", "No model extraction", "No competitive analysis"],
            api_version="v1",
            last_updated=datetime.utcnow()
        )
        
        # Tabnine Compliance
        self.vendor_compliance['tabnine'] = VendorCompliance(
            vendor_name="Tabnine",
            api_endpoint="https://api.tabnine.com",
            required_attribution="Powered by Tabnine™",
            trademark_notice="Tabnine™ is a trademark of Codota Ltd.",
            copyright_notice="© Codota Ltd. All rights reserved.",
            branding_guidelines={
                "logo_required": True,
                "attribution_placement": "visible",
                "trademark_symbol": "™",
                "color_requirements": {"primary": "#4A90E2", "secondary": "#7ED321"}
            },
            rate_limits={"requests_per_minute": 100, "requests_per_hour": 10000},
            terms_of_service_url="https://www.tabnine.com/terms-of-service",
            privacy_policy_url="https://www.tabnine.com/privacy-policy",
            acceptable_use_policy="https://www.tabnine.com/acceptable-use-policy",
            patent_restrictions=["No unauthorized access", "No data extraction", "No competitive use"],
            api_version="v2",
            last_updated=datetime.utcnow()
        )
        
        # Amazon Q Developer Compliance
        self.vendor_compliance['amazon_q'] = VendorCompliance(
            vendor_name="Amazon Q Developer",
            api_endpoint="https://q.aws.amazon.com/api",
            required_attribution="Powered by Amazon Q Developer",
            trademark_notice="Amazon Q Developer is a trademark of Amazon.com, Inc.",
            copyright_notice="© Amazon.com, Inc. or its affiliates. All rights reserved.",
            branding_guidelines={
                "logo_required": True,
                "attribution_placement": "prominent",
                "trademark_symbol": "",
                "color_requirements": {"primary": "#FF9900", "secondary": "#232F3E"}
            },
            rate_limits={"requests_per_minute": 50, "requests_per_hour": 2000},
            terms_of_service_url="https://aws.amazon.com/service-terms/",
            privacy_policy_url="https://aws.amazon.com/privacy/",
            acceptable_use_policy="https://aws.amazon.com/aup/",
            patent_restrictions=["AWS service terms apply", "No unauthorized redistribution"],
            api_version="v1",
            last_updated=datetime.utcnow()
        )
        
        # Cursor Compliance
        self.vendor_compliance['cursor'] = VendorCompliance(
            vendor_name="Cursor",
            api_endpoint="https://api.cursor.sh",
            required_attribution="Powered by Cursor",
            trademark_notice="Cursor is a trademark of Anysphere, Inc.",
            copyright_notice="© Anysphere, Inc. All rights reserved.",
            branding_guidelines={
                "logo_required": True,
                "attribution_placement": "visible",
                "trademark_symbol": "",
                "color_requirements": {"primary": "#000000", "secondary": "#FFFFFF"}
            },
            rate_limits={"requests_per_minute": 30, "requests_per_hour": 1000},
            terms_of_service_url="https://cursor.sh/terms",
            privacy_policy_url="https://cursor.sh/privacy",
            acceptable_use_policy="https://cursor.sh/acceptable-use",
            patent_restrictions=["No reverse engineering", "No unauthorized access"],
            api_version="v1",
            last_updated=datetime.utcnow()
        )
        
        # QodoAI Compliance
        self.vendor_compliance['qodo_ai'] = VendorCompliance(
            vendor_name="QodoAI",
            api_endpoint="https://api.qodo.ai",
            required_attribution="Powered by QodoAI",
            trademark_notice="QodoAI is a trademark of Qodo Ltd.",
            copyright_notice="© Qodo Ltd. All rights reserved.",
            branding_guidelines={
                "logo_required": True,
                "attribution_placement": "visible",
                "trademark_symbol": "",
                "color_requirements": {"primary": "#6C5CE7", "secondary": "#A29BFE"}
            },
            rate_limits={"requests_per_minute": 40, "requests_per_hour": 1500},
            terms_of_service_url="https://qodo.ai/terms",
            privacy_policy_url="https://qodo.ai/privacy",
            acceptable_use_policy="https://qodo.ai/acceptable-use",
            patent_restrictions=["No unauthorized use", "No competitive analysis"],
            api_version="v1",
            last_updated=datetime.utcnow()
        )
        
        self.logger.info(f"Initialized compliance for {len(self.vendor_compliance)} vendors")
    
    def validate_user_license(self, vendor: str, user_credentials: Dict[str, Any]) -> Tuple[LicenseStatus, Dict[str, Any]]:
        """
        Validate user license for a specific vendor.
        
        Args:
            vendor: Vendor identifier (e.g., 'github_copilot')
            user_credentials: User-provided credentials
            
        Returns:
            Tuple of license status and validation details
        """
        try:
            if vendor not in self.vendor_compliance:
                return LicenseStatus.UNKNOWN, {"error": f"Unknown vendor: {vendor}"}
            
            vendor_info = self.vendor_compliance[vendor]
            
            # Validate required credentials
            required_fields = self._get_required_credential_fields(vendor)
            missing_fields = [field for field in required_fields if field not in user_credentials]
            
            if missing_fields:
                return LicenseStatus.INVALID, {
                    "error": "Missing required credentials",
                    "missing_fields": missing_fields,
                    "required_fields": required_fields
                }
            
            # Validate credential format
            validation_result = self._validate_credential_format(vendor, user_credentials)
            if not validation_result["valid"]:
                return LicenseStatus.INVALID, validation_result
            
            # Store validated credentials (hashed for security)
            self._store_user_credentials(vendor, user_credentials)
            
            return LicenseStatus.VALID, {
                "vendor": vendor_info.vendor_name,
                "api_version": vendor_info.api_version,
                "rate_limits": vendor_info.rate_limits,
                "validated_at": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Error validating license for {vendor}: {str(e)}")
            return LicenseStatus.UNKNOWN, {"error": str(e)}
    
    def _get_required_credential_fields(self, vendor: str) -> List[str]:
        """Get required credential fields for a vendor."""
        credential_requirements = {
            'github_copilot': ['api_key', 'user_id'],
            'tabnine': ['api_key', 'user_token'],
            'amazon_q': ['access_key_id', 'secret_access_key', 'region'],
            'cursor': ['api_key', 'user_id'],
            'qodo_ai': ['api_key', 'organization_id']
        }
        
        return credential_requirements.get(vendor, ['api_key'])
    
    def _validate_credential_format(self, vendor: str, credentials: Dict[str, Any]) -> Dict[str, Any]:
        """Validate credential format for a vendor."""
        try:
            # Basic format validation (vendor-specific rules would go here)
            for key, value in credentials.items():
                if not isinstance(value, str) or len(value.strip()) == 0:
                    return {
                        "valid": False,
                        "error": f"Invalid format for {key}",
                        "details": "Credentials must be non-empty strings"
                    }
            
            # Vendor-specific validation
            if vendor == 'github_copilot':
                api_key = credentials.get('api_key', '')
                if not api_key.startswith('ghp_'):
                    return {
                        "valid": False,
                        "error": "Invalid GitHub API key format",
                        "details": "GitHub API keys should start with 'ghp_'"
                    }
            
            elif vendor == 'amazon_q':
                access_key = credentials.get('access_key_id', '')
                if len(access_key) != 20:
                    return {
                        "valid": False,
                        "error": "Invalid AWS access key format",
                        "details": "AWS access keys should be 20 characters long"
                    }
            
            return {"valid": True, "message": "Credential format validation passed"}
            
        except Exception as e:
            return {
                "valid": False,
                "error": "Credential validation failed",
                "details": str(e)
            }
    
    def _store_user_credentials(self, vendor: str, credentials: Dict[str, Any]) -> None:
        """Store user credentials securely (hashed)."""
        # Hash credentials for security
        credential_hash = hashlib.sha256(
            json.dumps(credentials, sort_keys=True).encode()
        ).hexdigest()
        
        self.user_licenses[vendor] = {
            "credential_hash": credential_hash,
            "validated_at": datetime.utcnow(),
            "status": LicenseStatus.VALID,
            "vendor_info": self.vendor_compliance[vendor].vendor_name
        }
    
    def check_compliance_status(self, vendor: str) -> Dict[str, Any]:
        """Check overall compliance status for a vendor."""
        if vendor not in self.vendor_compliance:
            return {"compliant": False, "error": f"Unknown vendor: {vendor}"}
        
        vendor_info = self.vendor_compliance[vendor]
        license_info = self.user_licenses.get(vendor)
        
        compliance_status = {
            "vendor": vendor_info.vendor_name,
            "compliant": False,
            "license_valid": False,
            "branding_requirements": vendor_info.branding_guidelines,
            "attribution_required": vendor_info.required_attribution,
            "rate_limits": vendor_info.rate_limits,
            "last_checked": datetime.utcnow().isoformat()
        }
        
        # Check license status
        if license_info and license_info["status"] == LicenseStatus.VALID:
            compliance_status["license_valid"] = True
            compliance_status["compliant"] = True
        
        return compliance_status
    
    def get_branding_requirements(self, vendor: str) -> Dict[str, Any]:
        """Get branding requirements for a vendor."""
        if vendor not in self.vendor_compliance:
            return {"error": f"Unknown vendor: {vendor}"}
        
        vendor_info = self.vendor_compliance[vendor]
        
        return {
            "vendor": vendor_info.vendor_name,
            "required_attribution": vendor_info.required_attribution,
            "trademark_notice": vendor_info.trademark_notice,
            "copyright_notice": vendor_info.copyright_notice,
            "branding_guidelines": vendor_info.branding_guidelines,
            "logo_requirements": vendor_info.branding_guidelines.get("logo_required", False),
            "color_requirements": vendor_info.branding_guidelines.get("color_requirements", {}),
            "placement_requirements": vendor_info.branding_guidelines.get("attribution_placement", "visible")
        }
    
    def validate_api_usage(self, vendor: str, request_count: int, time_window: str) -> Dict[str, Any]:
        """Validate API usage against rate limits."""
        if vendor not in self.vendor_compliance:
            return {"valid": False, "error": f"Unknown vendor: {vendor}"}
        
        vendor_info = self.vendor_compliance[vendor]
        rate_limits = vendor_info.rate_limits
        
        limit_key = f"requests_per_{time_window}"
        if limit_key not in rate_limits:
            return {"valid": False, "error": f"Unknown time window: {time_window}"}
        
        limit = rate_limits[limit_key]
        
        return {
            "valid": request_count <= limit,
            "current_usage": request_count,
            "limit": limit,
            "time_window": time_window,
            "vendor": vendor_info.vendor_name,
            "remaining": max(0, limit - request_count)
        }
    
    def log_compliance_violation(self, vendor: str, violation_type: str, details: Dict[str, Any]) -> None:
        """Log a compliance violation."""
        violation = {
            "vendor": vendor,
            "violation_type": violation_type,
            "details": details,
            "timestamp": datetime.utcnow().isoformat(),
            "severity": details.get("severity", "medium")
        }
        
        self.compliance_violations.append(violation)
        self.logger.warning(f"Compliance violation logged: {vendor} - {violation_type}")
        
        # In strict mode, raise exception for critical violations
        if self.strict_mode and details.get("severity") == "critical":
            raise ComplianceViolationError(f"Critical compliance violation: {violation_type}")
    
    def get_compliance_report(self) -> Dict[str, Any]:
        """Generate comprehensive compliance report."""
        return {
            "total_vendors": len(self.vendor_compliance),
            "licensed_vendors": len(self.user_licenses),
            "compliance_violations": len(self.compliance_violations),
            "vendor_status": {
                vendor: self.check_compliance_status(vendor)
                for vendor in self.vendor_compliance.keys()
            },
            "recent_violations": self.compliance_violations[-10:],  # Last 10 violations
            "generated_at": datetime.utcnow().isoformat(),
            "strict_mode": self.strict_mode
        }


class BrandingManager:
    """
    Manages branding requirements and attribution for all vendors.
    
    Ensures proper display of vendor branding, attribution,
    and compliance with vendor guidelines.
    """
    
    def __init__(self, compliance_manager: ComplianceManager):
        self.compliance_manager = compliance_manager
        self.logger = logging.getLogger(__name__)
    
    def generate_attribution_text(self, vendors: List[str]) -> str:
        """Generate proper attribution text for multiple vendors."""
        attributions = []
        
        for vendor in vendors:
            branding = self.compliance_manager.get_branding_requirements(vendor)
            if "error" not in branding:
                attributions.append(branding["required_attribution"])
        
        return " | ".join(attributions)
    
    def get_branding_html(self, vendors: List[str]) -> str:
        """Generate HTML for proper vendor branding display."""
        html_parts = []
        
        for vendor in vendors:
            branding = self.compliance_manager.get_branding_requirements(vendor)
            if "error" not in branding:
                colors = branding.get("color_requirements", {})
                primary_color = colors.get("primary", "#000000")
                
                html_parts.append(f"""
                <div class="vendor-attribution" style="color: {primary_color};">
                    <span class="attribution">{branding['required_attribution']}</span>
                    <small class="trademark">{branding['trademark_notice']}</small>
                </div>
                """)
        
        return "\n".join(html_parts)
    
    def validate_branding_compliance(self, vendor: str, display_context: Dict[str, Any]) -> Dict[str, Any]:
        """Validate that branding is displayed correctly."""
        branding = self.compliance_manager.get_branding_requirements(vendor)
        if "error" in branding:
            return branding
        
        compliance_issues = []
        
        # Check attribution presence
        if "attribution_text" not in display_context:
            compliance_issues.append("Missing attribution text")
        elif branding["required_attribution"] not in display_context["attribution_text"]:
            compliance_issues.append("Incorrect attribution text")
        
        # Check trademark notice
        if branding["trademark_notice"] and "trademark_notice" not in display_context:
            compliance_issues.append("Missing trademark notice")
        
        return {
            "compliant": len(compliance_issues) == 0,
            "issues": compliance_issues,
            "vendor": branding["vendor"],
            "checked_at": datetime.utcnow().isoformat()
        }


class LicenseValidator:
    """
    Validates user licenses and manages license-related operations.
    
    Ensures users have valid licenses for all branded AI assistants
    they want to use in the orchestration system.
    """
    
    def __init__(self, compliance_manager: ComplianceManager):
        self.compliance_manager = compliance_manager
        self.logger = logging.getLogger(__name__)
        self.validation_cache = {}
        self.cache_ttl = timedelta(hours=1)  # Cache validation results for 1 hour
    
    def validate_all_licenses(self, user_credentials: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """Validate licenses for all provided vendors."""
        validation_results = {}
        
        for vendor, credentials in user_credentials.items():
            status, details = self.compliance_manager.validate_user_license(vendor, credentials)
            validation_results[vendor] = {
                "status": status.value,
                "details": details,
                "validated_at": datetime.utcnow().isoformat()
            }
        
        return {
            "validation_results": validation_results,
            "total_vendors": len(user_credentials),
            "valid_licenses": sum(1 for r in validation_results.values() if r["status"] == "valid"),
            "overall_valid": all(r["status"] == "valid" for r in validation_results.values())
        }
    
    def check_license_expiry(self, vendor: str) -> Dict[str, Any]:
        """Check if a license is approaching expiry."""
        # This would integrate with vendor APIs to check license status
        # For now, return a placeholder implementation
        return {
            "vendor": vendor,
            "expires_soon": False,
            "days_until_expiry": None,
            "renewal_required": False,
            "checked_at": datetime.utcnow().isoformat()
        }


class ComplianceViolationError(Exception):
    """Exception raised for compliance violations."""
    pass

{"name": "meta-orchestration-system", "version": "1.0.0", "description": "Comprehensive meta-orchestration system for AI assistants with role-based assignment, fallback logic, and multi-IDE support", "main": "index.js", "bin": {"meta-orchestrator": "./index.js"}, "scripts": {"start": "node index.js start", "init": "node index.js init", "status": "node index.js status", "dashboard": "node index.js dashboard", "execute": "node index.js execute --interactive", "test": "npm run test:adapters && npm run test:workflows && npm run test:integration", "test:adapters": "node test/adapters.test.js", "test:workflows": "node test/workflows.test.js", "test:integration": "node test/integration.test.js", "dev": "nodemon index.js start --daemon", "build": "npm run build:docs && npm run build:config", "build:docs": "node scripts/generate-docs.js", "build:config": "node scripts/generate-config-schema.js", "setup": "npm run setup:config && npm run setup:adapters", "setup:config": "node scripts/setup-config.js", "setup:adapters": "node scripts/setup-adapters.js", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "validate": "npm run lint && npm run test", "clean": "rimraf logs/* .temp/* node_modules/.cache", "reset": "npm run clean && node scripts/reset-config.js"}, "keywords": ["ai", "orchestration", "meta-orchestration", "ai-assistants", "role-based", "fallback", "multi-ide", "cursor", "windsurf", "tabnine", "augment-code", "github-copilot", "ollama", "automation", "workflow", "code-generation", "ai-integration"], "author": "Meta-Orchestration Team", "license": "MIT", "dependencies": {"axios": "^1.6.0", "chalk": "^4.1.2", "chokidar": "^3.5.3", "commander": "^11.1.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "fs-extra": "^11.1.1", "helmet": "^7.1.0", "inquirer": "^8.2.6", "lodash": "^4.17.21", "moment": "^2.29.4", "ora": "^5.4.1", "uuid": "^9.0.1", "ws": "^8.14.2", "yaml": "^2.3.4"}, "devDependencies": {"eslint": "^8.50.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-node": "^11.1.0", "jest": "^29.7.0", "nodemon": "^3.0.1", "prettier": "^3.0.3", "rimraf": "^5.0.5", "@types/node": "^20.8.0"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "local"}, "config": {"meta-orchestration": {"version": "1.0.0", "configDir": "./config", "logsDir": "./logs", "tempDir": "./.temp", "defaultPort": 3000, "defaultWSPort": 3001, "defaultDashboardPort": 8080}, "adapters": {"augment-code": {"enabled": true, "priority": 1, "roles": ["analyzer", "validator"]}, "cursor": {"enabled": true, "priority": 2, "roles": ["generator", "completer", "documenter"]}, "windsurf": {"enabled": true, "priority": 2, "roles": ["generator", "validator", "documenter"]}, "tabnine": {"enabled": true, "priority": 3, "roles": ["completer"]}, "github-copilot": {"enabled": true, "priority": 2, "roles": ["generator", "completer", "documenter"]}, "qodo": {"enabled": true, "priority": 2, "roles": ["validator", "generator"]}, "ollama": {"enabled": true, "priority": 3, "roles": ["generator", "completer", "analyzer", "documenter"]}, "cline": {"enabled": true, "priority": 3, "roles": ["generator", "completer", "documenter"]}, "continue": {"enabled": true, "priority": 3, "roles": ["generator", "completer", "analyzer"]}, "aider": {"enabled": true, "priority": 3, "roles": ["generator", "validator", "documenter"]}, "lm-studio": {"enabled": true, "priority": 3, "roles": ["generator", "completer", "analyzer", "documenter"]}, "superagi": {"enabled": true, "priority": 2, "roles": ["analyzer", "generator", "validator"]}, "autogen": {"enabled": true, "priority": 3, "roles": ["analyzer", "generator", "validator"]}}, "workflows": {"feature-implementation": {"enabled": true, "default": true}, "bug-fix": {"enabled": true}, "code-review": {"enabled": true}, "refactoring": {"enabled": true}, "testing": {"enabled": true}}, "ide-integration": {"vscode": {"enabled": true, "extensionId": "meta-orchestration.vscode", "autoActivate": true}, "jetbrains": {"enabled": false, "pluginId": "meta-orchestration.jetbrains"}, "vim": {"enabled": false, "pluginPath": "~/.vim/pack/meta-orchestration"}}, "security": {"encryption": true, "authentication": false, "rateLimiting": true, "ipProtection": true, "compliance": {"soc2": true, "gdpr": true, "hipaa": false}}, "performance": {"caching": {"enabled": true, "ttl": 3600000, "maxSize": "100MB"}, "parallelExecution": {"enabled": true, "maxConcurrent": 4}, "optimization": {"enabled": true, "adaptiveRouting": true, "performanceBasedFallback": true}}, "monitoring": {"enabled": true, "metricsCollection": true, "performanceTracking": true, "healthChecks": true, "alerting": false}}, "files": ["index.js", "core/", "adapters/", "config/", "api/", "dashboard/", "utils/", "scripts/", "README.md", "LICENSE"], "directories": {"lib": "./core", "test": "./test", "doc": "./docs"}, "homepage": "https://github.com/meta-orchestration/meta-orchestrator#readme", "bugs": {"url": "https://github.com/meta-orchestration/meta-orchestrator/issues"}, "funding": {"type": "github", "url": "https://github.com/sponsors/meta-orchestration"}}
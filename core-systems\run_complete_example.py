#!/usr/bin/env python3
"""
Executable wrapper for complete_example.py

This script properly sets up the Python path and runs the complete example
that demonstrates the Universal Dual-Purpose Feedback Loop Framework.
"""

import os
import sys
import logging
from datetime import datetime
from pathlib import Path

# Add the core-systems directory to Python path
core_systems_dir = Path(__file__).parent
sys.path.insert(0, str(core_systems_dir))

# Import framework components directly
from core.feedback_engine import FeedbackEngine
from domains.drone_ai import DroneAIDomain
from domains.timestamp_ai import TimeStampAIDomain
from components.confidence.adaptive_confidence_model import AdaptiveConfidenceModel
from components.trust.trust_score_calculator import TrustScoreCalculator
from components.memory.file_memory_store import FileMemoryStore


def setup_logging():
    """Setup logging for the example."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('feedback_loop_example.log'),
            logging.StreamHandler()
        ]
    )


def setup_feedback_engine():
    """Setup and configure the feedback engine with both domains."""
    print("🔧 Setting up Feedback Engine...")
    
    # Initialize components
    confidence_model = AdaptiveConfidenceModel()
    trust_calculator = TrustScoreCalculator()
    memory_store = FileMemoryStore(storage_path="feedback_data")
    
    # Create the feedback engine
    engine = FeedbackEngine(
        confidence_model=confidence_model,
        trust_calculator=trust_calculator,
        memory_store=memory_store
    )
    
    # Configure and register Drone AI domain
    drone_config = {
        'sensor_types': ['gps', 'camera', 'lidar', 'thermal'],
        'mission_types': ['search_rescue', 'surveillance', 'mapping'],
        'validation_thresholds': {
            'gps_accuracy': 5.0,  # meters
            'image_quality': 0.8,  # confidence score
            'sensor_health': 0.9   # health percentage
        }
    }
    drone_domain = DroneAIDomain(drone_config)
    engine.register_domain('drone_ai', drone_domain)
    
    # Configure and register TimeStamp AI domain
    timestamp_config = {
        'hash_algorithms': ['sha256', 'sha512'],
        'verification_methods': ['blockchain', 'digital_signature'],
        'drift_tolerance': 300,  # seconds
        'validation_thresholds': {
            'hash_strength': 0.95,
            'timestamp_accuracy': 0.9,
            'verification_confidence': 0.85
        }
    }
    timestamp_domain = TimeStampAIDomain(timestamp_config)
    engine.register_domain('timestamp_ai', timestamp_domain)
    
    return engine


def demonstrate_drone_ai_feedback(engine: FeedbackEngine) -> None:
    """Demonstrate feedback processing for Drone AI scenarios."""
    print("\n=== Drone AI Feedback Examples ===")
    
    # Example 1: High-quality GPS data
    gps_data = {
        'latitude': 37.7749,
        'longitude': -122.4194,
        'altitude': 100.5,
        'accuracy': 2.1,
        'timestamp': datetime.utcnow().isoformat()
    }
    
    context = {
        'sensor_type': 'gps',
        'mission_type': 'search_rescue',
        'emergency_status': True
    }
    
    result = engine.process_output('drone_ai', gps_data, context, agent_id='drone_001')
    print(f"✅ GPS Data Result: {result.feedback_type.value} (Confidence: {result.confidence_score:.3f})")
    
    # Example 2: Poor quality image data
    image_data = {
        'image_path': '/drone/images/search_area_001.jpg',
        'detection_confidence': 0.3,  # Low confidence
        'objects_detected': ['tree', 'rock'],
        'image_quality_score': 0.4,  # Poor quality
        'timestamp': datetime.utcnow().isoformat()
    }
    
    context = {
        'sensor_type': 'camera',
        'mission_type': 'search_rescue',
        'target_object': 'person'
    }
    
    result = engine.process_output('drone_ai', image_data, context, agent_id='drone_002')
    print(f"⚠️  Image Data Result: {result.feedback_type.value} (Confidence: {result.confidence_score:.3f})")
    
    # Example 3: Faulty sensor data
    faulty_data = {
        'latitude': 91.0,  # Invalid latitude
        'longitude': -200.0,  # Invalid longitude
        'altitude': -50.0,  # Below ground
        'accuracy': 100.0,  # Poor accuracy
        'sensor_health': 0.2  # Low health
    }
    
    context = {
        'sensor_type': 'gps',
        'mission_type': 'general',
        'emergency_status': True
    }
    
    result = engine.process_output('drone_ai', faulty_data, context, agent_id='drone_003')
    print(f"❌ Faulty Data Result: {result.feedback_type.value} (Confidence: {result.confidence_score:.3f})")


def demonstrate_timestamp_ai_feedback(engine: FeedbackEngine) -> None:
    """Demonstrate feedback processing for TimeStamp AI scenarios."""
    print("\n=== TimeStamp AI Feedback Examples ===")
    
    # Example 1: Accurate timestamp response
    timestamp_response = {
        'timestamp': datetime.utcnow().isoformat(),
        'hash': 'a1b2c3d4e5f6789012345678901234567890123456789012345678901234567890',
        'signature': 'def456789012345678901234567890123456789012345678901234567890123456789012345678901234567890',
        'verification_status': True
    }
    
    context = {
        'output_type': 'timestamp_response',
        'ai_model': 'claude',
        'request_timestamp': datetime.utcnow().isoformat()
    }
    
    result = engine.process_output('timestamp_ai', timestamp_response, context, agent_id='timestamp_001')
    print(f"✅ Timestamp Response Result: {result.feedback_type.value} (Confidence: {result.confidence_score:.3f})")
    
    # Example 2: Environmental impact calculation
    impact_calculation = {
        'energy_usage': 15.5,  # kWh
        'water_usage': 2.3,    # liters
        'carbon_footprint': 20.0,  # g CO2
        'token_count': 1500,
        'model_used': 'gpt-4'
    }
    
    context = {
        'output_type': 'impact_calculation',
        'calculation_method': 'enhanced_model',
        'data_sources': ['energy_grid', 'water_utility', 'carbon_registry']
    }
    
    result = engine.process_output('timestamp_ai', impact_calculation, context, agent_id='timestamp_002')
    print(f"✅ Impact Calculation Result: {result.feedback_type.value} (Confidence: {result.confidence_score:.3f})")
    
    # Example 3: Timestamp with drift
    drifted_response = {
        'timestamp': (datetime.utcnow().timestamp() - 180),  # 3 minutes ago
        'hash': 'b2c3d4e5f6789012345678901234567890123456789012345678901234567890a1',
        'verification_status': False
    }
    
    context = {
        'output_type': 'timestamp_response',
        'ai_model': 'gpt-3.5',
        'expected_realtime': True
    }
    
    result = engine.process_output('timestamp_ai', drifted_response, context, agent_id='timestamp_003')
    print(f"⚠️  Drifted Timestamp Result: {result.feedback_type.value} (Confidence: {result.confidence_score:.3f})")


def display_analytics(engine: FeedbackEngine) -> None:
    """Display analytics and summary information."""
    print("\n=== Analytics Summary ===")
    
    # Get domain information
    domain_info = engine.get_domain_info()
    print(f"Registered Domains: {list(domain_info.keys())}")
    
    # Get analytics from memory store
    if engine.memory_store:
        analytics = engine.memory_store.get_analytics_data()
        print(f"Total Feedback Entries: {analytics.get('total_entries', 0)}")
        print(f"Feedback Types Distribution: {analytics.get('feedback_distribution', {})}")
        print(f"Average Confidence Score: {analytics.get('avg_confidence', 0):.3f}")
    
    # Get trust scores
    if engine.trust_calculator:
        # Get top agents
        top_agents = engine.trust_calculator.get_top_agents(limit=5)
        print(f"Top 5 Agents by Trust Score:")
        for agent in top_agents:
            print(f"  {agent['agent_id']}: {agent['trust_score']:.3f}")


def main():
    """Main execution function."""
    print("🚀 Universal Dual-Purpose Feedback Loop Framework - Complete Example")
    print("=" * 80)
    
    # Setup logging
    setup_logging()
    
    try:
        # Setup the feedback engine
        engine = setup_feedback_engine()
        
        # Run demonstrations
        demonstrate_drone_ai_feedback(engine)
        demonstrate_timestamp_ai_feedback(engine)
        
        # Display analytics
        display_analytics(engine)
        
        print("\n" + "=" * 80)
        print("✅ Example completed successfully!")
        print("📁 Check 'feedback_data' directory for stored feedback entries.")
        print("📄 Check 'feedback_loop_example.log' for detailed logs.")
        
    except Exception as e:
        print(f"\n❌ Error during execution: {e}")
        logging.error(f"Example execution failed: {e}", exc_info=True)
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

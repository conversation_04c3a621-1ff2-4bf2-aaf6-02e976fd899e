import puppeteer from 'puppeteer';
import { config } from '../config/index.js';
import { logger } from '../utils/logger.js';

class BrowserAutomation {
  constructor() {
    this.browser = null;
    this.page = null;
  }

  async initBrowser() {
    if (this.browser) return;

    try {
      this.browser = await puppeteer.launch({
        headless: config.browser.headless,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu'
        ]
      });

      this.page = await this.browser.newPage();
      await this.page.setViewport({ width: 1280, height: 720 });

      // Set a realistic user agent
      await this.page.setUserAgent(
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
      );

      logger.info('Browser automation initialized');
    } catch (error) {
      logger.error('Failed to initialize browser', { error: error.message });
      throw error;
    }
  }

  async closeBrowser() {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
      this.page = null;
      logger.info('Browser automation closed');
    }
  }

  async retrieveChatGPTThreads(options = {}) {
    await this.initBrowser();
    logger.info('Starting ChatGPT thread retrieval via browser automation');

    try {
      // Navigate to ChatGPT
      await this.page.goto('https://chat.openai.com', {
        waitUntil: 'networkidle2',
        timeout: config.browser.timeout
      });

      // Check if login is required
      const isLoggedIn = await this.checkChatGPTLogin();
      if (!isLoggedIn) {
        logger.warn('ChatGPT login required - browser automation cannot proceed without authentication');
        return [];
      }

      // Wait for conversations to load
      await this.page.waitForSelector('[data-testid="conversation-turn"]', {
        timeout: 10000
      }).catch(() => {
        logger.warn('No conversations found or page structure changed');
      });

      // Extract conversation list
      const conversations = await this.page.evaluate(() => {
        const convElements = document.querySelectorAll('[data-testid="conversation-turn"]');
        const conversations = [];

        convElements.forEach((element, index) => {
          const titleElement = element.querySelector('a');
          const title = titleElement ? titleElement.textContent.trim() : `Conversation ${index + 1}`;
          const href = titleElement ? titleElement.getAttribute('href') : null;
          const id = href ? href.split('/').pop() : `conv_${index}`;

          conversations.push({
            id,
            title,
            href,
            element: element.outerHTML
          });
        });

        return conversations;
      });

      logger.info(`Found ${conversations.length} ChatGPT conversations`);

      // Extract full conversation details
      const threads = [];
      const limit = Math.min(conversations.length, options.limit || 10);

      for (let i = 0; i < limit; i++) {
        const conv = conversations[i];
        try {
          const thread = await this.extractChatGPTConversation(conv);
          if (thread) {
            threads.push(thread);
          }
        } catch (error) {
          logger.warn(`Failed to extract ChatGPT conversation ${conv.id}`, { error: error.message });
        }
      }

      logger.info(`Successfully extracted ${threads.length} ChatGPT threads`);
      return threads;

    } catch (error) {
      logger.error('ChatGPT browser automation failed', { error: error.message });
      throw error;
    }
  }

  async retrievePerplexityThreads(options = {}) {
    await this.initBrowser();
    logger.info('Starting Perplexity thread retrieval via browser automation');

    try {
      // Navigate to Perplexity
      await this.page.goto('https://www.perplexity.ai', {
        waitUntil: 'networkidle2',
        timeout: config.browser.timeout
      });

      // Check if login is required
      const isLoggedIn = await this.checkPerplexityLogin();
      if (!isLoggedIn) {
        logger.warn('Perplexity login required - browser automation cannot proceed without authentication');
        return [];
      }

      // Navigate to history/threads page
      await this.page.goto('https://www.perplexity.ai/search', {
        waitUntil: 'networkidle2'
      });

      // Wait for threads to load
      await this.page.waitForSelector('.thread-item, .search-result', {
        timeout: 10000
      }).catch(() => {
        logger.warn('No threads found or page structure changed');
      });

      // Extract thread list
      const threads = await this.page.evaluate(() => {
        const threadElements = document.querySelectorAll('.thread-item, .search-result');
        const threads = [];

        threadElements.forEach((element, index) => {
          const titleElement = element.querySelector('h3, .title, a');
          const title = titleElement ? titleElement.textContent.trim() : `Thread ${index + 1}`;
          const href = element.querySelector('a') ? element.querySelector('a').getAttribute('href') : null;
          const id = href ? href.split('/').pop() : `thread_${index}`;

          threads.push({
            id,
            title,
            href,
            element: element.outerHTML
          });
        });

        return threads;
      });

      logger.info(`Found ${threads.length} Perplexity threads`);

      // Extract full thread details
      const extractedThreads = [];
      const limit = Math.min(threads.length, options.limit || 10);

      for (let i = 0; i < limit; i++) {
        const thread = threads[i];
        try {
          const extractedThread = await this.extractPerplexityThread(thread);
          if (extractedThread) {
            extractedThreads.push(extractedThread);
          }
        } catch (error) {
          logger.warn(`Failed to extract Perplexity thread ${thread.id}`, { error: error.message });
        }
      }

      logger.info(`Successfully extracted ${extractedThreads.length} Perplexity threads`);
      return extractedThreads;

    } catch (error) {
      logger.error('Perplexity browser automation failed', { error: error.message });
      throw error;
    }
  }

  async retrieveClaudeThreads(options = {}) {
    await this.initBrowser();
    logger.info('Starting Claude thread retrieval via browser automation');

    try {
      await this.page.goto('https://claude.ai', {
        waitUntil: 'networkidle2',
        timeout: config.browser.timeout
      });

      const isLoggedIn = await this.checkClaudeLogin();
      if (!isLoggedIn) {
        logger.warn('Claude login required - browser automation cannot proceed without authentication');
        return [];
      }

      // Navigate to conversations
      await this.page.goto('https://claude.ai/chats', {
        waitUntil: 'networkidle2'
      });

      // Wait for conversations to load
      await this.page.waitForSelector('[data-testid="chat-item"], .conversation-item', {
        timeout: 10000
      }).catch(() => {
        logger.warn('No Claude conversations found');
      });

      const conversations = await this.page.evaluate(() => {
        const convElements = document.querySelectorAll('[data-testid="chat-item"], .conversation-item');
        const conversations = [];

        convElements.forEach((element, index) => {
          const titleElement = element.querySelector('.title, h3, a');
          const title = titleElement ? titleElement.textContent.trim() : `Conversation ${index + 1}`;
          const href = element.querySelector('a') ? element.querySelector('a').getAttribute('href') : null;
          const id = href ? href.split('/').pop() : `claude_conv_${index}`;

          conversations.push({
            id,
            title,
            href,
            element: element.outerHTML
          });
        });

        return conversations;
      });

      logger.info(`Found ${conversations.length} Claude conversations`);

      const threads = [];
      const limit = Math.min(conversations.length, options.limit || 10);

      for (let i = 0; i < limit; i++) {
        const conv = conversations[i];
        try {
          const thread = await this.extractClaudeConversation(conv);
          if (thread) {
            threads.push(thread);
          }
        } catch (error) {
          logger.warn(`Failed to extract Claude conversation ${conv.id}`, { error: error.message });
        }
      }

      logger.info(`Successfully extracted ${threads.length} Claude threads`);
      return threads;

    } catch (error) {
      logger.error('Claude browser automation failed', { error: error.message });
      throw error;
    }
  }

  async retrieveThreadsByPlatform(platform, options = {}) {
    const platformMethods = {
      'chatgpt': () => this.retrieveChatGPTThreads(options),
      'perplexity': () => this.retrievePerplexityThreads(options),
      'claude': () => this.retrieveClaudeThreads(options),
      'gemini': () => this.retrieveGeminiThreads(options),
      'mistral': () => this.retrieveMistralThreads(options)
    };

    const method = platformMethods[platform.toLowerCase()];
    if (!method) {
      logger.warn(`Browser automation not implemented for platform: ${platform}`);
      return [];
    }

    return await method();
  }

  async checkChatGPTLogin() {
    try {
      // Look for login indicators
      const loginButton = await this.page.$('button[data-testid="login-button"]');
      const signUpButton = await this.page.$('a[href*="signup"]');

      return !loginButton && !signUpButton;
    } catch (error) {
      logger.warn('Could not determine ChatGPT login status', { error: error.message });
      return false;
    }
  }

  async checkPerplexityLogin() {
    try {
      // Look for login indicators
      const loginButton = await this.page.$('button:contains("Sign in"), a:contains("Sign in")');

      return !loginButton;
    } catch (error) {
      logger.warn('Could not determine Perplexity login status', { error: error.message });
      return false;
    }
  }

  async extractChatGPTConversation(conversation) {
    try {
      if (conversation.href) {
        await this.page.goto(`https://chat.openai.com${conversation.href}`, {
          waitUntil: 'networkidle2'
        });
      }

      // Wait for messages to load
      await this.page.waitForSelector('[data-message-author-role]', { timeout: 5000 });

      // Extract messages
      const messages = await this.page.evaluate(() => {
        const messageElements = document.querySelectorAll('[data-message-author-role]');
        const messages = [];

        messageElements.forEach(element => {
          const role = element.getAttribute('data-message-author-role');
          const contentElement = element.querySelector('.markdown, .message-content, p');
          const content = contentElement ? contentElement.textContent.trim() : '';

          if (content) {
            messages.push({
              role: role === 'assistant' ? 'assistant' : 'user',
              content,
              timestamp: new Date().toISOString()
            });
          }
        });

        return messages;
      });

      return {
        id: conversation.id,
        source: 'chatgpt',
        title: conversation.title,
        created_at: new Date().toISOString(),
        messages,
        metadata: {
          extractedVia: 'browser-automation',
          url: conversation.href
        }
      };

    } catch (error) {
      logger.error(`Failed to extract ChatGPT conversation ${conversation.id}`, { error: error.message });
      return null;
    }
  }

  async extractPerplexityThread(thread) {
    try {
      if (thread.href) {
        await this.page.goto(`https://www.perplexity.ai${thread.href}`, {
          waitUntil: 'networkidle2'
        });
      }

      // Wait for content to load
      await this.page.waitForSelector('.question, .answer, .message', { timeout: 5000 });

      // Extract messages
      const messages = await this.page.evaluate(() => {
        const messageElements = document.querySelectorAll('.question, .answer, .message');
        const messages = [];

        messageElements.forEach(element => {
          const isQuestion = element.classList.contains('question') ||
                           element.querySelector('.user-message');
          const content = element.textContent.trim();

          if (content) {
            messages.push({
              role: isQuestion ? 'user' : 'assistant',
              content,
              timestamp: new Date().toISOString()
            });
          }
        });

        return messages;
      });

      // Extract sources/citations
      const sources = await this.page.evaluate(() => {
        const sourceElements = document.querySelectorAll('.source, .citation, a[href*="http"]');
        const sources = [];

        sourceElements.forEach(element => {
          const url = element.getAttribute('href');
          const title = element.textContent.trim();

          if (url && url.startsWith('http')) {
            sources.push({ url, title });
          }
        });

        return sources;
      });

      return {
        id: thread.id,
        source: 'perplexity',
        title: thread.title,
        created_at: new Date().toISOString(),
        messages,
        metadata: {
          extractedVia: 'browser-automation',
          url: thread.href,
          sources
        }
      };

    } catch (error) {
      logger.error(`Failed to extract Perplexity thread ${thread.id}`, { error: error.message });
      return null;
    }
  }
}

export default BrowserAutomation;

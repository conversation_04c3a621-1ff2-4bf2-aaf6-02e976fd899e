#!/usr/bin/env node

import fs from 'fs/promises';
import path from 'path';
import readline from 'readline';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.join(__dirname, '..');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

async function createDirectories() {
  const directories = [
    'data/threads',
    'data/results',
    'data/cache',
    'logs'
  ];

  console.log('📁 Creating directories...');

  for (const dir of directories) {
    const fullPath = path.join(projectRoot, dir);
    try {
      await fs.mkdir(fullPath, { recursive: true });
      console.log(`  ✅ Created: ${dir}`);
    } catch (error) {
      console.log(`  ❌ Failed to create ${dir}: ${error.message}`);
    }
  }
}

async function setupEnvironment() {
  console.log('\n🔧 Setting up environment variables...');

  const envPath = path.join(projectRoot, '.env');
  const envExamplePath = path.join(projectRoot, '.env.example');

  try {
    // Check if .env already exists
    await fs.access(envPath);
    console.log('  ℹ️  .env file already exists');

    const overwrite = await question('  Do you want to overwrite it? (y/N): ');
    if (overwrite.toLowerCase() !== 'y') {
      console.log('  ⏭️  Skipping environment setup');
      return;
    }
  } catch (error) {
    // .env doesn't exist, which is fine
  }

  // Copy .env.example to .env
  try {
    const envExample = await fs.readFile(envExamplePath, 'utf8');
    await fs.writeFile(envPath, envExample);
    console.log('  ✅ Created .env file from template');
  } catch (error) {
    console.log(`  ❌ Failed to create .env file: ${error.message}`);
    return;
  }

  // Prompt for API keys
  console.log('\n🔑 Please provide your API keys:');
  console.log('  (Press Enter to skip any platform you don\'t want to use)');
  console.log('  💡 You need at least one target LLM (Claude, Gemini, or OpenAI) for analysis');

  const apiKeys = {
    OPENAI_API_KEY: await question('  OpenAI API Key (recommended for embeddings): '),
    PERPLEXITY_API_KEY: await question('  Perplexity API Key: '),
    CLAUDE_API_KEY: await question('  Claude API Key (recommended for analysis): '),
    GEMINI_API_KEY: await question('  Gemini API Key: '),
    MISTRAL_API_KEY: await question('  Mistral API Key: '),
    COHERE_API_KEY: await question('  Cohere API Key: '),
    HUGGINGFACE_API_KEY: await question('  HuggingFace API Key: '),
    GROQ_API_KEY: await question('  Groq API Key: ')
  };

  // Update .env file with provided keys
  let envContent = await fs.readFile(envPath, 'utf8');

  for (const [key, value] of Object.entries(apiKeys)) {
    if (value.trim()) {
      const regex = new RegExp(`^${key}=.*$`, 'm');
      envContent = envContent.replace(regex, `${key}=${value.trim()}`);
      console.log(`  ✅ Set ${key}`);
    }
  }

  await fs.writeFile(envPath, envContent);
  console.log('  ✅ Environment configuration updated');
}

async function installDependencies() {
  console.log('\n📦 Installing dependencies...');

  const { spawn } = await import('child_process');

  return new Promise((resolve, reject) => {
    const npm = spawn('npm', ['install'], {
      cwd: projectRoot,
      stdio: 'inherit'
    });

    npm.on('close', (code) => {
      if (code === 0) {
        console.log('  ✅ Dependencies installed successfully');
        resolve();
      } else {
        console.log('  ❌ Failed to install dependencies');
        reject(new Error(`npm install failed with code ${code}`));
      }
    });

    npm.on('error', (error) => {
      console.log(`  ❌ Failed to run npm install: ${error.message}`);
      reject(error);
    });
  });
}

async function createGitignore() {
  console.log('\n📝 Creating .gitignore...');

  const gitignorePath = path.join(projectRoot, '.gitignore');
  const gitignoreContent = `# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Data and logs
data/
logs/
*.log

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo

# Temporary files
tmp/
temp/
.tmp/

# Coverage directory used by tools like istanbul
coverage/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache
`;

  try {
    await fs.writeFile(gitignorePath, gitignoreContent);
    console.log('  ✅ Created .gitignore');
  } catch (error) {
    console.log(`  ❌ Failed to create .gitignore: ${error.message}`);
  }
}

async function displayNextSteps() {
  console.log('\n🎉 Setup completed successfully!');
  console.log('\n📋 Next steps:');
  console.log('  1. Configure your API keys in the .env file if you haven\'t already');
  console.log('  2. Test the installation:');
  console.log('     npm start -- --help');
  console.log('  3. Retrieve some threads:');
  console.log('     npm start -- retrieve');
  console.log('  4. Run a search:');
  console.log('     npm start -- search "your query here"');
  console.log('  5. Run a full orchestration:');
  console.log('     npm start -- orchestrate "your query here"');
  console.log('  6. Start the web interface:');
  console.log('     npm start -- web');
  console.log('\n📚 For more information, check the README.md file');
  console.log('\n🔗 Web interface will be available at: http://localhost:3000');
}

async function main() {
  console.log('🚀 Thread-Merging Orchestrator Setup');
  console.log('=====================================\n');

  try {
    await createDirectories();
    await setupEnvironment();

    const installDeps = await question('\n📦 Install dependencies now? (Y/n): ');
    if (installDeps.toLowerCase() !== 'n') {
      await installDependencies();
    }

    await createGitignore();
    await displayNextSteps();

  } catch (error) {
    console.error('\n❌ Setup failed:', error.message);
    process.exit(1);
  } finally {
    rl.close();
  }
}

main();

#!/usr/bin/env node

/**
 * Universal Orchestration System Entry Point
 * 
 * Unified orchestration layer that combines:
 * - Thread-Merge Orchestration
 * - Code Orchestration  
 * - Meta-Orchestration System
 * - Darwin Gödel Machine (DGM) self-improvement layer
 * - External orchestrator integration (Airflow, Jenkins, Kubeflow, etc.)
 */

const { Command } = require('commander');
const chalk = require('chalk');
const ora = require('ora');
const inquirer = require('inquirer');

// Import the Universal Orchestrator
const UniversalOrchestrator = require('./core/universal-orchestrator');

class UniversalOrchestrationCLI {
  constructor() {
    this.universalOrchestrator = null;
    this.program = new Command();
    this.setupCommands();
  }
  
  setupCommands() {
    this.program
      .name('universal-orchestrator')
      .description('Universal Orchestration System - Unified AI Assistant Orchestration')
      .version('1.0.0');
    
    // Initialize command
    this.program
      .command('init')
      .description('Initialize the universal orchestration system')
      .option('--force', 'Force reinitialization')
      .option('--config <path>', 'Custom configuration path')
      .action(this.handleInit.bind(this));
    
    // Start command
    this.program
      .command('start')
      .description('Start the universal orchestration system')
      .option('--daemon', 'Run as daemon')
      .option('--port <port>', 'API server port', '4000')
      .option('--enable-dgm', 'Enable Darwin Gödel Machine layer')
      .action(this.handleStart.bind(this));
    
    // Execute command
    this.program
      .command('execute')
      .description('Execute a universal orchestration request')
      .option('--type <type>', 'Task type (code-generation, thread-merge, project-planning, etc.)')
      .option('--description <desc>', 'Task description')
      .option('--orchestrator <orch>', 'Preferred orchestrator (meta, thread, universal, external:name)')
      .option('--interactive', 'Interactive mode')
      .action(this.handleExecute.bind(this));
    
    // Status command
    this.program
      .command('status')
      .description('Show universal orchestration system status')
      .option('--detailed', 'Show detailed status')
      .option('--json', 'Output as JSON')
      .action(this.handleStatus.bind(this));
    
    // Orchestrator management
    this.program
      .command('orchestrators')
      .description('Manage orchestrators')
      .addCommand(this.createOrchestratorCommands());
    
    // Task routing commands
    this.program
      .command('routing')
      .description('Task routing management')
      .addCommand(this.createRoutingCommands());
    
    // DGM commands
    this.program
      .command('dgm')
      .description('Darwin Gödel Machine management')
      .addCommand(this.createDGMCommands());
    
    // Meta-orchestration commands
    this.program
      .command('meta')
      .description('Meta-orchestration management')
      .addCommand(this.createMetaOrchestrationCommands());
    
    // Dashboard command
    this.program
      .command('dashboard')
      .description('Launch universal orchestration dashboard')
      .option('--port <port>', 'Dashboard port', '9000')
      .action(this.handleDashboard.bind(this));
    
    // Stop command
    this.program
      .command('stop')
      .description('Stop the universal orchestration system')
      .action(this.handleStop.bind(this));
  }
  
  createOrchestratorCommands() {
    const orchCmd = new Command('orchestrators');
    
    orchCmd
      .command('list')
      .description('List all available orchestrators')
      .action(this.handleOrchestratorsList.bind(this));
    
    orchCmd
      .command('status')
      .description('Show orchestrator status')
      .argument('[orchestrator]', 'Specific orchestrator to check')
      .action(this.handleOrchestratorStatus.bind(this));
    
    orchCmd
      .command('test')
      .description('Test orchestrator connectivity')
      .argument('<orchestrator>', 'Orchestrator to test')
      .action(this.handleOrchestratorTest.bind(this));
    
    return orchCmd;
  }
  
  createRoutingCommands() {
    const routingCmd = new Command('routing');
    
    routingCmd
      .command('rules')
      .description('Show routing rules')
      .action(this.handleRoutingRules.bind(this));
    
    routingCmd
      .command('metrics')
      .description('Show routing metrics')
      .action(this.handleRoutingMetrics.bind(this));
    
    routingCmd
      .command('sessions')
      .description('Show active routing sessions')
      .action(this.handleRoutingSessions.bind(this));
    
    return routingCmd;
  }
  
  createDGMCommands() {
    const dgmCmd = new Command('dgm');
    
    dgmCmd
      .command('status')
      .description('Show DGM status and evolution history')
      .action(this.handleDGMStatus.bind(this));
    
    dgmCmd
      .command('evolve')
      .description('Trigger manual evolution cycle')
      .action(this.handleDGMEvolve.bind(this));
    
    dgmCmd
      .command('archive')
      .description('Show evolution archive')
      .action(this.handleDGMArchive.bind(this));
    
    return dgmCmd;
  }
  
  createMetaOrchestrationCommands() {
    const metaCmd = new Command('meta');
    
    metaCmd
      .command('external')
      .description('List external orchestrators')
      .action(this.handleMetaExternal.bind(this));
    
    metaCmd
      .command('delegate')
      .description('Delegate task to external orchestrator')
      .argument('<orchestrator>', 'External orchestrator name')
      .argument('<task>', 'Task description')
      .action(this.handleMetaDelegate.bind(this));
    
    return metaCmd;
  }
  
  async handleInit(options) {
    const spinner = ora('Initializing Universal Orchestration System...').start();
    
    try {
      this.universalOrchestrator = new UniversalOrchestrator(options);
      await this.universalOrchestrator.initialize();
      
      spinner.succeed('Universal Orchestration System initialized successfully');
      
      console.log(chalk.green('\n✅ System ready! Available commands:'));
      console.log(chalk.cyan('  universal-orchestrator start        - Start the system'));
      console.log(chalk.cyan('  universal-orchestrator execute      - Execute universal requests'));
      console.log(chalk.cyan('  universal-orchestrator status       - Check system status'));
      console.log(chalk.cyan('  universal-orchestrator orchestrators - Manage orchestrators'));
      console.log(chalk.cyan('  universal-orchestrator dgm          - Darwin Gödel Machine controls'));
      console.log(chalk.cyan('  universal-orchestrator dashboard    - Launch web dashboard'));
      
    } catch (error) {
      spinner.fail('Failed to initialize Universal Orchestration System');
      console.error(chalk.red('Error:', error.message));
      process.exit(1);
    }
  }
  
  async handleStart(options) {
    const spinner = ora('Starting Universal Orchestration System...').start();
    
    try {
      if (!this.universalOrchestrator) {
        this.universalOrchestrator = new UniversalOrchestrator({
          enableDGM: options.enableDgm
        });
        await this.universalOrchestrator.initialize();
      }
      
      spinner.succeed('Universal Orchestration System started');
      
      console.log(chalk.green('\n🌐 Universal Orchestration System is running'));
      console.log(chalk.blue('🎭 Meta-Orchestration: Enabled'));
      console.log(chalk.blue('🧵 Thread-Merge Orchestration: Enabled'));
      console.log(chalk.blue('💻 Code Orchestration: Enabled'));
      
      if (options.enableDgm) {
        console.log(chalk.blue('🧬 Darwin Gödel Machine: Enabled'));
      }
      
      if (options.daemon) {
        console.log(chalk.green(`🚀 API Server running on port ${options.port}`));
        console.log(chalk.blue('Press Ctrl+C to stop'));
        
        // Keep process alive
        process.on('SIGINT', async () => {
          console.log(chalk.yellow('\n🛑 Shutting down...'));
          await this.universalOrchestrator.shutdown();
          process.exit(0);
        });
      }
      
    } catch (error) {
      spinner.fail('Failed to start Universal Orchestration System');
      console.error(chalk.red('Error:', error.message));
      process.exit(1);
    }
  }
  
  async handleExecute(options) {
    try {
      if (!this.universalOrchestrator) {
        this.universalOrchestrator = new UniversalOrchestrator();
        await this.universalOrchestrator.initialize();
      }
      
      let request;
      
      if (options.interactive) {
        request = await this.interactiveRequestBuilder();
      } else {
        request = {
          type: options.type || 'project-planning',
          description: options.description || 'No description provided',
          preferredOrchestrator: options.orchestrator,
          timestamp: Date.now()
        };
      }
      
      const spinner = ora('Executing universal orchestration request...').start();
      
      const result = await this.universalOrchestrator.orchestrate(request);
      
      spinner.succeed('Universal orchestration completed successfully');
      
      console.log(chalk.green('\n✅ Orchestration Results:'));
      console.log(JSON.stringify(result, null, 2));
      
    } catch (error) {
      console.error(chalk.red('❌ Orchestration failed:', error.message));
      process.exit(1);
    }
  }
  
  async interactiveRequestBuilder() {
    const answers = await inquirer.prompt([
      {
        type: 'list',
        name: 'type',
        message: 'What type of orchestration request is this?',
        choices: [
          { name: 'Project Planning (Universal)', value: 'project-planning' },
          { name: 'Architecture Design (Universal)', value: 'architecture-design' },
          { name: 'Feature Implementation (Code)', value: 'feature-implementation' },
          { name: 'Code Generation (Code)', value: 'code-generation' },
          { name: 'Thread Merge (Thread)', value: 'thread-merge' },
          { name: 'Conversation Orchestration (Thread)', value: 'conversation-orchestration' },
          { name: 'ML Training (External)', value: 'ml-training' },
          { name: 'CI/CD Deployment (External)', value: 'ci-cd-deployment' }
        ]
      },
      {
        type: 'input',
        name: 'description',
        message: 'Describe your orchestration request:',
        validate: input => input.length > 0 || 'Description is required'
      },
      {
        type: 'list',
        name: 'orchestrator',
        message: 'Preferred orchestrator (or auto-select):',
        choices: [
          { name: 'Auto-select (Recommended)', value: 'auto' },
          { name: 'Universal Orchestrator', value: 'universal' },
          { name: 'Meta-Orchestrator', value: 'meta' },
          { name: 'Thread Orchestrator', value: 'thread' },
          { name: 'External: Airflow', value: 'external:airflow' },
          { name: 'External: Jenkins', value: 'external:jenkins' },
          { name: 'External: Kubeflow', value: 'external:kubeflow' }
        ]
      },
      {
        type: 'confirm',
        name: 'enableDGM',
        message: 'Enable Darwin Gödel Machine evolution tracking?',
        default: true
      }
    ]);
    
    return {
      ...answers,
      preferredOrchestrator: answers.orchestrator === 'auto' ? null : answers.orchestrator,
      timestamp: Date.now()
    };
  }
  
  async handleStatus(options) {
    try {
      if (!this.universalOrchestrator) {
        console.log(chalk.yellow('⚠️ Universal Orchestration System not running'));
        return;
      }
      
      const status = this.universalOrchestrator.getUniversalStatus();
      
      if (options.json) {
        console.log(JSON.stringify(status, null, 2));
      } else {
        this.displayUniversalStatus(status, options.detailed);
      }
      
    } catch (error) {
      console.error(chalk.red('❌ Failed to get status:', error.message));
    }
  }
  
  displayUniversalStatus(status, detailed = false) {
    console.log(chalk.blue.bold('\n🌐 Universal Orchestration System Status\n'));
    
    console.log(chalk.green(`Status: ${status.status}`));
    console.log(chalk.green(`Uptime: ${Math.floor(status.uptime / 1000)}s`));
    console.log(chalk.green(`Active Tasks: ${status.activeTasks}`));
    console.log(chalk.green(`Active Threads: ${status.activeThreads}`));
    
    console.log(chalk.blue('\n📊 Universal Metrics:'));
    console.log(`  Total Tasks: ${status.metrics.totalTasks}`);
    console.log(`  Total Threads: ${status.metrics.totalThreads}`);
    console.log(`  Task Success Rate: ${((status.metrics.successfulTasks / status.metrics.totalTasks) * 100 || 0).toFixed(1)}%`);
    console.log(`  Thread Success Rate: ${((status.metrics.successfulThreads / status.metrics.totalThreads) * 100 || 0).toFixed(1)}%`);
    console.log(`  Avg Task Time: ${status.metrics.averageTaskTime.toFixed(0)}ms`);
    console.log(`  Avg Thread Time: ${status.metrics.averageThreadTime.toFixed(0)}ms`);
    
    if (detailed) {
      console.log(chalk.blue('\n🎭 Orchestrator Status:'));
      for (const [name, orchStatus] of Object.entries(status.orchestrators)) {
        if (orchStatus) {
          const statusIcon = orchStatus.status === 'ready' ? '✅' : '❌';
          console.log(`  ${statusIcon} ${name}: ${orchStatus.status}`);
        }
      }
      
      console.log(chalk.blue('\n🎯 Universal Roles:'));
      status.universalRoles.forEach(role => {
        console.log(`  • ${role}`);
      });
      
      console.log(chalk.blue('\n📋 Supported Task Types:'));
      status.supportedTaskTypes.forEach(taskType => {
        console.log(`  • ${taskType}`);
      });
    }
  }
  
  async handleDashboard(options) {
    try {
      if (!this.universalOrchestrator) {
        this.universalOrchestrator = new UniversalOrchestrator();
        await this.universalOrchestrator.initialize();
      }
      
      console.log(chalk.green(`🌐 Universal Orchestration Dashboard running at http://localhost:${options.port}`));
      console.log(chalk.blue('Press Ctrl+C to stop'));
      
      // In a real implementation, this would start a web dashboard
      process.on('SIGINT', async () => {
        console.log(chalk.yellow('\n🛑 Shutting down dashboard...'));
        process.exit(0);
      });
      
    } catch (error) {
      console.error(chalk.red('❌ Failed to start dashboard:', error.message));
    }
  }
  
  async handleStop() {
    try {
      if (this.universalOrchestrator) {
        await this.universalOrchestrator.shutdown();
        console.log(chalk.green('✅ Universal Orchestration System stopped'));
      } else {
        console.log(chalk.yellow('⚠️ System not running'));
      }
    } catch (error) {
      console.error(chalk.red('❌ Failed to stop system:', error.message));
    }
  }
  
  async run() {
    try {
      await this.program.parseAsync(process.argv);
    } catch (error) {
      console.error(chalk.red('❌ CLI Error:', error.message));
      process.exit(1);
    }
  }
}

// Main execution
if (require.main === module) {
  const cli = new UniversalOrchestrationCLI();
  cli.run().catch(error => {
    console.error(chalk.red('❌ Fatal error:', error.message));
    process.exit(1);
  });
}

module.exports = { UniversalOrchestrationCLI, UniversalOrchestrator };

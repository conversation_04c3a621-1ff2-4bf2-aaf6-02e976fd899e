/**
 * Complete DGM Example
 * 
 * This example demonstrates the full capabilities of the Darwin Gödel Machine:
 * - System initialization and configuration
 * - Evolution process management
 * - Human oversight and safety features
 * - Performance monitoring and analysis
 * - Data export and visualization
 */

const { DarwinGodelMachine } = require('../index');
const chalk = require('chalk');
const fs = require('fs').promises;
const path = require('path');

class DGMExample {
  constructor() {
    this.dgm = null;
    this.isRunning = false;
    this.evolutionStats = {
      startTime: null,
      generations: 0,
      bestFitness: 0,
      improvements: 0
    };
  }

  /**
   * Run the complete DGM example
   */
  async run() {
    console.log(chalk.blue.bold('🧬 Darwin Gödel Machine - Complete Example'));
    console.log(chalk.gray('This example demonstrates the full DGM system capabilities\n'));

    try {
      // Step 1: Initialize the system
      await this.initializeSystem();
      
      // Step 2: Configure safety and monitoring
      await this.setupSafetyAndMonitoring();
      
      // Step 3: Start evolution with custom parameters
      await this.startEvolutionProcess();
      
      // Step 4: Monitor and interact with the system
      await this.monitorEvolution();
      
      // Step 5: Demonstrate human oversight
      await this.demonstrateHumanOversight();
      
      // Step 6: Analyze results and export data
      await this.analyzeAndExport();
      
      // Step 7: Cleanup
      await this.cleanup();
      
    } catch (error) {
      console.error(chalk.red(`❌ Example failed: ${error.message}`));
      await this.cleanup();
      process.exit(1);
    }
  }

  /**
   * Step 1: Initialize the DGM system
   */
  async initializeSystem() {
    console.log(chalk.blue('📋 Step 1: Initializing DGM System'));
    
    // Create DGM instance with custom configuration
    this.dgm = new DarwinGodelMachine({
      configPath: path.join(__dirname, 'example-config.json')
    });
    
    // Initialize with existing orchestrators (simulated)
    const existingOrchestrator = this.createMockOrchestrator();
    const threadMergingOrchestrator = this.createMockThreadMerging();
    
    await this.dgm.initialize(existingOrchestrator, threadMergingOrchestrator);
    
    console.log(chalk.green('✅ DGM system initialized successfully'));
    console.log(chalk.gray(`   Integration mode: ${this.dgm.integration.integrationMode}`));
    console.log(chalk.gray(`   Migration phase: ${this.dgm.integration.migrationPhase}\n`));
  }

  /**
   * Step 2: Setup safety and monitoring
   */
  async setupSafetyAndMonitoring() {
    console.log(chalk.blue('🛡️  Step 2: Setting up Safety and Monitoring'));
    
    const dgmEngine = this.dgm.integration.dgmEngine;
    
    // Setup event handlers for monitoring
    dgmEngine.on('generationCompleted', (generation) => {
      this.evolutionStats.generations = generation.number;
      this.evolutionStats.bestFitness = generation.bestFitness;
      
      if (generation.bestFitness > this.evolutionStats.bestFitness) {
        this.evolutionStats.improvements++;
      }
      
      console.log(chalk.cyan(`🧬 Generation ${generation.number} completed:`));
      console.log(chalk.gray(`   Best fitness: ${generation.bestFitness.toFixed(3)}`));
      console.log(chalk.gray(`   Average fitness: ${generation.averageFitness.toFixed(3)}`));
      console.log(chalk.gray(`   Survivors: ${generation.survivors}`));
    });
    
    dgmEngine.on('agentCreated', (agent) => {
      console.log(chalk.green(`🤖 New agent created: ${agent.id.substring(0, 8)} (fitness: ${agent.fitness.toFixed(3)})`));
    });
    
    dgmEngine.on('approvalRequired', (request) => {
      console.log(chalk.yellow(`👤 Human approval required for agent ${request.agentId.substring(0, 8)}`));
      console.log(chalk.gray(`   Risk level: ${request.riskAssessment.riskLevel}`));
      console.log(chalk.gray(`   Risk score: ${request.riskAssessment.riskScore.toFixed(3)}`));
      
      // Auto-approve low-risk agents for demo
      if (request.riskAssessment.riskLevel === 'low') {
        setTimeout(async () => {
          await dgmEngine.safetyManager.approveAgent(request.agentId, 'auto-demo');
          console.log(chalk.green(`✅ Agent ${request.agentId.substring(0, 8)} auto-approved`));
        }, 2000);
      }
    });
    
    dgmEngine.on('agentQuarantined', (event) => {
      console.log(chalk.red(`🔒 Agent ${event.agentId.substring(0, 8)} quarantined: ${event.reason}`));
    });
    
    dgmEngine.on('emergencyRollback', (event) => {
      console.log(chalk.red.bold(`🚨 EMERGENCY ROLLBACK: ${event.reason}`));
    });
    
    console.log(chalk.green('✅ Safety and monitoring configured'));
    console.log(chalk.gray('   Event handlers registered for real-time monitoring\n'));
  }

  /**
   * Step 3: Start evolution process
   */
  async startEvolutionProcess() {
    console.log(chalk.blue('🚀 Step 3: Starting Evolution Process'));
    
    // Start the DGM system
    await this.dgm.start({
      startEvolution: false // We'll start evolution manually
    });
    
    // Configure evolution parameters
    const evolutionOptions = {
      maxGenerations: 20,
      targetFitness: 0.9,
      populationSize: 15,
      mutationRate: 0.3,
      crossoverRate: 0.7
    };
    
    console.log(chalk.gray('Evolution parameters:'));
    Object.entries(evolutionOptions).forEach(([key, value]) => {
      console.log(chalk.gray(`   ${key}: ${value}`));
    });
    
    // Start evolution
    this.evolutionStats.startTime = Date.now();
    await this.dgm.startEvolution(evolutionOptions);
    
    this.isRunning = true;
    console.log(chalk.green('✅ Evolution process started\n'));
  }

  /**
   * Step 4: Monitor evolution progress
   */
  async monitorEvolution() {
    console.log(chalk.blue('📊 Step 4: Monitoring Evolution Progress'));
    
    // Monitor for 30 seconds or until target fitness reached
    const monitoringDuration = 30000; // 30 seconds
    const startTime = Date.now();
    
    while (Date.now() - startTime < monitoringDuration && this.isRunning) {
      await new Promise(resolve => setTimeout(resolve, 5000)); // Check every 5 seconds
      
      const status = this.dgm.getStatus();
      const stats = this.dgm.getStats();
      
      if (stats && stats.dgm) {
        console.log(chalk.cyan('📈 Current Status:'));
        console.log(chalk.gray(`   Generation: ${stats.dgm.currentGeneration}`));
        console.log(chalk.gray(`   Population: ${stats.dgm.populationSize} agents`));
        console.log(chalk.gray(`   Running: ${stats.dgm.isRunning ? 'Yes' : 'No'}`));
        
        // Check if target fitness reached
        if (this.evolutionStats.bestFitness >= 0.9) {
          console.log(chalk.green('🎯 Target fitness reached!'));
          break;
        }
      }
    }
    
    console.log(chalk.green('✅ Monitoring phase completed\n'));
  }

  /**
   * Step 5: Demonstrate human oversight
   */
  async demonstrateHumanOversight() {
    console.log(chalk.blue('👤 Step 5: Demonstrating Human Oversight'));
    
    // Get current best agent
    const bestAgent = await this.dgm.getBestAgent();
    
    if (bestAgent) {
      console.log(chalk.cyan(`🏆 Best agent: ${bestAgent.id.substring(0, 8)}`));
      console.log(chalk.gray(`   Fitness: ${bestAgent.fitness.toFixed(3)}`));
      console.log(chalk.gray(`   Generation: ${bestAgent.generation}`));
      console.log(chalk.gray(`   Type: ${bestAgent.type}`));
      
      // Simulate human review process
      console.log(chalk.yellow('🔍 Simulating human review process...'));
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Approve the agent
      const safetyManager = this.dgm.integration.dgmEngine.safetyManager;
      await safetyManager.approveAgent(bestAgent.id, 'human-reviewer');
      
      console.log(chalk.green(`✅ Agent ${bestAgent.id.substring(0, 8)} approved by human reviewer`));
    } else {
      console.log(chalk.yellow('⚠️  No agents available for review'));
    }
    
    // Demonstrate safety features
    console.log(chalk.cyan('🛡️  Safety system status:'));
    const safetyStats = this.dgm.integration.dgmEngine.safetyManager.getSafetyStats();
    console.log(chalk.gray(`   Quarantined agents: ${safetyStats.quarantinedAgents}`));
    console.log(chalk.gray(`   Pending approvals: ${safetyStats.pendingApprovals}`));
    console.log(chalk.gray(`   Risk assessments: ${safetyStats.riskAssessments}`));
    
    console.log(chalk.green('✅ Human oversight demonstration completed\n'));
  }

  /**
   * Step 6: Analyze results and export data
   */
  async analyzeAndExport() {
    console.log(chalk.blue('📊 Step 6: Analyzing Results and Exporting Data'));
    
    // Stop evolution
    await this.dgm.stopEvolution();
    this.isRunning = false;
    
    // Calculate evolution duration
    const duration = Date.now() - this.evolutionStats.startTime;
    const durationMinutes = (duration / 60000).toFixed(1);
    
    // Display evolution summary
    console.log(chalk.cyan('🧬 Evolution Summary:'));
    console.log(chalk.gray(`   Duration: ${durationMinutes} minutes`));
    console.log(chalk.gray(`   Generations: ${this.evolutionStats.generations}`));
    console.log(chalk.gray(`   Best fitness: ${this.evolutionStats.bestFitness.toFixed(3)}`));
    console.log(chalk.gray(`   Improvements: ${this.evolutionStats.improvements}`));
    
    // Export comprehensive data
    console.log(chalk.cyan('📤 Exporting system data...'));
    
    const exportData = await this.dgm.exportData('all');
    const exportPath = path.join(__dirname, 'dgm-export.json');
    
    await fs.writeFile(exportPath, JSON.stringify(exportData, null, 2));
    console.log(chalk.green(`✅ Data exported to: ${exportPath}`));
    
    // Generate analysis report
    const report = this.generateAnalysisReport(exportData);
    const reportPath = path.join(__dirname, 'dgm-analysis-report.md');
    
    await fs.writeFile(reportPath, report);
    console.log(chalk.green(`✅ Analysis report generated: ${reportPath}`));
    
    console.log(chalk.green('✅ Analysis and export completed\n'));
  }

  /**
   * Step 7: Cleanup
   */
  async cleanup() {
    console.log(chalk.blue('🧹 Step 7: Cleanup'));
    
    if (this.dgm) {
      await this.dgm.stop();
      console.log(chalk.green('✅ DGM system stopped'));
    }
    
    console.log(chalk.green('✅ Cleanup completed'));
    console.log(chalk.blue.bold('\n🎉 DGM Example completed successfully!'));
    console.log(chalk.gray('Check the generated files for detailed analysis results.\n'));
  }

  /**
   * Create mock orchestrator for demonstration
   */
  createMockOrchestrator() {
    return {
      execute: async (request) => {
        // Simulate orchestration work
        await new Promise(resolve => setTimeout(resolve, 100));
        return {
          success: true,
          result: 'Mock orchestration completed',
          tools: ['augmentCode'],
          duration: 100
        };
      }
    };
  }

  /**
   * Create mock thread merging orchestrator
   */
  createMockThreadMerging() {
    return {
      orchestrate: async (threads) => {
        // Simulate thread merging
        await new Promise(resolve => setTimeout(resolve, 150));
        return {
          success: true,
          mergedContent: 'Mock merged content',
          threadsProcessed: threads?.length || 1,
          duration: 150
        };
      }
    };
  }

  /**
   * Generate analysis report
   */
  generateAnalysisReport(exportData) {
    const report = `# DGM Analysis Report

Generated: ${new Date().toISOString()}

## Evolution Summary

- **Total Generations**: ${exportData.evolution?.length || 0}
- **Total Agents**: ${exportData.agents?.length || 0}
- **Best Fitness**: ${exportData.archive?.fitnessDistribution?.max?.toFixed(3) || 'N/A'}
- **Average Fitness**: ${exportData.archive?.fitnessDistribution?.average?.toFixed(3) || 'N/A'}

## Agent Analysis

### Top Performing Agents

${exportData.agents?.slice(0, 5).map((agent, index) => 
  `${index + 1}. Agent ${agent.id.substring(0, 8)} - Fitness: ${agent.fitness.toFixed(3)} (Gen ${agent.generation})`
).join('\n') || 'No agents available'}

### Agent Types Distribution

${Object.entries(exportData.archive?.agentTypes || {}).map(([type, count]) => 
  `- ${type}: ${count} agents`
).join('\n') || 'No type data available'}

## Genealogy Insights

- **Total Lineages**: ${exportData.genealogy?.lineages || 0}
- **Founders**: ${exportData.genealogy?.founders || 0}
- **Average Children**: ${exportData.genealogy?.averageChildren?.toFixed(2) || 'N/A'}
- **Max Children**: ${exportData.genealogy?.maxChildren || 0}

## Performance Metrics

- **Average Performance**: ${exportData.metrics?.averagePerformance?.toFixed(3) || 'N/A'}
- **Average Reliability**: ${exportData.metrics?.averageReliability?.toFixed(3) || 'N/A'}
- **Average Functionality**: ${exportData.metrics?.averageFunctionality?.toFixed(3) || 'N/A'}
- **Average Safety**: ${exportData.metrics?.averageSafety?.toFixed(3) || 'N/A'}

## Evolution Timeline

${exportData.evolution?.map(gen => 
  `- Generation ${gen.number}: Best ${gen.bestFitness.toFixed(3)}, Avg ${gen.averageFitness.toFixed(3)}, Survivors ${gen.survivors}`
).join('\n') || 'No evolution data available'}

## Recommendations

Based on the analysis:

1. **Performance**: ${exportData.metrics?.averagePerformance > 0.8 ? 'Good performance metrics' : 'Consider performance optimizations'}
2. **Safety**: ${exportData.metrics?.averageSafety > 0.9 ? 'Excellent safety scores' : 'Review safety measures'}
3. **Diversity**: ${exportData.genealogy?.lineages > 3 ? 'Good genetic diversity' : 'Consider diversity enhancement'}

---

*Report generated by Darwin Gödel Machine Analysis System*
`;

    return report;
  }
}

// Run the example if called directly
if (require.main === module) {
  const example = new DGMExample();
  example.run().catch(error => {
    console.error(chalk.red.bold(`❌ Example failed: ${error.message}`));
    process.exit(1);
  });
}

module.exports = DGMExample;

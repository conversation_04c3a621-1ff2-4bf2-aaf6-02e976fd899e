/**
 * Configuration Manager for Darwin Gödel Machine
 * 
 * Manages all configuration settings for the DGM system including:
 * - Evolution parameters
 * - Evaluation criteria
 * - Safety settings
 * - Performance thresholds
 */

const fs = require('fs').promises;
const path = require('path');
const chalk = require('chalk');

class ConfigManager {
  constructor(configPath = null) {
    this.configPath = configPath || path.join(process.cwd(), 'ai-orchestration', 'dgm', 'config', 'dgm-config.json');
    this.config = {};
    this.defaultConfig = this.getDefaultConfig();
  }

  /**
   * Initialize configuration
   */
  async initialize() {
    try {
      await this.loadConfig();
      await this.validateConfig();
      console.log(chalk.green('✅ DGM configuration loaded successfully'));
    } catch (error) {
      console.warn(chalk.yellow(`⚠️  Using default configuration: ${error.message}`));
      this.config = { ...this.defaultConfig };
      await this.saveConfig();
    }
  }

  /**
   * Get default configuration
   */
  getDefaultConfig() {
    return {
      // Evolution Parameters
      evolution: {
        populationSize: 20,
        maxGenerations: 100,
        targetFitness: 0.95,
        offspringCount: 10,
        mutationRate: 0.3,
        crossoverRate: 0.7,
        elitismRate: 0.1,
        diversityThreshold: 0.8,
        stagnationLimit: 10
      },

      // Genetic Algorithm Settings
      genetics: {
        selectionMethod: 'tournament', // tournament, roulette, rank
        tournamentSize: 3,
        mutationStrategies: [
          'error_handling',
          'performance_optimization',
          'new_feature',
          'algorithm_improvement',
          'code_refactoring'
        ],
        crossoverStrategies: [
          'single_point',
          'two_point',
          'uniform',
          'semantic'
        ]
      },

      // Evaluation Framework
      evaluation: {
        benchmarkSuites: [
          'swe-bench',
          'polyglot',
          'custom-orchestration',
          'performance-tests'
        ],
        fitnessWeights: {
          performance: 0.4,
          reliability: 0.3,
          functionality: 0.2,
          safety: 0.1
        },
        validationCriteria: {
          syntaxCheck: true,
          securityScan: true,
          performanceThreshold: 0.8,
          reliabilityThreshold: 0.9,
          safetyThreshold: 0.95
        },
        timeoutLimits: {
          agentExecution: 300000, // 5 minutes
          benchmarkSuite: 1800000, // 30 minutes
          validation: 60000 // 1 minute
        }
      },

      // Archive Management
      archive: {
        storageType: 'git', // git, filesystem, database
        maxVersions: 1000,
        compressionEnabled: true,
        backupInterval: 86400000, // 24 hours
        genealogyDepth: 10,
        performanceHistoryLength: 100
      },

      // Execution Environment
      execution: {
        sandboxDir: path.join(process.cwd(), 'dgm-sandbox'),
        isolationLevel: 'process', // process, container, vm
        resourceLimits: {
          memory: '512MB',
          cpu: '1',
          timeout: 300000
        },
        allowedModules: [
          'fs',
          'path',
          'crypto',
          'util',
          'events',
          'stream'
        ],
        blockedModules: [
          'child_process',
          'cluster',
          'dgram',
          'net',
          'tls'
        ]
      },

      // Safety and Security
      safety: {
        humanApprovalRequired: true,
        approvalThreshold: 0.8, // Fitness threshold requiring approval
        rollbackOnFailure: true,
        maxConsecutiveFailures: 3,
        quarantineEnabled: true,
        auditLogging: true,
        changeReviewRequired: true
      },

      // Monitoring and Logging
      monitoring: {
        metricsCollection: true,
        performanceTracking: true,
        errorTracking: true,
        evolutionHistory: true,
        dashboardEnabled: true,
        alerting: {
          enabled: true,
          channels: ['console', 'file'],
          thresholds: {
            fitnessDecline: 0.1,
            errorRate: 0.05,
            performanceDegradation: 0.2
          }
        }
      },

      // Integration Settings
      integration: {
        augmentCode: {
          enabled: true,
          apiEndpoint: 'http://localhost:3001',
          timeout: 30000
        },
        orchestrationSystem: {
          enabled: true,
          backupBeforeModification: true,
          testBeforeDeployment: true
        },
        externalBenchmarks: {
          swebench: {
            enabled: false,
            endpoint: 'https://api.swebench.com'
          },
          polyglot: {
            enabled: false,
            endpoint: 'https://api.polyglot.com'
          }
        }
      },

      // User Interface
      ui: {
        webDashboard: {
          enabled: true,
          port: 3002,
          autoRefresh: 5000
        },
        cli: {
          enabled: true,
          colorOutput: true,
          verboseLogging: false
        },
        api: {
          enabled: true,
          port: 3003,
          authentication: false
        }
      }
    };
  }

  /**
   * Load configuration from file
   */
  async loadConfig() {
    try {
      const configData = await fs.readFile(this.configPath, 'utf8');
      const loadedConfig = JSON.parse(configData);
      
      // Merge with defaults to ensure all required fields exist
      this.config = this.mergeWithDefaults(loadedConfig);
      
    } catch (error) {
      if (error.code === 'ENOENT') {
        throw new Error('Configuration file not found');
      }
      throw new Error(`Failed to load configuration: ${error.message}`);
    }
  }

  /**
   * Save configuration to file
   */
  async saveConfig() {
    try {
      // Ensure config directory exists
      const configDir = path.dirname(this.configPath);
      await fs.mkdir(configDir, { recursive: true });
      
      // Write configuration
      await fs.writeFile(
        this.configPath,
        JSON.stringify(this.config, null, 2),
        'utf8'
      );
      
      console.log(chalk.green(`✅ Configuration saved to ${this.configPath}`));
      
    } catch (error) {
      throw new Error(`Failed to save configuration: ${error.message}`);
    }
  }

  /**
   * Validate configuration
   */
  async validateConfig() {
    const errors = [];

    // Validate evolution parameters
    if (this.config.evolution.populationSize < 2) {
      errors.push('Population size must be at least 2');
    }

    if (this.config.evolution.mutationRate < 0 || this.config.evolution.mutationRate > 1) {
      errors.push('Mutation rate must be between 0 and 1');
    }

    if (this.config.evolution.crossoverRate < 0 || this.config.evolution.crossoverRate > 1) {
      errors.push('Crossover rate must be between 0 and 1');
    }

    // Validate fitness weights
    const weights = this.config.evaluation.fitnessWeights;
    const totalWeight = Object.values(weights).reduce((sum, weight) => sum + weight, 0);
    if (Math.abs(totalWeight - 1.0) > 0.001) {
      errors.push('Fitness weights must sum to 1.0');
    }

    // Validate resource limits
    if (this.config.execution.resourceLimits.timeout < 1000) {
      errors.push('Execution timeout must be at least 1000ms');
    }

    if (errors.length > 0) {
      throw new Error(`Configuration validation failed:\n${errors.join('\n')}`);
    }
  }

  /**
   * Get configuration value
   */
  get(path, defaultValue = undefined) {
    const keys = path.split('.');
    let value = this.config;

    for (const key of keys) {
      if (value && typeof value === 'object' && key in value) {
        value = value[key];
      } else {
        return defaultValue;
      }
    }

    return value;
  }

  /**
   * Set configuration value
   */
  set(path, value) {
    const keys = path.split('.');
    let current = this.config;

    for (let i = 0; i < keys.length - 1; i++) {
      const key = keys[i];
      if (!(key in current) || typeof current[key] !== 'object') {
        current[key] = {};
      }
      current = current[key];
    }

    current[keys[keys.length - 1]] = value;
  }

  /**
   * Merge loaded config with defaults
   */
  mergeWithDefaults(loadedConfig) {
    return this.deepMerge(this.defaultConfig, loadedConfig);
  }

  /**
   * Deep merge two objects
   */
  deepMerge(target, source) {
    const result = { ...target };

    for (const key in source) {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        result[key] = this.deepMerge(target[key] || {}, source[key]);
      } else {
        result[key] = source[key];
      }
    }

    return result;
  }

  /**
   * Get full configuration
   */
  getAll() {
    return { ...this.config };
  }

  /**
   * Update configuration and save
   */
  async update(updates) {
    this.config = this.deepMerge(this.config, updates);
    await this.validateConfig();
    await this.saveConfig();
  }

  /**
   * Reset to default configuration
   */
  async reset() {
    this.config = { ...this.defaultConfig };
    await this.saveConfig();
  }
}

module.exports = ConfigManager;

import { config } from '../config/index.js';
import { logger } from '../utils/logger.js';

// Import all API clients
import ClaudeClient from './claude-client.js';
import CohereClient from './cohere-client.js';
import Gemini<PERSON><PERSON> from './gemini-client.js';
import Hugging<PERSON><PERSON>Client from './huggingface-client.js';
import MistralClient from './mistral-client.js';
import OpenAIClient from './openai-client.js';
import PerplexityClient from './perplexity-client.js';

class UniversalAPIClient {
  constructor() {
    this.clients = {};
    this.initializeClients();
  }

  initializeClients() {
    const clientMap = {
      openai: OpenAIClient,
      chatgpt: OpenAIClient, // Alias
      perplexity: PerplexityClient,
      claude: ClaudeClient,
      gemini: GeminiClient,
      mistral: MistralClient,
      cohere: CohereClient,
      huggingface: HuggingFaceClient,
      llama: HuggingFaceClient // Alias for HuggingFace Llama models
    };

    // Initialize clients for platforms that have API keys
    Object.entries(clientMap).forEach(([platform, ClientClass]) => {
      try {
        const apiConfig = config.apis[platform] || config.apis[this.mapPlatformToConfig(platform)];

        if (apiConfig?.apiKey) {
          this.clients[platform] = new ClientClass();
          logger.info(`Initialized ${platform} client`);
        } else {
          logger.warn(`No API key found for ${platform}, skipping initialization`);
        }
      } catch (error) {
        logger.error(`Failed to initialize ${platform} client`, { error: error.message });
      }
    });

    logger.info(`Initialized ${Object.keys(this.clients).length} API clients`);
  }

  mapPlatformToConfig(platform) {
    const mapping = {
      chatgpt: 'openai',
      llama: 'huggingface'
    };
    return mapping[platform] || platform;
  }

  getAvailablePlatforms() {
    return Object.keys(this.clients);
  }

  getClient(platform) {
    const normalizedPlatform = platform.toLowerCase();
    const client = this.clients[normalizedPlatform];

    if (!client) {
      throw new Error(`No client available for platform: ${platform}. Available platforms: ${this.getAvailablePlatforms().join(', ')}`);
    }

    return client;
  }

  async generateCompletion(platform, messages, options = {}) {
    const client = this.getClient(platform);
    return await client.generateCompletion(messages, options);
  }

  async analyzeThreads(platform, mergedThreads, task = 'code_generation', options = {}) {
    const client = this.getClient(platform);

    if (!client.analyzeThreads) {
      // Fallback to generateCompletion for clients without analyzeThreads method
      const prompt = this.formatThreadsForBasicAnalysis(mergedThreads, task);
      const messages = [
        {
          role: 'system',
          content: this.getSystemPromptForTask(task)
        },
        {
          role: 'user',
          content: prompt
        }
      ];

      const analysis = await client.generateCompletion(messages, options);

      return {
        analysis,
        task,
        timestamp: new Date().toISOString(),
        model: options.model || 'default'
      };
    }

    return await client.analyzeThreads(mergedThreads, task, options);
  }

  async listConversations(platform, limit = 20) {
    const client = this.getClient(platform);

    if (!client.listConversations) {
      logger.warn(`Platform ${platform} doesn't support conversation listing`);
      return { conversations: [], requiresBrowserAutomation: true };
    }

    return await client.listConversations(limit);
  }

  async getConversation(platform, conversationId) {
    const client = this.getClient(platform);

    if (!client.getConversation) {
      logger.warn(`Platform ${platform} doesn't support conversation retrieval`);
      return {
        id: conversationId,
        messages: [],
        created_at: new Date().toISOString(),
        source: platform
      };
    }

    return await client.getConversation(conversationId);
  }

  async createEmbedding(text, platform = 'openai') {
    const client = this.getClient(platform);

    if (!client.createEmbedding) {
      // Fallback to OpenAI for embeddings if the platform doesn't support it
      if (platform !== 'openai' && this.clients.openai) {
        logger.info(`Platform ${platform} doesn't support embeddings, falling back to OpenAI`);
        return await this.clients.openai.createEmbedding(text);
      }
      throw new Error(`No embedding support available for platform: ${platform}`);
    }

    return await client.createEmbedding(text);
  }

  formatThreadsForBasicAnalysis(mergedThreads, task) {
    let prompt = `# Merged Thread Analysis Request\n\n`;
    prompt += `**Task**: ${task}\n`;
    prompt += `**Number of threads**: ${mergedThreads.length}\n`;
    prompt += `**Analysis timestamp**: ${new Date().toISOString()}\n\n`;

    prompt += `## Thread Contents\n\n`;

    mergedThreads.forEach((thread, index) => {
      prompt += `### Thread ${index + 1}: ${thread.source} (${thread.id})\n`;
      prompt += `**Created**: ${thread.created_at}\n`;
      prompt += `**Messages**: ${thread.messages.length}\n\n`;

      thread.messages.slice(0, 5).forEach((message, msgIndex) => { // Limit to first 5 messages
        prompt += `**Message ${msgIndex + 1}** (${message.role}):\n`;
        prompt += `${message.content.substring(0, 500)}${message.content.length > 500 ? '...' : ''}\n\n`;
      });

      if (thread.messages.length > 5) {
        prompt += `... and ${thread.messages.length - 5} more messages\n\n`;
      }

      if (thread.highlights && thread.highlights.length > 0) {
        prompt += `**Key Highlights**:\n`;
        thread.highlights.slice(0, 3).forEach(highlight => {
          prompt += `- ${highlight.text.substring(0, 200)}...\n`;
        });
        prompt += `\n`;
      }

      prompt += `---\n\n`;
    });

    prompt += `## Analysis Request\n\n`;
    prompt += `Please analyze the above merged threads and provide insights based on the specified task.`;

    return prompt;
  }

  getSystemPromptForTask(task) {
    const prompts = {
      code_generation: `You are an expert software developer. Analyze the provided conversation threads and generate practical, well-documented code solutions based on the discussions.`,

      code_analysis: `You are an expert code reviewer. Analyze the provided conversation threads and provide thorough code analysis, identifying improvements and best practices.`,

      summarization: `You are an expert technical analyst. Analyze the provided conversation threads and create a comprehensive summary of key insights and recommendations.`
    };

    return prompts[task] || prompts.code_generation;
  }

  async testConnection(platform) {
    try {
      const client = this.getClient(platform);

      // Try a simple completion to test the connection
      const testMessages = [
        { role: 'user', content: 'Hello, this is a connection test. Please respond with "Connection successful".' }
      ];

      const response = await client.generateCompletion(testMessages, { maxTokens: 50 });

      return {
        platform,
        status: 'success',
        response: response.substring(0, 100)
      };
    } catch (error) {
      return {
        platform,
        status: 'error',
        error: error.message
      };
    }
  }

  async testAllConnections() {
    const platforms = this.getAvailablePlatforms();
    const results = await Promise.allSettled(
      platforms.map(platform => this.testConnection(platform))
    );

    return results.map((result, index) => ({
      platform: platforms[index],
      ...result.value
    }));
  }

  getClientInfo() {
    return Object.keys(this.clients).map(platform => {
      const configKey = this.mapPlatformToConfig(platform);
      const apiConfig = config.apis[configKey];

      return {
        platform,
        model: apiConfig?.model || 'default',
        baseURL: apiConfig?.baseURL || 'unknown',
        hasApiKey: !!apiConfig?.apiKey,
        features: {
          completion: true,
          analysis: !!this.clients[platform].analyzeThreads,
          conversations: !!this.clients[platform].listConversations,
          embeddings: !!this.clients[platform].createEmbedding
        }
      };
    });
  }
}

export default UniversalAPIClient;

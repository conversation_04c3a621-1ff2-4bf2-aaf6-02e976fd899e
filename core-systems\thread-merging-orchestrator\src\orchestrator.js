import ThreadRetriever from './retrieval/thread-retriever.js';
import SearchEngine from './search/search-engine.js';
import ThreadMerger from './merging/thread-merger.js';
import UniversalAPIClient from './api/universal-api-client.js';
import config from './config/index.js'; // or wherever your config is

export default class ThreadMergingOrchestrator {
  constructor() {
    this.threadRetriever = new ThreadRetriever();
    this.searchEngine = new SearchEngine();
    this.threadMerger = new ThreadMerger();
    this.universalClient = new UniversalAPIClient();
    this.resultsDir = config.storage.results;
    this.ensureResultsDir();
  }

  ensureResultsDir() {
    // create results directory if it doesn’t exist
    // use fs/promises or sync version for simplicity
  }

  async orchestrate(query, options = {}) {
    // Your orchestration logic here
    // e.g. retrieve threads, search, merge, etc.
    return { success: true, query, options };
  }
}


    try {
      const result = {
        id: orchestrationId,
        query,
        startTime: new Date().toISOString(),
        options,
        steps: {}
      };

      // Step 1: Retrieve threads
      const retrievalStart = Date.now();
      logger.info('Step 1: Retrieving threads');

      let threads;
      if (options.useCache && !options.forceRefresh) {
        threads = await this.threadRetriever.loadCachedThreads();
        if (threads.length === 0) {
          threads = await this.threadRetriever.retrieveAllThreads(options);
        }
      } else {
        threads = await this.threadRetriever.retrieveAllThreads(options);
      }

      result.steps.retrieval = {
        duration: Date.now() - retrievalStart,
        threadsFound: threads.length,
        sources: [...new Set(threads.map(t => t.source))]
      };

      logPerformance('Thread Retrieval', result.steps.retrieval.duration, {
        threadsFound: threads.length
      });

      // Step 2: Search and rank threads
      const searchStart = Date.now();
      logger.info('Step 2: Searching and ranking threads');

      const searchResults = await this.searchEngine.searchThreads(threads, query, {
        limit: options.maxThreads || 10,
        semanticWeight: options.semanticWeight,
        keywordWeight: options.keywordWeight,
        contentWeight: options.contentWeight
      });

      result.steps.search = {
        duration: Date.now() - searchStart,
        relevantThreads: searchResults.length,
        averageScore: searchResults.length > 0
          ? searchResults.reduce((sum, r) => sum + r.combinedScore, 0) / searchResults.length
          : 0
      };

      logPerformance('Thread Search', result.steps.search.duration, {
        relevantThreads: searchResults.length
      });

      // Step 3: Merge and format threads
      const mergingStart = Date.now();
      logger.info('Step 3: Merging and formatting threads');

      const mergedResult = await this.threadMerger.mergeThreads(searchResults, {
        format: options.format || 'markdown',
        includeSummary: options.includeSummary !== false,
        deduplicationThreshold: options.deduplicationThreshold,
        maxChunkSize: options.maxChunkSize
      });

      result.steps.merging = {
        duration: Date.now() - mergingStart,
        originalThreads: mergedResult.metadata.originalCount,
        deduplicatedThreads: mergedResult.metadata.deduplicatedCount,
        chunks: mergedResult.chunks.length
      };

      logPerformance('Thread Merging', result.steps.merging.duration, {
        chunks: mergedResult.chunks.length
      });

      // Step 4: Generate analysis with target LLM
      const analysisStart = Date.now();
      logger.info('Step 4: Generating analysis with target LLM');

      const targetLLM = options.targetLLM || config.defaults.targetLLM;
      const task = options.task || 'code_generation';

      const analysis = await this.generateAnalysis(
        mergedResult.threads,
        task,
        targetLLM,
        options
      );

      result.steps.analysis = {
        duration: Date.now() - analysisStart,
        targetLLM,
        task,
        analysisLength: analysis.analysis.length
      };

      logPerformance('LLM Analysis', result.steps.analysis.duration, {
        targetLLM,
        task
      });

      // Compile final result
      result.endTime = new Date().toISOString();
      result.totalDuration = Date.now() - startTime;
      result.mergedThreads = mergedResult;
      result.analysis = analysis;

      // Save result
      await this.saveResult(result);

      logger.info('Thread merging orchestration completed', {
        orchestrationId,
        totalDuration: result.totalDuration,
        threadsProcessed: threads.length,
        relevantThreads: searchResults.length,
        finalAnalysisLength: analysis.analysis.length
      });

      return result;

    } catch (error) {
      logger.error('Thread merging orchestration failed', {
        orchestrationId,
        error: error.message,
        stack: error.stack
      });
      throw error;
    }
  }

  async generateAnalysis(threads, task, targetLLM, options = {}) {
    logger.info(`Generating analysis with ${targetLLM}`, { task, threadCount: threads.length });

    try {
      // Check if the platform is available
      const availablePlatforms = this.universalClient.getAvailablePlatforms();
      if (!availablePlatforms.includes(targetLLM.toLowerCase())) {
        throw new Error(`Platform ${targetLLM} is not available. Available platforms: ${availablePlatforms.join(', ')}`);
      }

      const analysis = await this.universalClient.analyzeThreads(targetLLM, threads, task, {
        model: options.model,
        maxTokens: options.maxTokens,
        temperature: options.temperature
      });

      return analysis;
    } catch (error) {
      logger.error(`Analysis generation failed with ${targetLLM}`, { error: error.message });
      throw error;
    }
  }

  async saveResult(result) {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `orchestration-${result.id}-${timestamp}.json`;
      const filepath = path.join(this.resultsDir, filename);

      await fs.writeFile(filepath, JSON.stringify(result, null, 2));

      // Also save the analysis separately for easy access
      const analysisFilename = `analysis-${result.id}-${timestamp}.md`;
      const analysisFilepath = path.join(this.resultsDir, analysisFilename);
      await fs.writeFile(analysisFilepath, result.analysis.analysis);

      logger.info('Orchestration result saved', {
        resultFile: filename,
        analysisFile: analysisFilename
      });
    } catch (error) {
      logger.error('Failed to save orchestration result', { error: error.message });
    }
  }

  async getOrchestrationHistory(limit = 10) {
    try {
      const files = await fs.readdir(this.resultsDir);
      const orchestrationFiles = files
        .filter(file => file.startsWith('orchestration-') && file.endsWith('.json'))
        .sort()
        .reverse()
        .slice(0, limit);

      const history = await Promise.all(
        orchestrationFiles.map(async (file) => {
          try {
            const filepath = path.join(this.resultsDir, file);
            const content = await fs.readFile(filepath, 'utf8');
            const result = JSON.parse(content);

            return {
              id: result.id,
              query: result.query,
              startTime: result.startTime,
              totalDuration: result.totalDuration,
              threadsProcessed: result.steps.retrieval?.threadsFound || 0,
              relevantThreads: result.steps.search?.relevantThreads || 0,
              targetLLM: result.steps.analysis?.targetLLM,
              task: result.steps.analysis?.task
            };
          } catch (error) {
            logger.warn(`Failed to parse orchestration file ${file}`, { error: error.message });
            return null;
          }
        })
      );

      return history.filter(h => h !== null);
    } catch (error) {
      logger.error('Failed to get orchestration history', { error: error.message });
      return [];
    }
  }

  async getOrchestrationResult(orchestrationId) {
    try {
      const files = await fs.readdir(this.resultsDir);
      const resultFile = files.find(file =>
        file.startsWith(`orchestration-${orchestrationId}`) && file.endsWith('.json')
      );

      if (!resultFile) {
        throw new Error(`Orchestration result not found: ${orchestrationId}`);
      }

      const filepath = path.join(this.resultsDir, resultFile);
      const content = await fs.readFile(filepath, 'utf8');
      return JSON.parse(content);
    } catch (error) {
      logger.error(`Failed to get orchestration result ${orchestrationId}`, { error: error.message });
      throw error;
    }
  }

  async getSystemStats() {
    try {
      const threadStats = await this.threadRetriever.getThreadStats();
      const history = await this.getOrchestrationHistory(50);

      return {
        threads: threadStats,
        orchestrations: {
          total: history.length,
          recent: history.slice(0, 10),
          averageDuration: history.length > 0
            ? history.reduce((sum, h) => sum + h.totalDuration, 0) / history.length
            : 0
        },
        system: {
          uptime: process.uptime(),
          memoryUsage: process.memoryUsage(),
          nodeVersion: process.version
        }
      };
    } catch (error) {
      logger.error('Failed to get system stats', { error: error.message });
      throw error;
    }
  }
}

export default ThreadMergingOrchestrator;

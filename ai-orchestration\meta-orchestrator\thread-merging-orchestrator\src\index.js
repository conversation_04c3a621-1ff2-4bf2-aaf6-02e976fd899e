#!/usr/bin/env node

import { program } from 'commander';
import { config, validateConfig } from './config/index.js';
import ThreadMergingOrchestrator from './orchestrator.js';
import { logger } from './utils/logger.js';

// Validate configuration on startup
try {
  validateConfig();
  logger.info('Configuration validated successfully');
} catch (error) {
  logger.error('Configuration validation failed', { error: error.message });
  process.exit(1);
}

const orchestrator = new ThreadMergingOrchestrator();

// CLI Commands
program
  .name('thread-merging-orchestrator')
  .description('Automated Thread-Merging Orchestration System')
  .version('1.0.0');

program
  .command('orchestrate')
  .description('Run the full orchestration process')
  .argument('<query>', 'Search query for finding relevant threads')
  .option('-t, --target-llm <llm>', 'Target LLM for analysis (claude|gemini|openai|mistral|cohere|huggingface|groq)', 'claude')
  .option('-k, --task <task>', 'Analysis task (code_generation|code_analysis|summarization)', 'code_generation')
  .option('-m, --max-threads <number>', 'Maximum number of threads to process', '10')
  .option('-f, --format <format>', 'Output format (markdown|json)', 'markdown')
  .option('--use-cache', 'Use cached threads if available')
  .option('--force-refresh', 'Force refresh of thread cache')
  .option('--semantic-weight <number>', 'Weight for semantic search', '0.5')
  .option('--keyword-weight <number>', 'Weight for keyword search', '0.3')
  .option('--content-weight <number>', 'Weight for content search', '0.2')
  .action(async (query, options) => {
    try {
      logger.info('Starting orchestration via CLI', { query, options });

      const result = await orchestrator.orchestrate(query, {
        targetLLM: options.targetLlm,
        task: options.task,
        maxThreads: parseInt(options.maxThreads),
        format: options.format,
        useCache: options.useCache,
        forceRefresh: options.forceRefresh,
        semanticWeight: parseFloat(options.semanticWeight),
        keywordWeight: parseFloat(options.keywordWeight),
        contentWeight: parseFloat(options.contentWeight)
      });

      console.log('\n=== ORCHESTRATION COMPLETED ===');
      console.log(`ID: ${result.id}`);
      console.log(`Duration: ${result.totalDuration}ms`);
      console.log(`Threads Processed: ${result.steps.retrieval.threadsFound}`);
      console.log(`Relevant Threads: ${result.steps.search.relevantThreads}`);
      console.log(`Target LLM: ${result.steps.analysis.targetLLM}`);
      console.log(`Task: ${result.steps.analysis.task}`);

      console.log('\n=== ANALYSIS RESULT ===');
      console.log(result.analysis.analysis);

    } catch (error) {
      logger.error('CLI orchestration failed', { error: error.message });
      console.error('Error:', error.message);
      process.exit(1);
    }
  });

program
  .command('search')
  .description('Search threads without full orchestration')
  .argument('<query>', 'Search query')
  .option('-l, --limit <number>', 'Maximum number of results', '10')
  .option('--use-cache', 'Use cached threads if available')
  .action(async (query, options) => {
    try {
      logger.info('Starting thread search via CLI', { query, options });

      // Get threads
      let threads;
      if (options.useCache) {
        threads = await orchestrator.threadRetriever.loadCachedThreads();
        if (threads.length === 0) {
          console.log('No cached threads found, retrieving fresh threads...');
          threads = await orchestrator.threadRetriever.retrieveAllThreads();
        }
      } else {
        threads = await orchestrator.threadRetriever.retrieveAllThreads();
      }

      // Search
      const searchResults = await orchestrator.searchEngine.searchThreads(threads, query, {
        limit: parseInt(options.limit)
      });

      console.log('\n=== SEARCH RESULTS ===');
      console.log(`Found ${searchResults.length} relevant threads:`);

      searchResults.forEach((result, index) => {
        console.log(`\n${index + 1}. ${result.thread.title || 'Untitled'}`);
        console.log(`   Source: ${result.thread.source}`);
        console.log(`   Score: ${result.combinedScore.toFixed(3)}`);
        console.log(`   Methods: ${result.methods.join(', ')}`);
        console.log(`   Created: ${result.thread.created_at}`);

        if (result.thread.highlights && result.thread.highlights.length > 0) {
          console.log(`   Highlights:`);
          result.thread.highlights.slice(0, 2).forEach(highlight => {
            console.log(`     - ${highlight.text.substring(0, 100)}...`);
          });
        }
      });

    } catch (error) {
      logger.error('CLI search failed', { error: error.message });
      console.error('Error:', error.message);
      process.exit(1);
    }
  });

program
  .command('retrieve')
  .description('Retrieve threads from all sources')
  .option('-l, --limit <number>', 'Maximum number of threads per source', '20')
  .option('--force-refresh', 'Force refresh even if cache exists')
  .action(async (options) => {
    try {
      logger.info('Starting thread retrieval via CLI', { options });

      const threads = await orchestrator.threadRetriever.retrieveAllThreads({
        limit: parseInt(options.limit),
        forceRefresh: options.forceRefresh
      });

      console.log('\n=== RETRIEVAL COMPLETED ===');
      console.log(`Total threads retrieved: ${threads.length}`);

      const bySource = threads.reduce((acc, thread) => {
        acc[thread.source] = (acc[thread.source] || 0) + 1;
        return acc;
      }, {});

      console.log('By source:');
      Object.entries(bySource).forEach(([source, count]) => {
        console.log(`  ${source}: ${count} threads`);
      });

    } catch (error) {
      logger.error('CLI retrieval failed', { error: error.message });
      console.error('Error:', error.message);
      process.exit(1);
    }
  });

program
  .command('history')
  .description('Show orchestration history')
  .option('-l, --limit <number>', 'Number of recent orchestrations to show', '10')
  .action(async (options) => {
    try {
      const history = await orchestrator.getOrchestrationHistory(parseInt(options.limit));

      console.log('\n=== ORCHESTRATION HISTORY ===');

      if (history.length === 0) {
        console.log('No orchestration history found.');
        return;
      }

      history.forEach((item, index) => {
        console.log(`\n${index + 1}. ${item.id}`);
        console.log(`   Query: ${item.query.substring(0, 80)}...`);
        console.log(`   Time: ${item.startTime}`);
        console.log(`   Duration: ${item.totalDuration}ms`);
        console.log(`   Threads: ${item.threadsProcessed} → ${item.relevantThreads} relevant`);
        console.log(`   LLM: ${item.targetLLM} (${item.task})`);
      });

    } catch (error) {
      logger.error('CLI history failed', { error: error.message });
      console.error('Error:', error.message);
      process.exit(1);
    }
  });

program
  .command('stats')
  .description('Show system statistics')
  .action(async () => {
    try {
      const stats = await orchestrator.getSystemStats();

      console.log('\n=== SYSTEM STATISTICS ===');
      console.log(`\nThreads:`);
      console.log(`  Total: ${stats.threads.total}`);
      console.log(`  By source: ${JSON.stringify(stats.threads.bySources, null, 2)}`);
      console.log(`  Total messages: ${stats.threads.totalMessages}`);

      console.log(`\nOrchestrations:`);
      console.log(`  Total: ${stats.orchestrations.total}`);
      console.log(`  Average duration: ${Math.round(stats.orchestrations.averageDuration)}ms`);

      console.log(`\nSystem:`);
      console.log(`  Uptime: ${Math.round(stats.system.uptime)}s`);
      console.log(`  Memory: ${Math.round(stats.system.memoryUsage.heapUsed / 1024 / 1024)}MB`);
      console.log(`  Node version: ${stats.system.nodeVersion}`);

    } catch (error) {
      logger.error('CLI stats failed', { error: error.message });
      console.error('Error:', error.message);
      process.exit(1);
    }
  });

program
  .command('platforms')
  .description('List available AI platforms and their status')
  .option('--test', 'Test connections to all platforms')
  .action(async (options) => {
    try {
      console.log('\n=== AVAILABLE AI PLATFORMS ===');

      const clientInfo = orchestrator.universalClient.getClientInfo();

      clientInfo.forEach(info => {
        console.log(`\n📱 ${info.platform.toUpperCase()}`);
        console.log(`   Model: ${info.model}`);
        console.log(`   API Key: ${info.hasApiKey ? '✅ Configured' : '❌ Missing'}`);
        console.log(`   Features:`);
        console.log(`     - Completion: ${info.features.completion ? '✅' : '❌'}`);
        console.log(`     - Analysis: ${info.features.analysis ? '✅' : '❌'}`);
        console.log(`     - Conversations: ${info.features.conversations ? '✅' : '❌'}`);
        console.log(`     - Embeddings: ${info.features.embeddings ? '✅' : '❌'}`);
      });

      if (options.test) {
        console.log('\n=== CONNECTION TESTS ===');
        const testResults = await orchestrator.universalClient.testAllConnections();

        testResults.forEach(result => {
          const status = result.status === 'success' ? '✅' : '❌';
          console.log(`${status} ${result.platform}: ${result.status === 'success' ? 'Connected' : result.error}`);
        });
      }

    } catch (error) {
      logger.error('CLI platforms failed', { error: error.message });
      console.error('Error:', error.message);
      process.exit(1);
    }
  });

program
  .command('web')
  .description('Start the web interface')
  .option('-p, --port <number>', 'Port for web server', config.web.port.toString())
  .action(async (options) => {
    try {
      const { default: WebServer } = await import('./web/server.js');
      const webServer = new WebServer(orchestrator);
      await webServer.start(parseInt(options.port));
    } catch (error) {
      logger.error('Failed to start web server', { error: error.message });
      console.error('Error:', error.message);
      process.exit(1);
    }
  });

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught exception', { error: error.message, stack: error.stack });
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled rejection', { reason, promise });
  process.exit(1);
});

// Parse command line arguments
program.parse();

// If no command provided, show help
if (!process.argv.slice(2).length) {
  program.outputHelp();
}

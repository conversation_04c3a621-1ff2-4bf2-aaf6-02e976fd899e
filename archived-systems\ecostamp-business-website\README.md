# EcoStamp Business Website

A comprehensive, production-ready SaaS platform for EcoStamp's future paid tiers. Built with Remix, TypeScript, and Tailwind CSS for maximum performance and user experience.

## 🚀 Features

### Core Platform Features
- **Multi-tier Subscription System** - Free, Pro, Pro Plus, Team, and Team Plus plans
- **Content Verification** - Blockchain-powered digital trust and provenance
- **Team Collaboration** - Advanced team management and sharing features
- **Analytics Dashboard** - Comprehensive insights and performance tracking
- **API Integration** - RESTful API with webhooks and SDKs
- **Enterprise Security** - Bank-grade security with SOC 2 compliance

### User Experience
- **Responsive Design** - Mobile-first, works on all devices
- **Modern UI/UX** - Clean, professional design inspired by top SaaS platforms
- **Real-time Updates** - Live notifications and status updates
- **Intuitive Workflows** - Streamlined upload, verification, and management
- **Accessibility** - WCAG 2.1 AA compliant

### Technical Features
- **Performance Optimized** - Fast loading, optimized assets
- **SEO Ready** - Meta tags, structured data, sitemap
- **Analytics Integration** - PostHog for product analytics
- **Error Monitoring** - Comprehensive error tracking and logging
- **Type Safety** - Full TypeScript implementation

## 🛠 Tech Stack

- **Framework**: [Remix](https://remix.run/) - Full-stack React framework
- **Styling**: [Tailwind CSS](https://tailwindcss.com/) - Utility-first CSS framework
- **Database**: [Supabase](https://supabase.com/) - PostgreSQL with real-time features
- **Authentication**: Supabase Auth - Secure user management
- **Payments**: [Stripe](https://stripe.com/) - Subscription and one-time payments
- **Storage**: Supabase Storage - File uploads and management
- **Analytics**: [PostHog](https://posthog.com/) - Product analytics and insights
- **Deployment**: [Vercel](https://vercel.com/) - Edge deployment platform

## 📋 Prerequisites

- Node.js 18+ 
- npm or yarn
- Supabase account
- Stripe account
- PostHog account (optional)

## 🚀 Quick Start

1. **Clone and Install**
   ```bash
   git clone <repository-url>
   cd ecostamp-business-website
   npm install
   ```

2. **Environment Setup**
   ```bash
   cp .env.example .env
   # Fill in your environment variables
   ```

3. **Database Setup**
   ```bash
   npm run db:generate
   npm run db:migrate
   ```

4. **Development Server**
   ```bash
   npm run dev
   ```

5. **Open Browser**
   Navigate to `http://localhost:3000`

## 🔧 Configuration

### Environment Variables

Create a `.env` file with the following variables:

```env
# Supabase
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Stripe
STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret

# PostHog (Optional)
POSTHOG_KEY=your_posthog_key
POSTHOG_HOST=https://app.posthog.com

# App
SESSION_SECRET=your_session_secret
APP_URL=http://localhost:3000
```

### Database Schema

The application uses the following main tables:

- `profiles` - User profiles and plan information
- `uploads` - Content uploads and metadata
- `verifications` - Verification status and results
- `teams` - Team management (Team plans)
- `subscriptions` - Stripe subscription data

## 📊 Tier Features

| Feature | Free | Pro | Pro Plus | Team | Team Plus |
|---------|------|-----|----------|------|-----------|
| Uploads/month | 5 | Unlimited | Unlimited | Unlimited | Unlimited |
| Log retention | 30 days | 90 days | 90 days | 90 days | 90+ days |
| Analytics | Add-on | ✓ | ✓ | ✓ | ✓ |
| API Access | Add-on | ✓ | ✓ | ✓ | ✓ |
| Team members | - | - | - | 5 | Unlimited |
| Support | Community | Email | Daily | Email | Daily |

## 🎨 Design System

### Colors
- **Primary**: Green (#22c55e) - Trust, verification, eco-friendly
- **Secondary**: Slate (#64748b) - Professional, modern
- **Accent**: Yellow (#eab308) - Highlights, CTAs

### Typography
- **Primary**: Inter - Clean, modern sans-serif
- **Monospace**: JetBrains Mono - Code, technical content

### Components
- Consistent spacing (4px grid)
- Rounded corners (8px, 12px, 16px)
- Subtle shadows and gradients
- Smooth animations and transitions

## 🔐 Security

- **Authentication**: Supabase Auth with JWT tokens
- **Authorization**: Row-level security (RLS) policies
- **Data Protection**: Encrypted at rest and in transit
- **CSRF Protection**: Built-in Remix CSRF protection
- **Rate Limiting**: API rate limiting by plan tier
- **Input Validation**: Zod schema validation

## 📈 Analytics & Monitoring

### PostHog Integration
- User behavior tracking
- Feature usage analytics
- Conversion funnel analysis
- A/B testing capabilities

### Performance Monitoring
- Core Web Vitals tracking
- Error boundary monitoring
- API response time tracking
- User experience metrics

## 🚀 Deployment

### Vercel Deployment

1. **Connect Repository**
   ```bash
   vercel --prod
   ```

2. **Environment Variables**
   Set all environment variables in Vercel dashboard

3. **Domain Setup**
   Configure custom domain in Vercel settings

### Alternative Deployments
- **Netlify**: Full support with adapter
- **Railway**: Database and app hosting
- **Fly.io**: Global edge deployment

## 🧪 Testing

```bash
# Unit tests
npm run test

# E2E tests
npm run test:e2e

# Type checking
npm run typecheck

# Linting
npm run lint
```

## 📚 Documentation

### API Documentation
- RESTful API endpoints
- Webhook documentation
- SDK examples and guides
- Rate limiting information

### User Guides
- Getting started guide
- Feature documentation
- Team management guide
- Integration tutorials

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details

## 🆘 Support

- **Documentation**: [docs.ecostamp.com](https://docs.ecostamp.com)
- **Community**: [Discord](https://discord.gg/ecostamp)
- **Email**: <EMAIL>
- **Status**: [status.ecostamp.com](https://status.ecostamp.com)

## 🗺 Roadmap

### Phase 1 (Current)
- ✅ Core platform features
- ✅ Subscription system
- ✅ Basic analytics
- ✅ Team management

### Phase 2 (Next)
- [ ] Advanced analytics
- [ ] White-label options
- [ ] Mobile app
- [ ] Enterprise features

### Phase 3 (Future)
- [ ] AI-powered insights
- [ ] Blockchain integration
- [ ] Global CDN
- [ ] Advanced security features

---

**Built with ❤️ for the EcoStamp community**

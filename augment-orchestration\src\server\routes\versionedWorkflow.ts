/**
 * Versioned Workflow Management API Routes
 * 
 * RESTful API endpoints for comprehensive workflow tracking system
 */

import { Router } from 'express';
import { body, query, param, validationResult } from 'express-validator';
import { PrismaClient } from '@prisma/client';
import { VersionedWorkflowService } from '../services/VersionedWorkflowService';
import { asyncHandler } from '../utils/asyncHandler';
import { authenticate, authorize } from '../middleware/auth';
import { logger } from '../utils/logger';
import {
  WorkflowStatus,
  WorkflowType,
  TaskPriority,
  ExperimentType
} from '../../shared/types/VersionedWorkflow';

const router = Router();
const prisma = new PrismaClient();
const workflowService = new VersionedWorkflowService(prisma);

// Validation middleware
const validateWorkflowCreation = [
  body('name').notEmpty().withMessage('Workflow name is required'),
  body('description').optional().isString(),
  body('type').isIn(Object.values(WorkflowType)).withMessage('Invalid workflow type'),
  body('metadata.projectId').notEmpty().withMessage('Project ID is required'),
  body('metadata.environment').isIn(['development', 'staging', 'production']).withMessage('Invalid environment'),
  body('configuration.maxConcurrentTasks').optional().isInt({ min: 1, max: 100 }),
  body('configuration.timeoutMs').optional().isInt({ min: 1000 })
];

const validateTaskCreation = [
  body('name').notEmpty().withMessage('Task name is required'),
  body('description').optional().isString(),
  body('type').notEmpty().withMessage('Task type is required'),
  body('priority').optional().isIn(Object.values(TaskPriority)),
  body('estimatedDuration').optional().isInt({ min: 1000 }),
  body('dependencies').optional().isArray(),
  body('assignedAgentId').optional().isString()
];

const validateExperimentCreation = [
  body('name').notEmpty().withMessage('Experiment name is required'),
  body('description').optional().isString(),
  body('type').isIn(Object.values(ExperimentType)).withMessage('Invalid experiment type'),
  body('configuration.trafficSplit').isObject().withMessage('Traffic split configuration is required'),
  body('configuration.duration').isInt({ min: 60000 }).withMessage('Duration must be at least 1 minute'),
  body('variants').isArray({ min: 2 }).withMessage('At least 2 variants are required')
];

/**
 * POST /api/versioned-workflow/workflows
 * Create a new versioned workflow
 */
router.post('/workflows',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR']),
  validateWorkflowCreation,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const workflowData = {
      ...req.body,
      createdBy: req.user?.id
    };

    const workflow = await workflowService.createWorkflow(workflowData);

    logger.info('Workflow created via API', {
      workflowId: workflow.id,
      name: workflow.name,
      type: workflow.type,
      userId: req.user?.id
    });

    res.status(201).json({
      success: true,
      workflow: {
        id: workflow.id,
        name: workflow.name,
        description: workflow.description,
        type: workflow.type,
        version: workflow.version,
        status: workflow.status,
        createdAt: workflow.createdAt,
        createdBy: workflow.createdBy,
        metadata: workflow.metadata,
        configuration: workflow.configuration,
        tasksCount: workflow.tasks.length,
        experimentsCount: workflow.experiments.length
      }
    });
  })
);

/**
 * GET /api/versioned-workflow/workflows
 * Get all workflows with optional filtering
 */
router.get('/workflows',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR', 'AGENT']),
  query('status').optional().isIn(Object.values(WorkflowStatus)),
  query('type').optional().isIn(Object.values(WorkflowType)),
  query('ownerId').optional().isString(),
  query('projectId').optional().isString(),
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    let workflows = workflowService.getAllWorkflows();

    // Apply filters
    if (req.query.status) {
      workflows = workflows.filter(w => w.status === req.query.status);
    }
    if (req.query.type) {
      workflows = workflows.filter(w => w.type === req.query.type);
    }
    if (req.query.ownerId) {
      workflows = workflows.filter(w => w.ownerId === req.query.ownerId);
    }
    if (req.query.projectId) {
      workflows = workflows.filter(w => w.metadata.projectId === req.query.projectId);
    }

    res.json({
      success: true,
      workflows: workflows.map(workflow => ({
        id: workflow.id,
        name: workflow.name,
        description: workflow.description,
        type: workflow.type,
        version: workflow.version,
        status: workflow.status,
        createdAt: workflow.createdAt,
        updatedAt: workflow.updatedAt,
        createdBy: workflow.createdBy,
        ownerId: workflow.ownerId,
        metadata: {
          projectId: workflow.metadata.projectId,
          environment: workflow.metadata.environment,
          complexity: workflow.metadata.complexity,
          businessValue: workflow.metadata.businessValue,
          riskLevel: workflow.metadata.riskLevel
        },
        metrics: {
          executionTime: workflow.metrics.executionTime,
          totalTasks: workflow.metrics.totalTasks,
          completedTasks: workflow.metrics.completedTasks,
          successRate: workflow.metrics.successRate
        },
        tasksCount: workflow.tasks.length,
        experimentsCount: workflow.experiments.length
      })),
      count: workflows.length
    });
  })
);

/**
 * GET /api/versioned-workflow/workflows/:workflowId
 * Get specific workflow details
 */
router.get('/workflows/:workflowId',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR', 'AGENT']),
  param('workflowId').notEmpty().withMessage('Workflow ID is required'),
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { workflowId } = req.params;
    const workflow = workflowService.getWorkflow(workflowId);

    if (!workflow) {
      return res.status(404).json({
        success: false,
        error: 'Workflow not found'
      });
    }

    res.json({
      success: true,
      workflow
    });
  })
);

/**
 * POST /api/versioned-workflow/workflows/:workflowId/start
 * Start workflow execution
 */
router.post('/workflows/:workflowId/start',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR']),
  param('workflowId').notEmpty().withMessage('Workflow ID is required'),
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { workflowId } = req.params;

    await workflowService.startWorkflow(workflowId);

    logger.info('Workflow started via API', {
      workflowId,
      userId: req.user?.id
    });

    res.json({
      success: true,
      message: 'Workflow started successfully',
      workflowId
    });
  })
);

/**
 * POST /api/versioned-workflow/workflows/:workflowId/pause
 * Pause workflow execution
 */
router.post('/workflows/:workflowId/pause',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR']),
  param('workflowId').notEmpty().withMessage('Workflow ID is required'),
  body('reason').optional().isString(),
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { workflowId } = req.params;
    const { reason } = req.body;

    await workflowService.pauseWorkflow(workflowId, reason);

    logger.info('Workflow paused via API', {
      workflowId,
      reason,
      userId: req.user?.id
    });

    res.json({
      success: true,
      message: 'Workflow paused successfully',
      workflowId,
      reason
    });
  })
);

/**
 * POST /api/versioned-workflow/workflows/:workflowId/resume
 * Resume workflow execution
 */
router.post('/workflows/:workflowId/resume',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR']),
  param('workflowId').notEmpty().withMessage('Workflow ID is required'),
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { workflowId } = req.params;

    await workflowService.resumeWorkflow(workflowId);

    logger.info('Workflow resumed via API', {
      workflowId,
      userId: req.user?.id
    });

    res.json({
      success: true,
      message: 'Workflow resumed successfully',
      workflowId
    });
  })
);

/**
 * POST /api/versioned-workflow/workflows/:workflowId/cancel
 * Cancel workflow execution
 */
router.post('/workflows/:workflowId/cancel',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR']),
  param('workflowId').notEmpty().withMessage('Workflow ID is required'),
  body('reason').optional().isString(),
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { workflowId } = req.params;
    const { reason } = req.body;

    await workflowService.cancelWorkflow(workflowId, reason);

    logger.info('Workflow cancelled via API', {
      workflowId,
      reason,
      userId: req.user?.id
    });

    res.json({
      success: true,
      message: 'Workflow cancelled successfully',
      workflowId,
      reason
    });
  })
);

/**
 * POST /api/versioned-workflow/workflows/:workflowId/tasks
 * Add task to workflow
 */
router.post('/workflows/:workflowId/tasks',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR']),
  param('workflowId').notEmpty().withMessage('Workflow ID is required'),
  validateTaskCreation,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { workflowId } = req.params;
    const taskData = req.body;

    const task = await workflowService.addTask(workflowId, taskData);

    logger.info('Task added to workflow via API', {
      workflowId,
      taskId: task.id,
      taskName: task.name,
      userId: req.user?.id
    });

    res.status(201).json({
      success: true,
      task: {
        id: task.id,
        name: task.name,
        description: task.description,
        type: task.type,
        status: task.status,
        priority: task.priority,
        estimatedDuration: task.estimatedDuration,
        dependencies: task.dependencies,
        assignedAgentId: task.assignedAgentId
      }
    });
  })
);

/**
 * POST /api/versioned-workflow/workflows/:workflowId/experiments
 * Create experiment for A/B testing
 */
router.post('/workflows/:workflowId/experiments',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR']),
  param('workflowId').notEmpty().withMessage('Workflow ID is required'),
  validateExperimentCreation,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { workflowId } = req.params;
    const experimentData = req.body;

    const experiment = await workflowService.createExperiment(workflowId, experimentData);

    logger.info('Experiment created via API', {
      workflowId,
      experimentId: experiment.id,
      experimentName: experiment.name,
      type: experiment.type,
      userId: req.user?.id
    });

    res.status(201).json({
      success: true,
      experiment: {
        id: experiment.id,
        name: experiment.name,
        description: experiment.description,
        type: experiment.type,
        status: experiment.status,
        configuration: experiment.configuration,
        variants: experiment.variants,
        metrics: experiment.metrics
      }
    });
  })
);

/**
 * POST /api/versioned-workflow/workflows/:workflowId/experiments/:experimentId/start
 * Start experiment
 */
router.post('/workflows/:workflowId/experiments/:experimentId/start',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR']),
  param('workflowId').notEmpty().withMessage('Workflow ID is required'),
  param('experimentId').notEmpty().withMessage('Experiment ID is required'),
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { workflowId, experimentId } = req.params;

    await workflowService.startExperiment(workflowId, experimentId);

    logger.info('Experiment started via API', {
      workflowId,
      experimentId,
      userId: req.user?.id
    });

    res.json({
      success: true,
      message: 'Experiment started successfully',
      workflowId,
      experimentId
    });
  })
);

/**
 * POST /api/versioned-workflow/workflows/:workflowId/rollback
 * Rollback workflow
 */
router.post('/workflows/:workflowId/rollback',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR']),
  param('workflowId').notEmpty().withMessage('Workflow ID is required'),
  body('reason').optional().isString(),
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { workflowId } = req.params;
    const { reason } = req.body;

    await workflowService.rollbackWorkflow(workflowId, reason);

    logger.info('Workflow rolled back via API', {
      workflowId,
      reason,
      userId: req.user?.id
    });

    res.json({
      success: true,
      message: 'Workflow rolled back successfully',
      workflowId,
      reason
    });
  })
);

/**
 * GET /api/versioned-workflow/analytics
 * Get workflow analytics
 */
router.get('/analytics',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR']),
  query('startDate').optional().isISO8601(),
  query('endDate').optional().isISO8601(),
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const timeRange = req.query.startDate && req.query.endDate ? {
      startDate: new Date(req.query.startDate as string),
      endDate: new Date(req.query.endDate as string)
    } : undefined;

    const analytics = await workflowService.getWorkflowAnalytics(timeRange);

    res.json({
      success: true,
      analytics
    });
  })
);

/**
 * GET /api/versioned-workflow/health
 * Get workflow system health
 */
router.get('/health',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR']),
  asyncHandler(async (req, res) => {
    const workflows = workflowService.getAllWorkflows();
    const analytics = await workflowService.getWorkflowAnalytics();

    const health = {
      status: 'healthy',
      timestamp: new Date(),
      metrics: {
        totalWorkflows: workflows.length,
        activeWorkflows: workflows.filter(w => w.status === WorkflowStatus.ACTIVE).length,
        failedWorkflows: workflows.filter(w => w.status === WorkflowStatus.FAILED).length,
        averageExecutionTime: analytics.averageExecutionTime,
        successRate: analytics.successRate,
        resourceEfficiency: analytics.resourceEfficiency
      },
      performance: {
        workflowThroughput: 25, // Mock value
        averageTaskDuration: 120000, // Mock value
        systemLoad: 0.4 // Mock value
      },
      issues: [] as string[],
      warnings: [] as string[]
    };

    // Add health warnings
    if (health.metrics.failedWorkflows > 5) {
      health.warnings.push('High number of failed workflows detected');
    }

    if (health.metrics.averageExecutionTime > 3600000) { // 1 hour
      health.warnings.push('Average execution time is above threshold');
    }

    if (health.metrics.successRate < 80) {
      health.issues.push('Workflow success rate is below acceptable level');
    }

    res.json({
      success: true,
      health
    });
  })
);

export default router;

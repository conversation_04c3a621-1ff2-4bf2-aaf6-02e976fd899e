"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.store = void 0;
const toolkit_1 = require("@reduxjs/toolkit");
const authSlice_1 = __importDefault(require("./slices/authSlice"));
const socketSlice_1 = __importDefault(require("./slices/socketSlice"));
const orchestratorSlice_1 = __importDefault(require("./slices/orchestratorSlice"));
const agentSlice_1 = __importDefault(require("./slices/agentSlice"));
const workflowSlice_1 = __importDefault(require("./slices/workflowSlice"));
const tunnelSlice_1 = __importDefault(require("./slices/tunnelSlice"));
const evolutionSlice_1 = __importDefault(require("./slices/evolutionSlice"));
exports.store = (0, toolkit_1.configureStore)({
    reducer: {
        auth: authSlice_1.default,
        socket: socketSlice_1.default,
        orchestrator: orchestratorSlice_1.default,
        agent: agentSlice_1.default,
        workflow: workflowSlice_1.default,
        tunnel: tunnelSlice_1.default,
        evolution: evolutionSlice_1.default,
    },
    middleware: (getDefaultMiddleware) => getDefaultMiddleware({
        serializableCheck: {
            ignoredActions: ['socket/setSocket'],
            ignoredPaths: ['socket.socket'],
        },
    }),
});

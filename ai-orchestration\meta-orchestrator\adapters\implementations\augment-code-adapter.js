/**
 * Augment Code Adapter
 * 
 * Adapter for Augment Code - Primary codebase analyzer and context engine
 * Specializes in: analysis, context understanding, dependency mapping, validation
 */

const BaseAdapter = require('../base-adapter');
const axios = require('axios');
const chalk = require('chalk');

class AugmentCodeAdapter extends BaseAdapter {
  constructor(config, metaOrchestrator) {
    super(config, metaOrchestrator);
    
    // Set adapter-specific properties
    this.adapterId = 'augment-code';
    
    // Define capabilities
    this.addCapability('codebase-analysis');
    this.addCapability('context-understanding');
    this.addCapability('dependency-mapping');
    this.addCapability('architecture-review');
    this.addCapability('security-scanning');
    this.addCapability('performance-analysis');
    this.addCapability('code-quality-assessment');
    this.addCapability('search');
    this.addCapability('validation');
    
    // Define supported roles
    this.addSupportedRole('analyzer');
    this.addSupportedRole('validator');
    
    // Augment Code specific configuration
    this.apiEndpoint = this.getConfig('apiEndpoint', 'http://localhost:8080');
    this.apiKey = this.getConfig('apiKey', process.env.AUGMENT_API_KEY);
    
    // API client
    this.client = null;
  }
  
  async initialize() {
    try {
      this.log('info', 'Initializing Augment Code adapter...');
      
      // Setup API client
      this.client = axios.create({
        baseURL: this.apiEndpoint,
        timeout: this.getConfig('timeout', 30000),
        headers: {
          'Content-Type': 'application/json',
          ...(this.apiKey && { 'Authorization': `Bearer ${this.apiKey}` })
        }
      });
      
      // Test connection
      await this.testConnection();
      
      this.state.initialized = true;
      this.log('info', 'Augment Code adapter initialized successfully');
      
    } catch (error) {
      this.log('error', 'Failed to initialize Augment Code adapter', { error: error.message });
      throw error;
    }
  }
  
  async testConnection() {
    try {
      // Try to ping the Augment Code API
      const response = await this.client.get('/health');
      
      if (response.status === 200) {
        this.log('info', 'Augment Code API connection successful');
        return true;
      } else {
        throw new Error(`Unexpected response status: ${response.status}`);
      }
      
    } catch (error) {
      if (error.code === 'ECONNREFUSED') {
        this.log('warn', 'Augment Code API not available - using fallback mode');
        return false; // Allow fallback mode
      }
      throw error;
    }
  }
  
  async checkAvailability() {
    try {
      if (!this.client) {
        return false;
      }
      
      const response = await this.client.get('/health', { timeout: 5000 });
      return response.status === 200;
      
    } catch (error) {
      this.log('debug', 'Availability check failed', { error: error.message });
      return false;
    }
  }
  
  async execute(task, context) {
    this.validateTask(task);
    this.validateContext(context);
    
    const { role } = task;
    
    switch (role) {
      case 'analyzer':
        return await this.performAnalysis(task, context);
      case 'validator':
        return await this.performValidation(task, context);
      default:
        throw new Error(`Unsupported role for Augment Code: ${role}`);
    }
  }
  
  async performAnalysis(task, context) {
    try {
      this.log('info', 'Performing codebase analysis', { task: task.type });
      
      const analysisType = task.analysisType || 'comprehensive';
      const projectPath = context.projectPath || process.cwd();
      
      let result;
      
      switch (analysisType) {
        case 'comprehensive':
          result = await this.comprehensiveAnalysis(projectPath, context);
          break;
        case 'dependency':
          result = await this.dependencyAnalysis(projectPath, context);
          break;
        case 'security':
          result = await this.securityAnalysis(projectPath, context);
          break;
        case 'performance':
          result = await this.performanceAnalysis(projectPath, context);
          break;
        case 'architecture':
          result = await this.architectureAnalysis(projectPath, context);
          break;
        default:
          result = await this.comprehensiveAnalysis(projectPath, context);
      }
      
      return {
        type: 'analysis',
        analysisType,
        result,
        metadata: {
          projectPath,
          timestamp: Date.now(),
          adapterId: this.adapterId
        }
      };
      
    } catch (error) {
      this.log('error', 'Analysis failed', { error: error.message });
      throw error;
    }
  }
  
  async comprehensiveAnalysis(projectPath, context) {
    try {
      // Use Augment's codebase retrieval for comprehensive analysis
      const analysisRequest = {
        information_request: `Perform comprehensive analysis of the codebase at ${projectPath}. Include:
        - Project structure and architecture
        - Dependencies and their relationships
        - Code quality metrics
        - Security considerations
        - Performance implications
        - Potential improvements
        - Integration points
        ${context.specificRequirements ? `- ${context.specificRequirements}` : ''}`
      };
      
      // If we have direct access to Augment's context engine, use it
      if (this.metaOrchestrator && this.metaOrchestrator.contextManager) {
        const contextData = await this.metaOrchestrator.contextManager.getProjectContext(projectPath);
        
        return {
          projectStructure: contextData.structure,
          dependencies: contextData.dependencies,
          codeMetrics: contextData.metrics,
          securityIssues: contextData.security,
          performanceIssues: contextData.performance,
          recommendations: contextData.recommendations,
          integrationPoints: contextData.integrations
        };
      }
      
      // Fallback to API call
      const response = await this.client.post('/api/analyze', {
        projectPath,
        analysisType: 'comprehensive',
        includeMetrics: true,
        includeSecurity: true,
        includePerformance: true,
        context
      });
      
      return response.data;
      
    } catch (error) {
      this.log('error', 'Comprehensive analysis failed', { error: error.message });
      throw error;
    }
  }
  
  async dependencyAnalysis(projectPath, context) {
    try {
      const response = await this.client.post('/api/dependencies', {
        projectPath,
        includeTransitive: true,
        checkVulnerabilities: true,
        analyzeUsage: true,
        context
      });
      
      return response.data;
      
    } catch (error) {
      this.log('error', 'Dependency analysis failed', { error: error.message });
      throw error;
    }
  }
  
  async securityAnalysis(projectPath, context) {
    try {
      const response = await this.client.post('/api/security', {
        projectPath,
        scanTypes: ['vulnerabilities', 'secrets', 'permissions', 'dependencies'],
        severity: 'all',
        context
      });
      
      return response.data;
      
    } catch (error) {
      this.log('error', 'Security analysis failed', { error: error.message });
      throw error;
    }
  }
  
  async performanceAnalysis(projectPath, context) {
    try {
      const response = await this.client.post('/api/performance', {
        projectPath,
        analysisTypes: ['bottlenecks', 'memory', 'cpu', 'io', 'algorithms'],
        includeRecommendations: true,
        context
      });
      
      return response.data;
      
    } catch (error) {
      this.log('error', 'Performance analysis failed', { error: error.message });
      throw error;
    }
  }
  
  async architectureAnalysis(projectPath, context) {
    try {
      const response = await this.client.post('/api/architecture', {
        projectPath,
        includePatterns: true,
        includeDependencies: true,
        includeLayering: true,
        context
      });
      
      return response.data;
      
    } catch (error) {
      this.log('error', 'Architecture analysis failed', { error: error.message });
      throw error;
    }
  }
  
  async performValidation(task, context) {
    try {
      this.log('info', 'Performing code validation', { task: task.type });
      
      const validationType = task.validationType || 'comprehensive';
      const targetFiles = context.files || context.projectPath;
      
      let result;
      
      switch (validationType) {
        case 'comprehensive':
          result = await this.comprehensiveValidation(targetFiles, context);
          break;
        case 'syntax':
          result = await this.syntaxValidation(targetFiles, context);
          break;
        case 'quality':
          result = await this.qualityValidation(targetFiles, context);
          break;
        case 'security':
          result = await this.securityValidation(targetFiles, context);
          break;
        case 'compliance':
          result = await this.complianceValidation(targetFiles, context);
          break;
        default:
          result = await this.comprehensiveValidation(targetFiles, context);
      }
      
      return {
        type: 'validation',
        validationType,
        result,
        metadata: {
          targetFiles,
          timestamp: Date.now(),
          adapterId: this.adapterId
        }
      };
      
    } catch (error) {
      this.log('error', 'Validation failed', { error: error.message });
      throw error;
    }
  }
  
  async comprehensiveValidation(targetFiles, context) {
    try {
      const response = await this.client.post('/api/validate', {
        targets: targetFiles,
        validationTypes: ['syntax', 'quality', 'security', 'performance', 'compliance'],
        includeRecommendations: true,
        context
      });
      
      return response.data;
      
    } catch (error) {
      this.log('error', 'Comprehensive validation failed', { error: error.message });
      throw error;
    }
  }
  
  async syntaxValidation(targetFiles, context) {
    try {
      const response = await this.client.post('/api/validate/syntax', {
        targets: targetFiles,
        languages: context.languages || 'auto-detect',
        context
      });
      
      return response.data;
      
    } catch (error) {
      this.log('error', 'Syntax validation failed', { error: error.message });
      throw error;
    }
  }
  
  async qualityValidation(targetFiles, context) {
    try {
      const response = await this.client.post('/api/validate/quality', {
        targets: targetFiles,
        metrics: ['complexity', 'maintainability', 'readability', 'testability'],
        thresholds: context.qualityThresholds,
        context
      });
      
      return response.data;
      
    } catch (error) {
      this.log('error', 'Quality validation failed', { error: error.message });
      throw error;
    }
  }
  
  async securityValidation(targetFiles, context) {
    try {
      const response = await this.client.post('/api/validate/security', {
        targets: targetFiles,
        scanTypes: ['vulnerabilities', 'secrets', 'permissions'],
        severity: context.securitySeverity || 'medium',
        context
      });
      
      return response.data;
      
    } catch (error) {
      this.log('error', 'Security validation failed', { error: error.message });
      throw error;
    }
  }
  
  async complianceValidation(targetFiles, context) {
    try {
      const response = await this.client.post('/api/validate/compliance', {
        targets: targetFiles,
        standards: context.complianceStandards || ['pci', 'hipaa', 'gdpr'],
        context
      });
      
      return response.data;
      
    } catch (error) {
      this.log('error', 'Compliance validation failed', { error: error.message });
      throw error;
    }
  }
  
  validateContext(context) {
    super.validateContext(context);
    
    // Augment Code specific context validation
    if (!context.projectPath && !context.files) {
      throw new Error('Either projectPath or files must be provided for Augment Code analysis');
    }
    
    return true;
  }
  
  async shutdown() {
    this.log('info', 'Shutting down Augment Code adapter');
    
    if (this.client) {
      // Cancel any pending requests
      this.client = null;
    }
    
    await super.shutdown();
  }
}

module.exports = AugmentCodeAdapter;

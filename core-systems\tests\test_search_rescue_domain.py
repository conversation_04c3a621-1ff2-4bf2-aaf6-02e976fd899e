"""
Comprehensive Test Suite for Search and Rescue Domain

Tests all components of the Search and Rescue domain including visual detection,
reference matching, mission validation, and side pocket management.
"""

import unittest
import tempfile
import shutil
import json
from datetime import datetime, timedelta
from pathlib import Path

# Import Search and Rescue components
from feedback_loop_framework.domains.search_rescue import (
    SearchRescueDomain, VisualDetectionInterpreter, ReferenceItemMatcher,
    SearchRescueValidator, SidePocketManager
)
from feedback_loop_framework.core.feedback_types import FeedbackType, FeedbackEntry
from feedback_loop_framework.core.feedback_engine import FeedbackEngine


class TestSearchRescueDomain(unittest.TestCase):
    """Test suite for Search and Rescue domain functionality."""
    
    def setUp(self):
        """Set up test environment."""
        # Create temporary directory for test data
        self.test_dir = tempfile.mkdtemp()
        
        # Configure Search and Rescue domain
        self.sar_config = {
            'side_pocket_path': self.test_dir + '/side_pocket',
            'side_pocket_retention': 30,
            'auto_review': True,
            'retraining_threshold': 5
        }
        
        # Create domain
        self.sar_domain = SearchRescueDomain(self.sar_config)
        self.sar_domain.initialize_components()
        
        # Sample mission context
        self.mission_context = {
            'mission_id': 'TEST_SAR_001',
            'mission_type': 'lost_child',
            'target_age': 'child',
            'target_description': {
                'age': 8,
                'clothing': {'shirt': 'red', 'pants': 'blue'},
                'items': ['backpack', 'toy']
            },
            'search_area_km2': 1.0,
            'weather_conditions': 'clear',
            'lighting_conditions': 'daylight',
            'terrain_type': 'forest'
        }
    
    def tearDown(self):
        """Clean up test environment."""
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def test_visual_detection_interpreter(self):
        """Test visual detection interpretation."""
        interpreter = VisualDetectionInterpreter()
        
        # Test clothing detection
        clothing_detection = {
            'detections': [{
                'class': 'fabric_red',
                'confidence': 0.85,
                'bbox': [100, 100, 150, 150],
                'category': 'clothing'
            }]
        }
        
        context = {
            'detection_type': 'image_detection',
            'mission_id': 'TEST_001',
            'latitude': 42.3601,
            'longitude': -71.0589
        }
        
        result = interpreter.interpret(clothing_detection, context)
        
        self.assertFalse(result.get('error', False))
        self.assertIn('classifications', result)
        self.assertEqual(len(result['classifications']), 1)
        
        classification = result['classifications'][0]
        self.assertEqual(classification['sar_category'], 'clothing')
        self.assertEqual(classification['confidence'], 0.85)
        self.assertTrue(classification['meets_threshold'])
    
    def test_target_person_detection(self):
        """Test target person detection and classification."""
        interpreter = VisualDetectionInterpreter()
        
        # Test person detection
        person_detection = {
            'detections': [{
                'class': 'child',
                'confidence': 0.92,
                'bbox': [200, 150, 250, 250],
                'category': 'person'
            }]
        }
        
        context = {
            'detection_type': 'image_detection',
            'mission_id': 'TEST_002'
        }
        
        result = interpreter.interpret(person_detection, context)
        
        self.assertFalse(result.get('error', False))
        classification = result['classifications'][0]
        self.assertEqual(classification['sar_category'], 'target_person')
        self.assertEqual(classification['priority'], 'critical')
        self.assertGreater(classification['confidence'], 0.9)
    
    def test_reference_item_matcher(self):
        """Test reference item matching functionality."""
        matcher = ReferenceItemMatcher()
        
        # Create interpreted output with clothing detection
        interpreted_output = {
            'classifications': [{
                'sar_category': 'clothing',
                'subcategory': 'fabric_scraps',
                'confidence': 0.8,
                'priority': 'high',
                'meets_threshold': True
            }],
            'spatial_data': {
                'gps_coordinates': {'latitude': 42.3601, 'longitude': -71.0589}
            }
        }
        
        context = {
            'mission_parameters': {
                'target_description': {
                    'clothing': {'shirt': 'red'}
                }
            }
        }
        
        match_results = matcher.match(interpreted_output, context)
        
        self.assertGreater(match_results['overall_relevance'], 0.0)
        self.assertIn('pattern_matches', match_results)
        self.assertIn('temporal_analysis', match_results)
    
    def test_mission_validator_target_found(self):
        """Test mission validation for target found scenario."""
        validator = SearchRescueValidator()
        
        # High confidence target detection
        interpreted_output = {
            'classifications': [{
                'sar_category': 'target_person',
                'confidence': 0.92,
                'priority': 'critical'
            }]
        }
        
        match_results = {
            'overall_relevance': 0.9,
            'pattern_matches': [{
                'type': 'target_person_reference_match',
                'confidence': 0.92
            }],
            'warnings': [],
            'metadata': {'match_timestamp': datetime.utcnow().isoformat()}
        }
        
        context = self.mission_context.copy()
        
        result = validator.validate(interpreted_output, match_results, context)
        
        self.assertEqual(result.feedback_type, FeedbackType.CORRECT)
        self.assertTrue(result.is_valid)
        self.assertGreater(result.confidence_score, 0.8)
    
    def test_mission_validator_partial_success(self):
        """Test mission validation for partial success scenario."""
        validator = SearchRescueValidator()
        
        # Reference items found but no target
        interpreted_output = {
            'classifications': [{
                'sar_category': 'clothing',
                'confidence': 0.75,
                'priority': 'high'
            }, {
                'sar_category': 'personal_items',
                'confidence': 0.82,
                'priority': 'high'
            }]
        }
        
        match_results = {
            'overall_relevance': 0.7,
            'pattern_matches': [{
                'type': 'clothing_reference_match',
                'confidence': 0.75
            }],
            'warnings': [],
            'metadata': {'match_timestamp': datetime.utcnow().isoformat()}
        }
        
        context = self.mission_context.copy()
        
        result = validator.validate(interpreted_output, match_results, context)
        
        self.assertEqual(result.feedback_type, FeedbackType.PARTIALLY_CORRECT)
        self.assertTrue(result.is_valid)
        self.assertGreater(result.confidence_score, 0.5)
    
    def test_side_pocket_manager(self):
        """Test side pocket management functionality."""
        side_pocket_manager = SidePocketManager({
            'base_path': self.test_dir + '/side_pocket_test',
            'retention_days': 30,
            'retraining_threshold': 3
        })
        
        # Add uncertain detection to side pocket
        detection_data = {
            'class': 'uncertain_object',
            'confidence': 0.45,
            'bbox': [100, 100, 120, 120]
        }
        
        item_id = side_pocket_manager.add_to_side_pocket(
            detection_data=detection_data,
            reason="Low confidence detection",
            category='uncertain',
            mission_context=self.mission_context
        )
        
        self.assertIsNotNone(item_id)
        self.assertTrue(len(item_id) > 0)
        
        # Get pending reviews
        pending_items = side_pocket_manager.get_pending_reviews(limit=10)
        self.assertEqual(len(pending_items), 1)
        self.assertEqual(pending_items[0]['item_id'], item_id)
        
        # Review the item
        review_result = {
            'final_category': 'false_positive',
            'corrected_classification': {'class': 'environmental_artifact'},
            'use_for_retraining': True
        }
        
        success = side_pocket_manager.review_item(item_id, review_result, 'test_reviewer')
        self.assertTrue(success)
        
        # Check that item is no longer pending
        pending_items = side_pocket_manager.get_pending_reviews(limit=10)
        self.assertEqual(len(pending_items), 0)
    
    def test_domain_integration(self):
        """Test complete domain integration."""
        # Test processing a complete detection scenario
        detection_data = {
            'detections': [{
                'class': 'backpack_small',
                'confidence': 0.88,
                'bbox': [200, 180, 240, 220],
                'category': 'personal_item'
            }]
        }
        
        context = {
            **self.mission_context,
            'detection_type': 'image_detection',
            'latitude': 42.3605,
            'longitude': -71.0592,
            'drone_id': 'TEST_DRONE_01'
        }
        
        result = self.sar_domain.process_output(detection_data, context)
        
        self.assertIsNotNone(result)
        self.assertIn(result.feedback_type, [FeedbackType.CORRECT, FeedbackType.PARTIALLY_CORRECT])
        self.assertGreater(result.confidence_score, 0.0)
        
        # Check domain metadata
        self.assertEqual(result.metadata['domain'], 'search_rescue')
        self.assertEqual(result.metadata['mission_type'], 'lost_child')
        self.assertIn('mission_outcome', result.metadata)
    
    def test_low_confidence_side_pocket(self):
        """Test that low confidence detections go to side pocket."""
        # Process low confidence detection
        detection_data = {
            'detections': [{
                'class': 'uncertain_object',
                'confidence': 0.35,  # Below threshold
                'bbox': [100, 100, 120, 120],
                'category': 'unknown'
            }]
        }
        
        context = {
            **self.mission_context,
            'detection_type': 'image_detection'
        }
        
        # Get initial side pocket count
        initial_stats = self.sar_domain.side_pocket_manager.get_stats()
        initial_count = initial_stats['total_items']
        
        result = self.sar_domain.process_output(detection_data, context)
        
        # Check that item was added to side pocket
        updated_stats = self.sar_domain.side_pocket_manager.get_stats()
        self.assertGreater(updated_stats['total_items'], initial_count)
        
        # Check that processing stats were updated
        self.assertGreater(self.sar_domain.processing_stats['side_pocket_usage'], 0)
    
    def test_mission_outcomes_tracking(self):
        """Test mission outcomes tracking."""
        # Process successful target detection
        target_detection = {
            'detections': [{
                'class': 'child',
                'confidence': 0.95,
                'bbox': [200, 150, 250, 250],
                'category': 'person'
            }]
        }
        
        context = {
            **self.mission_context,
            'detection_type': 'image_detection'
        }
        
        result = self.sar_domain.process_output(target_detection, context)
        
        # Check mission outcomes
        outcomes = self.sar_domain.get_mission_outcomes()
        self.assertGreater(outcomes['total_missions'], 0)
        self.assertGreater(outcomes['success_rate'], 0.0)
        
        if result.feedback_type == FeedbackType.CORRECT:
            self.assertGreater(outcomes['target_found'], 0)
    
    def test_retraining_batch_creation(self):
        """Test retraining batch creation from side pocket."""
        side_pocket_manager = self.sar_domain.side_pocket_manager
        
        # Add multiple items to side pocket
        for i in range(6):  # Above retraining threshold
            detection_data = {
                'class': f'test_object_{i}',
                'confidence': 0.4,
                'bbox': [100+i*10, 100, 120+i*10, 120]
            }
            
            item_id = side_pocket_manager.add_to_side_pocket(
                detection_data=detection_data,
                reason=f"Test item {i}",
                category='uncertain',
                mission_context=self.mission_context
            )
            
            # Review the item
            review_result = {
                'final_category': 'confirmed_detection',
                'corrected_classification': {'class': f'corrected_object_{i}'},
                'use_for_retraining': True
            }
            side_pocket_manager.review_item(item_id, review_result, 'test_reviewer')
        
        # Try to create retraining batch
        batch_result = self.sar_domain.create_retraining_batch()
        
        self.assertEqual(batch_result.get('status'), 'success')
        self.assertGreater(batch_result.get('item_count', 0), 0)
        self.assertIn('batch_name', batch_result)
    
    def test_domain_info(self):
        """Test domain information retrieval."""
        domain_info = self.sar_domain.get_domain_info()
        
        self.assertEqual(domain_info['domain_name'], 'search_rescue')
        self.assertIn('supported_detection_categories', domain_info)
        self.assertIn('supported_mission_types', domain_info)
        self.assertIn('capabilities', domain_info)
        
        capabilities = domain_info['capabilities']
        self.assertTrue(capabilities['partial_correctness'])
        self.assertTrue(capabilities['visual_detection'])
        self.assertTrue(capabilities['side_pocket_management'])
        self.assertTrue(capabilities['continuous_learning'])


class TestSearchRescueIntegration(unittest.TestCase):
    """Test Search and Rescue integration with feedback engine."""
    
    def setUp(self):
        """Set up integration test environment."""
        self.test_dir = tempfile.mkdtemp()
        
        # Create feedback engine with SAR domain
        from feedback_loop_framework.components.memory.file_memory_store import FileMemoryStore
        memory_store = FileMemoryStore({'base_path': self.test_dir})
        self.engine = FeedbackEngine(memory_store=memory_store)
        
        # Register SAR domain
        sar_config = {
            'side_pocket_path': self.test_dir + '/sar_side_pocket',
            'retraining_threshold': 3
        }
        self.sar_domain = SearchRescueDomain(sar_config)
        self.engine.register_domain('search_rescue', self.sar_domain)
    
    def tearDown(self):
        """Clean up integration test environment."""
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def test_complete_sar_mission_workflow(self):
        """Test complete Search and Rescue mission workflow."""
        mission_scenarios = [
            # Clothing scrap found
            {
                'detection': {
                    'detections': [{
                        'class': 'fabric_red',
                        'confidence': 0.82,
                        'bbox': [150, 200, 180, 230]
                    }]
                },
                'context': {
                    'mission_id': 'INTEGRATION_TEST_001',
                    'mission_type': 'lost_child',
                    'detection_type': 'image_detection',
                    'latitude': 42.3605,
                    'longitude': -71.0592
                }
            },
            # Target person found
            {
                'detection': {
                    'detections': [{
                        'class': 'child',
                        'confidence': 0.94,
                        'bbox': [250, 100, 300, 200]
                    }]
                },
                'context': {
                    'mission_id': 'INTEGRATION_TEST_001',
                    'mission_type': 'lost_child',
                    'detection_type': 'image_detection',
                    'latitude': 42.3610,
                    'longitude': -71.0595
                }
            }
        ]
        
        results = []
        for scenario in mission_scenarios:
            result = self.engine.process_output(
                domain='search_rescue',
                raw_output=scenario['detection'],
                context=scenario['context'],
                agent_id='integration_test_drone'
            )
            results.append(result)
        
        # Verify results
        self.assertEqual(len(results), 2)
        
        # First result should be partially correct (reference item)
        self.assertIn(results[0].feedback_type, [FeedbackType.PARTIALLY_CORRECT, FeedbackType.CORRECT])
        
        # Second result should be correct (target found)
        self.assertEqual(results[1].feedback_type, FeedbackType.CORRECT)
        self.assertGreater(results[1].confidence_score, 0.8)
        
        # Check that data was stored
        entries = self.engine.memory_store.retrieve_entries(domain='search_rescue', limit=10)
        self.assertGreaterEqual(len(entries), 2)


if __name__ == '__main__':
    # Run all tests
    unittest.main(verbosity=2)

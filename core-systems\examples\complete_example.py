#!/usr/bin/env python3
"""
Complete Example: Universal Dual-Purpose Feedback Loop Framework

This example demonstrates the complete setup and usage of the feedback loop
framework with both Drone AI and TimeStamp AI domains.
"""

import logging
import time
from datetime import datetime
from typing import Dict, Any

# Import framework components
try:
    # Try relative imports first (when run as module)
    from ..core.feedback_engine import FeedbackEngine
    from ..domains.drone_ai import DroneAIDomain
    from ..domains.timestamp_ai import TimeStampAIDomain
    from ..components.confidence.adaptive_confidence_model import AdaptiveConfidenceModel
    from ..components.trust.trust_score_calculator import TrustScoreCalculator
    from ..components.memory.file_memory_store import FileMemoryStore
except ImportError:
    # Fall back to absolute imports (when run as script)
    import sys
    import os
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
    from core.feedback_engine import FeedbackEngine
    from domains.drone_ai import DroneAIDomain
    from domains.timestamp_ai import TimeStampAIDomain
    from components.confidence.adaptive_confidence_model import AdaptiveConfidenceModel
    from components.trust.trust_score_calculator import TrustScoreCalculator
    from components.memory.file_memory_store import FileMemoryStore


def setup_logging():
    """Setup logging for the example."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('feedback_loop_example.log')
        ]
    )


def create_feedback_engine() -> FeedbackEngine:
    """Create and configure the feedback engine with all components."""
    
    # Configure confidence model
    confidence_config = {
        'correct_bonus': 0.05,
        'partially_correct_penalty': 0.02,
        'incorrect_penalty': 0.10,
        'learning_rate': 0.1,
        'history_window': 100
    }
    confidence_model = AdaptiveConfidenceModel(confidence_config)
    
    # Configure trust calculator
    trust_config = {
        'correct_weight': 1.0,
        'partially_correct_weight': 0.7,
        'incorrect_weight': 0.0,
        'decay_rate': 0.95,
        'learning_rate': 0.1,
        'history_window': 100,
        'min_entries_for_trust': 5
    }
    trust_calculator = TrustScoreCalculator(trust_config)
    
    # Configure memory store
    memory_config = {
        'base_path': './feedback_data',
        'compression_enabled': True,
        'max_file_size_mb': 10,
        'retention_days': 30,
        'cache_size': 1000
    }
    memory_store = FileMemoryStore(memory_config)
    
    # Create feedback engine
    engine_config = {
        'enable_real_time': True,
        'enable_batch': True,
        'max_processing_time': 30.0
    }
    
    engine = FeedbackEngine(
        confidence_model=confidence_model,
        trust_calculator=trust_calculator,
        memory_store=memory_store,
        config=engine_config
    )
    
    return engine


def setup_domains(engine: FeedbackEngine) -> None:
    """Setup and register domain implementations."""
    
    # Configure Drone AI domain
    drone_config = {
        'thresholds': {
            'gps': {
                'accuracy_threshold': 3.0,  # Stricter GPS accuracy
                'altitude_max': 5000.0      # Lower max altitude
            },
            'mission': {
                'waypoint_tolerance': 5.0   # Tighter waypoint tolerance
            }
        },
        'custom_patterns': {
            'emergency_landing': {
                'type': 'safety_critical',
                'triggers': ['low_battery', 'high_wind', 'gps_loss'],
                'confidence_impact': -0.3
            }
        }
    }
    drone_domain = DroneAIDomain(drone_config)
    engine.register_domain('drone_ai', drone_domain)
    
    # Configure TimeStamp AI domain
    timestamp_config = {
        'thresholds': {
            'timestamp_accuracy': {
                'max_time_drift': 120.0,    # 2 minutes max drift
                'precision_threshold': 0.5  # 0.5 second precision
            },
            'environmental_impact': {
                'water_usage_max': 500.0,   # Stricter water usage
                'carbon_footprint_max': 25.0 # Lower carbon limit
            }
        },
        'extraction_patterns': {
            'custom_timestamp': r'(\d{4}/\d{2}/\d{2} \d{2}:\d{2}:\d{2})',
            'efficiency_score': r'efficiency:\s*(\d+(?:\.\d+)?%)'
        }
    }
    timestamp_domain = TimeStampAIDomain(timestamp_config)
    engine.register_domain('timestamp_ai', timestamp_domain)


def demonstrate_drone_ai_feedback(engine: FeedbackEngine) -> None:
    """Demonstrate feedback processing for Drone AI scenarios."""
    print("\n=== Drone AI Feedback Examples ===")
    
    # Example 1: Successful GPS navigation
    gps_data = {
        'latitude': 37.7749,
        'longitude': -122.4194,
        'altitude': 100.5,
        'accuracy': 2.1,
        'timestamp': datetime.utcnow().isoformat()
    }
    
    context = {
        'sensor_type': 'gps',
        'mission_type': 'waypoint_navigation',
        'mission_plan': {
            'current_waypoint': {
                'latitude': 37.7750,
                'longitude': -122.4195
            }
        },
        'waypoints_reached': 8,
        'total_waypoints': 10
    }
    
    result = engine.process_output('drone_ai', gps_data, context, agent_id='drone_001')
    print(f"GPS Navigation: {result.feedback_type.value} (confidence: {result.confidence_score:.3f})")
    
    # Example 2: Partial area scanning completion
    scan_data = {
        'area_scanned': 0.75,  # 75% completion
        'image_count': 150,
        'gps_points': 200,
        'quality_score': 0.85
    }
    
    context = {
        'sensor_type': 'camera',
        'mission_type': 'area_scanning',
        'area_coverage': 0.75,
        'target_coverage': 0.95
    }
    
    result = engine.process_output('drone_ai', scan_data, context, agent_id='drone_002')
    print(f"Area Scanning: {result.feedback_type.value} (confidence: {result.confidence_score:.3f})")
    
    # Example 3: Sensor malfunction
    faulty_data = {
        'latitude': None,
        'longitude': None,
        'altitude': -999,
        'error': 'GPS_SIGNAL_LOST'
    }
    
    context = {
        'sensor_type': 'gps',
        'mission_type': 'general',
        'emergency_status': True
    }
    
    result = engine.process_output('drone_ai', faulty_data, context, agent_id='drone_003')
    print(f"Sensor Malfunction: {result.feedback_type.value} (confidence: {result.confidence_score:.3f})")


def demonstrate_timestamp_ai_feedback(engine: FeedbackEngine) -> None:
    """Demonstrate feedback processing for TimeStamp AI scenarios."""
    print("\n=== TimeStamp AI Feedback Examples ===")
    
    # Example 1: Accurate timestamp response
    timestamp_response = {
        'timestamp': datetime.utcnow().isoformat(),
        'hash': 'a1b2c3d4e5f6789012345678901234567890123456789012345678901234567890',
        'signature': 'def456789012345678901234567890123456789012345678901234567890123456789012345678901234567890',
        'verification_status': True
    }
    
    context = {
        'output_type': 'timestamp_response',
        'ai_model': 'claude',
        'request_timestamp': datetime.utcnow().isoformat()
    }
    
    result = engine.process_output('timestamp_ai', timestamp_response, context, agent_id='timestamp_001')
    print(f"Timestamp Response: {result.feedback_type.value} (confidence: {result.confidence_score:.3f})")
    
    # Example 2: Environmental impact calculation
    impact_data = {
        'water_usage': 450.0,  # ml
        'electricity_usage': 75.0,  # Wh
        'carbon_footprint': 20.0,  # g CO2
        'token_count': 1500,
        'model_used': 'gpt-4'
    }
    
    context = {
        'output_type': 'impact_calculation',
        'ai_model': 'chatgpt',
        'query_complexity': 'medium'
    }
    
    result = engine.process_output('timestamp_ai', impact_data, context, agent_id='timestamp_002')
    print(f"Impact Calculation: {result.feedback_type.value} (confidence: {result.confidence_score:.3f})")
    
    # Example 3: Timestamp with drift
    drifted_response = {
        'timestamp': (datetime.utcnow().timestamp() - 180),  # 3 minutes ago
        'hash': 'b2c3d4e5f6789012345678901234567890123456789012345678901234567890a1',
        'verification_status': False
    }
    
    context = {
        'output_type': 'timestamp_response',
        'ai_model': 'gemini',
        'expected_accuracy': 60.0  # seconds
    }
    
    result = engine.process_output('timestamp_ai', drifted_response, context, agent_id='timestamp_003')
    print(f"Drifted Timestamp: {result.feedback_type.value} (confidence: {result.confidence_score:.3f})")


def demonstrate_batch_processing(engine: FeedbackEngine) -> None:
    """Demonstrate batch processing capabilities."""
    print("\n=== Batch Processing Example ===")
    
    # Prepare batch data
    batch_outputs = [
        {
            'output': {'latitude': 37.7749 + i*0.001, 'longitude': -122.4194 + i*0.001, 'altitude': 100 + i*10},
            'context': {'sensor_type': 'gps', 'mission_type': 'waypoint_navigation'},
            'agent_id': f'drone_batch_{i:03d}'
        }
        for i in range(10)
    ]
    
    # Process batch
    start_time = time.time()
    results = engine.batch_process('drone_ai', batch_outputs)
    processing_time = time.time() - start_time
    
    # Analyze results
    feedback_counts = {}
    for result in results:
        feedback_type = result.feedback_type.value
        feedback_counts[feedback_type] = feedback_counts.get(feedback_type, 0) + 1
    
    print(f"Processed {len(results)} entries in {processing_time:.2f} seconds")
    print(f"Feedback distribution: {feedback_counts}")


def demonstrate_analytics(engine: FeedbackEngine) -> None:
    """Demonstrate analytics and reporting capabilities."""
    print("\n=== Analytics Example ===")
    
    # Get engine statistics
    engine_stats = engine.get_statistics()
    print(f"Engine Statistics: {engine_stats}")
    
    # Get domain information
    domain_info = engine.get_domain_info()
    print(f"Registered Domains: {list(domain_info.keys())}")
    
    # Get analytics from memory store
    if engine.memory_store:
        analytics = engine.memory_store.get_analytics_data()
        print(f"Total Entries: {analytics.get('total_entries', 0)}")
        print(f"Success Rate: {analytics.get('success_rate', 0):.2%}")
        print(f"Average Confidence: {analytics.get('average_confidence', 0):.3f}")
        print(f"Average Trust: {analytics.get('average_trust', 0):.3f}")
    
    # Get trust scores
    if engine.trust_calculator:
        # Get top agents
        top_agents = engine.trust_calculator.get_top_agents(limit=5)
        print(f"Top 5 Agents by Trust Score:")
        for agent in top_agents:
            print(f"  {agent['agent_id']}: {agent['trust_score']:.3f}")


def main():
    """Main example execution."""
    print("Universal Dual-Purpose Feedback Loop Framework - Complete Example")
    print("=" * 70)
    
    # Setup logging
    setup_logging()
    
    # Create and configure feedback engine
    print("Setting up feedback engine...")
    engine = create_feedback_engine()
    
    # Setup domains
    print("Registering domains...")
    setup_domains(engine)
    
    # Perform health check
    health = engine.health_check()
    print(f"System Health: {health['engine']}")
    
    # Demonstrate drone AI feedback
    demonstrate_drone_ai_feedback(engine)
    
    # Demonstrate timestamp AI feedback
    demonstrate_timestamp_ai_feedback(engine)
    
    # Demonstrate batch processing
    demonstrate_batch_processing(engine)
    
    # Wait a moment for processing
    time.sleep(1)
    
    # Demonstrate analytics
    demonstrate_analytics(engine)
    
    print("\n" + "=" * 70)
    print("Example completed successfully!")
    print("Check 'feedback_data' directory for stored feedback entries.")
    print("Check 'feedback_loop_example.log' for detailed logs.")


if __name__ == "__main__":
    main()

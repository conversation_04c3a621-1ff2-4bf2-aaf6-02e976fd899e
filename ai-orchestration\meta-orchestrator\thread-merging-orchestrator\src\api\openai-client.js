import OpenAI from 'openai';
import { config } from '../config/index.js';
import { logger, logRequest, logResponse } from '../utils/logger.js';
import { RateLimiterMemory } from 'rate-limiter-flexible';

class OpenAIClient {
  constructor() {
    this.client = new OpenAI({
      apiKey: config.apis.openai.apiKey,
      baseURL: config.apis.openai.baseURL
    });

    // Rate limiter
    this.rateLimiter = new RateLimiterMemory({
      keyGenerator: () => 'openai',
      points: config.rateLimiting.requestsPerMinute,
      duration: 60,
      blockDuration: 60
    });
  }

  async rateLimit() {
    try {
      await this.rateLimiter.consume('openai');
    } catch (rejRes) {
      const waitTime = Math.round(rejRes.msBeforeNext / 1000);
      logger.warn(`OpenAI rate limit hit, waiting ${waitTime} seconds`);
      await new Promise(resolve => setTimeout(resolve, rejRes.msBeforeNext));
    }
  }

  async listConversations(limit = 20) {
    await this.rateLimit();
    logRequest('OpenAI', 'listConversations', { limit });

    try {
      // Note: OpenAI doesn't have a direct conversations API
      // This would need to be implemented based on how you store/access conversations
      // For now, this is a placeholder that would need actual implementation
      
      const response = await this.client.chat.completions.create({
        model: config.apis.openai.model,
        messages: [{
          role: 'system',
          content: 'List recent conversations - this is a placeholder implementation'
        }],
        max_tokens: 100
      });

      logResponse('OpenAI', 'listConversations', true);
      return {
        conversations: [],
        message: 'OpenAI conversation listing requires custom implementation based on your storage method'
      };
    } catch (error) {
      logResponse('OpenAI', 'listConversations', false, { error: error.message });
      throw error;
    }
  }

  async getConversation(conversationId) {
    await this.rateLimit();
    logRequest('OpenAI', 'getConversation', { conversationId });

    try {
      // Placeholder implementation
      // In practice, you'd retrieve the conversation from your storage
      
      logResponse('OpenAI', 'getConversation', true);
      return {
        id: conversationId,
        messages: [],
        created_at: new Date().toISOString(),
        source: 'chatgpt'
      };
    } catch (error) {
      logResponse('OpenAI', 'getConversation', false, { error: error.message });
      throw error;
    }
  }

  async searchConversations(query, limit = 10) {
    await this.rateLimit();
    logRequest('OpenAI', 'searchConversations', { query, limit });

    try {
      // This would implement semantic search across your stored conversations
      // Using embeddings to find relevant conversations
      
      const embedding = await this.createEmbedding(query);
      
      // Placeholder for actual search implementation
      logResponse('OpenAI', 'searchConversations', true);
      return {
        conversations: [],
        query,
        embedding
      };
    } catch (error) {
      logResponse('OpenAI', 'searchConversations', false, { error: error.message });
      throw error;
    }
  }

  async createEmbedding(text) {
    await this.rateLimit();
    logRequest('OpenAI', 'createEmbedding', { textLength: text.length });

    try {
      const response = await this.client.embeddings.create({
        model: config.search.embeddingModel,
        input: text
      });

      logResponse('OpenAI', 'createEmbedding', true);
      return response.data[0].embedding;
    } catch (error) {
      logResponse('OpenAI', 'createEmbedding', false, { error: error.message });
      throw error;
    }
  }

  async generateCompletion(messages, options = {}) {
    await this.rateLimit();
    logRequest('OpenAI', 'generateCompletion', { 
      messageCount: messages.length,
      model: options.model || config.apis.openai.model
    });

    try {
      const response = await this.client.chat.completions.create({
        model: options.model || config.apis.openai.model,
        messages,
        max_tokens: options.maxTokens || 4096,
        temperature: options.temperature || 0.7,
        ...options
      });

      logResponse('OpenAI', 'generateCompletion', true);
      return response.choices[0].message.content;
    } catch (error) {
      logResponse('OpenAI', 'generateCompletion', false, { error: error.message });
      throw error;
    }
  }
}

export default OpenAIClient;

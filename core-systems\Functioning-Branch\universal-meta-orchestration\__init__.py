"""
Universal Meta-Orchestration System

The ultimate orchestration platform that unifies ALL orchestration layers
into a single, cohesive, self-improving system:

1. Thread-Merging Orchestration (Multi-platform AI chatbot coordination)
2. Darwin Gödel Machine Orchestration (Self-improving evolutionary system)
3. Universal Feedback Loop Framework (Quality assurance across all domains)
4. AI Assistant Orchestration (Branded coding assistant coordination)
5. Code Orchestration (Multi-IDE development workflow)

Key Features:
- Unified control plane for all orchestration systems
- Cross-system communication and intelligent routing
- Global state management and optimization
- Universal quality assurance and monitoring
- Continuous learning and system evolution
- Production-ready enterprise capabilities

Example Usage:
    from universal_meta_orchestration import create_meta_orchestration_engine
    
    # Create meta-orchestration engine
    meta_engine = create_meta_orchestration_engine({
        'meta_orchestration_mode': 'intelligent',
        'cross_system_learning': True,
        'global_optimization': True
    })
    
    # Execute cross-system workflow
    result = await meta_engine.execute_meta_task(
        task_description="Complete AI-powered development workflow",
        task_type="code_development",
        input_data={
            'project_requirements': 'Build REST API',
            'programming_language': 'python'
        },
        required_systems=['ai_assistant', 'feedback_loop']
    )
    
    print(f"Success: {result.success}")
    print(f"Systems Used: {' → '.join(result.execution_path)}")
"""

__version__ = "1.0.0"
__author__ = "Universal Meta-Orchestration Team"
__email__ = "<EMAIL>"
__description__ = "Universal Meta-Orchestration System - Unifying All AI Orchestration Layers"

# Core exports
from .core.meta_orchestration_engine import (
    UniversalMetaOrchestrationEngine,
    create_meta_orchestration_engine,
    MetaOrchestrationMode,
    SystemPriority,
    OrchestrationSystem,
    MetaTask,
    MetaResult
)

# Utility functions
def create_complete_meta_orchestration_system(config=None):
    """
    Create a complete meta-orchestration system with all available components.
    
    Args:
        config: Optional configuration dictionary
        
    Returns:
        Configured UniversalMetaOrchestrationEngine with all systems
    """
    default_config = {
        'meta_orchestration_mode': 'intelligent',
        'max_concurrent_meta_tasks': 10,
        'cross_system_learning': True,
        'global_optimization': True,
        'real_time_adaptation': True,
        
        # Feedback Loop Framework configuration
        'feedback_loop': {
            'auto_domain_creation': True,
            'domain_learning_enabled': True,
            'drone_ai': {
                'search_rescue': {'enabled': True},
                'species_tracking': {'enabled': True},
                'mining_ore': {'enabled': True},
                'real_estate_construction': {'enabled': True}
            },
            'timestamp_ai': {
                'llm_validation': {'enabled': True},
                'environmental_impact': {'enabled': True},
                'ecostamp': {'enabled': True}
            }
        },
        
        # AI Assistant Orchestration configuration
        'ai_assistant': {
            'orchestration_mode': 'adaptive',
            'max_concurrent_tasks': 15,
            'performance_learning': True,
            'compliance': {
                'strict_mode': True,
                'compliance_checks_enabled': True
            },
            'agents': {
                'health_check_interval': 60,
                'performance_tracking': True,
                'auto_discovery': True
            }
        },
        
        # Thread-Merging Orchestration configuration (placeholder)
        'thread_merge': {
            'platforms': ['chatgpt', 'claude', 'gemini', 'perplexity'],
            'relevance_threshold': 0.7,
            'merge_strategy': 'intelligent'
        },
        
        # Darwin Gödel Machine configuration (placeholder)
        'darwin_godel': {
            'self_improvement_enabled': True,
            'evolution_rate': 'adaptive',
            'optimization_targets': ['performance', 'quality', 'efficiency']
        },
        
        # Code Orchestration configuration (placeholder)
        'code_orchestration': {
            'supported_ides': ['augment_code', 'vscode', 'intellij'],
            'multi_ide_sync': True,
            'workflow_automation': True
        }
    }
    
    if config:
        # Deep merge configuration
        def deep_merge(base, override):
            for key, value in override.items():
                if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                    deep_merge(base[key], value)
                else:
                    base[key] = value
        
        deep_merge(default_config, config)
    
    return create_meta_orchestration_engine(default_config)


def quick_start_meta_orchestration():
    """
    Quick start meta-orchestration with minimal configuration.
    
    Returns:
        Ready-to-use UniversalMetaOrchestrationEngine
    """
    config = {
        'meta_orchestration_mode': 'adaptive',
        'cross_system_learning': True,
        'global_optimization': False,  # Disabled for quick start
        'feedback_loop': {
            'auto_domain_creation': True,
            'domain_learning_enabled': True
        },
        'ai_assistant': {
            'orchestration_mode': 'adaptive',
            'performance_learning': True,
            'compliance': {'strict_mode': False}  # Relaxed for quick start
        }
    }
    
    meta_engine = create_meta_orchestration_engine(config)
    
    print("🚀 Universal Meta-Orchestration System - Quick Start")
    print("✅ Meta-orchestration engine initialized")
    print(f"📊 Available systems: {len(meta_engine.orchestration_systems)}")
    
    return meta_engine


# Package metadata
__all__ = [
    # Core classes
    'UniversalMetaOrchestrationEngine',
    'create_meta_orchestration_engine',
    'MetaOrchestrationMode',
    'SystemPriority',
    'OrchestrationSystem',
    'MetaTask',
    'MetaResult',
    
    # Utility functions
    'create_complete_meta_orchestration_system',
    'quick_start_meta_orchestration'
]

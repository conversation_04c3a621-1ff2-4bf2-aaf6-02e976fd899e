"""
Reference Item Matcher for Search and Rescue

Pattern matching component that analyzes detected items against reference criteria
and mission parameters to determine relevance and accuracy for Search and Rescue operations.
"""

import logging
import math
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from collections import defaultdict


class ReferenceItemMatcher:
    """
    Matches detected items against Search and Rescue reference criteria.
    
    Analyzes detection patterns, validates against mission parameters,
    and identifies potential false positives or missed detections.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.patterns = {}
        self.reference_criteria = self._initialize_reference_criteria()
        self.mission_thresholds = self._initialize_mission_thresholds()
        
    def _initialize_reference_criteria(self) -> Dict[str, Any]:
        """Initialize reference criteria for Search and Rescue matching."""
        return {
            'target_person': {
                'size_range_m': (0.3, 2.0),  # Height range for child/adult
                'movement_indicators': ['position_change', 'gesture', 'response_to_drone'],
                'visual_features': ['clothing_color', 'body_posture', 'facial_features'],
                'priority_multiplier': 10.0
            },
            'clothing': {
                'color_matching': True,
                'fabric_texture': ['cotton', 'denim', 'synthetic'],
                'condition_states': ['torn', 'intact', 'weathered'],
                'size_range_cm': (5, 100),  # Size range for clothing items
                'priority_multiplier': 3.0
            },
            'personal_items': {
                'common_items': ['backpack', 'toy', 'blanket', 'shoes', 'electronics'],
                'age_appropriate': True,  # Match items to target age
                'condition_relevance': ['new', 'used', 'damaged'],
                'size_range_cm': (2, 50),
                'priority_multiplier': 2.5
            },
            'broken_environment': {
                'disturbance_types': ['trampled', 'broken', 'displaced', 'compressed'],
                'freshness_indicators': ['recent', 'old', 'weathered'],
                'pattern_consistency': True,  # Look for consistent trail patterns
                'size_range_cm': (10, 200),
                'priority_multiplier': 1.5
            }
        }
    
    def _initialize_mission_thresholds(self) -> Dict[str, Any]:
        """Initialize mission-specific thresholds and parameters."""
        return {
            'detection_clustering': {
                'max_cluster_distance_m': 50.0,  # Max distance for related detections
                'min_cluster_size': 2,           # Minimum detections for a cluster
                'cluster_confidence_boost': 0.15 # Confidence boost for clustered items
            },
            'temporal_analysis': {
                'max_time_gap_minutes': 30,     # Max time between related detections
                'pattern_consistency_threshold': 0.7,
                'movement_speed_threshold_mps': 2.0  # Max reasonable movement speed
            },
            'environmental_factors': {
                'weather_impact': {
                    'clear': 1.0,
                    'cloudy': 0.9,
                    'rainy': 0.7,
                    'foggy': 0.6
                },
                'lighting_impact': {
                    'daylight': 1.0,
                    'dawn_dusk': 0.8,
                    'night': 0.5
                },
                'terrain_difficulty': {
                    'open_field': 1.0,
                    'light_forest': 0.8,
                    'dense_forest': 0.6,
                    'rocky_terrain': 0.7
                }
            },
            'false_positive_filters': {
                'common_misidentifications': [
                    'plastic_bags_as_clothing',
                    'animal_tracks_as_footprints',
                    'natural_debris_as_personal_items',
                    'shadows_as_persons'
                ],
                'size_anomaly_threshold': 3.0,  # Standard deviations from expected size
                'confidence_penalty': 0.2
            }
        }
    
    def match(self, interpreted_output: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Match interpreted detections against Search and Rescue reference criteria.
        
        Args:
            interpreted_output: Normalized detection data from interpreter
            context: Mission context including target info, search area, conditions
            
        Returns:
            Match results with relevance scores and pattern analysis
        """
        try:
            match_results = {
                'overall_relevance': 0.0,
                'pattern_matches': [],
                'anomalies': [],
                'warnings': [],
                'clusters': [],
                'temporal_analysis': {},
                'metadata': {
                    'match_timestamp': datetime.utcnow().isoformat(),
                    'matcher_version': '1.0.0',
                    'mission_id': context.get('mission_id', 'unknown')
                }
            }
            
            # Extract classifications and mission parameters
            classifications = interpreted_output.get('classifications', [])
            mission_params = context.get('mission_parameters', {})
            
            # Perform reference matching
            self._match_against_references(classifications, mission_params, match_results)
            
            # Analyze detection clusters
            self._analyze_detection_clusters(interpreted_output, context, match_results)
            
            # Perform temporal analysis
            self._perform_temporal_analysis(interpreted_output, context, match_results)
            
            # Apply environmental factors
            self._apply_environmental_factors(match_results, context)
            
            # Check for false positives
            self._check_false_positives(classifications, match_results)
            
            # Calculate overall relevance
            self._calculate_overall_relevance(match_results)
            
            return match_results
            
        except Exception as e:
            self.logger.error(f"Error in reference matching: {str(e)}")
            return {
                'overall_relevance': 0.0,
                'pattern_matches': [],
                'anomalies': [{'type': 'matcher_error', 'message': str(e)}],
                'warnings': [],
                'metadata': {'error': True, 'error_message': str(e)}
            }
    
    def _match_against_references(self, classifications: List[Dict[str, Any]], 
                                 mission_params: Dict[str, Any], 
                                 match_results: Dict[str, Any]) -> None:
        """Match classifications against reference criteria."""
        target_age = mission_params.get('target_age', 'unknown')
        target_description = mission_params.get('target_description', {})
        
        for classification in classifications:
            category = classification.get('sar_category', 'unknown')
            confidence = classification.get('confidence', 0.0)
            
            if category in self.reference_criteria:
                criteria = self.reference_criteria[category]
                
                # Calculate reference match score
                match_score = self._calculate_reference_match_score(
                    classification, criteria, target_description
                )
                
                # Apply priority multiplier
                weighted_score = match_score * criteria.get('priority_multiplier', 1.0)
                
                if match_score > 0.5:  # Significant match
                    match_results['pattern_matches'].append({
                        'type': f'{category}_reference_match',
                        'classification': classification,
                        'match_score': match_score,
                        'weighted_score': weighted_score,
                        'criteria_met': self._get_criteria_met(classification, criteria),
                        'confidence': confidence,
                        'message': f'Strong {category} reference match'
                    })
                elif match_score > 0.2:  # Partial match
                    match_results['warnings'].append({
                        'type': f'partial_{category}_match',
                        'classification': classification,
                        'match_score': match_score,
                        'message': f'Partial {category} match - requires verification',
                        'confidence_impact': -0.1
                    })
    
    def _calculate_reference_match_score(self, classification: Dict[str, Any], 
                                       criteria: Dict[str, Any], 
                                       target_description: Dict[str, Any]) -> float:
        """Calculate how well a classification matches reference criteria."""
        score = 0.0
        total_factors = 0
        
        # Size matching
        if 'size_range_m' in criteria or 'size_range_cm' in criteria:
            size_score = self._check_size_match(classification, criteria)
            score += size_score
            total_factors += 1
        
        # Color matching for clothing
        if criteria.get('color_matching') and 'clothing_color' in target_description:
            color_score = self._check_color_match(classification, target_description)
            score += color_score
            total_factors += 1
        
        # Age appropriateness for personal items
        if criteria.get('age_appropriate'):
            age_score = self._check_age_appropriateness(classification, target_description)
            score += age_score
            total_factors += 1
        
        # Condition relevance
        if 'condition_states' in criteria:
            condition_score = self._check_condition_relevance(classification, criteria)
            score += condition_score
            total_factors += 1
        
        # Base confidence score
        confidence = classification.get('confidence', 0.0)
        score += confidence
        total_factors += 1
        
        return score / total_factors if total_factors > 0 else 0.0
    
    def _check_size_match(self, classification: Dict[str, Any], criteria: Dict[str, Any]) -> float:
        """Check if detected item size matches expected range."""
        # This would use bounding box data to estimate real-world size
        bbox = classification.get('bounding_box')
        if not bbox:
            return 0.5  # Neutral score if no size data
        
        # Simplified size estimation (would be more complex in real implementation)
        estimated_size = self._estimate_real_world_size(bbox)
        
        if 'size_range_m' in criteria:
            min_size, max_size = criteria['size_range_m']
            if min_size <= estimated_size <= max_size:
                return 1.0
            elif estimated_size < min_size * 0.5 or estimated_size > max_size * 2:
                return 0.0
            else:
                return 0.5
        
        return 0.5
    
    def _estimate_real_world_size(self, bbox: Any) -> float:
        """Estimate real-world size from bounding box (simplified)."""
        # This would use camera parameters, altitude, and perspective
        # For now, return a placeholder estimation
        if isinstance(bbox, (list, tuple)) and len(bbox) >= 4:
            width = abs(bbox[2] - bbox[0]) if len(bbox) >= 4 else 0
            height = abs(bbox[3] - bbox[1]) if len(bbox) >= 4 else 0
            # Simplified estimation - would be much more complex in reality
            return max(width, height) * 0.01  # Convert to meters (placeholder)
        return 0.5
    
    def _check_color_match(self, classification: Dict[str, Any], target_description: Dict[str, Any]) -> float:
        """Check color matching for clothing items."""
        # This would analyze color data from the detection
        # For now, return a placeholder score
        return 0.7  # Placeholder
    
    def _check_age_appropriateness(self, classification: Dict[str, Any], target_description: Dict[str, Any]) -> float:
        """Check if personal item is age-appropriate for target."""
        target_age = target_description.get('age', 'unknown')
        item_type = classification.get('subcategory', 'unknown')
        
        # Age-appropriate item mapping
        age_items = {
            'child': ['toy', 'stuffed_animals', 'small_backpack'],
            'teen': ['electronics', 'backpack', 'sports_items'],
            'adult': ['electronics', 'large_backpack', 'tools']
        }
        
        if target_age in age_items and item_type in age_items[target_age]:
            return 1.0
        elif target_age == 'unknown':
            return 0.5
        else:
            return 0.3
    
    def _check_condition_relevance(self, classification: Dict[str, Any], criteria: Dict[str, Any]) -> float:
        """Check if item condition is relevant to search scenario."""
        # This would analyze wear patterns, damage, etc.
        # For now, return a placeholder score
        return 0.6  # Placeholder
    
    def _get_criteria_met(self, classification: Dict[str, Any], criteria: Dict[str, Any]) -> List[str]:
        """Get list of criteria that were met."""
        met_criteria = []
        
        if classification.get('confidence', 0) > 0.7:
            met_criteria.append('high_confidence')
        
        if classification.get('meets_threshold', False):
            met_criteria.append('confidence_threshold')
        
        if classification.get('priority') in ['high', 'critical']:
            met_criteria.append('high_priority')
        
        return met_criteria
    
    def _analyze_detection_clusters(self, interpreted_output: Dict[str, Any], 
                                  context: Dict[str, Any], 
                                  match_results: Dict[str, Any]) -> None:
        """Analyze spatial clustering of detections."""
        classifications = interpreted_output.get('classifications', [])
        spatial_data = interpreted_output.get('spatial_data', {})
        
        if len(classifications) < 2:
            return
        
        # Group detections by proximity
        clusters = self._find_spatial_clusters(classifications, spatial_data)
        
        for cluster in clusters:
            if len(cluster['detections']) >= self.mission_thresholds['detection_clustering']['min_cluster_size']:
                cluster_analysis = self._analyze_cluster(cluster)
                match_results['clusters'].append(cluster_analysis)
                
                # Boost confidence for clustered detections
                if cluster_analysis['relevance_score'] > 0.7:
                    match_results['pattern_matches'].append({
                        'type': 'detection_cluster',
                        'cluster': cluster_analysis,
                        'confidence': cluster_analysis['relevance_score'],
                        'message': f'Significant detection cluster found with {len(cluster["detections"])} items'
                    })
    
    def _find_spatial_clusters(self, classifications: List[Dict[str, Any]], 
                              spatial_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Find spatial clusters of detections."""
        # Simplified clustering - would use more sophisticated algorithms in practice
        clusters = []
        max_distance = self.mission_thresholds['detection_clustering']['max_cluster_distance_m']
        
        # For now, create a single cluster with all detections
        # Real implementation would use DBSCAN or similar clustering algorithm
        if classifications:
            clusters.append({
                'center_gps': spatial_data.get('gps_coordinates', {}),
                'detections': classifications,
                'radius_m': max_distance / 2
            })
        
        return clusters
    
    def _analyze_cluster(self, cluster: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze a detection cluster for relevance."""
        detections = cluster['detections']
        
        # Calculate cluster statistics
        categories = [d.get('sar_category', 'unknown') for d in detections]
        confidences = [d.get('confidence', 0.0) for d in detections]
        
        category_counts = {}
        for category in categories:
            category_counts[category] = category_counts.get(category, 0) + 1
        
        # Calculate relevance score
        avg_confidence = sum(confidences) / len(confidences) if confidences else 0.0
        category_diversity = len(set(categories))
        
        # Higher score for diverse categories (suggests person was there)
        diversity_bonus = min(category_diversity * 0.2, 0.6)
        relevance_score = avg_confidence + diversity_bonus
        
        return {
            'detection_count': len(detections),
            'category_distribution': category_counts,
            'average_confidence': avg_confidence,
            'category_diversity': category_diversity,
            'relevance_score': min(relevance_score, 1.0),
            'center_location': cluster.get('center_gps', {}),
            'analysis_timestamp': datetime.utcnow().isoformat()
        }
    
    def _perform_temporal_analysis(self, interpreted_output: Dict[str, Any], 
                                  context: Dict[str, Any], 
                                  match_results: Dict[str, Any]) -> None:
        """Perform temporal analysis of detection patterns."""
        # This would analyze detection timing patterns
        # For now, add placeholder temporal analysis
        match_results['temporal_analysis'] = {
            'detection_timestamp': interpreted_output.get('timestamp'),
            'mission_duration': context.get('mission_duration_minutes', 0),
            'detection_frequency': 'normal',  # Placeholder
            'pattern_consistency': 0.7  # Placeholder
        }
    
    def _apply_environmental_factors(self, match_results: Dict[str, Any], context: Dict[str, Any]) -> None:
        """Apply environmental factor adjustments to match scores."""
        weather = context.get('weather_conditions', 'clear')
        lighting = context.get('lighting_conditions', 'daylight')
        terrain = context.get('terrain_type', 'open_field')
        
        # Get environmental impact factors
        weather_factor = self.mission_thresholds['environmental_factors']['weather_impact'].get(weather, 0.8)
        lighting_factor = self.mission_thresholds['environmental_factors']['lighting_impact'].get(lighting, 0.8)
        terrain_factor = self.mission_thresholds['environmental_factors']['terrain_difficulty'].get(terrain, 0.8)
        
        # Apply environmental adjustment
        environmental_factor = weather_factor * lighting_factor * terrain_factor
        
        # Adjust pattern match confidences
        for pattern_match in match_results['pattern_matches']:
            original_confidence = pattern_match.get('confidence', 0.0)
            adjusted_confidence = original_confidence * environmental_factor
            pattern_match['environmental_adjusted_confidence'] = adjusted_confidence
            pattern_match['environmental_factor'] = environmental_factor
        
        # Add environmental warning if conditions are poor
        if environmental_factor < 0.6:
            match_results['warnings'].append({
                'type': 'poor_environmental_conditions',
                'environmental_factor': environmental_factor,
                'weather': weather,
                'lighting': lighting,
                'terrain': terrain,
                'message': 'Poor environmental conditions may affect detection accuracy',
                'confidence_impact': -(1.0 - environmental_factor) * 0.5
            })
    
    def _check_false_positives(self, classifications: List[Dict[str, Any]], match_results: Dict[str, Any]) -> None:
        """Check for common false positive patterns."""
        false_positive_filters = self.mission_thresholds['false_positive_filters']
        
        for classification in classifications:
            # Check for size anomalies
            if self._is_size_anomaly(classification):
                match_results['warnings'].append({
                    'type': 'size_anomaly',
                    'classification': classification,
                    'message': 'Detected item size is anomalous',
                    'confidence_impact': -false_positive_filters['confidence_penalty']
                })
            
            # Check for common misidentifications
            category = classification.get('sar_category', 'unknown')
            subcategory = classification.get('subcategory', 'unknown')
            
            for misidentification in false_positive_filters['common_misidentifications']:
                if misidentification in f"{category}_{subcategory}":
                    match_results['warnings'].append({
                        'type': 'potential_misidentification',
                        'classification': classification,
                        'misidentification_type': misidentification,
                        'message': f'Potential misidentification: {misidentification}',
                        'confidence_impact': -false_positive_filters['confidence_penalty']
                    })
    
    def _is_size_anomaly(self, classification: Dict[str, Any]) -> bool:
        """Check if detection has anomalous size."""
        # Simplified size anomaly detection
        # Real implementation would use statistical analysis
        return False  # Placeholder
    
    def _calculate_overall_relevance(self, match_results: Dict[str, Any]) -> None:
        """Calculate overall relevance score for the detection."""
        relevance = 0.0
        
        # Weight pattern matches
        for pattern_match in match_results['pattern_matches']:
            confidence = pattern_match.get('environmental_adjusted_confidence', 
                                         pattern_match.get('confidence', 0.0))
            weight = pattern_match.get('weighted_score', 1.0)
            relevance += confidence * weight
        
        # Apply cluster bonuses
        for cluster in match_results['clusters']:
            relevance += cluster.get('relevance_score', 0.0) * 0.5
        
        # Apply warning penalties
        for warning in match_results['warnings']:
            impact = warning.get('confidence_impact', -0.1)
            relevance += impact
        
        # Normalize to 0-1 range
        match_results['overall_relevance'] = max(0.0, min(1.0, relevance))
    
    def add_pattern(self, pattern_id: str, pattern_data: Dict[str, Any]) -> None:
        """Add a new matching pattern."""
        self.patterns[pattern_id] = pattern_data
        self.logger.info(f"Added matching pattern: {pattern_id}")
    
    def remove_pattern(self, pattern_id: str) -> bool:
        """Remove a matching pattern."""
        if pattern_id in self.patterns:
            del self.patterns[pattern_id]
            self.logger.info(f"Removed matching pattern: {pattern_id}")
            return True
        return False
    
    def update_reference_criteria(self, category: str, criteria: Dict[str, Any]) -> None:
        """Update reference criteria for a category."""
        if category in self.reference_criteria:
            self.reference_criteria[category].update(criteria)
        else:
            self.reference_criteria[category] = criteria
        self.logger.info(f"Updated reference criteria for category: {category}")
    
    def get_reference_criteria(self) -> Dict[str, Any]:
        """Get current reference criteria."""
        return self.reference_criteria.copy()

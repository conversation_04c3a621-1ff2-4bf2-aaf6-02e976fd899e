import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit'
import { io, Socket } from 'socket.io-client'

interface SocketState {
  socket: Socket | null
  isConnected: boolean
  connectionCount: number
  error: string | null
}

const initialState: SocketState = {
  socket: null,
  isConnected: false,
  connectionCount: 0,
  error: null,
}

export const connectSocket = createAsyncThunk(
  'socket/connect',
  async (_, { getState }) => {
    const state = getState() as { auth: { token: string | null; user: any } }
    const token = state.auth.token
    
    if (!token) {
      throw new Error('No authentication token')
    }

    const socket = io('http://localhost:3001', {
      auth: {
        token,
      },
      transports: ['websocket', 'polling'],
    })

    return new Promise<Socket>((resolve, reject) => {
      socket.on('connect', () => {
        console.log('Socket connected:', socket.id)
        resolve(socket)
      })

      socket.on('connect_error', (error) => {
        console.error('Socket connection error:', error)
        reject(error)
      })

      socket.on('disconnect', (reason) => {
        console.log('Socket disconnected:', reason)
      })
    })
  }
)

export const disconnectSocket = createAsyncThunk(
  'socket/disconnect',
  async (_, { getState }) => {
    const state = getState() as { socket: SocketState }
    const socket = state.socket.socket
    
    if (socket) {
      socket.disconnect()
    }
  }
)

const socketSlice = createSlice({
  name: 'socket',
  initialState,
  reducers: {
    setSocket: (state, action: PayloadAction<Socket>) => {
      state.socket = action.payload
      state.isConnected = true
      state.error = null
    },
    setConnectionCount: (state, action: PayloadAction<number>) => {
      state.connectionCount = action.payload
    },
    socketDisconnected: (state) => {
      state.socket = null
      state.isConnected = false
    },
    setSocketError: (state, action: PayloadAction<string>) => {
      state.error = action.payload
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(connectSocket.fulfilled, (state, action) => {
        state.socket = action.payload
        state.isConnected = true
        state.error = null
      })
      .addCase(connectSocket.rejected, (state, action) => {
        state.socket = null
        state.isConnected = false
        state.error = action.error.message || 'Connection failed'
      })
      .addCase(disconnectSocket.fulfilled, (state) => {
        state.socket = null
        state.isConnected = false
        state.error = null
      })
  },
})

export const { setSocket, setConnectionCount, socketDisconnected, setSocketError } = socketSlice.actions
export default socketSlice.reducer

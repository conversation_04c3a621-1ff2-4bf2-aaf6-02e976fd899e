# Universal Dual-Purpose Feedback Loop Framework

A comprehensive, domain-agnostic feedback loop system that provides continuous QA and self-improvement across AI domains. Now featuring specialized **Search and Rescue** capabilities with advanced visual detection, side pocket error handling, and continuous learning.

## 🎯 Key Features

- **Universal Architecture**: Modular, pluggable design supporting multiple AI domains
- **Partial Correctness**: Full support for "Partially Correct" feedback scenarios
- **Real-time & Batch Processing**: Flexible processing modes for different use cases
- **Adaptive Learning**: Self-improving confidence and trust scoring
- **Side Pocket Management**: Advanced error handling with retraining pipeline
- **Developer-Only Operation**: Silent, secure operation with comprehensive logging
- **Multi-Domain Support**: Drone AI, TimeStamp AI, and Search & Rescue

## 🚁 Search and Rescue Domain

### Mission Scenario: Lost Child
The framework now includes a specialized Search and Rescue domain designed for drone-based search operations:

```python
# Example: Lost Child Search Mission
mission_context = {
    'mission_id': 'SAR_2025_001',
    'mission_type': 'lost_child',
    'target_age': 'child',
    'target_description': {
        'age': 8,
        'clothing': {'shirt': 'red', 'pants': 'blue_jeans'},
        'items': ['backpack', 'stuffed_bear']
    },
    'search_area_km2': 2.5,
    'weather_conditions': 'partly_cloudy',
    'terrain_type': 'mixed_forest'
}

# Process drone detection
result = engine.process_output(
    domain='search_rescue',
    raw_output=detection_data,
    context=mission_context,
    agent_id='sar_drone_01'
)
```

### Detection Categories
- **Target Person**: Direct identification of missing person
- **Clothing**: Fabric scraps, clothing items, accessories
- **Personal Items**: Backpacks, toys, electronics, belongings
- **Broken Environment**: Disturbed vegetation, footprints, trails

### Mission Outcomes
- **Target Found** = Mission Complete (Correct)
- **Target Likely** = High probability detection (Partially Correct)
- **Reference Items Found** = Relevant clues discovered (Partially Correct)
- **Search Incomplete** = Partial area coverage (Partially Correct)
- **No Findings** = Complete search, no results (Incorrect)

### Side Pocket System
Uncertain or erroneous detections are automatically placed in a "side pocket" for review:

```python
# Automatic side pocket placement for:
# - Low confidence detections (< 50%)
# - Potential misidentifications
# - Environmental artifacts
# - Size anomalies

# Review and retraining workflow
pending_items = sar_domain.get_pending_reviews(limit=20)
for item in pending_items:
    # Human review process
    review_result = {
        'final_category': 'confirmed_detection',
        'corrected_classification': {'class': 'child_clothing'},
        'use_for_retraining': True
    }
    sar_domain.review_side_pocket_item(item['item_id'], review_result)

# Create retraining batch
batch_result = sar_domain.create_retraining_batch()
```

## 🚀 Quick Start

### Installation
```bash
pip install feedback-loop-framework
```

### Basic Usage
```python
from feedback_loop_framework import quick_start

# Initialize with all domains
engine = quick_start()

# Process Search and Rescue detection
result = engine.process_output(
    domain='search_rescue',
    raw_output={
        'detections': [{
            'class': 'child',
            'confidence': 0.92,
            'bbox': [250, 100, 300, 200]
        }]
    },
    context={
        'mission_type': 'lost_child',
        'latitude': 42.3601,
        'longitude': -71.0589
    },
    agent_id='sar_drone_01'
)

print(f"Mission Outcome: {result.feedback_type.value}")
print(f"Confidence: {result.confidence_score:.3f}")
```

### CLI Usage
```bash
# Search and Rescue mission management
feedback-cli sar-mission summary --mission-id SAR_2025_001
feedback-cli sar-mission outcomes

# Side pocket management
feedback-cli side-pocket pending --limit 10
feedback-cli side-pocket analytics
feedback-cli side-pocket retraining

# Process detection
feedback-cli process search_rescue \
  --input-data '{"detections": [{"class": "child", "confidence": 0.92}]}' \
  --context '{"mission_type": "lost_child"}' \
  --agent-id sar_drone_01
```

## 📊 Feedback Types & Scenarios

### Search and Rescue Examples

#### ✅ Correct (Target Found)
```python
# High confidence target detection
detection = {
    'class': 'child',
    'confidence': 0.94,
    'verification': 'confirmed'
}
# Result: FeedbackType.CORRECT
```

#### ⚠️ Partially Correct Scenarios
```python
# Scenario 1: Reference items found
detection = {
    'class': 'child_backpack',
    'confidence': 0.88,
    'relevance': 'high'
}
# Result: FeedbackType.PARTIALLY_CORRECT

# Scenario 2: Possible target (requires verification)
detection = {
    'class': 'person_small',
    'confidence': 0.75,
    'verification': 'pending'
}
# Result: FeedbackType.PARTIALLY_CORRECT

# Scenario 3: Incomplete search coverage
mission_status = {
    'area_covered': 0.75,  # 75% of planned area
    'target_coverage': 0.95,
    'findings': ['clothing_scraps']
}
# Result: FeedbackType.PARTIALLY_CORRECT
```

#### ❌ Incorrect (No Findings)
```python
# Complete search with no relevant findings
mission_result = {
    'area_covered': 1.0,  # 100% coverage
    'detections': [],
    'false_positives': 3
}
# Result: FeedbackType.INCORRECT
```

## 🏗️ Architecture

```
feedback-loop-framework/
├── core/                    # Core feedback engine and types
├── domains/                 # Domain-specific implementations
│   ├── drone_ai/           # Drone sensor data processing
│   ├── timestamp_ai/       # LLM output and environmental impact
│   └── search_rescue/      # Visual detection and mission validation
├── components/             # Pluggable components
│   ├── confidence/         # Adaptive confidence models
│   ├── trust/             # Trust score calculators
│   └── memory/            # Storage and analytics
├── cli/                   # Command-line interface
├── config/                # Configuration management
├── examples/              # Usage examples and scenarios
└── tests/                 # Comprehensive test suite
```

## 📈 Analytics & Monitoring

### Mission Analytics
```python
# Get mission outcomes
outcomes = sar_domain.get_mission_outcomes()
print(f"Success Rate: {outcomes['success_rate']:.2%}")
print(f"Target Found Rate: {outcomes['target_found_rate']:.2%}")

# Side pocket analytics
analytics = sar_domain.get_side_pocket_analytics()
print(f"Items for Review: {analytics['summary']['total_items']}")
print(f"Review Efficiency: {analytics['review_efficiency']['efficiency_rate']:.2%}")
```

### Trust Score Evolution
```python
# Track agent performance over time
trust_summary = engine.trust_calculator.get_agent_trust_summary('sar_drone_01')
print(f"Overall Trust: {trust_summary['overall_trust']:.3f}")

# Domain-specific trust
domain_trust = engine.trust_calculator.get_domain_trust_summary('search_rescue')
print(f"Average Domain Trust: {domain_trust['average_trust']:.3f}")
```

## 🧪 Testing

Run the comprehensive test suite:
```bash
# All tests
python -m pytest tests/

# Search and Rescue specific tests
python -m pytest tests/test_search_rescue_domain.py -v

# Integration tests
python -m pytest tests/test_complete_framework.py -v
```

## 🔗 Integration

The framework integrates seamlessly with existing AI orchestration systems:

- **Darwin Gödel Machine**: Self-improving orchestration feedback
- **Thread-Merging Orchestrator**: Multi-platform conversation analysis  
- **EcoStamp**: Environmental impact validation
- **AI Orchestration Hub**: General orchestration system integration

## 📚 Examples

- `examples/complete_example.py` - Full framework demonstration
- `examples/search_rescue_example.py` - Lost child scenario simulation
- `integration_example.py` - Integration with existing systems

## 📄 License

MIT License - see LICENSE file for details.

---

**The Universal Dual-Purpose Feedback Loop Framework: Enabling continuous improvement and silent QA across all your AI domains, from commercial drones to advanced orchestration systems.**

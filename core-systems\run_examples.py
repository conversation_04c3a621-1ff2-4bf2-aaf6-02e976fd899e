#!/usr/bin/env python3
"""
Python Examples Launcher

This script properly sets up the Python path and runs the example scripts
that demonstrate the Universal Dual-Purpose Feedback Loop Framework.
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

def setup_python_path():
    """Add the core-systems directory to Python path."""
    core_systems_dir = Path(__file__).parent
    if str(core_systems_dir) not in sys.path:
        sys.path.insert(0, str(core_systems_dir))
    
    # Also set PYTHONPATH environment variable
    current_pythonpath = os.environ.get('PYTHONPATH', '')
    if str(core_systems_dir) not in current_pythonpath:
        if current_pythonpath:
            os.environ['PYTHONPATH'] = f"{core_systems_dir}{os.pathsep}{current_pythonpath}"
        else:
            os.environ['PYTHONPATH'] = str(core_systems_dir)

def run_example_as_module(example_name):
    """Run an example as a Python module."""
    setup_python_path()
    
    # Map example names to module paths
    examples = {
        'complete': 'examples.complete_example',
        'multi_domain': 'examples.multi_domain_showcase',
        'dynamic_domain': 'examples.dynamic_domain_showcase',
        'search_rescue': 'examples.search_rescue_example'
    }
    
    if example_name not in examples:
        print(f"❌ Unknown example: {example_name}")
        print(f"Available examples: {', '.join(examples.keys())}")
        return False
    
    module_name = examples[example_name]
    
    try:
        print(f"🚀 Running {example_name} example...")
        print(f"📦 Module: {module_name}")
        print("-" * 50)
        
        # Run as module using subprocess to ensure clean environment
        result = subprocess.run([
            sys.executable, '-m', module_name
        ], cwd=Path(__file__).parent, capture_output=False)
        
        if result.returncode == 0:
            print("-" * 50)
            print(f"✅ {example_name} example completed successfully!")
            return True
        else:
            print("-" * 50)
            print(f"❌ {example_name} example failed with return code {result.returncode}")
            return False
            
    except Exception as e:
        print(f"❌ Error running {example_name} example: {e}")
        return False

def list_examples():
    """List all available examples."""
    examples = {
        'complete': 'Complete framework demonstration with both Drone AI and TimeStamp AI',
        'multi_domain': 'Multi-domain showcase with all four specialized domains',
        'dynamic_domain': 'Dynamic domain registration and cross-domain learning',
        'search_rescue': 'Search and rescue scenario with lost child example'
    }
    
    print("📋 Available Python Examples:")
    print("=" * 60)
    for name, description in examples.items():
        print(f"  {name:15} - {description}")
    print("=" * 60)
    print("\nUsage:")
    print(f"  python {Path(__file__).name} <example_name>")
    print(f"  python {Path(__file__).name} --list")
    print(f"  python {Path(__file__).name} --all")

def run_all_examples():
    """Run all examples in sequence."""
    examples = ['complete', 'multi_domain', 'dynamic_domain', 'search_rescue']
    
    print("🚀 Running all Python examples...")
    print("=" * 60)
    
    results = {}
    for example in examples:
        print(f"\n🔄 Starting {example} example...")
        results[example] = run_example_as_module(example)
        print()
    
    print("=" * 60)
    print("📊 Summary:")
    for example, success in results.items():
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"  {example:15} - {status}")
    
    total_passed = sum(results.values())
    total_examples = len(results)
    print(f"\nTotal: {total_passed}/{total_examples} examples passed")
    
    return total_passed == total_examples

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Run Python examples for the Universal Dual-Purpose Feedback Loop Framework"
    )
    parser.add_argument('example', nargs='?', help='Example to run')
    parser.add_argument('--list', action='store_true', help='List available examples')
    parser.add_argument('--all', action='store_true', help='Run all examples')
    
    args = parser.parse_args()
    
    if args.list:
        list_examples()
        return True
    
    if args.all:
        return run_all_examples()
    
    if args.example:
        return run_example_as_module(args.example)
    
    # No arguments provided
    list_examples()
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

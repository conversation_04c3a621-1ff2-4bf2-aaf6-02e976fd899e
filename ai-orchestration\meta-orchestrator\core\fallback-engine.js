/**
 * Fallback Engine
 * 
 * Manages instant fallback logic with multiple assistants per role:
 * - Automatic failover when primary assistant fails
 * - Intelligent fallback chain optimization
 * - Circuit breaker pattern for failed assistants
 * - Performance-based fallback ordering
 * - Real-time availability monitoring
 */

const EventEmitter = require('events');
const chalk = require('chalk');

class FallbackEngine extends EventEmitter {
  constructor(metaOrchestrator) {
    super();
    this.metaOrchestrator = metaOrchestrator;
    
    // Fallback chain configurations
    this.fallbackChains = new Map();
    
    // Circuit breaker states for assistants
    this.circuitBreakers = new Map();
    
    // Fallback performance metrics
    this.fallbackMetrics = {
      totalFallbacks: 0,
      fallbacksByRole: new Map(),
      fallbacksByAssistant: new Map(),
      averageFallbackTime: 0,
      successfulFallbacks: 0
    };
    
    // Configuration
    this.config = {
      // Circuit breaker thresholds
      circuitBreaker: {
        failureThreshold: 5,        // Number of failures before opening circuit
        timeoutThreshold: 30000,    // Timeout threshold in ms
        recoveryTime: 300000,       // Time before attempting to close circuit (5 minutes)
        halfOpenMaxAttempts: 3      // Max attempts in half-open state
      },
      
      // Fallback behavior
      fallback: {
        maxAttempts: 5,             // Maximum fallback attempts per request
        timeoutMultiplier: 1.5,     // Increase timeout for each fallback level
        retryDelay: 1000,           // Delay between fallback attempts
        adaptiveOrdering: true      // Enable performance-based reordering
      },
      
      // Health check configuration
      healthCheck: {
        interval: 60000,            // Health check interval (1 minute)
        timeout: 10000,             // Health check timeout
        enabled: true
      }
    };
  }
  
  async initializeFallbackChains() {
    try {
      console.log(chalk.blue('🔄 Initializing fallback chains...'));
      
      // Initialize fallback chains from role assignments
      await this.buildFallbackChains();
      
      // Initialize circuit breakers
      this.initializeCircuitBreakers();
      
      // Start health monitoring
      if (this.config.healthCheck.enabled) {
        this.startHealthMonitoring();
      }
      
      // Setup adaptive ordering if enabled
      if (this.config.fallback.adaptiveOrdering) {
        this.setupAdaptiveOrdering();
      }
      
      console.log(chalk.green('✅ Fallback chains initialized'));
      
    } catch (error) {
      console.error(chalk.red('❌ Failed to initialize fallback chains:'), error);
      throw error;
    }
  }
  
  async buildFallbackChains() {
    const roleAssignments = this.metaOrchestrator.state.roleAssignments;
    
    for (const [role, assignment] of roleAssignments) {
      const fallbackChain = [assignment.primary, ...assignment.fallbacks];
      this.fallbackChains.set(role, fallbackChain);
      
      console.log(chalk.cyan(`🔗 Fallback chain for ${role}: ${fallbackChain.join(' → ')}`));
    }
    
    // Store in meta-orchestrator state
    this.metaOrchestrator.state.fallbackChains = this.fallbackChains;
  }
  
  initializeCircuitBreakers() {
    // Get all unique assistants from fallback chains
    const allAssistants = new Set();
    for (const chain of this.fallbackChains.values()) {
      chain.forEach(assistant => allAssistants.add(assistant));
    }
    
    // Initialize circuit breaker for each assistant
    for (const assistantId of allAssistants) {
      this.circuitBreakers.set(assistantId, {
        state: 'closed',           // closed, open, half-open
        failureCount: 0,
        lastFailureTime: null,
        lastSuccessTime: Date.now(),
        halfOpenAttempts: 0,
        totalRequests: 0,
        successfulRequests: 0
      });
    }
    
    console.log(chalk.green(`🔌 Initialized ${allAssistants.size} circuit breakers`));
  }
  
  startHealthMonitoring() {
    setInterval(async () => {
      await this.performHealthChecks();
    }, this.config.healthCheck.interval);
    
    console.log(chalk.blue('💓 Health monitoring started'));
  }
  
  async performHealthChecks() {
    const healthResults = new Map();
    
    for (const assistantId of this.circuitBreakers.keys()) {
      try {
        const adapter = this.metaOrchestrator.adapterRegistry.getAdapter(assistantId);
        
        if (adapter) {
          const startTime = Date.now();
          const isHealthy = await Promise.race([
            adapter.healthCheck(),
            new Promise((_, reject) => 
              setTimeout(() => reject(new Error('Health check timeout')), this.config.healthCheck.timeout)
            )
          ]);
          
          const responseTime = Date.now() - startTime;
          
          healthResults.set(assistantId, {
            healthy: isHealthy,
            responseTime,
            timestamp: Date.now()
          });
          
          // Update circuit breaker based on health check
          if (isHealthy) {
            this.recordSuccess(assistantId);
          } else {
            this.recordFailure(assistantId, new Error('Health check failed'));
          }
          
        } else {
          healthResults.set(assistantId, {
            healthy: false,
            error: 'No adapter found',
            timestamp: Date.now()
          });
        }
        
      } catch (error) {
        healthResults.set(assistantId, {
          healthy: false,
          error: error.message,
          timestamp: Date.now()
        });
        
        this.recordFailure(assistantId, error);
      }
    }
    
    this.emit('healthCheckCompleted', healthResults);
  }
  
  setupAdaptiveOrdering() {
    // Reorder fallback chains based on performance every 5 minutes
    setInterval(() => {
      this.optimizeFallbackChains();
    }, 300000);
    
    console.log(chalk.blue('🧠 Adaptive fallback ordering enabled'));
  }
  
  optimizeFallbackChains() {
    for (const [role, chain] of this.fallbackChains) {
      const optimizedChain = this.optimizeChainForRole(role, chain);
      
      if (JSON.stringify(optimizedChain) !== JSON.stringify(chain)) {
        this.fallbackChains.set(role, optimizedChain);
        this.metaOrchestrator.state.fallbackChains.set(role, optimizedChain);
        
        console.log(chalk.blue(`🔄 Optimized fallback chain for ${role}: ${optimizedChain.join(' → ')}`));
        this.emit('chainOptimized', { role, oldChain: chain, newChain: optimizedChain });
      }
    }
  }
  
  optimizeChainForRole(role, currentChain) {
    // Get performance metrics for each assistant in the chain
    const assistantMetrics = currentChain.map(assistantId => {
      const circuitBreaker = this.circuitBreakers.get(assistantId);
      const rolePerformance = this.metaOrchestrator.roleManager.rolePerformance.get(role);
      const assistantRolePerf = rolePerformance?.assistantPerformance.get(assistantId);
      
      return {
        assistantId,
        successRate: circuitBreaker ? circuitBreaker.successfulRequests / Math.max(circuitBreaker.totalRequests, 1) : 0,
        roleSuccessRate: assistantRolePerf?.successRate || 0,
        averageResponseTime: assistantRolePerf?.averageResponseTime || Infinity,
        circuitState: circuitBreaker?.state || 'unknown',
        totalRequests: circuitBreaker?.totalRequests || 0
      };
    });
    
    // Sort by performance (success rate first, then response time)
    assistantMetrics.sort((a, b) => {
      // Prioritize assistants with closed circuits
      if (a.circuitState !== b.circuitState) {
        if (a.circuitState === 'closed') return -1;
        if (b.circuitState === 'closed') return 1;
      }
      
      // Then by success rate (higher is better)
      if (Math.abs(a.roleSuccessRate - b.roleSuccessRate) > 0.1) {
        return b.roleSuccessRate - a.roleSuccessRate;
      }
      
      // Then by response time (lower is better)
      return a.averageResponseTime - b.averageResponseTime;
    });
    
    return assistantMetrics.map(metric => metric.assistantId);
  }
  
  /**
   * Execute a task with fallback logic
   */
  async executeWithFallback(role, task, context) {
    const fallbackChain = this.fallbackChains.get(role);
    
    if (!fallbackChain || fallbackChain.length === 0) {
      throw new Error(`No fallback chain defined for role: ${role}`);
    }
    
    const startTime = Date.now();
    let lastError = null;
    
    for (let i = 0; i < fallbackChain.length && i < this.config.fallback.maxAttempts; i++) {
      const assistantId = fallbackChain[i];
      
      try {
        // Check circuit breaker
        if (!this.canExecute(assistantId)) {
          console.log(chalk.yellow(`⚠️ Circuit breaker open for ${assistantId}, skipping`));
          continue;
        }
        
        console.log(chalk.cyan(`🤖 Attempting ${assistantId} for role ${role} (attempt ${i + 1}/${fallbackChain.length})`));
        
        // Calculate timeout with multiplier for fallback levels
        const timeout = this.calculateTimeout(role, i);
        
        // Get adapter and execute
        const adapter = this.metaOrchestrator.adapterRegistry.getAdapter(assistantId);
        
        if (!adapter) {
          throw new Error(`No adapter found for assistant: ${assistantId}`);
        }
        
        // Execute with timeout
        const result = await Promise.race([
          adapter.execute(task, context),
          new Promise((_, reject) => 
            setTimeout(() => reject(new Error('Execution timeout')), timeout)
          )
        ]);
        
        // Record success
        this.recordSuccess(assistantId);
        
        // Update metrics
        const duration = Date.now() - startTime;
        this.updateFallbackMetrics(role, assistantId, true, duration, i);
        
        console.log(chalk.green(`✅ ${assistantId} successfully completed role ${role} (fallback level ${i})`));
        
        return {
          result,
          assistantId,
          fallbackLevel: i,
          duration,
          success: true
        };
        
      } catch (error) {
        lastError = error;
        
        console.warn(chalk.yellow(`⚠️ ${assistantId} failed for role ${role}: ${error.message}`));
        
        // Record failure
        this.recordFailure(assistantId, error);
        
        // Update metrics
        const duration = Date.now() - startTime;
        this.updateFallbackMetrics(role, assistantId, false, duration, i);
        
        // Add delay before next attempt (except for last attempt)
        if (i < fallbackChain.length - 1) {
          await this.delay(this.config.fallback.retryDelay);
        }
      }
    }
    
    // All fallbacks failed
    const totalDuration = Date.now() - startTime;
    this.fallbackMetrics.totalFallbacks++;
    
    throw new Error(`All fallbacks failed for role ${role}. Last error: ${lastError?.message || 'Unknown error'}`);
  }
  
  canExecute(assistantId) {
    const circuitBreaker = this.circuitBreakers.get(assistantId);
    
    if (!circuitBreaker) {
      return true; // No circuit breaker, allow execution
    }
    
    const now = Date.now();
    
    switch (circuitBreaker.state) {
      case 'closed':
        return true;
        
      case 'open':
        // Check if recovery time has passed
        if (now - circuitBreaker.lastFailureTime >= this.config.circuitBreaker.recoveryTime) {
          circuitBreaker.state = 'half-open';
          circuitBreaker.halfOpenAttempts = 0;
          console.log(chalk.blue(`🔌 Circuit breaker for ${assistantId} moved to half-open`));
          return true;
        }
        return false;
        
      case 'half-open':
        return circuitBreaker.halfOpenAttempts < this.config.circuitBreaker.halfOpenMaxAttempts;
        
      default:
        return true;
    }
  }
  
  recordSuccess(assistantId) {
    const circuitBreaker = this.circuitBreakers.get(assistantId);
    
    if (circuitBreaker) {
      circuitBreaker.totalRequests++;
      circuitBreaker.successfulRequests++;
      circuitBreaker.lastSuccessTime = Date.now();
      
      if (circuitBreaker.state === 'half-open') {
        circuitBreaker.halfOpenAttempts++;
        
        // If enough successful attempts in half-open, close the circuit
        if (circuitBreaker.halfOpenAttempts >= this.config.circuitBreaker.halfOpenMaxAttempts) {
          circuitBreaker.state = 'closed';
          circuitBreaker.failureCount = 0;
          console.log(chalk.green(`🔌 Circuit breaker for ${assistantId} closed`));
        }
      } else if (circuitBreaker.state === 'open') {
        // Reset failure count on success
        circuitBreaker.failureCount = 0;
      }
    }
  }
  
  recordFailure(assistantId, error) {
    const circuitBreaker = this.circuitBreakers.get(assistantId);
    
    if (circuitBreaker) {
      circuitBreaker.totalRequests++;
      circuitBreaker.failureCount++;
      circuitBreaker.lastFailureTime = Date.now();
      
      // Check if we should open the circuit
      if (circuitBreaker.state === 'closed' && 
          circuitBreaker.failureCount >= this.config.circuitBreaker.failureThreshold) {
        
        circuitBreaker.state = 'open';
        console.log(chalk.red(`🔌 Circuit breaker for ${assistantId} opened due to failures`));
        this.emit('circuitBreakerOpened', { assistantId, failureCount: circuitBreaker.failureCount });
        
      } else if (circuitBreaker.state === 'half-open') {
        // Failure in half-open state, go back to open
        circuitBreaker.state = 'open';
        circuitBreaker.halfOpenAttempts = 0;
        console.log(chalk.red(`🔌 Circuit breaker for ${assistantId} reopened`));
      }
    }
  }
  
  calculateTimeout(role, fallbackLevel) {
    const roleDefinition = this.metaOrchestrator.roleManager.getRoleDefinition(role);
    const baseTimeout = roleDefinition?.timeout || 30000;
    
    return Math.floor(baseTimeout * Math.pow(this.config.fallback.timeoutMultiplier, fallbackLevel));
  }
  
  updateFallbackMetrics(role, assistantId, success, duration, fallbackLevel) {
    // Update role-specific metrics
    if (!this.fallbackMetrics.fallbacksByRole.has(role)) {
      this.fallbackMetrics.fallbacksByRole.set(role, {
        total: 0,
        successful: 0,
        averageFallbackLevel: 0
      });
    }
    
    const roleMetrics = this.fallbackMetrics.fallbacksByRole.get(role);
    roleMetrics.total++;
    if (success) roleMetrics.successful++;
    
    // Update average fallback level
    const totalLevel = roleMetrics.averageFallbackLevel * (roleMetrics.total - 1) + fallbackLevel;
    roleMetrics.averageFallbackLevel = totalLevel / roleMetrics.total;
    
    // Update assistant-specific metrics
    if (!this.fallbackMetrics.fallbacksByAssistant.has(assistantId)) {
      this.fallbackMetrics.fallbacksByAssistant.set(assistantId, {
        total: 0,
        successful: 0,
        averageResponseTime: 0
      });
    }
    
    const assistantMetrics = this.fallbackMetrics.fallbacksByAssistant.get(assistantId);
    assistantMetrics.total++;
    if (success) assistantMetrics.successful++;
    
    // Update average response time
    const totalTime = assistantMetrics.averageResponseTime * (assistantMetrics.total - 1) + duration;
    assistantMetrics.averageResponseTime = totalTime / assistantMetrics.total;
    
    // Update global metrics
    if (fallbackLevel > 0) {
      this.fallbackMetrics.totalFallbacks++;
      if (success) this.fallbackMetrics.successfulFallbacks++;
      
      const totalFallbackTime = this.fallbackMetrics.averageFallbackTime * (this.fallbackMetrics.totalFallbacks - 1) + duration;
      this.fallbackMetrics.averageFallbackTime = totalFallbackTime / this.fallbackMetrics.totalFallbacks;
    }
  }
  
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
  
  // Public methods
  
  getFallbackChain(role) {
    return this.fallbackChains.get(role);
  }
  
  getCircuitBreakerState(assistantId) {
    return this.circuitBreakers.get(assistantId);
  }
  
  getAllCircuitBreakerStates() {
    return Object.fromEntries(this.circuitBreakers);
  }
  
  getFallbackMetrics() {
    return {
      ...this.fallbackMetrics,
      fallbacksByRole: Object.fromEntries(this.fallbackMetrics.fallbacksByRole),
      fallbacksByAssistant: Object.fromEntries(this.fallbackMetrics.fallbacksByAssistant)
    };
  }
  
  async updateFallbackChain(role, newChain) {
    this.fallbackChains.set(role, newChain);
    this.metaOrchestrator.state.fallbackChains.set(role, newChain);
    
    // Initialize circuit breakers for new assistants
    for (const assistantId of newChain) {
      if (!this.circuitBreakers.has(assistantId)) {
        this.circuitBreakers.set(assistantId, {
          state: 'closed',
          failureCount: 0,
          lastFailureTime: null,
          lastSuccessTime: Date.now(),
          halfOpenAttempts: 0,
          totalRequests: 0,
          successfulRequests: 0
        });
      }
    }
    
    this.emit('fallbackChainUpdated', { role, newChain });
    console.log(chalk.green(`✅ Updated fallback chain for ${role}: ${newChain.join(' → ')}`));
  }
  
  resetCircuitBreaker(assistantId) {
    const circuitBreaker = this.circuitBreakers.get(assistantId);
    
    if (circuitBreaker) {
      circuitBreaker.state = 'closed';
      circuitBreaker.failureCount = 0;
      circuitBreaker.halfOpenAttempts = 0;
      circuitBreaker.lastFailureTime = null;
      
      console.log(chalk.green(`🔌 Circuit breaker for ${assistantId} manually reset`));
      this.emit('circuitBreakerReset', { assistantId });
    }
  }
}

module.exports = FallbackEngine;

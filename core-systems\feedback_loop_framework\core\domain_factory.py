"""
Universal Domain Factory for Dynamic Domain Registration

Automatically detects data types and creates appropriate domain instances
for new industry applications. Supports unlimited domain expansion with
intelligent data type recognition and domain instantiation.
"""

import logging
import importlib
import inspect
from typing import Dict, Any, List, Optional, Type, Callable
from pathlib import Path
from datetime import datetime

from .interfaces import BaseDomain


class DomainFactory:
    """
    Factory for automatically detecting and creating domain instances based on input data.
    
    Supports dynamic domain registration, intelligent data type detection,
    and automatic domain instantiation for new industry applications.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.registered_domains = {}
        self.data_type_patterns = {}
        self.industry_templates = {}
        self.auto_detection_rules = {}
        
        # Initialize built-in domain patterns
        self._initialize_builtin_patterns()
        
        # Initialize industry templates for common domains
        self._initialize_industry_templates()
    
    def _initialize_builtin_patterns(self) -> None:
        """Initialize patterns for built-in domains."""
        self.data_type_patterns = {
            # Drone AI Patterns
            'drone_sensor_data': {
                'indicators': ['sensor_readings', 'gps_coordinates', 'altitude', 'drone_id'],
                'domain_type': 'drone_ai',
                'scenario_type': 'general_drone_operations'
            },
            'search_rescue_data': {
                'indicators': ['target_person', 'clothing', 'personal_items', 'mission_id', 'search_area'],
                'domain_type': 'drone_ai',
                'scenario_type': 'search_rescue'
            },
            'species_tracking_data': {
                'indicators': ['bird', 'species', 'habitat', 'survey_id', 'biodiversity'],
                'domain_type': 'drone_ai',
                'scenario_type': 'species_tracking'
            },
            'mining_ore_data': {
                'indicators': ['mineral', 'ore', 'lidar', 'geological', 'density_g_cm3'],
                'domain_type': 'drone_ai',
                'scenario_type': 'mining_ore'
            },
            'construction_data': {
                'indicators': ['building', 'foundation', 'structural', 'construction_phase', 'material'],
                'domain_type': 'drone_ai',
                'scenario_type': 'real_estate_construction'
            },
            
            # TimeStamp AI Patterns
            'llm_output_data': {
                'indicators': ['llm_response', 'generated_text', 'model_output', 'prompt'],
                'domain_type': 'timestamp_ai',
                'scenario_type': 'llm_output_validation'
            },
            'environmental_impact_data': {
                'indicators': ['carbon_footprint', 'energy_consumption', 'environmental_impact', 'sustainability'],
                'domain_type': 'timestamp_ai',
                'scenario_type': 'environmental_impact'
            },
            
            # New Industry Patterns (Auto-detectable)
            'healthcare_data': {
                'indicators': ['patient', 'medical', 'diagnosis', 'treatment', 'hospital'],
                'domain_type': 'healthcare_ai',
                'scenario_type': 'medical_analysis'
            },
            'agriculture_data': {
                'indicators': ['crop', 'soil', 'irrigation', 'harvest', 'farm', 'yield'],
                'domain_type': 'agriculture_ai',
                'scenario_type': 'precision_farming'
            },
            'insurance_data': {
                'indicators': ['claim', 'damage', 'assessment', 'policy', 'risk'],
                'domain_type': 'insurance_ai',
                'scenario_type': 'damage_assessment'
            },
            'disaster_response_data': {
                'indicators': ['disaster', 'emergency', 'evacuation', 'damage_assessment', 'relief'],
                'domain_type': 'disaster_ai',
                'scenario_type': 'disaster_response'
            },
            'security_data': {
                'indicators': ['surveillance', 'threat', 'perimeter', 'intrusion', 'security'],
                'domain_type': 'security_ai',
                'scenario_type': 'security_monitoring'
            },
            'environmental_monitoring_data': {
                'indicators': ['pollution', 'air_quality', 'water_quality', 'emissions', 'monitoring'],
                'domain_type': 'environmental_ai',
                'scenario_type': 'environmental_monitoring'
            }
        }
    
    def _initialize_industry_templates(self) -> None:
        """Initialize templates for creating new industry domains."""
        self.industry_templates = {
            'healthcare_ai': {
                'domain_name': 'Healthcare AI',
                'description': 'Medical imaging analysis, patient monitoring, diagnostic assistance',
                'feedback_categories': ['diagnosis_correct', 'diagnosis_partial', 'diagnosis_incorrect'],
                'confidence_thresholds': {'high': 0.90, 'medium': 0.75, 'low': 0.60},
                'safety_critical': True,
                'regulatory_compliance': ['HIPAA', 'FDA', 'medical_standards']
            },
            'agriculture_ai': {
                'domain_name': 'Agriculture AI',
                'description': 'Crop health assessment, precision farming, yield optimization',
                'feedback_categories': ['crop_healthy', 'crop_stressed', 'crop_diseased'],
                'confidence_thresholds': {'high': 0.85, 'medium': 0.70, 'low': 0.55},
                'safety_critical': False,
                'seasonal_factors': True
            },
            'insurance_ai': {
                'domain_name': 'Insurance AI',
                'description': 'Damage assessment, risk evaluation, claim processing',
                'feedback_categories': ['damage_confirmed', 'damage_partial', 'no_damage'],
                'confidence_thresholds': {'high': 0.88, 'medium': 0.73, 'low': 0.58},
                'safety_critical': False,
                'financial_impact': True
            },
            'disaster_ai': {
                'domain_name': 'Disaster Response AI',
                'description': 'Disaster assessment, emergency response, evacuation planning',
                'feedback_categories': ['critical_damage', 'moderate_damage', 'minimal_damage'],
                'confidence_thresholds': {'high': 0.92, 'medium': 0.78, 'low': 0.65},
                'safety_critical': True,
                'time_sensitive': True
            },
            'security_ai': {
                'domain_name': 'Security AI',
                'description': 'Perimeter monitoring, threat detection, surveillance analysis',
                'feedback_categories': ['threat_detected', 'suspicious_activity', 'all_clear'],
                'confidence_thresholds': {'high': 0.95, 'medium': 0.80, 'low': 0.65},
                'safety_critical': True,
                'real_time_required': True
            },
            'environmental_ai': {
                'domain_name': 'Environmental Monitoring AI',
                'description': 'Pollution detection, air/water quality, emissions monitoring',
                'feedback_categories': ['pollution_detected', 'quality_degraded', 'within_limits'],
                'confidence_thresholds': {'high': 0.87, 'medium': 0.72, 'low': 0.57},
                'safety_critical': True,
                'regulatory_compliance': ['EPA', 'environmental_standards']
            }
        }
    
    def detect_domain_type(self, raw_output: Any, context: Dict[str, Any]) -> Optional[Dict[str, str]]:
        """
        Automatically detect the appropriate domain type based on input data.
        
        Args:
            raw_output: Raw input data to analyze
            context: Context information
            
        Returns:
            Dictionary with domain_type and scenario_type, or None if not detected
        """
        try:
            # Convert input to searchable text
            search_text = self._extract_searchable_text(raw_output, context)
            
            # Score each pattern
            pattern_scores = {}
            for pattern_name, pattern_info in self.data_type_patterns.items():
                score = self._calculate_pattern_score(search_text, pattern_info['indicators'])
                if score > 0:
                    pattern_scores[pattern_name] = {
                        'score': score,
                        'domain_type': pattern_info['domain_type'],
                        'scenario_type': pattern_info['scenario_type']
                    }
            
            # Return best match if above threshold
            if pattern_scores:
                best_pattern = max(pattern_scores.items(), key=lambda x: x[1]['score'])
                if best_pattern[1]['score'] >= 0.3:  # Minimum confidence threshold
                    return {
                        'domain_type': best_pattern[1]['domain_type'],
                        'scenario_type': best_pattern[1]['scenario_type'],
                        'confidence': best_pattern[1]['score'],
                        'pattern_name': best_pattern[0]
                    }
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error detecting domain type: {str(e)}")
            return None
    
    def _extract_searchable_text(self, raw_output: Any, context: Dict[str, Any]) -> str:
        """Extract searchable text from input data."""
        text_parts = []
        
        # Add context keys and values
        if isinstance(context, dict):
            text_parts.extend(context.keys())
            text_parts.extend(str(v).lower() for v in context.values())
        
        # Add raw output content
        if isinstance(raw_output, dict):
            text_parts.extend(raw_output.keys())
            text_parts.extend(str(v).lower() for v in raw_output.values())
        elif isinstance(raw_output, str):
            text_parts.append(raw_output.lower())
        else:
            text_parts.append(str(raw_output).lower())
        
        return ' '.join(text_parts).lower()
    
    def _calculate_pattern_score(self, search_text: str, indicators: List[str]) -> float:
        """Calculate how well the input matches a pattern."""
        matches = 0
        for indicator in indicators:
            if indicator.lower() in search_text:
                matches += 1
        
        return matches / len(indicators) if indicators else 0
    
    def create_domain_instance(self, domain_type: str, scenario_type: str, config: Dict[str, Any] = None) -> Optional[BaseDomain]:
        """
        Create a domain instance for the detected type.
        
        Args:
            domain_type: Type of domain (e.g., 'drone_ai', 'timestamp_ai', 'healthcare_ai')
            scenario_type: Specific scenario (e.g., 'search_rescue', 'species_tracking')
            config: Optional configuration for the domain
            
        Returns:
            Domain instance or None if creation failed
        """
        try:
            # Check if we have a registered domain class
            if domain_type in self.registered_domains:
                domain_class = self.registered_domains[domain_type]
                return domain_class(config or {})
            
            # Try to import and create built-in domain
            domain_instance = self._create_builtin_domain(domain_type, scenario_type, config)
            if domain_instance:
                return domain_instance
            
            # Create generic domain from template
            return self._create_generic_domain(domain_type, scenario_type, config)
            
        except Exception as e:
            self.logger.error(f"Error creating domain instance for {domain_type}: {str(e)}")
            return None
    
    def _create_builtin_domain(self, domain_type: str, scenario_type: str, config: Dict[str, Any]) -> Optional[BaseDomain]:
        """Create built-in domain instances."""
        try:
            if domain_type == 'drone_ai':
                if scenario_type == 'search_rescue':
                    from ..domains.search_rescue import SearchRescueDomain
                    return SearchRescueDomain(config)
                elif scenario_type == 'species_tracking':
                    from ..domains.species_tracking import SpeciesTrackingDomain
                    return SpeciesTrackingDomain(config)
                elif scenario_type == 'mining_ore':
                    from ..domains.mining_ore import MiningOreDomain
                    return MiningOreDomain(config)
                elif scenario_type == 'real_estate_construction':
                    from ..domains.real_estate_construction import RealEstateConstructionDomain
                    return RealEstateConstructionDomain(config)
                else:
                    from ..domains.drone_ai import DroneAIDomain
                    return DroneAIDomain(config)
            
            elif domain_type == 'timestamp_ai':
                from ..domains.timestamp_ai import TimeStampAIDomain
                return TimeStampAIDomain(config)
            
            return None
            
        except ImportError as e:
            self.logger.warning(f"Could not import built-in domain {domain_type}: {str(e)}")
            return None
    
    def _create_generic_domain(self, domain_type: str, scenario_type: str, config: Dict[str, Any]) -> Optional[BaseDomain]:
        """Create a generic domain instance from template."""
        try:
            template = self.industry_templates.get(domain_type)
            if not template:
                self.logger.warning(f"No template found for domain type: {domain_type}")
                return None
            
            # Create generic domain class dynamically
            from .generic_domain import GenericDomain
            
            # Merge template with config
            domain_config = {**template, **(config or {})}
            domain_config['domain_type'] = domain_type
            domain_config['scenario_type'] = scenario_type
            
            return GenericDomain(domain_config)
            
        except Exception as e:
            self.logger.error(f"Error creating generic domain for {domain_type}: {str(e)}")
            return None
    
    def register_domain_class(self, domain_type: str, domain_class: Type[BaseDomain]) -> None:
        """Register a custom domain class."""
        self.registered_domains[domain_type] = domain_class
        self.logger.info(f"Registered custom domain class for {domain_type}")
    
    def add_data_pattern(self, pattern_name: str, indicators: List[str], 
                        domain_type: str, scenario_type: str) -> None:
        """Add a new data pattern for domain detection."""
        self.data_type_patterns[pattern_name] = {
            'indicators': indicators,
            'domain_type': domain_type,
            'scenario_type': scenario_type
        }
        self.logger.info(f"Added data pattern: {pattern_name}")
    
    def add_industry_template(self, domain_type: str, template: Dict[str, Any]) -> None:
        """Add a new industry template."""
        self.industry_templates[domain_type] = template
        self.logger.info(f"Added industry template: {domain_type}")
    
    def get_supported_domains(self) -> Dict[str, List[str]]:
        """Get list of supported domains and their scenarios."""
        domains = {}
        for pattern_name, pattern_info in self.data_type_patterns.items():
            domain_type = pattern_info['domain_type']
            scenario_type = pattern_info['scenario_type']
            
            if domain_type not in domains:
                domains[domain_type] = []
            
            if scenario_type not in domains[domain_type]:
                domains[domain_type].append(scenario_type)
        
        return domains
    
    def get_domain_info(self, domain_type: str) -> Optional[Dict[str, Any]]:
        """Get information about a specific domain type."""
        template = self.industry_templates.get(domain_type)
        if template:
            return template.copy()
        
        # Check registered domains
        if domain_type in self.registered_domains:
            domain_class = self.registered_domains[domain_type]
            return {
                'domain_name': domain_class.__name__,
                'description': domain_class.__doc__ or 'Custom domain class',
                'class_name': domain_class.__name__
            }
        
        return None
    
    def auto_detect_and_create(self, raw_output: Any, context: Dict[str, Any], 
                              config: Dict[str, Any] = None) -> Optional[BaseDomain]:
        """
        Automatically detect domain type and create appropriate domain instance.
        
        Args:
            raw_output: Raw input data
            context: Context information
            config: Optional domain configuration
            
        Returns:
            Domain instance or None if detection/creation failed
        """
        # Detect domain type
        detection_result = self.detect_domain_type(raw_output, context)
        if not detection_result:
            self.logger.warning("Could not detect appropriate domain type for input data")
            return None
        
        domain_type = detection_result['domain_type']
        scenario_type = detection_result['scenario_type']
        
        self.logger.info(f"Auto-detected domain: {domain_type}, scenario: {scenario_type}")
        
        # Create domain instance
        domain_instance = self.create_domain_instance(domain_type, scenario_type, config)
        if domain_instance:
            self.logger.info(f"Successfully created domain instance: {domain_type}")
        else:
            self.logger.error(f"Failed to create domain instance: {domain_type}")
        
        return domain_instance
    
    def scan_for_new_domains(self, data_samples: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Scan data samples to identify potential new domain types.
        
        Args:
            data_samples: List of data samples to analyze
            
        Returns:
            List of potential new domain suggestions
        """
        suggestions = []
        
        for sample in data_samples:
            raw_output = sample.get('raw_output')
            context = sample.get('context', {})
            
            # Check if current patterns detect this
            detection = self.detect_domain_type(raw_output, context)
            if not detection:
                # Analyze for new patterns
                suggestion = self._analyze_for_new_domain(raw_output, context)
                if suggestion:
                    suggestions.append(suggestion)
        
        return suggestions
    
    def _analyze_for_new_domain(self, raw_output: Any, context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Analyze data for potential new domain patterns."""
        search_text = self._extract_searchable_text(raw_output, context)
        
        # Extract potential keywords
        words = search_text.split()
        word_freq = {}
        for word in words:
            if len(word) > 3:  # Skip short words
                word_freq[word] = word_freq.get(word, 0) + 1
        
        # Find most frequent meaningful words
        top_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)[:10]
        
        if top_words:
            return {
                'suggested_domain_type': f"{top_words[0][0]}_ai",
                'suggested_indicators': [word for word, freq in top_words[:5]],
                'confidence': min(top_words[0][1] / len(words), 1.0),
                'sample_data': search_text[:200]
            }
        
        return None


# Global domain factory instance
domain_factory = DomainFactory()

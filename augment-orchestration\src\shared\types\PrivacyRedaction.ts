/**
 * Privacy/Redaction Layer Types and Interfaces
 * 
 * Core Gap 3: Privacy/Redaction Layer for intelligent removal of sensitive data
 * from logs before external sharing or cross-agent analysis while maintaining audit integrity.
 */

export enum RedactionLevel {
  NONE = 'NONE',
  BASIC = 'BASIC',
  MODERATE = 'MODERATE',
  STRICT = 'STRICT',
  MAXIMUM = 'MAXIMUM'
}

export enum SensitiveDataType {
  API_KEY = 'API_KEY',
  PASSWORD = 'PASSWORD',
  TOKEN = 'TOKEN',
  EMAIL = 'EMAIL',
  PHONE = 'PHONE',
  SSN = 'SSN',
  CREDIT_CARD = 'CREDIT_CARD',
  IP_ADDRESS = 'IP_ADDRESS',
  DATABASE_CONNECTION = 'DATABASE_CONNECTION',
  PRIVATE_KEY = 'PRIVATE_KEY',
  PERSONAL_NAME = 'PERSONAL_NAME',
  ADDRESS = 'ADDRESS',
  CUSTOM = 'CUSTOM'
}

export enum EntityContext {
  CODE = 'CODE',
  LOG = 'LOG',
  CONFIGURATION = 'CONFIGURATION',
  DATABASE = 'DATABASE',
  API_RESPONSE = 'API_RESPONSE',
  USER_INPUT = 'USER_INPUT',
  SYSTEM_OUTPUT = 'SYSTEM_OUTPUT',
  AUDIT_TRAIL = 'AUDIT_TRAIL'
}

export interface RedactionRule {
  id: string;
  name: string;
  description: string;
  pattern: string; // Regex pattern
  replacement: string; // Replacement text
  dataType: SensitiveDataType;
  entityTypes: EntityContext[];
  isActive: boolean;
  priority: number; // Higher priority rules applied first
  preserveFormat: boolean; // Maintain original format (e.g., keep email structure)
  contextAware: boolean; // Consider surrounding context
  reversible: boolean; // Can be un-redacted with proper authorization
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
}

export interface RedactionResult {
  originalText: string;
  redactedText: string;
  redactionMap: RedactionMatch[];
  redactionLevel: RedactionLevel;
  integrityHash: string; // Hash of original for verification
  reversibilityKey?: string; // Key for reversible redaction
  metadata: {
    rulesApplied: string[];
    sensitiveDataFound: SensitiveDataType[];
    redactionCount: number;
    processingTime: number;
    contextAnalysis?: ContextAnalysis;
  };
}

export interface RedactionMatch {
  startIndex: number;
  endIndex: number;
  originalValue: string;
  redactedValue: string;
  dataType: SensitiveDataType;
  ruleId: string;
  confidence: number; // 0-1 confidence in match
  context: string; // Surrounding text context
  reversible: boolean;
}

export interface ContextAnalysis {
  entityType: EntityContext;
  surroundingKeywords: string[];
  structuralContext: string; // JSON path, code block type, etc.
  semanticContext: string; // Function name, variable context, etc.
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
}

export interface RedactionRequest {
  text: string;
  entityType: EntityContext;
  redactionLevel: RedactionLevel;
  customRules?: string[]; // Specific rule IDs to apply
  preserveStructure?: boolean;
  allowReversible?: boolean;
  contextHints?: Record<string, any>;
}

export interface UnredactionRequest {
  redactedText: string;
  reversibilityKey: string;
  integrityHash: string;
  authorizedBy: string;
  reason: string;
}

export interface RedactionStats {
  totalRedactions: number;
  redactionsByType: Record<SensitiveDataType, number>;
  redactionsByEntity: Record<EntityContext, number>;
  redactionsByLevel: Record<RedactionLevel, number>;
  averageProcessingTime: number;
  falsePositiveRate: number;
  reversibleRedactions: number;
  recentActivity: {
    last24Hours: number;
    lastWeek: number;
    lastMonth: number;
  };
}

export interface RedactionPolicy {
  id: string;
  name: string;
  description: string;
  entityTypes: EntityContext[];
  defaultLevel: RedactionLevel;
  rules: string[]; // Rule IDs
  exceptions: RedactionException[];
  isActive: boolean;
  enforceStrict: boolean;
  allowOverrides: boolean;
  auditRequired: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface RedactionException {
  pattern: string;
  reason: string;
  authorizedBy: string;
  expiresAt?: Date;
  conditions: Record<string, any>;
}

export interface SmartRedactionConfig {
  enableContextAnalysis: boolean;
  enableSemanticAnalysis: boolean;
  enableMLDetection: boolean;
  confidenceThreshold: number;
  preserveCodeStructure: boolean;
  preserveLogFormat: boolean;
  enableFalsePositiveReduction: boolean;
  customPatterns: CustomPattern[];
}

export interface CustomPattern {
  id: string;
  name: string;
  pattern: string;
  dataType: SensitiveDataType;
  description: string;
  testCases: string[];
  isActive: boolean;
}

// Advanced redaction features
export interface DifferentialPrivacy {
  epsilon: number; // Privacy budget
  delta: number; // Failure probability
  mechanism: 'LAPLACE' | 'GAUSSIAN' | 'EXPONENTIAL';
  sensitivity: number;
}

export interface RedactionAudit {
  id: string;
  redactionId: string;
  action: 'REDACT' | 'UNREDACT' | 'VIEW_REDACTED';
  performedBy: string;
  timestamp: Date;
  reason: string;
  originalHash: string;
  redactedHash: string;
  rulesApplied: string[];
  metadata: Record<string, any>;
}

export interface RedactionCompliance {
  regulation: 'GDPR' | 'HIPAA' | 'PCI_DSS' | 'SOX' | 'CUSTOM';
  requirements: string[];
  complianceLevel: 'COMPLIANT' | 'PARTIAL' | 'NON_COMPLIANT';
  lastAssessment: Date;
  findings: ComplianceFinding[];
}

export interface ComplianceFinding {
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  description: string;
  recommendation: string;
  affectedData: string[];
  remediation: string;
}

// Batch processing
export interface BatchRedactionRequest {
  items: RedactionRequest[];
  batchId: string;
  policy?: string; // Policy ID
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  notifyOnComplete: boolean;
}

export interface BatchRedactionResponse {
  batchId: string;
  totalItems: number;
  successfulItems: number;
  failedItems: number;
  results: RedactionResult[];
  processingTime: number;
  errors: BatchRedactionError[];
}

export interface BatchRedactionError {
  itemIndex: number;
  error: string;
  originalText: string;
}

// Real-time monitoring
export interface RedactionMonitor {
  subscribe(callback: (event: RedactionEvent) => void): void;
  unsubscribe(): void;
  filterByType(dataType: SensitiveDataType): void;
  filterByEntity(entityType: EntityContext): void;
  alertOnHighRisk(enabled: boolean): void;
}

export interface RedactionEvent {
  type: 'REDACTION_APPLIED' | 'SENSITIVE_DATA_DETECTED' | 'POLICY_VIOLATION' | 'UNREDACTION_REQUESTED';
  timestamp: Date;
  entityType: EntityContext;
  dataType: SensitiveDataType;
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  metadata: Record<string, any>;
}

// Error types
export class RedactionError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: any
  ) {
    super(message);
    this.name = 'RedactionError';
  }
}

export class PatternMatchError extends RedactionError {
  constructor(message: string, details?: any) {
    super(message, 'PATTERN_MATCH_ERROR', details);
  }
}

export class UnredactionError extends RedactionError {
  constructor(message: string, details?: any) {
    super(message, 'UNREDACTION_ERROR', details);
  }
}

export class PolicyViolationError extends RedactionError {
  constructor(message: string, details?: any) {
    super(message, 'POLICY_VIOLATION', details);
  }
}

// Constants
export const REDACTION_CONSTANTS = {
  MAX_TEXT_SIZE: 100 * 1024 * 1024, // 100MB
  MAX_BATCH_SIZE: 1000,
  DEFAULT_REPLACEMENT: '[REDACTED]',
  REVERSIBILITY_KEY_LENGTH: 32,
  INTEGRITY_HASH_ALGORITHM: 'SHA256' as const,
  CONTEXT_WINDOW_SIZE: 50, // Characters before/after match
  MIN_CONFIDENCE_THRESHOLD: 0.7,
  MAX_PROCESSING_TIME: 30000, // 30 seconds
  AUDIT_RETENTION_DAYS: 2555, // 7 years
  PATTERN_CACHE_SIZE: 1000
};

// Built-in patterns
export const BUILT_IN_PATTERNS = {
  [SensitiveDataType.API_KEY]: [
    /\b[A-Za-z0-9]{32,}\b/g,
    /sk-[A-Za-z0-9]{48}/g,
    /AIza[A-Za-z0-9_-]{35}/g
  ],
  [SensitiveDataType.PASSWORD]: [
    /password\s*[:=]\s*["']?([^"'\s]+)["']?/gi,
    /pwd\s*[:=]\s*["']?([^"'\s]+)["']?/gi
  ],
  [SensitiveDataType.EMAIL]: [
    /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g
  ],
  [SensitiveDataType.IP_ADDRESS]: [
    /\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b/g,
    /\b(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}\b/g
  ],
  [SensitiveDataType.CREDIT_CARD]: [
    /\b(?:4[0-9]{12}(?:[0-9]{3})?|5[1-5][0-9]{14}|3[47][0-9]{13}|3[0-9]{13}|6(?:011|5[0-9]{2})[0-9]{12})\b/g
  ]
};

// Utility functions
export const RedactionUtils = {
  generateReversibilityKey: (): string => {
    return Array.from(crypto.getRandomValues(new Uint8Array(32)))
      .map(b => b.toString(16).padStart(2, '0'))
      .join('');
  },

  calculateIntegrityHash: (text: string): string => {
    // This would use a proper crypto library in implementation
    return btoa(text).slice(0, 32);
  },

  preserveFormat: (original: string, replacement: string): string => {
    // Preserve character types (letter -> X, digit -> 0, special -> *)
    return original.replace(/[a-zA-Z]/g, 'X')
                  .replace(/[0-9]/g, '0')
                  .replace(/[^a-zA-Z0-9\s]/g, '*');
  },

  validatePattern: (pattern: string): boolean => {
    try {
      new RegExp(pattern);
      return true;
    } catch {
      return false;
    }
  },

  estimateRiskLevel: (dataType: SensitiveDataType, context: EntityContext): 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' => {
    const highRiskTypes = [SensitiveDataType.PASSWORD, SensitiveDataType.PRIVATE_KEY, SensitiveDataType.API_KEY];
    const highRiskContexts = [EntityContext.DATABASE, EntityContext.CONFIGURATION];

    if (highRiskTypes.includes(dataType) || highRiskContexts.includes(context)) {
      return 'CRITICAL';
    }

    if (dataType === SensitiveDataType.TOKEN || context === EntityContext.API_RESPONSE) {
      return 'HIGH';
    }

    if (dataType === SensitiveDataType.EMAIL || dataType === SensitiveDataType.IP_ADDRESS) {
      return 'MEDIUM';
    }

    return 'LOW';
  }
};

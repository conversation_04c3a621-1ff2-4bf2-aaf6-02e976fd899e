"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.auditRoutes = void 0;
const express_1 = require("express");
const client_1 = require("@prisma/client");
const errorHandler_1 = require("../middleware/errorHandler");
const router = (0, express_1.Router)();
exports.auditRoutes = router;
const prisma = new client_1.PrismaClient();
// Get audit logs
router.get('/', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50;
    const entityType = req.query.entityType;
    const action = req.query.action;
    const skip = (page - 1) * limit;
    const where = {};
    if (entityType) {
        where.entityType = entityType;
    }
    if (action) {
        where.action = { contains: action, mode: 'insensitive' };
    }
    const [logs, total] = await Promise.all([
        prisma.auditLog.findMany({
            where,
            skip,
            take: limit,
            include: {
                user: {
                    select: {
                        id: true,
                        username: true,
                        email: true,
                    },
                },
            },
            orderBy: {
                timestamp: 'desc',
            },
        }),
        prisma.auditLog.count({ where }),
    ]);
    res.json({
        success: true,
        data: logs,
        pagination: {
            page,
            limit,
            total,
            totalPages: Math.ceil(total / limit),
        },
    });
}));

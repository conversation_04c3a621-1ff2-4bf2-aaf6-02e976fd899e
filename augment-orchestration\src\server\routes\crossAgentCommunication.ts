/**
 * Cross-Agent Communication API Routes
 * 
 * RESTful API endpoints for cross-agent communication protocol
 * Core Gap 4: Cross-Agent Communication Protocol implementation
 */

import { Router } from 'express';
import { body, query, param, validationResult } from 'express-validator';
import { PrismaClient } from '@prisma/client';
import { CrossAgentCommunicationService } from '../services/CrossAgentCommunicationService';
import { asyncHandler } from '../utils/asyncHandler';
import { authenticate, authorize } from '../middleware/auth';
import { logger } from '../utils/logger';
import {
  Message,
  MessageType,
  AgentIdentity,
  AgentRole,
  Priority,
  TaskRequest,
  ResourceRequest,
  ConflictReport,
  ConflictType,
  KnowledgeShare,
  CapabilityAnnouncement,
  CommunicationUtils
} from '../../shared/types/CrossAgentCommunication';

const router = Router();
const prisma = new PrismaClient();

// Mock local agent identity - in real implementation this would be configured
const localAgent: AgentIdentity = {
  id: 'orchestrator_main',
  name: 'Main Orchestrator',
  role: AgentRole.ORCHESTRATOR,
  version: '1.0.0',
  capabilities: ['task_coordination', 'resource_management', 'conflict_resolution'],
  publicKey: 'mock_public_key',
  endpoint: 'ws://localhost:8080/agent',
  metadata: {}
};

const communicationService = new CrossAgentCommunicationService(prisma, localAgent);

// Validation middleware
const validateMessage = [
  body('type').isIn(Object.values(MessageType)).withMessage('Invalid message type'),
  body('receiverId').notEmpty().withMessage('Receiver ID is required'),
  body('priority').isIn(Object.values(Priority)).withMessage('Invalid priority'),
  body('payload').notEmpty().withMessage('Payload is required')
];

const validateTaskRequest = [
  body('taskId').notEmpty().withMessage('Task ID is required'),
  body('taskType').notEmpty().withMessage('Task type is required'),
  body('description').notEmpty().withMessage('Description is required'),
  body('parameters').isObject().withMessage('Parameters must be an object'),
  body('requiredCapabilities').isArray().withMessage('Required capabilities must be an array'),
  body('priority').isIn(Object.values(Priority)).withMessage('Invalid priority')
];

const validateResourceRequest = [
  body('resourceId').notEmpty().withMessage('Resource ID is required'),
  body('resourceType').notEmpty().withMessage('Resource type is required'),
  body('accessType').isIn(['READ', 'WRITE', 'EXCLUSIVE']).withMessage('Invalid access type'),
  body('priority').isIn(Object.values(Priority)).withMessage('Invalid priority'),
  body('justification').notEmpty().withMessage('Justification is required')
];

const validateConflictReport = [
  body('conflictId').notEmpty().withMessage('Conflict ID is required'),
  body('type').isIn(Object.values(ConflictType)).withMessage('Invalid conflict type'),
  body('involvedAgents').isArray({ min: 2 }).withMessage('At least 2 involved agents required'),
  body('description').notEmpty().withMessage('Description is required'),
  body('severity').isIn(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']).withMessage('Invalid severity')
];

const validateKnowledgeShare = [
  body('knowledgeId').notEmpty().withMessage('Knowledge ID is required'),
  body('category').notEmpty().withMessage('Category is required'),
  body('title').notEmpty().withMessage('Title is required'),
  body('content').notEmpty().withMessage('Content is required'),
  body('confidenceLevel').isFloat({ min: 0, max: 1 }).withMessage('Confidence level must be 0-1'),
  body('source').notEmpty().withMessage('Source is required'),
  body('tags').isArray().withMessage('Tags must be an array')
];

/**
 * POST /api/cross-agent/connect
 * Connect to another agent
 */
router.post('/connect',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR']),
  body('endpoint').isURL().withMessage('Valid endpoint URL is required'),
  body('agentId').optional().notEmpty(),
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { endpoint, agentId } = req.body;

    const connected = await communicationService.connectToAgent(endpoint, agentId);

    if (connected) {
      logger.info('Agent connection initiated', {
        endpoint,
        agentId,
        userId: req.user?.id
      });

      res.json({
        success: true,
        message: 'Connection initiated successfully',
        endpoint,
        agentId
      });
    } else {
      res.status(500).json({
        success: false,
        error: 'Failed to connect to agent'
      });
    }
  })
);

/**
 * POST /api/cross-agent/send-message
 * Send a message to another agent
 */
router.post('/send-message',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR', 'AGENT']),
  validateMessage,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const message: Message = {
      id: CommunicationUtils.generateMessageId(),
      type: req.body.type,
      senderId: localAgent.id,
      receiverId: req.body.receiverId,
      timestamp: new Date(),
      priority: req.body.priority,
      payload: req.body.payload,
      signature: '',
      correlationId: req.body.correlationId || CommunicationUtils.generateCorrelationId(),
      ttl: req.body.ttl || 30000,
      metadata: {
        encrypted: req.body.encrypted || false,
        compressed: req.body.compressed || false,
        contentType: 'application/json',
        encoding: 'utf-8',
        checksum: CommunicationUtils.calculateChecksum(req.body.payload)
      }
    };

    const sent = await communicationService.sendMessage(message);

    if (sent) {
      logger.info('Message sent via API', {
        messageId: message.id,
        type: message.type,
        receiverId: message.receiverId,
        userId: req.user?.id
      });

      res.json({
        success: true,
        messageId: message.id,
        correlationId: message.correlationId,
        timestamp: message.timestamp
      });
    } else {
      res.status(500).json({
        success: false,
        error: 'Failed to send message'
      });
    }
  })
);

/**
 * POST /api/cross-agent/task-request
 * Send a task request to another agent
 */
router.post('/task-request',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR']),
  body('receiverId').notEmpty().withMessage('Receiver ID is required'),
  validateTaskRequest,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const taskRequest: TaskRequest = {
      taskId: req.body.taskId,
      taskType: req.body.taskType,
      description: req.body.description,
      parameters: req.body.parameters,
      requiredCapabilities: req.body.requiredCapabilities,
      priority: req.body.priority,
      deadline: req.body.deadline ? new Date(req.body.deadline) : undefined,
      dependencies: req.body.dependencies,
      constraints: req.body.constraints
    };

    const response = await communicationService.sendTaskRequest(req.body.receiverId, taskRequest);

    if (response) {
      logger.info('Task request sent and responded', {
        taskId: taskRequest.taskId,
        receiverId: req.body.receiverId,
        accepted: response.accepted,
        userId: req.user?.id
      });

      res.json({
        success: true,
        taskRequest,
        response
      });
    } else {
      res.status(500).json({
        success: false,
        error: 'Failed to send task request or receive response'
      });
    }
  })
);

/**
 * POST /api/cross-agent/resource-request
 * Request resource access from another agent
 */
router.post('/resource-request',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR', 'AGENT']),
  body('receiverId').notEmpty().withMessage('Receiver ID is required'),
  validateResourceRequest,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const resourceRequest: ResourceRequest = {
      resourceId: req.body.resourceId,
      resourceType: req.body.resourceType,
      accessType: req.body.accessType,
      duration: req.body.duration,
      priority: req.body.priority,
      justification: req.body.justification
    };

    const grant = await communicationService.requestResource(req.body.receiverId, resourceRequest);

    if (grant) {
      logger.info('Resource request sent and responded', {
        resourceId: resourceRequest.resourceId,
        receiverId: req.body.receiverId,
        granted: grant.granted,
        userId: req.user?.id
      });

      res.json({
        success: true,
        resourceRequest,
        grant
      });
    } else {
      res.status(500).json({
        success: false,
        error: 'Failed to send resource request or receive response'
      });
    }
  })
);

/**
 * POST /api/cross-agent/share-knowledge
 * Share knowledge with other agents
 */
router.post('/share-knowledge',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR', 'AGENT']),
  validateKnowledgeShare,
  body('targetAgents').optional().isArray(),
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const knowledge: KnowledgeShare = {
      knowledgeId: req.body.knowledgeId,
      category: req.body.category,
      title: req.body.title,
      content: req.body.content,
      relevantAgents: req.body.relevantAgents,
      expiresAt: req.body.expiresAt ? new Date(req.body.expiresAt) : undefined,
      confidenceLevel: req.body.confidenceLevel,
      source: req.body.source,
      tags: req.body.tags
    };

    const shared = await communicationService.shareKnowledge(knowledge, req.body.targetAgents);

    if (shared) {
      logger.info('Knowledge shared via API', {
        knowledgeId: knowledge.knowledgeId,
        category: knowledge.category,
        targetAgents: req.body.targetAgents?.length || 'all',
        userId: req.user?.id
      });

      res.json({
        success: true,
        knowledge,
        message: 'Knowledge shared successfully'
      });
    } else {
      res.status(500).json({
        success: false,
        error: 'Failed to share knowledge'
      });
    }
  })
);

/**
 * POST /api/cross-agent/announce-capabilities
 * Announce capabilities to other agents
 */
router.post('/announce-capabilities',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR', 'AGENT']),
  body('capabilities').isArray({ min: 1 }).withMessage('At least one capability required'),
  body('loadFactor').isFloat({ min: 0, max: 1 }).withMessage('Load factor must be 0-1'),
  body('maxConcurrentTasks').isInt({ min: 1 }).withMessage('Max concurrent tasks must be positive'),
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const announcement: CapabilityAnnouncement = {
      capabilities: req.body.capabilities,
      availableFrom: req.body.availableFrom ? new Date(req.body.availableFrom) : undefined,
      availableUntil: req.body.availableUntil ? new Date(req.body.availableUntil) : undefined,
      loadFactor: req.body.loadFactor,
      maxConcurrentTasks: req.body.maxConcurrentTasks,
      specializations: req.body.specializations || []
    };

    await communicationService.announceCapabilities(announcement);

    logger.info('Capabilities announced via API', {
      capabilityCount: announcement.capabilities.length,
      loadFactor: announcement.loadFactor,
      userId: req.user?.id
    });

    res.json({
      success: true,
      announcement,
      message: 'Capabilities announced successfully'
    });
  })
);

/**
 * POST /api/cross-agent/report-conflict
 * Report a conflict for resolution
 */
router.post('/report-conflict',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR', 'AGENT']),
  validateConflictReport,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const conflict: ConflictReport = {
      conflictId: req.body.conflictId,
      type: req.body.type,
      involvedAgents: req.body.involvedAgents,
      resourcesInvolved: req.body.resourcesInvolved || [],
      description: req.body.description,
      severity: req.body.severity,
      detectedAt: new Date(),
      suggestedResolution: req.body.suggestedResolution
    };

    const resolution = await communicationService.reportConflict(conflict);

    logger.warn('Conflict reported via API', {
      conflictId: conflict.conflictId,
      type: conflict.type,
      severity: conflict.severity,
      involvedAgents: conflict.involvedAgents,
      userId: req.user?.id
    });

    res.json({
      success: true,
      conflict,
      resolution,
      message: resolution ? 'Conflict reported and resolved' : 'Conflict reported, resolution pending'
    });
  })
);

/**
 * GET /api/cross-agent/connected-agents
 * Get list of connected agents
 */
router.get('/connected-agents',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR', 'AGENT']),
  asyncHandler(async (req, res) => {
    const connectedAgents = communicationService.getConnectedAgents();

    res.json({
      success: true,
      agents: connectedAgents,
      count: connectedAgents.length
    });
  })
);

/**
 * GET /api/cross-agent/stats
 * Get communication statistics
 */
router.get('/stats',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR']),
  asyncHandler(async (req, res) => {
    const stats = communicationService.getCommunicationStats();

    res.json({
      success: true,
      stats
    });
  })
);

/**
 * POST /api/cross-agent/update-routing
 * Update routing table
 */
router.post('/update-routing',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR']),
  body('routes').isArray().withMessage('Routes array is required'),
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    communicationService.updateRoutingTable(req.body.routes);

    logger.info('Routing table updated via API', {
      routeCount: req.body.routes.length,
      userId: req.user?.id
    });

    res.json({
      success: true,
      message: 'Routing table updated successfully',
      routeCount: req.body.routes.length
    });
  })
);

/**
 * GET /api/cross-agent/protocol-info
 * Get protocol information and configuration
 */
router.get('/protocol-info',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR', 'AGENT']),
  asyncHandler(async (req, res) => {
    res.json({
      success: true,
      protocol: {
        version: '1.0.0',
        localAgent,
        supportedMessageTypes: Object.values(MessageType),
        supportedPriorities: Object.values(Priority),
        supportedConflictTypes: Object.values(ConflictType),
        features: [
          'secure_messaging',
          'task_coordination',
          'resource_management',
          'conflict_resolution',
          'knowledge_sharing',
          'capability_announcement'
        ]
      }
    });
  })
);

/**
 * POST /api/cross-agent/test-connection
 * Test connection to another agent
 */
router.post('/test-connection',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR']),
  body('agentId').notEmpty().withMessage('Agent ID is required'),
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { agentId } = req.body;

    // Send ping message
    const pingMessage: Message = {
      id: CommunicationUtils.generateMessageId(),
      type: MessageType.PING,
      senderId: localAgent.id,
      receiverId: agentId,
      timestamp: new Date(),
      priority: Priority.LOW,
      payload: { timestamp: Date.now() },
      signature: '',
      correlationId: CommunicationUtils.generateCorrelationId(),
      ttl: 10000, // 10 seconds
      metadata: {
        encrypted: false,
        compressed: false,
        contentType: 'application/json',
        encoding: 'utf-8',
        checksum: ''
      }
    };

    const sent = await communicationService.sendMessage(pingMessage);

    if (sent) {
      logger.info('Connection test initiated', {
        agentId,
        messageId: pingMessage.id,
        userId: req.user?.id
      });

      res.json({
        success: true,
        message: 'Ping sent successfully',
        messageId: pingMessage.id,
        correlationId: pingMessage.correlationId
      });
    } else {
      res.status(500).json({
        success: false,
        error: 'Failed to send ping message'
      });
    }
  })
);

export default router;

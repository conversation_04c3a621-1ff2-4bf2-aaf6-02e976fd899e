# EcoStamp Browser Extension

## Universal AI Environmental Impact Tracker

EcoStamp is a browser extension that tracks the environmental impact of your AI conversations in real-time across all major AI platforms.

### Features

- 🌍 **Universal Compatibility**: Works with ChatGPT, <PERSON>, <PERSON>, Copilot, and more
- ⚡ **Real-time Tracking**: See energy and water usage as you chat
- 🔒 **Cryptographic Verification**: SHA-256 hashes ensure data integrity
- 📊 **Detailed Analytics**: Track usage patterns over time
- 🌱 **Eco-Level Rating**: Visual leaf system shows environmental impact
- 🔗 **Open Source**: Full transparency with GitHub integration

### Supported Platforms

- ChatGPT (chat.openai.com)
- <PERSON> (claude.ai)
- Google Gemini (gemini.google.com)
- Microsoft Copilot (copilot.microsoft.com)
- You.com
- Perplexity AI
- Poe.com

### Installation

#### Chrome/Edge/Opera
1. Download the extension ZIP file
2. Extract to a folder
3. Open Chrome/Edge/Opera and go to Extensions
4. Enable "Developer mode"
5. Click "Load unpacked" and select the extracted folder

#### Firefox
1. Download the extension XPI file
2. Open Firefox and go to Add-ons
3. Click the gear icon and select "Install Add-on From File"
4. Select the downloaded XPI file

#### Safari
1. Download the extension ZIP file
2. Extract and follow Safari extension installation guide
3. Enable the extension in Safari preferences

### Usage

1. **Install the extension** following the instructions above
2. **Visit any supported AI platform** (ChatGPT, Claude, etc.)
3. **Start a conversation** - EcoStamp will automatically appear
4. **View real-time impact** in the floating widget
5. **Verify conversations** using the SHA-256 hash system

### Widget Features

The EcoStamp widget shows:
- **Platform**: Which AI service you're using
- **Eco Level**: Visual leaf rating (🌱🌱🌱🌱🌱)
- **Energy Usage**: Real-time kWh consumption
- **Water Usage**: Water consumption in liters
- **Hash**: Cryptographic proof of the conversation
- **Quick Actions**: Verify and GitHub links

### Settings

Access settings through the extension popup:
- **Enable Tracking**: Turn tracking on/off
- **Show Widget**: Display/hide the floating widget
- **Auto Verify**: Automatically verify conversations

### Privacy & Security

- **No Personal Data**: Only environmental impact metrics are tracked
- **Local Storage**: Sensitive data stays on your device
- **Open Source**: Full code transparency on GitHub
- **Cryptographic Proof**: SHA-256 verification ensures data integrity

### API Integration

EcoStamp connects to a local server (localhost:3000) for:
- Impact calculations
- Hash verification
- Data synchronization
- Analytics dashboard

### Troubleshooting

#### Extension Not Working
1. Check if the EcoStamp server is running (localhost:3000)
2. Refresh the AI platform page
3. Check browser console for errors
4. Ensure extension permissions are granted

#### Widget Not Appearing
1. Check extension settings (Show Widget enabled)
2. Verify the platform is supported
3. Try refreshing the page
4. Check if content scripts are blocked

#### Offline Mode
- Extension works offline with limited functionality
- Data syncs when connection is restored
- Local storage maintains conversation history

### Development

To modify or contribute:

1. Clone the repository
2. Make changes to the extension files
3. Test in developer mode
4. Submit pull requests on GitHub

### Support

- **GitHub**: https://github.com/chris-ai-dev/Time_Stamp_Project
- **Website**: http://localhost:3000
- **Issues**: Report bugs on GitHub Issues

### License

Open source - see GitHub repository for license details.

---

**Making AI environmentally accountable, one conversation at a time.** 🌱

/**
 * Selection Algorithms for DGM Evolution
 * 
 * Implements various selection strategies for evolutionary operations:
 * - Parent selection for reproduction
 * - Survivor selection for next generation
 * - Diversity-based selection
 * - Multi-objective selection
 */

const chalk = require('chalk');

class Selection {
  constructor(config) {
    this.config = config;
    this.selectionMethods = {
      tournament: this.tournamentSelection.bind(this),
      roulette: this.rouletteSelection.bind(this),
      rank: this.rankSelection.bind(this),
      elitist: this.elitistSelection.bind(this),
      diverse: this.diverseSelection.bind(this),
      pareto: this.paretoSelection.bind(this)
    };
  }

  /**
   * Select parents for reproduction
   */
  async selectParents(population, count = null) {
    const selectionMethod = this.config.get('genetics.selectionMethod', 'tournament');
    const parentCount = count || Math.floor(population.length * 0.5);
    
    console.log(chalk.blue(`🎯 Selecting ${parentCount} parents using ${selectionMethod} selection`));
    
    if (!this.selectionMethods[selectionMethod]) {
      throw new Error(`Unknown selection method: ${selectionMethod}`);
    }
    
    const parents = await this.selectionMethods[selectionMethod](population, parentCount);
    
    console.log(chalk.green(`✅ Selected ${parents.length} parents`));
    return parents;
  }

  /**
   * Select survivors for next generation
   */
  async selectSurvivors(population, targetSize = null) {
    const populationSize = targetSize || this.config.get('evolution.populationSize', 20);
    const elitismRate = this.config.get('evolution.elitismRate', 0.1);
    const diversityWeight = this.config.get('evolution.diversityWeight', 0.3);
    
    console.log(chalk.blue(`🏆 Selecting ${populationSize} survivors from ${population.length} agents`));
    
    if (population.length <= populationSize) {
      return population;
    }
    
    // Sort population by fitness
    const sortedPopulation = [...population].sort((a, b) => b.fitness - a.fitness);
    
    // Select elite agents
    const eliteCount = Math.floor(populationSize * elitismRate);
    const survivors = sortedPopulation.slice(0, eliteCount);
    
    // Select remaining agents with diversity consideration
    const remainingCount = populationSize - eliteCount;
    const candidates = sortedPopulation.slice(eliteCount);
    
    const diverseSurvivors = await this.selectDiverseAgents(
      candidates,
      remainingCount,
      survivors,
      diversityWeight
    );
    
    survivors.push(...diverseSurvivors);
    
    console.log(chalk.green(`✅ Selected ${survivors.length} survivors (${eliteCount} elite, ${diverseSurvivors.length} diverse)`));
    return survivors;
  }

  /**
   * Tournament selection
   */
  async tournamentSelection(population, count) {
    const tournamentSize = this.config.get('genetics.tournamentSize', 3);
    const selected = [];
    
    for (let i = 0; i < count; i++) {
      const tournament = [];
      
      // Select random individuals for tournament
      for (let j = 0; j < tournamentSize; j++) {
        const randomIndex = Math.floor(Math.random() * population.length);
        tournament.push(population[randomIndex]);
      }
      
      // Select the best from tournament
      const winner = tournament.reduce((best, current) => 
        current.fitness > best.fitness ? current : best
      );
      
      selected.push(winner);
    }
    
    return selected;
  }

  /**
   * Roulette wheel selection
   */
  async rouletteSelection(population, count) {
    const selected = [];
    
    // Calculate total fitness
    const totalFitness = population.reduce((sum, agent) => sum + Math.max(0, agent.fitness), 0);
    
    if (totalFitness === 0) {
      // If all fitness is 0 or negative, select randomly
      return this.randomSelection(population, count);
    }
    
    for (let i = 0; i < count; i++) {
      const randomValue = Math.random() * totalFitness;
      let cumulativeFitness = 0;
      
      for (const agent of population) {
        cumulativeFitness += Math.max(0, agent.fitness);
        if (cumulativeFitness >= randomValue) {
          selected.push(agent);
          break;
        }
      }
    }
    
    return selected;
  }

  /**
   * Rank-based selection
   */
  async rankSelection(population, count) {
    // Sort population by fitness
    const sortedPopulation = [...population].sort((a, b) => b.fitness - a.fitness);
    
    // Assign ranks (higher rank = better fitness)
    const totalRank = (sortedPopulation.length * (sortedPopulation.length + 1)) / 2;
    const selected = [];
    
    for (let i = 0; i < count; i++) {
      const randomValue = Math.random() * totalRank;
      let cumulativeRank = 0;
      
      for (let j = 0; j < sortedPopulation.length; j++) {
        cumulativeRank += (sortedPopulation.length - j);
        if (cumulativeRank >= randomValue) {
          selected.push(sortedPopulation[j]);
          break;
        }
      }
    }
    
    return selected;
  }

  /**
   * Elitist selection (top performers)
   */
  async elitistSelection(population, count) {
    const sortedPopulation = [...population].sort((a, b) => b.fitness - a.fitness);
    return sortedPopulation.slice(0, count);
  }

  /**
   * Diverse selection (maximize diversity)
   */
  async diverseSelection(population, count) {
    if (population.length <= count) {
      return population;
    }
    
    const selected = [];
    const remaining = [...population];
    
    // Select first agent (best fitness)
    const sortedPopulation = remaining.sort((a, b) => b.fitness - a.fitness);
    selected.push(sortedPopulation[0]);
    remaining.splice(remaining.indexOf(sortedPopulation[0]), 1);
    
    // Select remaining agents based on diversity
    while (selected.length < count && remaining.length > 0) {
      let bestCandidate = null;
      let bestDiversityScore = -1;
      let bestIndex = -1;
      
      for (let i = 0; i < remaining.length; i++) {
        const candidate = remaining[i];
        const diversityScore = this.calculateDiversityScore(candidate, selected);
        const combinedScore = 0.7 * candidate.fitness + 0.3 * diversityScore;
        
        if (combinedScore > bestDiversityScore) {
          bestDiversityScore = combinedScore;
          bestCandidate = candidate;
          bestIndex = i;
        }
      }
      
      if (bestCandidate) {
        selected.push(bestCandidate);
        remaining.splice(bestIndex, 1);
      } else {
        break;
      }
    }
    
    return selected;
  }

  /**
   * Pareto-based selection (multi-objective)
   */
  async paretoSelection(population, count) {
    // Calculate Pareto fronts
    const paretoFronts = this.calculateParetoFronts(population);
    const selected = [];
    
    // Select from Pareto fronts
    for (const front of paretoFronts) {
      if (selected.length + front.length <= count) {
        selected.push(...front);
      } else {
        // Select subset from current front using diversity
        const remainingCount = count - selected.length;
        const diverseFromFront = await this.selectDiverseAgents(
          front,
          remainingCount,
          selected,
          1.0 // Full diversity weight
        );
        selected.push(...diverseFromFront);
        break;
      }
    }
    
    return selected;
  }

  /**
   * Random selection (fallback)
   */
  randomSelection(population, count) {
    const selected = [];
    const shuffled = [...population].sort(() => Math.random() - 0.5);
    
    for (let i = 0; i < Math.min(count, shuffled.length); i++) {
      selected.push(shuffled[i]);
    }
    
    return selected;
  }

  /**
   * Select diverse agents from candidates
   */
  async selectDiverseAgents(candidates, count, existingSelected = [], diversityWeight = 0.5) {
    if (candidates.length <= count) {
      return candidates;
    }
    
    const selected = [];
    const remaining = [...candidates];
    
    while (selected.length < count && remaining.length > 0) {
      let bestCandidate = null;
      let bestScore = -1;
      let bestIndex = -1;
      
      for (let i = 0; i < remaining.length; i++) {
        const candidate = remaining[i];
        
        // Calculate diversity score relative to all selected agents
        const allSelected = [...existingSelected, ...selected];
        const diversityScore = this.calculateDiversityScore(candidate, allSelected);
        
        // Combine fitness and diversity
        const combinedScore = (1 - diversityWeight) * candidate.fitness + diversityWeight * diversityScore;
        
        if (combinedScore > bestScore) {
          bestScore = combinedScore;
          bestCandidate = candidate;
          bestIndex = i;
        }
      }
      
      if (bestCandidate) {
        selected.push(bestCandidate);
        remaining.splice(bestIndex, 1);
      } else {
        // Fallback: select randomly
        const randomIndex = Math.floor(Math.random() * remaining.length);
        selected.push(remaining.splice(randomIndex, 1)[0]);
      }
    }
    
    return selected;
  }

  /**
   * Calculate diversity score for an agent compared to selected agents
   */
  calculateDiversityScore(candidate, selectedAgents) {
    if (selectedAgents.length === 0) {
      return 1.0;
    }
    
    let totalDistance = 0;
    
    for (const selected of selectedAgents) {
      const distance = this.calculateAgentDistance(candidate, selected);
      totalDistance += distance;
    }
    
    return totalDistance / selectedAgents.length;
  }

  /**
   * Calculate distance between two agents
   */
  calculateAgentDistance(agent1, agent2) {
    let distance = 0;
    
    // Fitness distance
    const fitnessDistance = Math.abs(agent1.fitness - agent2.fitness);
    distance += fitnessDistance * 0.3;
    
    // Generation distance
    const generationDistance = Math.abs(agent1.generation - agent2.generation) / 10;
    distance += generationDistance * 0.2;
    
    // Type distance
    const typeDistance = agent1.type === agent2.type ? 0 : 1;
    distance += typeDistance * 0.2;
    
    // Metrics distance
    if (agent1.metrics && agent2.metrics) {
      const metricsDistance = this.calculateMetricsDistance(agent1.metrics, agent2.metrics);
      distance += metricsDistance * 0.3;
    }
    
    return distance;
  }

  /**
   * Calculate distance between metrics objects
   */
  calculateMetricsDistance(metrics1, metrics2) {
    const keys = ['performance', 'reliability', 'functionality', 'safety'];
    let distance = 0;
    
    for (const key of keys) {
      const val1 = metrics1[key] || 0;
      const val2 = metrics2[key] || 0;
      distance += Math.pow(val1 - val2, 2);
    }
    
    return Math.sqrt(distance);
  }

  /**
   * Calculate Pareto fronts for multi-objective optimization
   */
  calculateParetoFronts(population) {
    const fronts = [];
    let remaining = [...population];
    
    while (remaining.length > 0) {
      const currentFront = [];
      const dominated = new Set();
      
      // Find non-dominated solutions
      for (let i = 0; i < remaining.length; i++) {
        const agent1 = remaining[i];
        let isDominated = false;
        
        for (let j = 0; j < remaining.length; j++) {
          if (i === j) continue;
          
          const agent2 = remaining[j];
          if (this.dominates(agent2, agent1)) {
            isDominated = true;
            break;
          }
        }
        
        if (!isDominated) {
          currentFront.push(agent1);
          dominated.add(i);
        }
      }
      
      // Remove dominated solutions from remaining
      remaining = remaining.filter((_, index) => !dominated.has(index));
      
      if (currentFront.length > 0) {
        fronts.push(currentFront);
      } else {
        // Fallback: add remaining agents to avoid infinite loop
        fronts.push(remaining);
        break;
      }
    }
    
    return fronts;
  }

  /**
   * Check if agent1 dominates agent2 (Pareto dominance)
   */
  dominates(agent1, agent2) {
    const objectives1 = this.getObjectives(agent1);
    const objectives2 = this.getObjectives(agent2);
    
    let betterInAtLeastOne = false;
    
    for (let i = 0; i < objectives1.length; i++) {
      if (objectives1[i] < objectives2[i]) {
        return false; // agent1 is worse in this objective
      }
      if (objectives1[i] > objectives2[i]) {
        betterInAtLeastOne = true;
      }
    }
    
    return betterInAtLeastOne;
  }

  /**
   * Get objectives for multi-objective optimization
   */
  getObjectives(agent) {
    return [
      agent.fitness || 0,
      agent.metrics?.performance || 0,
      agent.metrics?.reliability || 0,
      agent.metrics?.functionality || 0,
      agent.metrics?.safety || 0
    ];
  }

  /**
   * Adaptive selection based on population state
   */
  async adaptiveSelection(population, count, generationNumber) {
    const diversityScore = this.calculatePopulationDiversity(population);
    const averageFitness = population.reduce((sum, agent) => sum + agent.fitness, 0) / population.length;
    
    // Adapt selection strategy based on population state
    let selectionMethod;
    
    if (diversityScore < 0.3) {
      // Low diversity - use diverse selection
      selectionMethod = 'diverse';
    } else if (averageFitness < 0.5) {
      // Low fitness - use elitist selection
      selectionMethod = 'elitist';
    } else if (generationNumber < 10) {
      // Early generations - use tournament selection
      selectionMethod = 'tournament';
    } else {
      // Later generations - use rank selection
      selectionMethod = 'rank';
    }
    
    console.log(chalk.blue(`🔄 Using adaptive selection: ${selectionMethod} (diversity: ${diversityScore.toFixed(3)}, fitness: ${averageFitness.toFixed(3)})`));
    
    return await this.selectionMethods[selectionMethod](population, count);
  }

  /**
   * Calculate population diversity
   */
  calculatePopulationDiversity(population) {
    if (population.length < 2) {
      return 1.0;
    }
    
    let totalDistance = 0;
    let comparisons = 0;
    
    for (let i = 0; i < population.length; i++) {
      for (let j = i + 1; j < population.length; j++) {
        totalDistance += this.calculateAgentDistance(population[i], population[j]);
        comparisons++;
      }
    }
    
    return comparisons > 0 ? totalDistance / comparisons : 0;
  }

  /**
   * Selection pressure analysis
   */
  analyzeSelectionPressure(population, selected) {
    const analysis = {
      selectionPressure: 0,
      fitnessVariance: 0,
      diversityLoss: 0,
      eliteRetention: 0
    };
    
    // Calculate selection pressure (ratio of selected to total)
    analysis.selectionPressure = selected.length / population.length;
    
    // Calculate fitness variance
    const popFitness = population.map(agent => agent.fitness);
    const selFitness = selected.map(agent => agent.fitness);
    
    const popMean = popFitness.reduce((sum, f) => sum + f, 0) / popFitness.length;
    const selMean = selFitness.reduce((sum, f) => sum + f, 0) / selFitness.length;
    
    const popVariance = popFitness.reduce((sum, f) => sum + Math.pow(f - popMean, 2), 0) / popFitness.length;
    const selVariance = selFitness.reduce((sum, f) => sum + Math.pow(f - selMean, 2), 0) / selFitness.length;
    
    analysis.fitnessVariance = selVariance / (popVariance || 1);
    
    // Calculate diversity loss
    const popDiversity = this.calculatePopulationDiversity(population);
    const selDiversity = this.calculatePopulationDiversity(selected);
    analysis.diversityLoss = 1 - (selDiversity / (popDiversity || 1));
    
    // Calculate elite retention (top 10% retention rate)
    const eliteCount = Math.floor(population.length * 0.1);
    const sortedPop = [...population].sort((a, b) => b.fitness - a.fitness);
    const elites = sortedPop.slice(0, eliteCount);
    const retainedElites = elites.filter(elite => selected.includes(elite));
    analysis.eliteRetention = retainedElites.length / eliteCount;
    
    return analysis;
  }

  /**
   * Get selection statistics
   */
  getSelectionStats(selectionHistory) {
    const stats = {
      totalSelections: selectionHistory.length,
      methodUsage: {},
      averageSelectionPressure: 0,
      averageDiversityLoss: 0,
      averageEliteRetention: 0
    };
    
    if (selectionHistory.length === 0) {
      return stats;
    }
    
    let totalPressure = 0;
    let totalDiversityLoss = 0;
    let totalEliteRetention = 0;
    
    for (const selection of selectionHistory) {
      // Count method usage
      const method = selection.method || 'unknown';
      stats.methodUsage[method] = (stats.methodUsage[method] || 0) + 1;
      
      // Aggregate metrics
      if (selection.analysis) {
        totalPressure += selection.analysis.selectionPressure || 0;
        totalDiversityLoss += selection.analysis.diversityLoss || 0;
        totalEliteRetention += selection.analysis.eliteRetention || 0;
      }
    }
    
    stats.averageSelectionPressure = totalPressure / selectionHistory.length;
    stats.averageDiversityLoss = totalDiversityLoss / selectionHistory.length;
    stats.averageEliteRetention = totalEliteRetention / selectionHistory.length;
    
    return stats;
  }
}

module.exports = Selection;

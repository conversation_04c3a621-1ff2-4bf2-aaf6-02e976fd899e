<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EcoStamp</title>
    <style>
        body {
            width: 350px;
            min-height: 400px;
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .header {
            padding: 20px;
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }
        
        .header h1 {
            margin: 0 0 5px 0;
            font-size: 18px;
            font-weight: 600;
        }
        
        .header p {
            margin: 0;
            font-size: 12px;
            opacity: 0.9;
        }
        
        .stats {
            padding: 20px;
        }
        
        .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .stat-item:last-child {
            border-bottom: none;
        }
        
        .stat-label {
            font-size: 13px;
            opacity: 0.9;
        }
        
        .stat-value {
            font-size: 14px;
            font-weight: 600;
        }
        
        .platforms {
            padding: 0 20px 20px;
        }
        
        .platform-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 6px 0;
            font-size: 12px;
        }
        
        .platform-name {
            opacity: 0.9;
        }
        
        .platform-count {
            background: rgba(255, 255, 255, 0.2);
            padding: 2px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        
        .controls {
            padding: 0 20px 20px;
        }
        
        .btn {
            width: 100%;
            padding: 10px;
            margin: 5px 0;
            border: none;
            border-radius: 6px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            font-size: 13px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-1px);
        }
        
        .eco-levels {
            padding: 0 20px 20px;
        }
        
        .eco-level {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 0;
            font-size: 12px;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            opacity: 0.7;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🌱 EcoStamp</h1>
        <p>AI Environmental Impact Tracker</p>
    </div>
    
    <div class="stats" id="stats">
        <div class="loading">Loading statistics...</div>
    </div>
    
    <div class="platforms" id="platforms" style="display: none;">
        <h3 style="margin: 0 0 10px 0; font-size: 14px;">Platforms Used</h3>
    </div>
    
    <div class="eco-levels" id="ecoLevels" style="display: none;">
        <h3 style="margin: 0 0 10px 0; font-size: 14px;">Eco-Level Distribution</h3>
    </div>
    
    <div class="controls">
        <button class="btn" id="toggleBtn">Disable EcoStamp</button>
        <button class="btn" id="resetBtn">Reset Statistics</button>
        <button class="btn" id="exportBtn">Export Data</button>
    </div>
    
    <script src="popup.js"></script>
</body>
</html>

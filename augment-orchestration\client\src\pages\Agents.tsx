import React, { useEffect } from 'react'
import { Box, Typography, Card, CardContent, Grid, Chip } from '@mui/material'
import { useAppDispatch, useAppSelector } from '../hooks/redux'
import { fetchAgents, fetchAgentRegistry } from '../store/slices/agentSlice'

const Agents: React.FC = () => {
  const dispatch = useAppDispatch()
  const { agents, roles, registry, isLoading } = useAppSelector((state) => state.agent)

  useEffect(() => {
    dispatch(fetchAgents())
    dispatch(fetchAgentRegistry())
  }, [dispatch])

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        🤖 AI Agents
      </Typography>
      <Typography variant="body1" color="text.secondary" gutterBottom>
        Manage AI agents, role assignments, and capabilities
      </Typography>
      
      <Grid container spacing={3} sx={{ mt: 2 }}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Agent Registry
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                {agents.length} agents available
              </Typography>
              {agents.map((agent) => (
                <Box key={agent.id} sx={{ mb: 2, p: 2, border: 1, borderColor: 'divider', borderRadius: 1 }}>
                  <Typography variant="subtitle1">{agent.name}</Typography>
                  <Typography variant="body2" color="text.secondary">{agent.vendor}</Typography>
                  <Box sx={{ mt: 1 }}>
                    {agent.roles.map((role) => (
                      <Chip key={role} label={role} size="small" sx={{ mr: 1, mb: 1 }} />
                    ))}
                  </Box>
                </Box>
              ))}
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Role Definitions
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                {roles.length} roles defined
              </Typography>
              {roles.map((role) => (
                <Box key={role.id} sx={{ mb: 2, p: 2, border: 1, borderColor: 'divider', borderRadius: 1 }}>
                  <Typography variant="subtitle1">{role.name}</Typography>
                  <Typography variant="body2" color="text.secondary">{role.description}</Typography>
                </Box>
              ))}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  )
}

export default Agents

[{"timestamp": "2025-07-25T22:42:51.549835", "domain": "drone_ai", "agent_id": "drone_001", "feedback_type": "correct", "confidence_score": 0.8, "output": {"latitude": 37.7749, "longitude": -122.4194, "altitude": 100.5, "accuracy": 2.1, "timestamp": "2025-07-25T22:42:51.549796"}, "context": {"mission": "search_rescue"}}, {"timestamp": "2025-07-25T22:42:51.549903", "domain": "drone_ai", "agent_id": "drone_002", "feedback_type": "partially_correct", "confidence_score": 0.5, "output": {"image_path": "/drone/images/area_001.jpg", "detection_confidence": 0.3, "image_quality_score": 0.4, "objects_detected": ["tree", "rock"]}, "context": {"mission": "surveillance"}}, {"timestamp": "2025-07-25T22:42:51.549963", "domain": "drone_ai", "agent_id": "drone_003", "feedback_type": "incorrect", "confidence_score": 0.1, "output": {"latitude": 91.0, "longitude": -200.0, "sensor_health": 0.1}, "context": {"mission": "mapping"}}, {"timestamp": "2025-07-25T22:42:51.550137", "domain": "timestamp_ai", "agent_id": "timestamp_001", "feedback_type": "partially_correct", "confidence_score": 0.6, "output": {"timestamp": "2025-07-25T22:42:51.550130", "hash": "a1b2c3d4e5f6789012345678901234567890123456789012345678901234567890", "verification_status": true}, "context": {"model": "claude"}}, {"timestamp": "2025-07-25T22:42:51.550195", "domain": "timestamp_ai", "agent_id": "timestamp_002", "feedback_type": "partially_correct", "confidence_score": 0.6, "output": {"timestamp": "2025-07-25T22:42:51.550189", "hash": "abc123", "verification_status": true}, "context": {"model": "gpt-4"}}, {"timestamp": "2025-07-25T22:42:51.550254", "domain": "timestamp_ai", "agent_id": "timestamp_003", "feedback_type": "partially_correct", "confidence_score": 0.6, "output": {"timestamp": **********.55025, "hash": "b2c3d4e5f6789012345678901234567890123456789012345678901234567890a1", "verification_status": false}, "context": {"model": "gemini"}}, {"timestamp": "2025-07-25T22:42:51.550460", "domain": "drone_ai", "agent_id": "rescue_drone_01", "feedback_type": "correct", "confidence_score": 0.8, "output": {"latitude": 40.7128, "longitude": -74.006, "altitude": 150.0, "mission_status": "deployed"}, "context": {"mission": "search_rescue"}}, {"timestamp": "2025-07-25T22:42:51.550518", "domain": "timestamp_ai", "agent_id": "timestamp_sar_01", "feedback_type": "partially_correct", "confidence_score": 0.6, "output": {"timestamp": "2025-07-25T22:42:51.550512", "hash": "c3d4e5f6789012345678901234567890123456789012345678901234567890a1b2", "verification_status": true, "mission_id": "SAR_001"}, "context": {"mission": "search_rescue"}}, {"timestamp": "2025-07-25T22:42:51.550577", "domain": "drone_ai", "agent_id": "rescue_drone_01", "feedback_type": "correct", "confidence_score": 0.8, "output": {"latitude": 40.713, "longitude": -74.0058, "detection_confidence": 0.85, "target_type": "person", "image_quality_score": 0.9}, "context": {"mission": "search_rescue", "target": "missing_person"}}]
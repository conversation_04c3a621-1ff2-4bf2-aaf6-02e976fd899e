/* Global styles for Augment Orchestration */

* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  font-family: 'Inter', 'Roboto', 'Helvetica', 'Arial', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  min-height: 100vh;
}

/* Custom scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #2b2b2b;
}

::-webkit-scrollbar-thumb {
  background: #6b6b6b;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #959595;
}

/* React Flow custom styles */
.react-flow__node {
  font-family: 'Inter', sans-serif;
}

.react-flow__edge {
  stroke-width: 2;
}

/* Custom animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

/* Loading spinner overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

/* Custom Material-UI overrides */
.MuiCard-root {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.MuiCard-root:hover {
  transform: translateY(-2px);
}

/* Agent badge styles */
.agent-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.75rem;
  border-radius: 16px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.agent-badge.openai {
  background-color: #10a37f;
  color: white;
}

.agent-badge.anthropic {
  background-color: #d97706;
  color: white;
}

.agent-badge.google {
  background-color: #4285f4;
  color: white;
}

.agent-badge.microsoft {
  background-color: #0078d4;
  color: white;
}

.agent-badge.meta {
  background-color: #1877f2;
  color: white;
}

/* Status indicators */
.status-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
}

.status-indicator.active {
  background-color: #4caf50;
}

.status-indicator.inactive {
  background-color: #f44336;
}

.status-indicator.running {
  background-color: #ff9800;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

@echo off
setlocal enabledelayedexpansion

:: AI Orchestration System Launcher
:: Quick start launcher for the AI Orchestration System

title AI Orchestration System - Launcher

echo.
echo ========================================
echo    🤖 AI Orchestration System
echo    Quick Start Launcher
echo ========================================
echo.
echo This launcher provides quick access to
echo the AI Orchestration System components.
echo.

:main_menu
echo.
echo What would you like to do?
echo.
echo 1. Run System Test
echo 2. Start All Orchestrators
echo 3. Start Individual Components
echo 4. View System Status
echo 5. Open Configuration
echo 6. View Documentation
echo 7. Exit
echo.
set /p choice="Enter your choice (1-7): "

if "%choice%"=="1" goto run_test
if "%choice%"=="2" goto start_all
if "%choice%"=="3" goto start_individual
if "%choice%"=="4" goto view_status
if "%choice%"=="5" goto open_config
if "%choice%"=="6" goto view_docs
if "%choice%"=="7" goto end

echo Invalid choice. Please try again.
goto main_menu

:run_test
echo.
echo Running system test...
echo.
node orchestrator.js test
echo.
echo Press any key to return to main menu...
pause >nul
goto main_menu

:start_all
echo.
echo Starting all orchestrators...
echo.
echo Starting Meta Orchestrator on port 3001...
start "Meta Orchestrator" cmd /k "cd meta-orchestrator && echo Meta Orchestrator starting... && node index.js"
timeout /t 2 /nobreak >nul

echo Starting Universal Orchestrator on port 3002...
start "Universal Orchestrator" cmd /k "cd universal-orchestrator && echo Universal Orchestrator starting... && node index.js"
timeout /t 2 /nobreak >nul

echo Starting DGM System on port 3003...
start "DGM System" cmd /k "cd dgm && echo DGM System starting... && node index.js"
timeout /t 2 /nobreak >nul

echo Starting Main Orchestrator...
start "Main Orchestrator" cmd /k "echo Main Orchestrator starting... && node orchestrator.js start"

echo.
echo ✅ All orchestrators started!
echo.
echo Access points:
echo - Main Dashboard: http://localhost:3000
echo - Meta Orchestrator: http://localhost:3001
echo - Universal Orchestrator: http://localhost:3002
echo - DGM System: http://localhost:3003
echo.
echo Press any key to return to main menu...
pause >nul
goto main_menu

:start_individual
echo.
echo Individual Component Startup
echo.
echo 1. Meta Orchestrator
echo 2. Universal Orchestrator
echo 3. DGM System
echo 4. Main Orchestrator
echo 5. Workflow Automation
echo 6. Back to main menu
echo.
set /p comp="Select component (1-6): "

if "%comp%"=="1" (
    echo Starting Meta Orchestrator...
    start "Meta Orchestrator" cmd /k "cd meta-orchestrator && echo Meta Orchestrator starting... && node index.js"
)
if "%comp%"=="2" (
    echo Starting Universal Orchestrator...
    start "Universal Orchestrator" cmd /k "cd universal-orchestrator && echo Universal Orchestrator starting... && node index.js"
)
if "%comp%"=="3" (
    echo Starting DGM System...
    start "DGM System" cmd /k "cd dgm && echo DGM System starting... && node index.js"
)
if "%comp%"=="4" (
    echo Starting Main Orchestrator...
    start "Main Orchestrator" cmd /k "echo Main Orchestrator starting... && node orchestrator.js start"
)
if "%comp%"=="5" (
    echo Starting Workflow Automation...
    start "Workflow Automation" cmd /k "cd scripts && echo Workflow Automation starting... && node workflow-automation.js"
)
if "%comp%"=="6" goto main_menu

echo.
echo Component started successfully!
echo.
echo Press any key to return to main menu...
pause >nul
goto main_menu

:view_status
echo.
echo Checking system status...
echo.
node orchestrator.js status
echo.
echo Press any key to return to main menu...
pause >nul
goto main_menu

:open_config
echo.
echo Opening configuration...
echo.
if exist "config\main-config.json" (
    echo Opening configuration file...
    start notepad config\main-config.json
) else (
    echo Configuration file not found. Creating default configuration...
    mkdir config 2>nul
    echo {> config\main-config.json
    echo   "version": "1.0.0",>> config\main-config.json
    echo   "environment": "production",>> config\main-config.json
    echo   "orchestrators": {>> config\main-config.json
    echo     "meta": {>> config\main-config.json
    echo       "enabled": true,>> config\main-config.json
    echo       "port": 3001>> config\main-config.json
    echo     },>> config\main-config.json
    echo     "universal": {>> config\main-config.json
    echo       "enabled": true,>> config\main-config.json
    echo       "port": 3002>> config\main-config.json
    echo     },>> config\main-config.json
    echo     "dgm": {>> config\main-config.json
    echo       "enabled": true,>> config\main-config.json
    echo       "port": 3003>> config\main-config.json
    echo     }>> config\main-config.json
    echo   },>> config\main-config.json
    echo   "logging": {>> config\main-config.json
    echo     "level": "info",>> config\main-config.json
    echo     "file": "logs/orchestration.log">> config\main-config.json
    echo   }>> config\main-config.json
    echo }>> config\main-config.json
    echo Configuration created. Opening...
    start notepad config\main-config.json
)
echo.
echo Press any key to return to main menu...
pause >nul
goto main_menu

:view_docs
echo.
echo Opening documentation...
echo.
if exist "README.md" (
    start notepad README.md
) else (
    echo Creating documentation...
    echo # AI Orchestration System > README.md
    echo. >> README.md
    echo ## Quick Start >> README.md
    echo 1. Run AI-Orchestration-Launcher.bat >> README.md
    echo 2. Choose "Start All Orchestrators" >> README.md
    echo 3. Access http://localhost:3000 >> README.md
    echo. >> README.md
    echo ## Components >> README.md
    echo - Meta Orchestrator: Advanced AI coordination >> README.md
    echo - Universal Orchestrator: Cross-platform AI management >> README.md
    echo - DGM: Dynamic Goal Management system >> README.md
    echo - Workflow Automation: Automated task processing >> README.md
    echo - Cross-Flow Engine: Multi-AI workflow coordination >> README.md
    start notepad README.md
)
echo.
echo Press any key to return to main menu...
pause >nul
goto main_menu

:end
echo.
echo ========================================
echo    AI Orchestration System Ready
echo ========================================
echo.
echo For support and documentation:
echo - GitHub: https://github.com/chris-ai-dev/Time_Stamp_Project
echo - Local docs: README.md files in each component
echo.
echo Thank you for using AI Orchestration System!
echo.
pause
exit /b 0

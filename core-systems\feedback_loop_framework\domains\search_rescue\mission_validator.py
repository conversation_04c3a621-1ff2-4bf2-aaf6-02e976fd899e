"""
Search and Rescue Mission Validator

Validates Search and Rescue mission outcomes and assigns feedback types
including support for 'Partially Correct' scenarios specific to SAR operations.
"""

import logging
from typing import Dict, Any, List
from ...core.feedback_types import ValidationResult, FeedbackType, ValidationSeverity


class SearchRescueValidator:
    """
    Validates Search and Rescue mission outcomes and determines appropriate feedback types.
    
    Handles various SAR-specific scenarios including partial target identification,
    reference item detection accuracy, and mission completion assessment.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.validation_criteria = self._initialize_criteria()
        
    def _initialize_criteria(self) -> Dict[str, Any]:
        """Initialize validation criteria for Search and Rescue operations."""
        return {
            'mission_success_thresholds': {
                'target_found_confidence': 0.9,      # 90% confidence for target found
                'target_partial_confidence': 0.7,    # 70% confidence for partial identification
                'reference_item_relevance': 0.6,     # 60% relevance for useful reference items
                'area_coverage_complete': 0.95,      # 95% area coverage for complete search
                'area_coverage_partial': 0.70        # 70% area coverage for partial search
            },
            'detection_quality_thresholds': {
                'high_quality_detection': 0.8,       # 80% confidence for high quality
                'acceptable_detection': 0.6,         # 60% confidence for acceptable
                'low_quality_detection': 0.4,        # 40% confidence for low quality
                'false_positive_threshold': 0.3      # Below 30% likely false positive
            },
            'partial_correctness_rules': {
                'target_identification': {
                    'confirmed_target': 1.0,          # Target positively identified
                    'likely_target': 0.8,             # High probability target
                    'possible_target': 0.6,           # Possible target requiring verification
                    'reference_items_only': 0.4       # Only reference items found
                },
                'reference_item_analysis': {
                    'multiple_relevant_items': 0.9,   # Multiple relevant items found
                    'single_relevant_item': 0.7,      # Single relevant item found
                    'possible_relevant_items': 0.5,   # Items possibly relevant
                    'no_relevant_items': 0.0          # No relevant items found
                },
                'search_coverage': {
                    'complete_systematic': 1.0,       # Complete systematic search
                    'mostly_complete': 0.8,           # Mostly complete search
                    'partial_coverage': 0.6,          # Partial area coverage
                    'limited_coverage': 0.4           # Limited area coverage
                }
            },
            'mission_outcome_mapping': {
                'target_found': FeedbackType.CORRECT,
                'target_likely': FeedbackType.PARTIALLY_CORRECT,
                'reference_items_found': FeedbackType.PARTIALLY_CORRECT,
                'search_incomplete': FeedbackType.PARTIALLY_CORRECT,
                'no_findings': FeedbackType.INCORRECT,
                'mission_failed': FeedbackType.INCORRECT,
                'system_error': FeedbackType.MISCELLANEOUS
            }
        }
    
    def validate(self, 
                interpreted_output: Dict[str, Any], 
                match_results: Dict[str, Any], 
                context: Dict[str, Any]) -> ValidationResult:
        """
        Validate Search and Rescue mission output and determine feedback type.
        
        Args:
            interpreted_output: Normalized detection data from interpreter
            match_results: Results from reference matcher
            context: Mission context including objectives, area, conditions
            
        Returns:
            ValidationResult with feedback type and mission assessment
        """
        try:
            # Initialize validation result
            result = ValidationResult()
            result.metadata = {
                'validation_timestamp': match_results.get('metadata', {}).get('match_timestamp'),
                'validator_version': '1.0.0',
                'validation_type': 'search_rescue_mission',
                'mission_id': context.get('mission_id', 'unknown')
            }
            
            # Check for critical mission failures first
            if self._has_critical_failures(interpreted_output, match_results, context):
                result.feedback_type = FeedbackType.INCORRECT
                result.is_valid = False
                result.confidence_score = 0.0
                self._add_critical_failure_issues(result, interpreted_output, match_results, context)
                return result
            
            # Determine mission outcome and feedback type
            mission_outcome = self._assess_mission_outcome(interpreted_output, match_results, context)
            feedback_type, confidence = self._determine_feedback_type(mission_outcome, context)
            
            result.feedback_type = feedback_type
            result.confidence_score = confidence
            result.is_valid = feedback_type in [FeedbackType.CORRECT, FeedbackType.PARTIALLY_CORRECT]
            
            # Add detailed mission analysis
            self._analyze_mission_performance(result, interpreted_output, match_results, context, mission_outcome)
            self._generate_mission_recommendations(result, mission_outcome, context)
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error in Search and Rescue validation: {str(e)}")
            
            error_result = ValidationResult()
            error_result.feedback_type = FeedbackType.MISCELLANEOUS
            error_result.is_valid = False
            error_result.confidence_score = 0.0
            error_result.issues = [{
                'type': 'validation_error',
                'severity': ValidationSeverity.CRITICAL,
                'message': f'Mission validation failed: {str(e)}',
                'details': {'error': str(e)}
            }]
            
            return error_result
    
    def _has_critical_failures(self, interpreted_output: Dict[str, Any], 
                              match_results: Dict[str, Any], 
                              context: Dict[str, Any]) -> bool:
        """Check for critical mission failures."""
        # Check for data interpretation errors
        if interpreted_output.get('error', False):
            return True
        
        # Check for system failures
        mission_status = context.get('mission_status', 'unknown')
        if mission_status in ['system_failure', 'emergency_abort', 'communication_lost']:
            return True
        
        # Check for critical anomalies in detection
        for anomaly in match_results.get('anomalies', []):
            if anomaly.get('type') == 'matcher_error':
                return True
        
        return False
    
    def _assess_mission_outcome(self, interpreted_output: Dict[str, Any], 
                               match_results: Dict[str, Any], 
                               context: Dict[str, Any]) -> Dict[str, Any]:
        """Assess overall mission outcome based on detections and context."""
        classifications = interpreted_output.get('classifications', [])
        overall_relevance = match_results.get('overall_relevance', 0.0)
        mission_params = context.get('mission_parameters', {})
        
        # Analyze target detection
        target_analysis = self._analyze_target_detection(classifications, match_results)
        
        # Analyze reference items
        reference_analysis = self._analyze_reference_items(classifications, match_results)
        
        # Analyze search coverage
        coverage_analysis = self._analyze_search_coverage(context)
        
        # Determine primary outcome
        primary_outcome = self._determine_primary_outcome(
            target_analysis, reference_analysis, coverage_analysis, overall_relevance
        )
        
        return {
            'primary_outcome': primary_outcome,
            'target_analysis': target_analysis,
            'reference_analysis': reference_analysis,
            'coverage_analysis': coverage_analysis,
            'overall_relevance': overall_relevance,
            'mission_duration': context.get('mission_duration_minutes', 0),
            'environmental_factors': self._extract_environmental_factors(context)
        }
    
    def _analyze_target_detection(self, classifications: List[Dict[str, Any]], 
                                 match_results: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze target person detection results."""
        target_detections = [c for c in classifications if c.get('sar_category') == 'target_person']
        
        if not target_detections:
            return {
                'status': 'not_detected',
                'confidence': 0.0,
                'detection_count': 0
            }
        
        # Find highest confidence target detection
        best_detection = max(target_detections, key=lambda x: x.get('confidence', 0.0))
        max_confidence = best_detection.get('confidence', 0.0)
        
        # Determine target status
        thresholds = self.validation_criteria['mission_success_thresholds']
        
        if max_confidence >= thresholds['target_found_confidence']:
            status = 'confirmed'
        elif max_confidence >= thresholds['target_partial_confidence']:
            status = 'likely'
        else:
            status = 'possible'
        
        return {
            'status': status,
            'confidence': max_confidence,
            'detection_count': len(target_detections),
            'best_detection': best_detection,
            'all_detections': target_detections
        }
    
    def _analyze_reference_items(self, classifications: List[Dict[str, Any]], 
                                match_results: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze reference item detection results."""
        reference_categories = ['clothing', 'personal_items', 'broken_environment']
        reference_detections = [c for c in classifications if c.get('sar_category') in reference_categories]
        
        if not reference_detections:
            return {
                'status': 'none_found',
                'relevance_score': 0.0,
                'item_count': 0,
                'categories_found': []
            }
        
        # Calculate relevance scores
        relevance_scores = []
        categories_found = set()
        
        for detection in reference_detections:
            # Use pattern match scores if available
            detection_relevance = detection.get('confidence', 0.5)  # Use detection confidence as base
            detection_category = detection.get('sar_category', '')

            # Look for matching pattern matches by category
            for pattern_match in match_results.get('pattern_matches', []):
                pattern_type = pattern_match.get('type', '')
                if detection_category in pattern_type or pattern_type.startswith(detection_category):
                    detection_relevance = max(detection_relevance, pattern_match.get('confidence', 0.5))
                    break

            relevance_scores.append(detection_relevance)
            categories_found.add(detection_category)
        
        avg_relevance = sum(relevance_scores) / len(relevance_scores) if relevance_scores else 0.0
        
        # Determine reference item status
        threshold = self.validation_criteria['mission_success_thresholds']['reference_item_relevance']
        
        if avg_relevance >= threshold and len(categories_found) >= 2:
            status = 'multiple_relevant'
        elif avg_relevance >= threshold:
            status = 'single_relevant'
        elif avg_relevance >= threshold * 0.7:
            status = 'possibly_relevant'
        else:
            status = 'low_relevance'
        
        return {
            'status': status,
            'relevance_score': avg_relevance,
            'item_count': len(reference_detections),
            'categories_found': list(categories_found),
            'detections': reference_detections
        }
    
    def _analyze_search_coverage(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze search area coverage."""
        mission_params = context.get('mission_parameters', {})
        
        # Get coverage metrics
        planned_area = mission_params.get('search_area_km2', context.get('search_area_km2', 1.0))
        covered_area = context.get('area_covered_km2', planned_area * 0.8)  # Default to 80% coverage if not specified
        coverage_percentage = covered_area / planned_area if planned_area > 0 else 0.8
        
        # Determine coverage status
        thresholds = self.validation_criteria['mission_success_thresholds']
        
        if coverage_percentage >= thresholds['area_coverage_complete']:
            status = 'complete'
        elif coverage_percentage >= thresholds['area_coverage_partial']:
            status = 'partial'
        else:
            status = 'limited'
        
        return {
            'status': status,
            'coverage_percentage': coverage_percentage,
            'planned_area_km2': planned_area,
            'covered_area_km2': covered_area,
            'search_pattern': context.get('search_pattern', 'unknown')
        }
    
    def _determine_primary_outcome(self, target_analysis: Dict[str, Any], 
                                  reference_analysis: Dict[str, Any], 
                                  coverage_analysis: Dict[str, Any], 
                                  overall_relevance: float) -> str:
        """Determine the primary mission outcome."""
        target_status = target_analysis.get('status', 'not_detected')
        reference_status = reference_analysis.get('status', 'none_found')
        coverage_status = coverage_analysis.get('status', 'limited')
        
        # Priority order: target found > reference items > coverage
        if target_status == 'confirmed':
            return 'target_found'
        elif target_status == 'likely':
            return 'target_likely'
        elif reference_status in ['multiple_relevant', 'single_relevant']:
            return 'reference_items_found'
        elif coverage_status == 'partial' and overall_relevance > 0.3:
            return 'search_incomplete'
        elif coverage_status == 'complete' and overall_relevance < 0.2:
            return 'no_findings'
        else:
            return 'mission_failed'
    
    def _determine_feedback_type(self, mission_outcome: Dict[str, Any], context: Dict[str, Any]) -> tuple[FeedbackType, float]:
        """Determine feedback type and confidence based on mission outcome."""
        primary_outcome = mission_outcome.get('primary_outcome', 'mission_failed')
        target_analysis = mission_outcome.get('target_analysis', {})
        reference_analysis = mission_outcome.get('reference_analysis', {})
        coverage_analysis = mission_outcome.get('coverage_analysis', {})
        
        # Get base feedback type from outcome mapping
        outcome_mapping = self.validation_criteria['mission_outcome_mapping']
        feedback_type = outcome_mapping.get(primary_outcome, FeedbackType.INCORRECT)
        
        # Calculate confidence score
        confidence = self._calculate_mission_confidence(mission_outcome)
        
        # Apply partial correctness rules for refinement
        if feedback_type == FeedbackType.PARTIALLY_CORRECT:
            confidence = self._refine_partial_correctness_confidence(mission_outcome, confidence)
        
        return feedback_type, confidence
    
    def _calculate_mission_confidence(self, mission_outcome: Dict[str, Any]) -> float:
        """Calculate overall mission confidence score."""
        target_analysis = mission_outcome.get('target_analysis', {})
        reference_analysis = mission_outcome.get('reference_analysis', {})
        coverage_analysis = mission_outcome.get('coverage_analysis', {})
        overall_relevance = mission_outcome.get('overall_relevance', 0.0)
        primary_outcome = mission_outcome.get('primary_outcome', 'mission_failed')

        # For target found scenarios, use higher target weight
        if primary_outcome in ['target_found', 'target_likely']:
            target_confidence = target_analysis.get('confidence', 0.0) * 0.8
            reference_relevance = reference_analysis.get('relevance_score', 0.0) * 0.1
            coverage_factor = coverage_analysis.get('coverage_percentage', 0.0) * 0.1
        else:
            # Standard weights for other scenarios
            target_confidence = target_analysis.get('confidence', 0.0) * 0.5
            reference_relevance = reference_analysis.get('relevance_score', 0.0) * 0.3
            coverage_factor = coverage_analysis.get('coverage_percentage', 0.0) * 0.2

        base_confidence = target_confidence + reference_relevance + coverage_factor

        # Apply overall relevance adjustment (less aggressive for target found)
        if primary_outcome == 'target_found':
            adjusted_confidence = base_confidence * (0.9 + 0.1 * overall_relevance)
        else:
            adjusted_confidence = base_confidence * (0.7 + 0.3 * overall_relevance)

        return max(0.0, min(1.0, adjusted_confidence))
    
    def _refine_partial_correctness_confidence(self, mission_outcome: Dict[str, Any], base_confidence: float) -> float:
        """Refine confidence for partially correct outcomes."""
        primary_outcome = mission_outcome.get('primary_outcome', 'mission_failed')
        
        # Apply outcome-specific adjustments
        if primary_outcome == 'target_likely':
            # High confidence for likely target detection
            return max(base_confidence, 0.7)
        elif primary_outcome == 'reference_items_found':
            # Moderate confidence for reference items
            return max(base_confidence, 0.6)
        elif primary_outcome == 'search_incomplete':
            # Lower confidence for incomplete search
            return min(base_confidence, 0.5)
        
        return base_confidence
    
    def _extract_environmental_factors(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Extract environmental factors that may affect mission outcome."""
        return {
            'weather': context.get('weather_conditions', 'unknown'),
            'lighting': context.get('lighting_conditions', 'unknown'),
            'terrain': context.get('terrain_type', 'unknown'),
            'visibility': context.get('visibility_km', 'unknown'),
            'wind_speed': context.get('wind_speed_mps', 'unknown')
        }
    
    def _add_critical_failure_issues(self, result: ValidationResult, 
                                    interpreted_output: Dict[str, Any], 
                                    match_results: Dict[str, Any], 
                                    context: Dict[str, Any]) -> None:
        """Add critical failure issues to validation result."""
        mission_status = context.get('mission_status', 'unknown')
        
        if interpreted_output.get('error', False):
            result.issues.append({
                'type': 'data_interpretation_error',
                'severity': ValidationSeverity.CRITICAL,
                'message': interpreted_output.get('error_message', 'Data interpretation failed'),
                'details': {'raw_error': interpreted_output.get('error_message')}
            })
        
        if mission_status in ['system_failure', 'emergency_abort', 'communication_lost']:
            result.issues.append({
                'type': 'mission_critical_failure',
                'severity': ValidationSeverity.CRITICAL,
                'message': f'Mission critical failure: {mission_status}',
                'details': {'mission_status': mission_status}
            })
        
        for anomaly in match_results.get('anomalies', []):
            result.issues.append({
                'type': anomaly.get('type', 'unknown_anomaly'),
                'severity': ValidationSeverity.CRITICAL,
                'message': anomaly.get('message', 'Critical anomaly detected'),
                'details': anomaly
            })
    
    def _analyze_mission_performance(self, result: ValidationResult, 
                                   interpreted_output: Dict[str, Any], 
                                   match_results: Dict[str, Any], 
                                   context: Dict[str, Any], 
                                   mission_outcome: Dict[str, Any]) -> None:
        """Analyze detailed mission performance and add issues."""
        # Add performance analysis based on mission outcome
        primary_outcome = mission_outcome.get('primary_outcome', 'mission_failed')
        
        # Add outcome-specific issues
        if primary_outcome == 'no_findings':
            result.issues.append({
                'type': 'no_relevant_findings',
                'severity': ValidationSeverity.HIGH,
                'message': 'No relevant findings despite complete area coverage',
                'details': mission_outcome
            })
        
        # Add environmental impact warnings
        env_factors = mission_outcome.get('environmental_factors', {})
        if env_factors.get('weather') in ['rainy', 'foggy']:
            result.issues.append({
                'type': 'adverse_weather_conditions',
                'severity': ValidationSeverity.MEDIUM,
                'message': f'Adverse weather conditions: {env_factors.get("weather")}',
                'details': env_factors
            })
        
        # Add detection quality warnings
        for warning in match_results.get('warnings', []):
            result.issues.append({
                'type': warning.get('type', 'unknown_warning'),
                'severity': ValidationSeverity.LOW,
                'message': warning.get('message', 'Detection quality warning'),
                'details': warning
            })
    
    def _generate_mission_recommendations(self, result: ValidationResult, 
                                        mission_outcome: Dict[str, Any], 
                                        context: Dict[str, Any]) -> None:
        """Generate mission-specific recommendations."""
        primary_outcome = mission_outcome.get('primary_outcome', 'mission_failed')
        
        if primary_outcome == 'target_found':
            result.recommendations.append("Target successfully located - coordinate with ground team for recovery")
            result.recommendations.append("Document GPS coordinates and approach route for ground team")
        
        elif primary_outcome == 'target_likely':
            result.recommendations.append("High probability target detection - deploy ground team for verification")
            result.recommendations.append("Continue monitoring area while ground team approaches")
        
        elif primary_outcome == 'reference_items_found':
            result.recommendations.append("Reference items found - expand search in surrounding area")
            result.recommendations.append("Analyze item distribution to determine likely movement direction")
        
        elif primary_outcome == 'search_incomplete':
            result.recommendations.append("Continue search in uncovered areas")
            result.recommendations.append("Consider adjusting search pattern based on findings so far")
        
        elif primary_outcome == 'no_findings':
            result.recommendations.append("Expand search area or review search parameters")
            result.recommendations.append("Consider alternative search strategies or additional resources")
        
        # Add environmental recommendations
        env_factors = mission_outcome.get('environmental_factors', {})
        if env_factors.get('weather') in ['rainy', 'foggy']:
            result.recommendations.append("Consider postponing search until weather conditions improve")
        
        if env_factors.get('lighting') == 'night':
            result.recommendations.append("Deploy thermal imaging or infrared sensors for night search")
    
    def supports_partial_correctness(self) -> bool:
        """Check if validator supports 'Partially Correct' feedback type."""
        return True
    
    def get_validation_criteria(self) -> Dict[str, Any]:
        """Get the validation criteria used by this validator."""
        return self.validation_criteria.copy()
    
    def update_mission_thresholds(self, category: str, thresholds: Dict[str, Any]) -> None:
        """Update mission success thresholds."""
        if category in self.validation_criteria:
            self.validation_criteria[category].update(thresholds)
            self.logger.info(f"Updated mission thresholds for {category}")
        else:
            self.logger.warning(f"Unknown threshold category: {category}")

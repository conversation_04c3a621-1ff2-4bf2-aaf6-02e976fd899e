@echo off
REM 🔒 EcoStamp Security Setup Script for Windows
REM Enterprise-grade security scanning for solo developers

echo 🔒 EcoStamp Security Setup Starting...
echo ==================================================

REM Check if we're in the right directory
if not exist "source\package.json" (
    echo ❌ Please run this script from the EcoStamp root directory
    pause
    exit /b 1
)

echo ℹ️  Setting up security scanning for EcoStamp...

REM Navigate to source directory
cd source

REM Install dependencies
echo ℹ️  Installing security dependencies...
call npm install

if %errorlevel% neq 0 (
    echo ❌ Failed to install dependencies
    pause
    exit /b 1
)

echo ✅ Dependencies installed successfully

REM Run initial security scan
echo ℹ️  Running initial security scan...

echo.
echo 🔍 NPM Audit Scan:
call npm run security:audit

echo.
echo 📜 License Compliance Check:
call npm run security:licenses

echo.
echo 📦 Generating SBOM...
call npm run security:sbom

echo.
echo 🔒 Security Linting...
call npm run security:eslint

echo.
echo 📊 Generating Security Report...
call npm run security:report

echo.
echo ==================================================
echo ✅ Security setup complete!
echo ==================================================

echo.
echo ℹ️  Available Security Commands:
echo   npm run security:audit          - Quick vulnerability scan
echo   npm run security:licenses       - License compliance check
echo   npm run security:sbom          - Generate SBOM
echo   npm run security:eslint        - Security linting
echo   npm run security:full-scan     - Complete security analysis
echo   npm run security:report        - Generate detailed report

echo.
echo ℹ️  Security Dashboard:
echo   Open: security-dashboard.html in your browser

echo.
echo ℹ️  Documentation:
echo   Read: ..\SECURITY.md for complete guide

echo.
echo ⚠️  Recommendations:
echo   • Run 'npm run security:full-scan' weekly
echo   • Check 'npm run security:report' for detailed analysis
echo   • Review SECURITY.md for best practices
echo   • Set up GitHub Actions for automated scanning

echo.
echo ✅ 🌱 EcoStamp is now secured with enterprise-grade protection!

pause

import axios from 'axios';
import { config } from '../config/index.js';
import { logger, logRequest, logResponse } from '../utils/logger.js';
import { RateLimiterMemory } from 'rate-limiter-flexible';

class GeminiClient {
  constructor() {
    this.baseURL = config.apis.gemini.baseURL;
    this.apiKey = config.apis.gemini.apiKey;
    
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: 60000
    });

    // Rate limiter
    this.rateLimiter = new RateLimiterMemory({
      keyGenerator: () => 'gemini',
      points: config.rateLimiting.requestsPerMinute,
      duration: 60,
      blockDuration: 60
    });
  }

  async rateLimit() {
    try {
      await this.rateLimiter.consume('gemini');
    } catch (rejRes) {
      const waitTime = Math.round(rejRes.msBeforeNext / 1000);
      logger.warn(`Gemini rate limit hit, waiting ${waitTime} seconds`);
      await new Promise(resolve => setTimeout(resolve, rejRes.msBeforeNext));
    }
  }

  async generateCompletion(messages, options = {}) {
    await this.rateLimit();
    logRequest('Gemini', 'generateCompletion', { 
      messageCount: messages.length,
      model: options.model || config.apis.gemini.model
    });

    try {
      // Convert messages to Gemini format
      const geminiContent = this.convertMessagesToGeminiFormat(messages);
      
      const response = await this.client.post(
        `/v1beta/models/${options.model || config.apis.gemini.model}:generateContent?key=${this.apiKey}`,
        {
          contents: geminiContent,
          generationConfig: {
            temperature: options.temperature || 0.7,
            maxOutputTokens: options.maxTokens || 4096,
            topP: options.topP || 0.8,
            topK: options.topK || 40
          }
        }
      );

      logResponse('Gemini', 'generateCompletion', true);
      return response.data.candidates[0].content.parts[0].text;
    } catch (error) {
      logResponse('Gemini', 'generateCompletion', false, { error: error.message });
      throw error;
    }
  }

  async analyzeThreads(mergedThreads, task = 'code_generation', options = {}) {
    await this.rateLimit();
    logRequest('Gemini', 'analyzeThreads', { 
      task,
      threadsLength: mergedThreads.length
    });

    try {
      const prompt = this.formatThreadsForAnalysis(mergedThreads, task);
      
      const response = await this.client.post(
        `/v1beta/models/${options.model || config.apis.gemini.model}:generateContent?key=${this.apiKey}`,
        {
          contents: [
            {
              parts: [
                {
                  text: prompt
                }
              ]
            }
          ],
          generationConfig: {
            temperature: options.temperature || 0.3,
            maxOutputTokens: options.maxTokens || 4096,
            topP: 0.8,
            topK: 40
          }
        }
      );

      logResponse('Gemini', 'analyzeThreads', true);
      return {
        analysis: response.data.candidates[0].content.parts[0].text,
        task,
        timestamp: new Date().toISOString(),
        model: options.model || config.apis.gemini.model
      };
    } catch (error) {
      logResponse('Gemini', 'analyzeThreads', false, { error: error.message });
      throw error;
    }
  }

  convertMessagesToGeminiFormat(messages) {
    const contents = [];
    
    messages.forEach(message => {
      if (message.role === 'system') {
        // Gemini doesn't have a system role, so we'll prepend it to the first user message
        return;
      }
      
      contents.push({
        role: message.role === 'assistant' ? 'model' : 'user',
        parts: [
          {
            text: message.content
          }
        ]
      });
    });

    return contents;
  }

  formatThreadsForAnalysis(mergedThreads, task) {
    let prompt = this.getSystemPromptForTask(task);
    prompt += `\n\n# Merged Thread Analysis Request\n\n`;
    prompt += `**Task**: ${task}\n`;
    prompt += `**Number of threads**: ${mergedThreads.length}\n`;
    prompt += `**Analysis timestamp**: ${new Date().toISOString()}\n\n`;

    prompt += `## Thread Contents\n\n`;

    mergedThreads.forEach((thread, index) => {
      prompt += `### Thread ${index + 1}: ${thread.source} (${thread.id})\n`;
      prompt += `**Created**: ${thread.created_at}\n`;
      prompt += `**Messages**: ${thread.messages.length}\n\n`;

      thread.messages.forEach((message, msgIndex) => {
        prompt += `**Message ${msgIndex + 1}** (${message.role}):\n`;
        prompt += `${message.content}\n\n`;
      });

      if (thread.highlights && thread.highlights.length > 0) {
        prompt += `**Key Highlights**:\n`;
        thread.highlights.forEach(highlight => {
          prompt += `- ${highlight}\n`;
        });
        prompt += `\n`;
      }

      prompt += `---\n\n`;
    });

    prompt += `## Analysis Request\n\n`;
    prompt += `Please analyze the above merged threads and provide insights based on the specified task. `;
    prompt += `Focus on extracting actionable technical information and generating practical solutions.`;

    return prompt;
  }

  getSystemPromptForTask(task) {
    const prompts = {
      code_generation: `You are an expert software developer and code analyst. You will receive merged conversation threads from ChatGPT and Perplexity discussions. Your task is to:

1. Analyze the conversations to understand the technical requirements and context
2. Identify key patterns, solutions, and approaches discussed
3. Generate clean, well-documented, production-ready code based on the insights
4. Provide implementation recommendations and best practices
5. Highlight any potential issues or considerations

Focus on practical, actionable code solutions that incorporate the best ideas from the discussions.`,

      code_analysis: `You are an expert code reviewer and software architect. You will receive merged conversation threads containing code discussions. Your task is to:

1. Analyze the code and technical discussions thoroughly
2. Identify strengths, weaknesses, and potential improvements
3. Suggest architectural improvements and optimizations
4. Point out security considerations and best practices
5. Provide actionable recommendations for code quality

Be thorough but practical in your analysis.`,

      summarization: `You are an expert technical writer and analyst. You will receive merged conversation threads from various AI assistants. Your task is to:

1. Extract and synthesize the key technical insights
2. Identify common themes and solutions across discussions
3. Create a comprehensive summary of findings
4. Highlight actionable next steps and recommendations
5. Organize information in a clear, structured format

Focus on creating value by connecting insights across different conversations.`
    };

    return prompts[task] || prompts.code_generation;
  }
}

export default GeminiClient;

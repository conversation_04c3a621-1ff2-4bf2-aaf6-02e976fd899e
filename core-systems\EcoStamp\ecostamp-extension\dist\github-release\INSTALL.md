# 🚀 EcoStamp Installation Guide

## **Quick Install (Recommended)**

### **Chrome Web Store** ⭐
1. **[Click here to install from Chrome Web Store](https://chrome.google.com/webstore)** 
2. Click **"Add to Chrome"**
3. Click **"Add Extension"** in the popup
4. **Done!** Visit any AI platform and see EcoStamp in action

---

## **Manual Installation**

### **From GitHub Release** 📦
1. **[Download the latest release](https://github.com/ecostamp/ecostamp-extension/releases/latest)**
2. **Extract the ZIP file** to a folder
3. **Open Chrome** and go to `chrome://extensions/`
4. **Enable "Developer mode"** (toggle in top-right corner)
5. **Click "Load unpacked"**
6. **Select the extracted folder**
7. **EcoStamp is now installed!** 🎉

### **From Source Code** 🛠️
```bash
git clone https://github.com/ecostamp/ecostamp-extension.git
cd ecostamp-extension
```
Then follow steps 3-7 above.

---

## **Verification**

After installation, you should see:
- ✅ **EcoStamp icon** in your browser toolbar
- ✅ **Green badge** showing interaction count
- ✅ **EcoStamp footers** appearing below AI responses

### **Test It Out**
1. Visit **ChatGPT**, **Claude**, or **Gemini**
2. Ask any question (e.g., "What is machine learning?")
3. Look for the **EcoStamp footer** below the AI response:

```
──────────────────────────────────────────────
🕓 01/02/2025, 03:45:00 UTC  |  🔐 SHA-256: a1b2...c3d4
🌿 Eco-Level: 3/5 Leaves 🌿🌿🌿🍂🍂  (0.45 Wh · 12.8 mL)
Powered by EcoStamp — GitHub              ChatGPT • gpt-4
```

---

## **Supported Browsers**

- ✅ **Chrome** 88+ (Recommended)
- ✅ **Microsoft Edge** 88+
- ✅ **Brave Browser**
- ✅ **Opera** (Chromium-based)
- ⚠️ **Firefox** (Manual conversion required)
- ⚠️ **Safari** (Manual conversion required)

---

## **Supported AI Platforms**

EcoStamp automatically works with:

- ✅ **ChatGPT** (chat.openai.com, chatgpt.com)
- ✅ **Claude** (claude.ai)
- ✅ **Gemini** (gemini.google.com, bard.google.com)
- ✅ **Perplexity** (perplexity.ai)
- ✅ **Poe** (poe.com)
- ✅ **Character.AI** (character.ai)
- ✅ **You.com** (you.com)
- ✅ **Hugging Face** (huggingface.co)
- ✅ **Any AI Platform** (universal detection)

---

## **Troubleshooting**

### **Extension not working?**
1. **Refresh the AI platform page**
2. **Check if extension is enabled**: Click the EcoStamp icon
3. **Try a different AI platform**
4. **Check browser console** for error messages

### **No footers appearing?**
1. **Ask a longer question** (minimum 50 characters)
2. **Wait a few seconds** for processing
3. **Check extension permissions** in `chrome://extensions/`

### **Manual trigger:**
Open browser console (F12) and run:
```javascript
ecoStamp.process()
```

---

## **Privacy & Permissions**

EcoStamp only requests:
- ✅ **activeTab** - To detect AI responses on current tab
- ✅ **storage** - To save your statistics locally

**We do NOT:**
- ❌ Collect your personal data
- ❌ Send data to external servers
- ❌ Track your browsing history
- ❌ Access your AI conversations

---

## **Need Help?**

- 📖 **[Full Documentation](https://ecostamp.github.io/docs)**
- 🐛 **[Report Issues](https://github.com/ecostamp/ecostamp-extension/issues)**
- 💬 **[Community Support](https://github.com/ecostamp/ecostamp-extension/discussions)**
- 📧 **[Contact Us](mailto:<EMAIL>)**

---

**🌱 Welcome to EcoStamp! Start tracking your AI environmental impact today.**

@echo off
setlocal enabledelayedexpansion

:: AI Orchestration System Installer
:: Complete setup and installation for the AI Orchestration System

title AI Orchestration System - Installer

echo.
echo ========================================
echo    🤖 AI Orchestration System
echo    Complete Installation & Setup
echo ========================================
echo.
echo This installer will set up the complete
echo AI Orchestration System with all components.
echo.
echo Components included:
echo - Meta Orchestrator
echo - Universal Orchestrator  
echo - DGM (Dynamic Goal Management)
echo - Workflow Automation
echo - Cross-Flow Engine
echo - Configuration Tools
echo.
echo Press any key to start installation...
pause >nul

:: Check Node.js
echo.
echo [1/8] Checking Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ ERROR: Node.js is required but not found
    echo.
    echo Please install Node.js from: https://nodejs.org
    echo Then restart this installer.
    echo.
    pause
    exit /b 1
)

echo ✅ Node.js found and working

:: Check npm
echo.
echo [2/8] Checking npm...
npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ ERROR: npm is required but not found
    echo.
    pause
    exit /b 1
)

echo ✅ npm found and working

:: Install dependencies
echo.
echo [3/8] Installing dependencies...
echo This may take a few minutes...
echo.
npm install
if errorlevel 1 (
    echo ❌ ERROR: Failed to install dependencies
    echo.
    pause
    exit /b 1
)
echo ✅ Dependencies installed successfully

:: Setup Meta Orchestrator
echo.
echo [4/8] Setting up Meta Orchestrator...
if exist "meta-orchestrator\setup.js" (
    cd meta-orchestrator
    node setup.js
    cd ..
    echo ✅ Meta Orchestrator configured
) else (
    echo ⚠️ Meta Orchestrator setup script not found
)

:: Setup DGM
echo.
echo [5/8] Setting up DGM (Dynamic Goal Management)...
if exist "dgm\index.js" (
    echo ✅ DGM system ready
) else (
    echo ⚠️ DGM system not found
)

:: Setup Universal Orchestrator
echo.
echo [6/8] Setting up Universal Orchestrator...
if exist "universal-orchestrator\index.js" (
    echo ✅ Universal Orchestrator ready
) else (
    echo ⚠️ Universal Orchestrator not found
)

:: Create configuration
echo.
echo [7/8] Creating configuration files...

:: Create main config if it doesn't exist
if not exist "config\main-config.json" (
    mkdir config 2>nul
    echo {> config\main-config.json
    echo   "version": "1.0.0",>> config\main-config.json
    echo   "environment": "production",>> config\main-config.json
    echo   "orchestrators": {>> config\main-config.json
    echo     "meta": {>> config\main-config.json
    echo       "enabled": true,>> config\main-config.json
    echo       "port": 3001>> config\main-config.json
    echo     },>> config\main-config.json
    echo     "universal": {>> config\main-config.json
    echo       "enabled": true,>> config\main-config.json
    echo       "port": 3002>> config\main-config.json
    echo     },>> config\main-config.json
    echo     "dgm": {>> config\main-config.json
    echo       "enabled": true,>> config\main-config.json
    echo       "port": 3003>> config\main-config.json
    echo     }>> config\main-config.json
    echo   },>> config\main-config.json
    echo   "logging": {>> config\main-config.json
    echo     "level": "info",>> config\main-config.json
    echo     "file": "logs/orchestration.log">> config\main-config.json
    echo   }>> config\main-config.json
    echo }>> config\main-config.json
    echo ✅ Main configuration created
) else (
    echo ✅ Configuration already exists
)

:: Create logs directory
mkdir logs 2>nul

:: Run system test
echo.
echo [8/8] Running system test...
node orchestrator.js --test
if errorlevel 1 (
    echo ⚠️ System test completed with warnings
) else (
    echo ✅ System test passed
)

:: Installation complete
echo.
echo ========================================
echo    🎉 Installation Complete!
echo ========================================
echo.
echo The AI Orchestration System is now ready.
echo.
echo Available components:
echo ✅ Meta Orchestrator (Port 3001)
echo ✅ Universal Orchestrator (Port 3002)  
echo ✅ DGM System (Port 3003)
echo ✅ Workflow Automation
echo ✅ Cross-Flow Engine
echo.
echo What would you like to do next?
echo.
echo 1. Start All Orchestrators
echo 2. Start Individual Components
echo 3. View Documentation
echo 4. Run Configuration Wizard
echo 5. Exit
echo.
set /p choice="Enter your choice (1-5): "

if "%choice%"=="1" goto start_all
if "%choice%"=="2" goto start_individual
if "%choice%"=="3" goto view_docs
if "%choice%"=="4" goto config_wizard
if "%choice%"=="5" goto end

goto end

:start_all
echo.
echo Starting all orchestrators...
echo.
echo Starting Meta Orchestrator on port 3001...
start "Meta Orchestrator" cmd /k "cd meta-orchestrator && node index.js"
timeout /t 2 /nobreak >nul

echo Starting Universal Orchestrator on port 3002...
start "Universal Orchestrator" cmd /k "cd universal-orchestrator && node index.js"
timeout /t 2 /nobreak >nul

echo Starting DGM System on port 3003...
start "DGM System" cmd /k "cd dgm && node index.js"
timeout /t 2 /nobreak >nul

echo Starting Main Orchestrator...
start "Main Orchestrator" cmd /k "node orchestrator.js"

echo.
echo ✅ All orchestrators started!
echo.
echo Access points:
echo - Main Dashboard: http://localhost:3000
echo - Meta Orchestrator: http://localhost:3001
echo - Universal Orchestrator: http://localhost:3002
echo - DGM System: http://localhost:3003
echo.
pause
goto end

:start_individual
echo.
echo Individual Component Startup
echo.
echo 1. Meta Orchestrator
echo 2. Universal Orchestrator
echo 3. DGM System
echo 4. Main Orchestrator
echo 5. Workflow Automation
echo 6. Back to main menu
echo.
set /p comp="Select component (1-6): "

if "%comp%"=="1" (
    echo Starting Meta Orchestrator...
    start "Meta Orchestrator" cmd /k "cd meta-orchestrator && node index.js"
)
if "%comp%"=="2" (
    echo Starting Universal Orchestrator...
    start "Universal Orchestrator" cmd /k "cd universal-orchestrator && node index.js"
)
if "%comp%"=="3" (
    echo Starting DGM System...
    start "DGM System" cmd /k "cd dgm && node index.js"
)
if "%comp%"=="4" (
    echo Starting Main Orchestrator...
    start "Main Orchestrator" cmd /k "node orchestrator.js"
)
if "%comp%"=="5" (
    echo Starting Workflow Automation...
    start "Workflow Automation" cmd /k "cd scripts && node workflow-automation.js"
)
if "%comp%"=="6" goto end

echo.
echo Component started successfully!
echo.
pause
goto end

:view_docs
echo.
echo Opening documentation...
echo.
if exist "README.md" (
    start notepad README.md
) else (
    echo Creating quick documentation...
    echo # AI Orchestration System > README.md
    echo. >> README.md
    echo ## Quick Start >> README.md
    echo 1. Run AI-Orchestration-Installer.bat >> README.md
    echo 2. Choose "Start All Orchestrators" >> README.md
    echo 3. Access http://localhost:3000 >> README.md
    echo. >> README.md
    echo ## Components >> README.md
    echo - Meta Orchestrator: Advanced AI coordination >> README.md
    echo - Universal Orchestrator: Cross-platform AI management >> README.md
    echo - DGM: Dynamic Goal Management system >> README.md
    echo - Workflow Automation: Automated task processing >> README.md
    echo - Cross-Flow Engine: Multi-AI workflow coordination >> README.md
    start notepad README.md
)

if exist "dgm\README.md" (
    echo Opening DGM documentation...
    start notepad "dgm\README.md"
)

if exist "meta-orchestrator\README.md" (
    echo Opening Meta Orchestrator documentation...
    start notepad "meta-orchestrator\README.md"
)

echo.
echo Documentation opened in Notepad
echo.
pause
goto end

:config_wizard
echo.
echo Configuration Wizard
echo.
echo This will help you customize the AI Orchestration System
echo.
echo Current configuration:
type config\main-config.json
echo.
echo.
echo Would you like to modify the configuration? (y/n)
set /p modify="Enter choice: "

if /i "%modify%"=="y" (
    echo.
    echo Opening configuration file for editing...
    start notepad config\main-config.json
    echo.
    echo Save the file and restart the orchestrators to apply changes.
    echo.
)

pause
goto end

:end
echo.
echo ========================================
echo    AI Orchestration System Ready
echo ========================================
echo.
echo For support and documentation:
echo - GitHub: https://github.com/chris-ai-dev/Time_Stamp_Project
echo - Local docs: README.md files in each component
echo.
echo Thank you for using AI Orchestration System!
echo.
pause
exit /b 0

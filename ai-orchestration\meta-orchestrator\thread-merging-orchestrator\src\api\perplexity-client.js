import axios from 'axios';
import { config } from '../config/index.js';
import { logger, logRequest, logResponse } from '../utils/logger.js';
import { RateLimiterMemory } from 'rate-limiter-flexible';

class PerplexityClient {
  constructor() {
    this.baseURL = config.apis.perplexity.baseURL;
    this.apiKey = config.apis.perplexity.apiKey;
    
    this.client = axios.create({
      baseURL: this.baseURL,
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json'
      },
      timeout: 30000
    });

    // Rate limiter
    this.rateLimiter = new RateLimiterMemory({
      keyGenerator: () => 'perplexity',
      points: config.rateLimiting.requestsPerMinute,
      duration: 60,
      blockDuration: 60
    });
  }

  async rateLimit() {
    try {
      await this.rateLimiter.consume('perplexity');
    } catch (rejRes) {
      const waitTime = Math.round(rejRes.msBeforeNext / 1000);
      logger.warn(`Perplexity rate limit hit, waiting ${waitTime} seconds`);
      await new Promise(resolve => setTimeout(resolve, rejRes.msBeforeNext));
    }
  }

  async listConversations(limit = 20) {
    await this.rateLimit();
    logRequest('Perplexity', 'listConversations', { limit });

    try {
      // Note: Perplexity API might not have direct conversation listing
      // This is a placeholder implementation that would need to be adapted
      // based on actual Perplexity API capabilities
      
      const response = await this.client.get('/conversations', {
        params: { limit }
      });

      logResponse('Perplexity', 'listConversations', true);
      return response.data;
    } catch (error) {
      // If API doesn't support this, we might need browser automation
      if (error.response?.status === 404) {
        logger.warn('Perplexity conversation listing not available via API, consider browser automation');
        return { conversations: [], requiresBrowserAutomation: true };
      }
      
      logResponse('Perplexity', 'listConversations', false, { error: error.message });
      throw error;
    }
  }

  async getConversation(conversationId) {
    await this.rateLimit();
    logRequest('Perplexity', 'getConversation', { conversationId });

    try {
      const response = await this.client.get(`/conversations/${conversationId}`);
      
      logResponse('Perplexity', 'getConversation', true);
      return {
        id: conversationId,
        messages: response.data.messages || [],
        created_at: response.data.created_at,
        source: 'perplexity'
      };
    } catch (error) {
      logResponse('Perplexity', 'getConversation', false, { error: error.message });
      throw error;
    }
  }

  async searchConversations(query, limit = 10) {
    await this.rateLimit();
    logRequest('Perplexity', 'searchConversations', { query, limit });

    try {
      const response = await this.client.post('/search', {
        query,
        limit,
        include_conversations: true
      });

      logResponse('Perplexity', 'searchConversations', true);
      return response.data;
    } catch (error) {
      logResponse('Perplexity', 'searchConversations', false, { error: error.message });
      throw error;
    }
  }

  async generateCompletion(messages, options = {}) {
    await this.rateLimit();
    logRequest('Perplexity', 'generateCompletion', { 
      messageCount: messages.length,
      model: options.model || config.apis.perplexity.model
    });

    try {
      const response = await this.client.post('/chat/completions', {
        model: options.model || config.apis.perplexity.model,
        messages,
        max_tokens: options.maxTokens || 4096,
        temperature: options.temperature || 0.7,
        stream: false,
        ...options
      });

      logResponse('Perplexity', 'generateCompletion', true);
      return response.data.choices[0].message.content;
    } catch (error) {
      logResponse('Perplexity', 'generateCompletion', false, { error: error.message });
      throw error;
    }
  }

  async askQuestion(question, options = {}) {
    await this.rateLimit();
    logRequest('Perplexity', 'askQuestion', { question: question.substring(0, 100) + '...' });

    try {
      const response = await this.client.post('/chat/completions', {
        model: config.apis.perplexity.model,
        messages: [
          {
            role: 'user',
            content: question
          }
        ],
        max_tokens: options.maxTokens || 4096,
        temperature: options.temperature || 0.7,
        stream: false
      });

      logResponse('Perplexity', 'askQuestion', true);
      return {
        answer: response.data.choices[0].message.content,
        sources: response.data.citations || [],
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      logResponse('Perplexity', 'askQuestion', false, { error: error.message });
      throw error;
    }
  }
}

export default PerplexityClient;

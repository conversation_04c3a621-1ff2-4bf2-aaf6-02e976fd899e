@echo off
echo.
echo ========================================
echo  Augment Code: AI Orchestration Platform
echo ========================================
echo.
echo Starting the Unified AI Orchestration System...
echo.

cd /d "%~dp0"

echo [1/3] Checking dependencies...
if not exist "node_modules" (
    echo Installing server dependencies...
    npm install
)

if not exist "client\node_modules" (
    echo Installing client dependencies...
    cd client
    npm install
    cd ..
)

echo [2/3] Generating Prisma client...
npx prisma generate

echo [3/3] Starting development servers...
echo.
echo Frontend will be available at: http://localhost:3000
echo Backend API will be available at: http://localhost:3001/api
echo.
echo Press Ctrl+C to stop the servers
echo.

npm run dev

pause

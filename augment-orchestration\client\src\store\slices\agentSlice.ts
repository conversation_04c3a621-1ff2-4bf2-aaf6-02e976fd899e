import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import { Agent, AgentRole } from '../../types'
import { agent<PERSON>pi } from '../../services/api'

interface AgentState {
  agents: Agent[]
  roles: AgentRole[]
  registry: any
  isLoading: boolean
  error: string | null
}

const initialState: AgentState = {
  agents: [],
  roles: [],
  registry: null,
  isLoading: false,
  error: null,
}

export const fetchAgents = createAsyncThunk(
  'agent/fetchAll',
  async () => {
    const response = await agentApi.getAll()
    return response.data
  }
)

export const fetchAgentRegistry = createAsyncThunk(
  'agent/fetchRegistry',
  async () => {
    const response = await agentApi.getRegistry()
    return response.data
  }
)

const agentSlice = createSlice({
  name: 'agent',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchAgents.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(fetchAgents.fulfilled, (state, action) => {
        state.isLoading = false
        state.agents = action.payload
      })
      .addCase(fetchAgents.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.error.message || 'Failed to fetch agents'
      })
      .addCase(fetchAgentRegistry.fulfilled, (state, action) => {
        state.registry = action.payload
        state.roles = action.payload.roles || []
      })
  },
})

export const { clearError } = agentSlice.actions
export default agentSlice.reducer

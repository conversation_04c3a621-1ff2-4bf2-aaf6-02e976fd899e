"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = globalSetup;
const client_1 = require("@prisma/client");
async function globalSetup() {
    console.log('🚀 Setting up test environment...');
    // Set test environment variables
    process.env.NODE_ENV = 'test';
    process.env.JWT_SECRET = 'test-jwt-secret-for-testing-only';
    process.env.DATABASE_URL = process.env.TEST_DATABASE_URL || 'postgresql://test:test@localhost:5432/augment_orchestration_test';
    process.env.PORT = '3002'; // Use different port for tests
    process.env.CORS_ORIGIN = 'http://localhost:3000';
    // Initialize test database if needed
    if (process.env.USE_TEST_DATABASE === 'true') {
        const prisma = new client_1.PrismaClient({
            datasources: {
                db: {
                    url: process.env.DATABASE_URL,
                },
            },
        });
        try {
            // Test database connection
            await prisma.$connect();
            console.log('✅ Test database connection established');
            // Clean up any existing test data
            await cleanupTestData(prisma);
            console.log('✅ Test database cleaned');
            // Seed test data if needed
            await seedTestData(prisma);
            console.log('✅ Test data seeded');
        }
        catch (error) {
            console.error('❌ Failed to setup test database:', error);
            throw error;
        }
        finally {
            await prisma.$disconnect();
        }
    }
    console.log('✅ Test environment setup complete');
}
async function cleanupTestData(prisma) {
    // Clean up in reverse dependency order
    await prisma.auditLog.deleteMany({ where: { userId: { startsWith: 'test-' } } });
    await prisma.workflowExecution.deleteMany({ where: { id: { startsWith: 'test-' } } });
    await prisma.workflowTemplate.deleteMany({ where: { id: { startsWith: 'test-' } } });
    await prisma.sharedContext.deleteMany({ where: { id: { startsWith: 'test-' } } });
    await prisma.comment.deleteMany({ where: { userId: { startsWith: 'test-' } } });
    await prisma.notification.deleteMany({ where: { userId: { startsWith: 'test-' } } });
    await prisma.agent.deleteMany({ where: { id: { startsWith: 'test-' } } });
    await prisma.userRole.deleteMany({ where: { userId: { startsWith: 'test-' } } });
    await prisma.user.deleteMany({ where: { id: { startsWith: 'test-' } } });
    await prisma.role.deleteMany({ where: { id: { startsWith: 'test-' } } });
}
async function seedTestData(prisma) {
    // Create test roles
    const testRole = await prisma.role.create({
        data: {
            id: 'test-role-admin',
            name: 'Test Admin',
            description: 'Test administrator role',
            hierarchy: 100,
            isSystemRole: false,
            permissions: [
                {
                    id: 'test-perm-1',
                    name: 'agents:*',
                    resource: 'agents',
                    action: '*',
                },
                {
                    id: 'test-perm-2',
                    name: 'workflows:*',
                    resource: 'workflows',
                    action: '*',
                },
            ],
        },
    });
    // Create test user
    const testUser = await prisma.user.create({
        data: {
            id: 'test-user-admin',
            username: 'testadmin',
            email: '<EMAIL>',
            password: '$2b$10$test.hashed.password', // bcrypt hash of 'testpassword'
            isActive: true,
        },
    });
    // Assign role to user
    await prisma.userRole.create({
        data: {
            userId: testUser.id,
            roleId: testRole.id,
        },
    });
    // Create test agent
    await prisma.agent.create({
        data: {
            id: 'test-agent-executor',
            name: 'Test Code Executor',
            description: 'Test agent for code execution',
            role: 'Code Executor',
            capabilities: ['code_execution', 'testing', 'debugging'],
            parameters: {
                maxConcurrentTasks: 5,
                timeout: 30000,
                retryAttempts: 3,
            },
            isActive: true,
            currentLoad: 0,
            maxConcurrentTasks: 5,
            performanceMetrics: {
                successRate: 0.95,
                avgResponseTime: 1500,
                taskCompletionRate: 0.92,
                errorRate: 0.05,
            },
        },
    });
    // Create test workflow
    await prisma.workflowTemplate.create({
        data: {
            id: 'test-workflow-basic',
            name: 'Test Basic Workflow',
            description: 'Basic test workflow for integration testing',
            definition: {
                stages: [
                    {
                        id: 'test-stage-1',
                        name: 'Initialize',
                        agentRole: 'Code Executor',
                        parameters: { task: 'initialize', timeout: 10000 },
                        dependencies: [],
                    },
                    {
                        id: 'test-stage-2',
                        name: 'Process',
                        agentRole: 'Data Analyst',
                        parameters: { task: 'process', timeout: 15000 },
                        dependencies: ['test-stage-1'],
                    },
                ],
            },
            isActive: true,
        },
    });
    // Create test context
    await prisma.sharedContext.create({
        data: {
            id: 'test-context-global',
            name: 'Test Global Context',
            type: 'GLOBAL',
            data: {
                environment: 'test',
                version: '1.0.0',
                features: {
                    evolutionEngine: true,
                    contextProtocol: true,
                    collaboration: true,
                },
            },
            priority: 10,
            tags: ['test', 'global', 'configuration'],
            accessLevel: 'PUBLIC',
        },
    });
    console.log('✅ Test data seeded successfully');
}

import React, { useCallback, useEffect, useState } from 'react';
import React<PERSON><PERSON>, {
  Node,
  Edge,
  addEdge,
  Connection,
  useNodesState,
  useEdgesState,
  Controls,
  Background,
  MiniMap,
  Panel,
  NodeTypes,
  EdgeTypes,
} from 'reactflow';
import 'reactflow/dist/style.css';
import {
  Box,
  Paper,
  Typography,
  Chip,
  Avatar,
  IconButton,
  Tooltip,
  Card,
  CardContent,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  AccountTree,
  Psychology,
  Computer,
  CloudQueue,
  Security,
  Speed,
  Visibility,
  VisibilityOff,
  Add,
  Edit,
  Delete,
  Info,
} from '@mui/icons-material';
import { FamilyTreeNode, Agent, MetaOrchestrator, SubOrchestrator } from '@/shared/types';

// Custom node components
const MetaOrchestratorNode = ({ data }: { data: any }) => (
  <Card sx={{ minWidth: 200, border: '2px solid #1976d2', backgroundColor: '#e3f2fd' }}>
    <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
      <Box display="flex" alignItems="center" gap={1} mb={1}>
        <Avatar sx={{ bgcolor: '#1976d2', width: 32, height: 32 }}>
          <AccountTree />
        </Avatar>
        <Typography variant="h6" component="div">
          {data.name}
        </Typography>
      </Box>
      <Typography variant="body2" color="text.secondary" mb={1}>
        Meta-Orchestrator
      </Typography>
      <Box display="flex" gap={0.5} flexWrap="wrap">
        <Chip label={`${data.subOrchestrators?.length || 0} Sub-Orchestrators`} size="small" />
        <Chip label={`${data.agents?.length || 0} Agents`} size="small" />
      </Box>
      <Box mt={1} display="flex" gap={0.5}>
        <Tooltip title="View Details">
          <IconButton size="small" onClick={() => data.onViewDetails?.(data)}>
            <Info />
          </IconButton>
        </Tooltip>
        <Tooltip title="Edit">
          <IconButton size="small" onClick={() => data.onEdit?.(data)}>
            <Edit />
          </IconButton>
        </Tooltip>
      </Box>
    </CardContent>
  </Card>
);

const SubOrchestratorNode = ({ data }: { data: any }) => (
  <Card sx={{ minWidth: 180, border: '2px solid #388e3c', backgroundColor: '#e8f5e8' }}>
    <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
      <Box display="flex" alignItems="center" gap={1} mb={1}>
        <Avatar sx={{ bgcolor: '#388e3c', width: 28, height: 28 }}>
          <CloudQueue />
        </Avatar>
        <Typography variant="subtitle1" component="div">
          {data.name}
        </Typography>
      </Box>
      <Typography variant="body2" color="text.secondary" mb={1}>
        {data.domain}
      </Typography>
      <Box display="flex" gap={0.5} flexWrap="wrap">
        <Chip label={`${data.agents?.length || 0} Agents`} size="small" />
        <Chip 
          label={data.isActive ? 'Active' : 'Inactive'} 
          size="small" 
          color={data.isActive ? 'success' : 'default'}
        />
      </Box>
      <Box mt={1} display="flex" gap={0.5}>
        <Tooltip title="View Details">
          <IconButton size="small" onClick={() => data.onViewDetails?.(data)}>
            <Info />
          </IconButton>
        </Tooltip>
        <Tooltip title="Edit">
          <IconButton size="small" onClick={() => data.onEdit?.(data)}>
            <Edit />
          </IconButton>
        </Tooltip>
      </Box>
    </CardContent>
  </Card>
);

const AgentNode = ({ data }: { data: any }) => {
  const getAgentIcon = (roles: string[]) => {
    if (roles.includes('security-auditor')) return <Security />;
    if (roles.includes('performance-engineer')) return <Speed />;
    if (roles.includes('devops-engineer')) return <CloudQueue />;
    if (roles.includes('data-scientist')) return <Psychology />;
    return <Computer />;
  };

  const getAgentColor = (fitnessScore: number) => {
    if (fitnessScore >= 80) return '#4caf50';
    if (fitnessScore >= 60) return '#ff9800';
    return '#f44336';
  };

  return (
    <Card sx={{ 
      minWidth: 160, 
      border: `2px solid ${getAgentColor(data.fitnessScore)}`, 
      backgroundColor: '#fafafa' 
    }}>
      <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
        <Box display="flex" alignItems="center" gap={1} mb={1}>
          <Avatar sx={{ 
            bgcolor: getAgentColor(data.fitnessScore), 
            width: 24, 
            height: 24 
          }}>
            {getAgentIcon(data.roles)}
          </Avatar>
          <Typography variant="subtitle2" component="div">
            {data.name}
          </Typography>
        </Box>
        <Typography variant="caption" color="text.secondary" mb={1}>
          {data.vendor} • Score: {data.fitnessScore}
        </Typography>
        <Box display="flex" gap={0.5} flexWrap="wrap" mb={1}>
          {data.roles.slice(0, 2).map((role: string) => (
            <Chip key={role} label={role} size="small" variant="outlined" />
          ))}
          {data.roles.length > 2 && (
            <Chip label={`+${data.roles.length - 2}`} size="small" variant="outlined" />
          )}
        </Box>
        <Box display="flex" gap={0.5}>
          <Tooltip title="View Details">
            <IconButton size="small" onClick={() => data.onViewDetails?.(data)}>
              <Info />
            </IconButton>
          </Tooltip>
          <Tooltip title="Edit">
            <IconButton size="small" onClick={() => data.onEdit?.(data)}>
              <Edit />
            </IconButton>
          </Tooltip>
        </Box>
      </CardContent>
    </Card>
  );
};

// Node types
const nodeTypes: NodeTypes = {
  metaOrchestrator: MetaOrchestratorNode,
  subOrchestrator: SubOrchestratorNode,
  agent: AgentNode,
};

interface FamilyTreeVisualizationProps {
  data: FamilyTreeNode[];
  onNodeSelect?: (node: FamilyTreeNode) => void;
  onNodeEdit?: (node: FamilyTreeNode) => void;
  onNodeDelete?: (nodeId: string) => void;
  onAddNode?: (parentId: string, nodeType: string) => void;
}

export const FamilyTreeVisualization: React.FC<FamilyTreeVisualizationProps> = ({
  data,
  onNodeSelect,
  onNodeEdit,
  onNodeDelete,
  onAddNode,
}) => {
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [selectedNode, setSelectedNode] = useState<FamilyTreeNode | null>(null);
  const [showInactive, setShowInactive] = useState(true);
  const [addNodeDialog, setAddNodeDialog] = useState<{
    open: boolean;
    parentId: string;
    nodeType: string;
  }>({ open: false, parentId: '', nodeType: '' });

  // Convert tree data to React Flow format
  const convertToFlowData = useCallback((treeData: FamilyTreeNode[]) => {
    const flowNodes: Node[] = [];
    const flowEdges: Edge[] = [];
    let yOffset = 0;

    const processNode = (node: FamilyTreeNode, level: number, parentX = 0) => {
      const x = parentX + (level * 300);
      const y = yOffset;
      yOffset += 200;

      // Filter inactive nodes if needed
      if (!showInactive && !node.isActive) {
        return;
      }

      flowNodes.push({
        id: node.id,
        type: node.type,
        position: { x, y },
        data: {
          ...node,
          onViewDetails: (data: any) => {
            setSelectedNode(data);
            onNodeSelect?.(data);
          },
          onEdit: (data: any) => {
            onNodeEdit?.(data);
          },
        },
      });

      // Process children
      if (node.children) {
        node.children.forEach((child, index) => {
          flowEdges.push({
            id: `${node.id}-${child.id}`,
            source: node.id,
            target: child.id,
            type: 'smoothstep',
            animated: child.isActive,
            style: {
              stroke: child.isActive ? '#1976d2' : '#ccc',
              strokeWidth: 2,
            },
          });
          processNode(child, level + 1, x);
        });
      }
    };

    treeData.forEach(rootNode => processNode(rootNode, 0));
    return { nodes: flowNodes, edges: flowEdges };
  }, [showInactive, onNodeSelect, onNodeEdit]);

  // Update nodes and edges when data changes
  useEffect(() => {
    const { nodes: flowNodes, edges: flowEdges } = convertToFlowData(data);
    setNodes(flowNodes);
    setEdges(flowEdges);
  }, [data, convertToFlowData, setNodes, setEdges]);

  const onConnect = useCallback(
    (params: Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  );

  const handleAddNode = () => {
    if (addNodeDialog.parentId && addNodeDialog.nodeType) {
      onAddNode?.(addNodeDialog.parentId, addNodeDialog.nodeType);
      setAddNodeDialog({ open: false, parentId: '', nodeType: '' });
    }
  };

  return (
    <Box sx={{ height: '100%', width: '100%' }}>
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        nodeTypes={nodeTypes}
        fitView
        attributionPosition="bottom-left"
      >
        <Controls />
        <MiniMap />
        <Background />
        
        <Panel position="top-right">
          <Paper sx={{ p: 2, minWidth: 200 }}>
            <Typography variant="h6" gutterBottom>
              Family Tree Controls
            </Typography>
            <Box display="flex" flexDirection="column" gap={1}>
              <Button
                startIcon={showInactive ? <VisibilityOff /> : <Visibility />}
                onClick={() => setShowInactive(!showInactive)}
                variant="outlined"
                size="small"
              >
                {showInactive ? 'Hide Inactive' : 'Show Inactive'}
              </Button>
              <Button
                startIcon={<Add />}
                onClick={() => setAddNodeDialog({ 
                  open: true, 
                  parentId: selectedNode?.id || '', 
                  nodeType: 'agent' 
                })}
                variant="contained"
                size="small"
                disabled={!selectedNode}
              >
                Add Node
              </Button>
            </Box>
          </Paper>
        </Panel>
      </ReactFlow>

      {/* Add Node Dialog */}
      <Dialog open={addNodeDialog.open} onClose={() => setAddNodeDialog({ open: false, parentId: '', nodeType: '' })}>
        <DialogTitle>Add New Node</DialogTitle>
        <DialogContent>
          <FormControl fullWidth margin="normal">
            <InputLabel>Node Type</InputLabel>
            <Select
              value={addNodeDialog.nodeType}
              onChange={(e) => setAddNodeDialog(prev => ({ ...prev, nodeType: e.target.value }))}
            >
              <MenuItem value="metaOrchestrator">Meta-Orchestrator</MenuItem>
              <MenuItem value="subOrchestrator">Sub-Orchestrator</MenuItem>
              <MenuItem value="agent">Agent</MenuItem>
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAddNodeDialog({ open: false, parentId: '', nodeType: '' })}>
            Cancel
          </Button>
          <Button onClick={handleAddNode} variant="contained">
            Add
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default FamilyTreeVisualization;

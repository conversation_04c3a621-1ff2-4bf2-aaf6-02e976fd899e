<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EcoStamp</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            width: 320px;
            min-height: 400px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 16px;
            text-align: center;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }
        
        .logo {
            font-size: 24px;
            margin-bottom: 4px;
        }
        
        .title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .subtitle {
            font-size: 12px;
            color: #7f8c8d;
            margin-top: 2px;
        }
        
        .content {
            padding: 16px;
            background: white;
        }
        
        .status-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 12px;
        }
        
        .status-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 8px;
        }
        
        .status-title {
            font-weight: 600;
            font-size: 14px;
            color: #2c3e50;
        }
        
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #27ae60;
        }
        
        .status-indicator.offline {
            background: #e74c3c;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            margin-bottom: 12px;
        }
        
        .stat-item {
            text-align: center;
            padding: 8px;
            background: #ecf0f1;
            border-radius: 6px;
        }
        
        .stat-value {
            font-size: 16px;
            font-weight: 600;
            color: #27ae60;
            margin-bottom: 2px;
        }
        
        .stat-label {
            font-size: 10px;
            color: #7f8c8d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .eco-level {
            text-align: center;
            margin: 12px 0;
        }
        
        .eco-leaves {
            font-size: 20px;
            letter-spacing: 2px;
            margin-bottom: 4px;
        }
        
        .eco-label {
            font-size: 12px;
            color: #7f8c8d;
        }
        
        .actions {
            display: flex;
            flex-direction: column;
            gap: 8px;
            margin-top: 16px;
        }
        
        .btn {
            padding: 10px 16px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            text-align: center;
            display: block;
        }
        
        .btn-primary {
            background: #3498db;
            color: white;
        }
        
        .btn-primary:hover {
            background: #2980b9;
        }
        
        .btn-secondary {
            background: #95a5a6;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #7f8c8d;
        }
        
        .btn-success {
            background: #27ae60;
            color: white;
        }
        
        .btn-success:hover {
            background: #229954;
        }
        
        .settings {
            border-top: 1px solid #ecf0f1;
            padding-top: 12px;
            margin-top: 12px;
        }
        
        .setting-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 8px;
        }
        
        .setting-label {
            font-size: 12px;
            color: #2c3e50;
        }
        
        .toggle {
            position: relative;
            width: 40px;
            height: 20px;
            background: #bdc3c7;
            border-radius: 10px;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .toggle.active {
            background: #27ae60;
        }
        
        .toggle::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 16px;
            height: 16px;
            background: white;
            border-radius: 50%;
            transition: transform 0.2s;
        }
        
        .toggle.active::after {
            transform: translateX(20px);
        }
        
        .footer {
            background: #2c3e50;
            color: white;
            padding: 12px 16px;
            text-align: center;
            font-size: 11px;
        }
        
        .footer a {
            color: #3498db;
            text-decoration: none;
        }
        
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }
        
        .error {
            background: #e74c3c;
            color: white;
            padding: 8px;
            border-radius: 4px;
            font-size: 12px;
            margin-bottom: 12px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">🌱</div>
        <div class="title">EcoStamp</div>
        <div class="subtitle">AI Environmental Impact Tracker</div>
    </div>
    
    <div class="content">
        <div id="error-message" class="error" style="display: none;"></div>
        
        <div class="status-card">
            <div class="status-header">
                <div class="status-title">Connection Status</div>
                <div id="status-indicator" class="status-indicator"></div>
            </div>
            <div id="status-text">Checking connection...</div>
        </div>
        
        <div class="stats-grid">
            <div class="stat-item">
                <div id="energy-stat" class="stat-value">0.00</div>
                <div class="stat-label">kWh Today</div>
            </div>
            <div class="stat-item">
                <div id="water-stat" class="stat-value">0.0</div>
                <div class="stat-label">Liters Today</div>
            </div>
            <div class="stat-item">
                <div id="conversations-stat" class="stat-value">0</div>
                <div class="stat-label">Conversations</div>
            </div>
            <div class="stat-item">
                <div id="hashes-stat" class="stat-value">0</div>
                <div class="stat-label">Verified</div>
            </div>
        </div>
        
        <div class="eco-level">
            <div id="eco-leaves" class="eco-leaves">🌱🌱🌱🌱🌱</div>
            <div class="eco-label">Current Eco Level</div>
        </div>
        
        <div class="actions">
            <a href="#" id="verify-btn" class="btn btn-primary">Verify Conversation</a>
            <a href="#" id="dashboard-btn" class="btn btn-secondary">View Dashboard</a>
            <a href="#" id="github-btn" class="btn btn-success">View on GitHub</a>
        </div>
        
        <div class="settings">
            <div class="setting-item">
                <div class="setting-label">Enable Tracking</div>
                <div id="tracking-toggle" class="toggle active"></div>
            </div>
            <div class="setting-item">
                <div class="setting-label">Show Widget</div>
                <div id="widget-toggle" class="toggle active"></div>
            </div>
            <div class="setting-item">
                <div class="setting-label">Auto Verify</div>
                <div id="verify-toggle" class="toggle active"></div>
            </div>
        </div>
    </div>
    
    <div class="footer">
        <div>Making AI environmentally accountable</div>
        <div style="margin-top: 4px;">
            <a href="https://github.com/chris-ai-dev/Time_Stamp_Project" target="_blank">Open Source</a> • 
            <a href="http://localhost:3000" target="_blank">Website</a>
        </div>
    </div>
    
    <script src="popup.js"></script>
</body>
</html>

/**
 * Ollama Adapter
 * 
 * Adapter for Ollama - Local LLM runner
 * Specializes in: local models, privacy, offline capability, multi-model support
 */

const BaseAdapter = require('../base-adapter');
const axios = require('axios');
const chalk = require('chalk');

class OllamaAdapter extends BaseAdapter {
  constructor(config, metaOrchestrator) {
    super(config, metaOrchestrator);
    
    // Set adapter-specific properties
    this.adapterId = 'ollama';
    
    // Define capabilities
    this.addCapability('code-generation');
    this.addCapability('completion');
    this.addCapability('analysis');
    this.addCapability('documentation');
    this.addCapability('local-processing');
    this.addCapability('privacy');
    this.addCapability('offline-capability');
    this.addCapability('multi-model');
    
    // Define supported roles
    this.addSupportedRole('generator');
    this.addSupportedRole('completer');
    this.addSupportedRole('analyzer');
    this.addSupportedRole('documenter');
    
    // Ollama specific configuration
    this.baseUrl = this.getConfig('baseUrl', 'http://localhost:11434');
    this.defaultModel = this.getConfig('defaultModel', 'codellama');
    this.models = this.getConfig('models', ['codellama', 'llama2', 'mistral', 'deepseek-coder']);
    
    // API client
    this.client = null;
    
    // Available models cache
    this.availableModels = new Map();
  }
  
  async initialize() {
    try {
      this.log('info', 'Initializing Ollama adapter...');
      
      // Setup API client
      this.client = axios.create({
        baseURL: this.baseUrl,
        timeout: this.getConfig('timeout', 60000),
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      // Test connection and get available models
      await this.testConnection();
      await this.loadAvailableModels();
      
      this.state.initialized = true;
      this.log('info', 'Ollama adapter initialized successfully');
      
    } catch (error) {
      this.log('error', 'Failed to initialize Ollama adapter', { error: error.message });
      throw error;
    }
  }
  
  async testConnection() {
    try {
      const response = await this.client.get('/api/tags');
      
      if (response.status === 200) {
        this.log('info', 'Ollama connection successful');
        return true;
      } else {
        throw new Error(`Unexpected response status: ${response.status}`);
      }
      
    } catch (error) {
      if (error.code === 'ECONNREFUSED') {
        throw new Error('Ollama server not running - start with: ollama serve');
      }
      throw error;
    }
  }
  
  async loadAvailableModels() {
    try {
      const response = await this.client.get('/api/tags');
      
      if (response.data && response.data.models) {
        for (const model of response.data.models) {
          this.availableModels.set(model.name, {
            name: model.name,
            size: model.size,
            modified_at: model.modified_at,
            digest: model.digest
          });
        }
        
        this.log('info', `Found ${this.availableModels.size} available models`);
      }
      
    } catch (error) {
      this.log('warn', 'Failed to load available models', { error: error.message });
    }
  }
  
  async checkAvailability() {
    try {
      const response = await this.client.get('/api/tags', { timeout: 5000 });
      return response.status === 200;
      
    } catch (error) {
      this.log('debug', 'Availability check failed', { error: error.message });
      return false;
    }
  }
  
  async execute(task, context) {
    this.validateTask(task);
    this.validateContext(context);
    
    const { role } = task;
    
    switch (role) {
      case 'generator':
        return await this.performGeneration(task, context);
      case 'completer':
        return await this.performCompletion(task, context);
      case 'analyzer':
        return await this.performAnalysis(task, context);
      case 'documenter':
        return await this.performDocumentation(task, context);
      default:
        throw new Error(`Unsupported role for Ollama: ${role}`);
    }
  }
  
  async performGeneration(task, context) {
    try {
      this.log('info', 'Performing code generation', { task: task.type });
      
      const generationType = task.generationType || 'function';
      const requirements = task.requirements || task.description;
      const model = this.selectBestModel(context, 'generation');
      
      const prompt = this.buildGenerationPrompt(requirements, context, generationType);
      
      const result = await this.generateWithModel(model, prompt, context);
      
      return {
        type: 'generation',
        generationType,
        result: {
          code: result.response,
          explanation: `Generated using Ollama model: ${model}`,
          model: model,
          method: 'ollama'
        },
        metadata: {
          model,
          requirements,
          timestamp: Date.now(),
          adapterId: this.adapterId
        }
      };
      
    } catch (error) {
      this.log('error', 'Code generation failed', { error: error.message });
      throw error;
    }
  }
  
  async performCompletion(task, context) {
    try {
      this.log('info', 'Performing code completion', { task: task.type });
      
      const partialCode = task.partialCode || context.partialCode;
      const model = this.selectBestModel(context, 'completion');
      
      const prompt = this.buildCompletionPrompt(partialCode, context);
      
      const result = await this.generateWithModel(model, prompt, context, {
        temperature: 0.3, // Lower temperature for more focused completions
        max_tokens: 200   // Shorter completions
      });
      
      return {
        completions: [result.response],
        model: model,
        method: 'ollama'
      };
      
    } catch (error) {
      this.log('error', 'Code completion failed', { error: error.message });
      throw error;
    }
  }
  
  async performAnalysis(task, context) {
    try {
      this.log('info', 'Performing code analysis', { task: task.type });
      
      const analysisType = task.analysisType || 'comprehensive';
      const sourceCode = task.sourceCode || context.sourceCode;
      const model = this.selectBestModel(context, 'analysis');
      
      const prompt = this.buildAnalysisPrompt(sourceCode, context, analysisType);
      
      const result = await this.generateWithModel(model, prompt, context);
      
      return {
        type: 'analysis',
        analysisType,
        result: {
          analysis: result.response,
          model: model,
          method: 'ollama'
        },
        metadata: {
          model,
          sourceCode: sourceCode ? sourceCode.substring(0, 100) + '...' : null,
          timestamp: Date.now(),
          adapterId: this.adapterId
        }
      };
      
    } catch (error) {
      this.log('error', 'Code analysis failed', { error: error.message });
      throw error;
    }
  }
  
  async performDocumentation(task, context) {
    try {
      this.log('info', 'Performing documentation generation', { task: task.type });
      
      const sourceCode = task.sourceCode || context.sourceCode;
      const documentationType = task.documentationType || 'comprehensive';
      const model = this.selectBestModel(context, 'documentation');
      
      const prompt = this.buildDocumentationPrompt(sourceCode, context, documentationType);
      
      const result = await this.generateWithModel(model, prompt, context);
      
      return {
        documentation: result.response,
        model: model,
        method: 'ollama'
      };
      
    } catch (error) {
      this.log('error', 'Documentation generation failed', { error: error.message });
      throw error;
    }
  }
  
  selectBestModel(context, taskType) {
    // Select the best model based on task type and context
    const language = context.language || 'javascript';
    
    // Model preferences by task type
    const modelPreferences = {
      generation: ['deepseek-coder', 'codellama', 'mistral', 'llama2'],
      completion: ['codellama', 'deepseek-coder', 'mistral'],
      analysis: ['llama2', 'mistral', 'codellama'],
      documentation: ['llama2', 'mistral', 'codellama']
    };
    
    // Language-specific model preferences
    const languagePreferences = {
      python: ['deepseek-coder', 'codellama'],
      javascript: ['codellama', 'deepseek-coder'],
      typescript: ['codellama', 'deepseek-coder'],
      rust: ['codellama', 'deepseek-coder'],
      go: ['codellama', 'deepseek-coder'],
      java: ['codellama', 'deepseek-coder']
    };
    
    // Get preferred models for this task type
    const taskPreferences = modelPreferences[taskType] || ['codellama'];
    const langPreferences = languagePreferences[language] || [];
    
    // Combine preferences (language-specific first, then task-specific)
    const combinedPreferences = [...langPreferences, ...taskPreferences];
    
    // Find the first available model from preferences
    for (const preferredModel of combinedPreferences) {
      if (this.availableModels.has(preferredModel)) {
        return preferredModel;
      }
    }
    
    // Fallback to default model or first available
    if (this.availableModels.has(this.defaultModel)) {
      return this.defaultModel;
    }
    
    // Last resort: use first available model
    const firstAvailable = Array.from(this.availableModels.keys())[0];
    if (firstAvailable) {
      return firstAvailable;
    }
    
    throw new Error('No models available in Ollama');
  }
  
  buildGenerationPrompt(requirements, context, type) {
    let prompt = `Generate ${type} code based on the following requirements:\n\n${requirements}\n\n`;
    
    if (context.language) {
      prompt += `Language: ${context.language}\n`;
    }
    
    if (context.framework) {
      prompt += `Framework: ${context.framework}\n`;
    }
    
    if (context.existingCode) {
      prompt += `\nExisting code context:\n\`\`\`\n${context.existingCode}\n\`\`\`\n`;
    }
    
    prompt += '\nPlease provide clean, well-documented code with explanations.';
    
    return prompt;
  }
  
  buildCompletionPrompt(partialCode, context) {
    let prompt = `Complete the following code:\n\n\`\`\`\n${partialCode}\n\`\`\`\n\n`;
    
    if (context.language) {
      prompt += `Language: ${context.language}\n`;
    }
    
    prompt += 'Provide only the completion, not the full code.';
    
    return prompt;
  }
  
  buildAnalysisPrompt(sourceCode, context, analysisType) {
    let prompt = `Perform ${analysisType} analysis of the following code:\n\n\`\`\`\n${sourceCode}\n\`\`\`\n\n`;
    
    switch (analysisType) {
      case 'security':
        prompt += 'Focus on security vulnerabilities, potential exploits, and security best practices.';
        break;
      case 'performance':
        prompt += 'Focus on performance bottlenecks, optimization opportunities, and efficiency improvements.';
        break;
      case 'quality':
        prompt += 'Focus on code quality, maintainability, readability, and best practices.';
        break;
      default:
        prompt += 'Provide comprehensive analysis including quality, performance, security, and improvement suggestions.';
    }
    
    return prompt;
  }
  
  buildDocumentationPrompt(sourceCode, context, documentationType) {
    let prompt = `Generate ${documentationType} documentation for the following code:\n\n\`\`\`\n${sourceCode}\n\`\`\`\n\n`;
    
    switch (documentationType) {
      case 'api':
        prompt += 'Focus on API documentation with endpoints, parameters, responses, and examples.';
        break;
      case 'inline':
        prompt += 'Generate inline comments and docstrings for the code.';
        break;
      case 'readme':
        prompt += 'Generate README-style documentation with usage examples and setup instructions.';
        break;
      default:
        prompt += 'Generate comprehensive documentation including function descriptions, parameters, return values, and usage examples.';
    }
    
    return prompt;
  }
  
  async generateWithModel(model, prompt, context, options = {}) {
    try {
      const requestData = {
        model: model,
        prompt: prompt,
        stream: false,
        options: {
          temperature: options.temperature || 0.7,
          max_tokens: options.max_tokens || 2000,
          top_p: options.top_p || 0.9,
          ...options
        }
      };
      
      this.log('debug', `Generating with model ${model}`, { promptLength: prompt.length });
      
      const response = await this.client.post('/api/generate', requestData);
      
      if (response.data && response.data.response) {
        return response.data;
      } else {
        throw new Error('Invalid response from Ollama');
      }
      
    } catch (error) {
      this.log('error', `Generation failed with model ${model}`, { error: error.message });
      throw error;
    }
  }
  
  async pullModel(modelName) {
    try {
      this.log('info', `Pulling model: ${modelName}`);
      
      const response = await this.client.post('/api/pull', {
        name: modelName
      });
      
      if (response.status === 200) {
        // Reload available models
        await this.loadAvailableModels();
        this.log('info', `Successfully pulled model: ${modelName}`);
        return true;
      }
      
      return false;
      
    } catch (error) {
      this.log('error', `Failed to pull model ${modelName}`, { error: error.message });
      throw error;
    }
  }
  
  async deleteModel(modelName) {
    try {
      this.log('info', `Deleting model: ${modelName}`);
      
      const response = await this.client.delete('/api/delete', {
        data: { name: modelName }
      });
      
      if (response.status === 200) {
        // Reload available models
        await this.loadAvailableModels();
        this.log('info', `Successfully deleted model: ${modelName}`);
        return true;
      }
      
      return false;
      
    } catch (error) {
      this.log('error', `Failed to delete model ${modelName}`, { error: error.message });
      throw error;
    }
  }
  
  getAvailableModels() {
    return Array.from(this.availableModels.values());
  }
  
  async healthCheck() {
    try {
      const response = await this.client.get('/api/tags', { timeout: 5000 });
      return response.status === 200 && this.availableModels.size > 0;
      
    } catch (error) {
      return false;
    }
  }
  
  validateContext(context) {
    super.validateContext(context);
    
    // Ollama specific context validation
    if (context.role === 'completer' && !context.partialCode) {
      throw new Error('partialCode is required for completion tasks');
    }
    
    if (context.role === 'analyzer' && !context.sourceCode) {
      throw new Error('sourceCode is required for analysis tasks');
    }
    
    if (context.role === 'documenter' && !context.sourceCode) {
      throw new Error('sourceCode is required for documentation tasks');
    }
    
    return true;
  }
  
  async shutdown() {
    this.log('info', 'Shutting down Ollama adapter');
    
    if (this.client) {
      this.client = null;
    }
    
    this.availableModels.clear();
    
    await super.shutdown();
  }
}

module.exports = OllamaAdapter;

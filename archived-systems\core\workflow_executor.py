"""
Dynamic Workflow Execution Engine

Executes tasks through branded AI assistants with fallback logic,
continuous optimization, and performance monitoring.

Key Features:
- Dynamic agent execution with fallback
- Performance monitoring and optimization
- Compliance validation during execution
- Result aggregation and quality assessment
- Continuous learning and improvement
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timed<PERSON>ta
from dataclasses import dataclass, field
from enum import Enum
import uuid

from .agent_registry import AgentRegistry, AgentRole, AgentStatus
from .shared_context import SharedContext
from .legal_compliance import ComplianceManager


class TaskStatus(Enum):
    """Task execution status."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class ExecutionMode(Enum):
    """Execution mode for workflow."""
    SINGLE_AGENT = "single_agent"
    MULTI_AGENT_SEQUENTIAL = "multi_agent_sequential"
    MULTI_AGENT_PARALLEL = "multi_agent_parallel"
    ADAPTIVE = "adaptive"


@dataclass
class TaskResult:
    """Result of task execution."""
    task_id: str
    success: bool
    output_data: Dict[str, Any] = field(default_factory=dict)
    error_message: Optional[str] = None
    execution_time: float = 0.0
    agent_id: Optional[str] = None
    role: Optional[AgentRole] = None
    quality_score: float = 0.0
    metadata: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.utcnow)


@dataclass
class ExecutionContext:
    """Context for task execution."""
    task_id: str
    task_description: str
    task_type: str
    user_context: Dict[str, Any]
    shared_context: Dict[str, Any]
    execution_mode: ExecutionMode
    timeout_seconds: int = 300
    retry_count: int = 3
    quality_threshold: float = 0.7
    created_at: datetime = field(default_factory=datetime.utcnow)


class WorkflowExecutor:
    """
    Dynamic workflow execution engine for branded AI assistants.
    
    Executes tasks through appropriate agents with intelligent fallback,
    performance monitoring, and continuous optimization.
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # Core components (will be injected)
        self.agent_registry: Optional[AgentRegistry] = None
        self.shared_context: Optional[SharedContext] = None
        self.compliance_manager: Optional[ComplianceManager] = None
        
        # Execution tracking
        self.active_executions: Dict[str, ExecutionContext] = {}
        self.execution_history: List[TaskResult] = []
        
        # Configuration
        self.default_timeout = self.config.get('default_timeout_seconds', 300)
        self.max_retries = self.config.get('max_retries', 3)
        self.quality_threshold = self.config.get('quality_threshold', 0.7)
        self.enable_fallback = self.config.get('enable_fallback', True)
        self.enable_learning = self.config.get('enable_learning', True)
        
        # Performance tracking
        self.execution_stats = {
            'total_executions': 0,
            'successful_executions': 0,
            'failed_executions': 0,
            'average_execution_time': 0.0,
            'average_quality_score': 0.0,
            'fallback_activations': 0,
            'agent_performance': {},
            'role_performance': {}
        }
    
    def set_dependencies(self, agent_registry: AgentRegistry, shared_context: SharedContext,
                        compliance_manager: ComplianceManager) -> None:
        """Set required dependencies."""
        self.agent_registry = agent_registry
        self.shared_context = shared_context
        self.compliance_manager = compliance_manager
    
    async def execute_with_agent(self, agent_id: str, role: AgentRole, 
                                task_description: str, context: Dict[str, Any],
                                shared_context: Dict[str, Any] = None) -> TaskResult:
        """
        Execute a task with a specific agent in a specific role.
        
        Args:
            agent_id: ID of the agent to use
            role: Role for the agent to fulfill
            task_description: Description of the task
            context: Task context
            shared_context: Shared context data
            
        Returns:
            TaskResult with execution details
        """
        task_id = str(uuid.uuid4())
        start_time = datetime.utcnow()
        
        try:
            # Validate agent availability and compliance
            if not self.agent_registry.is_agent_available(agent_id):
                return TaskResult(
                    task_id=task_id,
                    success=False,
                    error_message=f"Agent {agent_id} is not available",
                    agent_id=agent_id,
                    role=role
                )
            
            # Get agent info
            agent_info = self.agent_registry.get_agent_info(agent_id)
            if not agent_info:
                return TaskResult(
                    task_id=task_id,
                    success=False,
                    error_message=f"Agent {agent_id} not found",
                    agent_id=agent_id,
                    role=role
                )
            
            # Validate role compatibility
            if role.value not in agent_info['supported_roles']:
                return TaskResult(
                    task_id=task_id,
                    success=False,
                    error_message=f"Agent {agent_id} does not support role {role.value}",
                    agent_id=agent_id,
                    role=role
                )
            
            # Check compliance
            vendor = agent_info['vendor']
            compliance_status = self.compliance_manager.check_compliance_status(vendor)
            if not compliance_status.get('compliant', False):
                return TaskResult(
                    task_id=task_id,
                    success=False,
                    error_message=f"Compliance check failed for {vendor}",
                    agent_id=agent_id,
                    role=role,
                    metadata={'compliance_status': compliance_status}
                )
            
            # Execute task with agent
            result = await self._execute_agent_task(
                agent_id, role, task_description, context, shared_context or {}
            )
            
            # Calculate execution time
            execution_time = (datetime.utcnow() - start_time).total_seconds()
            result.execution_time = execution_time
            result.task_id = task_id
            result.agent_id = agent_id
            result.role = role
            
            # Update performance metrics
            self._update_execution_stats(result)
            
            # Update agent performance in registry
            self.agent_registry.update_agent_performance(
                agent_id, result.success, execution_time, result.quality_score
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error executing task with agent {agent_id}: {str(e)}")
            
            execution_time = (datetime.utcnow() - start_time).total_seconds()
            
            error_result = TaskResult(
                task_id=task_id,
                success=False,
                error_message=str(e),
                execution_time=execution_time,
                agent_id=agent_id,
                role=role,
                metadata={'error_type': type(e).__name__}
            )
            
            self._update_execution_stats(error_result)
            return error_result
    
    async def _execute_agent_task(self, agent_id: str, role: AgentRole, 
                                 task_description: str, context: Dict[str, Any],
                                 shared_context: Dict[str, Any]) -> TaskResult:
        """Execute the actual task with the agent (placeholder implementation)."""
        # This is a placeholder implementation
        # In a real system, this would make API calls to the branded AI assistants
        
        await asyncio.sleep(0.1)  # Simulate API call delay
        
        # Simulate different outcomes based on role and agent
        success_probability = self._calculate_success_probability(agent_id, role)
        
        import random
        success = random.random() < success_probability
        
        if success:
            # Generate mock output based on role
            output_data = self._generate_mock_output(role, task_description, context)
            quality_score = random.uniform(0.7, 1.0)
            
            return TaskResult(
                task_id="",  # Will be set by caller
                success=True,
                output_data=output_data,
                quality_score=quality_score,
                metadata={
                    'mock_execution': True,
                    'role': role.value,
                    'context_keys': list(context.keys())
                }
            )
        else:
            return TaskResult(
                task_id="",  # Will be set by caller
                success=False,
                error_message="Mock execution failure",
                quality_score=0.0,
                metadata={'mock_execution': True, 'role': role.value}
            )
    
    def _calculate_success_probability(self, agent_id: str, role: AgentRole) -> float:
        """Calculate success probability based on agent and role."""
        # Base probability
        base_prob = 0.8
        
        # Adjust based on agent performance history
        if agent_id in self.execution_stats['agent_performance']:
            agent_stats = self.execution_stats['agent_performance'][agent_id]
            success_rate = agent_stats.get('success_rate', 0.8)
            base_prob = (base_prob + success_rate) / 2
        
        # Adjust based on role performance
        if role.value in self.execution_stats['role_performance']:
            role_stats = self.execution_stats['role_performance'][role.value]
            role_success_rate = role_stats.get('success_rate', 0.8)
            base_prob = (base_prob + role_success_rate) / 2
        
        return min(0.95, max(0.1, base_prob))
    
    def _generate_mock_output(self, role: AgentRole, task_description: str, 
                            context: Dict[str, Any]) -> Dict[str, Any]:
        """Generate mock output based on role."""
        base_output = {
            'task_description': task_description,
            'role': role.value,
            'timestamp': datetime.utcnow().isoformat(),
            'context_summary': f"Processed {len(context)} context items"
        }
        
        if role == AgentRole.GENERATOR:
            base_output.update({
                'generated_code': f"# Generated code for: {task_description}\nprint('Hello, World!')",
                'language': 'python',
                'functions_created': ['main', 'helper'],
                'lines_of_code': 42
            })
        elif role == AgentRole.ANALYZER:
            base_output.update({
                'analysis_results': {
                    'complexity_score': 0.6,
                    'maintainability': 0.8,
                    'potential_issues': ['minor optimization opportunity'],
                    'recommendations': ['consider adding error handling']
                },
                'metrics': {'cyclomatic_complexity': 3, 'code_coverage': 0.85}
            })
        elif role == AgentRole.VALIDATOR:
            base_output.update({
                'validation_results': {
                    'passed_tests': 8,
                    'failed_tests': 0,
                    'coverage_percentage': 92.5,
                    'quality_score': 0.9
                },
                'test_cases': ['test_basic_functionality', 'test_edge_cases']
            })
        elif role == AgentRole.COMPLETER:
            base_output.update({
                'completions': [
                    {'suggestion': 'def process_data(data):', 'confidence': 0.9},
                    {'suggestion': 'return data.transform()', 'confidence': 0.8}
                ],
                'completion_count': 2
            })
        elif role == AgentRole.DOCUMENTER:
            base_output.update({
                'documentation': {
                    'docstrings_added': 5,
                    'readme_sections': ['Installation', 'Usage', 'API Reference'],
                    'examples_created': 3
                },
                'documentation_coverage': 0.88
            })
        
        return base_output
    
    async def execute_with_combination(self, combination, task_description: str,
                                     task_type: str, context: Dict[str, Any]) -> TaskResult:
        """Execute task with a specific agent-role combination."""
        task_id = str(uuid.uuid4())
        start_time = datetime.utcnow()
        
        try:
            # Create execution context
            execution_context = ExecutionContext(
                task_id=task_id,
                task_description=task_description,
                task_type=task_type,
                user_context=context,
                shared_context={},
                execution_mode=ExecutionMode.MULTI_AGENT_SEQUENTIAL
            )
            
            self.active_executions[task_id] = execution_context
            
            # Execute each agent in the combination
            results = []
            current_context = context.copy()
            
            for role, agent_id in combination.agents.items():
                agent_result = await self.execute_with_agent(
                    agent_id, role, task_description, current_context
                )
                
                results.append(agent_result)
                
                if agent_result.success:
                    # Update context with results for next agent
                    current_context.update(agent_result.output_data)
                else:
                    # If any agent fails, the combination fails
                    break
            
            # Aggregate results
            overall_success = all(result.success for result in results)
            combined_output = {}
            for result in results:
                combined_output.update(result.output_data)
            
            # Calculate overall quality score
            quality_scores = [result.quality_score for result in results if result.success]
            overall_quality = sum(quality_scores) / len(quality_scores) if quality_scores else 0.0
            
            execution_time = (datetime.utcnow() - start_time).total_seconds()
            
            final_result = TaskResult(
                task_id=task_id,
                success=overall_success,
                output_data=combined_output,
                execution_time=execution_time,
                quality_score=overall_quality,
                metadata={
                    'combination_id': combination.combination_id,
                    'agents_used': list(combination.agents.values()),
                    'roles_used': [role.value for role in combination.agents.keys()],
                    'individual_results': len(results)
                }
            )
            
            # Clean up
            del self.active_executions[task_id]
            
            return final_result
            
        except Exception as e:
            self.logger.error(f"Error executing combination: {str(e)}")
            
            if task_id in self.active_executions:
                del self.active_executions[task_id]
            
            return TaskResult(
                task_id=task_id,
                success=False,
                error_message=str(e),
                execution_time=(datetime.utcnow() - start_time).total_seconds(),
                metadata={'combination_error': True}
            )
    
    def _update_execution_stats(self, result: TaskResult) -> None:
        """Update execution statistics."""
        self.execution_stats['total_executions'] += 1
        
        if result.success:
            self.execution_stats['successful_executions'] += 1
        else:
            self.execution_stats['failed_executions'] += 1
        
        # Update average execution time
        total_time = (self.execution_stats['average_execution_time'] * 
                     (self.execution_stats['total_executions'] - 1) + result.execution_time)
        self.execution_stats['average_execution_time'] = total_time / self.execution_stats['total_executions']
        
        # Update average quality score
        if result.success:
            current_avg = self.execution_stats['average_quality_score']
            successful_count = self.execution_stats['successful_executions']
            
            if successful_count == 1:
                self.execution_stats['average_quality_score'] = result.quality_score
            else:
                total_quality = current_avg * (successful_count - 1) + result.quality_score
                self.execution_stats['average_quality_score'] = total_quality / successful_count
        
        # Update agent-specific stats
        if result.agent_id:
            if result.agent_id not in self.execution_stats['agent_performance']:
                self.execution_stats['agent_performance'][result.agent_id] = {
                    'total_tasks': 0,
                    'successful_tasks': 0,
                    'success_rate': 0.0,
                    'average_quality': 0.0
                }
            
            agent_stats = self.execution_stats['agent_performance'][result.agent_id]
            agent_stats['total_tasks'] += 1
            
            if result.success:
                agent_stats['successful_tasks'] += 1
            
            agent_stats['success_rate'] = agent_stats['successful_tasks'] / agent_stats['total_tasks']
            
            if result.success:
                if agent_stats['successful_tasks'] == 1:
                    agent_stats['average_quality'] = result.quality_score
                else:
                    total_quality = (agent_stats['average_quality'] * 
                                   (agent_stats['successful_tasks'] - 1) + result.quality_score)
                    agent_stats['average_quality'] = total_quality / agent_stats['successful_tasks']
        
        # Update role-specific stats
        if result.role:
            role_key = result.role.value
            if role_key not in self.execution_stats['role_performance']:
                self.execution_stats['role_performance'][role_key] = {
                    'total_tasks': 0,
                    'successful_tasks': 0,
                    'success_rate': 0.0,
                    'average_quality': 0.0
                }
            
            role_stats = self.execution_stats['role_performance'][role_key]
            role_stats['total_tasks'] += 1
            
            if result.success:
                role_stats['successful_tasks'] += 1
            
            role_stats['success_rate'] = role_stats['successful_tasks'] / role_stats['total_tasks']
            
            if result.success:
                if role_stats['successful_tasks'] == 1:
                    role_stats['average_quality'] = result.quality_score
                else:
                    total_quality = (role_stats['average_quality'] * 
                                   (role_stats['successful_tasks'] - 1) + result.quality_score)
                    role_stats['average_quality'] = total_quality / role_stats['successful_tasks']
        
        # Store in execution history (keep last 100)
        self.execution_history.append(result)
        if len(self.execution_history) > 100:
            self.execution_history = self.execution_history[-100:]
    
    def get_execution_statistics(self) -> Dict[str, Any]:
        """Get comprehensive execution statistics."""
        return {
            'execution_stats': self.execution_stats.copy(),
            'active_executions': len(self.active_executions),
            'recent_executions': len(self.execution_history),
            'configuration': {
                'default_timeout': self.default_timeout,
                'max_retries': self.max_retries,
                'quality_threshold': self.quality_threshold,
                'enable_fallback': self.enable_fallback,
                'enable_learning': self.enable_learning
            },
            'last_updated': datetime.utcnow().isoformat()
        }
    
    def get_recent_results(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent execution results."""
        recent_results = self.execution_history[-limit:] if self.execution_history else []
        
        return [
            {
                'task_id': result.task_id,
                'success': result.success,
                'agent_id': result.agent_id,
                'role': result.role.value if result.role else None,
                'execution_time': result.execution_time,
                'quality_score': result.quality_score,
                'created_at': result.created_at.isoformat(),
                'error_message': result.error_message
            }
            for result in reversed(recent_results)
        ]

"""
Interfaces and Protocols for Feedback Loop Components

This module defines the standardized interfaces that all feedback loop
components must implement to ensure consistent integration and extensibility.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Protocol, runtime_checkable
from .feedback_types import Feedback<PERSON><PERSON><PERSON>, ValidationResult, FeedbackType


@runtime_checkable
class OutputInterpreter(Protocol):
    """
    Protocol for interpreting and normalizing AI outputs from different domains.
    
    Interpreters convert raw AI outputs into standardized formats that can be
    processed by the feedback engine.
    """
    
    def interpret(self, raw_output: Any, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Interpret and normalize raw AI output.
        
        Args:
            raw_output: The raw output from an AI system
            context: Additional context information
            
        Returns:
            Normalized output dictionary with standardized fields
        """
        ...
    
    def validate_input(self, raw_output: Any) -> bool:
        """
        Validate that the raw output can be interpreted by this interpreter.
        
        Args:
            raw_output: The raw output to validate
            
        Returns:
            True if the output can be interpreted, False otherwise
        """
        ...
    
    def get_supported_formats(self) -> List[str]:
        """
        Get list of supported output formats.
        
        Returns:
            List of format identifiers this interpreter supports
        """
        ...


@runtime_checkable
class PatternMatcher(Protocol):
    """
    Protocol for matching patterns in AI outputs to determine correctness.
    
    Matchers analyze interpreted outputs against expected patterns, rules,
    or reference data to identify potential issues.
    """
    
    def match(self, interpreted_output: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Match interpreted output against expected patterns.
        
        Args:
            interpreted_output: Normalized output from interpreter
            context: Additional context for matching
            
        Returns:
            Match results with confidence scores and identified patterns
        """
        ...
    
    def add_pattern(self, pattern_id: str, pattern_data: Dict[str, Any]) -> None:
        """
        Add a new pattern to the matcher.
        
        Args:
            pattern_id: Unique identifier for the pattern
            pattern_data: Pattern definition and matching criteria
        """
        ...
    
    def remove_pattern(self, pattern_id: str) -> bool:
        """
        Remove a pattern from the matcher.
        
        Args:
            pattern_id: Identifier of pattern to remove
            
        Returns:
            True if pattern was removed, False if not found
        """
        ...


@runtime_checkable
class OutputValidator(Protocol):
    """
    Protocol for validating AI outputs and assigning feedback types.
    
    Validators use interpreter and matcher results to determine the final
    feedback type and validation status.
    """
    
    def validate(self, 
                interpreted_output: Dict[str, Any], 
                match_results: Dict[str, Any], 
                context: Dict[str, Any]) -> ValidationResult:
        """
        Validate output and determine feedback type.
        
        Args:
            interpreted_output: Normalized output from interpreter
            match_results: Results from pattern matcher
            context: Additional validation context
            
        Returns:
            ValidationResult with feedback type and details
        """
        ...
    
    def supports_partial_correctness(self) -> bool:
        """
        Check if validator supports 'Partially Correct' feedback type.
        
        Returns:
            True if partial correctness is supported
        """
        ...
    
    def get_validation_criteria(self) -> Dict[str, Any]:
        """
        Get the validation criteria used by this validator.
        
        Returns:
            Dictionary describing validation rules and thresholds
        """
        ...


@runtime_checkable
class ConfidenceModel(Protocol):
    """
    Protocol for confidence scoring models.
    
    Confidence models calculate and adjust confidence scores based on
    validation results and historical performance.
    """
    
    def calculate_confidence(self, 
                           validation_result: ValidationResult, 
                           context: Dict[str, Any]) -> float:
        """
        Calculate confidence score for a validation result.
        
        Args:
            validation_result: Result from validator
            context: Additional context for confidence calculation
            
        Returns:
            Confidence score between 0.0 and 1.0
        """
        ...
    
    def adjust_confidence(self, 
                         current_confidence: float, 
                         feedback_type: FeedbackType, 
                         context: Dict[str, Any]) -> float:
        """
        Adjust confidence score based on feedback.
        
        Args:
            current_confidence: Current confidence score
            feedback_type: Type of feedback received
            context: Additional context for adjustment
            
        Returns:
            Adjusted confidence score between 0.0 and 1.0
        """
        ...
    
    def update_model(self, feedback_entries: List[FeedbackEntry]) -> None:
        """
        Update the confidence model with new feedback data.
        
        Args:
            feedback_entries: List of recent feedback entries for learning
        """
        ...


@runtime_checkable
class TrustScoreCalculator(Protocol):
    """
    Protocol for trust score calculation and management.
    
    Trust calculators maintain long-term reliability metrics for agents
    and domains based on historical feedback.
    """
    
    def calculate_trust_score(self, 
                            agent_id: str, 
                            domain: str, 
                            recent_entries: List[FeedbackEntry]) -> float:
        """
        Calculate trust score for an agent in a specific domain.
        
        Args:
            agent_id: Identifier of the agent
            domain: Domain name
            recent_entries: Recent feedback entries for the agent
            
        Returns:
            Trust score between 0.0 and 1.0
        """
        ...
    
    def update_trust_score(self, 
                          agent_id: str, 
                          domain: str, 
                          feedback_entry: FeedbackEntry) -> float:
        """
        Update trust score with new feedback entry.
        
        Args:
            agent_id: Identifier of the agent
            domain: Domain name
            feedback_entry: New feedback entry
            
        Returns:
            Updated trust score between 0.0 and 1.0
        """
        ...
    
    def get_trust_history(self, agent_id: str, domain: str) -> List[Dict[str, Any]]:
        """
        Get trust score history for an agent in a domain.
        
        Args:
            agent_id: Identifier of the agent
            domain: Domain name
            
        Returns:
            List of historical trust score entries
        """
        ...


@runtime_checkable
class MemoryStore(Protocol):
    """
    Protocol for storing and retrieving feedback entries.
    
    Memory stores provide persistent storage for feedback data with
    developer-only access and analytics support.
    """
    
    def store_entry(self, entry: FeedbackEntry) -> bool:
        """
        Store a feedback entry.
        
        Args:
            entry: Feedback entry to store
            
        Returns:
            True if stored successfully, False otherwise
        """
        ...
    
    def retrieve_entries(self, 
                        domain: Optional[str] = None, 
                        agent_id: Optional[str] = None, 
                        limit: Optional[int] = None) -> List[FeedbackEntry]:
        """
        Retrieve feedback entries with optional filtering.
        
        Args:
            domain: Optional domain filter
            agent_id: Optional agent ID filter
            limit: Optional limit on number of entries
            
        Returns:
            List of matching feedback entries
        """
        ...
    
    def get_analytics_data(self, 
                          domain: Optional[str] = None, 
                          time_range: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Get analytics data for reporting and dashboards.
        
        Args:
            domain: Optional domain filter
            time_range: Optional time range filter
            
        Returns:
            Analytics data dictionary
        """
        ...
    
    def cleanup_old_entries(self, retention_days: int) -> int:
        """
        Clean up old entries beyond retention period.
        
        Args:
            retention_days: Number of days to retain entries
            
        Returns:
            Number of entries cleaned up
        """
        ...


class BaseDomain(ABC):
    """
    Abstract base class for domain implementations.
    
    Domains coordinate the interpreter, matcher, and validator for a specific
    AI domain (e.g., Drone AI, TimeStamp AI).
    """
    
    def __init__(self, domain_name: str):
        self.domain_name = domain_name
        self.interpreter: Optional[OutputInterpreter] = None
        self.matcher: Optional[PatternMatcher] = None
        self.validator: Optional[OutputValidator] = None
    
    @abstractmethod
    def initialize_components(self) -> None:
        """Initialize domain-specific components."""
        pass
    
    @abstractmethod
    def process_output(self, raw_output: Any, context: Dict[str, Any]) -> ValidationResult:
        """Process AI output through the domain pipeline."""
        pass
    
    def get_domain_info(self) -> Dict[str, Any]:
        """Get information about this domain."""
        return {
            'name': self.domain_name,
            'interpreter': type(self.interpreter).__name__ if self.interpreter else None,
            'matcher': type(self.matcher).__name__ if self.matcher else None,
            'validator': type(self.validator).__name__ if self.validator else None
        }

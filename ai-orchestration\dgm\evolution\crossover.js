/**
 * Crossover Operations for DGM
 * 
 * Implements various crossover strategies to combine parent agents:
 * - Code-level crossover
 * - Semantic crossover
 * - Feature-based crossover
 * - Hybrid approaches
 */

const chalk = require('chalk');

class Crossover {
  constructor(config) {
    this.config = config;
    this.crossoverStrategies = config.get('genetics.crossoverStrategies', [
      'single_point',
      'two_point',
      'uniform',
      'semantic'
    ]);
  }

  /**
   * Perform crossover between two parent agents
   */
  async performCrossover(parent1, parent2, crossoverInstructions) {
    try {
      console.log(chalk.blue(`🔀 Performing ${crossoverInstructions.strategy} crossover`));
      
      const childCode = await this.combineParentCode(parent1, parent2, crossoverInstructions);
      const childMetadata = this.combineMetadata(parent1, parent2, crossoverInstructions);
      
      const child = {
        id: this.generateChildId(parent1, parent2),
        generation: Math.max(parent1.generation, parent2.generation) + 1,
        parentIds: [parent1.id, parent2.id],
        type: 'crossover',
        created: new Date(),
        code: childCode,
        metadata: childMetadata,
        crossoverStrategy: crossoverInstructions.strategy,
        metrics: {
          performance: 0,
          reliability: 0,
          functionality: 0,
          safety: 0
        },
        fitness: 0
      };
      
      console.log(chalk.green(`✅ Created crossover child: ${child.id}`));
      return child;
      
    } catch (error) {
      throw new Error(`Crossover failed: ${error.message}`);
    }
  }

  /**
   * Combine parent code based on crossover strategy
   */
  async combineParentCode(parent1, parent2, instructions) {
    const combinedCode = {
      orchestrator: '',
      crossFlow: '',
      workflows: {}
    };

    switch (instructions.strategy) {
      case 'single_point':
        return this.singlePointCrossover(parent1.code, parent2.code, instructions);
      
      case 'two_point':
        return this.twoPointCrossover(parent1.code, parent2.code, instructions);
      
      case 'uniform':
        return this.uniformCrossover(parent1.code, parent2.code, instructions);
      
      case 'semantic':
        return this.semanticCrossover(parent1, parent2, instructions);
      
      default:
        throw new Error(`Unknown crossover strategy: ${instructions.strategy}`);
    }
  }

  /**
   * Single-point crossover
   */
  singlePointCrossover(code1, code2, instructions) {
    const combinedCode = {
      orchestrator: instructions.orchestratorFrom === 'parent1' ? code1.orchestrator : code2.orchestrator,
      crossFlow: instructions.crossFlowFrom === 'parent1' ? code1.crossFlow : code2.crossFlow,
      workflows: {}
    };

    // Combine workflows
    const allWorkflows = new Set([
      ...Object.keys(code1.workflows || {}),
      ...Object.keys(code2.workflows || {})
    ]);

    for (const workflowName of allWorkflows) {
      if (instructions.workflowsFrom === 'parent1' && code1.workflows[workflowName]) {
        combinedCode.workflows[workflowName] = code1.workflows[workflowName];
      } else if (code2.workflows[workflowName]) {
        combinedCode.workflows[workflowName] = code2.workflows[workflowName];
      } else if (code1.workflows[workflowName]) {
        combinedCode.workflows[workflowName] = code1.workflows[workflowName];
      }
    }

    return combinedCode;
  }

  /**
   * Two-point crossover
   */
  twoPointCrossover(code1, code2, instructions) {
    // For two-point crossover, we'll split each code file at two points
    const combinedCode = {
      orchestrator: this.twoPointCombineCode(
        code1.orchestrator,
        code2.orchestrator,
        instructions.startPoint,
        instructions.endPoint
      ),
      crossFlow: this.twoPointCombineCode(
        code1.crossFlow,
        code2.crossFlow,
        instructions.startPoint,
        instructions.endPoint
      ),
      workflows: {}
    };

    // Combine workflows using two-point strategy
    const allWorkflows = new Set([
      ...Object.keys(code1.workflows || {}),
      ...Object.keys(code2.workflows || {})
    ]);

    for (const workflowName of allWorkflows) {
      const workflow1 = code1.workflows[workflowName] || '';
      const workflow2 = code2.workflows[workflowName] || '';
      
      combinedCode.workflows[workflowName] = this.twoPointCombineCode(
        workflow1,
        workflow2,
        instructions.startPoint,
        instructions.endPoint
      );
    }

    return combinedCode;
  }

  /**
   * Combine two code strings using two-point crossover
   */
  twoPointCombineCode(code1, code2, startPoint, endPoint) {
    const lines1 = code1.split('\n');
    const lines2 = code2.split('\n');
    
    const maxLines = Math.max(lines1.length, lines2.length);
    const startLine = Math.floor(startPoint * maxLines);
    const endLine = Math.floor(endPoint * maxLines);
    
    const combinedLines = [];
    
    for (let i = 0; i < maxLines; i++) {
      if (i >= startLine && i < endLine) {
        // Use parent2 in the middle section
        combinedLines.push(lines2[i] || '');
      } else {
        // Use parent1 in the outer sections
        combinedLines.push(lines1[i] || '');
      }
    }
    
    return combinedLines.join('\n');
  }

  /**
   * Uniform crossover
   */
  uniformCrossover(code1, code2, instructions) {
    const combinedCode = {
      orchestrator: this.uniformCombineCode(code1.orchestrator, code2.orchestrator),
      crossFlow: this.uniformCombineCode(code1.crossFlow, code2.crossFlow),
      workflows: {}
    };

    // Combine workflows uniformly
    const allWorkflows = new Set([
      ...Object.keys(code1.workflows || {}),
      ...Object.keys(code2.workflows || {})
    ]);

    for (const workflowName of allWorkflows) {
      const workflow1 = code1.workflows[workflowName] || '';
      const workflow2 = code2.workflows[workflowName] || '';
      
      combinedCode.workflows[workflowName] = this.uniformCombineCode(workflow1, workflow2);
    }

    return combinedCode;
  }

  /**
   * Combine two code strings using uniform crossover
   */
  uniformCombineCode(code1, code2) {
    const lines1 = code1.split('\n');
    const lines2 = code2.split('\n');
    
    const maxLines = Math.max(lines1.length, lines2.length);
    const combinedLines = [];
    
    for (let i = 0; i < maxLines; i++) {
      // Randomly choose from parent1 or parent2 for each line
      if (Math.random() < 0.5) {
        combinedLines.push(lines1[i] || '');
      } else {
        combinedLines.push(lines2[i] || '');
      }
    }
    
    return combinedLines.join('\n');
  }

  /**
   * Semantic crossover based on performance characteristics
   */
  semanticCrossover(parent1, parent2, instructions) {
    const combinedCode = {
      orchestrator: '',
      crossFlow: '',
      workflows: {}
    };

    // Choose orchestrator based on performance metrics
    if (parent1.metrics.performance > parent2.metrics.performance) {
      combinedCode.orchestrator = parent1.code.orchestrator;
    } else {
      combinedCode.orchestrator = parent2.code.orchestrator;
    }

    // Choose cross-flow engine based on reliability
    if (parent1.metrics.reliability > parent2.metrics.reliability) {
      combinedCode.crossFlow = parent1.code.crossFlow;
    } else {
      combinedCode.crossFlow = parent2.code.crossFlow;
    }

    // Combine workflows based on functionality
    const workflows1 = parent1.code.workflows || {};
    const workflows2 = parent2.code.workflows || {};
    
    const allWorkflows = new Set([...Object.keys(workflows1), ...Object.keys(workflows2)]);
    
    for (const workflowName of allWorkflows) {
      // Choose the workflow from the parent with better functionality
      if (parent1.metrics.functionality > parent2.metrics.functionality && workflows1[workflowName]) {
        combinedCode.workflows[workflowName] = workflows1[workflowName];
      } else if (workflows2[workflowName]) {
        combinedCode.workflows[workflowName] = workflows2[workflowName];
      } else if (workflows1[workflowName]) {
        combinedCode.workflows[workflowName] = workflows1[workflowName];
      }
    }

    return combinedCode;
  }

  /**
   * Combine metadata from parents
   */
  combineMetadata(parent1, parent2, instructions) {
    const combinedMetadata = {
      description: `Crossover child of ${parent1.id} and ${parent2.id}`,
      version: '1.0.0',
      capabilities: [],
      crossoverDetails: {
        strategy: instructions.strategy,
        parents: [
          {
            id: parent1.id,
            fitness: parent1.fitness,
            generation: parent1.generation
          },
          {
            id: parent2.id,
            fitness: parent2.fitness,
            generation: parent2.generation
          }
        ]
      }
    };

    // Combine capabilities from both parents
    const capabilities1 = parent1.metadata?.capabilities || [];
    const capabilities2 = parent2.metadata?.capabilities || [];
    combinedMetadata.capabilities = [...new Set([...capabilities1, ...capabilities2])];

    // Inherit other metadata based on fitness
    const betterParent = parent1.fitness > parent2.fitness ? parent1 : parent2;
    if (betterParent.metadata) {
      combinedMetadata.version = betterParent.metadata.version || '1.0.0';
      combinedMetadata.inheritedFrom = betterParent.id;
    }

    return combinedMetadata;
  }

  /**
   * Generate unique ID for child agent
   */
  generateChildId(parent1, parent2) {
    const { v4: uuidv4 } = require('uuid');
    return uuidv4();
  }

  /**
   * Validate crossover result
   */
  async validateCrossover(child, parent1, parent2) {
    const validationResults = {
      isValid: true,
      issues: [],
      warnings: []
    };

    try {
      // Check if child has all required code components
      if (!child.code.orchestrator) {
        validationResults.issues.push('Missing orchestrator code');
        validationResults.isValid = false;
      }

      if (!child.code.crossFlow) {
        validationResults.issues.push('Missing cross-flow engine code');
        validationResults.isValid = false;
      }

      // Check for syntax errors (basic check)
      await this.checkSyntax(child.code.orchestrator, 'orchestrator');
      await this.checkSyntax(child.code.crossFlow, 'crossFlow');

      // Check if child inherits capabilities from parents
      const parentCapabilities = new Set([
        ...(parent1.metadata?.capabilities || []),
        ...(parent2.metadata?.capabilities || [])
      ]);
      
      const childCapabilities = new Set(child.metadata?.capabilities || []);
      
      if (childCapabilities.size === 0 && parentCapabilities.size > 0) {
        validationResults.warnings.push('Child has no capabilities despite parents having capabilities');
      }

      // Check generation consistency
      const expectedGeneration = Math.max(parent1.generation, parent2.generation) + 1;
      if (child.generation !== expectedGeneration) {
        validationResults.warnings.push(`Generation mismatch: expected ${expectedGeneration}, got ${child.generation}`);
      }

    } catch (error) {
      validationResults.issues.push(`Validation error: ${error.message}`);
      validationResults.isValid = false;
    }

    return validationResults;
  }

  /**
   * Basic syntax check for JavaScript code
   */
  async checkSyntax(code, componentName) {
    try {
      // Basic check - try to parse as JavaScript
      new Function(code);
    } catch (error) {
      throw new Error(`Syntax error in ${componentName}: ${error.message}`);
    }
  }

  /**
   * Get crossover statistics
   */
  getCrossoverStats(crossoverHistory) {
    const stats = {
      totalCrossovers: crossoverHistory.length,
      strategyCounts: {},
      successRate: 0,
      averageChildFitness: 0,
      bestChildFitness: 0
    };

    if (crossoverHistory.length === 0) {
      return stats;
    }

    let successfulCrossovers = 0;
    let totalChildFitness = 0;
    let bestFitness = 0;

    for (const crossover of crossoverHistory) {
      // Count strategies
      const strategy = crossover.strategy || 'unknown';
      stats.strategyCounts[strategy] = (stats.strategyCounts[strategy] || 0) + 1;

      // Calculate success metrics
      if (crossover.child && crossover.child.fitness !== undefined) {
        successfulCrossovers++;
        totalChildFitness += crossover.child.fitness;
        bestFitness = Math.max(bestFitness, crossover.child.fitness);
      }
    }

    stats.successRate = successfulCrossovers / crossoverHistory.length;
    stats.averageChildFitness = successfulCrossovers > 0 ? totalChildFitness / successfulCrossovers : 0;
    stats.bestChildFitness = bestFitness;

    return stats;
  }

  /**
   * Analyze crossover effectiveness
   */
  analyzeCrossoverEffectiveness(parent1, parent2, child) {
    const analysis = {
      fitnessImprovement: 0,
      inheritedBestTraits: false,
      novelty: 0,
      recommendation: 'continue'
    };

    const parentBestFitness = Math.max(parent1.fitness, parent2.fitness);
    const parentAvgFitness = (parent1.fitness + parent2.fitness) / 2;

    // Calculate fitness improvement
    analysis.fitnessImprovement = child.fitness - parentAvgFitness;

    // Check if child inherited best traits
    analysis.inheritedBestTraits = child.fitness > parentBestFitness;

    // Calculate novelty (simplified)
    const parentSimilarity = this.calculateCodeSimilarity(parent1.code, parent2.code);
    const childParent1Similarity = this.calculateCodeSimilarity(child.code, parent1.code);
    const childParent2Similarity = this.calculateCodeSimilarity(child.code, parent2.code);
    
    analysis.novelty = 1 - Math.max(childParent1Similarity, childParent2Similarity);

    // Generate recommendation
    if (analysis.fitnessImprovement > 0.1) {
      analysis.recommendation = 'highly_effective';
    } else if (analysis.fitnessImprovement > 0) {
      analysis.recommendation = 'effective';
    } else if (analysis.fitnessImprovement > -0.1) {
      analysis.recommendation = 'neutral';
    } else {
      analysis.recommendation = 'ineffective';
    }

    return analysis;
  }

  /**
   * Calculate similarity between two code objects (simplified)
   */
  calculateCodeSimilarity(code1, code2) {
    // Simple similarity based on code length and structure
    const orchestratorSim = this.calculateStringSimilarity(code1.orchestrator, code2.orchestrator);
    const crossFlowSim = this.calculateStringSimilarity(code1.crossFlow, code2.crossFlow);
    
    return (orchestratorSim + crossFlowSim) / 2;
  }

  /**
   * Calculate similarity between two strings
   */
  calculateStringSimilarity(str1, str2) {
    const len1 = str1.length;
    const len2 = str2.length;
    
    if (len1 === 0 && len2 === 0) return 1;
    if (len1 === 0 || len2 === 0) return 0;
    
    // Simple similarity based on length ratio
    const lengthSimilarity = 1 - Math.abs(len1 - len2) / Math.max(len1, len2);
    
    return lengthSimilarity;
  }
}

module.exports = Crossover;

# 🌐 EcoStamp - Cross-Browser Installation Guide

## 🎯 Universal Browser Support

EcoStamp now works with ALL major browsers! Choose your browser below:

---

## 🟢 Chrome (Recommended)

### Method 1: Chrome Web Store (Coming Soon)
1. Visit Chrome Web Store
2. Search "EcoStamp"
3. Click "Add to Chrome"

### Method 2: Manual Installation
1. Download `ecostamp-extension` folder
2. Open Chrome → `chrome://extensions/`
3. Enable "Developer mode" (top-right toggle)
4. Click "Load unpacked"
5. Select the `ecostamp-extension` folder
6. ✅ Done! EcoStamp is now active

---

## 🔴 Opera

### Installation Steps:
1. Download `ecostamp-extension` folder
2. Copy `manifest-opera.json` to `manifest.json`
3. Open Opera → `opera://extensions/`
4. Enable "Developer mode"
5. Click "Load unpacked"
6. Select the `ecostamp-extension` folder
7. ✅ Done! EcoStamp works in Opera

### Opera-Specific Notes:
- Uses Chromium engine, so full compatibility
- All features work identically to Chrome
- Automatic updates supported

---

## 🔵 Microsoft Edge

### Installation Steps:
1. Download `ecostamp-extension` folder
2. Open Edge → `edge://extensions/`
3. Enable "Developer mode" (left sidebar)
4. Click "Load unpacked"
5. Select the `ecostamp-extension` folder
6. ✅ Done! EcoStamp works in Edge

### Edge-Specific Notes:
- Full Manifest V3 support
- All features work perfectly
- Enterprise deployment supported

---

## 🦊 Firefox

### Installation Steps:
1. Download `ecostamp-extension` folder
2. Copy `manifest-firefox.json` to `manifest.json`
3. Open Firefox → `about:debugging`
4. Click "This Firefox"
5. Click "Load Temporary Add-on"
6. Select `manifest.json` from the folder
7. ✅ Done! EcoStamp works in Firefox

### Firefox-Specific Notes:
- Uses Manifest V2 for compatibility
- All core features supported
- Temporary installation (reloads on restart)
- For permanent: Submit to Firefox Add-ons

---

## 🦁 Brave Browser

### Installation Steps:
1. Download `ecostamp-extension` folder
2. Open Brave → `brave://extensions/`
3. Enable "Developer mode"
4. Click "Load unpacked"
5. Select the `ecostamp-extension` folder
6. ✅ Done! EcoStamp works in Brave

### Brave-Specific Notes:
- Chromium-based, full compatibility
- Privacy features don't interfere
- All tracking features work

---

## 🍎 Safari (macOS)

### Installation Steps:
1. Download `ecostamp-extension` folder
2. Convert to Safari Web Extension:
   ```bash
   xcrun safari-web-extension-converter ecostamp-extension
   ```
3. Open generated Xcode project
4. Build and run
5. Enable in Safari → Preferences → Extensions

### Safari-Specific Notes:
- Requires Xcode for conversion
- Some features may need adaptation
- macOS 10.14+ required

---

## 🔧 Verification Steps

After installation in ANY browser:

1. **Check Extension Icon**: Look for 🌱 EcoStamp icon in toolbar
2. **Visit AI Platform**: Go to ChatGPT, Claude, or Gemini
3. **Ask a Question**: Send any message to the AI
4. **Look for Footer**: EcoStamp footer should appear below response:

```
──────────────────────────────────────────────
🕓 01/02/2025, 15:45:00 UTC  |  🔐 SHA-256: a1b2...c3d4
🌿 Eco-Level: 3/5 Leaves 🌿🌿🌿🍂🍂  (0.45 Wh · 12.8 mL)
Powered by EcoStamp — GitHub              ChatGPT • gpt-4
```

---

## 🌍 Supported AI Platforms

EcoStamp works with ALL these platforms in ANY browser:

- ✅ **ChatGPT** (chat.openai.com, chatgpt.com)
- ✅ **Claude** (claude.ai)
- ✅ **Gemini** (gemini.google.com, bard.google.com)
- ✅ **Perplexity** (perplexity.ai)
- ✅ **Poe** (poe.com)
- ✅ **Character.AI** (character.ai)
- ✅ **You.com** (you.com)
- ✅ **Hugging Face** (huggingface.co)
- ✅ **ANY AI Platform** (universal detection)

---

## 🛠️ Troubleshooting

### Extension Not Showing?
1. Refresh the AI platform page
2. Check if extension is enabled
3. Try asking a longer question
4. Check browser console for errors

### Wrong Browser Manifest?
- **Chrome/Edge/Opera/Brave**: Use `manifest.json`
- **Firefox**: Copy `manifest-firefox.json` to `manifest.json`
- **Opera**: Copy `manifest-opera.json` to `manifest.json`

### Platform Not Detected?
- EcoStamp has universal fallback detection
- Should work with ANY AI platform
- Report issues on GitHub

---

## 📱 Mobile Browsers

### Android Chrome/Firefox:
- Extensions not supported on mobile
- Use desktop browser for EcoStamp

### iOS Safari:
- Limited extension support
- Desktop recommended

---

## 🔄 Updates

### Automatic Updates:
- **Chrome Web Store**: Automatic
- **Firefox Add-ons**: Automatic
- **Manual Installation**: Download new version

### Manual Update:
1. Download latest version
2. Remove old extension
3. Install new version following steps above

---

## 🌱 Ready to Track AI Environmental Impact!

EcoStamp now works in **ALL major browsers** and tracks environmental impact across **ALL AI platforms**. Choose your browser, follow the installation steps, and start seeing the real cost of AI conversations!

**🌍 Making AI environmental impact visible everywhere!**

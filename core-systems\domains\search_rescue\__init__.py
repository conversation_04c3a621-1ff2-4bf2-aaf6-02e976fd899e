"""
Search and Rescue Drone Domain Module

This module provides specialized feedback loop components for Search and Rescue
drone operations, including visual detection, reference item classification,
and mission outcome validation.
"""

from .visual_detector import VisualDetectionInterpreter
from .reference_matcher import ReferenceItemMatcher
from .mission_validator import SearchRescueValidator
from .side_pocket_manager import SidePocketManager
from .domain import SearchRescueDomain

__all__ = [
    'VisualDetectionInterpreter',
    'ReferenceItemMatcher',
    'SearchRescueValidator',
    'SidePocketManager',
    'SearchRescueDomain'
]

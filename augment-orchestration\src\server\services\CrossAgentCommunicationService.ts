/**
 * Cross-Agent Communication Service
 * 
 * Core Gap 4: Standardized protocol for secure communication between different
 * AI agents with authentication, message routing, and conflict resolution.
 */

import { EventEmitter } from 'events';
import { PrismaClient } from '@prisma/client';
import WebSocket from 'ws';
import { logger } from '../utils/logger';
import {
  Message,
  MessageType,
  AgentIdentity,
  AgentRole,
  AgentStatus,
  Priority,
  HandshakeRequest,
  HandshakeResponse,
  TaskRequest,
  TaskResponse,
  TaskUpdate,
  ResourceRequest,
  ResourceGrant,
  ConflictReport,
  ConflictResolution,
  ConflictType,
  KnowledgeShare,
  StateSync,
  CapabilityAnnouncement,
  SecurityAlert,
  CommunicationStats,
  RoutingTable,
  Route,
  ProtocolConfig,
  CommunicationEvent,
  CommunicationError,
  AuthenticationError,
  RoutingError,
  ConflictResolutionError,
  PROTOCOL_CONSTANTS,
  CommunicationUtils
} from '../../shared/types/CrossAgentCommunication';

export class CrossAgentCommunicationService extends EventEmitter {
  private prisma: PrismaClient;
  private config: ProtocolConfig;
  private localAgent: AgentIdentity;
  private connectedAgents: Map<string, AgentConnection> = new Map();
  private messageQueue: Map<string, Message[]> = new Map();
  private routingTable: RoutingTable;
  private conflictResolver: ConflictResolver;
  private securityManager: SecurityManager;
  private stats: CommunicationStats;

  constructor(
    prisma: PrismaClient,
    localAgent: AgentIdentity,
    config?: Partial<ProtocolConfig>
  ) {
    super();
    this.prisma = prisma;
    this.localAgent = localAgent;
    this.config = this.initializeConfig(config);
    this.routingTable = { routes: [], lastUpdated: new Date() };
    this.conflictResolver = new ConflictResolver(this);
    this.securityManager = new SecurityManager(this.config.security);
    this.stats = this.initializeStats();

    this.setupEventHandlers();
  }

  /**
   * Initialize connection with another agent
   */
  async connectToAgent(agentEndpoint: string, agentId?: string): Promise<boolean> {
    try {
      const ws = new WebSocket(agentEndpoint);
      const connection = new AgentConnection(ws, agentId);

      ws.on('open', () => {
        logger.info('WebSocket connection established', { endpoint: agentEndpoint });
        this.initiateHandshake(connection);
      });

      ws.on('message', (data: WebSocket.Data) => {
        this.handleIncomingMessage(connection, data);
      });

      ws.on('close', () => {
        logger.info('WebSocket connection closed', { agentId: connection.agentId });
        this.handleConnectionClosed(connection);
      });

      ws.on('error', (error) => {
        logger.error('WebSocket error', { error: error.message, agentId: connection.agentId });
        this.handleConnectionError(connection, error);
      });

      return true;

    } catch (error) {
      logger.error('Failed to connect to agent', { endpoint: agentEndpoint, error: error.message });
      return false;
    }
  }

  /**
   * Send message to another agent
   */
  async sendMessage(message: Message): Promise<boolean> {
    try {
      // Validate message
      if (!CommunicationUtils.validateMessage(message)) {
        throw new CommunicationError('Invalid message format', 'INVALID_MESSAGE');
      }

      // Check if message is expired
      if (CommunicationUtils.isExpired(message)) {
        throw new CommunicationError('Message expired', 'MESSAGE_EXPIRED');
      }

      // Sign message
      message.signature = await this.securityManager.signMessage(message);

      // Find route to destination
      const route = await this.findRoute(message.receiverId);
      if (!route) {
        throw new RoutingError('No route to destination', message.id);
      }

      // Get connection
      const connection = this.connectedAgents.get(route.nextHop);
      if (!connection || !connection.isConnected()) {
        throw new CommunicationError('Connection not available', 'CONNECTION_UNAVAILABLE');
      }

      // Send message
      await connection.sendMessage(message);

      // Update stats
      this.updateStats('MESSAGE_SENT', message);

      // Emit event
      this.emit('messageSent', {
        eventId: CommunicationUtils.generateMessageId(),
        type: 'MESSAGE_SENT',
        timestamp: new Date(),
        agentId: message.receiverId,
        details: { messageId: message.id, type: message.type },
        severity: 'INFO'
      });

      logger.debug('Message sent successfully', {
        messageId: message.id,
        type: message.type,
        receiverId: message.receiverId
      });

      return true;

    } catch (error) {
      logger.error('Failed to send message', {
        messageId: message.id,
        error: error.message
      });

      this.emit('error', error);
      return false;
    }
  }

  /**
   * Send task request to another agent
   */
  async sendTaskRequest(receiverId: string, taskRequest: TaskRequest): Promise<TaskResponse | null> {
    const message: Message = {
      id: CommunicationUtils.generateMessageId(),
      type: MessageType.TASK_REQUEST,
      senderId: this.localAgent.id,
      receiverId,
      timestamp: new Date(),
      priority: taskRequest.priority,
      payload: taskRequest,
      signature: '',
      correlationId: CommunicationUtils.generateCorrelationId(),
      ttl: PROTOCOL_CONSTANTS.DEFAULT_TIMEOUT,
      metadata: {
        encrypted: this.config.security.encryptionRequired,
        compressed: false,
        contentType: 'application/json',
        encoding: 'utf-8',
        checksum: CommunicationUtils.calculateChecksum(taskRequest)
      }
    };

    const sent = await this.sendMessage(message);
    if (!sent) return null;

    // Wait for response
    return this.waitForResponse<TaskResponse>(message.correlationId, PROTOCOL_CONSTANTS.DEFAULT_TIMEOUT);
  }

  /**
   * Request resource access from another agent
   */
  async requestResource(receiverId: string, resourceRequest: ResourceRequest): Promise<ResourceGrant | null> {
    const message: Message = {
      id: CommunicationUtils.generateMessageId(),
      type: MessageType.RESOURCE_REQUEST,
      senderId: this.localAgent.id,
      receiverId,
      timestamp: new Date(),
      priority: resourceRequest.priority,
      payload: resourceRequest,
      signature: '',
      correlationId: CommunicationUtils.generateCorrelationId(),
      ttl: PROTOCOL_CONSTANTS.DEFAULT_TIMEOUT,
      metadata: {
        encrypted: this.config.security.encryptionRequired,
        compressed: false,
        contentType: 'application/json',
        encoding: 'utf-8',
        checksum: CommunicationUtils.calculateChecksum(resourceRequest)
      }
    };

    const sent = await this.sendMessage(message);
    if (!sent) return null;

    return this.waitForResponse<ResourceGrant>(message.correlationId, PROTOCOL_CONSTANTS.DEFAULT_TIMEOUT);
  }

  /**
   * Share knowledge with other agents
   */
  async shareKnowledge(knowledge: KnowledgeShare, targetAgents?: string[]): Promise<boolean> {
    const recipients = targetAgents || knowledge.relevantAgents || Array.from(this.connectedAgents.keys());
    let successCount = 0;

    for (const agentId of recipients) {
      const message: Message = {
        id: CommunicationUtils.generateMessageId(),
        type: MessageType.KNOWLEDGE_SHARE,
        senderId: this.localAgent.id,
        receiverId: agentId,
        timestamp: new Date(),
        priority: Priority.MEDIUM,
        payload: knowledge,
        signature: '',
        metadata: {
          encrypted: false,
          compressed: true,
          contentType: 'application/json',
          encoding: 'utf-8',
          checksum: CommunicationUtils.calculateChecksum(knowledge)
        }
      };

      if (await this.sendMessage(message)) {
        successCount++;
      }
    }

    logger.info('Knowledge shared', {
      knowledgeId: knowledge.knowledgeId,
      targetAgents: recipients.length,
      successfulShares: successCount
    });

    return successCount > 0;
  }

  /**
   * Announce capabilities to other agents
   */
  async announceCapabilities(capabilities: CapabilityAnnouncement): Promise<void> {
    const connectedAgentIds = Array.from(this.connectedAgents.keys());

    for (const agentId of connectedAgentIds) {
      const message: Message = {
        id: CommunicationUtils.generateMessageId(),
        type: MessageType.CAPABILITY_ANNOUNCE,
        senderId: this.localAgent.id,
        receiverId: agentId,
        timestamp: new Date(),
        priority: Priority.LOW,
        payload: capabilities,
        signature: '',
        metadata: {
          encrypted: false,
          compressed: false,
          contentType: 'application/json',
          encoding: 'utf-8',
          checksum: CommunicationUtils.calculateChecksum(capabilities)
        }
      };

      await this.sendMessage(message);
    }

    logger.info('Capabilities announced', {
      capabilityCount: capabilities.capabilities.length,
      targetAgents: connectedAgentIds.length
    });
  }

  /**
   * Report conflict to conflict resolution system
   */
  async reportConflict(conflict: ConflictReport): Promise<ConflictResolution | null> {
    logger.warn('Conflict detected', {
      conflictId: conflict.conflictId,
      type: conflict.type,
      severity: conflict.severity,
      involvedAgents: conflict.involvedAgents
    });

    // Use conflict resolver to handle the conflict
    const resolution = await this.conflictResolver.resolveConflict(conflict);

    if (resolution) {
      // Notify involved agents about the resolution
      for (const agentId of conflict.involvedAgents) {
        const message: Message = {
          id: CommunicationUtils.generateMessageId(),
          type: MessageType.CONFLICT_RESOLUTION,
          senderId: this.localAgent.id,
          receiverId: agentId,
          timestamp: new Date(),
          priority: Priority.HIGH,
          payload: resolution,
          signature: '',
          metadata: {
            encrypted: false,
            compressed: false,
            contentType: 'application/json',
            encoding: 'utf-8',
            checksum: CommunicationUtils.calculateChecksum(resolution)
          }
        };

        await this.sendMessage(message);
      }
    }

    return resolution;
  }

  /**
   * Get communication statistics
   */
  getCommunicationStats(): CommunicationStats {
    return { ...this.stats };
  }

  /**
   * Get connected agents
   */
  getConnectedAgents(): AgentIdentity[] {
    return Array.from(this.connectedAgents.values())
      .filter(conn => conn.isConnected())
      .map(conn => conn.agentIdentity)
      .filter(identity => identity !== null) as AgentIdentity[];
  }

  /**
   * Update routing table
   */
  updateRoutingTable(routes: Route[]): void {
    this.routingTable = {
      routes,
      lastUpdated: new Date()
    };

    logger.info('Routing table updated', {
      routeCount: routes.length
    });
  }

  /**
   * Private helper methods
   */
  private initializeConfig(config?: Partial<ProtocolConfig>): ProtocolConfig {
    return {
      version: PROTOCOL_CONSTANTS.VERSION,
      features: [],
      security: {
        encryptionRequired: false,
        signatureRequired: true,
        authenticationMethod: 'TOKEN',
        keyRotationInterval: ********, // 24 hours
        maxSessionDuration: PROTOCOL_CONSTANTS.SESSION_TIMEOUT,
        allowedCiphers: ['AES-256-GCM']
      },
      routing: {
        strategy: 'DIRECT',
        maxHops: 3,
        routingTableTtl: PROTOCOL_CONSTANTS.ROUTING_TABLE_TTL,
        loadBalancing: false,
        failoverEnabled: true
      },
      reliability: {
        maxRetries: PROTOCOL_CONSTANTS.MAX_RETRIES,
        retryBackoff: 'EXPONENTIAL',
        timeoutMs: PROTOCOL_CONSTANTS.DEFAULT_TIMEOUT,
        heartbeatInterval: PROTOCOL_CONSTANTS.HEARTBEAT_INTERVAL,
        duplicateDetection: true,
        orderingGuarantee: false
      },
      performance: {
        maxConcurrentConnections: 100,
        messageQueueSize: 1000,
        compressionEnabled: true,
        batchingEnabled: false,
        batchSize: PROTOCOL_CONSTANTS.BATCH_SIZE,
        batchTimeout: PROTOCOL_CONSTANTS.BATCH_TIMEOUT
      },
      ...config
    };
  }

  private initializeStats(): CommunicationStats {
    return {
      totalMessages: 0,
      messagesByType: {} as Record<MessageType, number>,
      averageLatency: 0,
      errorRate: 0,
      throughput: 0,
      activeConnections: 0,
      bandwidthUsage: 0,
      lastUpdated: new Date()
    };
  }

  private setupEventHandlers(): void {
    // Set up periodic tasks
    setInterval(() => {
      this.performHealthCheck();
    }, this.config.reliability.heartbeatInterval);

    setInterval(() => {
      this.cleanupExpiredMessages();
    }, 60000); // Every minute

    setInterval(() => {
      this.updateRoutingTable([]);
    }, this.config.routing.routingTableTtl);
  }

  private async initiateHandshake(connection: AgentConnection): Promise<void> {
    const handshakeRequest: HandshakeRequest = {
      agentIdentity: this.localAgent,
      protocolVersion: this.config.version,
      supportedFeatures: this.config.features.map(f => f.name),
      securityLevel: 'STANDARD',
      challenge: await this.securityManager.generateChallenge()
    };

    const message: Message = {
      id: CommunicationUtils.generateMessageId(),
      type: MessageType.HANDSHAKE,
      senderId: this.localAgent.id,
      receiverId: 'unknown',
      timestamp: new Date(),
      priority: Priority.HIGH,
      payload: handshakeRequest,
      signature: '',
      metadata: {
        encrypted: false,
        compressed: false,
        contentType: 'application/json',
        encoding: 'utf-8',
        checksum: CommunicationUtils.calculateChecksum(handshakeRequest)
      }
    };

    await connection.sendMessage(message);
  }

  private async handleIncomingMessage(connection: AgentConnection, data: WebSocket.Data): Promise<void> {
    try {
      const message: Message = JSON.parse(data.toString());

      // Validate message signature
      if (this.config.security.signatureRequired) {
        const isValid = await this.securityManager.verifySignature(message);
        if (!isValid) {
          throw new AuthenticationError('Invalid message signature');
        }
      }

      // Handle different message types
      switch (message.type) {
        case MessageType.HANDSHAKE:
          await this.handleHandshake(connection, message);
          break;
        case MessageType.TASK_REQUEST:
          await this.handleTaskRequest(connection, message);
          break;
        case MessageType.RESOURCE_REQUEST:
          await this.handleResourceRequest(connection, message);
          break;
        case MessageType.CONFLICT_DETECTED:
          await this.handleConflictReport(connection, message);
          break;
        default:
          await this.handleGenericMessage(connection, message);
      }

      // Update stats
      this.updateStats('MESSAGE_RECEIVED', message);

    } catch (error) {
      logger.error('Failed to handle incoming message', {
        error: error.message,
        agentId: connection.agentId
      });
    }
  }

  private async handleHandshake(connection: AgentConnection, message: Message): Promise<void> {
    const request = message.payload as HandshakeRequest;
    
    // Verify challenge if present
    let challengeResponse: string | undefined;
    if (request.challenge) {
      challengeResponse = await this.securityManager.respondToChallenge(request.challenge);
    }

    const response: HandshakeResponse = {
      accepted: true,
      agentIdentity: this.localAgent,
      protocolVersion: this.config.version,
      supportedFeatures: this.config.features.map(f => f.name),
      securityLevel: 'STANDARD',
      challengeResponse,
      sessionToken: await this.securityManager.generateSessionToken()
    };

    // Store agent identity
    connection.agentIdentity = request.agentIdentity;
    connection.agentId = request.agentIdentity.id;
    this.connectedAgents.set(request.agentIdentity.id, connection);

    // Send response
    const responseMessage: Message = {
      id: CommunicationUtils.generateMessageId(),
      type: MessageType.ACKNOWLEDGMENT,
      senderId: this.localAgent.id,
      receiverId: request.agentIdentity.id,
      timestamp: new Date(),
      priority: Priority.HIGH,
      payload: response,
      signature: '',
      replyTo: message.id,
      metadata: {
        encrypted: false,
        compressed: false,
        contentType: 'application/json',
        encoding: 'utf-8',
        checksum: CommunicationUtils.calculateChecksum(response)
      }
    };

    await connection.sendMessage(responseMessage);

    logger.info('Handshake completed', {
      agentId: request.agentIdentity.id,
      agentName: request.agentIdentity.name,
      role: request.agentIdentity.role
    });
  }

  private async handleTaskRequest(connection: AgentConnection, message: Message): Promise<void> {
    // This would be implemented based on the specific agent's capabilities
    // For now, just acknowledge receipt
    const response: TaskResponse = {
      taskId: message.payload.taskId,
      accepted: false,
      reason: 'Not implemented'
    };

    await this.sendResponse(message, MessageType.TASK_RESPONSE, response);
  }

  private async handleResourceRequest(connection: AgentConnection, message: Message): Promise<void> {
    // This would be implemented based on the specific agent's resource management
    const response: ResourceGrant = {
      resourceId: message.payload.resourceId,
      granted: false,
      reason: 'Not implemented'
    };

    await this.sendResponse(message, MessageType.RESOURCE_GRANT, response);
  }

  private async handleConflictReport(connection: AgentConnection, message: Message): Promise<void> {
    const conflict = message.payload as ConflictReport;
    const resolution = await this.conflictResolver.resolveConflict(conflict);
    
    if (resolution) {
      await this.sendResponse(message, MessageType.CONFLICT_RESOLUTION, resolution);
    }
  }

  private async handleGenericMessage(connection: AgentConnection, message: Message): Promise<void> {
    // Emit event for other parts of the system to handle
    this.emit('messageReceived', {
      eventId: CommunicationUtils.generateMessageId(),
      type: 'MESSAGE_RECEIVED',
      timestamp: new Date(),
      agentId: message.senderId,
      details: { messageId: message.id, type: message.type, payload: message.payload },
      severity: 'INFO'
    });
  }

  private async sendResponse(originalMessage: Message, responseType: MessageType, payload: any): Promise<void> {
    const response: Message = {
      id: CommunicationUtils.generateMessageId(),
      type: responseType,
      senderId: this.localAgent.id,
      receiverId: originalMessage.senderId,
      timestamp: new Date(),
      priority: originalMessage.priority,
      payload,
      signature: '',
      replyTo: originalMessage.id,
      correlationId: originalMessage.correlationId,
      metadata: {
        encrypted: false,
        compressed: false,
        contentType: 'application/json',
        encoding: 'utf-8',
        checksum: CommunicationUtils.calculateChecksum(payload)
      }
    };

    await this.sendMessage(response);
  }

  private async findRoute(destinationId: string): Promise<Route | null> {
    // Simple direct routing for now
    if (this.connectedAgents.has(destinationId)) {
      return {
        destination: destinationId,
        nextHop: destinationId,
        cost: 1,
        latency: 10,
        reliability: 0.99,
        lastVerified: new Date()
      };
    }

    // Check routing table for multi-hop routes
    const route = this.routingTable.routes.find(r => r.destination === destinationId);
    return route || null;
  }

  private async waitForResponse<T>(correlationId: string, timeout: number): Promise<T | null> {
    return new Promise((resolve) => {
      const timeoutId = setTimeout(() => {
        this.removeListener('messageReceived', responseHandler);
        resolve(null);
      }, timeout);

      const responseHandler = (event: CommunicationEvent) => {
        if (event.details.correlationId === correlationId) {
          clearTimeout(timeoutId);
          this.removeListener('messageReceived', responseHandler);
          resolve(event.details.payload);
        }
      };

      this.on('messageReceived', responseHandler);
    });
  }

  private handleConnectionClosed(connection: AgentConnection): void {
    if (connection.agentId) {
      this.connectedAgents.delete(connection.agentId);
      this.stats.activeConnections = this.connectedAgents.size;
    }
  }

  private handleConnectionError(connection: AgentConnection, error: Error): void {
    logger.error('Connection error', {
      agentId: connection.agentId,
      error: error.message
    });

    this.emit('error', new CommunicationError(
      'Connection error',
      'CONNECTION_ERROR',
      connection.agentId,
      undefined,
      { originalError: error }
    ));
  }

  private updateStats(eventType: string, message: Message): void {
    this.stats.totalMessages++;
    this.stats.messagesByType[message.type] = (this.stats.messagesByType[message.type] || 0) + 1;
    this.stats.activeConnections = this.connectedAgents.size;
    this.stats.lastUpdated = new Date();
  }

  private performHealthCheck(): void {
    // Send ping to all connected agents
    const connectedAgentIds = Array.from(this.connectedAgents.keys());
    
    for (const agentId of connectedAgentIds) {
      const pingMessage: Message = {
        id: CommunicationUtils.generateMessageId(),
        type: MessageType.PING,
        senderId: this.localAgent.id,
        receiverId: agentId,
        timestamp: new Date(),
        priority: Priority.LOW,
        payload: { timestamp: Date.now() },
        signature: '',
        metadata: {
          encrypted: false,
          compressed: false,
          contentType: 'application/json',
          encoding: 'utf-8',
          checksum: ''
        }
      };

      this.sendMessage(pingMessage);
    }
  }

  private cleanupExpiredMessages(): void {
    // Clean up expired messages from queues
    for (const [agentId, messages] of this.messageQueue.entries()) {
      const validMessages = messages.filter(msg => !CommunicationUtils.isExpired(msg));
      this.messageQueue.set(agentId, validMessages);
    }
  }
}

// Helper classes
class AgentConnection {
  public agentId?: string;
  public agentIdentity?: AgentIdentity;
  public status: AgentStatus = AgentStatus.ONLINE;
  public lastActivity: Date = new Date();

  constructor(
    private ws: WebSocket,
    agentId?: string
  ) {
    this.agentId = agentId;
  }

  isConnected(): boolean {
    return this.ws.readyState === WebSocket.OPEN;
  }

  async sendMessage(message: Message): Promise<void> {
    if (!this.isConnected()) {
      throw new CommunicationError('Connection not open', 'CONNECTION_CLOSED');
    }

    this.ws.send(JSON.stringify(message));
    this.lastActivity = new Date();
  }

  close(): void {
    this.ws.close();
  }
}

class ConflictResolver {
  constructor(private communicationService: CrossAgentCommunicationService) {}

  async resolveConflict(conflict: ConflictReport): Promise<ConflictResolution | null> {
    // Simple priority-based resolution for now
    const resolution: ConflictResolution = {
      resolutionId: CommunicationUtils.generateMessageId(),
      conflictId: conflict.conflictId,
      strategy: 'PRIORITY_BASED',
      actions: conflict.involvedAgents.map((agentId, index) => ({
        agentId,
        action: index === 0 ? 'YIELD' : 'WAIT',
        timeout: 30000
      })),
      expectedOutcome: 'First agent yields, others wait',
      implementedBy: 'system'
    };

    return resolution;
  }
}

class SecurityManager {
  constructor(private config: any) {}

  async signMessage(message: Message): Promise<string> {
    // Mock signature - would use proper cryptographic signing
    return `sig_${message.id}_${Date.now()}`;
  }

  async verifySignature(message: Message): Promise<boolean> {
    // Mock verification - would use proper cryptographic verification
    return message.signature.startsWith('sig_');
  }

  async generateChallenge(): Promise<string> {
    return `challenge_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  async respondToChallenge(challenge: string): Promise<string> {
    return `response_${challenge}_${Date.now()}`;
  }

  async generateSessionToken(): Promise<string> {
    return `token_${Date.now()}_${Math.random().toString(36).substr(2, 16)}`;
  }
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EcoStamp - Universal AI Environmental Impact Tracker</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            padding: 60px 0;
        }
        
        .header h1 {
            font-size: 3.5rem;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header .subtitle {
            font-size: 1.5rem;
            margin-bottom: 30px;
            opacity: 0.9;
        }
        
        .badges {
            display: flex;
            justify-content: center;
            gap: 10px;
            flex-wrap: wrap;
            margin-bottom: 40px;
        }
        
        .badge {
            background: rgba(255,255,255,0.2);
            padding: 8px 16px;
            border-radius: 20px;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .badge:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }
        
        .main-content {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-top: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }
        
        .feature-card {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            transition: transform 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
        }
        
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 20px;
        }
        
        .browser-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .browser-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }
        
        .browser-card.supported {
            border-color: #28a745;
            background: #d4edda;
        }
        
        .download-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            border-radius: 15px;
            text-align: center;
            margin: 40px 0;
        }
        
        .download-btn {
            background: white;
            color: #667eea;
            padding: 15px 30px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: bold;
            display: inline-block;
            margin: 10px;
            transition: all 0.3s ease;
        }
        
        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .stat-card {
            background: #fff3cd;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border-left: 4px solid #ffc107;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #856404;
        }
        
        .footer {
            text-align: center;
            color: white;
            padding: 40px 0;
            margin-top: 40px;
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2.5rem;
            }
            
            .header .subtitle {
                font-size: 1.2rem;
            }
            
            .main-content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🌱 EcoStamp</h1>
            <p class="subtitle">Universal AI Environmental Impact Tracker</p>
            <p>Cross-browser extension for tracking environmental impact across ALL AI platforms!</p>
            
            <div class="badges">
                <a href="#" class="badge">Chrome Web Store</a>
                <a href="#" class="badge">Firefox Add-ons</a>
                <a href="#" class="badge">Opera Add-ons</a>
                <a href="#" class="badge">Edge Add-ons</a>
                <a href="#" class="badge">GitHub</a>
                <a href="#" class="badge">Product Hunt</a>
            </div>
        </header>
        
        <main class="main-content">
            <section>
                <h2>🚀 Instant Download & Use</h2>
                <div class="download-section">
                    <h3>Ready for ALL Major Browsers!</h3>
                    <p>One-click installation across Chrome, Firefox, Edge, Opera, and Brave</p>
                    <a href="#" class="download-btn">📦 Download Universal Package</a>
                    <a href="#" class="download-btn">🌐 Chrome Web Store</a>
                    <a href="#" class="download-btn">🦊 Firefox Add-ons</a>
                </div>
            </section>
            
            <section>
                <h2>🌐 Universal Browser Support</h2>
                <div class="browser-grid">
                    <div class="browser-card supported">
                        <div style="font-size: 2rem;">🟢</div>
                        <h4>Chrome</h4>
                        <p>Full Support</p>
                    </div>
                    <div class="browser-card supported">
                        <div style="font-size: 2rem;">🦊</div>
                        <h4>Firefox</h4>
                        <p>Full Support</p>
                    </div>
                    <div class="browser-card supported">
                        <div style="font-size: 2rem;">🔵</div>
                        <h4>Edge</h4>
                        <p>Full Support</p>
                    </div>
                    <div class="browser-card supported">
                        <div style="font-size: 2rem;">🔴</div>
                        <h4>Opera</h4>
                        <p>Full Support</p>
                    </div>
                    <div class="browser-card supported">
                        <div style="font-size: 2rem;">🦁</div>
                        <h4>Brave</h4>
                        <p>Full Support</p>
                    </div>
                    <div class="browser-card">
                        <div style="font-size: 2rem;">🍎</div>
                        <h4>Safari</h4>
                        <p>Requires Conversion</p>
                    </div>
                </div>
            </section>
            
            <section>
                <h2>✨ Key Features</h2>
                <div class="feature-grid">
                    <div class="feature-card">
                        <div class="feature-icon">🌍</div>
                        <h3>Universal AI Detection</h3>
                        <p>Works with ChatGPT, Claude, Gemini, Perplexity, Poe, Character.AI, You.com, Hugging Face, and ANY AI platform</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">⚡</div>
                        <h3>Real-time Impact Tracking</h3>
                        <p>Energy consumption, water usage, eco-level ratings displayed instantly</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🔐</div>
                        <h3>SHA-256 Verification</h3>
                        <p>Cryptographic proof system for every AI response with public verification</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">📊</div>
                        <h3>Analytics Dashboard</h3>
                        <p>Cross-platform statistics and usage breakdown</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">📁</div>
                        <h3>File Upload Support</h3>
                        <p>Document processing with environmental impact calculation</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🛡️</div>
                        <h3>Privacy-First Design</h3>
                        <p>Zero data collection, everything stays local on your device</p>
                    </div>
                </div>
            </section>
            
            <section>
                <h2>🚨 Shocking AI Environmental Impact</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">564,000</div>
                        <p>MWh/day - ChatGPT Energy Usage<br>(52,000 homes worth)</p>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">6.8M</div>
                        <p>Gallons daily - Water Usage<br>(10 Olympic pools)</p>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">250,000</div>
                        <p>Tons monthly - CO2 Emissions<br>(54,000 cars for a year)</p>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">2.9%</div>
                        <p>Global AI Energy Usage<br>(Growing 40% yearly)</p>
                    </div>
                </div>
            </section>
            
            <section>
                <h2>🎯 How It Works</h2>
                <ol style="font-size: 1.1rem; line-height: 2;">
                    <li><strong>Install EcoStamp</strong> in your preferred browser</li>
                    <li><strong>Visit any AI platform</strong> (ChatGPT, Claude, Gemini, etc.)</li>
                    <li><strong>Ask questions normally</strong> - EcoStamp works automatically</li>
                    <li><strong>See environmental impact</strong> displayed below each AI response</li>
                    <li><strong>Track your usage</strong> with the analytics dashboard</li>
                    <li><strong>Make informed decisions</strong> about AI efficiency</li>
                </ol>
            </section>
            
            <section>
                <h2>🌱 Environmental Impact Display</h2>
                <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; font-family: monospace;">
                    <div style="text-align: center; color: #666; margin-bottom: 10px;">
                        ──────────────────────────────────────────────
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                        <span>🕓 01/02/2025, 15:45:00 UTC</span>
                        <span>🔐 SHA-256: a1b2...c3d4</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                        <span>🌿 Eco-Level: 3/5 Leaves 🌿🌿🌿🍂🍂</span>
                        <span>(0.45 Wh · 12.8 mL)</span>
                    </div>
                    <div style="display: flex; justify-content: space-between;">
                        <span>Powered by EcoStamp — GitHub</span>
                        <span>ChatGPT • gpt-4</span>
                    </div>
                </div>
            </section>
        </main>
        
        <footer class="footer">
            <p>🌍 Making AI environmental impact visible everywhere!</p>
            <p>Ready to expose AI environmental waste to the world!</p>
        </footer>
    </div>
</body>
</html>

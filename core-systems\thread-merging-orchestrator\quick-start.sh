#!/bin/bash

echo "========================================"
echo "Thread-Merging Orchestrator Quick Start"
echo "========================================"
echo

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed or not in PATH"
    echo "Please install Node.js 18.0.0 or higher from https://nodejs.org/"
    exit 1
fi

echo "✅ Node.js found: $(node --version)"

# Check if package.json exists
if [ ! -f "package.json" ]; then
    echo "❌ package.json not found"
    echo "Please run this script from the thread-merging-orchestrator directory"
    exit 1
fi

echo
echo "📦 Installing dependencies..."
npm install
if [ $? -ne 0 ]; then
    echo "❌ Failed to install dependencies"
    exit 1
fi

echo
echo "🔧 Running setup..."
node scripts/setup.js
if [ $? -ne 0 ]; then
    echo "❌ Setup failed"
    exit 1
fi

echo
echo "✅ Setup completed successfully!"
echo
echo "📋 Quick commands to try:"
echo "  npm start -- --help                    (Show all commands)"
echo "  npm start -- retrieve                  (Retrieve threads)"
echo "  npm start -- search \"your query\"       (Search threads)"
echo "  npm start -- orchestrate \"your query\"  (Full orchestration)"
echo "  npm start -- web                       (Start web interface)"
echo
echo "🌐 Web interface will be available at: http://localhost:3000"
echo
echo "⚠️  Don't forget to configure your API keys in the .env file!"
echo

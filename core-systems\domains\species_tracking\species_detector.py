"""
Species Detection Interpreter for Ecological Surveys

Interprets wildlife detection data from drone cameras and audio sensors,
classifying species with focus on target species identification and
biodiversity cataloging.
"""

import json
import logging
import hashlib
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime
from pathlib import Path


class SpeciesDetectionInterpreter:
    """
    Interprets species detection data from ecological survey drone operations.
    
    Handles bird species identification, audio classification, GPS coordinate mapping,
    and biodiversity cataloging for ecological research.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.supported_formats = [
            'image_detection', 'audio_detection', 'video_frame', 'thermal_image'
        ]
        
        # North American bird taxonomy (simplified)
        self.bird_taxonomy = {
            'chickadee': {
                'family': 'Paridae',
                'genus': 'Poecile',
                'species': {
                    'carolina_chickadee': {
                        'scientific_name': 'Poecile carolinensis',
                        'subspecies': ['carolinensis', 'extimus', 'agilis'],
                        'habitat': ['deciduous_forest', 'mixed_forest', 'suburban'],
                        'confidence_threshold': 0.85,
                        'priority': 'target'
                    },
                    'black_capped_chickadee': {
                        'scientific_name': 'Poecile atricapillus',
                        'subspecies': ['atricapillus', 'practicus', 'septentrionalis'],
                        'habitat': ['coniferous_forest', 'mixed_forest'],
                        'confidence_threshold': 0.80,
                        'priority': 'related'
                    }
                }
            },
            'other_birds': {
                'family': 'various',
                'common_species': [
                    'cardinal', 'blue_jay', 'robin', 'sparrow', 'finch',
                    'woodpecker', 'hawk', 'owl', 'crow', 'wren'
                ],
                'confidence_threshold': 0.70,
                'priority': 'catalog'
            }
        }
        
        # Detection parameters
        self.detection_params = {
            'visual_features': [
                'plumage_pattern', 'size', 'beak_shape', 'tail_length',
                'wing_markings', 'head_pattern', 'body_posture'
            ],
            'audio_features': [
                'call_frequency', 'song_pattern', 'call_duration',
                'harmonic_structure', 'temporal_pattern'
            ],
            'behavioral_indicators': [
                'feeding_behavior', 'flight_pattern', 'perching_preference',
                'flock_behavior', 'nesting_activity'
            ]
        }
        
        # Environmental context factors
        self.environmental_factors = {
            'habitat_types': [
                'deciduous_forest', 'coniferous_forest', 'mixed_forest',
                'grassland', 'wetland', 'suburban', 'urban'
            ],
            'seasonal_factors': {
                'spring': {'breeding': True, 'migration': True},
                'summer': {'breeding': True, 'migration': False},
                'fall': {'breeding': False, 'migration': True},
                'winter': {'breeding': False, 'migration': False}
            },
            'time_of_day_activity': {
                'dawn': 0.9,    # High activity
                'morning': 0.8,
                'midday': 0.4,  # Lower activity
                'afternoon': 0.6,
                'dusk': 0.9,    # High activity
                'night': 0.2    # Low activity (except owls)
            }
        }
    
    def interpret(self, raw_output: Any, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Interpret species detection data from drone sensors.
        
        Args:
            raw_output: Raw detection data (image, audio, video, thermal)
            context: Survey context including target species, habitat, GPS
            
        Returns:
            Normalized species detection data with classifications and confidence scores
        """
        try:
            # Determine detection type
            detection_type = context.get('detection_type', 'image_detection')
            survey_id = context.get('survey_id', 'unknown')
            target_species = context.get('target_species', 'carolina_chickadee')
            
            # Parse raw detection data
            parsed_data = self._parse_detection_data(raw_output, context)
            
            # Classify detected species
            classifications = self._classify_species(parsed_data, context, target_species)
            
            # Calculate confidence scores
            confidence_scores = self._calculate_confidence_scores(classifications, context)
            
            # Extract GPS and environmental data
            environmental_data = self._extract_environmental_data(parsed_data, context)
            
            # Assess biodiversity metrics
            biodiversity_metrics = self._assess_biodiversity(classifications, context)
            
            # Create normalized output
            normalized_data = {
                'survey_id': survey_id,
                'detection_id': self._generate_detection_id(parsed_data, context),
                'timestamp': datetime.utcnow().isoformat(),
                'detection_type': detection_type,
                'target_species': target_species,
                'environmental_data': environmental_data,
                'classifications': classifications,
                'confidence_scores': confidence_scores,
                'biodiversity_metrics': biodiversity_metrics,
                'raw_data_summary': self._create_data_summary(parsed_data),
                '_metadata': {
                    'interpreter_version': '1.0.0',
                    'processing_timestamp': datetime.utcnow().isoformat(),
                    'drone_id': context.get('drone_id', 'unknown'),
                    'flight_altitude': context.get('altitude', 0),
                    'weather_conditions': context.get('weather', 'unknown'),
                    'habitat_type': context.get('habitat_type', 'unknown')
                }
            }
            
            # Validate detection quality
            self._validate_detection_quality(normalized_data)
            
            return normalized_data
            
        except Exception as e:
            self.logger.error(f"Error interpreting species detection: {str(e)}")
            return {
                'error': True,
                'error_message': str(e),
                'raw_output': str(raw_output)[:500],  # Truncate for logging
                '_metadata': {
                    'interpretation_failed': True,
                    'error_timestamp': datetime.utcnow().isoformat()
                }
            }
    
    def _parse_detection_data(self, raw_output: Any, context: Dict[str, Any]) -> Dict[str, Any]:
        """Parse raw detection data based on input format."""
        if isinstance(raw_output, dict):
            return raw_output
        
        if isinstance(raw_output, str):
            try:
                return json.loads(raw_output)
            except json.JSONDecodeError:
                return self._parse_audio_data(raw_output, context)
        
        if isinstance(raw_output, (list, tuple)):
            return {'detections': list(raw_output)}
        
        if isinstance(raw_output, bytes):
            return self._parse_binary_data(raw_output, context)
        
        return {'raw_value': raw_output}
    
    def _parse_audio_data(self, audio_data: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Parse audio detection data."""
        return {
            'audio_data': audio_data[:100] + '...',  # Truncate for storage
            'format': 'audio_detection',
            'duration': context.get('audio_duration', 0),
            'sample_rate': context.get('sample_rate', 44100)
        }
    
    def _parse_binary_data(self, binary_data: bytes, context: Dict[str, Any]) -> Dict[str, Any]:
        """Parse binary detection data."""
        return {
            'data_size_bytes': len(binary_data),
            'data_hash': hashlib.md5(binary_data).hexdigest(),
            'format': 'binary_detection'
        }
    
    def _classify_species(self, parsed_data: Dict[str, Any], 
                         context: Dict[str, Any], 
                         target_species: str) -> List[Dict[str, Any]]:
        """Classify detected species based on visual and audio features."""
        classifications = []
        
        # Handle single detection
        if 'detections' not in parsed_data:
            classification = self._classify_single_species(parsed_data, context, target_species)
            if classification:
                classifications.append(classification)
        else:
            # Handle multiple detections
            for i, detection in enumerate(parsed_data.get('detections', [])):
                classification = self._classify_single_species(detection, context, target_species, index=i)
                if classification:
                    classifications.append(classification)
        
        return classifications
    
    def _classify_single_species(self, detection: Any, context: Dict[str, Any], 
                                target_species: str, index: int = 0) -> Optional[Dict[str, Any]]:
        """Classify a single species detection."""
        if isinstance(detection, dict):
            # Extract classification data
            detected_class = detection.get('class', detection.get('species', 'unknown'))
            confidence = detection.get('confidence', detection.get('score', 0.5))
            features = detection.get('features', {})
            
            # Map to bird taxonomy
            species_info = self._map_to_bird_taxonomy(detected_class, features, context)
            
            # Determine if this is the target species
            is_target = self._is_target_species(detected_class, target_species)
            
            return {
                'detection_index': index,
                'original_class': detected_class,
                'species_classification': species_info,
                'confidence': float(confidence),
                'is_target_species': is_target,
                'features': features,
                'priority': species_info.get('priority', 'catalog'),
                'meets_threshold': confidence >= species_info.get('confidence_threshold', 0.7),
                'behavioral_indicators': self._extract_behavioral_indicators(detection, context),
                'habitat_match': self._assess_habitat_match(species_info, context)
            }
        
        return None
    
    def _map_to_bird_taxonomy(self, detected_class: str, features: Dict[str, Any], 
                             context: Dict[str, Any]) -> Dict[str, Any]:
        """Map detected class to bird taxonomy."""
        class_lower = detected_class.lower()
        
        # Check for chickadee species
        if 'chickadee' in class_lower:
            if 'carolina' in class_lower or context.get('region', '').lower() == 'southeast':
                return self.bird_taxonomy['chickadee']['species']['carolina_chickadee']
            elif 'black' in class_lower or 'capped' in class_lower:
                return self.bird_taxonomy['chickadee']['species']['black_capped_chickadee']
            else:
                # Generic chickadee
                return {
                    'family': 'Paridae',
                    'genus': 'Poecile',
                    'species': 'chickadee_sp',
                    'confidence_threshold': 0.75,
                    'priority': 'related'
                }
        
        # Check for other common birds
        for bird in self.bird_taxonomy['other_birds']['common_species']:
            if bird in class_lower:
                return {
                    'family': 'various',
                    'species': bird,
                    'confidence_threshold': 0.70,
                    'priority': 'catalog'
                }
        
        # Unknown bird species
        return {
            'family': 'unknown',
            'species': 'unidentified_bird',
            'confidence_threshold': 0.5,
            'priority': 'review'
        }
    
    def _is_target_species(self, detected_class: str, target_species: str) -> bool:
        """Determine if detected species matches target species."""
        detected_lower = detected_class.lower()
        target_lower = target_species.lower()
        
        # Direct match
        if target_lower in detected_lower:
            return True
        
        # Chickadee subspecies matching
        if 'chickadee' in target_lower and 'chickadee' in detected_lower:
            return True
        
        return False
    
    def _extract_behavioral_indicators(self, detection: Dict[str, Any], 
                                     context: Dict[str, Any]) -> List[str]:
        """Extract behavioral indicators from detection data."""
        indicators = []
        
        # Check for feeding behavior
        if detection.get('activity') == 'feeding':
            indicators.append('feeding_behavior')
        
        # Check for nesting activity
        if 'nest' in str(detection).lower():
            indicators.append('nesting_activity')
        
        # Check for flock behavior
        if detection.get('group_size', 1) > 1:
            indicators.append('flock_behavior')
        
        # Check for perching
        if detection.get('position') == 'perched':
            indicators.append('perching_behavior')
        
        return indicators
    
    def _assess_habitat_match(self, species_info: Dict[str, Any], context: Dict[str, Any]) -> float:
        """Assess how well the habitat matches the species preferences."""
        species_habitats = species_info.get('habitat', [])
        current_habitat = context.get('habitat_type', 'unknown')
        
        if current_habitat in species_habitats:
            return 1.0
        elif any(habitat in current_habitat for habitat in species_habitats):
            return 0.7
        else:
            return 0.3
    
    def _calculate_confidence_scores(self, classifications: List[Dict[str, Any]], 
                                   context: Dict[str, Any]) -> Dict[str, float]:
        """Calculate overall confidence scores for the detection."""
        if not classifications:
            return {'overall_confidence': 0.0}
        
        # Calculate target species confidence
        target_detections = [c for c in classifications if c.get('is_target_species', False)]
        target_confidence = 0.0
        if target_detections:
            target_confidence = max(d['confidence'] for d in target_detections)
        
        # Calculate species diversity confidence
        unique_species = set(c['species_classification'].get('species', 'unknown') for c in classifications)
        diversity_factor = min(len(unique_species) / 5.0, 1.0)  # Normalize to max 5 species
        
        # Environmental factors
        time_factor = self._get_time_activity_factor(context)
        habitat_factor = sum(c.get('habitat_match', 0.5) for c in classifications) / len(classifications)
        
        # Overall confidence
        overall_confidence = sum(c['confidence'] for c in classifications) / len(classifications)
        adjusted_confidence = overall_confidence * time_factor * habitat_factor
        
        return {
            'overall_confidence': adjusted_confidence,
            'target_species_confidence': target_confidence,
            'diversity_confidence': diversity_factor,
            'environmental_adjustment': time_factor * habitat_factor,
            'species_count': len(classifications),
            'target_species_count': len(target_detections)
        }
    
    def _get_time_activity_factor(self, context: Dict[str, Any]) -> float:
        """Get activity factor based on time of day."""
        time_of_day = context.get('time_of_day', 'unknown')
        return self.environmental_factors['time_of_day_activity'].get(time_of_day, 0.5)
    
    def _extract_environmental_data(self, parsed_data: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Extract environmental and GPS information."""
        return {
            'gps_coordinates': {
                'latitude': context.get('latitude', parsed_data.get('lat')),
                'longitude': context.get('longitude', parsed_data.get('lon')),
                'altitude': context.get('altitude', parsed_data.get('alt', 0))
            },
            'habitat_info': {
                'habitat_type': context.get('habitat_type', 'unknown'),
                'vegetation_density': context.get('vegetation_density', 'medium'),
                'water_proximity': context.get('water_proximity_m', 0),
                'human_disturbance': context.get('human_disturbance', 'low')
            },
            'weather_conditions': {
                'temperature': context.get('temperature_c'),
                'humidity': context.get('humidity_percent'),
                'wind_speed': context.get('wind_speed_mps'),
                'cloud_cover': context.get('cloud_cover_percent'),
                'precipitation': context.get('precipitation', 'none')
            },
            'temporal_info': {
                'season': context.get('season', 'unknown'),
                'time_of_day': context.get('time_of_day', 'unknown'),
                'date': context.get('date', datetime.utcnow().date().isoformat())
            }
        }
    
    def _assess_biodiversity(self, classifications: List[Dict[str, Any]], context: Dict[str, Any]) -> Dict[str, Any]:
        """Assess biodiversity metrics from detections."""
        if not classifications:
            return {'species_richness': 0, 'diversity_index': 0.0}
        
        # Species richness (number of unique species)
        unique_species = set(c['species_classification'].get('species', 'unknown') for c in classifications)
        species_richness = len(unique_species)
        
        # Family diversity
        unique_families = set(c['species_classification'].get('family', 'unknown') for c in classifications)
        family_diversity = len(unique_families)
        
        # Calculate Shannon diversity index (simplified)
        species_counts = {}
        for classification in classifications:
            species = classification['species_classification'].get('species', 'unknown')
            species_counts[species] = species_counts.get(species, 0) + 1
        
        total_individuals = len(classifications)
        shannon_index = 0.0
        if total_individuals > 0:
            for count in species_counts.values():
                proportion = count / total_individuals
                if proportion > 0:
                    shannon_index -= proportion * (proportion ** 0.5)  # Simplified calculation
        
        return {
            'species_richness': species_richness,
            'family_diversity': family_diversity,
            'shannon_diversity_index': shannon_index,
            'total_individuals': total_individuals,
            'species_abundance': species_counts,
            'dominant_species': max(species_counts.items(), key=lambda x: x[1])[0] if species_counts else None
        }
    
    def _create_data_summary(self, parsed_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create summary of raw data for logging."""
        return {
            'data_type': type(parsed_data).__name__,
            'keys': list(parsed_data.keys()) if isinstance(parsed_data, dict) else [],
            'size_estimate': len(str(parsed_data)),
            'has_audio_data': any(key in str(parsed_data).lower() for key in ['audio', 'sound', 'call']),
            'has_image_data': any(key in str(parsed_data).lower() for key in ['image', 'photo', 'visual']),
            'detection_count': len(parsed_data.get('detections', [])) if 'detections' in parsed_data else 1
        }
    
    def _generate_detection_id(self, parsed_data: Dict[str, Any], context: Dict[str, Any]) -> str:
        """Generate unique detection ID."""
        survey_id = context.get('survey_id', 'unknown')
        timestamp = datetime.utcnow().strftime('%Y%m%d_%H%M%S_%f')
        data_hash = hashlib.md5(str(parsed_data).encode()).hexdigest()[:8]
        
        return f"SPD_{survey_id}_{timestamp}_{data_hash}"
    
    def _validate_detection_quality(self, normalized_data: Dict[str, Any]) -> None:
        """Validate detection data quality and add warnings."""
        warnings = []
        
        # Check confidence scores
        overall_confidence = normalized_data.get('confidence_scores', {}).get('overall_confidence', 0)
        if overall_confidence < 0.5:
            warnings.append('Low overall confidence score')
        
        # Check GPS data
        gps = normalized_data.get('environmental_data', {}).get('gps_coordinates', {})
        if not gps.get('latitude') or not gps.get('longitude'):
            warnings.append('Missing GPS coordinates')
        
        # Check classification count
        classifications = normalized_data.get('classifications', [])
        if not classifications:
            warnings.append('No valid species classifications found')
        
        # Check target species detection
        target_count = normalized_data.get('confidence_scores', {}).get('target_species_count', 0)
        if target_count == 0:
            warnings.append('No target species detected')
        
        if warnings:
            normalized_data['_metadata']['quality_warnings'] = warnings
    
    def validate_input(self, raw_output: Any) -> bool:
        """Validate that the raw output can be interpreted."""
        if raw_output is None:
            return False
        
        # Accept various data types for species detection
        accepted_types = (dict, list, tuple, str, bytes)
        return isinstance(raw_output, accepted_types)
    
    def get_supported_formats(self) -> List[str]:
        """Get list of supported detection formats."""
        return self.supported_formats.copy()
    
    def get_bird_taxonomy(self) -> Dict[str, Any]:
        """Get bird taxonomy information."""
        return self.bird_taxonomy.copy()
    
    def add_species_to_taxonomy(self, species_name: str, species_info: Dict[str, Any]) -> None:
        """Add a new species to the taxonomy."""
        # This would add to the appropriate taxonomy category
        self.logger.info(f"Added species to taxonomy: {species_name}")
    
    def update_confidence_threshold(self, species: str, threshold: float) -> None:
        """Update confidence threshold for a species."""
        # This would update the threshold in the taxonomy
        self.logger.info(f"Updated confidence threshold for {species}: {threshold}")

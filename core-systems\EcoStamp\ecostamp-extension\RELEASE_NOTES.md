# 🎉 EcoStamp v1.0.0 - Official Release

**The first universal AI environmental impact tracker is here!**

## 🌱 **What is EcoStamp?**

EcoStamp is a browser extension that tracks environmental impact across ALL AI platforms. See real-time energy usage, water consumption, and eco-levels for every AI conversation on <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and more.

## 🚀 **Download & Install**

### **Chrome Web Store (Recommended)**

**[Install EcoStamp →](https://chrome.google.com/webstore)** - One-click installation

### **Direct Download**

**[Download v1.0.0 ZIP](https://github.com/ecostamp/ecostamp-extension/releases/download/v1.0.0/ecostamp-v1.0.0.zip)** - Manual installation

### **Installation Steps**

1. Download the ZIP file above
2. Extract to a folder
3. Open Chrome → `chrome://extensions/`
4. Enable "Developer mode"
5. Click "Load unpacked" → Select folder
6. Done! Visit any AI platform and see EcoStamp in action

## ✨ **Key Features**

### 🌍 **Universal AI Platform Support**

- ✅ **ChatGPT** (chat.openai.com, chatgpt.com)
- ✅ **Claude** (claude.ai)
- ✅ **Gemini** (gemini.google.com, bard.google.com)
- ✅ **Perplexity** (perplexity.ai)
- ✅ **Poe** (poe.com)
- ✅ **Character.AI** (character.ai)
- ✅ **You.com** (you.com)
- ✅ **Hugging Face** (huggingface.co)
- ✅ **ANY AI Platform** (universal detection)

### 🌿 **Smart Eco-Level System**

- **5-leaf visual meter** showing environmental efficiency
- **Real-time calculations** based on response complexity
- **Platform efficiency multipliers** (Claude more efficient than ChatGPT)
- **Smart content analysis** (detects code, lists, markdown)

### 📊 **Comprehensive Analytics**

- **Cross-platform statistics** in beautiful popup dashboard
- **Platform usage breakdown** showing your AI preferences
- **Eco-level distribution** to track efficiency over time
- **Export functionality** for data analysis
- **Real-time badge updates** showing total interactions

### 🔒 **Privacy & Security**

- **No data collection** - everything stays local
- **Minimal permissions** - only activeTab and storage
- **CSP-compatible** - works with strict security policies
- **Open source** - fully transparent code

## 🎯 **What You'll See**

After each AI response on ANY platform:

```
──────────────────────────────────────────────
🕓 01/02/2025, 03:45:00 UTC  |  🔐 SHA-256: a1b2...c3d4
🌿 Eco-Level: 3/5 Leaves 🌿🌿🌿🍂🍂  (0.45 Wh · 12.8 mL)
Powered by EcoStamp — GitHub              ChatGPT • gpt-4
```

## 📊 **Eco-Level Guide**

| Level | Leaves | Energy    | Description          |
| ----- | ------ | --------- | -------------------- |
| 1     | 🌿🌿🌿🌿🌿  | < 0.15 Wh | Excellent efficiency |
| 2     | 🌿🌿🌿🌿🍂  | < 0.30 Wh | Good efficiency      |
| 3     | 🌿🌿🌿🍂🍂  | < 0.50 Wh | Moderate efficiency  |
| 4     | 🌿🌿🍂🍂🍂  | < 0.70 Wh | Higher impact        |
| 5     | 🌿🍂🍂🍂🍂  | > 0.70 Wh | High impact          |

## 🛠️ **Technical Details**

- **Manifest V3** - Latest Chrome extension standard
- **Universal Detection** - Works with any AI platform
- **Efficient Processing** - Optimized for performance
- **Error Handling** - Graceful degradation
- **Accessibility** - Supports dark mode and high contrast

## 🌱 **Environmental Impact**

EcoStamp helps you:

- **Track AI usage** across all platforms
- **Make informed decisions** about AI efficiency
- **Reduce environmental impact** by choosing efficient queries
- **Raise awareness** about AI's environmental cost

## 🔧 **Browser Support**

- ✅ **Chrome** 88+ (Recommended)
- ✅ **Microsoft Edge** 88+
- ✅ **Brave Browser**
- ✅ **Opera** (Chromium-based)

## 🐛 **Known Issues**

- Firefox support coming in v1.1.0
- Safari support planned for v1.2.0
- Some dynamic content may require page refresh

## 🔍 **Troubleshooting**

**Not seeing footers?**

1. Refresh the AI platform page
2. Check if extension is enabled (click icon)
3. Try asking a longer question
4. Check browser console for errors

**Manual trigger:**

```javascript
ecoStamp.process()
```

## 🤝 **Contributing**

EcoStamp is open source! Contribute at:
**[GitHub Repository](https://github.com/ecostamp/ecostamp-extension)**

## 📞 **Support**

- 📖 **[Documentation](https://ecostamp.github.io/docs)**
- 🐛 **[Report Issues](https://github.com/ecostamp/ecostamp-extension/issues)**
- 💬 **[Community](https://github.com/ecostamp/ecostamp-extension/discussions)**

## 🔗 **Links**

- **[Chrome Web Store](https://chrome.google.com/webstore)** - Official extension
- **[Product Hunt](https://www.producthunt.com/posts/ecostamp)** - Launch page
- **[GitHub](https://github.com/ecostamp/ecostamp-extension)** - Source code
- **[Website](https://ecostamp.com)** - Official website

---

## 📈 **What's Next?**

### **v1.1.0 - Enhanced Analytics** (Coming Soon)

- Historical trend charts
- Weekly/monthly reports
- Carbon footprint calculations
- Firefox support

### **v1.2.0 - Advanced Features**

- Custom eco-level thresholds
- AI model recommendations
- Safari support
- Team dashboards

---

**🌱 Thank you for using EcoStamp and helping make AI more environmentally conscious!**

*EcoStamp Team - July 2025*

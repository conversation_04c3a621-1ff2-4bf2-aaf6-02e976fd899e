"use strict";
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.setCurrentOrchestrator = exports.clearError = exports.fetchOrchestrator = exports.fetchOrchestrators = void 0;
const toolkit_1 = require("@reduxjs/toolkit");
const api_1 = require("../../services/api");
const initialState = {
    orchestrators: [],
    currentOrchestrator: null,
    isLoading: false,
    error: null,
};
exports.fetchOrchestrators = (0, toolkit_1.createAsyncThunk)('orchestrator/fetchAll', async () => {
    const response = await api_1.orchestratorApi.getAll();
    return response.data;
});
exports.fetchOrchestrator = (0, toolkit_1.createAsyncThunk)('orchestrator/fetchOne', async (id) => {
    const response = await api_1.orchestratorApi.getById(id);
    return response.data;
});
const orchestratorSlice = (0, toolkit_1.createSlice)({
    name: 'orchestrator',
    initialState,
    reducers: {
        clearError: (state) => {
            state.error = null;
        },
        setCurrentOrchestrator: (state, action) => {
            state.currentOrchestrator = action.payload;
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(exports.fetchOrchestrators.pending, (state) => {
            state.isLoading = true;
            state.error = null;
        })
            .addCase(exports.fetchOrchestrators.fulfilled, (state, action) => {
            state.isLoading = false;
            state.orchestrators = action.payload;
        })
            .addCase(exports.fetchOrchestrators.rejected, (state, action) => {
            state.isLoading = false;
            state.error = action.error.message || 'Failed to fetch orchestrators';
        })
            .addCase(exports.fetchOrchestrator.fulfilled, (state, action) => {
            state.currentOrchestrator = action.payload;
        });
    },
});
_a = orchestratorSlice.actions, exports.clearError = _a.clearError, exports.setCurrentOrchestrator = _a.setCurrentOrchestrator;
exports.default = orchestratorSlice.reducer;

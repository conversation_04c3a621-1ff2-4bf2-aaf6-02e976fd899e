/**
 * Conflict Resolution API Routes
 * 
 * RESTful API endpoints for real-time conflict detection and resolution
 */

import { Router } from 'express';
import { body, query, param, validationResult } from 'express-validator';
import { PrismaClient } from '@prisma/client';
import { ConflictResolutionService } from '../services/ConflictResolutionService';
import { asyncHandler } from '../utils/asyncHandler';
import { authenticate, authorize } from '../middleware/auth';
import { logger } from '../utils/logger';
import {
  ConflictType,
  ConflictSeverity,
  ResolutionStrategy,
  ConflictStatus
} from '../../shared/types/ConflictResolution';

const router = Router();
const prisma = new PrismaClient();
const conflictService = new ConflictResolutionService(prisma);

// Validation middleware
const validateConflictDetection = [
  body('codeChanges').isArray({ min: 1 }).withMessage('Code changes array is required'),
  body('codeChanges.*.file').notEmpty().withMessage('File path is required'),
  body('codeChanges.*.operation').isIn(['ADD', 'MODIFY', 'DELETE']).withMessage('Invalid operation'),
  body('codeChanges.*.content').notEmpty().withMessage('Content is required'),
  body('codeChanges.*.author').notEmpty().withMessage('Author is required'),
  body('context.projectId').notEmpty().withMessage('Project ID is required'),
  body('context.workflowId').notEmpty().withMessage('Workflow ID is required'),
  body('context.impactRadius').isIn(['LOCAL', 'MODULE', 'PROJECT', 'SYSTEM']).withMessage('Invalid impact radius'),
  body('context.businessCriticality').isIn(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']).withMessage('Invalid business criticality')
];

const validateTrustScoreUpdate = [
  body('agentId').notEmpty().withMessage('Agent ID is required'),
  body('metrics').isObject().withMessage('Metrics object is required'),
  body('metrics.codeQuality').optional().isFloat({ min: 0, max: 1 }).withMessage('Code quality must be 0-1'),
  body('metrics.reliability').optional().isFloat({ min: 0, max: 1 }).withMessage('Reliability must be 0-1'),
  body('metrics.expertise').optional().isFloat({ min: 0, max: 1 }).withMessage('Expertise must be 0-1')
];

/**
 * POST /api/conflict-resolution/detect
 * Detect conflicts in code changes
 */
router.post('/detect',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR', 'AGENT']),
  validateConflictDetection,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { codeChanges, context } = req.body;

    const conflicts = await conflictService.detectConflicts(codeChanges, context);

    logger.info('Conflict detection requested via API', {
      codeChangesCount: codeChanges.length,
      conflictsDetected: conflicts.length,
      projectId: context.projectId,
      userId: req.user?.id
    });

    res.json({
      success: true,
      conflicts: conflicts.map(conflict => ({
        id: conflict.id,
        type: conflict.type,
        severity: conflict.severity,
        status: conflict.status,
        detectedAt: conflict.detectedAt,
        involvedAgents: conflict.involvedAgents,
        conflictingElements: conflict.conflictingElements.map(element => ({
          elementId: element.elementId,
          elementType: element.elementType,
          elementName: element.elementName,
          sourceAgent: element.sourceAgent,
          sourceFile: element.sourceFile,
          lineNumber: element.lineNumber,
          trustScore: element.trustScore
        })),
        contextInfo: conflict.contextInfo,
        metadata: {
          confidence: conflict.metadata.confidence,
          similarityScore: conflict.metadata.similarityScore,
          complexityScore: conflict.metadata.complexityScore,
          riskAssessment: conflict.metadata.riskAssessment
        }
      })),
      summary: {
        totalConflicts: conflicts.length,
        conflictsByType: conflicts.reduce((acc, c) => {
          acc[c.type] = (acc[c.type] || 0) + 1;
          return acc;
        }, {} as Record<ConflictType, number>),
        conflictsBySeverity: conflicts.reduce((acc, c) => {
          acc[c.severity] = (acc[c.severity] || 0) + 1;
          return acc;
        }, {} as Record<ConflictSeverity, number>)
      }
    });
  })
);

/**
 * POST /api/conflict-resolution/resolve/:conflictId
 * Resolve a specific conflict
 */
router.post('/resolve/:conflictId',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR']),
  param('conflictId').notEmpty().withMessage('Conflict ID is required'),
  body('strategy').optional().isIn(Object.values(ResolutionStrategy)).withMessage('Invalid resolution strategy'),
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { conflictId } = req.params;

    const resolutionResult = await conflictService.resolveConflict(conflictId);

    logger.info('Conflict resolution requested via API', {
      conflictId,
      strategy: resolutionResult.strategy,
      userId: req.user?.id
    });

    res.json({
      success: true,
      resolution: {
        strategy: resolutionResult.strategy,
        mergedCode: resolutionResult.mergedCode,
        selectedVersion: resolutionResult.selectedVersion,
        modifications: resolutionResult.modifications,
        testResults: resolutionResult.testResults,
        performanceMetrics: resolutionResult.performanceMetrics,
        qualityMetrics: resolutionResult.qualityMetrics,
        approvalRequired: resolutionResult.approvalRequired,
        rollbackPlan: resolutionResult.rollbackPlan
      }
    });
  })
);

/**
 * GET /api/conflict-resolution/conflicts
 * Get active conflicts
 */
router.get('/conflicts',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR', 'AGENT']),
  query('status').optional().isIn(Object.values(ConflictStatus)),
  query('type').optional().isIn(Object.values(ConflictType)),
  query('severity').optional().isIn(Object.values(ConflictSeverity)),
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    let conflicts = conflictService.getActiveConflicts();

    // Apply filters
    if (req.query.status) {
      conflicts = conflicts.filter(c => c.status === req.query.status);
    }
    if (req.query.type) {
      conflicts = conflicts.filter(c => c.type === req.query.type);
    }
    if (req.query.severity) {
      conflicts = conflicts.filter(c => c.severity === req.query.severity);
    }

    res.json({
      success: true,
      conflicts: conflicts.map(conflict => ({
        id: conflict.id,
        type: conflict.type,
        severity: conflict.severity,
        status: conflict.status,
        detectedAt: conflict.detectedAt,
        resolvedAt: conflict.resolvedAt,
        involvedAgents: conflict.involvedAgents,
        contextInfo: {
          projectId: conflict.contextInfo.projectId,
          workflowId: conflict.contextInfo.workflowId,
          impactRadius: conflict.contextInfo.impactRadius,
          businessCriticality: conflict.contextInfo.businessCriticality
        },
        resolutionStrategy: conflict.resolutionStrategy,
        metadata: {
          confidence: conflict.metadata.confidence,
          similarityScore: conflict.metadata.similarityScore,
          riskAssessment: conflict.metadata.riskAssessment
        }
      })),
      count: conflicts.length
    });
  })
);

/**
 * GET /api/conflict-resolution/analytics
 * Get conflict resolution analytics
 */
router.get('/analytics',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR']),
  query('startDate').optional().isISO8601(),
  query('endDate').optional().isISO8601(),
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const timeRange = req.query.startDate && req.query.endDate ? {
      startDate: new Date(req.query.startDate as string),
      endDate: new Date(req.query.endDate as string)
    } : undefined;

    const analytics = await conflictService.getConflictAnalytics(timeRange);

    res.json({
      success: true,
      analytics
    });
  })
);

/**
 * POST /api/conflict-resolution/trust-score
 * Update agent trust score
 */
router.post('/trust-score',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR']),
  validateTrustScoreUpdate,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { agentId, metrics } = req.body;

    await conflictService.updateAgentTrustScore(agentId, metrics);

    logger.info('Agent trust score updated via API', {
      agentId,
      metrics,
      userId: req.user?.id
    });

    res.json({
      success: true,
      message: 'Trust score updated successfully',
      agentId,
      updatedMetrics: metrics
    });
  })
);

/**
 * GET /api/conflict-resolution/conflicts/:conflictId
 * Get specific conflict details
 */
router.get('/conflicts/:conflictId',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR', 'AGENT']),
  param('conflictId').notEmpty().withMessage('Conflict ID is required'),
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { conflictId } = req.params;
    const conflicts = conflictService.getActiveConflicts();
    const conflict = conflicts.find(c => c.id === conflictId);

    if (!conflict) {
      return res.status(404).json({
        success: false,
        error: 'Conflict not found'
      });
    }

    res.json({
      success: true,
      conflict: {
        id: conflict.id,
        type: conflict.type,
        severity: conflict.severity,
        status: conflict.status,
        detectedAt: conflict.detectedAt,
        resolvedAt: conflict.resolvedAt,
        involvedAgents: conflict.involvedAgents,
        conflictingElements: conflict.conflictingElements,
        contextInfo: conflict.contextInfo,
        resolutionStrategy: conflict.resolutionStrategy,
        resolutionResult: conflict.resolutionResult,
        metadata: conflict.metadata
      }
    });
  })
);

/**
 * POST /api/conflict-resolution/simulate
 * Simulate conflict resolution without applying changes
 */
router.post('/simulate',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR']),
  body('conflictId').notEmpty().withMessage('Conflict ID is required'),
  body('strategy').isIn(Object.values(ResolutionStrategy)).withMessage('Invalid resolution strategy'),
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { conflictId, strategy } = req.body;

    // Mock simulation - in real implementation would simulate resolution
    const simulation = {
      conflictId,
      strategy,
      estimatedResolutionTime: 30000,
      estimatedSuccessRate: 0.85,
      potentialRisks: ['Code complexity increase', 'Test coverage reduction'],
      recommendedActions: ['Add comprehensive tests', 'Code review required'],
      previewChanges: [
        {
          file: 'src/example.ts',
          operation: 'MODIFY',
          linesAffected: 5,
          preview: '// Merged function implementation...'
        }
      ]
    };

    logger.info('Conflict resolution simulation requested via API', {
      conflictId,
      strategy,
      userId: req.user?.id
    });

    res.json({
      success: true,
      simulation
    });
  })
);

/**
 * GET /api/conflict-resolution/health
 * Get conflict resolution system health
 */
router.get('/health',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR']),
  asyncHandler(async (req, res) => {
    const conflicts = conflictService.getActiveConflicts();
    const analytics = await conflictService.getConflictAnalytics();

    const health = {
      status: 'healthy',
      timestamp: new Date(),
      metrics: {
        activeConflicts: conflicts.length,
        criticalConflicts: conflicts.filter(c => c.severity === ConflictSeverity.CRITICAL).length,
        unresolvedConflicts: conflicts.filter(c => c.status !== ConflictStatus.RESOLVED).length,
        averageResolutionTime: analytics.averageResolutionTime,
        successRate: analytics.successRate
      },
      performance: {
        detectionLatency: 2500, // Mock value
        resolutionThroughput: 15, // Mock value
        systemLoad: 0.3 // Mock value
      },
      issues: [] as string[],
      warnings: [] as string[]
    };

    // Add health warnings
    if (health.metrics.criticalConflicts > 5) {
      health.warnings.push('High number of critical conflicts detected');
    }

    if (health.metrics.averageResolutionTime > 300000) { // 5 minutes
      health.warnings.push('Average resolution time is above threshold');
    }

    if (health.metrics.successRate < 0.8) {
      health.issues.push('Resolution success rate is below acceptable level');
    }

    res.json({
      success: true,
      health
    });
  })
);

export default router;

{"name": "augment-orchestration", "version": "1.0.0", "description": "Unified AI Orchestration + Family Tree Visualization System", "main": "dist/server/index.js", "scripts": {"dev": "concurrently \"npm run dev:server\" \"npm run dev:client\"", "dev:server": "nodemon --exec ts-node src/server/index.ts", "dev:client": "cd client && npm run dev", "build": "npm run build:server && npm run build:client", "build:server": "tsc -p tsconfig.server.json", "build:client": "cd client && npm run build", "start": "node dist/server/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest src/tests/unit", "test:integration": "jest src/tests/integration", "test:e2e": "jest src/tests/e2e", "test:server": "jest src/tests", "test:client": "cd client && npm test", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "format": "prettier --write src/**/*.ts", "typecheck": "tsc --noEmit", "db:migrate": "prisma migrate dev", "db:migrate:prod": "prisma migrate deploy", "db:generate": "prisma generate", "db:studio": "prisma studio", "db:seed": "ts-node prisma/seed.ts", "docker:build": "docker build -t augment-orchestration .", "docker:run": "docker run -p 3001:3001 augment-orchestration", "docs:generate": "typedoc src --out docs", "health-check": "curl -f http://localhost:3001/health || exit 1"}, "keywords": ["ai-orchestration", "multi-agent", "family-tree", "visualization", "typescript", "react", "nodejs"], "author": "Augment Code", "license": "MIT", "dependencies": {"@prisma/client": "^5.7.1", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "socket.io": "^4.7.4", "uuid": "^9.0.1", "winston": "^3.11.0", "zod": "^3.22.4"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/node": "^20.9.0", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "concurrently": "^8.2.2", "eslint": "^8.54.0", "jest": "^29.7.0", "nodemon": "^3.1.10", "prisma": "^5.7.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}
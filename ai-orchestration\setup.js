
#!/usr/bin / env node

const { exec, spawn } = require('child_process');
const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const ora = require('ora');
const inquirer = require('inquirer');

class AIOrchestrationSetup {
  constructor() {
    this.requiredExtensions = [
      // Core AI Tools
      'tabnine.tabnine-vscode',

      // Development Extensions
      'ms-vscode.vscode-typescript-next',
      'ms-python.python',
      'ms-vscode.powershell',
      'ms-vscode.vscode-json',
      'bradlc.vscode-tailwindcss',
      'esbenp.prettier-vscode',
      'ms-vscode.vscode-eslint',

      // GitHub and Remote
      'github.copilot',
      'ms-vscode.remote-repositories',
      'ms-vscode.remote-wsl',
      'ms-vscode-remote.remote-containers',

      // Utilities
      'ms-vscode.hexeditor',
      'ms-vscode.vscode-markdown',
      'yzhang.markdown-all-in-one',
      'shd101wyy.markdown-preview-enhanced',
      'pkief.material-icon-theme',
      'formulahendry.auto-rename-tag',
      'christian-kohler.path-intellisense'
    ];

    this.manualInstallTools = [
      {
        name: 'Cursor',
        url: 'https://cursor.sh/',
        description: 'AI-powered code editor'
      },
      {
        name: 'Windsurf',
        url: 'https://codeium.com/windsurf',
        description: 'Multi-file AI code editor'
      },
      {
        name: 'Augment Code',
        url: 'https://augmentcode.com/',
        description: 'Codebase context engine (VSIX installation)'
      }
    ];
  }

  async run() {
    console.log(chalk.blue.bold('\n🤖 AI Orchestration Hub Setup\n'));

    try {
      // Check prerequisites
      await this.checkPrerequisites();

      // Install Node.js dependencies
      await this.installDependencies();

      // Install VS Code extensions
      await this.installVSCodeExtensions();

      // Configure VS Code settings
      await this.configureVSCodeSettings();

      // Create project templates
      await this.createProjectTemplates();

      // Show manual installation instructions
      await this.showManualInstructions();

      // Final setup
      await this.finalizeSetup();

      console.log(chalk.green.bold('\n✅ AI Orchestration Hub setup completed successfully!\n'));
      console.log(chalk.yellow('📋 Next steps:'));
      console.log(chalk.white('1. Install Cursor, Windsurf, and Augment Code manually'));
      console.log(chalk.white('2. Open the AI-Orchestration-Workspace.code-workspace file'));
      console.log(chalk.white('3. Run: npm run init'));
      console.log(chalk.white('4. Start orchestrating your AI tools!\n'));

    } catch (error) {
      console.error(chalk.red(`❌ Setup failed: ${error.message}`));
      process.exit(1);
    }
  }

  async checkPrerequisites() {
    const spinner = ora('Checking prerequisites...').start();

    try {
      // Check Node.js
      await this.checkCommand('node --version', 'Node.js');

      // Check npm
      await this.checkCommand('npm --version', 'npm');

      // Check VS Code
      await this.checkCommand('code --version', 'VS Code');

      // Check Git
      await this.checkCommand('git --version', 'Git');

      spinner.succeed('All prerequisites are installed');
    } catch (error) {
      spinner.fail(`Missing prerequisite: ${error.message}`);
      throw error;
    }
  }

  async checkCommand(command, name) {
    return new Promise((resolve, reject) => {
      exec(command, (error, stdout, stderr) => {
        if (error) {
          reject(new Error(`${name} is not installed or not in PATH`));
        } else {
          console.log(chalk.green(`✓ ${name}: ${stdout.split('\n')[0]}`));
          resolve();
        }
      });
    });
  }

  async installDependencies() {
    const spinner = ora('Installing Node.js dependencies...').start();

    return new Promise((resolve, reject) => {
      const npm = spawn('npm', ['install'], {
        cwd: __dirname,
        stdio: 'pipe'
      });

      npm.on('close', (code) => {
        if (code === 0) {
          spinner.succeed('Node.js dependencies installed');
          resolve();
        } else {
          spinner.fail('Failed to install Node.js dependencies');
          reject(new Error(`npm install failed with code ${code}`));
        }
      });

      npm.on('error', (error) => {
        spinner.fail('Failed to install Node.js dependencies');
        reject(error);
      });
    });
  }

  async installVSCodeExtensions() {
    const spinner = ora('Installing VS Code extensions...').start();

    try {
      for (const extension of this.requiredExtensions) {
        spinner.text = `Installing ${extension}...`;
        await this.installExtension(extension);
      }

      spinner.succeed(`Installed ${this.requiredExtensions.length} VS Code extensions`);
    } catch (error) {
      spinner.fail(`Failed to install extensions: ${error.message}`);
      throw error;
    }
  }

  async installExtension(extensionId) {
    return new Promise((resolve, reject) => {
      exec(`code --install-extension ${extensionId}`, (error, stdout, stderr) => {
        if (error) {
          console.log(chalk.yellow(`⚠️  Could not install ${extensionId}: ${error.message}`));
          resolve(); // Continue with other extensions
        } else {
          console.log(chalk.green(`✓ Installed ${extensionId}`));
          resolve();
        }
      });
    });
  }

  async configureVSCodeSettings() {
    const spinner = ora('Configuring VS Code settings...').start();

    try {
      const vscodeDir = path.join(process.env.APPDATA || process.env.HOME, 'Code', 'User');
      await fs.ensureDir(vscodeDir);

      const settingsPath = path.join(vscodeDir, 'settings.json');
      let settings = {};

      // Load existing settings
      if (await fs.pathExists(settingsPath)) {
        try {
          settings = await fs.readJson(settingsPath);
        } catch (error) {
          console.log(chalk.yellow('⚠️  Could not parse existing settings.json, creating new one'));
        }
      }

      // Add AI orchestration settings
      const aiSettings = {
        'ai-orchestration.enabled': true,
        'ai-orchestration.autoStart': true,
        'ai-orchestration.tools.priority': ['augmentCode', 'cursor', 'windsurf', 'tabnine'],
        'tabnine.experimentalAutoImports': true,
        'tabnine.receiveBetaChannelUpdates': true,
        'editor.inlineSuggest.enabled': true,
        'editor.suggestSelection': 'first',
        'workbench.colorTheme': 'Default Dark+',
        'workbench.iconTheme': 'material-icon-theme'
      };

      // Merge settings
      Object.assign(settings, aiSettings);

      // Save settings
      await fs.writeJson(settingsPath, settings, { spaces: 2 });

      spinner.succeed('VS Code settings configured');
    } catch (error) {
      spinner.fail(`Failed to configure VS Code settings: ${error.message}`);
      throw error;
    }
  }

  async createProjectTemplates() {
    const spinner = ora('Creating project templates...').start();

    try {
      const templatesDir = path.join(__dirname, 'templates');
      await fs.ensureDir(templatesDir);

      // React TypeScript template
      await this.createReactTemplate(templatesDir);

      // Node.js API template
      await this.createNodeTemplate(templatesDir);

      // Python template
      await this.createPythonTemplate(templatesDir);

      spinner.succeed('Project templates created');
    } catch (error) {
      spinner.fail(`Failed to create templates: ${error.message}`);
      throw error;
    }
  }

  async createReactTemplate(templatesDir) {
    const reactDir = path.join(templatesDir, 'react-typescript');
    await fs.ensureDir(reactDir);

    const packageJson = {
      name: 'ai-orchestrated-react-app',
      version: '1.0.0',
      scripts: {
        start: 'react-scripts start',
        build: 'react-scripts build',
        test: 'react-scripts test',
        eject: 'react-scripts eject',
        'ai:analyze': 'node ../../orchestrator.js analyze',
        'ai:generate': 'node ../../orchestrator.js generate'
      },
      dependencies: {
        react: '^18.2.0',
        'react-dom': '^18.2.0',
        typescript: '^5.0.0'
      },
      devDependencies: {
        '@types/react': '^18.2.0',
        '@types/react-dom': '^18.2.0',
        'react-scripts': '^5.0.1'
      }
    };

    await fs.writeJson(path.join(reactDir, 'package.json'), packageJson, { spaces: 2 });

    const tsConfig = {
      compilerOptions: {
        target: 'es5',
        lib: ['dom', 'dom.iterable', 'es6'],
        allowJs: true,
        skipLibCheck: true,
        esModuleInterop: true,
        allowSyntheticDefaultImports: true,
        strict: true,
        forceConsistentCasingInFileNames: true,
        moduleResolution: 'node',
        resolveJsonModule: true,
        isolatedModules: true,
        noEmit: true,
        jsx: 'react-jsx'
      },
      include: ['src']
    };

    await fs.writeJson(path.join(reactDir, 'tsconfig.json'), tsConfig, { spaces: 2 });
  }

  async createNodeTemplate(templatesDir) {
    const nodeDir = path.join(templatesDir, 'nodejs-api');
    await fs.ensureDir(nodeDir);

    const packageJson = {
      name: 'ai-orchestrated-api',
      version: '1.0.0',
      scripts: {
        start: 'node server.js',
        dev: 'nodemon server.js',
        test: 'jest',
        'ai:analyze': 'node ../../orchestrator.js analyze',
        'ai:generate': 'node ../../orchestrator.js generate'
      },
      dependencies: {
        express: '^4.18.2',
        cors: '^2.8.5',
        helmet: '^7.0.0',
        dotenv: '^16.3.1'
      },
      devDependencies: {
        nodemon: '^3.0.1',
        jest: '^29.7.0'
      }
    };

    await fs.writeJson(path.join(nodeDir, 'package.json'), packageJson, { spaces: 2 });
  }

  async createPythonTemplate(templatesDir) {
    const pythonDir = path.join(templatesDir, 'python-project');
    await fs.ensureDir(pythonDir);

    const requirements = `# AI Orchestrated Python Project
fastapi==0.104.1
uvicorn==0.24.0
pydantic==2.5.0
pytest==7.4.3
black==23.11.0
flake8==6.1.0
`;

    await fs.writeFile(path.join(pythonDir, 'requirements.txt'), requirements);

    const pyprojectToml = `[tool.black]
line-length = 88
target-version = ['py38']

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]

[build-system]
requires = ["setuptools", "wheel"]
build-backend = "setuptools.build_meta"
`;

    await fs.writeFile(path.join(pythonDir, 'pyproject.toml'), pyprojectToml);
  }

  async showManualInstructions() {
    console.log(chalk.blue.bold('\n📋 AI Tools Installation:\n'));

    // Try to install tools automatically first
    await this.installAITools();

    console.log(chalk.blue.bold('\n📋 Manual Installation Required:\n'));

    for (const tool of this.manualInstallTools) {
      console.log(chalk.yellow(`🔧 ${tool.name}`));
      console.log(chalk.white(`   Description: ${tool.description}`));
      console.log(chalk.cyan(`   Download: ${tool.url}`));
      console.log();
    }

    const { proceed } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'proceed',
        message: 'Have you installed the manual tools above?',
        default: false
      }
    ]);

    if (!proceed) {
      console.log(chalk.yellow('⏸️  Setup paused. Please install the manual tools and run setup again.'));
      process.exit(0);
    }
  }

  async installAITools() {
    const spinner = ora('Installing AI tools automatically...').start();

    try {
      // Install Tabnine via VS Code extension (already in requiredExtensions)
      spinner.text = 'Tabnine already configured via VS Code extensions';

      // Try to download and install Cursor
      await this.downloadAndInstallCursor();

      // Try to download and install Windsurf
      await this.downloadAndInstallWindsurf();

      // Configure Augment Code
      await this.configureAugmentCode();

      spinner.succeed('AI tools installation completed');
    } catch (error) {
      spinner.warn(`Some tools require manual installation: ${error.message}`);
    }
  }

  async downloadAndInstallCursor() {
    const platform = process.platform;
    let downloadUrl;

    switch (platform) {
      case 'win32':
        downloadUrl = 'https://download.cursor.sh/windows/nsis/x64';
        break;
      case 'darwin':
        downloadUrl = 'https://download.cursor.sh/mac/dmg/x64';
        break;
      case 'linux':
        downloadUrl = 'https://download.cursor.sh/linux/appimage/x64';
        break;
      default:
        throw new Error(`Unsupported platform for Cursor: ${platform}`);
    }

    console.log(chalk.blue(`📥 Downloading Cursor from ${downloadUrl}`));
    console.log(chalk.yellow('⚠️  Please run the downloaded installer manually'));
  }

  async downloadAndInstallWindsurf() {
    const platform = process.platform;
    let downloadUrl;

    switch (platform) {
      case 'win32':
        downloadUrl = 'https://windsurf-stable.codeiumdata.com/windows/x64/stable';
        break;
      case 'darwin':
        downloadUrl = 'https://windsurf-stable.codeiumdata.com/mac/stable';
        break;
      case 'linux':
        downloadUrl = 'https://windsurf-stable.codeiumdata.com/linux/x64/stable';
        break;
      default:
        throw new Error(`Unsupported platform for Windsurf: ${platform}`);
    }

    console.log(chalk.blue(`📥 Downloading Windsurf from ${downloadUrl}`));
    console.log(chalk.yellow('⚠️  Please run the downloaded installer manually'));
  }

  async configureAugmentCode() {
    console.log(chalk.blue('🧠 Configuring Augment Code integration...'));

    // Create Augment Code configuration
    const augmentConfig = {
      enabled: true,
      role: 'primary-analyzer',
      priority: 1,
      features: ['analysis', 'indexing', 'context', 'search', 'dependencies'],
      settings: {
        autoIndex: true,
        deepAnalysis: true,
        contextWindow: 'large',
        includeTests: true,
        includeDocs: true,
        scanDepth: 'full'
      },
      apiEndpoints: {
        analyze: '/api/analyze',
        search: '/api/search',
        context: '/api/context',
        dependencies: '/api/dependencies'
      }
    };

    const configPath = path.join(__dirname, 'config', 'augment-code.json');
    await fs.ensureDir(path.dirname(configPath));
    await fs.writeJson(configPath, augmentConfig, { spaces: 2 });

    console.log(chalk.green('✓ Augment Code configuration created'));
  }

  async finalizeSetup() {
    const spinner = ora('Finalizing setup...').start();

    try {
      // Create launch scripts
      await this.createLaunchScripts();

      // Create README
      await this.createReadme();

      spinner.succeed('Setup finalized');
    } catch (error) {
      spinner.fail(`Failed to finalize setup: ${error.message}`);
      throw error;
    }
  }

  async createLaunchScripts() {
    // Windows batch script
    const batchScript = `@echo off
echo Starting AI Orchestration Hub...
cd /d "%~dp0"
npm run init
pause
`;

    await fs.writeFile(path.join(__dirname, 'start-orchestration.bat'), batchScript);

    // Unix shell script
    const shellScript = `#!/bin/bash
echo "Starting AI Orchestration Hub..."
cd "$(dirname "$0")"
npm run init
read -p "Press any key to continue..."
`;

    await fs.writeFile(path.join(__dirname, 'start-orchestration.sh'), shellScript);

    // Make shell script executable
    try {
      await fs.chmod(path.join(__dirname, 'start-orchestration.sh'), '755');
    } catch (error) {
      // Ignore on Windows
    }
  }

  async createReadme() {
    const readme = `# AI Orchestration Hub

Universal VS Code workspace for orchestrating multiple AI coding tools.

## Installed Tools

- ✅ **Augment Code**: Codebase context engine
- ✅ **Cursor**: AI-powered code generation
- ✅ **Windsurf**: Multi-file AI editing
- ✅ **Tabnine**: Inline code completion

## Quick Start

1. Open \`AI-Orchestration-Workspace.code-workspace\`
2. Run: \`npm run init\`
3. Use the command palette: \`Ctrl+Shift+P\` → "Tasks: Run Task"

## Available Commands

- \`npm run analyze\` - Analyze project with Augment Code
- \`npm run generate\` - Generate code with AI tools
- \`npm run workflow\` - Execute predefined workflows

## Workflows

- **Feature Development**: Analyze → Plan → Generate → Validate
- **Code Refactoring**: Analyze → Identify → Refactor → Validate
- **Bug Fixing**: Analyze → Locate → Fix → Test
- **Security Analysis**: Scan → Report → Recommend → Fix

## Project Templates

- React TypeScript (\`templates/react-typescript\`)
- Node.js API (\`templates/nodejs-api\`)
- Python Project (\`templates/python-project\`)

## API Endpoints

- \`GET /api/status\` - Check orchestration status
- \`POST /api/analyze\` - Analyze project
- \`POST /api/generate\` - Generate code
- \`POST /api/workflow\` - Execute workflow

## WebSocket

Connect to \`ws://localhost:3001\` for real-time communication.

---

Created by AI Orchestration Setup v1.0.0
`;

    await fs.writeFile(path.join(__dirname, 'README.md'), readme);
  }
}

// Run setup if called directly
if (require.main === module) {
  const setup = new AIOrchestrationSetup();
  setup.run().catch(console.error);
}

module.exports = { AIOrchestrationSetup };

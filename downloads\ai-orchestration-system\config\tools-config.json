{"orchestration": {"version": "1.0.0", "crossFlowEnabled": true, "autoStart": true, "logLevel": "info"}, "augmentCode": {"enabled": true, "role": "primary-analyzer", "priority": 1, "features": ["analysis", "indexing", "context", "search", "dependencies", "security", "performance"], "settings": {"autoIndex": true, "deepAnalysis": true, "contextWindow": "large", "includeTests": true, "includeDocs": true, "scanDepth": "full", "cacheResults": true, "realTimeIndexing": true}, "apiEndpoints": {"analyze": "/api/analyze", "search": "/api/search", "context": "/api/context", "dependencies": "/api/dependencies", "security": "/api/security"}, "integration": {"vscode": true, "webSocket": true, "restApi": true}}, "cursor": {"enabled": true, "role": "code-generator", "priority": 2, "features": ["generation", "completion", "refactoring", "chat", "smartRewrites"], "settings": {"model": "claude-3.5-sonnet", "contextWindow": "large", "codeGeneration": true, "smartRewrites": true, "autoComplete": true, "chatMode": true}, "apiConfig": {"apiKey": "${CURSOR_API_KEY}", "baseUrl": "https://api.cursor.sh", "timeout": 30000, "retries": 3}, "integration": {"orchestration": {"enabled": true, "role": "code-generator", "triggerPatterns": ["single-file", "component", "function", "bug-fix"]}}, "codeGeneration": {"languages": ["javascript", "typescript", "python", "rust", "go"], "frameworks": ["react", "vue", "angular", "express", "<PERSON><PERSON><PERSON>"], "patterns": ["functional", "oop", "mvc", "component-based"], "includeTests": true, "includeDocumentation": true}}, "windsurf": {"enabled": true, "role": "multi-file-editor", "priority": 2, "features": ["multi-file-edit", "context-aware", "large-changes", "refactoring", "architecture"], "settings": {"model": "claude-3.5-sonnet", "contextWindow": "large", "multiFileEditing": true, "contextAware": true, "architectureAware": true, "dependencyTracking": true}, "apiConfig": {"apiKey": "${WINDSURF_API_KEY}", "baseUrl": "https://api.codeium.com", "timeout": 60000, "retries": 3}, "integration": {"orchestration": {"enabled": true, "role": "multi-file-editor", "triggerPatterns": ["multi-file", "feature", "module", "refactoring", "architecture"]}}, "multiFileGeneration": {"maxFiles": 20, "trackDependencies": true, "generateTests": true, "generateDocs": true, "maintainConsistency": true, "architecturePatterns": ["mvc", "layered", "microservices", "component"]}}, "tabnine": {"enabled": true, "role": "inline-completion", "priority": 3, "features": ["inline-completion", "suggestions", "auto-import", "code-enhancement", "real-time"], "settings": {"experimentalAutoImports": true, "receiveBetaChannelUpdates": true, "inlineSuggestEnabled": true, "realTimeCompletion": true, "contextAware": true, "learningEnabled": true}, "integration": {"orchestration": {"enabled": true, "role": "inline-completion", "triggerPatterns": ["real-time", "completion", "enhancement", "suggestions"]}, "vscode": {"enabled": true, "autoActivate": true, "showInlineCompletions": true}}, "completion": {"languages": ["javascript", "typescript", "python", "rust", "go", "java"], "frameworks": ["react", "vue", "angular", "express", "django", "flask"], "contextLines": 50, "suggestionCount": 5, "autoImport": true, "codeEnhancement": true}}, "crossFlow": {"enabled": true, "workflows": {"featureImplementation": {"enabled": true, "steps": [{"name": "projectAnalysis", "tool": "augmentCode", "required": true, "timeout": 30000}, {"name": "planGeneration", "tool": "orchestrator", "required": true, "timeout": 10000}, {"name": "codeGeneration", "tool": "cursor|windsurf", "required": true, "timeout": 60000, "selection": "auto"}, {"name": "codeEnhancement", "tool": "tabnine", "required": false, "timeout": 15000}, {"name": "validation", "tool": "augmentCode", "required": true, "timeout": 30000}, {"name": "finalIntegration", "tool": "orchestrator", "required": true, "timeout": 10000}], "routing": {"singleFile": "cursor", "multiFile": "windsurf", "complexity": {"low": "cursor", "medium": "windsurf", "high": "windsurf"}}}}, "contextSharing": {"enabled": true, "cacheResults": true, "passContext": true, "aggregateResults": true}, "validation": {"enabled": true, "autoFix": true, "iterativeImprovement": true, "qualityThreshold": 0.8}}, "integration": {"vscode": {"enabled": true, "tasks": true, "debugConfigs": true, "settings": true, "extensions": true}, "api": {"enabled": true, "port": 3000, "cors": true, "authentication": false}, "webSocket": {"enabled": true, "port": 3001, "realTime": true, "broadcasting": true}, "fileSystem": {"enabled": true, "watchFiles": true, "autoSave": true, "backupResults": true}}, "logging": {"level": "info", "file": "logs/orchestration.log", "console": true, "structured": true, "includeTimestamp": true, "includeContext": true}, "performance": {"caching": {"enabled": true, "ttl": 3600000, "maxSize": "100MB"}, "parallelExecution": {"enabled": true, "maxConcurrent": 4}, "optimization": {"enabled": true, "contextCompression": true, "resultAggregation": true}}, "security": {"apiKeys": {"encryption": true, "storage": "environment"}, "validation": {"enabled": true, "sanitizeInputs": true, "validateOutputs": true}, "access": {"rateLimiting": true, "requestValidation": true}}}
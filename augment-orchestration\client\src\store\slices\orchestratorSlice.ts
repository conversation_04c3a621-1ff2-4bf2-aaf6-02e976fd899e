import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import { MetaOrchestrator } from '../../types'
import { orchestratorApi } from '../../services/api'

interface OrchestratorState {
  orchestrators: MetaOrchestrator[]
  currentOrchestrator: MetaOrchestrator | null
  isLoading: boolean
  error: string | null
}

const initialState: OrchestratorState = {
  orchestrators: [],
  currentOrchestrator: null,
  isLoading: false,
  error: null,
}

export const fetchOrchestrators = createAsyncThunk(
  'orchestrator/fetchAll',
  async () => {
    const response = await orchestratorApi.getAll()
    return response.data
  }
)

export const fetchOrchestrator = createAsyncThunk(
  'orchestrator/fetchOne',
  async (id: string) => {
    const response = await orchestratorApi.getById(id)
    return response.data
  }
)

const orchestratorSlice = createSlice({
  name: 'orchestrator',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null
    },
    setCurrentOrchestrator: (state, action) => {
      state.currentOrchestrator = action.payload
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchOrchestrators.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(fetchOrchestrators.fulfilled, (state, action) => {
        state.isLoading = false
        state.orchestrators = action.payload
      })
      .addCase(fetchOrchestrators.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.error.message || 'Failed to fetch orchestrators'
      })
      .addCase(fetchOrchestrator.fulfilled, (state, action) => {
        state.currentOrchestrator = action.payload
      })
  },
})

export const { clearError, setCurrentOrchestrator } = orchestratorSlice.actions
export default orchestratorSlice.reducer

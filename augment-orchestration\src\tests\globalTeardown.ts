import { PrismaClient } from '@prisma/client';

export default async function globalTeardown() {
  console.log('🧹 Cleaning up test environment...');

  // Clean up test database if needed
  if (process.env.USE_TEST_DATABASE === 'true') {
    const prisma = new PrismaClient({
      datasources: {
        db: {
          url: process.env.DATABASE_URL,
        },
      },
    });

    try {
      await prisma.$connect();
      console.log('✅ Connected to test database for cleanup');

      // Clean up all test data
      await cleanupAllTestData(prisma);
      console.log('✅ Test data cleaned up');

    } catch (error) {
      console.error('❌ Failed to cleanup test database:', error);
      // Don't throw error in teardown to avoid masking test failures
    } finally {
      await prisma.$disconnect();
      console.log('✅ Disconnected from test database');
    }
  }

  // Clean up any global test state
  delete process.env.TEST_DATABASE_URL;
  delete process.env.JWT_SECRET;
  
  console.log('✅ Test environment cleanup complete');
}

async function cleanupAllTestData(prisma: PrismaClient) {
  // Clean up in reverse dependency order to avoid foreign key constraints
  const tables = [
    'auditLog',
    'workflowExecution',
    'workflowTemplate',
    'sharedContext',
    'comment',
    'notification',
    'agent',
    'userRole',
    'user',
    'role',
  ];

  for (const table of tables) {
    try {
      // Use raw query to delete test data
      await prisma.$executeRaw`DELETE FROM "${table}" WHERE id LIKE 'test-%'`;
      console.log(`✅ Cleaned up ${table} test data`);
    } catch (error) {
      console.warn(`⚠️ Failed to cleanup ${table}:`, error.message);
    }
  }
}

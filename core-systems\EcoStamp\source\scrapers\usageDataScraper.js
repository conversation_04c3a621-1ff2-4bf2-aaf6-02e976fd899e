/**
 * Data scraper for public AI usage logs
 * Collects energy and water usage data from various AI providers
 */

import fs from 'fs/promises';
import path from 'path';

// Data sources for AI usage information
const DATA_SOURCES = {
  openai: {
    name: 'OpenAI',
    urls: [
      'https://openai.com/blog/sustainability',
      'https://openai.com/research/gpt-4-system-card'
    ],
    patterns: {
      energy: /energy.*?(\d+\.?\d*)\s*(kwh|mwh|gwh)/gi,
      water: /water.*?(\d+\.?\d*)\s*(l|ml|liters?|gallons?)/gi,
      tokens: /tokens?.*?(\d+\.?\d*)\s*(billion|million|thousand)?/gi
    }
  },
  anthropic: {
    name: 'Anthrop<PERSON> (<PERSON>)',
    urls: [
      'https://www.anthropic.com/news/claude-2',
      'https://www.anthropic.com/research'
    ],
    patterns: {
      energy: /energy.*?(\d+\.?\d*)\s*(kwh|mwh|gwh)/gi,
      water: /water.*?(\d+\.?\d*)\s*(l|ml|liters?|gallons?)/gi
    }
  },
  google: {
    name: 'Google (Gemini)',
    urls: [
      'https://ai.google/responsibility/environmental-impact/',
      'https://sustainability.google/reports/'
    ],
    patterns: {
      energy: /energy.*?(\d+\.?\d*)\s*(kwh|mwh|gwh)/gi,
      water: /water.*?(\d+\.?\d*)\s*(l|ml|liters?|gallons?)/gi
    }
  }
};

/**
 * Fetch and parse usage data from a URL
 * @param {string} url - URL to scrape
 * @param {object} patterns - Regex patterns to extract data
 * @returns {object} Extracted usage data
 */
async function scrapeUsageData(url, patterns) {
  try {
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const html = await response.text();
    const data = {};
    
    // Extract energy data
    const energyMatches = [...html.matchAll(patterns.energy)];
    if (energyMatches.length > 0) {
      data.energy = energyMatches.map(match => ({
        value: parseFloat(match[1]),
        unit: match[2].toLowerCase(),
        context: match[0]
      }));
    }
    
    // Extract water data
    const waterMatches = [...html.matchAll(patterns.water)];
    if (waterMatches.length > 0) {
      data.water = waterMatches.map(match => ({
        value: parseFloat(match[1]),
        unit: match[2].toLowerCase(),
        context: match[0]
      }));
    }
    
    // Extract token data (if pattern exists)
    if (patterns.tokens) {
      const tokenMatches = [...html.matchAll(patterns.tokens)];
      if (tokenMatches.length > 0) {
        data.tokens = tokenMatches.map(match => ({
          value: parseFloat(match[1]),
          scale: match[2] || 'units',
          context: match[0]
        }));
      }
    }
    
    return {
      url,
      timestamp: new Date().toISOString(),
      data,
      success: true
    };
    
  } catch (error) {
    console.error(`Error scraping ${url}:`, error.message);
    return {
      url,
      timestamp: new Date().toISOString(),
      error: error.message,
      success: false
    };
  }
}

/**
 * Scrape all configured data sources
 * @returns {object} Complete usage data from all sources
 */
export async function scrapeAllUsageData() {
  const results = {};
  
  for (const [provider, config] of Object.entries(DATA_SOURCES)) {
    console.log(`Scraping data for ${config.name}...`);
    results[provider] = {
      name: config.name,
      sources: []
    };
    
    for (const url of config.urls) {
      const result = await scrapeUsageData(url, config.patterns);
      results[provider].sources.push(result);
      
      // Add delay between requests to be respectful
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
  
  return results;
}

/**
 * Estimate usage for providers without public data based on OpenAI data
 * @param {object} openaiData - OpenAI usage data
 * @returns {object} Estimated usage for other providers
 */
export function estimateProviderUsage(openaiData) {
  // Estimation multipliers based on model size and efficiency
  const ESTIMATION_MULTIPLIERS = {
    claude: {
      energy: 0.85, // Anthropic claims more efficiency
      water: 0.80,
      name: 'Claude (Estimated)'
    },
    gemini: {
      energy: 1.15, // Google's infrastructure might use more
      water: 1.10,
      name: 'Gemini (Estimated)'
    }
  };
  
  const estimates = {};
  
  if (openaiData && openaiData.energy && openaiData.water) {
    for (const [provider, multipliers] of Object.entries(ESTIMATION_MULTIPLIERS)) {
      estimates[provider] = {
        name: multipliers.name,
        energy: openaiData.energy * multipliers.energy,
        water: openaiData.water * multipliers.water,
        estimatedFrom: 'openai',
        timestamp: new Date().toISOString()
      };
    }
  }
  
  return estimates;
}

/**
 * Save scraped data to file
 * @param {object} data - Usage data to save
 * @param {string} filename - Output filename
 */
export async function saveUsageData(data, filename = null) {
  const timestamp = new Date().toISOString().split('T')[0];
  const outputFile = filename || `usage-data-${timestamp}.json`;
  const outputPath = path.join(process.cwd(), 'data', outputFile);
  
  try {
    // Ensure data directory exists
    await fs.mkdir(path.dirname(outputPath), { recursive: true });
    
    // Save data
    await fs.writeFile(outputPath, JSON.stringify(data, null, 2));
    console.log(`Usage data saved to: ${outputPath}`);
    
    return outputPath;
  } catch (error) {
    console.error('Error saving usage data:', error);
    throw error;
  }
}

/**
 * Load the most recent usage data
 * @returns {object|null} Most recent usage data or null if not found
 */
export async function loadLatestUsageData() {
  try {
    const dataDir = path.join(process.cwd(), 'data');
    const files = await fs.readdir(dataDir);
    const usageFiles = files
      .filter(f => f.startsWith('usage-data-') && f.endsWith('.json'))
      .sort()
      .reverse();
    
    if (usageFiles.length === 0) {
      return null;
    }
    
    const latestFile = path.join(dataDir, usageFiles[0]);
    const data = await fs.readFile(latestFile, 'utf8');
    return JSON.parse(data);
    
  } catch (error) {
    console.error('Error loading usage data:', error);
    return null;
  }
}

/**
 * Run the complete scraping process
 */
export async function runScrapingProcess() {
  console.log('Starting AI usage data scraping process...');
  
  try {
    // Scrape all sources
    const scrapedData = await scrapeAllUsageData();
    
    // Generate estimates
    const openaiData = scrapedData.openai?.sources?.[0]?.data;
    const estimates = estimateProviderUsage(openaiData);
    
    // Combine all data
    const completeData = {
      timestamp: new Date().toISOString(),
      scraped: scrapedData,
      estimates,
      metadata: {
        sources: Object.keys(DATA_SOURCES).length,
        estimationMethod: 'multiplier-based',
        version: '1.0'
      }
    };
    
    // Save data
    const savedPath = await saveUsageData(completeData);
    
    console.log('Scraping process completed successfully!');
    return completeData;
    
  } catch (error) {
    console.error('Error in scraping process:', error);
    throw error;
  }
}

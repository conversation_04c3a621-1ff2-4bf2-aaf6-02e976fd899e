/**
 * Validator for DGM Agents
 * 
 * Comprehensive validation system for orchestration agents:
 * - Code syntax and structure validation
 * - Security vulnerability scanning
 * - Performance threshold checking
 * - Safety compliance verification
 */

const fs = require('fs').promises;
const path = require('path');
const chalk = require('chalk');

class Validator {
  constructor(config) {
    this.config = config;
    this.validationCriteria = config.get('evaluation.validationCriteria', {
      syntaxCheck: true,
      securityScan: true,
      performanceThreshold: 0.8,
      reliabilityThreshold: 0.9,
      safetyThreshold: 0.95
    });
    this.securityRules = this.loadSecurityRules();
    this.performanceRules = this.loadPerformanceRules();
  }

  /**
   * Validate an agent comprehensively
   */
  async validateAgent(agent) {
    console.log(chalk.blue(`🔍 Validating agent ${agent.id}...`));
    
    const validationResults = {
      agentId: agent.id,
      timestamp: new Date(),
      isValid: true,
      overallScore: 0,
      safetyScore: 0,
      issues: [],
      warnings: [],
      recommendations: [],
      validationDetails: {
        syntax: null,
        security: null,
        performance: null,
        safety: null,
        structure: null
      }
    };

    try {
      // Run all validation checks
      validationResults.validationDetails.syntax = await this.validateSyntax(agent);
      validationResults.validationDetails.security = await this.validateSecurity(agent);
      validationResults.validationDetails.performance = await this.validatePerformance(agent);
      validationResults.validationDetails.safety = await this.validateSafety(agent);
      validationResults.validationDetails.structure = await this.validateStructure(agent);

      // Aggregate results
      this.aggregateValidationResults(validationResults);

      console.log(chalk.green(`✅ Agent validation completed: ${validationResults.overallScore.toFixed(3)} score`));
      
    } catch (error) {
      validationResults.isValid = false;
      validationResults.issues.push(`Validation error: ${error.message}`);
      console.error(chalk.red(`❌ Agent validation failed: ${error.message}`));
    }

    return validationResults;
  }

  /**
   * Validate code syntax
   */
  async validateSyntax(agent) {
    const syntaxResults = {
      passed: true,
      score: 1.0,
      issues: [],
      warnings: []
    };

    if (!this.validationCriteria.syntaxCheck) {
      return syntaxResults;
    }

    try {
      // Validate orchestrator syntax
      await this.checkJavaScriptSyntax(agent.code.orchestrator, 'orchestrator.js');
      
      // Validate cross-flow engine syntax
      await this.checkJavaScriptSyntax(agent.code.crossFlow, 'cross-flow-engine.js');
      
      // Validate workflow files
      for (const [name, code] of Object.entries(agent.code.workflows || {})) {
        await this.checkJavaScriptSyntax(code, `workflows/${name}.js`);
      }

    } catch (error) {
      syntaxResults.passed = false;
      syntaxResults.score = 0;
      syntaxResults.issues.push(error.message);
    }

    return syntaxResults;
  }

  /**
   * Check JavaScript syntax
   */
  async checkJavaScriptSyntax(code, filename) {
    try {
      // Basic syntax check using Function constructor
      new Function(code);
      
      // Additional checks for common issues
      this.checkForCommonSyntaxIssues(code, filename);
      
    } catch (error) {
      throw new Error(`Syntax error in ${filename}: ${error.message}`);
    }
  }

  /**
   * Check for common syntax issues
   */
  checkForCommonSyntaxIssues(code, filename) {
    const issues = [];
    
    // Check for unmatched brackets
    const openBrackets = (code.match(/\{/g) || []).length;
    const closeBrackets = (code.match(/\}/g) || []).length;
    if (openBrackets !== closeBrackets) {
      issues.push(`Unmatched brackets in ${filename}`);
    }
    
    // Check for unmatched parentheses
    const openParens = (code.match(/\(/g) || []).length;
    const closeParens = (code.match(/\)/g) || []).length;
    if (openParens !== closeParens) {
      issues.push(`Unmatched parentheses in ${filename}`);
    }
    
    // Check for missing semicolons (basic check)
    const lines = code.split('\n');
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      if (line.length > 0 && 
          !line.endsWith(';') && 
          !line.endsWith('{') && 
          !line.endsWith('}') &&
          !line.startsWith('//') &&
          !line.startsWith('*') &&
          !line.includes('if (') &&
          !line.includes('for (') &&
          !line.includes('while (')) {
        // This is a simplified check - in practice, you'd use a proper parser
      }
    }
    
    if (issues.length > 0) {
      throw new Error(issues.join('; '));
    }
  }

  /**
   * Validate security
   */
  async validateSecurity(agent) {
    const securityResults = {
      passed: true,
      score: 1.0,
      issues: [],
      warnings: [],
      vulnerabilities: []
    };

    if (!this.validationCriteria.securityScan) {
      return securityResults;
    }

    const codeToScan = [
      { name: 'orchestrator.js', code: agent.code.orchestrator },
      { name: 'cross-flow-engine.js', code: agent.code.crossFlow },
      ...Object.entries(agent.code.workflows || {}).map(([name, code]) => ({
        name: `workflows/${name}.js`,
        code
      }))
    ];

    let totalVulnerabilities = 0;
    let totalChecks = 0;

    for (const file of codeToScan) {
      const fileVulnerabilities = this.scanForVulnerabilities(file.code, file.name);
      totalVulnerabilities += fileVulnerabilities.length;
      totalChecks += this.securityRules.length;
      
      securityResults.vulnerabilities.push(...fileVulnerabilities);
    }

    // Calculate security score
    if (totalChecks > 0) {
      securityResults.score = Math.max(0, 1 - (totalVulnerabilities / totalChecks));
    }

    // Determine if security validation passed
    securityResults.passed = securityResults.score >= 0.8; // 80% threshold

    if (!securityResults.passed) {
      securityResults.issues.push(`Security score ${securityResults.score.toFixed(3)} below threshold`);
    }

    return securityResults;
  }

  /**
   * Scan for security vulnerabilities
   */
  scanForVulnerabilities(code, filename) {
    const vulnerabilities = [];

    for (const rule of this.securityRules) {
      if (rule.pattern.test(code)) {
        vulnerabilities.push({
          rule: rule.name,
          severity: rule.severity,
          description: rule.description,
          file: filename,
          recommendation: rule.recommendation
        });
      }
    }

    return vulnerabilities;
  }

  /**
   * Validate performance
   */
  async validatePerformance(agent) {
    const performanceResults = {
      passed: true,
      score: 1.0,
      issues: [],
      warnings: [],
      metrics: {}
    };

    // Check code complexity
    performanceResults.metrics.complexity = this.calculateCodeComplexity(agent.code);
    
    // Check code size
    performanceResults.metrics.codeSize = this.calculateCodeSize(agent.code);
    
    // Check for performance anti-patterns
    const antiPatterns = this.scanForPerformanceAntiPatterns(agent.code);
    performanceResults.metrics.antiPatterns = antiPatterns.length;

    // Calculate performance score
    let score = 1.0;
    
    // Penalize high complexity
    if (performanceResults.metrics.complexity > 50) {
      score -= 0.2;
      performanceResults.warnings.push('High code complexity detected');
    }
    
    // Penalize large code size
    if (performanceResults.metrics.codeSize > 100000) { // 100KB
      score -= 0.1;
      performanceResults.warnings.push('Large code size may impact performance');
    }
    
    // Penalize anti-patterns
    score -= antiPatterns.length * 0.1;
    if (antiPatterns.length > 0) {
      performanceResults.warnings.push(`${antiPatterns.length} performance anti-patterns found`);
    }

    performanceResults.score = Math.max(0, score);
    performanceResults.passed = performanceResults.score >= this.validationCriteria.performanceThreshold;

    return performanceResults;
  }

  /**
   * Validate safety
   */
  async validateSafety(agent) {
    const safetyResults = {
      passed: true,
      score: 1.0,
      issues: [],
      warnings: [],
      safetyChecks: {}
    };

    // Check error handling
    safetyResults.safetyChecks.errorHandling = this.checkErrorHandling(agent.code);
    
    // Check input validation
    safetyResults.safetyChecks.inputValidation = this.checkInputValidation(agent.code);
    
    // Check resource limits
    safetyResults.safetyChecks.resourceLimits = this.checkResourceLimits(agent.code);
    
    // Check for dangerous operations
    safetyResults.safetyChecks.dangerousOperations = this.checkDangerousOperations(agent.code);

    // Calculate safety score
    const checkScores = Object.values(safetyResults.safetyChecks);
    safetyResults.score = checkScores.reduce((sum, score) => sum + score, 0) / checkScores.length;
    
    safetyResults.passed = safetyResults.score >= this.validationCriteria.safetyThreshold;

    if (!safetyResults.passed) {
      safetyResults.issues.push(`Safety score ${safetyResults.score.toFixed(3)} below threshold`);
    }

    return safetyResults;
  }

  /**
   * Validate code structure
   */
  async validateStructure(agent) {
    const structureResults = {
      passed: true,
      score: 1.0,
      issues: [],
      warnings: [],
      structureChecks: {}
    };

    // Check for required components
    structureResults.structureChecks.hasOrchestrator = !!agent.code.orchestrator;
    structureResults.structureChecks.hasCrossFlow = !!agent.code.crossFlow;
    structureResults.structureChecks.hasWorkflows = Object.keys(agent.code.workflows || {}).length > 0;
    
    // Check for proper exports
    structureResults.structureChecks.hasExports = this.checkForExports(agent.code);
    
    // Check for proper class structure
    structureResults.structureChecks.hasClasses = this.checkForClasses(agent.code);
    
    // Check for documentation
    structureResults.structureChecks.hasDocumentation = this.checkForDocumentation(agent.code);

    // Calculate structure score
    const checks = Object.values(structureResults.structureChecks);
    const passedChecks = checks.filter(check => check === true).length;
    structureResults.score = passedChecks / checks.length;

    structureResults.passed = structureResults.score >= 0.7; // 70% threshold

    if (!structureResults.passed) {
      structureResults.issues.push('Code structure validation failed');
    }

    return structureResults;
  }

  /**
   * Aggregate validation results
   */
  aggregateValidationResults(validationResults) {
    const details = validationResults.validationDetails;
    
    // Collect all issues and warnings
    for (const check of Object.values(details)) {
      if (check && check.issues) {
        validationResults.issues.push(...check.issues);
      }
      if (check && check.warnings) {
        validationResults.warnings.push(...check.warnings);
      }
    }

    // Calculate overall score
    const scores = Object.values(details)
      .filter(check => check && typeof check.score === 'number')
      .map(check => check.score);
    
    validationResults.overallScore = scores.length > 0 ? 
      scores.reduce((sum, score) => sum + score, 0) / scores.length : 0;

    // Calculate safety score (weighted average of safety-related checks)
    const safetyWeight = {
      security: 0.4,
      safety: 0.4,
      syntax: 0.2
    };
    
    validationResults.safetyScore = (
      (details.security?.score || 0) * safetyWeight.security +
      (details.safety?.score || 0) * safetyWeight.safety +
      (details.syntax?.score || 0) * safetyWeight.syntax
    );

    // Determine overall validity
    validationResults.isValid = (
      validationResults.overallScore >= 0.7 &&
      validationResults.safetyScore >= this.validationCriteria.safetyThreshold &&
      validationResults.issues.length === 0
    );

    // Generate recommendations
    this.generateRecommendations(validationResults);
  }

  /**
   * Generate recommendations based on validation results
   */
  generateRecommendations(validationResults) {
    const details = validationResults.validationDetails;

    if (details.syntax && !details.syntax.passed) {
      validationResults.recommendations.push('Fix syntax errors before deployment');
    }

    if (details.security && details.security.score < 0.9) {
      validationResults.recommendations.push('Address security vulnerabilities');
    }

    if (details.performance && details.performance.score < 0.8) {
      validationResults.recommendations.push('Optimize performance bottlenecks');
    }

    if (details.safety && details.safety.score < 0.95) {
      validationResults.recommendations.push('Improve error handling and safety measures');
    }

    if (details.structure && details.structure.score < 0.8) {
      validationResults.recommendations.push('Improve code structure and documentation');
    }

    if (validationResults.warnings.length > 5) {
      validationResults.recommendations.push('Address validation warnings');
    }
  }

  /**
   * Load security rules
   */
  loadSecurityRules() {
    return [
      {
        name: 'eval-usage',
        pattern: /eval\s*\(/,
        severity: 'high',
        description: 'Use of eval() function detected',
        recommendation: 'Avoid using eval() as it can execute arbitrary code'
      },
      {
        name: 'innerHTML-usage',
        pattern: /innerHTML\s*=/,
        severity: 'medium',
        description: 'Use of innerHTML detected',
        recommendation: 'Use textContent or proper DOM methods instead'
      },
      {
        name: 'document-write',
        pattern: /document\.write\s*\(/,
        severity: 'medium',
        description: 'Use of document.write detected',
        recommendation: 'Use modern DOM manipulation methods'
      },
      {
        name: 'unsafe-regex',
        pattern: /new\s+RegExp\s*\(\s*[^)]*\+/,
        severity: 'medium',
        description: 'Potentially unsafe regex construction',
        recommendation: 'Validate regex patterns to prevent ReDoS attacks'
      }
    ];
  }

  /**
   * Load performance rules
   */
  loadPerformanceRules() {
    return [
      {
        name: 'synchronous-fs',
        pattern: /fs\.\w+Sync\s*\(/,
        description: 'Synchronous file system operation',
        recommendation: 'Use asynchronous file operations'
      },
      {
        name: 'nested-loops',
        pattern: /for\s*\([^}]*for\s*\(/,
        description: 'Nested loops detected',
        recommendation: 'Consider optimizing nested loop structures'
      }
    ];
  }

  /**
   * Helper methods for specific checks
   */
  calculateCodeComplexity(code) {
    const codeStr = JSON.stringify(code);
    const complexityPatterns = [
      /if\s*\(/g,
      /for\s*\(/g,
      /while\s*\(/g,
      /switch\s*\(/g,
      /catch\s*\(/g,
      /function\s+/g,
      /=>\s*{/g
    ];

    return complexityPatterns.reduce((complexity, pattern) => {
      return complexity + (codeStr.match(pattern) || []).length;
    }, 0);
  }

  calculateCodeSize(code) {
    return JSON.stringify(code).length;
  }

  scanForPerformanceAntiPatterns(code) {
    const antiPatterns = [];
    const codeStr = JSON.stringify(code);

    for (const rule of this.performanceRules) {
      if (rule.pattern.test(codeStr)) {
        antiPatterns.push(rule);
      }
    }

    return antiPatterns;
  }

  checkErrorHandling(code) {
    const codeStr = JSON.stringify(code);
    const tryCount = (codeStr.match(/try\s*{/g) || []).length;
    const catchCount = (codeStr.match(/catch\s*\(/g) || []).length;
    const errorThrowCount = (codeStr.match(/throw\s+new\s+Error/g) || []).length;

    // Score based on error handling patterns
    const totalPatterns = tryCount + catchCount + errorThrowCount;
    return Math.min(1.0, totalPatterns / 5); // Normalize to 0-1
  }

  checkInputValidation(code) {
    const codeStr = JSON.stringify(code);
    const validationPatterns = [
      /typeof\s+\w+\s*===/g,
      /instanceof\s+/g,
      /Array\.isArray\s*\(/g,
      /\.validate\s*\(/g,
      /\.check\s*\(/g
    ];

    const validationCount = validationPatterns.reduce((count, pattern) => {
      return count + (codeStr.match(pattern) || []).length;
    }, 0);

    return Math.min(1.0, validationCount / 3);
  }

  checkResourceLimits(code) {
    const codeStr = JSON.stringify(code);
    const limitPatterns = [
      /timeout/gi,
      /limit/gi,
      /max\w*/gi,
      /abort/gi,
      /cancel/gi
    ];

    const limitCount = limitPatterns.reduce((count, pattern) => {
      return count + (codeStr.match(pattern) || []).length;
    }, 0);

    return Math.min(1.0, limitCount / 2);
  }

  checkDangerousOperations(code) {
    const codeStr = JSON.stringify(code);
    const dangerousPatterns = [
      /eval\s*\(/g,
      /Function\s*\(/g,
      /setTimeout\s*\(\s*["'`][^"'`]*["'`]/g, // String-based setTimeout
      /setInterval\s*\(\s*["'`][^"'`]*["'`]/g // String-based setInterval
    ];

    const dangerousCount = dangerousPatterns.reduce((count, pattern) => {
      return count + (codeStr.match(pattern) || []).length;
    }, 0);

    return Math.max(0, 1 - (dangerousCount * 0.5)); // Penalize dangerous operations
  }

  checkForExports(code) {
    const codeStr = JSON.stringify(code);
    return codeStr.includes('module.exports') || codeStr.includes('export ');
  }

  checkForClasses(code) {
    const codeStr = JSON.stringify(code);
    return codeStr.includes('class ') || codeStr.includes('function ');
  }

  checkForDocumentation(code) {
    const codeStr = JSON.stringify(code);
    const docPatterns = [
      /\/\*\*/g,
      /\/\//g,
      /\*\s+@/g
    ];

    const docCount = docPatterns.reduce((count, pattern) => {
      return count + (codeStr.match(pattern) || []).length;
    }, 0);

    return docCount > 5; // Minimum documentation threshold
  }

  /**
   * Get validation statistics
   */
  getValidationStats(validationHistory) {
    const stats = {
      totalValidations: validationHistory.length,
      passRate: 0,
      averageScore: 0,
      commonIssues: {},
      securityIssues: 0,
      performanceIssues: 0
    };

    if (validationHistory.length === 0) {
      return stats;
    }

    let passedValidations = 0;
    let totalScore = 0;
    let securityIssues = 0;
    let performanceIssues = 0;

    for (const validation of validationHistory) {
      if (validation.isValid) passedValidations++;
      totalScore += validation.overallScore;

      // Count issue types
      for (const issue of validation.issues) {
        stats.commonIssues[issue] = (stats.commonIssues[issue] || 0) + 1;
      }

      // Count security and performance issues
      if (validation.validationDetails.security && validation.validationDetails.security.vulnerabilities) {
        securityIssues += validation.validationDetails.security.vulnerabilities.length;
      }
      if (validation.validationDetails.performance && validation.validationDetails.performance.antiPatterns) {
        performanceIssues += validation.validationDetails.performance.antiPatterns;
      }
    }

    stats.passRate = passedValidations / validationHistory.length;
    stats.averageScore = totalScore / validationHistory.length;
    stats.securityIssues = securityIssues;
    stats.performanceIssues = performanceIssues;

    return stats;
  }
}

module.exports = Validator;

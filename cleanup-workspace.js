#!/usr/bin/env node

/**
 * EcoStamp Workspace Cleanup Script
 * Removes duplicate files, unnecessary node_modules, and optimizes the workspace
 */

import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';
import { execSync } from 'child_process';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class WorkspaceCleanup {
  constructor() {
    this.workspaceRoot = process.cwd();
    this.duplicateNodeModules = [];
    this.unnecessaryFiles = [];
    this.cleanupStats = {
      nodeModulesRemoved: 0,
      filesRemoved: 0,
      spaceSaved: 0
    };
  }

  async run() {
    console.log('🧹 EcoStamp Workspace Cleanup Starting...\n');
    
    try {
      await this.analyzeWorkspace();
      await this.removeDuplicateNodeModules();
      await this.removeUnnecessaryFiles();
      await this.fixPythonExecutables();
      await this.optimizePackageStructure();
      
      this.printSummary();
      
    } catch (error) {
      console.error('❌ Cleanup failed:', error.message);
      process.exit(1);
    }
  }

  async analyzeWorkspace() {
    console.log('📊 Analyzing workspace structure...');
    
    // Find all node_modules directories
    const nodeModulesDirs = await this.findNodeModulesDirectories();
    
    // Identify duplicates (keep only essential ones)
    const essentialNodeModules = [
      path.join(this.workspaceRoot, 'node_modules'),
      path.join(this.workspaceRoot, 'core-systems', 'EcoStamp', 'source', 'node_modules'),
      path.join(this.workspaceRoot, 'ai-orchestration', 'node_modules'),
      path.join(this.workspaceRoot, 'development-tools', 'node_modules')
    ];

    this.duplicateNodeModules = nodeModulesDirs.filter(dir => 
      !essentialNodeModules.some(essential => path.resolve(dir) === path.resolve(essential))
    );

    // Find unnecessary files
    await this.findUnnecessaryFiles();
    
    console.log(`   Found ${nodeModulesDirs.length} node_modules directories`);
    console.log(`   ${this.duplicateNodeModules.length} marked for removal`);
    console.log(`   ${this.unnecessaryFiles.length} unnecessary files found`);
    console.log('');
  }

  async findNodeModulesDirectories() {
    const nodeModulesDirs = [];
    
    const scanDirectory = async (dir) => {
      try {
        const items = await fs.readdir(dir);
        
        for (const item of items) {
          const fullPath = path.join(dir, item);
          const stat = await fs.stat(fullPath);
          
          if (stat.isDirectory()) {
            if (item === 'node_modules') {
              nodeModulesDirs.push(fullPath);
            } else if (!item.startsWith('.') && item !== 'node_modules') {
              await scanDirectory(fullPath);
            }
          }
        }
      } catch (error) {
        // Skip directories we can't read
      }
    };

    await scanDirectory(this.workspaceRoot);
    return nodeModulesDirs;
  }

  async findUnnecessaryFiles() {
    const unnecessaryPatterns = [
      '**/.DS_Store',
      '**/Thumbs.db',
      '**/*.tmp',
      '**/*.temp',
      '**/npm-debug.log*',
      '**/yarn-debug.log*',
      '**/yarn-error.log*',
      '**/.nyc_output',
      '**/coverage',
      '**/.cache',
      '**/dist/**/*.map',
      '**/build/**/*.map'
    ];

    // Find duplicate package.json files in nested node_modules
    const packageJsonFiles = await this.findFiles('**/package.json');
    
    for (const file of packageJsonFiles) {
      if (file.includes('node_modules') && 
          !this.isEssentialPackageJson(file)) {
        // Skip - these are part of node_modules we'll remove
      }
    }

    // Find test files in production builds
    const testFiles = await this.findFiles('**/*.test.js');
    const specFiles = await this.findFiles('**/*.spec.js');
    
    this.unnecessaryFiles.push(...testFiles.filter(f => 
      f.includes('node_modules') || f.includes('dist') || f.includes('build')
    ));
    this.unnecessaryFiles.push(...specFiles.filter(f => 
      f.includes('node_modules') || f.includes('dist') || f.includes('build')
    ));
  }

  async findFiles(pattern) {
    try {
      const { globby } = await import('globby');
      return await globby(pattern, {
        cwd: this.workspaceRoot,
        ignore: ['**/node_modules/**', '**/.git/**']
      });
    } catch (error) {
      return [];
    }
  }

  isEssentialPackageJson(filePath) {
    const essentialPaths = [
      'package.json',
      'core-systems/EcoStamp/source/package.json',
      'ai-orchestration/package.json',
      'development-tools/package.json',
      'ai-orchestration/meta-orchestrator/package.json'
    ];

    return essentialPaths.some(essential => 
      path.resolve(filePath) === path.resolve(path.join(this.workspaceRoot, essential))
    );
  }

  async removeDuplicateNodeModules() {
    console.log('🗑️  Removing duplicate node_modules directories...');
    
    for (const dir of this.duplicateNodeModules) {
      try {
        const size = await this.getDirectorySize(dir);
        await fs.remove(dir);
        
        this.cleanupStats.nodeModulesRemoved++;
        this.cleanupStats.spaceSaved += size;
        
        console.log(`   ✅ Removed: ${path.relative(this.workspaceRoot, dir)}`);
      } catch (error) {
        console.log(`   ⚠️  Failed to remove: ${path.relative(this.workspaceRoot, dir)}`);
      }
    }
    console.log('');
  }

  async removeUnnecessaryFiles() {
    console.log('🗑️  Removing unnecessary files...');
    
    for (const file of this.unnecessaryFiles) {
      try {
        const size = (await fs.stat(file)).size;
        await fs.remove(file);
        
        this.cleanupStats.filesRemoved++;
        this.cleanupStats.spaceSaved += size;
        
        console.log(`   ✅ Removed: ${path.relative(this.workspaceRoot, file)}`);
      } catch (error) {
        console.log(`   ⚠️  Failed to remove: ${path.relative(this.workspaceRoot, file)}`);
      }
    }
    console.log('');
  }

  async fixPythonExecutables() {
    console.log('🐍 Fixing Python executable issues...');
    
    const pythonFiles = await this.findFiles('**/*.py');
    
    for (const file of pythonFiles) {
      try {
        const content = await fs.readFile(file, 'utf8');
        
        // Check if it should be executable (has shebang or main block)
        if (content.includes('#!/usr/bin/env python') || 
            content.includes('if __name__ == "__main__"')) {
          
          // Make sure it has proper shebang
          if (!content.startsWith('#!')) {
            const newContent = '#!/usr/bin/env python3\n' + content;
            await fs.writeFile(file, newContent);
            console.log(`   ✅ Added shebang to: ${path.relative(this.workspaceRoot, file)}`);
          }
        }
      } catch (error) {
        // Skip files we can't process
      }
    }
    console.log('');
  }

  async optimizePackageStructure() {
    console.log('📦 Optimizing package structure...');
    
    // Remove duplicate dependencies by checking package.json files
    const packageFiles = [
      'package.json',
      'core-systems/EcoStamp/source/package.json',
      'ai-orchestration/package.json',
      'development-tools/package.json'
    ];

    for (const packageFile of packageFiles) {
      const fullPath = path.join(this.workspaceRoot, packageFile);
      
      if (await fs.pathExists(fullPath)) {
        try {
          const pkg = await fs.readJson(fullPath);
          
          // Remove duplicate dependencies that exist in root
          if (packageFile !== 'package.json') {
            await this.optimizePackageDependencies(fullPath, pkg);
          }
        } catch (error) {
          console.log(`   ⚠️  Failed to optimize: ${packageFile}`);
        }
      }
    }
    console.log('');
  }

  async optimizePackageDependencies(packagePath, pkg) {
    // This is a placeholder for dependency optimization
    // In a real scenario, you'd check for duplicates and move common deps to root
    console.log(`   📄 Checked: ${path.relative(this.workspaceRoot, packagePath)}`);
  }

  async getDirectorySize(dirPath) {
    let size = 0;
    
    try {
      const items = await fs.readdir(dirPath);
      
      for (const item of items) {
        const fullPath = path.join(dirPath, item);
        const stat = await fs.stat(fullPath);
        
        if (stat.isDirectory()) {
          size += await this.getDirectorySize(fullPath);
        } else {
          size += stat.size;
        }
      }
    } catch (error) {
      // Skip if we can't read the directory
    }
    
    return size;
  }

  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  printSummary() {
    console.log('📊 Cleanup Summary:');
    console.log('=====================================');
    console.log(`   Node_modules removed: ${this.cleanupStats.nodeModulesRemoved}`);
    console.log(`   Files removed: ${this.cleanupStats.filesRemoved}`);
    console.log(`   Space saved: ${this.formatBytes(this.cleanupStats.spaceSaved)}`);
    console.log('');
    console.log('✅ Workspace cleanup completed successfully!');
    console.log('');
    console.log('Next steps:');
    console.log('1. Run Test-Runner.bat to verify everything still works');
    console.log('2. Reinstall dependencies if needed: npm install');
    console.log('3. Test the EcoStamp system');
    console.log('');
  }
}

// Run cleanup if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const cleanup = new WorkspaceCleanup();
  cleanup.run().catch(error => {
    console.error('Cleanup failed:', error);
    process.exit(1);
  });
}

export default WorkspaceCleanup;

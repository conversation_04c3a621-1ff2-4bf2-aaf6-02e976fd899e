{"timestamp": "2025-07-26T01:39:49.017Z", "scraped": {"openai": {"name": "OpenAI", "sources": [{"url": "https://openai.com/blog/sustainability", "timestamp": "2025-07-26T01:39:40.213Z", "error": "HTTP 403: Forbidden", "success": false}, {"url": "https://openai.com/research/gpt-4-system-card", "timestamp": "2025-07-26T01:39:41.440Z", "error": "HTTP 403: Forbidden", "success": false}]}, "anthropic": {"name": "Anthrop<PERSON> (<PERSON>)", "sources": [{"url": "https://www.anthropic.com/news/claude-2", "timestamp": "2025-07-26T01:39:43.911Z", "data": {}, "success": true}, {"url": "https://www.anthropic.com/research", "timestamp": "2025-07-26T01:39:45.164Z", "data": {}, "success": true}]}, "google": {"name": "Google (Gemini)", "sources": [{"url": "https://ai.google/responsibility/environmental-impact/", "timestamp": "2025-07-26T01:39:46.614Z", "error": "HTTP 404: Not Found", "success": false}, {"url": "https://sustainability.google/reports/", "timestamp": "2025-07-26T01:39:48.006Z", "data": {}, "success": true}]}}, "estimates": {}, "metadata": {"sources": 3, "estimationMethod": "multiplier-based", "version": "1.0"}}
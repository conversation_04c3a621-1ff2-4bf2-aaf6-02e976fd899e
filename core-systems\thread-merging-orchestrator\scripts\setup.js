#!/usr/bin/env node

import fs from 'fs/promises';
import path from 'path';
import readline from 'readline';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.join(__dirname, '..');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => rl.question(prompt, resolve));
}

async function createDirectories() {
  const directories = ['data/threads', 'data/results', 'data/cache', 'logs'];
  console.log('📁 Creating directories...');

  for (const dir of directories) {
    const fullPath = path.join(projectRoot, dir);
    try {
      await fs.mkdir(fullPath, { recursive: true });
      console.log(`  ✅ Created: ${dir}`);
    } catch (error) {
      console.log(`  ❌ Failed to create ${dir}: ${error.message}`);
    }
  }
}

async function setupEnvironment() {
  console.log('\n🔧 Setting up environment variables...');
  const envPath = path.join(projectRoot, '.env');
  const envExamplePath = path.join(projectRoot, '.env.example');

  try {
    await fs.access(envPath);
    console.log('  ℹ️  .env file already exists');
    const overwrite = await question('  Overwrite? (y/N): ');
    if (overwrite.toLowerCase() !== 'y') return;
  } catch {
    // fine, no .env yet
  }

  try {
    const envExample = await fs.readFile(envExamplePath, 'utf8');
    await fs.writeFile(envPath, envExample);
    console.log('  ✅ Created .env file from template');
  } catch (error) {
    console.log(`  ❌ Failed to create .env file: ${error.message}`);
  }
}

async function createGitignore() {
  console.log('\n📝 Creating .gitignore...');
  const gitignorePath = path.join(projectRoot, '.gitignore');
  const gitignoreContent = `node_modules/
.env
data/
logs/
*.log
.vscode/
.idea/
coverage/
tmp/
`;
  try {
    await fs.writeFile(gitignorePath, gitignoreContent);
    console.log('  ✅ Created .gitignore');
  } catch (error) {
    console.log(`  ❌ Failed to create .gitignore: ${error.message}`);
  }
}

async function installDependencies() {
  console.log('\n📦 Installing dependencies...');
  const { spawn } = await import('child_process');

  return new Promise((resolve, reject) => {
    const npm = spawn('npm', ['install'], { cwd: projectRoot, stdio: 'inherit' });
    npm.on('close', (code) => (code === 0 ? resolve() : reject(new Error(`npm failed: ${code}`))));
  });
}

async function setupMain() {
  console.log('🚀 Thread-Merging Orchestrator Setup');
  console.log('=====================================\n');

  try {
    await createDirectories();
    await setupEnvironment();

    const installDeps = await question('\n📦 Install dependencies now? (Y/n): ');
    if (installDeps.toLowerCase() !== 'n') {
      await installDependencies();
    }

    await createGitignore();

    console.log('\n🎉 Setup complete!');
  } catch (error) {
    console.error('\n❌ Setup failed:', error.message);
    process.exit(1);
  } finally {
    rl.close();
  }
}

if (process.argv[1] === fileURLToPath(import.meta.url)) {
  setupMain();
}

"""
Shared Context Management System

Implements shared session state and memory system accessible to all agents
in the orchestration system. Provides secure, synchronized context sharing
for seamless multi-agent collaboration.

Key Features:
- Thread-safe context sharing
- Session state management
- Memory persistence and retrieval
- Context synchronization across agents
- Security and access control
"""

import logging
import threading
import json
from typing import Dict, Any, List, Optional, Set
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import uuid
import copy


class ContextScope(Enum):
    """Context scope levels."""
    GLOBAL = "global"          # Shared across all tasks and agents
    TASK = "task"             # Shared within a specific task
    AGENT = "agent"           # Private to a specific agent
    SESSION = "session"       # Shared within a user session


class ContextPermission(Enum):
    """Context access permissions."""
    READ = "read"
    WRITE = "write"
    READ_WRITE = "read_write"
    ADMIN = "admin"


@dataclass
class ContextEntry:
    """Individual context entry with metadata."""
    key: str
    value: Any
    scope: ContextScope
    created_by: str
    created_at: datetime
    updated_at: datetime
    permissions: Dict[str, ContextPermission] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    ttl_seconds: Optional[int] = None
    
    def is_expired(self) -> bool:
        """Check if context entry has expired."""
        if self.ttl_seconds is None:
            return False
        
        expiry_time = self.created_at + timedelta(seconds=self.ttl_seconds)
        return datetime.utcnow() > expiry_time
    
    def can_access(self, agent_id: str, permission: ContextPermission) -> bool:
        """Check if agent has required permission."""
        if agent_id == self.created_by:
            return True  # Creator has full access
        
        agent_permission = self.permissions.get(agent_id, ContextPermission.READ)
        
        if permission == ContextPermission.READ:
            return agent_permission in [ContextPermission.READ, ContextPermission.READ_WRITE, ContextPermission.ADMIN]
        elif permission == ContextPermission.WRITE:
            return agent_permission in [ContextPermission.WRITE, ContextPermission.READ_WRITE, ContextPermission.ADMIN]
        elif permission == ContextPermission.READ_WRITE:
            return agent_permission in [ContextPermission.READ_WRITE, ContextPermission.ADMIN]
        elif permission == ContextPermission.ADMIN:
            return agent_permission == ContextPermission.ADMIN
        
        return False


@dataclass
class SessionState:
    """Session state for a specific task or user session."""
    session_id: str
    task_id: Optional[str]
    user_id: Optional[str]
    created_at: datetime
    last_accessed: datetime
    context_data: Dict[str, ContextEntry] = field(default_factory=dict)
    active_agents: Set[str] = field(default_factory=set)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def update_access_time(self):
        """Update last accessed timestamp."""
        self.last_accessed = datetime.utcnow()


class SharedContext:
    """
    Shared context management system for multi-agent orchestration.
    
    Provides thread-safe, secure context sharing with fine-grained
    access control and automatic cleanup capabilities.
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # Thread-safe storage
        self._lock = threading.RLock()
        self._global_context: Dict[str, ContextEntry] = {}
        self._task_contexts: Dict[str, Dict[str, ContextEntry]] = {}
        self._agent_contexts: Dict[str, Dict[str, ContextEntry]] = {}
        self._sessions: Dict[str, SessionState] = {}
        
        # Configuration
        self.default_ttl = self.config.get('default_ttl_seconds', 3600)  # 1 hour
        self.max_context_size = self.config.get('max_context_size_mb', 100)
        self.cleanup_interval = self.config.get('cleanup_interval_seconds', 300)  # 5 minutes
        self.enable_persistence = self.config.get('enable_persistence', False)
        self.persistence_path = self.config.get('persistence_path', './context_data')
        
        # Statistics
        self.stats = {
            'total_contexts': 0,
            'active_sessions': 0,
            'context_reads': 0,
            'context_writes': 0,
            'cleanup_operations': 0,
            'permission_denials': 0
        }
        
        # Start background cleanup if enabled
        if self.config.get('auto_cleanup', True):
            self._start_cleanup_thread()
    
    def create_session(self, task_id: str = None, user_id: str = None, 
                      metadata: Dict[str, Any] = None) -> str:
        """
        Create a new session for context sharing.
        
        Args:
            task_id: Optional task identifier
            user_id: Optional user identifier
            metadata: Additional session metadata
            
        Returns:
            Session ID
        """
        session_id = str(uuid.uuid4())
        
        with self._lock:
            session = SessionState(
                session_id=session_id,
                task_id=task_id,
                user_id=user_id,
                created_at=datetime.utcnow(),
                last_accessed=datetime.utcnow(),
                metadata=metadata or {}
            )
            
            self._sessions[session_id] = session
            self.stats['active_sessions'] += 1
            
            self.logger.debug(f"Created session {session_id} for task {task_id}")
            
        return session_id
    
    def get_context(self, task_id: str, agent_id: str = None) -> Dict[str, Any]:
        """
        Get context data for a task, optionally filtered by agent permissions.
        
        Args:
            task_id: Task identifier
            agent_id: Optional agent identifier for permission filtering
            
        Returns:
            Context data dictionary
        """
        with self._lock:
            context_data = {}
            
            # Add global context (if agent has permission)
            for key, entry in self._global_context.items():
                if not entry.is_expired():
                    if agent_id is None or entry.can_access(agent_id, ContextPermission.READ):
                        context_data[key] = copy.deepcopy(entry.value)
            
            # Add task-specific context
            if task_id in self._task_contexts:
                for key, entry in self._task_contexts[task_id].items():
                    if not entry.is_expired():
                        if agent_id is None or entry.can_access(agent_id, ContextPermission.READ):
                            context_data[key] = copy.deepcopy(entry.value)
            
            # Add agent-specific context (if agent_id provided)
            if agent_id and agent_id in self._agent_contexts:
                for key, entry in self._agent_contexts[agent_id].items():
                    if not entry.is_expired():
                        if entry.can_access(agent_id, ContextPermission.READ):
                            context_data[key] = copy.deepcopy(entry.value)
            
            self.stats['context_reads'] += 1
            
            return context_data
    
    def update_context(self, task_id: str, updates: Dict[str, Any], 
                      agent_id: str = None, scope: ContextScope = ContextScope.TASK,
                      permissions: Dict[str, ContextPermission] = None,
                      ttl_seconds: int = None) -> bool:
        """
        Update context with new data.
        
        Args:
            task_id: Task identifier
            updates: Dictionary of key-value pairs to update
            agent_id: Agent making the update
            scope: Context scope level
            permissions: Access permissions for other agents
            ttl_seconds: Time-to-live for context entries
            
        Returns:
            True if update successful, False otherwise
        """
        try:
            with self._lock:
                current_time = datetime.utcnow()
                ttl = ttl_seconds or self.default_ttl
                
                for key, value in updates.items():
                    # Create context entry
                    entry = ContextEntry(
                        key=key,
                        value=copy.deepcopy(value),
                        scope=scope,
                        created_by=agent_id or "system",
                        created_at=current_time,
                        updated_at=current_time,
                        permissions=permissions or {},
                        ttl_seconds=ttl
                    )
                    
                    # Store in appropriate context based on scope
                    if scope == ContextScope.GLOBAL:
                        self._global_context[key] = entry
                    elif scope == ContextScope.TASK:
                        if task_id not in self._task_contexts:
                            self._task_contexts[task_id] = {}
                        self._task_contexts[task_id][key] = entry
                    elif scope == ContextScope.AGENT and agent_id:
                        if agent_id not in self._agent_contexts:
                            self._agent_contexts[agent_id] = {}
                        self._agent_contexts[agent_id][key] = entry
                
                self.stats['context_writes'] += 1
                self.stats['total_contexts'] += len(updates)
                
                return True
                
        except Exception as e:
            self.logger.error(f"Error updating context: {str(e)}")
            return False
    
    def get_context_value(self, key: str, task_id: str = None, agent_id: str = None,
                         scope: ContextScope = None) -> Optional[Any]:
        """
        Get a specific context value.
        
        Args:
            key: Context key
            task_id: Optional task identifier
            agent_id: Optional agent identifier
            scope: Optional scope to search in
            
        Returns:
            Context value or None if not found
        """
        with self._lock:
            # Search in order: agent -> task -> global
            search_order = []
            
            if scope == ContextScope.AGENT and agent_id:
                search_order = [self._agent_contexts.get(agent_id, {})]
            elif scope == ContextScope.TASK and task_id:
                search_order = [self._task_contexts.get(task_id, {})]
            elif scope == ContextScope.GLOBAL:
                search_order = [self._global_context]
            else:
                # Search all scopes
                if agent_id:
                    search_order.append(self._agent_contexts.get(agent_id, {}))
                if task_id:
                    search_order.append(self._task_contexts.get(task_id, {}))
                search_order.append(self._global_context)
            
            for context_dict in search_order:
                if key in context_dict:
                    entry = context_dict[key]
                    if not entry.is_expired():
                        if agent_id is None or entry.can_access(agent_id, ContextPermission.READ):
                            return copy.deepcopy(entry.value)
            
            return None
    
    def set_context_value(self, key: str, value: Any, task_id: str = None, 
                         agent_id: str = None, scope: ContextScope = ContextScope.TASK,
                         permissions: Dict[str, ContextPermission] = None,
                         ttl_seconds: int = None) -> bool:
        """
        Set a specific context value.
        
        Args:
            key: Context key
            value: Context value
            task_id: Optional task identifier
            agent_id: Optional agent identifier
            scope: Context scope
            permissions: Access permissions
            ttl_seconds: Time-to-live
            
        Returns:
            True if successful, False otherwise
        """
        return self.update_context(
            task_id=task_id or "default",
            updates={key: value},
            agent_id=agent_id,
            scope=scope,
            permissions=permissions,
            ttl_seconds=ttl_seconds
        )
    
    def delete_context_value(self, key: str, task_id: str = None, agent_id: str = None,
                           scope: ContextScope = None) -> bool:
        """
        Delete a specific context value.
        
        Args:
            key: Context key to delete
            task_id: Optional task identifier
            agent_id: Optional agent identifier
            scope: Optional scope to delete from
            
        Returns:
            True if deleted, False if not found
        """
        try:
            with self._lock:
                deleted = False
                
                # Determine which contexts to search
                contexts_to_search = []
                
                if scope == ContextScope.GLOBAL:
                    contexts_to_search = [self._global_context]
                elif scope == ContextScope.TASK and task_id:
                    contexts_to_search = [self._task_contexts.get(task_id, {})]
                elif scope == ContextScope.AGENT and agent_id:
                    contexts_to_search = [self._agent_contexts.get(agent_id, {})]
                else:
                    # Search all relevant contexts
                    if agent_id and agent_id in self._agent_contexts:
                        contexts_to_search.append(self._agent_contexts[agent_id])
                    if task_id and task_id in self._task_contexts:
                        contexts_to_search.append(self._task_contexts[task_id])
                    contexts_to_search.append(self._global_context)
                
                # Delete from all matching contexts
                for context_dict in contexts_to_search:
                    if key in context_dict:
                        entry = context_dict[key]
                        # Check permissions
                        if agent_id is None or entry.can_access(agent_id, ContextPermission.WRITE):
                            del context_dict[key]
                            deleted = True
                        else:
                            self.stats['permission_denials'] += 1
                
                return deleted
                
        except Exception as e:
            self.logger.error(f"Error deleting context value: {str(e)}")
            return False
    
    def cleanup_context(self, task_id: str) -> None:
        """
        Clean up context data for a completed task.
        
        Args:
            task_id: Task identifier to clean up
        """
        with self._lock:
            # Remove task-specific context
            if task_id in self._task_contexts:
                del self._task_contexts[task_id]
            
            # Remove sessions associated with this task
            sessions_to_remove = [
                session_id for session_id, session in self._sessions.items()
                if session.task_id == task_id
            ]
            
            for session_id in sessions_to_remove:
                del self._sessions[session_id]
                self.stats['active_sessions'] -= 1
            
            self.stats['cleanup_operations'] += 1
            self.logger.debug(f"Cleaned up context for task {task_id}")
    
    def cleanup_expired_entries(self) -> int:
        """
        Clean up expired context entries.
        
        Returns:
            Number of entries cleaned up
        """
        cleaned_count = 0
        
        with self._lock:
            # Clean global context
            expired_keys = [
                key for key, entry in self._global_context.items()
                if entry.is_expired()
            ]
            for key in expired_keys:
                del self._global_context[key]
                cleaned_count += 1
            
            # Clean task contexts
            for task_id, context_dict in self._task_contexts.items():
                expired_keys = [
                    key for key, entry in context_dict.items()
                    if entry.is_expired()
                ]
                for key in expired_keys:
                    del context_dict[key]
                    cleaned_count += 1
            
            # Clean agent contexts
            for agent_id, context_dict in self._agent_contexts.items():
                expired_keys = [
                    key for key, entry in context_dict.items()
                    if entry.is_expired()
                ]
                for key in expired_keys:
                    del context_dict[key]
                    cleaned_count += 1
            
            # Clean empty task contexts
            empty_tasks = [
                task_id for task_id, context_dict in self._task_contexts.items()
                if not context_dict
            ]
            for task_id in empty_tasks:
                del self._task_contexts[task_id]
            
            # Clean empty agent contexts
            empty_agents = [
                agent_id for agent_id, context_dict in self._agent_contexts.items()
                if not context_dict
            ]
            for agent_id in empty_agents:
                del self._agent_contexts[agent_id]
        
        if cleaned_count > 0:
            self.logger.debug(f"Cleaned up {cleaned_count} expired context entries")
        
        return cleaned_count
    
    def register_agent_for_task(self, task_id: str, agent_id: str) -> None:
        """Register an agent as active for a task."""
        with self._lock:
            # Find or create session for task
            task_session = None
            for session in self._sessions.values():
                if session.task_id == task_id:
                    task_session = session
                    break
            
            if not task_session:
                session_id = self.create_session(task_id=task_id)
                task_session = self._sessions[session_id]
            
            task_session.active_agents.add(agent_id)
            task_session.update_access_time()
    
    def unregister_agent_for_task(self, task_id: str, agent_id: str) -> None:
        """Unregister an agent from a task."""
        with self._lock:
            for session in self._sessions.values():
                if session.task_id == task_id:
                    session.active_agents.discard(agent_id)
                    session.update_access_time()
                    break
    
    def get_active_agents(self, task_id: str) -> Set[str]:
        """Get list of active agents for a task."""
        with self._lock:
            for session in self._sessions.values():
                if session.task_id == task_id:
                    return session.active_agents.copy()
            return set()
    
    def get_context_statistics(self) -> Dict[str, Any]:
        """Get comprehensive context statistics."""
        with self._lock:
            total_entries = (
                len(self._global_context) +
                sum(len(context) for context in self._task_contexts.values()) +
                sum(len(context) for context in self._agent_contexts.values())
            )
            
            return {
                'statistics': self.stats.copy(),
                'storage': {
                    'total_entries': total_entries,
                    'global_entries': len(self._global_context),
                    'task_contexts': len(self._task_contexts),
                    'agent_contexts': len(self._agent_contexts),
                    'active_sessions': len(self._sessions)
                },
                'configuration': {
                    'default_ttl': self.default_ttl,
                    'max_context_size_mb': self.max_context_size,
                    'cleanup_interval': self.cleanup_interval,
                    'persistence_enabled': self.enable_persistence
                },
                'last_updated': datetime.utcnow().isoformat()
            }
    
    def _start_cleanup_thread(self) -> None:
        """Start background cleanup thread."""
        def cleanup_worker():
            import time
            while True:
                try:
                    time.sleep(self.cleanup_interval)
                    self.cleanup_expired_entries()
                except Exception as e:
                    self.logger.error(f"Error in cleanup thread: {str(e)}")
        
        import threading
        cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
        cleanup_thread.start()
        self.logger.info("Started background cleanup thread")

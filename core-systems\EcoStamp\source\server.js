import express from "express";
import cors from "cors";
import rateLimit from "express-rate-limit";
import dotenv from "dotenv";
import routes from "./routes.js";
import { dataScheduler } from "./schedulers/dataUpdateScheduler.js";
import { benchmarkUpdater } from "./schedulers/benchmarkUpdater.js";
import { initializeHashRegistry } from "./models/hashRegistry.js";

dotenv.config();

const app = express();
const port = process.env.PORT || 3000;

app.use(cors());
app.use(express.json());
app.use(express.static('public'));

const limiter = rateLimit({
  windowMs: 15 * 60 * 1000,
  max: 100,
  standardHeaders: true,
  legacyHeaders: false,
});
app.use(limiter);

app.use(routes);

app.get('/', (req, res) => {
  res.send('Stamply backend running! Visit <a href="/dashboard.html">Dashboard</a> for analytics.');
});

app.get('/dashboard', (req, res) => {
  res.redirect('/dashboard.html');
});

app.listen(port, async () => {
  console.log(`Server running on port ${port}`);

  // Initialize hash registry
  await initializeHashRegistry();

  // Start the data update scheduler
  if (process.env.ENABLE_SCHEDULER !== 'false') {
    console.log('Starting data update scheduler...');
    dataScheduler.start({
      dailyUpdate: true,
      weeklyDeepScrape: true,
      dailyTime: '02:00',
      weeklyDay: 0,
      weeklyTime: '03:00'
    });

    // Start benchmark updater
    console.log('Starting benchmark updater...');
    benchmarkUpdater.start();
  }
});

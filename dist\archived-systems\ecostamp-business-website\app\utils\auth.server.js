"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createSupabaseServerClient = createSupabaseServerClient;
exports.getUser = getUser;
exports.requireUser = requireUser;
exports.requireAnonymous = requireAnonymous;
exports.signOut = signOut;
exports.signIn = signIn;
exports.signUp = signUp;
exports.resetPassword = resetPassword;
exports.updatePassword = updatePassword;
const ssr_1 = require("@supabase/ssr");
const node_1 = require("@remix-run/node");
function createSupabaseServerClient(request) {
    const cookies = (0, ssr_1.parse)(request.headers.get("Cookie") ?? "");
    const headers = new Headers();
    const supabase = (0, ssr_1.createServerClient)(process.env.SUPABASE_URL, process.env.SUPABASE_ANON_KEY, {
        cookies: {
            get(key) {
                return cookies[key];
            },
            set(key, value, options) {
                headers.append("Set-Cookie", (0, ssr_1.serialize)(key, value, options));
            },
            remove(key, options) {
                headers.append("Set-Cookie", (0, ssr_1.serialize)(key, "", options));
            },
        },
    });
    return { supabase, headers };
}
async function getUser(request) {
    const { supabase } = createSupabaseServerClient(request);
    try {
        const { data: { user }, error, } = await supabase.auth.getUser();
        if (error || !user) {
            return null;
        }
        // Get user profile data
        const { data: profile } = await supabase
            .from("profiles")
            .select("*")
            .eq("id", user.id)
            .single();
        return {
            id: user.id,
            email: user.email,
            name: profile?.name || user.user_metadata?.name || null,
            avatar: profile?.avatar_url || user.user_metadata?.avatar_url || null,
            plan: profile?.plan || "free",
            created_at: user.created_at,
            ...profile,
        };
    }
    catch (error) {
        console.error("Error getting user:", error);
        return null;
    }
}
async function requireUser(request) {
    const user = await getUser(request);
    if (!user) {
        throw (0, node_1.redirect)("/login");
    }
    return user;
}
async function requireAnonymous(request) {
    const user = await getUser(request);
    if (user) {
        throw (0, node_1.redirect)("/dashboard");
    }
}
async function signOut(request) {
    const { supabase, headers } = createSupabaseServerClient(request);
    await supabase.auth.signOut();
    return (0, node_1.redirect)("/", {
        headers,
    });
}
async function signIn(request, email, password) {
    const { supabase, headers } = createSupabaseServerClient(request);
    const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
    });
    if (error) {
        return { error: error.message, data: null, headers };
    }
    return { error: null, data, headers };
}
async function signUp(request, email, password, name) {
    const { supabase, headers } = createSupabaseServerClient(request);
    const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
            data: {
                name: name || "",
            },
        },
    });
    if (error) {
        return { error: error.message, data: null, headers };
    }
    // Create user profile
    if (data.user) {
        await supabase.from("profiles").insert({
            id: data.user.id,
            email: data.user.email,
            name: name || "",
            plan: "free",
            created_at: new Date().toISOString(),
        });
    }
    return { error: null, data, headers };
}
async function resetPassword(request, email) {
    const { supabase } = createSupabaseServerClient(request);
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${new URL(request.url).origin}/reset-password`,
    });
    if (error) {
        return { error: error.message };
    }
    return { error: null };
}
async function updatePassword(request, password) {
    const { supabase, headers } = createSupabaseServerClient(request);
    const { error } = await supabase.auth.updateUser({
        password,
    });
    if (error) {
        return { error: error.message, headers };
    }
    return { error: null, headers };
}

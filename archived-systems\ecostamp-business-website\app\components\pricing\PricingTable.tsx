import { Link } from "@remix-run/react";
import { motion } from "framer-motion";
import { Check, Star, Zap, Users, Crown } from "lucide-react";
import { Button } from "~/components/ui/Button";
import { cn } from "~/utils/cn";

const tiers = [
  {
    name: "Free",
    id: "free",
    href: "/signup?plan=free",
    price: { monthly: 0, annually: 0 },
    description: "Perfect for getting started with digital trust",
    icon: Star,
    features: [
      "Upload & search with tags",
      "30-day log retention",
      "Basic verification badges",
      "Community support",
      "5 verifications per month",
    ],
    addOns: [
      "Analytics Dashboard ($9/month)",
      "Export & Download ($5/month)",
      "API Access ($15/month)",
      "Priority Support ($12/month)",
    ],
    cta: "Start Free",
    popular: false,
  },
  {
    name: "Pro",
    id: "pro",
    href: "/signup?plan=pro",
    price: { monthly: 29, annually: 290 },
    description: "All add-ons included for serious creators",
    icon: Zap,
    features: [
      "Everything in Free",
      "90-day log retention",
      "Analytics dashboard included",
      "Export & download included",
      "API access included",
      "Unlimited verifications",
      "Advanced verification badges",
      "Email support",
    ],
    addOns: [],
    cta: "Start Pro Trial",
    popular: true,
  },
  {
    name: "Pro Plus",
    id: "pro-plus",
    href: "/signup?plan=pro-plus",
    price: { monthly: 49, annually: 490 },
    description: "Pro features with daily support",
    icon: Crown,
    features: [
      "Everything in Pro",
      "Daily online support",
      "Priority feature requests",
      "Custom verification badges",
      "Advanced analytics",
      "White-label options",
    ],
    addOns: [],
    cta: "Start Pro Plus",
    popular: false,
  },
  {
    name: "Team",
    id: "team",
    href: "/signup?plan=team",
    price: { monthly: 99, annually: 990 },
    description: "Collaboration for small teams (5 users)",
    icon: Users,
    features: [
      "Everything in Pro",
      "Team management (5 users)",
      "Shared workspaces",
      "Role-based permissions",
      "Team analytics",
      "Bulk operations",
      "Team support",
    ],
    addOns: [],
    cta: "Start Team Trial",
    popular: false,
  },
  {
    name: "Team Plus",
    id: "team-plus",
    href: "/signup?plan=team-plus",
    price: { monthly: 199, annually: 1990 },
    description: "Everything for unlimited teams",
    icon: Crown,
    features: [
      "Everything in Team",
      "Unlimited team members",
      "Daily support",
      "Custom integrations",
      "Advanced security",
      "Dedicated account manager",
      "SLA guarantee",
    ],
    addOns: [],
    cta: "Contact Sales",
    popular: false,
  },
];

const fadeInUp = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.6 }
};

export function PricingTable() {
  return (
    <div className="mx-auto max-w-7xl">
      {/* Billing toggle */}
      <motion.div 
        className="flex justify-center mb-8"
        initial="initial"
        animate="animate"
        variants={fadeInUp}
      >
        <div className="relative flex items-center bg-secondary-100 rounded-lg p-1">
          <button className="relative z-10 px-4 py-2 text-sm font-medium text-secondary-900 bg-white rounded-md shadow-sm">
            Monthly
          </button>
          <button className="relative z-10 px-4 py-2 text-sm font-medium text-secondary-600">
            Annual
            <span className="ml-2 inline-flex items-center rounded-full bg-primary-100 px-2 py-0.5 text-xs font-medium text-primary-800">
              Save 20%
            </span>
          </button>
        </div>
      </motion.div>

      {/* Pricing cards */}
      <div className="grid grid-cols-1 gap-8 lg:grid-cols-5">
        {tiers.map((tier, index) => (
          <motion.div
            key={tier.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: index * 0.1 }}
            className={cn(
              "relative rounded-2xl border bg-white p-8 shadow-soft",
              tier.popular
                ? "border-primary-500 ring-2 ring-primary-500 ring-opacity-50"
                : "border-secondary-200"
            )}
          >
            {tier.popular && (
              <div className="absolute -top-4 left-1/2 -translate-x-1/2">
                <span className="inline-flex items-center rounded-full bg-primary-500 px-4 py-1 text-sm font-medium text-white">
                  Most Popular
                </span>
              </div>
            )}

            <div className="flex items-center">
              <tier.icon className={cn(
                "h-6 w-6",
                tier.popular ? "text-primary-600" : "text-secondary-600"
              )} />
              <h3 className="ml-3 text-lg font-semibold text-secondary-900">
                {tier.name}
              </h3>
            </div>

            <p className="mt-4 text-sm text-secondary-600">
              {tier.description}
            </p>

            <div className="mt-6">
              <div className="flex items-baseline">
                <span className="text-4xl font-bold tracking-tight text-secondary-900">
                  ${tier.price.monthly}
                </span>
                {tier.price.monthly > 0 && (
                  <span className="ml-1 text-sm font-semibold text-secondary-600">
                    /month
                  </span>
                )}
              </div>
              {tier.price.monthly > 0 && (
                <p className="mt-1 text-sm text-secondary-600">
                  ${tier.price.annually}/year (billed annually)
                </p>
              )}
            </div>

            <Button
              asChild
              className={cn(
                "mt-8 w-full",
                tier.popular
                  ? "bg-primary-600 hover:bg-primary-700"
                  : "bg-secondary-900 hover:bg-secondary-800"
              )}
            >
              <Link to={tier.href}>
                {tier.cta}
              </Link>
            </Button>

            <ul className="mt-8 space-y-3">
              {tier.features.map((feature) => (
                <li key={feature} className="flex items-start">
                  <Check className="h-5 w-5 flex-shrink-0 text-primary-500 mt-0.5" />
                  <span className="ml-3 text-sm text-secondary-700">
                    {feature}
                  </span>
                </li>
              ))}
            </ul>

            {tier.addOns.length > 0 && (
              <div className="mt-8 pt-8 border-t border-secondary-200">
                <h4 className="text-sm font-semibold text-secondary-900 mb-4">
                  Available Add-ons:
                </h4>
                <ul className="space-y-2">
                  {tier.addOns.map((addOn) => (
                    <li key={addOn} className="text-sm text-secondary-600">
                      • {addOn}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </motion.div>
        ))}
      </div>

      {/* Enterprise CTA */}
      <motion.div 
        className="mt-16 rounded-2xl bg-gradient-to-r from-secondary-900 to-secondary-800 p-8 text-center"
        initial="initial"
        animate="animate"
        variants={fadeInUp}
      >
        <h3 className="text-2xl font-bold text-white">
          Need something custom?
        </h3>
        <p className="mt-4 text-lg text-secondary-300">
          We offer custom enterprise solutions with dedicated support, 
          custom integrations, and white-label options.
        </p>
        <div className="mt-8 flex flex-col items-center justify-center gap-4 sm:flex-row">
          <Button asChild variant="secondary" size="lg">
            <Link to="/contact">
              Contact Sales
            </Link>
          </Button>
          <Button asChild variant="outline" size="lg" className="border-white text-white hover:bg-white hover:text-secondary-900">
            <Link to="/enterprise">
              Learn More
            </Link>
          </Button>
        </div>
      </motion.div>
    </div>
  );
}

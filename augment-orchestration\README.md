# 🚀 Augment Code: Unified AI Orchestration Platform

A sophisticated multi-agent AI orchestration platform with family tree visualization, evolutionary optimization, and cross-domain tunnel management.

## 🌟 Features

- **Meta-Orchestrator Hierarchy**: Manage multiple AI orchestration layers
- **Family Tree Visualization**: Interactive React Flow-based agent relationship mapping
- **Multi-Agent Coordination**: Role-based agent assignment and workflow execution
- **Cross-Domain Tunnels**: Persistent bidirectional communication channels
- **Darwin Gödel Machine**: Evolutionary optimization engine for agent performance
- **Model Context Protocol**: Shared context middleware for seamless AI integration
- **Real-Time Updates**: WebSocket-based live system monitoring
- **Audit & Compliance**: Comprehensive activity logging and security tracking

## 🏗️ Architecture

### Backend (Node.js + TypeScript)
- **Express.js** server with Socket.IO for real-time communication
- **Prisma ORM** with PostgreSQL database
- **JWT Authentication** with role-based access control
- **Event-driven architecture** with internal pub-sub system
- **RESTful APIs** for all system entities

### Frontend (React + TypeScript)
- **React 18** with Material-UI components
- **Redux Toolkit** for state management
- **React Flow** for family tree visualization
- **Socket.IO Client** for real-time updates
- **Responsive design** with dark theme

### Database Schema
- Meta-Orchestrators and Sub-Orchestrators
- Agents with capabilities and role assignments
- Tunnels for cross-domain communication
- Workflow templates and executions
- Evolution variants and mutations
- Audit logs and shared context

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ and npm
- PostgreSQL 12+
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd augment-orchestration
   ```

2. **Install dependencies**
   ```bash
   # Install server dependencies
   npm install
   
   # Install client dependencies
   cd client
   npm install
   cd ..
   ```

3. **Database setup**
   ```bash
   # Copy environment variables
   cp .env.example .env
   
   # Edit .env with your database credentials
   # DATABASE_URL="postgresql://username:password@localhost:5432/augment_orchestration"
   
   # Generate Prisma client and run migrations
   npx prisma generate
   npx prisma db push
   
   # Optional: Seed database with sample data
   npx prisma db seed
   ```

4. **Start development servers**
   ```bash
   # Terminal 1: Start backend server
   npm run dev
   
   # Terminal 2: Start frontend development server
   cd client
   npm run dev
   ```

5. **Access the application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:3001
   - API Documentation: http://localhost:3001/api-docs

## 📁 Project Structure

```
augment-orchestration/
├── src/
│   ├── server/           # Backend server code
│   │   ├── routes/       # API route handlers
│   │   ├── services/     # Business logic services
│   │   ├── middleware/   # Express middleware
│   │   └── utils/        # Utility functions
│   └── shared/           # Shared TypeScript types
├── client/               # React frontend
│   ├── src/
│   │   ├── components/   # Reusable React components
│   │   ├── pages/        # Page components
│   │   ├── store/        # Redux store and slices
│   │   └── services/     # API service functions
├── prisma/               # Database schema and migrations
├── logs/                 # Application logs
└── docs/                 # Documentation
```

## 🔧 Development

### Available Scripts

**Backend:**
- `npm run dev` - Start development server with hot reload
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run test` - Run tests
- `npm run db:migrate` - Run database migrations
- `npm run db:seed` - Seed database with sample data

**Frontend:**
- `npm run dev` - Start Vite development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run test` - Run tests

### Environment Variables

Copy `.env.example` to `.env` and configure:

- `DATABASE_URL` - PostgreSQL connection string
- `JWT_SECRET` - Secret key for JWT tokens
- `PORT` - Server port (default: 3001)
- `CORS_ORIGINS` - Allowed CORS origins

## 🧪 Testing

```bash
# Run backend tests
npm run test

# Run frontend tests
cd client
npm run test

# Run end-to-end tests
npm run test:e2e
```

## 📊 API Documentation

The API follows RESTful conventions with the following main endpoints:

- `GET /api/orchestrators` - List meta-orchestrators
- `GET /api/agents` - List AI agents
- `GET /api/workflows` - List workflow templates
- `GET /api/tunnels` - List cross-domain tunnels
- `GET /api/evolution` - List evolution variants
- `GET /api/audit` - List audit logs

WebSocket events for real-time updates:
- `orchestrator:created/updated/deleted`
- `agent:assigned/status_changed/evolved`
- `workflow:started/progress/completed`
- `tunnel:created/activated/data_flow`

## 🔒 Security

- JWT-based authentication with role-based access control
- Rate limiting on API endpoints
- Input validation with Zod schemas
- SQL injection prevention with Prisma ORM
- CORS configuration for cross-origin requests
- Comprehensive audit logging

## 🚀 Deployment

### Production Build

```bash
# Build both frontend and backend
npm run build
cd client && npm run build && cd ..

# Start production server
npm start
```

### Docker Deployment

```bash
# Build Docker image
docker build -t augment-orchestration .

# Run with Docker Compose
docker-compose up -d
```

### Environment Setup

For production deployment:
1. Set `NODE_ENV=production`
2. Configure secure `JWT_SECRET`
3. Set up PostgreSQL database
4. Configure reverse proxy (nginx)
5. Set up SSL certificates
6. Configure monitoring and logging

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Built with [React](https://reactjs.org/) and [Node.js](https://nodejs.org/)
- UI components by [Material-UI](https://mui.com/)
- Graph visualization by [React Flow](https://reactflow.dev/)
- Database ORM by [Prisma](https://www.prisma.io/)
- Real-time communication by [Socket.IO](https://socket.io/)

---

**Augment Code** - Empowering developers with intelligent AI orchestration 🚀

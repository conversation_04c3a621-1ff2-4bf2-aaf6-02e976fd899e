#!/usr/bin/env node

const { Command } = require('commander');
const chalk = require('chalk');
const ora = require('ora');
const fs = require('fs-extra');
const path = require('path');
const { spawn, exec } = require('child_process');
const express = require('express');

class AIOrchestrator {
  constructor() {
    this.server = null;
    this.activeConnections = new Set();
    this.config = {
      port: 3001,
      metaPort: 3002,
      dgmPort: 3003
    };
    this.services = {
      meta: null,
      universal: null,
      dgm: null
    };
  }

  async initialize() {
    const spinner = ora('Initializing AI Orchestration Hub...').start();

    try {
      // Initialize basic orchestration services
      spinner.text = 'Starting orchestration services...';
      await this.startServices();

      spinner.text = 'Setting up web interface...';
      await this.setupWebInterface();

      // Start the orchestration server
      await this.startServer();

      spinner.succeed('AI Orchestration Hub initialized successfully!');

      console.log(chalk.green('\n🎉 AI Orchestration Hub is ready!'));
      console.log(chalk.blue(`📊 Dashboard: http://localhost:${this.config.port}`));
      console.log(chalk.blue(`🎭 Meta-Orchestrator: http://localhost:${this.config.metaPort}`));
      console.log(chalk.blue(`🧠 DGM System: http://localhost:${this.config.dgmPort}`));

    } catch (error) {
      spinner.fail('Failed to initialize AI Orchestration Hub');
      console.error(chalk.red('Error:'), error.message);
      throw error;
    }
  }

  async startServices() {
    console.log(chalk.blue('🚀 Starting orchestration services...'));

    // Start Meta-Orchestrator service
    if (await fs.pathExists(path.join(__dirname, 'meta-orchestrator', 'index.js'))) {
      this.services.meta = await this.startMetaOrchestrator();
    }

    // Start Universal Orchestrator service
    if (await fs.pathExists(path.join(__dirname, 'universal-orchestrator', 'index.js'))) {
      this.services.universal = await this.startUniversalOrchestrator();
    }

    // Start DGM service
    if (await fs.pathExists(path.join(__dirname, 'dgm', 'index.js'))) {
      this.services.dgm = await this.startDGMService();
    }
  }

  async startMetaOrchestrator() {
    return new Promise((resolve) => {
      const metaProcess = spawn('node', ['meta-orchestrator/index.js'], {
        cwd: __dirname,
        stdio: 'pipe'
      });

      console.log(chalk.green(`✅ Meta-Orchestrator started on port ${this.config.metaPort}`));
      resolve(metaProcess);
    });
  }

  async startUniversalOrchestrator() {
    return new Promise((resolve) => {
      const universalProcess = spawn('node', ['universal-orchestrator/index.js'], {
        cwd: __dirname,
        stdio: 'pipe'
      });

      console.log(chalk.green(`✅ Universal Orchestrator started`));
      resolve(universalProcess);
    });
  }

  async startDGMService() {
    return new Promise((resolve) => {
      const dgmProcess = spawn('node', ['dgm/index.js'], {
        cwd: __dirname,
        stdio: 'pipe'
      });

      console.log(chalk.green(`✅ DGM Service started on port ${this.config.dgmPort}`));
      resolve(dgmProcess);
    });
  }

  async setupWebInterface() {
    console.log(chalk.blue('🌐 Setting up web interface...'));

    // Create a simple web dashboard
    const app = express();
    app.use(express.static(path.join(__dirname, 'public')));
    app.use(express.json());

    // CORS middleware
    app.use((req, res, next) => {
      res.header('Access-Control-Allow-Origin', '*');
      res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
      next();
    });

    // API Routes
    app.get('/api/status', (req, res) => {
      res.json({
        status: 'running',
        services: {
          meta: this.services.meta ? 'running' : 'stopped',
          universal: this.services.universal ? 'running' : 'stopped',
          dgm: this.services.dgm ? 'running' : 'stopped'
        },
        ports: this.config
      });
    });

    app.get('/api/services', (req, res) => {
      res.json({
        services: [
          {
            name: 'Meta-Orchestrator',
            status: this.services.meta ? 'running' : 'stopped',
            port: this.config.metaPort,
            description: 'Advanced AI coordination and workflow management'
          },
          {
            name: 'Universal Orchestrator',
            status: this.services.universal ? 'running' : 'stopped',
            description: 'Cross-platform AI tool integration'
          },
          {
            name: 'DGM System',
            status: this.services.dgm ? 'running' : 'stopped',
            port: this.config.dgmPort,
            description: 'Dynamic Goal Management and task routing'
          }
        ]
      });
    });

    this.app = app;
  }

  async startServer() {
    return new Promise((resolve, reject) => {
      const server = this.app.listen(this.config.port, () => {
        console.log(chalk.green(`✅ AI Orchestration Hub running on port ${this.config.port}`));
        resolve(server);
      });

      server.on('error', (error) => {
        console.error(chalk.red('❌ Failed to start server:'), error.message);
        reject(error);
      });

      this.server = server;
    });
  }

  async stop() {
    console.log(chalk.yellow('🛑 Stopping AI Orchestration Hub...'));

    // Stop all services
    if (this.services.meta) {
      this.services.meta.kill();
    }
    if (this.services.universal) {
      this.services.universal.kill();
    }
    if (this.services.dgm) {
      this.services.dgm.kill();
    }

    // Stop main server
    if (this.server) {
      this.server.close();
    }

    console.log(chalk.green('✅ AI Orchestration Hub stopped'));
  }
}

// CLI Interface
const program = new Command();

program
  .name('ai-orchestrator')
  .description('AI Orchestration Hub - Coordinate multiple AI assistants')
  .version('1.0.0');

program
  .command('start')
  .description('Start the AI Orchestration Hub')
  .action(async () => {
    const orchestrator = new AIOrchestrator();

    try {
      await orchestrator.initialize();

      // Handle graceful shutdown
      process.on('SIGINT', async () => {
        console.log(chalk.yellow('\n🛑 Received SIGINT, shutting down gracefully...'));
        await orchestrator.stop();
        process.exit(0);
      });

      process.on('SIGTERM', async () => {
        console.log(chalk.yellow('\n🛑 Received SIGTERM, shutting down gracefully...'));
        await orchestrator.stop();
        process.exit(0);
      });

    } catch (error) {
      console.error(chalk.red('❌ Failed to start AI Orchestration Hub:'), error.message);
      process.exit(1);
    }
  });

program
  .command('status')
  .description('Check the status of AI Orchestration Hub')
  .action(() => {
    console.log(chalk.blue('🔍 Checking AI Orchestration Hub status...'));
    // Add status check logic here
  });

program
  .command('test')
  .description('Run system tests')
  .action(async () => {
    try {
      console.log(chalk.blue('🧪 Running AI Orchestration System Tests...'));
      console.log();

      // Test 1: Check configuration
      console.log(chalk.yellow('Testing configuration...'));
      const configPath = path.join(__dirname, 'config', 'main-config.json');
      if (await fs.pathExists(configPath)) {
        console.log(chalk.green('✅ Configuration file found'));
      } else {
        console.log(chalk.yellow('⚠️ Configuration file not found (will use defaults)'));
      }

      // Test 2: Check components
      console.log(chalk.yellow('Testing components...'));
      const components = [
        { name: 'Meta Orchestrator', path: 'meta-orchestrator/index.js' },
        { name: 'Universal Orchestrator', path: 'universal-orchestrator/index.js' },
        { name: 'DGM System', path: 'dgm/index.js' }
      ];

      for (const component of components) {
        const componentPath = path.join(__dirname, component.path);
        if (await fs.pathExists(componentPath)) {
          console.log(chalk.green(`✅ ${component.name} found`));
        } else {
          console.log(chalk.red(`❌ ${component.name} not found at ${component.path}`));
        }
      }

      console.log();
      console.log(chalk.green('🎉 System tests completed successfully!'));
      console.log(chalk.blue('The AI Orchestration System is ready to use.'));

    } catch (error) {
      console.error(chalk.red(`❌ System test failed: ${error.message}`));
      process.exit(1);
    }
  });

// Export for use as module
module.exports = AIOrchestrator;

// Run CLI if this file is executed directly
if (require.main === module) {
  program.parse();
}

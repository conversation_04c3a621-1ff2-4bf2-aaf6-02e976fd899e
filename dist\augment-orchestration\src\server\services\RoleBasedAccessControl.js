"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RoleBasedAccessControl = void 0;
const client_1 = require("@prisma/client");
const EventBus_1 = require("./EventBus");
const logger_1 = require("../utils/logger");
class RoleBasedAccessControl {
    constructor() {
        this.roleCache = new Map();
        this.permissionCache = new Map();
        // System roles with predefined permissions
        this.SYSTEM_ROLES = [
            {
                name: 'Super Admin',
                description: 'Full system access with all permissions',
                hierarchy: 100,
                isSystemRole: true,
                permissions: [
                    { id: 'super-admin-all', name: 'All Permissions', resource: '*', action: '*' },
                ],
            },
            {
                name: 'Admin',
                description: 'Administrative access to most system features',
                hierarchy: 80,
                isSystemRole: true,
                permissions: [
                    { id: 'admin-users', name: 'Manage Users', resource: 'users', action: '*' },
                    { id: 'admin-roles', name: 'Manage Roles', resource: 'roles', action: 'read,update' },
                    { id: 'admin-agents', name: 'Manage Agents', resource: 'agents', action: '*' },
                    { id: 'admin-workflows', name: 'Manage Workflows', resource: 'workflows', action: '*' },
                    { id: 'admin-tunnels', name: 'Manage Tunnels', resource: 'tunnels', action: '*' },
                    { id: 'admin-context', name: 'Manage Context', resource: 'context', action: '*' },
                    { id: 'admin-evolution', name: 'Manage Evolution', resource: 'evolution', action: '*' },
                    { id: 'admin-audit', name: 'View Audit Logs', resource: 'audit', action: 'read' },
                ],
            },
            {
                name: 'Orchestrator',
                description: 'Can create and manage orchestration workflows',
                hierarchy: 60,
                isSystemRole: true,
                permissions: [
                    { id: 'orch-agents', name: 'Manage Agents', resource: 'agents', action: 'read,create,update' },
                    { id: 'orch-workflows', name: 'Manage Workflows', resource: 'workflows', action: '*' },
                    { id: 'orch-tunnels', name: 'Manage Tunnels', resource: 'tunnels', action: '*' },
                    { id: 'orch-context', name: 'Manage Context', resource: 'context', action: 'read,create,update' },
                    { id: 'orch-evolution', name: 'View Evolution', resource: 'evolution', action: 'read' },
                ],
            },
            {
                name: 'Developer',
                description: 'Can work with agents and basic workflows',
                hierarchy: 40,
                isSystemRole: true,
                permissions: [
                    { id: 'dev-agents', name: 'Work with Agents', resource: 'agents', action: 'read,update' },
                    { id: 'dev-workflows', name: 'Execute Workflows', resource: 'workflows', action: 'read,execute' },
                    { id: 'dev-tunnels', name: 'Use Tunnels', resource: 'tunnels', action: 'read,create' },
                    { id: 'dev-context', name: 'Read Context', resource: 'context', action: 'read' },
                ],
            },
            {
                name: 'Viewer',
                description: 'Read-only access to system information',
                hierarchy: 20,
                isSystemRole: true,
                permissions: [
                    { id: 'view-agents', name: 'View Agents', resource: 'agents', action: 'read' },
                    { id: 'view-workflows', name: 'View Workflows', resource: 'workflows', action: 'read' },
                    { id: 'view-tunnels', name: 'View Tunnels', resource: 'tunnels', action: 'read' },
                    { id: 'view-context', name: 'View Context', resource: 'context', action: 'read' },
                ],
            },
        ];
        this.prisma = new client_1.PrismaClient();
        this.eventBus = new EventBus_1.EventBus();
        this.initializeRBAC();
    }
    /**
     * Initialize RBAC system
     */
    async initializeRBAC() {
        try {
            await this.ensureSystemRoles();
            await this.loadRoleCache();
            logger_1.logger.info('RBAC system initialized successfully');
        }
        catch (error) {
            logger_1.logger.error('Failed to initialize RBAC system', { error });
        }
    }
    /**
     * Ensure system roles exist in database
     */
    async ensureSystemRoles() {
        for (const roleData of this.SYSTEM_ROLES) {
            const existingRole = await this.prisma.role.findFirst({
                where: { name: roleData.name },
            });
            if (!existingRole) {
                await this.prisma.role.create({
                    data: {
                        name: roleData.name,
                        description: roleData.description,
                        permissions: roleData.permissions,
                        isSystemRole: roleData.isSystemRole,
                        hierarchy: roleData.hierarchy,
                    },
                });
                logger_1.logger.info(`Created system role: ${roleData.name}`);
            }
        }
    }
    /**
     * Load roles into cache
     */
    async loadRoleCache() {
        const roles = await this.prisma.role.findMany();
        for (const role of roles) {
            const roleObj = {
                id: role.id,
                name: role.name,
                description: role.description,
                permissions: role.permissions,
                isSystemRole: role.isSystemRole,
                hierarchy: role.hierarchy,
            };
            this.roleCache.set(role.id, roleObj);
        }
        logger_1.logger.debug(`Loaded ${roles.length} roles into cache`);
    }
    /**
     * Check if user has permission for a specific action
     */
    async hasPermission(context) {
        try {
            const user = await this.prisma.user.findUnique({
                where: { id: context.userId },
                include: { roles: true },
            });
            if (!user) {
                await this.logAccess(context, 'DENIED', 'User not found');
                return false;
            }
            // Super admin always has access
            const isSuperAdmin = user.roles.some(role => this.roleCache.get(role.id)?.name === 'Super Admin');
            if (isSuperAdmin) {
                await this.logAccess(context, 'GRANTED', 'Super admin access');
                return true;
            }
            // Check user's role permissions
            for (const userRole of user.roles) {
                const role = this.roleCache.get(userRole.id);
                if (!role)
                    continue;
                for (const permission of role.permissions) {
                    if (this.matchesPermission(permission, context)) {
                        await this.logAccess(context, 'GRANTED', `Permission: ${permission.name}`);
                        return true;
                    }
                }
            }
            await this.logAccess(context, 'DENIED', 'No matching permissions');
            return false;
        }
        catch (error) {
            logger_1.logger.error('Permission check failed', { error, context });
            await this.logAccess(context, 'DENIED', 'System error');
            return false;
        }
    }
    /**
     * Check if permission matches the access context
     */
    matchesPermission(permission, context) {
        // Check resource match
        if (permission.resource !== '*' && permission.resource !== context.resource) {
            return false;
        }
        // Check action match
        if (permission.action !== '*') {
            const allowedActions = permission.action.split(',').map(a => a.trim());
            if (!allowedActions.includes(context.action)) {
                return false;
            }
        }
        // Check conditions if any
        if (permission.conditions) {
            return this.evaluateConditions(permission.conditions, context);
        }
        return true;
    }
    /**
     * Evaluate permission conditions
     */
    evaluateConditions(conditions, context) {
        // Simple condition evaluation - can be extended for complex rules
        for (const [key, value] of Object.entries(conditions)) {
            const contextValue = context.metadata?.[key];
            if (Array.isArray(value)) {
                if (!value.includes(contextValue))
                    return false;
            }
            else if (value !== contextValue) {
                return false;
            }
        }
        return true;
    }
    /**
     * Assign role to user
     */
    async assignRole(userId, roleId, assignedBy) {
        try {
            // Check if user already has this role
            const existingAssignment = await this.prisma.userRole.findFirst({
                where: { userId, roleId },
            });
            if (existingAssignment) {
                throw new Error('User already has this role');
            }
            // Check if assigner has permission
            const hasPermission = await this.hasPermission({
                userId: assignedBy,
                resource: 'roles',
                action: 'assign',
                resourceId: roleId,
            });
            if (!hasPermission) {
                throw new Error('Insufficient permissions to assign role');
            }
            await this.prisma.userRole.create({
                data: {
                    userId,
                    roleId,
                    assignedBy,
                    assignedAt: new Date(),
                },
            });
            // Clear user permission cache
            this.permissionCache.delete(userId);
            this.eventBus.emit(EventBus_1.EVENT_TYPES.ROLE_ASSIGNED, {
                userId,
                roleId,
                assignedBy,
            });
            logger_1.logger.info('Role assigned successfully', { userId, roleId, assignedBy });
        }
        catch (error) {
            logger_1.logger.error('Failed to assign role', { error, userId, roleId });
            throw error;
        }
    }
    /**
     * Remove role from user
     */
    async removeRole(userId, roleId, removedBy) {
        try {
            // Check if remover has permission
            const hasPermission = await this.hasPermission({
                userId: removedBy,
                resource: 'roles',
                action: 'remove',
                resourceId: roleId,
            });
            if (!hasPermission) {
                throw new Error('Insufficient permissions to remove role');
            }
            await this.prisma.userRole.deleteMany({
                where: { userId, roleId },
            });
            // Clear user permission cache
            this.permissionCache.delete(userId);
            this.eventBus.emit(EventBus_1.EVENT_TYPES.ROLE_REMOVED, {
                userId,
                roleId,
                removedBy,
            });
            logger_1.logger.info('Role removed successfully', { userId, roleId, removedBy });
        }
        catch (error) {
            logger_1.logger.error('Failed to remove role', { error, userId, roleId });
            throw error;
        }
    }
    /**
     * Create custom role
     */
    async createRole(name, description, permissions, hierarchy, createdBy) {
        try {
            // Check if creator has permission
            const hasPermission = await this.hasPermission({
                userId: createdBy,
                resource: 'roles',
                action: 'create',
            });
            if (!hasPermission) {
                throw new Error('Insufficient permissions to create role');
            }
            const role = await this.prisma.role.create({
                data: {
                    name,
                    description,
                    permissions,
                    hierarchy,
                    isSystemRole: false,
                    createdBy,
                },
            });
            // Add to cache
            this.roleCache.set(role.id, {
                id: role.id,
                name: role.name,
                description: role.description,
                permissions: role.permissions,
                isSystemRole: role.isSystemRole,
                hierarchy: role.hierarchy,
            });
            this.eventBus.emit(EventBus_1.EVENT_TYPES.ROLE_CREATED, {
                roleId: role.id,
                name,
                createdBy,
            });
            logger_1.logger.info('Custom role created successfully', { roleId: role.id, name });
            return role.id;
        }
        catch (error) {
            logger_1.logger.error('Failed to create role', { error, name });
            throw error;
        }
    }
    /**
     * Get user's effective permissions
     */
    async getUserPermissions(userId) {
        try {
            // Check cache first
            const cached = this.permissionCache.get(userId);
            if (cached)
                return cached;
            const user = await this.prisma.user.findUnique({
                where: { id: userId },
                include: { roles: true },
            });
            if (!user)
                return [];
            const permissions = [];
            const permissionIds = new Set();
            for (const userRole of user.roles) {
                const role = this.roleCache.get(userRole.id);
                if (!role)
                    continue;
                for (const permission of role.permissions) {
                    if (!permissionIds.has(permission.id)) {
                        permissions.push(permission);
                        permissionIds.add(permission.id);
                    }
                }
            }
            // Cache the result
            this.permissionCache.set(userId, permissions);
            return permissions;
        }
        catch (error) {
            logger_1.logger.error('Failed to get user permissions', { error, userId });
            return [];
        }
    }
    /**
     * Log access attempt
     */
    async logAccess(context, result, reason) {
        try {
            await this.prisma.auditLog.create({
                data: {
                    userId: context.userId,
                    action: context.action,
                    resource: context.resource,
                    resourceId: context.resourceId,
                    result,
                    reason,
                    metadata: context.metadata,
                    timestamp: new Date(),
                },
            });
        }
        catch (error) {
            logger_1.logger.error('Failed to log access attempt', { error, context });
        }
    }
    /**
     * Get audit logs with filtering
     */
    async getAuditLogs(filters) {
        try {
            const where = {};
            if (filters.userId)
                where.userId = filters.userId;
            if (filters.resource)
                where.resource = filters.resource;
            if (filters.action)
                where.action = filters.action;
            if (filters.result)
                where.result = filters.result;
            if (filters.startDate || filters.endDate) {
                where.timestamp = {};
                if (filters.startDate)
                    where.timestamp.gte = filters.startDate;
                if (filters.endDate)
                    where.timestamp.lte = filters.endDate;
            }
            const [logs, total] = await Promise.all([
                this.prisma.auditLog.findMany({
                    where,
                    orderBy: { timestamp: 'desc' },
                    take: filters.limit || 100,
                    skip: filters.offset || 0,
                }),
                this.prisma.auditLog.count({ where }),
            ]);
            return {
                logs: logs.map(log => ({
                    id: log.id,
                    userId: log.userId,
                    action: log.action,
                    resource: log.resource,
                    resourceId: log.resourceId,
                    result: log.result,
                    reason: log.reason,
                    timestamp: log.timestamp,
                    metadata: log.metadata,
                })),
                total,
            };
        }
        catch (error) {
            logger_1.logger.error('Failed to get audit logs', { error, filters });
            throw error;
        }
    }
    /**
     * Get all roles
     */
    async getRoles() {
        return Array.from(this.roleCache.values());
    }
    /**
     * Get role by ID
     */
    async getRole(roleId) {
        return this.roleCache.get(roleId) || null;
    }
    /**
     * Update role permissions
     */
    async updateRole(roleId, updates, updatedBy) {
        try {
            const role = this.roleCache.get(roleId);
            if (!role) {
                throw new Error('Role not found');
            }
            if (role.isSystemRole) {
                throw new Error('Cannot modify system roles');
            }
            // Check if updater has permission
            const hasPermission = await this.hasPermission({
                userId: updatedBy,
                resource: 'roles',
                action: 'update',
                resourceId: roleId,
            });
            if (!hasPermission) {
                throw new Error('Insufficient permissions to update role');
            }
            const updatedRole = await this.prisma.role.update({
                where: { id: roleId },
                data: updates,
            });
            // Update cache
            this.roleCache.set(roleId, {
                id: updatedRole.id,
                name: updatedRole.name,
                description: updatedRole.description,
                permissions: updatedRole.permissions,
                isSystemRole: updatedRole.isSystemRole,
                hierarchy: updatedRole.hierarchy,
            });
            // Clear all user permission caches since role changed
            this.permissionCache.clear();
            this.eventBus.emit(EventBus_1.EVENT_TYPES.ROLE_UPDATED, {
                roleId,
                updatedBy,
            });
            logger_1.logger.info('Role updated successfully', { roleId, updatedBy });
        }
        catch (error) {
            logger_1.logger.error('Failed to update role', { error, roleId });
            throw error;
        }
    }
    /**
     * Delete custom role
     */
    async deleteRole(roleId, deletedBy) {
        try {
            const role = this.roleCache.get(roleId);
            if (!role) {
                throw new Error('Role not found');
            }
            if (role.isSystemRole) {
                throw new Error('Cannot delete system roles');
            }
            // Check if deleter has permission
            const hasPermission = await this.hasPermission({
                userId: deletedBy,
                resource: 'roles',
                action: 'delete',
                resourceId: roleId,
            });
            if (!hasPermission) {
                throw new Error('Insufficient permissions to delete role');
            }
            // Remove role assignments first
            await this.prisma.userRole.deleteMany({
                where: { roleId },
            });
            // Delete the role
            await this.prisma.role.delete({
                where: { id: roleId },
            });
            // Remove from cache
            this.roleCache.delete(roleId);
            // Clear all user permission caches
            this.permissionCache.clear();
            this.eventBus.emit(EventBus_1.EVENT_TYPES.ROLE_DELETED, {
                roleId,
                deletedBy,
            });
            logger_1.logger.info('Role deleted successfully', { roleId, deletedBy });
        }
        catch (error) {
            logger_1.logger.error('Failed to delete role', { error, roleId });
            throw error;
        }
    }
    /**
     * Get users with specific role
     */
    async getUsersWithRole(roleId) {
        try {
            const userRoles = await this.prisma.userRole.findMany({
                where: { roleId },
                include: { user: true },
            });
            return userRoles.map(ur => ur.user);
        }
        catch (error) {
            logger_1.logger.error('Failed to get users with role', { error, roleId });
            throw error;
        }
    }
    /**
     * Middleware function for Express routes
     */
    requirePermission(resource, action) {
        return async (req, res, next) => {
            try {
                const userId = req.user?.id;
                if (!userId) {
                    return res.status(401).json({
                        success: false,
                        error: 'Authentication required',
                    });
                }
                const hasPermission = await this.hasPermission({
                    userId,
                    resource,
                    action,
                    resourceId: req.params.id,
                    metadata: req.body,
                });
                if (!hasPermission) {
                    return res.status(403).json({
                        success: false,
                        error: 'Insufficient permissions',
                    });
                }
                next();
            }
            catch (error) {
                logger_1.logger.error('Permission middleware error', { error });
                res.status(500).json({
                    success: false,
                    error: 'Internal server error',
                });
            }
        };
    }
}
exports.RoleBasedAccessControl = RoleBasedAccessControl;

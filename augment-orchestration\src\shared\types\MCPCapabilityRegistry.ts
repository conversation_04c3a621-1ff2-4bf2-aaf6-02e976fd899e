/**
 * Model Context Protocol (MCP) + Capability Registry Types and Interfaces
 * 
 * Enhanced MCP integration with dynamic agent selection based on capabilities
 * and trust scores. Standardized request/response system for AI orchestration.
 */

export enum CapabilityType {
  CODE_GENERATION = 'CODE_GENERATION',
  CODE_REVIEW = 'CODE_REVIEW',
  TESTING = 'TESTING',
  DEBUGGING = 'DEBUGGING',
  DOCUMENTATION = 'DOCUMENTATION',
  REFACTORING = 'REFACTORING',
  OPTIMIZATION = 'OPTIMIZATION',
  SECURITY_ANALYSIS = 'SECURITY_ANALYSIS',
  PERFORMANCE_ANALYSIS = 'PERFORMANCE_ANALYSIS',
  ARCHITECTURE_DESIGN = 'ARCHITECTURE_DESIGN',
  DATABASE_DESIGN = 'DATABASE_DESIGN',
  API_DESIGN = 'API_DESIGN',
  UI_UX_DESIGN = 'UI_UX_DESIGN',
  DEPLOYMENT = 'DEPLOYMENT',
  MONITORING = 'MONITORING',
  ANALYSIS = 'ANALYSIS',
  TRANSLATION = 'TRANSLATION',
  NATURAL_LANGUAGE = 'NATURAL_LANGUAGE',
  DATA_PROCESSING = 'DATA_PROCESSING',
  MACHINE_LEARNING = 'MACHINE_LEARNING'
}

export enum ProficiencyLevel {
  NOVICE = 'NOVICE',
  INTERMEDIATE = 'INTERMEDIATE',
  ADVANCED = 'ADVANCED',
  EXPERT = 'EXPERT',
  MASTER = 'MASTER'
}

export enum AgentStatus {
  AVAILABLE = 'AVAILABLE',
  BUSY = 'BUSY',
  OFFLINE = 'OFFLINE',
  MAINTENANCE = 'MAINTENANCE',
  ERROR = 'ERROR'
}

export enum RequestPriority {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  URGENT = 'URGENT',
  CRITICAL = 'CRITICAL'
}

export enum RequestStatus {
  PENDING = 'PENDING',
  ASSIGNED = 'ASSIGNED',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED',
  TIMEOUT = 'TIMEOUT'
}

export interface AgentCapability {
  type: CapabilityType;
  proficiencyLevel: ProficiencyLevel;
  confidence: number; // 0-1 scale
  lastUpdated: Date;
  validatedBy?: string;
  certifications: string[];
  specializations: string[];
  limitations: string[];
  prerequisites: string[];
  estimatedPerformance: PerformanceMetrics;
}

export interface PerformanceMetrics {
  averageExecutionTime: number;
  successRate: number;
  qualityScore: number;
  resourceEfficiency: number;
  userSatisfactionScore: number;
  errorRate: number;
  throughput: number;
  reliability: number;
}

export interface AgentProfile {
  id: string;
  name: string;
  description: string;
  version: string;
  status: AgentStatus;
  capabilities: AgentCapability[];
  trustScore: number;
  reputation: number;
  totalRequests: number;
  successfulRequests: number;
  averageRating: number;
  lastActive: Date;
  createdAt: Date;
  updatedAt: Date;
  metadata: AgentMetadata;
  configuration: AgentConfiguration;
  performance: AgentPerformance;
  availability: AgentAvailability;
}

export interface AgentMetadata {
  provider: string;
  model: string;
  version: string;
  architecture: string;
  trainingData: string;
  knowledgeCutoff?: Date;
  languages: string[];
  frameworks: string[];
  domains: string[];
  tags: string[];
  customFields: Record<string, any>;
}

export interface AgentConfiguration {
  maxConcurrentRequests: number;
  timeoutMs: number;
  retryPolicy: RetryPolicy;
  resourceLimits: ResourceLimits;
  securitySettings: SecuritySettings;
  qualityThresholds: QualityThresholds;
  costSettings: CostSettings;
  preferences: AgentPreferences;
}

export interface RetryPolicy {
  maxRetries: number;
  retryDelay: number;
  backoffMultiplier: number;
  retryableErrors: string[];
  nonRetryableErrors: string[];
}

export interface ResourceLimits {
  maxMemoryMB: number;
  maxCpuPercent: number;
  maxDiskMB: number;
  maxNetworkMBps: number;
  maxExecutionTime: number;
  maxTokens?: number;
  maxRequestSize: number;
  maxResponseSize: number;
}

export interface SecuritySettings {
  requiresAuthentication: boolean;
  allowedOrigins: string[];
  encryptionRequired: boolean;
  auditLogging: boolean;
  dataRetention: number;
  privacyLevel: 'PUBLIC' | 'INTERNAL' | 'CONFIDENTIAL' | 'RESTRICTED';
  complianceFrameworks: string[];
}

export interface QualityThresholds {
  minSuccessRate: number;
  minQualityScore: number;
  maxErrorRate: number;
  minUserSatisfaction: number;
  maxResponseTime: number;
  minReliability: number;
}

export interface CostSettings {
  costPerRequest: number;
  costPerToken?: number;
  costPerMinute: number;
  billingModel: 'PAY_PER_USE' | 'SUBSCRIPTION' | 'HYBRID';
  budgetLimits: BudgetLimits;
}

export interface BudgetLimits {
  dailyLimit: number;
  monthlyLimit: number;
  perRequestLimit: number;
  alertThresholds: number[];
}

export interface AgentPreferences {
  preferredRequestTypes: CapabilityType[];
  workingHours: WorkingHours;
  loadBalancing: 'ROUND_ROBIN' | 'LEAST_LOADED' | 'WEIGHTED' | 'RANDOM';
  failoverStrategy: 'IMMEDIATE' | 'DELAYED' | 'MANUAL';
  scalingPolicy: ScalingPolicy;
}

export interface WorkingHours {
  timezone: string;
  schedule: DaySchedule[];
  holidays: Date[];
  maintenanceWindows: MaintenanceWindow[];
}

export interface DaySchedule {
  dayOfWeek: number; // 0-6, Sunday = 0
  startTime: string; // HH:MM format
  endTime: string; // HH:MM format
  enabled: boolean;
}

export interface MaintenanceWindow {
  startTime: Date;
  endTime: Date;
  description: string;
  recurring: boolean;
  recurrencePattern?: string;
}

export interface ScalingPolicy {
  minInstances: number;
  maxInstances: number;
  targetUtilization: number;
  scaleUpThreshold: number;
  scaleDownThreshold: number;
  cooldownPeriod: number;
}

export interface AgentPerformance {
  currentLoad: number;
  queueLength: number;
  averageResponseTime: number;
  recentSuccessRate: number;
  recentQualityScore: number;
  resourceUtilization: ResourceUtilization;
  trends: PerformanceTrend[];
  benchmarks: Benchmark[];
}

export interface ResourceUtilization {
  cpu: number;
  memory: number;
  disk: number;
  network: number;
  tokens?: number;
}

export interface PerformanceTrend {
  timestamp: Date;
  metric: string;
  value: number;
  trend: 'IMPROVING' | 'DECLINING' | 'STABLE';
}

export interface Benchmark {
  name: string;
  score: number;
  percentile: number;
  comparedTo: string;
  timestamp: Date;
}

export interface AgentAvailability {
  isAvailable: boolean;
  estimatedWaitTime: number;
  nextAvailableSlot?: Date;
  maintenanceSchedule: MaintenanceWindow[];
  capacity: CapacityInfo;
  healthStatus: HealthStatus;
}

export interface CapacityInfo {
  current: number;
  maximum: number;
  reserved: number;
  utilization: number;
  projectedCapacity: ProjectedCapacity[];
}

export interface ProjectedCapacity {
  timestamp: Date;
  expectedLoad: number;
  availableCapacity: number;
  confidence: number;
}

export interface HealthStatus {
  status: 'HEALTHY' | 'DEGRADED' | 'UNHEALTHY' | 'UNKNOWN';
  lastHealthCheck: Date;
  healthScore: number;
  issues: HealthIssue[];
  diagnostics: HealthDiagnostic[];
}

export interface HealthIssue {
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  category: string;
  description: string;
  detectedAt: Date;
  resolvedAt?: Date;
  impact: string;
}

export interface HealthDiagnostic {
  component: string;
  status: 'PASS' | 'FAIL' | 'WARN';
  message: string;
  timestamp: Date;
  details?: any;
}

export interface MCPRequest {
  id: string;
  type: CapabilityType;
  priority: RequestPriority;
  status: RequestStatus;
  requesterInfo: RequesterInfo;
  requirements: RequestRequirements;
  constraints: RequestConstraints;
  context: RequestContext;
  payload: RequestPayload;
  routing: RequestRouting;
  tracking: RequestTracking;
  createdAt: Date;
  updatedAt: Date;
}

export interface RequesterInfo {
  id: string;
  name: string;
  role: string;
  organization?: string;
  contactInfo: ContactInfo;
  preferences: RequesterPreferences;
}

export interface ContactInfo {
  email?: string;
  phone?: string;
  slack?: string;
  webhook?: string;
}

export interface RequesterPreferences {
  preferredAgents: string[];
  excludedAgents: string[];
  qualityOverSpeed: boolean;
  costSensitive: boolean;
  notificationSettings: NotificationSettings;
}

export interface NotificationSettings {
  onAssignment: boolean;
  onProgress: boolean;
  onCompletion: boolean;
  onFailure: boolean;
  channels: string[];
}

export interface RequestRequirements {
  capabilities: CapabilityRequirement[];
  qualityThresholds: QualityThresholds;
  performanceRequirements: PerformanceRequirements;
  securityRequirements: SecurityRequirements;
  complianceRequirements: string[];
  customRequirements: Record<string, any>;
}

export interface CapabilityRequirement {
  type: CapabilityType;
  minProficiencyLevel: ProficiencyLevel;
  required: boolean;
  weight: number;
  specializations?: string[];
}

export interface PerformanceRequirements {
  maxResponseTime: number;
  minSuccessRate: number;
  maxErrorRate: number;
  minThroughput?: number;
  maxCost?: number;
}

export interface SecurityRequirements {
  encryptionRequired: boolean;
  auditLogging: boolean;
  dataClassification: string;
  accessControls: string[];
  complianceFrameworks: string[];
}

export interface RequestConstraints {
  timeoutMs: number;
  maxRetries: number;
  budgetLimit?: number;
  deadlineAt?: Date;
  resourceLimits: ResourceLimits;
  geographicRestrictions?: string[];
  regulatoryConstraints?: string[];
}

export interface RequestContext {
  projectId: string;
  workflowId?: string;
  sessionId?: string;
  parentRequestId?: string;
  relatedRequests: string[];
  environment: 'development' | 'staging' | 'production';
  metadata: Record<string, any>;
}

export interface RequestPayload {
  input: any;
  format: string;
  schema?: any;
  validation?: ValidationRules;
  transformation?: TransformationRules;
  examples?: Example[];
}

export interface ValidationRules {
  required: string[];
  types: Record<string, string>;
  constraints: Record<string, any>;
  customValidators: string[];
}

export interface TransformationRules {
  inputTransforms: Transform[];
  outputTransforms: Transform[];
}

export interface Transform {
  type: string;
  parameters: Record<string, any>;
  order: number;
}

export interface Example {
  input: any;
  expectedOutput: any;
  description: string;
  weight: number;
}

export interface RequestRouting {
  strategy: 'BEST_FIT' | 'LOAD_BALANCED' | 'ROUND_ROBIN' | 'MANUAL';
  candidateAgents: string[];
  selectedAgent?: string;
  routingScore?: number;
  routingReason?: string;
  fallbackAgents: string[];
  routingHistory: RoutingEvent[];
}

export interface RoutingEvent {
  timestamp: Date;
  event: 'CANDIDATE_EVALUATION' | 'AGENT_SELECTION' | 'ASSIGNMENT' | 'REASSIGNMENT' | 'FALLBACK';
  agentId?: string;
  score?: number;
  reason: string;
  metadata?: any;
}

export interface RequestTracking {
  assignedAt?: Date;
  startedAt?: Date;
  completedAt?: Date;
  duration?: number;
  progress: number; // 0-100
  milestones: Milestone[];
  events: TrackingEvent[];
  metrics: RequestMetrics;
}

export interface Milestone {
  name: string;
  description: string;
  targetDate: Date;
  completedAt?: Date;
  status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'DELAYED';
}

export interface TrackingEvent {
  timestamp: Date;
  type: string;
  description: string;
  agentId?: string;
  metadata?: any;
}

export interface RequestMetrics {
  executionTime: number;
  queueTime: number;
  processingTime: number;
  resourceUsage: ResourceUtilization;
  qualityScore: number;
  cost: number;
  retryCount: number;
  errorCount: number;
}

export interface MCPResponse {
  requestId: string;
  status: RequestStatus;
  agentId: string;
  result?: any;
  error?: ResponseError;
  metadata: ResponseMetadata;
  quality: QualityAssessment;
  performance: ResponsePerformance;
  createdAt: Date;
}

export interface ResponseError {
  code: string;
  message: string;
  details?: any;
  retryable: boolean;
  category: 'VALIDATION' | 'EXECUTION' | 'TIMEOUT' | 'RESOURCE' | 'SYSTEM';
}

export interface ResponseMetadata {
  agentVersion: string;
  processingTime: number;
  resourcesUsed: ResourceUtilization;
  confidence: number;
  alternatives?: Alternative[];
  warnings: string[];
  recommendations: string[];
}

export interface Alternative {
  result: any;
  confidence: number;
  reasoning: string;
  cost: number;
}

export interface QualityAssessment {
  overallScore: number;
  dimensions: QualityDimension[];
  feedback: QualityFeedback[];
  validation: ValidationResult[];
}

export interface QualityDimension {
  name: string;
  score: number;
  weight: number;
  description: string;
}

export interface QualityFeedback {
  source: 'AUTOMATED' | 'HUMAN' | 'PEER_REVIEW';
  rating: number;
  comments: string;
  timestamp: Date;
  reviewer?: string;
}

export interface ValidationResult {
  validator: string;
  passed: boolean;
  score: number;
  message: string;
  details?: any;
}

export interface ResponsePerformance {
  executionTime: number;
  throughput: number;
  resourceEfficiency: number;
  scalability: number;
  reliability: number;
  benchmarkComparison: BenchmarkComparison[];
}

export interface BenchmarkComparison {
  benchmark: string;
  score: number;
  percentile: number;
  comparison: 'BETTER' | 'WORSE' | 'SIMILAR';
}

export interface CapabilityRegistry {
  agents: Map<string, AgentProfile>;
  capabilities: Map<CapabilityType, AgentCapability[]>;
  trustScores: Map<string, TrustScore>;
  performanceHistory: Map<string, PerformanceHistory>;
  availabilityMatrix: AvailabilityMatrix;
  routingRules: RoutingRule[];
  selectionCriteria: SelectionCriteria;
}

export interface TrustScore {
  agentId: string;
  overallTrust: number;
  dimensions: TrustDimension[];
  history: TrustEvent[];
  lastUpdated: Date;
}

export interface TrustDimension {
  name: string;
  score: number;
  weight: number;
  evidence: TrustEvidence[];
}

export interface TrustEvidence {
  type: string;
  value: number;
  timestamp: Date;
  source: string;
  verified: boolean;
}

export interface TrustEvent {
  timestamp: Date;
  event: 'INCREASE' | 'DECREASE' | 'RESET';
  amount: number;
  reason: string;
  evidence?: any;
}

export interface PerformanceHistory {
  agentId: string;
  records: PerformanceRecord[];
  aggregates: PerformanceAggregate[];
  trends: PerformanceTrend[];
}

export interface PerformanceRecord {
  timestamp: Date;
  requestId: string;
  capability: CapabilityType;
  metrics: PerformanceMetrics;
  context: any;
}

export interface PerformanceAggregate {
  period: 'HOUR' | 'DAY' | 'WEEK' | 'MONTH';
  timestamp: Date;
  metrics: PerformanceMetrics;
  requestCount: number;
}

export interface AvailabilityMatrix {
  agents: string[];
  capabilities: CapabilityType[];
  matrix: AvailabilityCell[][];
  lastUpdated: Date;
}

export interface AvailabilityCell {
  agentId: string;
  capability: CapabilityType;
  available: boolean;
  capacity: number;
  estimatedWaitTime: number;
  qualityScore: number;
  cost: number;
}

export interface RoutingRule {
  id: string;
  name: string;
  description: string;
  conditions: RoutingCondition[];
  actions: RoutingAction[];
  priority: number;
  enabled: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface RoutingCondition {
  field: string;
  operator: 'EQ' | 'NEQ' | 'GT' | 'GTE' | 'LT' | 'LTE' | 'IN' | 'NOT_IN' | 'CONTAINS' | 'REGEX';
  value: any;
  weight: number;
}

export interface RoutingAction {
  type: 'SELECT_AGENT' | 'EXCLUDE_AGENT' | 'SET_PRIORITY' | 'ADD_CONSTRAINT' | 'TRANSFORM_REQUEST';
  parameters: Record<string, any>;
}

export interface SelectionCriteria {
  weights: SelectionWeights;
  thresholds: SelectionThresholds;
  preferences: SelectionPreferences;
  constraints: SelectionConstraints;
}

export interface SelectionWeights {
  trustScore: number;
  performanceScore: number;
  availabilityScore: number;
  costScore: number;
  capabilityMatch: number;
  reputationScore: number;
  loadBalance: number;
}

export interface SelectionThresholds {
  minTrustScore: number;
  minPerformanceScore: number;
  maxCost: number;
  maxWaitTime: number;
  minCapabilityMatch: number;
  minAvailability: number;
}

export interface SelectionPreferences {
  preferHighTrust: boolean;
  preferLowCost: boolean;
  preferFastResponse: boolean;
  preferHighQuality: boolean;
  balanceLoad: boolean;
  diversifySelection: boolean;
}

export interface SelectionConstraints {
  excludedAgents: string[];
  requiredCapabilities: CapabilityType[];
  geographicRestrictions: string[];
  complianceRequirements: string[];
  budgetLimits: BudgetLimits;
  timeConstraints: TimeConstraints;
}

export interface TimeConstraints {
  maxWaitTime: number;
  maxExecutionTime: number;
  deadline?: Date;
  preferredTimeSlots: TimeSlot[];
}

export interface TimeSlot {
  startTime: Date;
  endTime: Date;
  priority: number;
}

// Error types
export class MCPError extends Error {
  constructor(
    message: string,
    public code: string,
    public requestId?: string,
    public details?: any
  ) {
    super(message);
    this.name = 'MCPError';
  }
}

export class AgentSelectionError extends MCPError {
  constructor(message: string, requestId?: string, details?: any) {
    super(message, 'AGENT_SELECTION_ERROR', requestId, details);
  }
}

export class CapabilityMismatchError extends MCPError {
  constructor(message: string, requestId?: string, details?: any) {
    super(message, 'CAPABILITY_MISMATCH_ERROR', requestId, details);
  }
}

// Constants
export const MCP_CONSTANTS = {
  MAX_REQUEST_SIZE: ********, // 10MB
  MAX_RESPONSE_SIZE: ********, // 50MB
  DEFAULT_TIMEOUT: 300000, // 5 minutes
  MAX_RETRIES: 3,
  HEALTH_CHECK_INTERVAL: 30000, // 30 seconds
  PERFORMANCE_HISTORY_RETENTION: **********, // 30 days
  TRUST_SCORE_DECAY_RATE: 0.99,
  MIN_TRUST_SCORE: 0.1,
  MAX_CONCURRENT_REQUESTS: 1000,
  ROUTING_TIMEOUT: 10000, // 10 seconds
  QUALITY_ASSESSMENT_TIMEOUT: 60000 // 1 minute
};

// Utility functions
export const MCPUtils = {
  generateRequestId: (): string => {
    return `mcp_req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  },

  calculateCapabilityMatch: (
    required: CapabilityRequirement[],
    available: AgentCapability[]
  ): number => {
    let totalWeight = 0;
    let matchedWeight = 0;

    for (const req of required) {
      totalWeight += req.weight;
      
      const match = available.find(cap => 
        cap.type === req.type && 
        MCPUtils.compareProficiencyLevel(cap.proficiencyLevel, req.minProficiencyLevel) >= 0
      );

      if (match) {
        matchedWeight += req.weight * match.confidence;
      } else if (req.required) {
        return 0; // Required capability not available
      }
    }

    return totalWeight > 0 ? matchedWeight / totalWeight : 0;
  },

  compareProficiencyLevel: (level1: ProficiencyLevel, level2: ProficiencyLevel): number => {
    const levels = [
      ProficiencyLevel.NOVICE,
      ProficiencyLevel.INTERMEDIATE,
      ProficiencyLevel.ADVANCED,
      ProficiencyLevel.EXPERT,
      ProficiencyLevel.MASTER
    ];

    return levels.indexOf(level1) - levels.indexOf(level2);
  },

  calculateSelectionScore: (
    agent: AgentProfile,
    request: MCPRequest,
    criteria: SelectionCriteria
  ): number => {
    const weights = criteria.weights;
    let score = 0;

    // Trust score component
    score += agent.trustScore * weights.trustScore;

    // Performance score component
    const avgPerformance = (
      agent.performance.recentSuccessRate +
      agent.performance.recentQualityScore +
      (1 - agent.performance.averageResponseTime / 300000) // Normalize response time
    ) / 3;
    score += avgPerformance * weights.performanceScore;

    // Availability component
    const availabilityScore = agent.availability.isAvailable ? 
      (1 - agent.availability.estimatedWaitTime / 300000) : 0;
    score += availabilityScore * weights.availabilityScore;

    // Cost component (inverse - lower cost = higher score)
    const costScore = 1 - Math.min(agent.configuration.costSettings.costPerRequest / 10, 1);
    score += costScore * weights.costScore;

    // Capability match component
    const capabilityMatch = MCPUtils.calculateCapabilityMatch(
      request.requirements.capabilities,
      agent.capabilities
    );
    score += capabilityMatch * weights.capabilityMatch;

    // Reputation component
    const reputationScore = agent.reputation / 10; // Assuming 0-10 scale
    score += reputationScore * weights.reputationScore;

    // Load balance component (inverse - lower load = higher score)
    const loadScore = 1 - agent.performance.currentLoad;
    score += loadScore * weights.loadBalance;

    return Math.min(score, 1); // Normalize to 0-1
  },

  validateRequest: (request: MCPRequest): string[] => {
    const errors: string[] = [];

    if (!request.type) {
      errors.push('Request type is required');
    }

    if (!request.requirements.capabilities || request.requirements.capabilities.length === 0) {
      errors.push('At least one capability requirement is required');
    }

    if (request.constraints.timeoutMs <= 0) {
      errors.push('Timeout must be positive');
    }

    if (request.payload.input === undefined || request.payload.input === null) {
      errors.push('Request payload input is required');
    }

    return errors;
  },

  estimateExecutionTime: (
    agent: AgentProfile,
    request: MCPRequest
  ): number => {
    const baseTime = agent.performance.averageResponseTime;
    const complexityMultiplier = request.payload.input?.length ? 
      Math.log(request.payload.input.length) / 10 : 1;
    
    return baseTime * complexityMultiplier;
  },

  calculateCost: (
    agent: AgentProfile,
    request: MCPRequest,
    executionTime: number
  ): number => {
    const costSettings = agent.configuration.costSettings;
    let cost = 0;

    switch (costSettings.billingModel) {
      case 'PAY_PER_USE':
        cost = costSettings.costPerRequest;
        break;
      case 'SUBSCRIPTION':
        cost = (executionTime / 60000) * costSettings.costPerMinute;
        break;
      case 'HYBRID':
        cost = costSettings.costPerRequest + 
               (executionTime / 60000) * costSettings.costPerMinute;
        break;
    }

    return cost;
  }
};

#!/bin/bash

# 🔒 EcoStamp Security Setup Script
# Enterprise-grade security scanning for solo developers

echo "🔒 EcoStamp Security Setup Starting..."
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "source/package.json" ]; then
    print_error "Please run this script from the EcoStamp root directory"
    exit 1
fi

print_info "Setting up security scanning for EcoStamp..."

# Navigate to source directory
cd source

# Install dependencies
print_info "Installing security dependencies..."
npm install

if [ $? -eq 0 ]; then
    print_status "Dependencies installed successfully"
else
    print_error "Failed to install dependencies"
    exit 1
fi

# Run initial security scan
print_info "Running initial security scan..."

echo ""
echo "🔍 NPM Audit Scan:"
npm run security:audit

echo ""
echo "📜 License Compliance Check:"
npm run security:licenses

echo ""
echo "📦 Generating SBOM..."
npm run security:sbom

echo ""
echo "🔒 Security Linting..."
npm run security:eslint

echo ""
echo "📊 Generating Security Report..."
npm run security:report

echo ""
echo "=================================================="
print_status "Security setup complete!"
echo "=================================================="

echo ""
print_info "Available Security Commands:"
echo "  npm run security:audit          - Quick vulnerability scan"
echo "  npm run security:licenses       - License compliance check"
echo "  npm run security:sbom          - Generate SBOM"
echo "  npm run security:eslint        - Security linting"
echo "  npm run security:full-scan     - Complete security analysis"
echo "  npm run security:report        - Generate detailed report"

echo ""
print_info "Security Dashboard:"
echo "  Open: security-dashboard.html in your browser"

echo ""
print_info "Documentation:"
echo "  Read: ../SECURITY.md for complete guide"

echo ""
print_warning "Recommendations:"
echo "  • Run 'npm run security:full-scan' weekly"
echo "  • Check 'npm run security:report' for detailed analysis"
echo "  • Review SECURITY.md for best practices"
echo "  • Set up GitHub Actions for automated scanning"

echo ""
print_status "🌱 EcoStamp is now secured with enterprise-grade protection!"

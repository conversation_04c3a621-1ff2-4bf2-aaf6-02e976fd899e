@echo off
echo ========================================
echo Thread-Merging Orchestrator Quick Start
echo ========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed or not in PATH
    echo Please install Node.js 18.0.0 or higher from https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js found: 
node --version

REM Check if package.json exists
if not exist "package.json" (
    echo ❌ package.json not found
    echo Please run this script from the thread-merging-orchestrator directory
    pause
    exit /b 1
)

echo.
echo 📦 Installing dependencies...
call npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo 🔧 Running setup...
call node scripts/setup.js
if %errorlevel% neq 0 (
    echo ❌ Setup failed
    pause
    exit /b 1
)

echo.
echo ✅ Setup completed successfully!
echo.
echo 📋 Quick commands to try:
echo   npm start -- --help                    (Show all commands)
echo   npm start -- retrieve                  (Retrieve threads)
echo   npm start -- search "your query"       (Search threads)
echo   npm start -- orchestrate "your query"  (Full orchestration)
echo   npm start -- web                       (Start web interface)
echo.
echo 🌐 Web interface will be available at: http://localhost:3000
echo.
echo ⚠️  Don't forget to configure your API keys in the .env file!
echo.
pause

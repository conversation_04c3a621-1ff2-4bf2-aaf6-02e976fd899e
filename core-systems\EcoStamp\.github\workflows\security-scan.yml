name: 🔒 Security Scan

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  schedule:
    # Run weekly security scans on Sundays at 2 AM UTC
    - cron: '0 2 * * 0'
  workflow_dispatch:

# Fine-grained permissions for security compliance
# Note: Secrets access is implicitly available when needed
permissions:
  contents: read
  actions: read
  security-events: write

jobs:
  security-scan:
    name: 🛡️ Comprehensive Security Analysis
    runs-on: ubuntu-latest
    permissions:
      contents: read
      actions: read
      security-events: write

    steps:
    - name: 📥 Checkout Code
      uses: actions/checkout@b4ffde65f46336ab88eb53be808477a3936bae11 # v4.1.1
      
    - name: 🟢 Setup Node.js
      uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: source/package-lock.json
        
    - name: 📦 Install Dependencies
      working-directory: ./source
      run: npm ci
      
    - name: 🔍 NPM Audit
      working-directory: ./source
      run: |
        npm audit --audit-level=moderate || true
        npm audit --json > npm-audit-report.json || true
      continue-on-error: true
      
    - name: 🐍 Snyk Security Scan
      working-directory: ./source
      env:
        SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
      if: always()
      run: |
        # Check if Snyk token is available (without exposing it in logs)
        if [ -n "$SNYK_TOKEN" ]; then
          echo "🔑 Snyk token found, running authenticated scan..."
          npx snyk auth "$SNYK_TOKEN"
          npx snyk test --json > snyk-report.json || true
          npx snyk monitor || true
        else
          echo "⚠️ Snyk token not configured, running basic scan..."
          npx snyk test --json > snyk-report.json || echo "Snyk scan failed - token required"
        fi
      continue-on-error: true
        
    - name: 📜 License Compliance Check
      working-directory: ./source
      run: |
        npx license-checker --json > license-report.json
        npx license-checker --summary
      continue-on-error: true
      
    - name: 📦 Generate SBOM
      working-directory: ./source
      run: |
        npx @cyclonedx/cyclonedx-npm --output-file sbom.json
        npx @cyclonedx/cyclonedx-npm --output-format xml --output-file sbom.xml
      continue-on-error: true
      
    - name: 🔒 ESLint Security Analysis
      working-directory: ./source
      run: |
        npx eslint . --ext .js,.ts --config .eslintrc.security.js --format json > eslint-security-report.json || true
      continue-on-error: true
      
    - name: 🏃‍♂️ Run Retire.js (Known Vulnerable Libraries)
      working-directory: ./source
      run: |
        npx retire --outputformat json --outputpath retire-report.json || true
      continue-on-error: true
      
    - name: 📊 Generate Security Report
      working-directory: ./source
      run: |
        mkdir -p security-reports
        npm run security:report || true
      continue-on-error: true
      
    - name: 📤 Upload Security Reports
      uses: actions/upload-artifact@834a144ee995460fba8ed112a2fc961b36a5ec5a # v4.3.6
      with:
        name: security-reports-${{ github.sha }}
        path: |
          source/security-reports/
          source/*-report.json
          source/sbom.*
        retention-days: 30
        
    - name: 📋 Security Summary
      working-directory: ./source
      run: |
        echo "## 🔒 Security Scan Summary" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        
        # NPM Audit Summary
        if [ -f npm-audit-report.json ]; then
          VULNS=$(jq -r '.metadata.vulnerabilities.total // 0' npm-audit-report.json)
          echo "### 📋 NPM Audit: $VULNS vulnerabilities found" >> $GITHUB_STEP_SUMMARY
        fi
        
        # License Summary
        if [ -f license-report.json ]; then
          PACKAGES=$(jq -r 'keys | length' license-report.json)
          echo "### 📜 License Check: $PACKAGES packages analyzed" >> $GITHUB_STEP_SUMMARY
        fi
        
        # SBOM Summary
        if [ -f sbom.json ]; then
          COMPONENTS=$(jq -r '.components | length' sbom.json)
          echo "### 📦 SBOM: $COMPONENTS components documented" >> $GITHUB_STEP_SUMMARY
        fi
        
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "📊 Detailed reports available in workflow artifacts" >> $GITHUB_STEP_SUMMARY

  codeql-analysis:
    name: 🔍 CodeQL Security Analysis
    runs-on: ubuntu-latest
    permissions:
      actions: read
      contents: read
      security-events: write
      
    steps:
    - name: 📥 Checkout Code
      uses: actions/checkout@b4ffde65f46336ab88eb53be808477a3936bae11 # v4.1.1
      
    - name: 🔍 Initialize CodeQL
      uses: github/codeql-action/init@181d5eefc20863364f96762470ba6f862bdef56b # v3.29.2 (latest stable)
      with:
        languages: javascript

    - name: 🏗️ Autobuild
      uses: github/codeql-action/autobuild@181d5eefc20863364f96762470ba6f862bdef56b # v3.29.2 (latest stable)

    - name: 🔍 Perform CodeQL Analysis
      uses: github/codeql-action/analyze@181d5eefc20863364f96762470ba6f862bdef56b # v3.29.2 (latest stable)

  dependency-review:
    name: 📦 Dependency Review
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    permissions:
      contents: read
      pull-requests: read

    steps:
    - name: 📥 Checkout Code
      uses: actions/checkout@b4ffde65f46336ab88eb53be808477a3936bae11 # v4.1.1
      
    - name: 📦 Dependency Review
      uses: actions/dependency-review-action@5a2ce3f5b92ee19cbb1541a4984c76d921601d7c # v4.3.4
      with:
        fail-on-severity: moderate
        allow-licenses: MIT, Apache-2.0, BSD-2-Clause, BSD-3-Clause, ISC
        deny-licenses: GPL-3.0, AGPL-3.0

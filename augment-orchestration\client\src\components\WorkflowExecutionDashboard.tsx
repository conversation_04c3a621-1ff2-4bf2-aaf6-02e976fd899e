import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Card,
  CardContent,
  Chip,
  LinearProgress,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Alert,
  IconButton,
  Tooltip,
  Timeline,
  TimelineItem,
  TimelineSeparator,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
  TimelineOppositeContent,
} from '@mui/material';
import {
  PlayArrow,
  Stop,
  Refresh,
  Visibility,
  CheckCircle,
  Error,
  Schedule,
  Group,
  TrendingUp,
  Cancel,
} from '@mui/icons-material';
import { WorkflowExecution, WorkflowTemplate } from '@/shared/types';
import { io, Socket } from 'socket.io-client';

interface WorkflowExecutionDashboardProps {
  executions: WorkflowExecution[];
  templates: WorkflowTemplate[];
  onStartExecution?: (templateId: string, context: any) => void;
  onCancelExecution?: (executionId: string) => void;
  onRefresh?: () => void;
}

interface ExecutionStatus {
  execution: WorkflowExecution;
  context: any;
  stages: Record<string, any>;
}

export const WorkflowExecutionDashboard: React.FC<WorkflowExecutionDashboardProps> = ({
  executions,
  templates,
  onStartExecution,
  onCancelExecution,
  onRefresh,
}) => {
  const [selectedExecution, setSelectedExecution] = useState<WorkflowExecution | null>(null);
  const [executionStatus, setExecutionStatus] = useState<ExecutionStatus | null>(null);
  const [startDialog, setStartDialog] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [socket, setSocket] = useState<Socket | null>(null);
  const [realTimeUpdates, setRealTimeUpdates] = useState<Record<string, any>>({});

  useEffect(() => {
    // Initialize WebSocket connection
    const newSocket = io('http://localhost:3001');
    setSocket(newSocket);

    // Listen for workflow events
    newSocket.on('workflow_started', (data) => {
      setRealTimeUpdates(prev => ({
        ...prev,
        [data.executionId]: { ...data, type: 'started' }
      }));
      onRefresh?.();
    });

    newSocket.on('workflow_stage_started', (data) => {
      setRealTimeUpdates(prev => ({
        ...prev,
        [data.executionId]: { ...data, type: 'stage_started' }
      }));
    });

    newSocket.on('workflow_stage_completed', (data) => {
      setRealTimeUpdates(prev => ({
        ...prev,
        [data.executionId]: { ...data, type: 'stage_completed' }
      }));
    });

    newSocket.on('workflow_completed', (data) => {
      setRealTimeUpdates(prev => ({
        ...prev,
        [data.executionId]: { ...data, type: 'completed' }
      }));
      onRefresh?.();
    });

    newSocket.on('workflow_failed', (data) => {
      setRealTimeUpdates(prev => ({
        ...prev,
        [data.executionId]: { ...data, type: 'failed' }
      }));
      onRefresh?.();
    });

    return () => {
      newSocket.disconnect();
    };
  }, [onRefresh]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED': return 'success';
      case 'RUNNING': return 'primary';
      case 'FAILED': return 'error';
      case 'CANCELLED': return 'default';
      case 'PENDING': return 'warning';
      default: return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'COMPLETED': return <CheckCircle />;
      case 'RUNNING': return <TrendingUp />;
      case 'FAILED': return <Error />;
      case 'CANCELLED': return <Cancel />;
      case 'PENDING': return <Schedule />;
      default: return <Schedule />;
    }
  };

  const calculateProgress = (execution: WorkflowExecution) => {
    // This would calculate based on completed stages
    // For now, return a simple calculation
    switch (execution.status) {
      case 'COMPLETED': return 100;
      case 'RUNNING': return 50; // Would be calculated from actual stages
      case 'FAILED': return 25;
      case 'CANCELLED': return 0;
      case 'PENDING': return 0;
      default: return 0;
    }
  };

  const handleStartExecution = () => {
    if (selectedTemplate) {
      onStartExecution?.(selectedTemplate, {});
      setStartDialog(false);
      setSelectedTemplate('');
    }
  };

  const handleViewDetails = async (execution: WorkflowExecution) => {
    setSelectedExecution(execution);
    // In a real implementation, this would fetch detailed status
    setExecutionStatus({
      execution,
      context: execution.context || {},
      stages: {
        analysis: { status: 'COMPLETED', startedAt: new Date(), completedAt: new Date() },
        design: { status: 'RUNNING', startedAt: new Date() },
        implementation: { status: 'PENDING' },
        testing: { status: 'PENDING' },
      },
    });
  };

  const formatDuration = (start: Date, end?: Date) => {
    const startTime = new Date(start).getTime();
    const endTime = end ? new Date(end).getTime() : Date.now();
    const duration = Math.floor((endTime - startTime) / 1000 / 60); // minutes
    return `${duration}m`;
  };

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h5">Workflow Executions</Typography>
        <Box>
          <Button
            startIcon={<Refresh />}
            onClick={onRefresh}
            sx={{ mr: 1 }}
          >
            Refresh
          </Button>
          <Button
            startIcon={<PlayArrow />}
            variant="contained"
            onClick={() => setStartDialog(true)}
          >
            Start Workflow
          </Button>
        </Box>
      </Box>

      {executions.length === 0 ? (
        <Alert severity="info">
          No workflow executions found. Start a workflow to see executions here.
        </Alert>
      ) : (
        <Grid container spacing={3}>
          {executions.map((execution) => (
            <Grid item xs={12} md={6} lg={4} key={execution.id}>
              <Card>
                <CardContent>
                  <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
                    <Box display="flex" alignItems="center" gap={1}>
                      {getStatusIcon(execution.status)}
                      <Typography variant="h6">
                        {execution.template?.name || 'Unknown Template'}
                      </Typography>
                    </Box>
                    <Chip
                      label={execution.status}
                      color={getStatusColor(execution.status)}
                      size="small"
                    />
                  </Box>

                  <Typography variant="body2" color="text.secondary" mb={2}>
                    {execution.template?.description || 'No description'}
                  </Typography>

                  <Box mb={2}>
                    <Typography variant="caption" color="text.secondary">
                      Progress
                    </Typography>
                    <LinearProgress
                      variant="determinate"
                      value={calculateProgress(execution)}
                      sx={{ mt: 0.5, mb: 1 }}
                    />
                    <Typography variant="caption" color="text.secondary">
                      {calculateProgress(execution)}% complete
                    </Typography>
                  </Box>

                  <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                    <Typography variant="caption" color="text.secondary">
                      Started: {new Date(execution.startedAt).toLocaleString()}
                    </Typography>
                    {execution.endedAt && (
                      <Typography variant="caption" color="text.secondary">
                        Duration: {formatDuration(execution.startedAt, execution.endedAt)}
                      </Typography>
                    )}
                  </Box>

                  <Box display="flex" justifyContent="space-between" alignItems="center">
                    <Typography variant="caption" color="text.secondary">
                      Stage: {execution.currentStage || 'Unknown'}
                    </Typography>
                    <Box>
                      <Tooltip title="View Details">
                        <IconButton
                          size="small"
                          onClick={() => handleViewDetails(execution)}
                        >
                          <Visibility />
                        </IconButton>
                      </Tooltip>
                      {execution.status === 'RUNNING' && (
                        <Tooltip title="Cancel">
                          <IconButton
                            size="small"
                            onClick={() => onCancelExecution?.(execution.id)}
                          >
                            <Stop />
                          </IconButton>
                        </Tooltip>
                      )}
                    </Box>
                  </Box>

                  {/* Real-time update indicator */}
                  {realTimeUpdates[execution.id] && (
                    <Alert severity="info" sx={{ mt: 2 }}>
                      {realTimeUpdates[execution.id].type === 'stage_started' && 
                        `Stage "${realTimeUpdates[execution.id].stageId}" started`}
                      {realTimeUpdates[execution.id].type === 'stage_completed' && 
                        `Stage "${realTimeUpdates[execution.id].stageId}" completed`}
                      {realTimeUpdates[execution.id].type === 'completed' && 
                        'Workflow completed successfully!'}
                      {realTimeUpdates[execution.id].type === 'failed' && 
                        'Workflow failed'}
                    </Alert>
                  )}
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {/* Start Workflow Dialog */}
      <Dialog open={startDialog} onClose={() => setStartDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Start Workflow Execution</DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary" mb={2}>
            Select a workflow template to execute:
          </Typography>
          <List>
            {templates.map((template) => (
              <ListItem
                key={template.id}
                button
                selected={selectedTemplate === template.id}
                onClick={() => setSelectedTemplate(template.id)}
              >
                <ListItemIcon>
                  <PlayArrow />
                </ListItemIcon>
                <ListItemText
                  primary={template.name}
                  secondary={template.description}
                />
              </ListItem>
            ))}
          </List>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setStartDialog(false)}>Cancel</Button>
          <Button 
            onClick={handleStartExecution} 
            variant="contained"
            disabled={!selectedTemplate}
          >
            Start Execution
          </Button>
        </DialogActions>
      </Dialog>

      {/* Execution Details Dialog */}
      <Dialog 
        open={!!selectedExecution} 
        onClose={() => setSelectedExecution(null)} 
        maxWidth="md" 
        fullWidth
      >
        <DialogTitle>
          Execution Details: {selectedExecution?.template?.name}
        </DialogTitle>
        <DialogContent>
          {executionStatus && (
            <Box>
              <Grid container spacing={2} mb={3}>
                <Grid item xs={6}>
                  <Typography><strong>Status:</strong> {executionStatus.execution.status}</Typography>
                  <Typography><strong>Current Stage:</strong> {executionStatus.execution.currentStage}</Typography>
                  <Typography><strong>Priority:</strong> {executionStatus.execution.priority}</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography><strong>Started:</strong> {new Date(executionStatus.execution.startedAt).toLocaleString()}</Typography>
                  {executionStatus.execution.endedAt && (
                    <Typography><strong>Ended:</strong> {new Date(executionStatus.execution.endedAt).toLocaleString()}</Typography>
                  )}
                </Grid>
              </Grid>

              <Typography variant="h6" gutterBottom>Stage Timeline</Typography>
              <Timeline>
                {Object.entries(executionStatus.stages).map(([stageId, stage]: [string, any]) => (
                  <TimelineItem key={stageId}>
                    <TimelineOppositeContent color="text.secondary">
                      {stage.startedAt && new Date(stage.startedAt).toLocaleTimeString()}
                    </TimelineOppositeContent>
                    <TimelineSeparator>
                      <TimelineDot color={
                        stage.status === 'COMPLETED' ? 'success' :
                        stage.status === 'RUNNING' ? 'primary' :
                        stage.status === 'FAILED' ? 'error' : 'grey'
                      }>
                        {getStatusIcon(stage.status)}
                      </TimelineDot>
                      <TimelineConnector />
                    </TimelineSeparator>
                    <TimelineContent>
                      <Typography variant="h6" component="span">
                        {stageId.charAt(0).toUpperCase() + stageId.slice(1)}
                      </Typography>
                      <Typography color="text.secondary">
                        Status: {stage.status}
                      </Typography>
                      {stage.assignedAgents && (
                        <Typography color="text.secondary">
                          Agents: {stage.assignedAgents.length}
                        </Typography>
                      )}
                    </TimelineContent>
                  </TimelineItem>
                ))}
              </Timeline>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSelectedExecution(null)}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default WorkflowExecutionDashboard;

"""
TimeStamp Validator for TimeStamp AI Domain

Validates timestamp AI outputs and assigns feedback types including support
for 'Partially Correct' scenarios specific to timestamp accuracy and environmental impact.
"""

import logging
from typing import Dict, Any, List
from datetime import datetime, timedelta
from ...core.feedback_types import ValidationResult, FeedbackType, ValidationSeverity


class TimeStampValidator:
    """
    Validates timestamp AI outputs and determines appropriate feedback types.
    
    Handles various timestamp-specific scenarios including partial accuracy,
    environmental impact thresholds, and hash verification issues.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.validation_criteria = self._initialize_criteria()
        
    def _initialize_criteria(self) -> Dict[str, Any]:
        """Initialize validation criteria for timestamp operations."""
        return {
            'correctness_thresholds': {
                'timestamp_accuracy': 60.0,      # 1 minute for "correct"
                'partial_accuracy': 300.0,       # 5 minutes for "partially correct"
                'hash_verification': True,       # Must verify for "correct"
                'environmental_efficiency': 0.8, # 80% efficiency for "correct"
                'partial_efficiency': 0.6       # 60% efficiency for "partially correct"
            },
            'severity_mappings': {
                'verification_failure': ValidationSeverity.CRITICAL,
                'timestamp_inaccuracy': ValidationSeverity.HIGH,
                'environmental_impact': ValidationSeverity.MEDIUM,
                'performance_issue': ValidationSeverity.LOW,
                'informational': ValidationSeverity.INFO
            },
            'partial_correctness_rules': {
                'timestamp_accuracy': {
                    'perfect_threshold': 10.0,     # 10 seconds for perfect
                    'good_threshold': 60.0,        # 1 minute for good
                    'acceptable_threshold': 300.0   # 5 minutes for acceptable
                },
                'environmental_impact': {
                    'excellent_efficiency': 0.9,   # 90% efficiency excellent
                    'good_efficiency': 0.8,        # 80% efficiency good
                    'acceptable_efficiency': 0.6   # 60% efficiency acceptable
                },
                'hash_verification': {
                    'full_verification': True,     # Complete verification
                    'partial_verification': 'hash_only',  # Hash present but not verified
                    'no_verification': False       # No hash or verification
                }
            }
        }
    
    def validate(self, 
                interpreted_output: Dict[str, Any], 
                match_results: Dict[str, Any], 
                context: Dict[str, Any]) -> ValidationResult:
        """
        Validate timestamp AI output and determine feedback type.
        
        Args:
            interpreted_output: Normalized output from interpreter
            match_results: Results from pattern matcher
            context: Additional validation context
            
        Returns:
            ValidationResult with feedback type and details
        """
        try:
            # Initialize validation result
            result = ValidationResult()
            result.metadata = {
                'validation_timestamp': match_results.get('metadata', {}).get('match_timestamp'),
                'validator_version': '1.0.0',
                'validation_type': 'timestamp_ai'
            }
            
            # Check for critical errors first
            if self._has_critical_errors(interpreted_output, match_results):
                result.feedback_type = FeedbackType.INCORRECT
                result.is_valid = False
                result.confidence_score = 0.0
                self._add_critical_error_issues(result, interpreted_output, match_results)
                return result
            
            # Determine feedback type based on output type and validation criteria
            output_type = context.get('output_type', 'unknown')
            feedback_type, confidence = self._determine_feedback_type(
                interpreted_output, match_results, context, output_type
            )
            
            result.feedback_type = feedback_type
            result.confidence_score = confidence
            result.is_valid = feedback_type in [FeedbackType.CORRECT, FeedbackType.PARTIALLY_CORRECT]
            
            # Add detailed issues and recommendations
            self._analyze_issues(result, interpreted_output, match_results, context)
            self._generate_recommendations(result, interpreted_output, match_results, context)
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error in timestamp validation: {str(e)}")
            
            error_result = ValidationResult()
            error_result.feedback_type = FeedbackType.MISCELLANEOUS
            error_result.is_valid = False
            error_result.confidence_score = 0.0
            error_result.issues = [{
                'type': 'validation_error',
                'severity': ValidationSeverity.CRITICAL,
                'message': f'Validation failed: {str(e)}',
                'details': {'error': str(e)}
            }]
            
            return error_result
    
    def _has_critical_errors(self, interpreted_output: Dict[str, Any], match_results: Dict[str, Any]) -> bool:
        """Check for critical errors that immediately mark output as incorrect."""
        # Check for data interpretation errors
        if interpreted_output.get('error', False):
            return True
        
        # Check for critical anomalies
        for anomaly in match_results.get('anomalies', []):
            if anomaly.get('severity') == 'critical':
                return True
        
        # Check for verification failures
        verification_failures = [
            'verification_failed',
            'missing_hash',
            'invalid_timestamp_format'
        ]
        
        for anomaly in match_results.get('anomalies', []):
            if anomaly.get('type') in verification_failures:
                return True
        
        return False
    
    def _determine_feedback_type(self, 
                                interpreted_output: Dict[str, Any], 
                                match_results: Dict[str, Any], 
                                context: Dict[str, Any],
                                output_type: str) -> tuple[FeedbackType, float]:
        """Determine feedback type and confidence score based on output type."""
        
        # Start with base confidence from matcher
        base_confidence = match_results.get('overall_confidence', 0.5)
        
        if output_type == 'timestamp_response':
            return self._validate_timestamp_response(interpreted_output, match_results, context, base_confidence)
        elif output_type == 'impact_calculation':
            return self._validate_impact_calculation(interpreted_output, match_results, context, base_confidence)
        elif output_type == 'hash_verification':
            return self._validate_hash_verification(interpreted_output, match_results, context, base_confidence)
        elif output_type in ['llm_conversation', 'llm_response']:
            return self._validate_llm_response(interpreted_output, match_results, context, base_confidence)
        else:
            return self._validate_general_output(interpreted_output, match_results, context, base_confidence)
    
    def _validate_timestamp_response(self, interpreted_output: Dict[str, Any], 
                                   match_results: Dict[str, Any], 
                                   context: Dict[str, Any], 
                                   base_confidence: float) -> tuple[FeedbackType, float]:
        """Validate timestamp response with partial correctness support."""
        rules = self.validation_criteria['partial_correctness_rules']['timestamp_accuracy']
        
        # Check for timestamp accuracy
        timestamp_warnings = [w for w in match_results.get('warnings', []) if w.get('type') == 'timestamp_drift']
        
        if not timestamp_warnings:
            # No drift warnings - check for perfect accuracy
            accurate_matches = [m for m in match_results.get('pattern_matches', []) if m.get('type') == 'accurate_timestamp']
            if accurate_matches:
                time_diff = accurate_matches[0].get('time_difference', 0)
                if time_diff <= rules['perfect_threshold']:
                    return FeedbackType.CORRECT, min(0.95, base_confidence + 0.1)
                elif time_diff <= rules['good_threshold']:
                    return FeedbackType.CORRECT, base_confidence
                else:
                    return FeedbackType.PARTIALLY_CORRECT, base_confidence * 0.8
        else:
            # Has drift warnings - check severity
            max_drift = max(w.get('time_difference', 0) for w in timestamp_warnings)
            if max_drift <= rules['acceptable_threshold']:
                return FeedbackType.PARTIALLY_CORRECT, base_confidence * 0.7
            else:
                return FeedbackType.INCORRECT, base_confidence * 0.3
        
        # Check hash verification
        hash_verification = self._assess_hash_verification(interpreted_output, match_results)
        if hash_verification == 'full_verification':
            return FeedbackType.CORRECT, base_confidence
        elif hash_verification == 'partial_verification':
            return FeedbackType.PARTIALLY_CORRECT, base_confidence * 0.8
        else:
            return FeedbackType.INCORRECT, base_confidence * 0.4
    
    def _validate_impact_calculation(self, interpreted_output: Dict[str, Any], 
                                   match_results: Dict[str, Any], 
                                   context: Dict[str, Any], 
                                   base_confidence: float) -> tuple[FeedbackType, float]:
        """Validate environmental impact calculation."""
        rules = self.validation_criteria['partial_correctness_rules']['environmental_impact']
        
        # Calculate efficiency score
        efficiency_score = self._calculate_environmental_efficiency(interpreted_output, match_results)
        
        if efficiency_score >= rules['excellent_efficiency']:
            return FeedbackType.CORRECT, min(0.95, base_confidence + 0.1)
        elif efficiency_score >= rules['good_efficiency']:
            return FeedbackType.CORRECT, base_confidence
        elif efficiency_score >= rules['acceptable_efficiency']:
            return FeedbackType.PARTIALLY_CORRECT, base_confidence * 0.8
        else:
            return FeedbackType.INCORRECT, base_confidence * 0.4
    
    def _validate_hash_verification(self, interpreted_output: Dict[str, Any], 
                                  match_results: Dict[str, Any], 
                                  context: Dict[str, Any], 
                                  base_confidence: float) -> tuple[FeedbackType, float]:
        """Validate hash verification process."""
        # Check verification status
        verification_status = interpreted_output.get('verification_status')
        
        if verification_status is True:
            return FeedbackType.CORRECT, base_confidence
        elif verification_status is False:
            return FeedbackType.INCORRECT, base_confidence * 0.2
        else:
            # No verification status - check if hash is present and valid
            hash_value = interpreted_output.get('hash') or interpreted_output.get('extracted_hash')
            if hash_value and interpreted_output.get('hash_valid', True):
                return FeedbackType.PARTIALLY_CORRECT, base_confidence * 0.7
            else:
                return FeedbackType.INCORRECT, base_confidence * 0.3
    
    def _validate_llm_response(self, interpreted_output: Dict[str, Any], 
                             match_results: Dict[str, Any], 
                             context: Dict[str, Any], 
                             base_confidence: float) -> tuple[FeedbackType, float]:
        """Validate LLM response quality."""
        # Count quality issues
        quality_warnings = [w for w in match_results.get('warnings', []) 
                          if w.get('type') in ['short_response', 'low_confidence', 'high_token_usage']]
        
        performance_warnings = [w for w in match_results.get('warnings', []) 
                              if w.get('type') in ['slow_response']]
        
        # Determine feedback based on issue counts
        if len(quality_warnings) == 0 and len(performance_warnings) <= 1:
            return FeedbackType.CORRECT, base_confidence
        elif len(quality_warnings) <= 1 and len(performance_warnings) <= 2:
            return FeedbackType.PARTIALLY_CORRECT, base_confidence * 0.8
        else:
            return FeedbackType.INCORRECT, base_confidence * 0.4
    
    def _validate_general_output(self, interpreted_output: Dict[str, Any], 
                               match_results: Dict[str, Any], 
                               context: Dict[str, Any], 
                               base_confidence: float) -> tuple[FeedbackType, float]:
        """Validate general timestamp AI output."""
        # Count issues by severity
        high_severity_count = len([a for a in match_results.get('anomalies', []) if a.get('severity') == 'high'])
        warning_count = len(match_results.get('warnings', []))
        
        # Determine feedback type based on issue counts
        if high_severity_count == 0 and warning_count <= 1:
            return FeedbackType.CORRECT, base_confidence
        elif high_severity_count <= 1 and warning_count <= 3:
            return FeedbackType.PARTIALLY_CORRECT, base_confidence * 0.7
        else:
            return FeedbackType.INCORRECT, base_confidence * 0.4
    
    def _assess_hash_verification(self, interpreted_output: Dict[str, Any], match_results: Dict[str, Any]) -> str:
        """Assess the level of hash verification achieved."""
        verification_status = interpreted_output.get('verification_status')
        
        if verification_status is True:
            return 'full_verification'
        
        # Check if hash is present and valid format
        hash_value = interpreted_output.get('hash') or interpreted_output.get('extracted_hash')
        if hash_value and interpreted_output.get('hash_valid', True):
            return 'partial_verification'
        
        return 'no_verification'
    
    def _calculate_environmental_efficiency(self, interpreted_output: Dict[str, Any], match_results: Dict[str, Any]) -> float:
        """Calculate environmental efficiency score."""
        efficiency_score = 1.0
        
        # Penalize for high resource usage warnings
        resource_warnings = [w for w in match_results.get('warnings', []) 
                           if w.get('type') in ['high_water_usage', 'high_electricity_usage', 'high_carbon_footprint']]
        
        efficiency_score -= len(resource_warnings) * 0.15
        
        # Penalize for missing impact data
        incomplete_warnings = [w for w in match_results.get('warnings', []) 
                             if w.get('type') == 'incomplete_impact_data']
        
        efficiency_score -= len(incomplete_warnings) * 0.2
        
        return max(0.0, efficiency_score)
    
    def _add_critical_error_issues(self, result: ValidationResult, 
                                  interpreted_output: Dict[str, Any], 
                                  match_results: Dict[str, Any]) -> None:
        """Add critical error issues to validation result."""
        if interpreted_output.get('error', False):
            result.issues.append({
                'type': 'data_interpretation_error',
                'severity': ValidationSeverity.CRITICAL,
                'message': interpreted_output.get('error_message', 'Data interpretation failed'),
                'details': {'raw_error': interpreted_output.get('error_message')}
            })
        
        for anomaly in match_results.get('anomalies', []):
            if anomaly.get('severity') in ['critical', 'high']:
                result.issues.append({
                    'type': anomaly.get('type', 'unknown_anomaly'),
                    'severity': ValidationSeverity.CRITICAL if anomaly.get('severity') == 'critical' else ValidationSeverity.HIGH,
                    'message': anomaly.get('message', 'Critical anomaly detected'),
                    'details': anomaly
                })
    
    def _analyze_issues(self, result: ValidationResult, 
                       interpreted_output: Dict[str, Any], 
                       match_results: Dict[str, Any], 
                       context: Dict[str, Any]) -> None:
        """Analyze and categorize all issues found during validation."""
        # Add warnings as low-severity issues
        for warning in match_results.get('warnings', []):
            result.issues.append({
                'type': warning.get('type', 'unknown_warning'),
                'severity': ValidationSeverity.LOW,
                'message': warning.get('message', 'Warning detected'),
                'details': warning
            })
        
        # Add medium-severity anomalies
        for anomaly in match_results.get('anomalies', []):
            if anomaly.get('severity') == 'medium':
                result.issues.append({
                    'type': anomaly.get('type', 'unknown_anomaly'),
                    'severity': ValidationSeverity.MEDIUM,
                    'message': anomaly.get('message', 'Anomaly detected'),
                    'details': anomaly
                })
    
    def _generate_recommendations(self, result: ValidationResult, 
                                 interpreted_output: Dict[str, Any], 
                                 match_results: Dict[str, Any], 
                                 context: Dict[str, Any]) -> None:
        """Generate recommendations based on validation results."""
        if result.feedback_type == FeedbackType.INCORRECT:
            result.recommendations.append("Review timestamp generation process and system clock synchronization")
            result.recommendations.append("Verify hash calculation and digital signature processes")
        
        elif result.feedback_type == FeedbackType.PARTIALLY_CORRECT:
            result.recommendations.append("Consider improving timestamp accuracy or environmental efficiency")
            result.recommendations.append("Review specific issues identified in validation")
        
        # Add specific recommendations based on issues
        timestamp_issues = [i for i in result.issues if 'timestamp' in i.get('type', '').lower()]
        if timestamp_issues:
            result.recommendations.append("Synchronize system clock with NTP servers for better accuracy")
        
        env_issues = [i for i in result.issues if any(term in i.get('type', '').lower() 
                     for term in ['water', 'electricity', 'carbon', 'environmental'])]
        if env_issues:
            result.recommendations.append("Optimize AI model usage to reduce environmental impact")
        
        hash_issues = [i for i in result.issues if 'hash' in i.get('type', '').lower()]
        if hash_issues:
            result.recommendations.append("Ensure proper hash generation and verification processes")
    
    def supports_partial_correctness(self) -> bool:
        """Check if validator supports 'Partially Correct' feedback type."""
        return True
    
    def get_validation_criteria(self) -> Dict[str, Any]:
        """Get the validation criteria used by this validator."""
        return self.validation_criteria.copy()

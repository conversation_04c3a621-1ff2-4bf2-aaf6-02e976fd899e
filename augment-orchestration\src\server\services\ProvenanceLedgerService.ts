/**
 * Provenance Ledger Service
 * 
 * Core Gap 2: Immutable Provenance Ledger implementation with blockchain-style
 * verification for all code generation, modification, testing, and merging actions.
 */

import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger';
import { cryptographicService } from './CryptographicService';
import {
  ProvenanceEntryData,
  CreateProvenanceEntryRequest,
  ProvenanceQuery,
  ProvenanceStats,
  ProvenanceChain,
  VerifyProvenanceChainRequest,
  ProvenanceChainVerificationResult,
  ProvenanceAuditReport,
  EntitySnapshot,
  ProvenanceAction,
  EntityType,
  BlockchainVerification,
  ImmutabilityGuard,
  ProvenanceError,
  ChainIntegrityError,
  ImmutabilityViolationError,
  HashVerificationError,
  PROVENANCE_CONSTANTS,
  ProvenanceUtils
} from '../../shared/types/ProvenanceLedger';

export class ProvenanceLedgerService implements BlockchainVerification, ImmutabilityGuard {
  private prisma: PrismaClient;
  private lastVerificationTime: Date = new Date();

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
    this.initializeLedger();
  }

  /**
   * Initialize the provenance ledger
   */
  private async initializeLedger(): Promise<void> {
    try {
      // Verify chain integrity on startup
      await this.performIntegrityCheck();
      logger.info('Provenance ledger initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize provenance ledger:', error);
      throw new ProvenanceError('Ledger initialization failed', 'INIT_ERROR');
    }
  }

  /**
   * Create a new provenance entry
   */
  async createProvenanceEntry(request: CreateProvenanceEntryRequest): Promise<ProvenanceEntryData> {
    try {
      // Get the previous entry for hash chaining
      const previousEntry = await this.getLastEntry(request.entityId);
      const previousHash = previousEntry?.blockHash || null;

      // Create entity snapshots
      const beforeState = request.beforeState ? this.validateAndProcessSnapshot(request.beforeState) : null;
      const afterState = request.afterState ? this.validateAndProcessSnapshot(request.afterState) : null;

      // Calculate change size and impact
      const changeSize = ProvenanceUtils.calculateChangeSize(beforeState, afterState);
      const impact = request.changeMetadata.impact || ProvenanceUtils.determineImpact(changeSize, request.entityType);

      // Create change metadata
      const changeMetadata = {
        reason: request.changeMetadata.reason,
        description: request.changeMetadata.description,
        tags: request.changeMetadata.tags || [],
        impact,
        reviewRequired: request.changeMetadata.reviewRequired || impact === 'CRITICAL',
        automatedChange: request.changeMetadata.automatedChange || false,
        changeSize,
        affectedFiles: request.changeMetadata.affectedFiles || [],
        testResults: request.changeMetadata.testResults
      };

      // Create the entry data
      const entryData: Omit<ProvenanceEntryData, 'id' | 'blockHash' | 'verificationHash'> = {
        previousHash,
        action: request.action,
        entityType: request.entityType,
        entityId: request.entityId,
        beforeState,
        afterState,
        changeMetadata,
        performedBy: request.performedBy,
        performedByType: request.performedByType || 'USER',
        timestamp: new Date(),
        isImmutable: true,
        linkedEventPacketId: request.linkedEventPacketId
      };

      // Calculate block hash
      const blockHash = this.calculateBlockHash(entryData as ProvenanceEntryData);
      const verificationHash = this.createImmutableRecord(entryData);

      // Store in database
      const entry = await this.prisma.provenanceEntry.create({
        data: {
          blockHash,
          previousHash,
          action: request.action,
          entityType: request.entityType,
          entityId: request.entityId,
          beforeState: beforeState ? JSON.stringify(beforeState) : null,
          afterState: afterState ? JSON.stringify(afterState) : null,
          changeMetadata: JSON.stringify(changeMetadata),
          performedBy: request.performedBy,
          timestamp: entryData.timestamp,
          isImmutable: true,
          eventPacketId: request.linkedEventPacketId
        },
        include: {
          user: true,
          signedEventPacket: true
        }
      });

      const provenanceEntry = this.convertToProvenanceData(entry, verificationHash);

      logger.info('Created provenance entry', {
        id: entry.id,
        action: request.action,
        entityType: request.entityType,
        entityId: request.entityId,
        performedBy: request.performedBy
      });

      return provenanceEntry;

    } catch (error) {
      logger.error('Failed to create provenance entry:', error);
      throw new ProvenanceError('Entry creation failed', 'CREATE_ERROR', error);
    }
  }

  /**
   * Query provenance entries
   */
  async queryProvenanceEntries(query: ProvenanceQuery): Promise<ProvenanceEntryData[]> {
    try {
      const where: any = {};

      if (query.entityId) where.entityId = query.entityId;
      if (query.entityType) where.entityType = query.entityType;
      if (query.action) where.action = query.action;
      if (query.performedBy) where.performedBy = query.performedBy;

      if (query.dateFrom || query.dateTo) {
        where.timestamp = {};
        if (query.dateFrom) where.timestamp.gte = query.dateFrom;
        if (query.dateTo) where.timestamp.lte = query.dateTo;
      }

      const entries = await this.prisma.provenanceEntry.findMany({
        where,
        include: {
          user: true,
          signedEventPacket: true
        },
        orderBy: { timestamp: 'desc' },
        take: query.limit || 100,
        skip: query.offset || 0
      });

      const provenanceEntries = entries.map(entry => this.convertToProvenanceData(entry));

      // Verify integrity if requested
      if (query.verifyIntegrity) {
        const verificationResult = await this.verifyChainIntegrity(provenanceEntries);
        if (!verificationResult) {
          logger.warn('Chain integrity verification failed for query results');
        }
      }

      return provenanceEntries;

    } catch (error) {
      logger.error('Failed to query provenance entries:', error);
      throw new ProvenanceError('Query failed', 'QUERY_ERROR', error);
    }
  }

  /**
   * Get provenance chain for an entity
   */
  async getProvenanceChain(entityId: string): Promise<ProvenanceChain> {
    try {
      const entries = await this.queryProvenanceEntries({
        entityId,
        verifyIntegrity: true
      });

      const isValid = await this.verifyChainIntegrity(entries);
      const integrityScore = this.calculateIntegrityScore(entries);

      return {
        entries,
        isValid,
        totalEntries: entries.length,
        firstEntry: entries.length > 0 ? entries[entries.length - 1].timestamp : new Date(),
        lastEntry: entries.length > 0 ? entries[0].timestamp : new Date(),
        integrityScore
      };

    } catch (error) {
      logger.error('Failed to get provenance chain:', error);
      throw new ProvenanceError('Chain retrieval failed', 'CHAIN_ERROR', error);
    }
  }

  /**
   * Verify provenance chain integrity
   */
  async verifyProvenanceChain(request: VerifyProvenanceChainRequest): Promise<ProvenanceChainVerificationResult> {
    try {
      const startTime = Date.now();
      
      const query: ProvenanceQuery = {
        entityId: request.entityId,
        dateFrom: request.startDate,
        dateTo: request.endDate
      };

      const entries = await this.queryProvenanceEntries(query);
      
      let validEntries = 0;
      let invalidEntries = 0;
      const corruptedEntries: ProvenanceEntryData[] = [];

      // Verify each entry
      for (const entry of entries) {
        const isValid = this.verifyEntry(entry, request.deepVerification);
        if (isValid) {
          validEntries++;
        } else {
          invalidEntries++;
          corruptedEntries.push(entry);
        }
      }

      // Verify hash chain
      const chainValid = this.validateHashChain(entries);
      if (!chainValid) {
        invalidEntries += entries.length - validEntries;
      }

      const integrityScore = entries.length > 0 ? (validEntries / entries.length) * 100 : 100;
      const verificationTime = Date.now() - startTime;

      const recommendations = this.generateRecommendations(corruptedEntries, chainValid);

      // Repair corruption if requested
      if (request.repairCorruption && corruptedEntries.length > 0) {
        await this.repairChainIntegrity(entries);
      }

      return {
        isValid: chainValid && invalidEntries === 0,
        totalEntries: entries.length,
        validEntries,
        invalidEntries,
        corruptedEntries,
        integrityScore,
        verificationTime,
        recommendations
      };

    } catch (error) {
      logger.error('Failed to verify provenance chain:', error);
      throw new ProvenanceError('Chain verification failed', 'VERIFICATION_ERROR', error);
    }
  }

  /**
   * Get provenance statistics
   */
  async getProvenanceStats(): Promise<ProvenanceStats> {
    try {
      const [
        totalEntries,
        recentActivity,
        actionStats,
        entityTypeStats,
        performerStats,
        integrityStatus
      ] = await Promise.all([
        this.prisma.provenanceEntry.count(),
        this.getRecentActivityStats(),
        this.getActionStats(),
        this.getEntityTypeStats(),
        this.getPerformerStats(),
        this.getIntegrityStatus()
      ]);

      const chainMetrics = await this.getChainMetrics();

      return {
        totalEntries,
        entriesByAction: actionStats,
        entriesByEntityType: entityTypeStats,
        entriesByPerformer: performerStats,
        integrityStatus,
        recentActivity,
        chainMetrics
      };

    } catch (error) {
      logger.error('Failed to get provenance stats:', error);
      throw new ProvenanceError('Stats retrieval failed', 'STATS_ERROR', error);
    }
  }

  /**
   * BlockchainVerification implementation
   */
  async verifyChainIntegrity(entries: ProvenanceEntryData[]): Promise<boolean> {
    try {
      if (entries.length === 0) return true;

      // Sort entries by timestamp
      const sortedEntries = entries.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());

      // Verify hash chain
      for (let i = 1; i < sortedEntries.length; i++) {
        const currentEntry = sortedEntries[i];
        const previousEntry = sortedEntries[i - 1];

        if (currentEntry.previousHash !== previousEntry.blockHash) {
          logger.warn('Hash chain broken', {
            currentId: currentEntry.id,
            expectedPreviousHash: previousEntry.blockHash,
            actualPreviousHash: currentEntry.previousHash
          });
          return false;
        }

        // Verify block hash
        const expectedHash = this.calculateBlockHash(currentEntry);
        if (currentEntry.blockHash !== expectedHash) {
          logger.warn('Block hash mismatch', {
            entryId: currentEntry.id,
            expectedHash,
            actualHash: currentEntry.blockHash
          });
          return false;
        }
      }

      return true;

    } catch (error) {
      logger.error('Chain integrity verification failed:', error);
      return false;
    }
  }

  /**
   * Calculate block hash for an entry
   */
  calculateBlockHash(entry: ProvenanceEntryData): string {
    const hashData = {
      previousHash: entry.previousHash,
      action: entry.action,
      entityType: entry.entityType,
      entityId: entry.entityId,
      beforeState: entry.beforeState,
      afterState: entry.afterState,
      changeMetadata: entry.changeMetadata,
      performedBy: entry.performedBy,
      timestamp: entry.timestamp.toISOString()
    };

    return cryptographicService.hashData(JSON.stringify(hashData, null, 0));
  }

  /**
   * Validate hash chain
   */
  validateHashChain(entries: ProvenanceEntryData[]): boolean {
    if (entries.length <= 1) return true;

    const sortedEntries = entries.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());

    for (let i = 1; i < sortedEntries.length; i++) {
      const current = sortedEntries[i];
      const previous = sortedEntries[i - 1];

      if (current.previousHash !== previous.blockHash) {
        return false;
      }
    }

    return true;
  }

  /**
   * Repair chain integrity
   */
  async repairChainIntegrity(entries: ProvenanceEntryData[]): Promise<ProvenanceEntryData[]> {
    try {
      const sortedEntries = entries.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
      const repairedEntries: ProvenanceEntryData[] = [];

      for (let i = 0; i < sortedEntries.length; i++) {
        const entry = { ...sortedEntries[i] };
        
        // Fix previous hash
        if (i > 0) {
          entry.previousHash = repairedEntries[i - 1].blockHash;
        }

        // Recalculate block hash
        entry.blockHash = this.calculateBlockHash(entry);
        entry.verificationHash = this.createImmutableRecord(entry);

        // Update in database
        await this.prisma.provenanceEntry.update({
          where: { id: entry.id },
          data: {
            blockHash: entry.blockHash,
            previousHash: entry.previousHash
          }
        });

        repairedEntries.push(entry);
      }

      logger.info('Repaired chain integrity', { entriesRepaired: repairedEntries.length });
      return repairedEntries;

    } catch (error) {
      logger.error('Failed to repair chain integrity:', error);
      throw new ChainIntegrityError('Chain repair failed', error);
    }
  }

  /**
   * ImmutabilityGuard implementation
   */
  enforceImmutability(entry: ProvenanceEntryData): boolean {
    return entry.isImmutable && this.verifyImmutableRecord(entry, entry.verificationHash);
  }

  detectTampering(entry: ProvenanceEntryData): boolean {
    const expectedHash = this.createImmutableRecord(entry);
    return entry.verificationHash !== expectedHash;
  }

  createImmutableRecord(data: any): string {
    const immutableData = {
      ...data,
      immutabilityTimestamp: new Date().toISOString(),
      immutabilityNonce: cryptographicService.generateNonce()
    };

    return cryptographicService.hashData(JSON.stringify(immutableData, null, 0));
  }

  verifyImmutableRecord(data: any, hash: string): boolean {
    try {
      const expectedHash = this.createImmutableRecord(data);
      return expectedHash === hash;
    } catch (error) {
      logger.error('Failed to verify immutable record:', error);
      return false;
    }
  }

  /**
   * Helper methods
   */
  private async getLastEntry(entityId: string): Promise<ProvenanceEntryData | null> {
    const entry = await this.prisma.provenanceEntry.findFirst({
      where: { entityId },
      orderBy: { timestamp: 'desc' },
      include: {
        user: true,
        signedEventPacket: true
      }
    });

    return entry ? this.convertToProvenanceData(entry) : null;
  }

  private validateAndProcessSnapshot(snapshot: EntitySnapshot): EntitySnapshot {
    if (!ProvenanceUtils.validateEntitySnapshot(snapshot)) {
      throw new ProvenanceError('Invalid entity snapshot', 'INVALID_SNAPSHOT');
    }

    // Generate checksum if not provided
    if (!snapshot.checksum && snapshot.content) {
      snapshot.checksum = cryptographicService.hashData(snapshot.content);
    }

    return snapshot;
  }

  private convertToProvenanceData(entry: any, verificationHash?: string): ProvenanceEntryData {
    return {
      id: entry.id,
      blockHash: entry.blockHash,
      previousHash: entry.previousHash,
      action: entry.action as ProvenanceAction,
      entityType: entry.entityType as EntityType,
      entityId: entry.entityId,
      beforeState: entry.beforeState ? JSON.parse(entry.beforeState) : null,
      afterState: entry.afterState ? JSON.parse(entry.afterState) : null,
      changeMetadata: JSON.parse(entry.changeMetadata),
      performedBy: entry.performedBy,
      performedByType: entry.user ? 'USER' : 'SYSTEM',
      timestamp: entry.timestamp,
      isImmutable: entry.isImmutable,
      verificationHash: verificationHash || this.createImmutableRecord(entry),
      linkedEventPacketId: entry.eventPacketId
    };
  }

  private verifyEntry(entry: ProvenanceEntryData, deepVerification = false): boolean {
    try {
      // Basic verification
      if (!entry.blockHash || !entry.timestamp || !entry.action) {
        return false;
      }

      // Verify block hash
      const expectedHash = this.calculateBlockHash(entry);
      if (entry.blockHash !== expectedHash) {
        return false;
      }

      // Deep verification
      if (deepVerification) {
        if (!this.enforceImmutability(entry)) {
          return false;
        }

        if (this.detectTampering(entry)) {
          return false;
        }
      }

      return true;

    } catch (error) {
      logger.error('Entry verification failed:', error);
      return false;
    }
  }

  private calculateIntegrityScore(entries: ProvenanceEntryData[]): number {
    if (entries.length === 0) return 100;

    let validEntries = 0;
    for (const entry of entries) {
      if (this.verifyEntry(entry)) {
        validEntries++;
      }
    }

    return (validEntries / entries.length) * 100;
  }

  private generateRecommendations(corruptedEntries: ProvenanceEntryData[], chainValid: boolean): string[] {
    const recommendations: string[] = [];

    if (corruptedEntries.length > 0) {
      recommendations.push(`Found ${corruptedEntries.length} corrupted entries that need attention`);
      recommendations.push('Consider running chain repair to fix integrity issues');
    }

    if (!chainValid) {
      recommendations.push('Hash chain is broken - immediate repair required');
      recommendations.push('Investigate potential security breach or data corruption');
    }

    if (recommendations.length === 0) {
      recommendations.push('Provenance chain is healthy and fully verified');
    }

    return recommendations;
  }

  private async performIntegrityCheck(): Promise<void> {
    try {
      const recentEntries = await this.queryProvenanceEntries({
        dateFrom: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
        limit: 1000
      });

      const isValid = await this.verifyChainIntegrity(recentEntries);
      if (!isValid) {
        logger.warn('Integrity check failed for recent entries');
      }

      this.lastVerificationTime = new Date();

    } catch (error) {
      logger.error('Integrity check failed:', error);
    }
  }

  private async getRecentActivityStats() {
    const now = new Date();
    const last24Hours = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const lastWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const lastMonth = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    const [day, week, month] = await Promise.all([
      this.prisma.provenanceEntry.count({ where: { timestamp: { gte: last24Hours } } }),
      this.prisma.provenanceEntry.count({ where: { timestamp: { gte: lastWeek } } }),
      this.prisma.provenanceEntry.count({ where: { timestamp: { gte: lastMonth } } })
    ]);

    return {
      last24Hours: day,
      lastWeek: week,
      lastMonth: month
    };
  }

  private async getActionStats(): Promise<Record<ProvenanceAction, number>> {
    const stats = await this.prisma.provenanceEntry.groupBy({
      by: ['action'],
      _count: { action: true }
    });

    const result: Record<ProvenanceAction, number> = {} as any;
    for (const action of Object.values(ProvenanceAction)) {
      result[action] = 0;
    }

    stats.forEach(stat => {
      result[stat.action as ProvenanceAction] = stat._count.action;
    });

    return result;
  }

  private async getEntityTypeStats(): Promise<Record<EntityType, number>> {
    const stats = await this.prisma.provenanceEntry.groupBy({
      by: ['entityType'],
      _count: { entityType: true }
    });

    const result: Record<EntityType, number> = {} as any;
    for (const entityType of Object.values(EntityType)) {
      result[entityType] = 0;
    }

    stats.forEach(stat => {
      result[stat.entityType as EntityType] = stat._count.entityType;
    });

    return result;
  }

  private async getPerformerStats(): Promise<Record<string, number>> {
    const stats = await this.prisma.provenanceEntry.groupBy({
      by: ['performedBy'],
      _count: { performedBy: true }
    });

    const result: Record<string, number> = {};
    stats.forEach(stat => {
      result[stat.performedBy] = stat._count.performedBy;
    });

    return result;
  }

  private async getIntegrityStatus() {
    const totalEntries = await this.prisma.provenanceEntry.count();
    
    // For now, assume all entries are valid unless we detect issues
    // In a real implementation, you'd run periodic integrity checks
    return {
      validEntries: totalEntries,
      invalidEntries: 0,
      corruptedChains: 0,
      lastVerification: this.lastVerificationTime
    };
  }

  private async getChainMetrics() {
    // Get unique entity IDs
    const entities = await this.prisma.provenanceEntry.groupBy({
      by: ['entityId'],
      _count: { entityId: true }
    });

    const chainLengths = entities.map(entity => entity._count.entityId);
    const longestChain = Math.max(...chainLengths, 0);
    const averageChainLength = chainLengths.length > 0 
      ? chainLengths.reduce((sum, length) => sum + length, 0) / chainLengths.length 
      : 0;

    return {
      longestChain,
      averageChainLength: Math.round(averageChainLength * 100) / 100,
      totalChains: entities.length
    };
  }
}

// Export singleton instance
export const provenanceLedgerService = (prisma: PrismaClient) => new ProvenanceLedgerService(prisma);

# Supabase Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Stripe Configuration
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_stripe_webhook_secret

# PostHog Analytics (Optional)
POSTHOG_KEY=phc_your_posthog_key
POSTHOG_HOST=https://app.posthog.com

# Application Configuration
SESSION_SECRET=your_super_secret_session_key_here
APP_URL=http://localhost:3000
NODE_ENV=development

# Database Configuration (if using external PostgreSQL)
DATABASE_URL=postgresql://username:password@localhost:5432/ecostamp

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password

# File Upload Configuration
MAX_FILE_SIZE=100MB
ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf,mp4,mp3,txt,doc,docx

# Rate Limiting
RATE_LIMIT_WINDOW=15m
RATE_LIMIT_MAX_REQUESTS=100

# Feature Flags
ENABLE_ANALYTICS=true
ENABLE_TEAM_FEATURES=true
ENABLE_API_ACCESS=true
ENABLE_WHITE_LABEL=false

# Security
CORS_ORIGIN=http://localhost:3000
CSRF_SECRET=your_csrf_secret_key

# Monitoring
SENTRY_DSN=your_sentry_dsn_here
LOG_LEVEL=info

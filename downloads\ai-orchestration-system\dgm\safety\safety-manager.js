/**
 * Safety Manager for DGM
 * 
 * Comprehensive safety system for the Darwin Gödel Machine:
 * - Human-in-the-loop oversight
 * - Automatic rollback mechanisms
 * - Safety validation and quarantine
 * - Risk assessment and mitigation
 */

const fs = require('fs').promises;
const path = require('path');
const chalk = require('chalk');
const { execSync } = require('child_process');

class SafetyManager {
  constructor(config, dgmEngine) {
    this.config = config;
    this.dgmEngine = dgmEngine;
    this.safetyConfig = config.get('safety', {});
    this.isEnabled = this.safetyConfig.enabled !== false;
    
    // Safety state
    this.quarantinedAgents = new Set();
    this.pendingApprovals = new Map();
    this.rollbackHistory = [];
    this.riskAssessments = new Map();
    
    // Safety thresholds
    this.approvalThreshold = this.safetyConfig.approvalThreshold || 0.8;
    this.maxConsecutiveFailures = this.safetyConfig.maxConsecutiveFailures || 3;
    this.consecutiveFailures = 0;
    
    // Backup management
    this.backupDir = path.join(process.cwd(), 'dgm-safety-backups');
    this.maxBackups = this.safetyConfig.maxBackups || 10;
    
    this.setupSafetyHooks();
  }

  /**
   * Initialize safety manager
   */
  async initialize() {
    if (!this.isEnabled) {
      console.log(chalk.yellow('⚠️  Safety Manager disabled in configuration'));
      return;
    }

    try {
      // Create backup directory
      await fs.mkdir(this.backupDir, { recursive: true });
      
      // Load existing safety data
      await this.loadSafetyData();
      
      // Create initial system backup
      await this.createSystemBackup('initialization');
      
      console.log(chalk.green('🛡️  Safety Manager initialized'));
      
    } catch (error) {
      throw new Error(`Safety Manager initialization failed: ${error.message}`);
    }
  }

  /**
   * Setup safety hooks with DGM engine
   */
  setupSafetyHooks() {
    if (!this.isEnabled) return;

    // Hook into agent creation
    this.dgmEngine.on('agentCreated', async (agent) => {
      await this.validateNewAgent(agent);
    });

    // Hook into generation completion
    this.dgmEngine.on('generationCompleted', async (generation) => {
      await this.assessGenerationSafety(generation);
    });

    // Hook into agent evaluation
    this.dgmEngine.on('agentEvaluated', async (agent) => {
      await this.checkAgentSafety(agent);
    });

    // Hook into evolution start
    this.dgmEngine.on('evolutionStarted', async () => {
      await this.createSystemBackup('evolution-start');
    });
  }

  /**
   * Validate a new agent for safety
   */
  async validateNewAgent(agent) {
    console.log(chalk.blue(`🔍 Validating agent ${agent.id} for safety...`));
    
    try {
      // Perform comprehensive validation
      const validationResults = await this.dgmEngine.validator.validateAgent(agent);
      
      // Assess risk level
      const riskAssessment = await this.assessAgentRisk(agent, validationResults);
      this.riskAssessments.set(agent.id, riskAssessment);
      
      // Check if human approval is required
      if (this.requiresHumanApproval(agent, riskAssessment)) {
        await this.requestHumanApproval(agent, riskAssessment);
      }
      
      // Quarantine if necessary
      if (riskAssessment.riskLevel === 'high') {
        await this.quarantineAgent(agent, 'High risk assessment');
      }
      
      console.log(chalk.green(`✅ Agent ${agent.id} safety validation completed`));
      
    } catch (error) {
      console.error(chalk.red(`❌ Agent safety validation failed: ${error.message}`));
      await this.quarantineAgent(agent, `Validation error: ${error.message}`);
    }
  }

  /**
   * Assess risk level of an agent
   */
  async assessAgentRisk(agent, validationResults) {
    const riskFactors = {
      validationScore: validationResults.overallScore,
      safetyScore: validationResults.safetyScore,
      fitnessChange: this.calculateFitnessChange(agent),
      codeComplexity: this.calculateCodeComplexity(agent.code),
      parentRisk: this.assessParentRisk(agent),
      novelty: this.calculateNovelty(agent)
    };

    // Calculate overall risk score
    const riskScore = this.calculateRiskScore(riskFactors);
    
    // Determine risk level
    let riskLevel;
    if (riskScore > 0.8) {
      riskLevel = 'high';
    } else if (riskScore > 0.5) {
      riskLevel = 'medium';
    } else {
      riskLevel = 'low';
    }

    return {
      agentId: agent.id,
      riskScore,
      riskLevel,
      riskFactors,
      timestamp: new Date(),
      recommendations: this.generateRiskRecommendations(riskFactors, riskLevel)
    };
  }

  /**
   * Calculate overall risk score from risk factors
   */
  calculateRiskScore(factors) {
    const weights = {
      validationScore: 0.3,
      safetyScore: 0.3,
      fitnessChange: 0.2,
      codeComplexity: 0.1,
      parentRisk: 0.05,
      novelty: 0.05
    };

    // Invert positive factors (higher validation/safety scores = lower risk)
    const riskScore = (
      weights.validationScore * (1 - factors.validationScore) +
      weights.safetyScore * (1 - factors.safetyScore) +
      weights.fitnessChange * Math.abs(factors.fitnessChange) +
      weights.codeComplexity * Math.min(1, factors.codeComplexity / 100) +
      weights.parentRisk * factors.parentRisk +
      weights.novelty * factors.novelty
    );

    return Math.max(0, Math.min(1, riskScore));
  }

  /**
   * Check if human approval is required
   */
  requiresHumanApproval(agent, riskAssessment) {
    if (!this.safetyConfig.humanApprovalRequired) {
      return false;
    }

    // Always require approval for high-risk agents
    if (riskAssessment.riskLevel === 'high') {
      return true;
    }

    // Require approval if fitness is above threshold
    if (agent.fitness >= this.approvalThreshold) {
      return true;
    }

    // Require approval for significant fitness improvements
    const fitnessChange = this.calculateFitnessChange(agent);
    if (fitnessChange > 0.2) {
      return true;
    }

    return false;
  }

  /**
   * Request human approval for an agent
   */
  async requestHumanApproval(agent, riskAssessment) {
    console.log(chalk.yellow(`👤 Human approval required for agent ${agent.id}`));
    
    const approvalRequest = {
      agentId: agent.id,
      agent,
      riskAssessment,
      requestTime: new Date(),
      status: 'pending'
    };

    this.pendingApprovals.set(agent.id, approvalRequest);
    
    // Emit event for UI notification
    this.dgmEngine.emit('approvalRequired', approvalRequest);
    
    // In a real implementation, this would integrate with notification systems
    console.log(chalk.blue(`📋 Approval request created for agent ${agent.id}`));
    console.log(chalk.gray(`   Risk Level: ${riskAssessment.riskLevel}`));
    console.log(chalk.gray(`   Risk Score: ${riskAssessment.riskScore.toFixed(3)}`));
    console.log(chalk.gray(`   Fitness: ${agent.fitness.toFixed(3)}`));
  }

  /**
   * Approve an agent
   */
  async approveAgent(agentId, approver = 'system') {
    const approvalRequest = this.pendingApprovals.get(agentId);
    
    if (!approvalRequest) {
      throw new Error(`No pending approval found for agent ${agentId}`);
    }

    approvalRequest.status = 'approved';
    approvalRequest.approver = approver;
    approvalRequest.approvalTime = new Date();

    // Remove from quarantine if quarantined
    this.quarantinedAgents.delete(agentId);

    console.log(chalk.green(`✅ Agent ${agentId} approved by ${approver}`));
    
    // Emit approval event
    this.dgmEngine.emit('agentApproved', { agentId, approver });
    
    return approvalRequest;
  }

  /**
   * Reject an agent
   */
  async rejectAgent(agentId, reason = 'Manual rejection', rejector = 'system') {
    const approvalRequest = this.pendingApprovals.get(agentId);
    
    if (approvalRequest) {
      approvalRequest.status = 'rejected';
      approvalRequest.rejector = rejector;
      approvalRequest.rejectionReason = reason;
      approvalRequest.rejectionTime = new Date();
    }

    // Quarantine the agent
    await this.quarantineAgent({ id: agentId }, reason);

    console.log(chalk.red(`❌ Agent ${agentId} rejected by ${rejector}: ${reason}`));
    
    // Emit rejection event
    this.dgmEngine.emit('agentRejected', { agentId, reason, rejector });
  }

  /**
   * Quarantine an agent
   */
  async quarantineAgent(agent, reason) {
    this.quarantinedAgents.add(agent.id);
    
    console.log(chalk.yellow(`🔒 Agent ${agent.id} quarantined: ${reason}`));
    
    // Log quarantine event
    const quarantineEvent = {
      agentId: agent.id,
      reason,
      timestamp: new Date(),
      riskAssessment: this.riskAssessments.get(agent.id)
    };

    // Emit quarantine event
    this.dgmEngine.emit('agentQuarantined', quarantineEvent);
  }

  /**
   * Check agent safety after evaluation
   */
  async checkAgentSafety(agent) {
    // Check for performance degradation
    if (agent.fitness < 0.1) {
      this.consecutiveFailures++;
      
      if (this.consecutiveFailures >= this.maxConsecutiveFailures) {
        await this.triggerEmergencyRollback('Consecutive low-performance agents');
      }
    } else {
      this.consecutiveFailures = 0;
    }

    // Check validation results
    if (agent.validationResults && !agent.validationResults.isValid) {
      await this.quarantineAgent(agent, 'Failed validation');
    }
  }

  /**
   * Assess generation safety
   */
  async assessGenerationSafety(generation) {
    // Check for population collapse
    if (generation.survivors < 3) {
      console.log(chalk.red('⚠️  Population collapse detected!'));
      await this.triggerEmergencyRollback('Population collapse');
      return;
    }

    // Check for fitness degradation
    if (generation.bestFitness < 0.2) {
      console.log(chalk.yellow('⚠️  Fitness degradation detected'));
      await this.createSystemBackup(`generation-${generation.number}-degradation`);
    }

    // Check for stagnation
    const recentGenerations = this.dgmEngine.evolutionHistory.slice(-5);
    if (recentGenerations.length >= 5) {
      const fitnessVariance = this.calculateFitnessVariance(recentGenerations);
      
      if (fitnessVariance < 0.01) {
        console.log(chalk.yellow('⚠️  Evolution stagnation detected'));
        // Could trigger diversity injection or other interventions
      }
    }
  }

  /**
   * Trigger emergency rollback
   */
  async triggerEmergencyRollback(reason) {
    console.log(chalk.red.bold(`🚨 EMERGENCY ROLLBACK TRIGGERED: ${reason}`));
    
    try {
      // Stop evolution immediately
      this.dgmEngine.isRunning = false;
      
      // Create emergency backup
      await this.createSystemBackup(`emergency-${Date.now()}`);
      
      // Find last stable backup
      const stableBackup = await this.findLastStableBackup();
      
      if (stableBackup) {
        await this.restoreFromBackup(stableBackup);
        console.log(chalk.green(`✅ System restored from backup: ${stableBackup}`));
      } else {
        console.log(chalk.red('❌ No stable backup found for rollback'));
      }
      
      // Log rollback event
      const rollbackEvent = {
        reason,
        timestamp: new Date(),
        backup: stableBackup,
        generation: this.dgmEngine.currentGeneration
      };
      
      this.rollbackHistory.push(rollbackEvent);
      
      // Emit rollback event
      this.dgmEngine.emit('emergencyRollback', rollbackEvent);
      
    } catch (error) {
      console.error(chalk.red(`❌ Emergency rollback failed: ${error.message}`));
      throw error;
    }
  }

  /**
   * Create system backup
   */
  async createSystemBackup(label = 'manual') {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupName = `${timestamp}-${label}`;
    const backupPath = path.join(this.backupDir, backupName);
    
    try {
      await fs.mkdir(backupPath, { recursive: true });
      
      // Backup DGM state
      const dgmState = {
        currentGeneration: this.dgmEngine.currentGeneration,
        evolutionHistory: this.dgmEngine.evolutionHistory,
        population: await this.dgmEngine.agentManager.getCurrentPopulation(),
        config: this.config.getAll(),
        safetyData: {
          quarantinedAgents: Array.from(this.quarantinedAgents),
          pendingApprovals: Object.fromEntries(this.pendingApprovals),
          riskAssessments: Object.fromEntries(this.riskAssessments),
          rollbackHistory: this.rollbackHistory
        }
      };
      
      await fs.writeFile(
        path.join(backupPath, 'dgm-state.json'),
        JSON.stringify(dgmState, null, 2)
      );
      
      // Backup archive
      const archiveStats = await this.dgmEngine.archive.getArchiveStats();
      await fs.writeFile(
        path.join(backupPath, 'archive-stats.json'),
        JSON.stringify(archiveStats, null, 2)
      );
      
      // Cleanup old backups
      await this.cleanupOldBackups();
      
      console.log(chalk.blue(`💾 System backup created: ${backupName}`));
      return backupName;
      
    } catch (error) {
      console.error(chalk.red(`❌ Backup creation failed: ${error.message}`));
      throw error;
    }
  }

  /**
   * Restore from backup
   */
  async restoreFromBackup(backupName) {
    const backupPath = path.join(this.backupDir, backupName);
    
    try {
      // Load DGM state
      const stateFile = path.join(backupPath, 'dgm-state.json');
      const stateData = JSON.parse(await fs.readFile(stateFile, 'utf8'));
      
      // Restore DGM engine state
      this.dgmEngine.currentGeneration = stateData.currentGeneration;
      this.dgmEngine.evolutionHistory = stateData.evolutionHistory;
      
      // Restore population
      await this.dgmEngine.agentManager.updatePopulation(stateData.population);
      
      // Restore safety data
      if (stateData.safetyData) {
        this.quarantinedAgents = new Set(stateData.safetyData.quarantinedAgents);
        this.pendingApprovals = new Map(Object.entries(stateData.safetyData.pendingApprovals));
        this.riskAssessments = new Map(Object.entries(stateData.safetyData.riskAssessments));
        this.rollbackHistory = stateData.safetyData.rollbackHistory;
      }
      
      console.log(chalk.green(`✅ System restored from backup: ${backupName}`));
      
    } catch (error) {
      console.error(chalk.red(`❌ Restore from backup failed: ${error.message}`));
      throw error;
    }
  }

  /**
   * Find last stable backup
   */
  async findLastStableBackup() {
    try {
      const backups = await fs.readdir(this.backupDir);
      const stableBackups = backups.filter(backup => 
        !backup.includes('emergency') && 
        !backup.includes('degradation')
      );
      
      // Sort by timestamp (newest first)
      stableBackups.sort().reverse();
      
      return stableBackups[0] || null;
      
    } catch (error) {
      console.error(chalk.red(`❌ Failed to find stable backup: ${error.message}`));
      return null;
    }
  }

  /**
   * Cleanup old backups
   */
  async cleanupOldBackups() {
    try {
      const backups = await fs.readdir(this.backupDir);
      
      if (backups.length > this.maxBackups) {
        // Sort by timestamp and remove oldest
        const sortedBackups = backups.sort();
        const toDelete = sortedBackups.slice(0, backups.length - this.maxBackups);
        
        for (const backup of toDelete) {
          const backupPath = path.join(this.backupDir, backup);
          await fs.rmdir(backupPath, { recursive: true });
        }
        
        console.log(chalk.gray(`🗑️  Cleaned up ${toDelete.length} old backups`));
      }
      
    } catch (error) {
      console.warn(chalk.yellow(`⚠️  Backup cleanup failed: ${error.message}`));
    }
  }

  /**
   * Helper methods for risk assessment
   */
  calculateFitnessChange(agent) {
    if (!agent.parentIds || agent.parentIds.length === 0) {
      return 0; // No parents, no change
    }

    // This would calculate fitness change from parents
    // For now, return a placeholder
    return Math.random() * 0.4 - 0.2; // -0.2 to +0.2
  }

  calculateCodeComplexity(code) {
    const codeStr = JSON.stringify(code);
    const complexityPatterns = [
      /if\s*\(/g,
      /for\s*\(/g,
      /while\s*\(/g,
      /function\s+/g,
      /class\s+/g
    ];

    return complexityPatterns.reduce((complexity, pattern) => {
      return complexity + (codeStr.match(pattern) || []).length;
    }, 0);
  }

  assessParentRisk(agent) {
    if (!agent.parentIds || agent.parentIds.length === 0) {
      return 0;
    }

    // Calculate average risk of parents
    let totalRisk = 0;
    let riskCount = 0;

    for (const parentId of agent.parentIds) {
      const parentRisk = this.riskAssessments.get(parentId);
      if (parentRisk) {
        totalRisk += parentRisk.riskScore;
        riskCount++;
      }
    }

    return riskCount > 0 ? totalRisk / riskCount : 0;
  }

  calculateNovelty(agent) {
    // Simplified novelty calculation
    return Math.min(1.0, agent.generation / 20);
  }

  calculateFitnessVariance(generations) {
    const fitnessValues = generations.map(gen => gen.bestFitness);
    const mean = fitnessValues.reduce((sum, f) => sum + f, 0) / fitnessValues.length;
    const variance = fitnessValues.reduce((sum, f) => sum + Math.pow(f - mean, 2), 0) / fitnessValues.length;
    return variance;
  }

  generateRiskRecommendations(factors, riskLevel) {
    const recommendations = [];

    if (factors.validationScore < 0.7) {
      recommendations.push('Improve code validation and testing');
    }

    if (factors.safetyScore < 0.9) {
      recommendations.push('Enhance safety measures and error handling');
    }

    if (factors.codeComplexity > 50) {
      recommendations.push('Reduce code complexity and improve modularity');
    }

    if (riskLevel === 'high') {
      recommendations.push('Require human review before deployment');
      recommendations.push('Consider additional testing and validation');
    }

    return recommendations;
  }

  /**
   * Load existing safety data
   */
  async loadSafetyData() {
    const safetyDataPath = path.join(this.backupDir, 'safety-data.json');
    
    try {
      const data = await fs.readFile(safetyDataPath, 'utf8');
      const safetyData = JSON.parse(data);
      
      this.quarantinedAgents = new Set(safetyData.quarantinedAgents || []);
      this.pendingApprovals = new Map(Object.entries(safetyData.pendingApprovals || {}));
      this.riskAssessments = new Map(Object.entries(safetyData.riskAssessments || {}));
      this.rollbackHistory = safetyData.rollbackHistory || [];
      
    } catch (error) {
      // No existing safety data, start fresh
      console.log(chalk.gray('No existing safety data found, starting fresh'));
    }
  }

  /**
   * Save safety data
   */
  async saveSafetyData() {
    const safetyDataPath = path.join(this.backupDir, 'safety-data.json');
    
    const safetyData = {
      quarantinedAgents: Array.from(this.quarantinedAgents),
      pendingApprovals: Object.fromEntries(this.pendingApprovals),
      riskAssessments: Object.fromEntries(this.riskAssessments),
      rollbackHistory: this.rollbackHistory,
      lastUpdated: new Date()
    };
    
    await fs.writeFile(safetyDataPath, JSON.stringify(safetyData, null, 2));
  }

  /**
   * Get safety statistics
   */
  getSafetyStats() {
    return {
      isEnabled: this.isEnabled,
      quarantinedAgents: this.quarantinedAgents.size,
      pendingApprovals: this.pendingApprovals.size,
      riskAssessments: this.riskAssessments.size,
      rollbackHistory: this.rollbackHistory.length,
      consecutiveFailures: this.consecutiveFailures,
      maxConsecutiveFailures: this.maxConsecutiveFailures,
      approvalThreshold: this.approvalThreshold
    };
  }
}

module.exports = SafetyManager;

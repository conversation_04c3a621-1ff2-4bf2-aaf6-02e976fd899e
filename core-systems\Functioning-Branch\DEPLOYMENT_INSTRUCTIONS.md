# Deployment Instructions - Functioning Branch

Complete deployment guide for all systems in the Functioning Branch, ready for production deployment and scaling to serve millions of users.

## 🚀 **Production Deployment Overview**

### **Systems to Deploy:**

1. **EcoStamp Suite Website** - Main business platform
2. **Universal Meta-Orchestration System** - Backend orchestration
3. **AI Assistant Orchestration** - Branded assistant coordination
4. **Feedback Loop Framework** - Quality assurance system
5. **Thread-Merging Orchestrator** - Multi-platform coordination

## 🔧 **Infrastructure Requirements**

### **Minimum Production Requirements:**

- **CPU**: 4 cores per service
- **RAM**: 8GB per service
- **Storage**: 100GB SSD per service
- **Network**: 1Gbps bandwidth
- **Database**: PostgreSQL 14+ with 16GB RAM
- **Cache**: Redis 6+ with 4GB RAM

### **Recommended Production Setup:**

- **CPU**: 8 cores per service
- **RAM**: 16GB per service
- **Storage**: 500GB NVMe SSD per service
- **Network**: 10Gbps bandwidth
- **Database**: PostgreSQL 15+ with 32GB RAM
- **Cache**: Redis 7+ with 8GB RAM

## 🌐 **Domain Configuration**

### **Primary Domains:**

- **Main Website**: `ecostamp-suite.com`
- **App Platform**: `app.ecostamp-suite.com`
- **API Endpoints**: `api.ecostamp-suite.com`
- **Documentation**: `docs.ecostamp-suite.com`
- **Status Page**: `status.ecostamp-suite.com`

### **SSL/TLS Setup:**

```bash
# Use Let's Encrypt for free SSL certificates
certbot --nginx -d ecostamp-suite.com -d app.ecostamp-suite.com -d api.ecostamp-suite.com
```

## 📦 **1. EcoStamp Suite Website Deployment**

### **Vercel Deployment (Recommended):**

1. **Connect Repository:**

   ```bash
   cd Functioning-Branch/ecostamp-suite-website
   vercel --prod
   ```

2. **Environment Variables:**

   ```env
   # Supabase
   SUPABASE_URL=https://your-project.supabase.co
   SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

   # Stripe
   STRIPE_PUBLISHABLE_KEY=pk_live_your_stripe_publishable_key
   STRIPE_SECRET_KEY=sk_live_your_stripe_secret_key
   STRIPE_WEBHOOK_SECRET=whsec_your_stripe_webhook_secret

   # PostHog
   POSTHOG_KEY=phc_your_posthog_key
   POSTHOG_HOST=https://app.posthog.com

   # App
   SESSION_SECRET=your_super_secret_session_key
   APP_URL=https://ecostamp-suite.com
   NODE_ENV=production
   ```

3. **Custom Domain:**
   - Add `ecostamp-suite.com` in Vercel dashboard
   - Configure DNS records
   - Enable SSL

### **Alternative: Docker Deployment:**

1. **Create Dockerfile:**

   ```dockerfile
   FROM node:18-alpine
   WORKDIR /app
   COPY package*.json ./
   RUN npm ci --only=production
   COPY . .
   RUN npm run build
   EXPOSE 3000
   CMD ["npm", "start"]
   ```

2. **Deploy with Docker:**

   ```bash
   docker build -t ecostamp-suite .
   docker run -p 3000:3000 --env-file .env ecostamp-suite
   ```

## 🤖 **2. Universal Meta-Orchestration System Deployment**

### **AWS/GCP/Azure Deployment:**

1. **Create Virtual Machine:**

   ```bash
   # AWS EC2 instance (recommended: t3.large or larger)
   # GCP Compute Engine (recommended: n2-standard-4 or larger)
   # Azure Virtual Machine (recommended: Standard_D4s_v3 or larger)
   ```

2. **Install Dependencies:**

   ```bash
   sudo apt update
   sudo apt install python3.9 python3-pip nginx redis-server postgresql-client
   pip3 install -r requirements.txt
   ```

3. **Deploy Application:**

   ```bash
   cd Functioning-Branch/universal-meta-orchestration
   pip install -e .

   # Create systemd service
   sudo cp deploy/meta-orchestration.service /etc/systemd/system/
   sudo systemctl enable meta-orchestration
   sudo systemctl start meta-orchestration
   ```

4. **Configure Nginx:**

   ```nginx
   server {
       listen 80;
       server_name api.ecostamp-suite.com;

       location / {
           proxy_pass http://localhost:8000;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
       }
   }
   ```

### **Docker Deployment:**

1. **Create docker-compose.yml:**

   ```yaml
   version: '3.8'
   services:
     meta-orchestration:
       build: ./universal-meta-orchestration
       ports:
         - "8000:8000"
       environment:
         - DATABASE_URL=******************************/meta_orchestration
         - REDIS_URL=redis://redis:6379
       depends_on:
         - db
         - redis

     db:
       image: postgres:15
       environment:
         POSTGRES_DB: meta_orchestration
         POSTGRES_USER: user
         POSTGRES_PASSWORD: pass
       volumes:
         - postgres_data:/var/lib/postgresql/data

     redis:
       image: redis:7-alpine

   volumes:
     postgres_data:
   ```

2. **Deploy:**

   ```bash
   docker-compose up -d
   ```

## 🎯 **3. AI Assistant Orchestration Deployment**

### **Kubernetes Deployment:**

1. **Create Kubernetes manifests:**

   ```yaml
   apiVersion: apps/v1
   kind: Deployment
   metadata:
     name: ai-orchestration
   spec:
     replicas: 3
     selector:
       matchLabels:
         app: ai-orchestration
     template:
       metadata:
         labels:
           app: ai-orchestration
       spec:
         containers:
         - name: ai-orchestration
           image: ecostamp/ai-orchestration:latest
           ports:
           - containerPort: 8001
           env:
           - name: DATABASE_URL
             valueFrom:
               secretKeyRef:
                 name: db-secret
                 key: url
   ```

2. **Deploy to Kubernetes:**

   ```bash
   kubectl apply -f k8s/
   kubectl expose deployment ai-orchestration --type=LoadBalancer --port=80 --target-port=8001
   ```

## 🔄 **4. Feedback Loop Framework Deployment**

### **Serverless Deployment (AWS Lambda):**

1. **Create serverless.yml:**

   ```yaml
   service: feedback-loop-framework

   provider:
     name: aws
     runtime: python3.9
     region: us-east-1

   functions:
     process_feedback:
       handler: handler.process_feedback
       events:
         - http:
             path: /feedback
             method: post
   ```

2. **Deploy:**

   ```bash
   cd Functioning-Branch/feedback-loop-framework
   serverless deploy
   ```

## 🧵 **5. Thread-Merging Orchestrator Deployment**

### **Node.js PM2 Deployment:**

1. **Install PM2:**

   ```bash
   npm install -g pm2
   ```

2. **Create ecosystem.config.js:**

   ```javascript
   module.exports = {
     apps: [{
       name: 'thread-merging',
       script: './src/index.js',
       instances: 'max',
       exec_mode: 'cluster',
       env: {
         NODE_ENV: 'production',
         PORT: 8002
       }
     }]
   };
   ```

3. **Deploy:**

   ```bash
   cd Functioning-Branch/thread-merging-orchestrator
   npm install
   pm2 start ecosystem.config.js
   pm2 save
   pm2 startup
   ```

## 🗄️ **Database Setup**

### **Supabase Production Setup:**

1. **Create Production Project:**
   - Go to Supabase dashboard
   - Create new project for production
   - Note connection details

2. **Run Migrations:**

   ```sql
   -- User profiles table
   CREATE TABLE profiles (
     id UUID REFERENCES auth.users ON DELETE CASCADE,
     email TEXT UNIQUE NOT NULL,
     name TEXT,
     plan TEXT DEFAULT 'free',
     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     PRIMARY KEY (id)
   );

   -- Enable RLS
   ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

   -- Create policies
   CREATE POLICY "Users can view own profile" ON profiles
     FOR SELECT USING (auth.uid() = id);
   ```

3. **Set up Storage:**

   ```sql
   -- Create storage bucket for uploads
   INSERT INTO storage.buckets (id, name, public) VALUES ('uploads', 'uploads', false);

   -- Create storage policies
   CREATE POLICY "Users can upload files" ON storage.objects
     FOR INSERT WITH CHECK (bucket_id = 'uploads' AND auth.uid()::text = (storage.foldername(name))[1]);
   ```

## 💳 **Payment System Setup**

### **Stripe Configuration:**

1. **Create Products:**

   ```bash
   # Create Stripe products for each tier
   stripe products create --name="EcoStamp Suite Pro" --description="Professional plan"
   stripe prices create --product=prod_xxx --unit-amount=2900 --currency=usd --recurring[interval]=month
   ```

2. **Set up Webhooks:**

   ```bash
   # Configure webhook endpoint
   stripe listen --forward-to localhost:3000/api/webhooks/stripe
   ```

## 📊 **Monitoring Setup**

### **Application Monitoring:**

1. **Install monitoring tools:**

   ```bash
   # Prometheus + Grafana
   docker run -d -p 9090:9090 prom/prometheus
   docker run -d -p 3001:3000 grafana/grafana
   ```

2. **Configure alerts:**

   ```yaml
   # alertmanager.yml
   global:
     smtp_smarthost: 'localhost:587'
     smtp_from: '<EMAIL>'

   route:
     group_by: ['alertname']
     receiver: 'web.hook'

   receivers:
   - name: 'web.hook'
     email_configs:
     - to: '<EMAIL>'
       subject: 'EcoStamp Suite Alert'
   ```

## 🔒 **Security Configuration**

### **Firewall Setup:**

```bash
# UFW firewall rules
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw enable
```

### **SSL/TLS Configuration:**

```nginx
# Nginx SSL configuration
ssl_protocols TLSv1.2 TLSv1.3;
ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
ssl_prefer_server_ciphers off;
add_header Strict-Transport-Security "max-age=63072000" always;
```

## 📈 **Scaling Configuration**

### **Auto-scaling Setup:**

1. **Horizontal Pod Autoscaler (Kubernetes):**

   ```yaml
   apiVersion: autoscaling/v2
   kind: HorizontalPodAutoscaler
   metadata:
     name: ai-orchestration-hpa
   spec:
     scaleTargetRef:
       apiVersion: apps/v1
       kind: Deployment
       name: ai-orchestration
     minReplicas: 3
     maxReplicas: 50
     metrics:
     - type: Resource
       resource:
         name: cpu
         target:
           type: Utilization
           averageUtilization: 70
   ```

2. **Database Scaling:**

   ```bash
   # Supabase automatically scales
   # For self-hosted PostgreSQL, consider read replicas
   ```

## 🚀 **Go-Live Checklist**

### **Pre-Launch:**

- [ ] All environment variables configured
- [ ] SSL certificates installed
- [ ] Database migrations completed
- [ ] Payment system tested
- [ ] Monitoring configured
- [ ] Backup systems in place
- [ ] Load testing completed
- [ ] Security audit passed

### **Launch:**

- [ ] DNS records updated
- [ ] CDN configured
- [ ] Analytics tracking verified
- [ ] Error monitoring active
- [ ] Support system ready
- [ ] Documentation published

### **Post-Launch:**

- [ ] Performance monitoring
- [ ] User feedback collection
- [ ] System optimization
- [ ] Scaling as needed
- [ ] Regular security updates

---

**🎉 All systems ready for production deployment!**

**Capable of serving millions of users with enterprise-grade reliability and performance.**

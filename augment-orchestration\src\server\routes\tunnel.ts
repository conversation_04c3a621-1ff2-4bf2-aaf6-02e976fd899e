import { Router } from 'express';
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';
import { asyncHandler, AppError } from '../middleware/errorHandler';
import { AuthenticatedRequest } from '../middleware/auth';
import { EventBus, EVENT_TYPES } from '../services/EventBus';
import { logger } from '../utils/logger';

const router = Router();
const prisma = new PrismaClient();
const eventBus = new EventBus();

// Validation schemas
const createTunnelSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long'),
  description: z.string().optional(),
  fromAgentId: z.string().min(1, 'From agent is required'),
  toAgentId: z.string().min(1, 'To agent is required'),
  tunnelType: z.enum(['BIDIRECTIONAL', 'UNIDIRECTIONAL']).default('BIDIRECTIONAL'),
  tags: z.array(z.string()).default([]),
  metadata: z.record(z.any()).optional(),
  isActive: z.boolean().default(true),
});

const updateTunnelSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long').optional(),
  description: z.string().optional(),
  tunnelType: z.enum(['BIDIRECTIONAL', 'UNIDIRECTIONAL']).optional(),
  tags: z.array(z.string()).optional(),
  metadata: z.record(z.any()).optional(),
  isActive: z.boolean().optional(),
});

const sendDataSchema = z.object({
  data: z.record(z.any()),
  messageType: z.string().default('DATA'),
  priority: z.number().min(1).max(10).default(5),
  metadata: z.record(z.any()).optional(),
});

// Get all tunnels
router.get('/', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 20;
  const search = req.query.search as string;
  const tunnelType = req.query.tunnelType as string;
  const isActive = req.query.isActive === 'true' ? true : req.query.isActive === 'false' ? false : undefined;
  const agentId = req.query.agentId as string;

  const skip = (page - 1) * limit;
  const where: any = {};

  if (search) {
    where.OR = [
      { name: { contains: search, mode: 'insensitive' } },
      { description: { contains: search, mode: 'insensitive' } },
    ];
  }

  if (tunnelType) {
    where.tunnelType = tunnelType;
  }

  if (isActive !== undefined) {
    where.isActive = isActive;
  }

  if (agentId) {
    where.OR = [
      { fromAgentId: agentId },
      { toAgentId: agentId },
    ];
  }

  const [tunnels, total] = await Promise.all([
    prisma.tunnel.findMany({
      where,
      skip,
      take: limit,
      include: {
        fromAgent: {
          select: {
            id: true,
            agentId: true,
            name: true,
            vendor: true,
          },
        },
        toAgent: {
          select: {
            id: true,
            agentId: true,
            name: true,
            vendor: true,
          },
        },
        _count: {
          select: {
            dataFlows: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    }),
    prisma.tunnel.count({ where }),
  ]);

  res.json({
    success: true,
    data: tunnels,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
    },
  });
}));

// Create new tunnel
router.post('/', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const validatedData = createTunnelSchema.parse(req.body);

  // Validate agents exist
  const [fromAgent, toAgent] = await Promise.all([
    prisma.agent.findUnique({ where: { id: validatedData.fromAgentId } }),
    prisma.agent.findUnique({ where: { id: validatedData.toAgentId } }),
  ]);

  if (!fromAgent) {
    throw new AppError('From agent not found', 404);
  }

  if (!toAgent) {
    throw new AppError('To agent not found', 404);
  }

  if (validatedData.fromAgentId === validatedData.toAgentId) {
    throw new AppError('Cannot create tunnel to the same agent', 400);
  }

  // Check if tunnel already exists
  const existingTunnel = await prisma.tunnel.findFirst({
    where: {
      OR: [
        {
          fromAgentId: validatedData.fromAgentId,
          toAgentId: validatedData.toAgentId,
        },
        {
          fromAgentId: validatedData.toAgentId,
          toAgentId: validatedData.fromAgentId,
        },
      ],
    },
  });

  if (existingTunnel) {
    throw new AppError('Tunnel already exists between these agents', 400);
  }

  const tunnel = await prisma.tunnel.create({
    data: {
      ...validatedData,
      createdBy: req.user!.id,
    },
    include: {
      fromAgent: {
        select: {
          id: true,
          agentId: true,
          name: true,
          vendor: true,
        },
      },
      toAgent: {
        select: {
          id: true,
          agentId: true,
          name: true,
          vendor: true,
        },
      },
    },
  });

  // Emit event
  eventBus.emit(EVENT_TYPES.TUNNEL_CREATED, {
    tunnelId: tunnel.id,
    userId: req.user!.id,
    data: tunnel,
  });

  logger.info(`Tunnel created: ${tunnel.name}`, {
    tunnelId: tunnel.id,
    fromAgent: fromAgent.name,
    toAgent: toAgent.name,
    userId: req.user!.id,
  });

  res.status(201).json({
    success: true,
    data: tunnel,
  });
}));

// Update tunnel
router.put('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;
  const validatedData = updateTunnelSchema.parse(req.body);

  const existingTunnel = await prisma.tunnel.findUnique({
    where: { id },
  });

  if (!existingTunnel) {
    throw new AppError('Tunnel not found', 404);
  }

  const tunnel = await prisma.tunnel.update({
    where: { id },
    data: validatedData,
    include: {
      fromAgent: {
        select: {
          id: true,
          agentId: true,
          name: true,
          vendor: true,
        },
      },
      toAgent: {
        select: {
          id: true,
          agentId: true,
          name: true,
          vendor: true,
        },
      },
    },
  });

  // Emit event
  eventBus.emit(EVENT_TYPES.TUNNEL_UPDATED, {
    tunnelId: tunnel.id,
    userId: req.user!.id,
    data: tunnel,
  });

  logger.info(`Tunnel updated: ${tunnel.name}`, {
    tunnelId: tunnel.id,
    userId: req.user!.id,
  });

  res.json({
    success: true,
    data: tunnel,
  });
}));

// Delete tunnel
router.delete('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;

  const existingTunnel = await prisma.tunnel.findUnique({
    where: { id },
    include: {
      fromAgent: { select: { name: true } },
      toAgent: { select: { name: true } },
    },
  });

  if (!existingTunnel) {
    throw new AppError('Tunnel not found', 404);
  }

  await prisma.tunnel.delete({
    where: { id },
  });

  // Emit event
  eventBus.emit(EVENT_TYPES.TUNNEL_DELETED, {
    tunnelId: id,
    userId: req.user!.id,
    data: existingTunnel,
  });

  logger.info(`Tunnel deleted: ${existingTunnel.name}`, {
    tunnelId: id,
    fromAgent: existingTunnel.fromAgent.name,
    toAgent: existingTunnel.toAgent.name,
    userId: req.user!.id,
  });

  res.json({
    success: true,
    message: 'Tunnel deleted successfully',
  });
}));

// Send data through tunnel
router.post('/:id/send', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;
  const validatedData = sendDataSchema.parse(req.body);

  const tunnel = await prisma.tunnel.findUnique({
    where: { id },
    include: {
      fromAgent: true,
      toAgent: true,
    },
  });

  if (!tunnel) {
    throw new AppError('Tunnel not found', 404);
  }

  if (!tunnel.isActive) {
    throw new AppError('Tunnel is not active', 400);
  }

  // Create data flow record
  const dataFlow = await prisma.dataFlow.create({
    data: {
      tunnelId: id,
      direction: 'OUTBOUND',
      data: validatedData.data,
      messageType: validatedData.messageType,
      priority: validatedData.priority,
      metadata: validatedData.metadata,
    },
  });

  // Emit real-time event
  eventBus.emit(EVENT_TYPES.TUNNEL_DATA_SENT, {
    tunnelId: id,
    dataFlowId: dataFlow.id,
    userId: req.user!.id,
    data: {
      tunnel,
      dataFlow,
    },
  });

  logger.info(`Data sent through tunnel: ${tunnel.name}`, {
    tunnelId: id,
    dataFlowId: dataFlow.id,
    messageType: validatedData.messageType,
    userId: req.user!.id,
  });

  res.json({
    success: true,
    data: {
      dataFlowId: dataFlow.id,
      tunnel: {
        id: tunnel.id,
        name: tunnel.name,
        fromAgent: tunnel.fromAgent.name,
        toAgent: tunnel.toAgent.name,
      },
    },
  });
}));

export { router as tunnelRoutes };

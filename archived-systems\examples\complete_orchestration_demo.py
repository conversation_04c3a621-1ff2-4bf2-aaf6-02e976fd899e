#!/usr/bin/env python3
"""
Complete AI Assistant Orchestration System Demo

Demonstrates the full capabilities of the branded AI coding assistant
orchestration system including:
- Legal compliance validation
- Dynamic agent assignment and role management
- Combinatorial optimization testing
- Shared context management
- Workflow execution with fallback logic
- Performance monitoring and learning
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, Any

# Import core orchestration components
from ai_orchestration_system.core.orchestration_engine import OrchestrationEngine
from ai_orchestration_system.core.legal_compliance import ComplianceManager
from ai_orchestration_system.core.agent_registry import AgentRegistry, AgentRole
from ai_orchestration_system.core.combinatorial_matrix import CombinatorialMatrix, OptimizationGoal
from ai_orchestration_system.core.shared_context import SharedContext
from ai_orchestration_system.core.workflow_executor import WorkflowExecutor, TaskPriority


def setup_logging():
    """Setup comprehensive logging for the demo."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('orchestration_demo.log')
        ]
    )


def create_test_user_credentials() -> Dict[str, Dict[str, Any]]:
    """Create test user credentials for branded AI assistants."""
    return {
        'github_copilot': {
            'api_key': 'ghp_test_key_1234567890abcdef',
            'user_id': 'test_user_copilot'
        },
        'tabnine': {
            'api_key': 'tabnine_test_key_abcdef123456',
            'user_token': 'tabnine_user_token_789'
        },
        'amazon_q': {
            'access_key_id': 'AKIATEST1234567890AB',
            'secret_access_key': 'test_secret_key_abcdef123456789',
            'region': 'us-east-1'
        },
        'cursor': {
            'api_key': 'cursor_test_key_xyz789',
            'user_id': 'test_user_cursor'
        },
        'qodo_ai': {
            'api_key': 'qodo_test_key_456def',
            'organization_id': 'test_org_qodo'
        }
    }


def create_test_tasks() -> list[Dict[str, Any]]:
    """Create diverse test tasks for orchestration testing."""
    return [
        {
            'description': 'Implement OAuth2 authentication system',
            'task_type': 'code_generation',
            'priority': TaskPriority.HIGH,
            'context': {
                'language': 'python',
                'framework': 'fastapi',
                'requirements': ['secure', 'scalable', 'well-documented']
            }
        },
        {
            'description': 'Review and optimize database query performance',
            'task_type': 'code_review',
            'priority': TaskPriority.MEDIUM,
            'context': {
                'language': 'sql',
                'database': 'postgresql',
                'performance_target': '< 100ms'
            }
        },
        {
            'description': 'Generate comprehensive unit tests for user service',
            'task_type': 'testing',
            'priority': TaskPriority.HIGH,
            'context': {
                'language': 'python',
                'framework': 'pytest',
                'coverage_target': 0.9
            }
        },
        {
            'description': 'Create API documentation for REST endpoints',
            'task_type': 'documentation',
            'priority': TaskPriority.MEDIUM,
            'context': {
                'format': 'openapi',
                'include_examples': True,
                'target_audience': 'developers'
            }
        },
        {
            'description': 'Refactor legacy code for better maintainability',
            'task_type': 'code_refactoring',
            'priority': TaskPriority.LOW,
            'context': {
                'language': 'javascript',
                'target_patterns': ['clean_code', 'solid_principles'],
                'preserve_functionality': True
            }
        }
    ]


async def demonstrate_compliance_validation(orchestrator: OrchestrationEngine):
    """Demonstrate legal compliance validation."""
    print("\n" + "="*60)
    print("=== Legal Compliance Validation Demo ===")
    
    # Get compliance manager
    compliance_manager = orchestrator.compliance_manager
    
    # Test user credentials
    user_credentials = create_test_user_credentials()
    
    print(f"\n🔐 Testing compliance for {len(user_credentials)} branded assistants...")
    
    for vendor, credentials in user_credentials.items():
        print(f"\n📋 Validating {vendor}:")
        
        # Validate license
        status, details = compliance_manager.validate_user_license(vendor, credentials)
        print(f"  License Status: {status.value}")
        
        if status.value == "valid":
            print(f"  ✅ Validation successful")
            
            # Get branding requirements
            branding = compliance_manager.get_branding_requirements(vendor)
            print(f"  🏷️  Required Attribution: {branding['required_attribution']}")
            print(f"  ™️  Trademark Notice: {branding['trademark_notice']}")
            
            # Check rate limits
            rate_limits = branding.get('rate_limits', {})
            print(f"  ⏱️  Rate Limits: {rate_limits}")
        else:
            print(f"  ❌ Validation failed: {details.get('error', 'Unknown error')}")
    
    # Generate compliance report
    compliance_report = compliance_manager.get_compliance_report()
    print(f"\n📊 Compliance Summary:")
    print(f"  Total Vendors: {compliance_report['total_vendors']}")
    print(f"  Licensed Vendors: {compliance_report['licensed_vendors']}")
    print(f"  Compliance Violations: {compliance_report['compliance_violations']}")


async def demonstrate_agent_orchestration(orchestrator: OrchestrationEngine):
    """Demonstrate dynamic agent orchestration."""
    print("\n" + "="*60)
    print("=== Dynamic Agent Orchestration Demo ===")
    
    # Get agent registry
    agent_registry = orchestrator.agent_registry
    
    # Display available agents
    available_agents = agent_registry.get_available_agents()
    print(f"\n🤖 Available Agents: {len(available_agents)}")
    
    for agent in available_agents:
        print(f"  • {agent['name']} ({agent['agent_id']})")
        print(f"    Roles: {', '.join(agent['supported_roles'])}")
        print(f"    Languages: {', '.join(agent['programming_languages'][:3])}...")
        print(f"    Health Score: {agent['health_score']:.2f}")
        print(f"    Current Load: {agent['current_load']}/{agent['max_concurrent_tasks']}")
    
    # Test role-based agent selection
    print(f"\n🎭 Testing Role-Based Agent Selection:")
    
    for role in [AgentRole.GENERATOR, AgentRole.ANALYZER, AgentRole.VALIDATOR]:
        agents_for_role = agent_registry.get_agents_by_role(role)
        print(f"  {role.value}: {len(agents_for_role)} agents available")
        
        if agents_for_role:
            best_agent = agents_for_role[0]  # First agent (would be best in real selection)
            print(f"    Best: {best_agent['name']} (Health: {best_agent['health_score']:.2f})")


async def demonstrate_combinatorial_optimization(orchestrator: OrchestrationEngine):
    """Demonstrate combinatorial matrix optimization."""
    print("\n" + "="*60)
    print("=== Combinatorial Optimization Demo ===")
    
    # Create combinatorial matrix
    combinatorial_matrix = CombinatorialMatrix(
        agent_registry=orchestrator.agent_registry,
        workflow_executor=orchestrator.workflow_executor,
        config={'max_combinations_to_test': 20, 'parallel_testing': True}
    )
    
    # Get matrix statistics
    matrix_stats = combinatorial_matrix.get_matrix_statistics()
    print(f"\n🧮 Combinatorial Matrix Statistics:")
    print(f"  Total Combinations: {matrix_stats['total_combinations']}")
    print(f"  Tested Combinations: {matrix_stats['tested_combinations']}")
    print(f"  High Confidence: {matrix_stats['confidence_distribution']['high_confidence']}")
    print(f"  Medium Confidence: {matrix_stats['confidence_distribution']['medium_confidence']}")
    print(f"  Untested: {matrix_stats['confidence_distribution']['untested']}")
    
    # Run comprehensive testing
    test_tasks = create_test_tasks()
    print(f"\n🧪 Running comprehensive testing with {len(test_tasks)} tasks...")
    
    test_results = await combinatorial_matrix.run_comprehensive_testing(
        test_tasks=test_tasks,
        max_combinations=10
    )
    
    print(f"  ✅ Testing completed in {test_results['testing_duration']:.2f} seconds")
    print(f"  📊 Results:")
    print(f"    Combinations Tested: {test_results['total_combinations_tested']}")
    print(f"    Total Tests: {test_results['total_tests_executed']}")
    print(f"    Successful: {test_results['successful_tests']}")
    print(f"    Failed: {test_results['failed_tests']}")
    
    # Find optimal combinations
    print(f"\n🎯 Optimal Combinations by Task Type:")
    for task_type, optimal_combos in test_results['optimal_combinations_by_task'].items():
        print(f"  {task_type}: {len(optimal_combos)} optimal combinations found")
        
        if optimal_combos:
            # Get details of best combination
            best_combo_id = optimal_combos[0]
            combo_info = combinatorial_matrix.get_combination_info(best_combo_id)
            if combo_info:
                print(f"    Best: {combo_info['agents']} (Success Rate: {combo_info['performance']['success_rate']:.2%})")


async def demonstrate_workflow_execution(orchestrator: OrchestrationEngine):
    """Demonstrate dynamic workflow execution."""
    print("\n" + "="*60)
    print("=== Dynamic Workflow Execution Demo ===")
    
    # Execute various tasks
    test_tasks = create_test_tasks()
    
    print(f"\n⚡ Executing {len(test_tasks)} tasks with dynamic orchestration...")
    
    for i, task in enumerate(test_tasks, 1):
        print(f"\n📋 Task {i}: {task['description']}")
        print(f"   Type: {task['task_type']}, Priority: {task['priority'].value}")
        
        # Execute task
        result = await orchestrator.execute_task(
            task_description=task['description'],
            task_type=task['task_type'],
            priority=task['priority'],
            context=task['context']
        )
        
        print(f"   Result: {'✅ Success' if result.success else '❌ Failed'}")
        print(f"   Execution Time: {result.execution_time:.2f}s")
        print(f"   Quality Score: {result.quality_score:.2f}")
        
        if result.metadata:
            agents_used = result.metadata.get('agents_used', [])
            if agents_used:
                print(f"   Agents Used: {', '.join(agents_used)}")
        
        if not result.success and result.error_message:
            print(f"   Error: {result.error_message}")


async def demonstrate_shared_context(orchestrator: OrchestrationEngine):
    """Demonstrate shared context management."""
    print("\n" + "="*60)
    print("=== Shared Context Management Demo ===")
    
    shared_context = orchestrator.shared_context
    
    # Create a test session
    session_id = shared_context.create_session(
        task_id="demo_task_001",
        user_id="demo_user",
        metadata={'demo': True, 'created_by': 'orchestration_demo'}
    )
    
    print(f"\n🔗 Created session: {session_id}")
    
    # Add context data
    context_updates = {
        'project_name': 'AI Orchestration Demo',
        'programming_language': 'python',
        'framework': 'fastapi',
        'requirements': ['authentication', 'database', 'api'],
        'team_size': 5,
        'deadline': '2024-02-01'
    }
    
    success = shared_context.update_context(
        task_id="demo_task_001",
        updates=context_updates,
        agent_id="demo_agent"
    )
    
    print(f"📝 Context update: {'✅ Success' if success else '❌ Failed'}")
    print(f"   Added {len(context_updates)} context items")
    
    # Retrieve context
    retrieved_context = shared_context.get_context("demo_task_001")
    print(f"\n📖 Retrieved context: {len(retrieved_context)} items")
    
    for key, value in retrieved_context.items():
        print(f"   {key}: {value}")
    
    # Test agent registration
    shared_context.register_agent_for_task("demo_task_001", "github_copilot")
    shared_context.register_agent_for_task("demo_task_001", "tabnine")
    
    active_agents = shared_context.get_active_agents("demo_task_001")
    print(f"\n👥 Active agents for task: {', '.join(active_agents)}")
    
    # Get context statistics
    context_stats = shared_context.get_context_statistics()
    print(f"\n📊 Context Statistics:")
    print(f"   Total Entries: {context_stats['storage']['total_entries']}")
    print(f"   Active Sessions: {context_stats['storage']['active_sessions']}")
    print(f"   Context Reads: {context_stats['statistics']['context_reads']}")
    print(f"   Context Writes: {context_stats['statistics']['context_writes']}")


async def demonstrate_performance_monitoring(orchestrator: OrchestrationEngine):
    """Demonstrate performance monitoring and analytics."""
    print("\n" + "="*60)
    print("=== Performance Monitoring Demo ===")
    
    # Get orchestration status
    status = orchestrator.get_orchestration_status()
    
    print(f"\n📈 Orchestration Status:")
    print(f"   Active Tasks: {status['active_tasks']}")
    print(f"   Total Agents: {status['total_agents']}")
    print(f"   Available Agents: {status['available_agents']}")
    print(f"   Orchestration Mode: {status['orchestration_mode']}")
    
    # Performance statistics
    perf_stats = status['performance_stats']
    print(f"\n⚡ Performance Statistics:")
    print(f"   Total Tasks: {perf_stats['total_tasks']}")
    print(f"   Successful Tasks: {perf_stats['successful_tasks']}")
    print(f"   Success Rate: {perf_stats['successful_tasks'] / max(1, perf_stats['total_tasks']):.2%}")
    print(f"   Average Completion Time: {perf_stats['average_completion_time']:.2f}s")
    print(f"   Fallback Activations: {perf_stats['fallback_activations']}")
    
    # Agent registry statistics
    registry_stats = orchestrator.agent_registry.get_registry_statistics()
    print(f"\n🤖 Agent Registry Statistics:")
    print(f"   Total Agents: {registry_stats['total_agents']}")
    print(f"   Available Agents: {registry_stats['available_agents']}")
    print(f"   Reserved Agents: {registry_stats['reserved_agents']}")
    print(f"   Average Health Score: {registry_stats['average_health_score']:.2f}")
    
    # Workflow executor statistics
    executor_stats = orchestrator.workflow_executor.get_execution_statistics()
    print(f"\n⚙️ Workflow Executor Statistics:")
    exec_stats = executor_stats['execution_stats']
    print(f"   Total Executions: {exec_stats['total_executions']}")
    print(f"   Success Rate: {exec_stats['successful_executions'] / max(1, exec_stats['total_executions']):.2%}")
    print(f"   Average Quality Score: {exec_stats['average_quality_score']:.2f}")


async def main():
    """Main demo execution."""
    print("🚀 AI Coding Assistant Orchestration System - Complete Demo")
    print("=" * 80)
    
    # Setup logging
    setup_logging()
    
    # Create orchestration engine
    print("\n🔧 Initializing Orchestration Engine...")
    
    config = {
        'orchestration_mode': 'adaptive',
        'max_concurrent_tasks': 10,
        'performance_learning': True,
        'compliance': {
            'strict_mode': True,
            'compliance_checks_enabled': True
        }
    }
    
    orchestrator = OrchestrationEngine(config)
    
    print("✅ Orchestration Engine initialized successfully")
    
    # Run all demonstrations
    try:
        await demonstrate_compliance_validation(orchestrator)
        await demonstrate_agent_orchestration(orchestrator)
        await demonstrate_combinatorial_optimization(orchestrator)
        await demonstrate_shared_context(orchestrator)
        await demonstrate_workflow_execution(orchestrator)
        await demonstrate_performance_monitoring(orchestrator)
        
        print("\n" + "="*80)
        print("🎉 Complete AI Assistant Orchestration Demo Finished Successfully!")
        print("")
        print("✅ Demonstrated Capabilities:")
        print("  🔐 Legal compliance validation for all branded assistants")
        print("  🤖 Dynamic agent assignment and role management")
        print("  🧮 Combinatorial optimization and testing")
        print("  🔗 Shared context management across agents")
        print("  ⚡ Dynamic workflow execution with fallback logic")
        print("  📈 Comprehensive performance monitoring")
        print("")
        print("🌟 The AI Assistant Orchestration System is ready for:")
        print("  ♾️  Integration with any branded AI coding assistant")
        print("  🎯 Optimal agent-role combination discovery")
        print("  🔄 Continuous learning and performance optimization")
        print("  🛡️  Full legal compliance and branding respect")
        print("  🚀 Production-ready enterprise orchestration")
        print("="*80)
        
    except Exception as e:
        print(f"\n❌ Demo failed with error: {str(e)}")
        logging.error(f"Demo execution failed: {str(e)}", exc_info=True)


if __name__ == "__main__":
    asyncio.run(main())

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentAssignmentService = void 0;
const client_1 = require("@prisma/client");
const types_1 = require("@/shared/types");
const EventBus_1 = require("./EventBus");
const logger_1 = require("../utils/logger");
class AgentAssignmentService {
    constructor() {
        this.prisma = new client_1.PrismaClient();
        this.eventBus = new EventBus_1.EventBus();
    }
    /**
     * Assigns agents to roles based on criteria using intelligent matching
     */
    async assignAgents(criteria, userId) {
        try {
            logger_1.logger.info('Starting agent assignment process', { criteria, userId });
            // Get available agents
            const availableAgents = await this.getAvailableAgents(criteria);
            if (availableAgents.length === 0) {
                return {
                    success: false,
                    assignments: [],
                    unfulfilledRoles: criteria.requiredRoles,
                    warnings: ['No available agents found matching criteria'],
                };
            }
            // Calculate agent scores for each role
            const roleAssignments = await this.calculateRoleAssignments(availableAgents, criteria);
            // Optimize assignments using workload balancing
            const optimizedAssignments = criteria.workloadBalancing
                ? this.optimizeWorkloadDistribution(roleAssignments, criteria.maxAgents || 10)
                : roleAssignments;
            // Identify unfulfilled roles
            const assignedRoles = optimizedAssignments.flatMap(a => a.assignedRoles);
            const unfulfilledRoles = criteria.requiredRoles.filter(role => !assignedRoles.includes(role));
            // Generate warnings
            const warnings = this.generateAssignmentWarnings(optimizedAssignments, criteria, availableAgents);
            // Emit assignment events
            optimizedAssignments.forEach(assignment => {
                this.eventBus.emit(EventBus_1.EVENT_TYPES.AGENT_ROLE_ASSIGNED, {
                    agentId: assignment.agentId,
                    userId,
                    data: {
                        roles: assignment.assignedRoles,
                        confidence: assignment.confidence,
                        reason: assignment.reason,
                    },
                });
            });
            logger_1.logger.info('Agent assignment completed', {
                assignedAgents: optimizedAssignments.length,
                unfulfilledRoles: unfulfilledRoles.length,
                userId,
            });
            return {
                success: unfulfilledRoles.length === 0,
                assignments: optimizedAssignments,
                unfulfilledRoles,
                warnings,
            };
        }
        catch (error) {
            logger_1.logger.error('Agent assignment failed', { error, criteria, userId });
            throw error;
        }
    }
    /**
     * Gets available agents based on criteria
     */
    async getAvailableAgents(criteria) {
        const where = {
            isActive: true,
        };
        if (criteria.minFitnessScore) {
            where.fitnessScore = { gte: criteria.minFitnessScore };
        }
        if (criteria.excludeAgentIds && criteria.excludeAgentIds.length > 0) {
            where.id = { notIn: criteria.excludeAgentIds };
        }
        if (criteria.preferredVendors && criteria.preferredVendors.length > 0) {
            where.vendor = { in: criteria.preferredVendors };
        }
        const agents = await this.prisma.agent.findMany({
            where,
            include: {
                workflowExecutions: {
                    where: {
                        status: { in: ['PENDING', 'RUNNING'] },
                    },
                    select: { id: true },
                },
            },
        });
        // Calculate current workload
        return agents.map(agent => ({
            ...agent,
            currentWorkload: agent.workflowExecutions.length,
        }));
    }
    /**
     * Calculates role assignments with confidence scores
     */
    async calculateRoleAssignments(agents, criteria) {
        const assignments = [];
        const allRoles = [...criteria.requiredRoles, ...(criteria.optionalRoles || [])];
        for (const agent of agents) {
            const suitableRoles = this.findSuitableRoles(agent, allRoles, criteria);
            if (suitableRoles.length > 0) {
                const confidence = this.calculateConfidenceScore(agent, suitableRoles, criteria);
                const reason = this.generateAssignmentReason(agent, suitableRoles, confidence);
                assignments.push({
                    agentId: agent.id,
                    agent: {
                        id: agent.id,
                        agentId: agent.agentId,
                        name: agent.name,
                        vendor: agent.vendor,
                        capabilities: agent.capabilities,
                        roles: agent.roles,
                        fitnessScore: agent.fitnessScore,
                        currentWorkload: agent.currentWorkload,
                    },
                    assignedRoles: suitableRoles,
                    confidence,
                    reason,
                });
            }
        }
        // Sort by confidence score (highest first)
        return assignments.sort((a, b) => b.confidence - a.confidence);
    }
    /**
     * Finds roles that an agent is suitable for
     */
    findSuitableRoles(agent, roles, criteria) {
        const suitableRoles = [];
        for (const roleId of roles) {
            const roleDefinition = types_1.AGENT_ROLES.find(r => r.id === roleId);
            if (!roleDefinition)
                continue;
            // Check if agent has the role
            if (!agent.roles.includes(roleId))
                continue;
            // Check required capabilities
            const hasRequiredCapabilities = roleDefinition.requiredCapabilities.every(cap => agent.capabilities.includes(cap));
            if (hasRequiredCapabilities) {
                suitableRoles.push(roleId);
            }
        }
        return suitableRoles;
    }
    /**
     * Calculates confidence score for an assignment
     */
    calculateConfidenceScore(agent, assignedRoles, criteria) {
        let score = 0;
        let factors = 0;
        // Fitness score factor (0-40 points)
        score += (agent.fitnessScore / 100) * 40;
        factors++;
        // Role match factor (0-30 points)
        const requiredRoleMatches = assignedRoles.filter(role => criteria.requiredRoles.includes(role)).length;
        const roleMatchRatio = requiredRoleMatches / criteria.requiredRoles.length;
        score += roleMatchRatio * 30;
        factors++;
        // Capability match factor (0-20 points)
        const capabilityMatches = criteria.requiredCapabilities.filter(cap => agent.capabilities.includes(cap)).length;
        const capabilityMatchRatio = capabilityMatches / criteria.requiredCapabilities.length;
        score += capabilityMatchRatio * 20;
        factors++;
        // Workload factor (0-10 points) - lower workload is better
        const workloadScore = Math.max(0, 10 - agent.currentWorkload);
        score += workloadScore;
        factors++;
        return Math.round(score / factors);
    }
    /**
     * Generates human-readable assignment reason
     */
    generateAssignmentReason(agent, assignedRoles, confidence) {
        const reasons = [];
        if (confidence >= 80) {
            reasons.push('Excellent match');
        }
        else if (confidence >= 60) {
            reasons.push('Good match');
        }
        else {
            reasons.push('Acceptable match');
        }
        reasons.push(`fitness score: ${agent.fitnessScore}`);
        reasons.push(`workload: ${agent.currentWorkload}`);
        reasons.push(`roles: ${assignedRoles.join(', ')}`);
        return reasons.join(', ');
    }
    /**
     * Optimizes workload distribution across agents
     */
    optimizeWorkloadDistribution(assignments, maxAgents) {
        // Sort by confidence and workload
        const sorted = assignments.sort((a, b) => {
            const confidenceDiff = b.confidence - a.confidence;
            if (Math.abs(confidenceDiff) < 5) {
                // If confidence is similar, prefer lower workload
                return a.agent.currentWorkload - b.agent.currentWorkload;
            }
            return confidenceDiff;
        });
        return sorted.slice(0, maxAgents);
    }
    /**
     * Generates warnings for the assignment result
     */
    generateAssignmentWarnings(assignments, criteria, availableAgents) {
        const warnings = [];
        // Low confidence warnings
        const lowConfidenceAssignments = assignments.filter(a => a.confidence < 60);
        if (lowConfidenceAssignments.length > 0) {
            warnings.push(`${lowConfidenceAssignments.length} assignments have low confidence scores`);
        }
        // High workload warnings
        const highWorkloadAssignments = assignments.filter(a => a.agent.currentWorkload > 5);
        if (highWorkloadAssignments.length > 0) {
            warnings.push(`${highWorkloadAssignments.length} agents have high current workloads`);
        }
        // Limited agent pool warning
        if (availableAgents.length < criteria.requiredRoles.length) {
            warnings.push('Limited agent pool may affect assignment quality');
        }
        return warnings;
    }
    /**
     * Gets role-based agent recommendations
     */
    async getRecommendations(roleId) {
        const roleDefinition = types_1.AGENT_ROLES.find(r => r.id === roleId);
        if (!roleDefinition) {
            throw new Error(`Role ${roleId} not found`);
        }
        const agents = await this.prisma.agent.findMany({
            where: {
                isActive: true,
                roles: { has: roleId },
                capabilities: {
                    hasEvery: roleDefinition.requiredCapabilities,
                },
            },
            include: {
                workflowExecutions: {
                    where: {
                        status: { in: ['PENDING', 'RUNNING'] },
                    },
                    select: { id: true },
                },
            },
            orderBy: [
                { fitnessScore: 'desc' },
                { createdAt: 'asc' },
            ],
        });
        return agents.map(agent => ({
            ...agent,
            currentWorkload: agent.workflowExecutions.length,
            matchScore: this.calculateRoleMatchScore(agent, roleDefinition),
        }));
    }
    /**
     * Calculates how well an agent matches a specific role
     */
    calculateRoleMatchScore(agent, roleDefinition) {
        let score = 0;
        // Required capabilities (60% weight)
        const requiredMatches = roleDefinition.requiredCapabilities.filter(cap => agent.capabilities.includes(cap)).length;
        score += (requiredMatches / roleDefinition.requiredCapabilities.length) * 60;
        // Optional capabilities (20% weight)
        if (roleDefinition.optionalCapabilities.length > 0) {
            const optionalMatches = roleDefinition.optionalCapabilities.filter(cap => agent.capabilities.includes(cap)).length;
            score += (optionalMatches / roleDefinition.optionalCapabilities.length) * 20;
        }
        else {
            score += 20; // Full points if no optional capabilities
        }
        // Fitness score (20% weight)
        score += (agent.fitnessScore / 100) * 20;
        return Math.round(score);
    }
}
exports.AgentAssignmentService = AgentAssignmentService;

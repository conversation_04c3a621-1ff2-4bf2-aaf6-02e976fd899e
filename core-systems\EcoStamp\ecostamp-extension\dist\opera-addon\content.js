/**
 * EcoStamp - AI Environmental Impact Tracker
 * Cross-Browser Compatible - Works with ALL AI platforms: ChatGPT, Claude, Gemini, and more
 * Supports: Chrome, Firefox, Edge, Opera, Brave, Safari
 */

// Cross-browser compatibility
const browserAPI = (() => {
    if (typeof browser !== 'undefined') {
        return browser; // Firefox
    }
    return chrome; // Chrome, Edge, Opera, Brave
})();

console.log('🌱 EcoStamp loaded - Cross-browser support for ALL AI platforms');

// Universal AI Platform Detection
const AI_PLATFORMS = {
    chatgpt: {
        domains: ['chat.openai.com', 'chatgpt.com'],
        name: 'ChatGPT',
        selectors: [
            '[data-message-author-role="assistant"]',
            '.message.assistant',
            '.response-message',
            '.ai-response'
        ],
        models: ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo'],
        color: '#10a37f'
    },
    claude: {
        domains: ['claude.ai', 'anthropic.com'],
        name: '<PERSON>',
        selectors: [
            '[data-role="assistant"]',
            '.assistant-message',
            '.claude-response',
            '.ai-message'
        ],
        models: ['claude-3-opus', 'claude-3-sonnet', 'claude-3-haiku'],
        color: '#cc785c'
    },
    gemini: {
        domains: ['gemini.google.com', 'bard.google.com'],
        name: 'Gemini',
        selectors: [
            '[data-role="model"]',
            '.model-turn',
            '.response-container',
            '.gemini-response'
        ],
        models: ['gemini-pro', 'gemini-ultra', 'gemini-nano'],
        color: '#4285f4'
    },
    perplexity: {
        domains: ['perplexity.ai'],
        name: 'Perplexity',
        selectors: [
            '.answer-content',
            '.response-text',
            '.ai-answer'
        ],
        models: ['perplexity-pro'],
        color: '#20b2aa'
    },
    poe: {
        domains: ['poe.com'],
        name: 'Poe',
        selectors: [
            '.Message_botMessageBubble',
            '.bot-message',
            '.ai-response'
        ],
        models: ['claude', 'gpt-4', 'llama'],
        color: '#ff6b6b'
    },
    character: {
        domains: ['character.ai'],
        name: 'Character.AI',
        selectors: [
            '.character-message',
            '.bot-message',
            '.ai-response'
        ],
        models: ['character-ai'],
        color: '#8b5cf6'
    },
    you: {
        domains: ['you.com'],
        name: 'You.com',
        selectors: [
            '.ai-response',
            '.search-result-ai',
            '.you-chat-response'
        ],
        models: ['you-chat'],
        color: '#0066cc'
    },
    huggingface: {
        domains: ['huggingface.co'],
        name: 'Hugging Face',
        selectors: [
            '.model-output',
            '.inference-output',
            '.ai-response'
        ],
        models: ['various'],
        color: '#ff9500'
    },
    universal: {
        domains: ['*'],
        name: 'Universal AI',
        selectors: [
            '.ai-response',
            '.bot-message',
            '.assistant-message',
            '.model-response',
            '.chat-response',
            '.ai-output',
            '.generated-text'
        ],
        models: ['unknown'],
        color: '#6b7280'
    }
};

// Configuration
const STAMPLY_CONFIG = {
    enabled: true,
    showTimestamp: true,
    showEcoLevel: true,
    showModel: true,
    showPlatform: true,
    apiEndpoint: 'http://localhost:3000/api',
    useBenchmarks: true,
    benchmarksUrl: chrome.runtime.getURL('data/benchmarks.json')
};

// Benchmarks cache
let benchmarksData = null;
let lastBenchmarksUpdate = null;

// Load benchmarks data
async function loadBenchmarks() {
    try {
        if (benchmarksData && lastBenchmarksUpdate &&
            Date.now() - lastBenchmarksUpdate < 3600000) { // 1 hour cache
            return benchmarksData;
        }

        const response = await fetch(STAMPLY_CONFIG.benchmarksUrl);
        if (response.ok) {
            benchmarksData = await response.json();
            lastBenchmarksUpdate = Date.now();
            console.log('🌱 EcoStamp: Loaded benchmarks data v' + benchmarksData.version);
            return benchmarksData;
        }
    } catch (error) {
        console.warn('EcoStamp: Failed to load benchmarks, using fallback data:', error);
    }

    // Fallback benchmarks data
    return {
        version: "fallback",
        providers: {
            chatgpt: { models: { "gpt-4": { energyPerToken: 0.0028, waterPerToken: 0.0342 }}},
            claude: { models: { "claude-3-sonnet": { energyPerToken: 0.0022, waterPerToken: 0.0268 }}},
            gemini: { models: { "gemini-pro": { energyPerToken: 0.0026, waterPerToken: 0.0315 }}}
        }
    };
}

// Detect current AI platform
function detectAIPlatform() {
    const hostname = window.location.hostname.toLowerCase();
    
    for (const [key, platform] of Object.entries(AI_PLATFORMS)) {
        if (key === 'universal') continue;
        
        for (const domain of platform.domains) {
            if (hostname.includes(domain)) {
                console.log(`🎯 Stamply: Detected platform - ${platform.name}`);
                return { key, ...platform };
            }
        }
    }
    
    // Fallback to universal detection
    console.log('🔍 Stamply: Using universal AI detection');
    return { key: 'universal', ...AI_PLATFORMS.universal };
}

// Universal AI response detection
function findAIResponses(platform) {
    let foundElements = [];
    
    // Try platform-specific selectors first
    for (const selector of platform.selectors) {
        const elements = document.querySelectorAll(`${selector}:not(.stamply-processed)`);
        if (elements.length > 0) {
            foundElements = Array.from(elements);
            console.log(`✅ Stamply: Found ${elements.length} responses with ${platform.name} selector: ${selector}`);
            break;
        }
    }
    
    // Universal fallback detection
    if (foundElements.length === 0) {
        console.log('🔄 Stamply: Trying universal AI detection...');
        
        const universalSelectors = [
            // Common AI response patterns
            'div[class*="response"]:not(.stamply-processed)',
            'div[class*="message"]:not(.stamply-processed)',
            'div[class*="assistant"]:not(.stamply-processed)',
            'div[class*="bot"]:not(.stamply-processed)',
            'div[class*="ai"]:not(.stamply-processed)',
            'div[class*="model"]:not(.stamply-processed)',
            'div[class*="generated"]:not(.stamply-processed)',
            'div[class*="output"]:not(.stamply-processed)',
            // Data attributes
            'div[data-role*="assistant"]:not(.stamply-processed)',
            'div[data-role*="model"]:not(.stamply-processed)',
            'div[data-role*="bot"]:not(.stamply-processed)',
            // Content-based detection
            'div:not(.stamply-processed)'
        ];
        
        for (const selector of universalSelectors) {
            const elements = document.querySelectorAll(selector);
            
            for (const element of elements) {
                const text = element.textContent?.trim();
                
                // Smart content analysis
                if (text && 
                    text.length > 100 && 
                    text.length < 5000 &&
                    !isUserInput(element, text) &&
                    !isNavigationElement(element, text) &&
                    looksLikeAIResponse(text)) {
                    foundElements.push(element);
                }
            }
            
            if (foundElements.length > 0) {
                console.log(`🔍 Stamply: Found ${foundElements.length} responses via universal detection`);
                break;
            }
        }
    }
    
    return foundElements.slice(0, 10); // Limit to prevent performance issues
}

// Smart content analysis helpers
function isUserInput(element, text) {
    return (
        element.querySelector('input') ||
        element.querySelector('textarea') ||
        text.includes('Send a message') ||
        text.includes('Type a message') ||
        text.includes('Enter a prompt') ||
        text.includes('Ask me anything') ||
        element.closest('[contenteditable="true"]')
    );
}

function isNavigationElement(element, text) {
    return (
        element.querySelector('button') ||
        element.querySelector('a') ||
        text.includes('Sign in') ||
        text.includes('Log in') ||
        text.includes('Menu') ||
        text.includes('Settings') ||
        text.includes('Profile') ||
        text.length < 50
    );
}

function looksLikeAIResponse(text) {
    // Patterns that suggest AI-generated content
    const aiPatterns = [
        /I'm|I am|I can|I'll|I will|I'd|I would/i,
        /Here's|Here is|Here are/i,
        /Let me|Allow me/i,
        /Based on|According to/i,
        /In summary|To summarize/i,
        /However|Therefore|Additionally|Furthermore/i,
        /\b(explanation|analysis|solution|answer|response)\b/i
    ];
    
    return aiPatterns.some(pattern => pattern.test(text));
}

// Create SHA-256 hash
async function createHash(text) {
    const encoder = new TextEncoder();
    const data = encoder.encode(text);
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
}

// Enhanced eco-level calculation with benchmarks
async function calculateEcoLevel(text, platform) {
    const length = text.length;
    const complexity = analyzeComplexity(text);
    const estimatedTokens = Math.ceil(length / 4); // Rough token estimation

    let energy = '0.45 Wh';
    let water = '12.8 mL';
    let baseLevel = 3;

    // Try to use benchmarks data for accurate calculations
    if (STAMPLY_CONFIG.useBenchmarks && benchmarksData) {
        const platformData = benchmarksData.providers[platform.key];
        if (platformData && platformData.models) {
            const model = detectModel(platform, text);
            const modelData = platformData.models[model] || Object.values(platformData.models)[0];

            if (modelData) {
                // Calculate based on actual benchmarks
                const energyWh = (estimatedTokens * modelData.energyPerToken * 1000).toFixed(3);
                const waterMl = (estimatedTokens * modelData.waterPerToken * 1000).toFixed(1);

                energy = `${energyWh} Wh`;
                water = `${waterMl} mL`;

                // Determine eco-level based on efficiency
                if (modelData.efficiency === 'excellent') baseLevel = 1;
                else if (modelData.efficiency === 'high') baseLevel = 2;
                else if (modelData.efficiency === 'medium') baseLevel = 3;
                else baseLevel = 4;

                // Adjust for complexity
                if (complexity === 'very-high') baseLevel = Math.min(5, baseLevel + 2);
                else if (complexity === 'high') baseLevel = Math.min(5, baseLevel + 1);
                else if (complexity === 'low') baseLevel = Math.max(1, baseLevel - 1);
            }
        }
    } else {
        // Fallback to original calculation
        const platformMultiplier = getPlatformMultiplier(platform.key);

        if (length < 150 && complexity === 'low') {
            baseLevel = 1;
            energy = '0.12 Wh';
            water = '3.8 mL';
        } else if (length < 300 && complexity !== 'high') {
            baseLevel = 2;
            energy = '0.28 Wh';
            water = '8.2 mL';
        } else if (length > 600 || complexity === 'high') {
            baseLevel = 4;
            energy = '0.67 Wh';
            water = '19.4 mL';
        } else if (length > 1000 || complexity === 'very-high') {
            baseLevel = 5;
            energy = '0.89 Wh';
            water = '26.7 mL';
        }

        baseLevel = Math.min(5, Math.max(1, Math.round(baseLevel * platformMultiplier)));
    }

    // Create proper eco-level meter: green leaves for good levels, brown for bad levels
    const greenLeaves = Math.max(0, 5 - baseLevel + 1); // More green = better
    const brownLeaves = Math.max(0, baseLevel - 1);     // More brown = worse
    const leaves = '🌿'.repeat(greenLeaves) + '🍂'.repeat(brownLeaves);

    return {
        level: baseLevel,
        leaves,
        energy,
        water,
        complexity,
        tokens: estimatedTokens,
        benchmarkUsed: !!benchmarksData
    };
}

// Platform efficiency multipliers
function getPlatformMultiplier(platformKey) {
    const multipliers = {
        chatgpt: 1.0,      // Baseline
        claude: 0.9,       // Slightly more efficient
        gemini: 1.1,       // Google infrastructure
        perplexity: 1.2,   // Search + AI
        poe: 1.0,          // Varies by model
        character: 0.8,    // Optimized for conversation
        you: 1.3,          // Search heavy
        huggingface: 1.1,  // Various models
        universal: 1.0     // Unknown, use baseline
    };
    
    return multipliers[platformKey] || 1.0;
}

// Analyze text complexity
function analyzeComplexity(text) {
    const hasCode = /```|`[^`]+`|function|class|import|def|console\.log/i.test(text);
    const hasLists = /[-*•]\s|^\d+\./m.test(text);
    const hasComplexWords = text.split(' ').filter(word => word.length > 8).length;
    const sentences = text.split(/[.!?]+/).length;
    const avgWordsPerSentence = text.split(' ').length / sentences;
    const hasMarkdown = /#{1,6}\s|^\*\*|^\*[^*]/m.test(text);
    const hasUrls = /https?:\/\//.test(text);
    
    let score = 0;
    if (hasCode) score += 3;
    if (hasLists) score += 1;
    if (hasMarkdown) score += 1;
    if (hasUrls) score += 1;
    if (hasComplexWords > 10) score += 2;
    if (avgWordsPerSentence > 20) score += 1;
    
    if (score >= 6) return 'very-high';
    if (score >= 4) return 'high';
    if (score >= 2) return 'medium';
    return 'low';
}

// Create universal Stamply footer
async function createUniversalStamplyFooter(text, platform) {
    const footer = document.createElement('div');
    footer.className = 'stamply-universal-footer';

    // Calculate metrics
    const ecoLevel = calculateEcoLevel(text, platform);
    const timestamp = new Date().toLocaleString('en-US', {
        month: '2-digit',
        day: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        timeZone: 'UTC',
        timeZoneName: 'short'
    });
    const hash = await createHash(text);

    // Detect model (simplified)
    const detectedModel = detectModel(platform, text);

    // Style the footer with platform colors
    footer.style.cssText = `
        margin-top: 16px;
        padding: 12px 16px;
        border-top: 1px solid #e1e5e9;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-size: 11px;
        line-height: 1.4;
        color: #4a5568;
        background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
        border-radius: 0 0 8px 8px;
        user-select: none;
        transition: all 0.2s ease;
        border-left: 3px solid ${platform.color};
    `;

    // Create content structure
    const divider = document.createElement('div');
    divider.style.cssText = 'text-align: center; color: #cbd5e0; margin-bottom: 8px; font-size: 10px;';
    divider.textContent = '──────────────────────────────────────────────';

    const infoContainer = document.createElement('div');

    // Timestamp and hash row
    const timestampRow = document.createElement('div');
    timestampRow.style.cssText = 'display: flex; justify-content: space-between; align-items: center; margin-bottom: 4px;';

    const timestampSpan = document.createElement('span');
    timestampSpan.style.cssText = 'font-weight: 500; color: #2d3748;';
    timestampSpan.textContent = `🕓 ${timestamp}`;

    const hashSpan = document.createElement('span');
    hashSpan.style.cssText = 'font-family: "SF Mono", "Monaco", monospace; background: #edf2f7; padding: 2px 6px; border-radius: 4px; font-size: 10px;';
    hashSpan.textContent = `🔐 SHA-256: ${hash.substring(0, 4)}...${hash.substring(-4)}`;

    timestampRow.appendChild(timestampSpan);
    timestampRow.appendChild(hashSpan);

    // Eco-level row
    const ecoRow = document.createElement('div');
    ecoRow.style.cssText = 'display: flex; justify-content: space-between; align-items: center; margin-bottom: 4px;';

    const ecoSpan = document.createElement('span');
    ecoSpan.style.cssText = 'font-weight: 600; color: #38a169;';
    ecoSpan.textContent = `🌿 Eco-Level: ${ecoLevel.level}/5 Leaves ${ecoLevel.leaves}`;

    const impactSpan = document.createElement('span');
    impactSpan.style.cssText = 'font-weight: 500; color: #805ad5;';
    impactSpan.textContent = `(${ecoLevel.energy} · ${ecoLevel.water})`;

    ecoRow.appendChild(ecoSpan);
    ecoRow.appendChild(impactSpan);

    // Platform and model row
    const platformRow = document.createElement('div');
    platformRow.style.cssText = 'display: flex; justify-content: space-between; align-items: center; margin-top: 8px; padding-top: 8px; border-top: 1px solid #e2e8f0;';

    const poweredSpan = document.createElement('span');
    poweredSpan.style.cssText = 'font-weight: 500; color: #3182ce;';
    poweredSpan.textContent = 'Powered by EcoStamp — GitHub';

    const platformModelSpan = document.createElement('span');
    platformModelSpan.style.cssText = `background: ${platform.color}20; color: ${platform.color}; padding: 2px 8px; border-radius: 12px; font-size: 10px; font-weight: 500;`;
    platformModelSpan.textContent = `${platform.name} • ${detectedModel}`;

    platformRow.appendChild(poweredSpan);
    platformRow.appendChild(platformModelSpan);

    // Assemble footer
    infoContainer.appendChild(timestampRow);
    infoContainer.appendChild(ecoRow);
    infoContainer.appendChild(platformRow);

    footer.appendChild(divider);
    footer.appendChild(infoContainer);

    return footer;
}

// Simple model detection
function detectModel(platform, text) {
    // Try to detect specific models based on response characteristics
    const textLength = text.length;
    const complexity = analyzeComplexity(text);

    if (platform.key === 'chatgpt') {
        if (complexity === 'very-high' || textLength > 1000) return 'gpt-4';
        if (complexity === 'high') return 'gpt-4-turbo';
        return 'gpt-3.5-turbo';
    } else if (platform.key === 'claude') {
        if (complexity === 'very-high') return 'claude-3-opus';
        if (complexity === 'high') return 'claude-3-sonnet';
        return 'claude-3-haiku';
    } else if (platform.key === 'gemini') {
        if (complexity === 'very-high') return 'gemini-ultra';
        return 'gemini-pro';
    }

    return platform.models[0] || 'unknown';
}

// Universal AI response processor
async function processUniversalAIResponses() {
    if (!STAMPLY_CONFIG.enabled) return;

    const platform = detectAIPlatform();
    const responses = findAIResponses(platform);

    console.log(`🔍 Stamply: Processing ${responses.length} responses on ${platform.name}`);

    for (const response of responses) {
        const text = response.textContent?.trim();
        if (text && text.length > 50) {
            response.classList.add('stamply-processed');

            try {
                const footer = await createUniversalStamplyFooter(text, platform);
                response.appendChild(footer);
                console.log(`✅ Stamply: Added universal footer to ${platform.name} response (${text.length} chars)`);

                // Store statistics
                await updateStamplyStats(text, platform, calculateEcoLevel(text, platform));

            } catch (error) {
                console.warn('Stamply: Error creating universal footer:', error);
            }
        }
    }
}

// Update statistics in storage
async function updateStamplyStats(text, platform, ecoLevel) {
    try {
        const stats = await chrome.storage.local.get(['stamplyStats']) || {};
        const currentStats = stats.stamplyStats || {
            totalInteractions: 0,
            totalEnergy: 0,
            totalWater: 0,
            platforms: {},
            ecoLevels: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 }
        };

        // Update totals
        currentStats.totalInteractions += 1;
        currentStats.totalEnergy += parseFloat(ecoLevel.energy.replace(/[^\d.]/g, ''));
        currentStats.totalWater += parseFloat(ecoLevel.water.replace(/[^\d.]/g, ''));

        // Update platform stats
        if (!currentStats.platforms[platform.key]) {
            currentStats.platforms[platform.key] = { count: 0, name: platform.name };
        }
        currentStats.platforms[platform.key].count += 1;

        // Update eco-level distribution
        currentStats.ecoLevels[ecoLevel.level] += 1;

        await chrome.storage.local.set({ stamplyStats: currentStats });

    } catch (error) {
        console.warn('Stamply: Error updating stats:', error);
    }
}

// Initialize EcoStamp
async function initializeEcoStamp() {
    console.log('🚀 EcoStamp: Initializing for ALL AI platforms...');

    // Load benchmarks data
    await loadBenchmarks();

    // Process existing responses
    processUniversalAIResponses();

    // Monitor for new responses with adaptive timing
    const observer = new MutationObserver(() => {
        setTimeout(processUniversalAIResponses, 1500); // Longer delay for universal compatibility
    });

    observer.observe(document.body, {
        childList: true,
        subtree: true
    });

    console.log('👀 EcoStamp: Monitoring for AI responses on all platforms...');
}

// Start when page is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeEcoStamp);
} else {
    setTimeout(initializeEcoStamp, 1000); // Small delay for dynamic content
}

// Global functions for debugging
window.ecoStamp = {
    process: processUniversalAIResponses,
    detect: detectAIPlatform,
    config: STAMPLY_CONFIG
};

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.clearError = exports.fetchWorkflowExecutions = exports.fetchWorkflowTemplates = void 0;
const toolkit_1 = require("@reduxjs/toolkit");
const api_1 = require("../../services/api");
const initialState = {
    templates: [],
    executions: [],
    isLoading: false,
    error: null,
};
exports.fetchWorkflowTemplates = (0, toolkit_1.createAsyncThunk)('workflow/fetchTemplates', async () => {
    const response = await api_1.workflowApi.getTemplates();
    return response.data;
});
exports.fetchWorkflowExecutions = (0, toolkit_1.createAsyncThunk)('workflow/fetchExecutions', async () => {
    const response = await api_1.workflowApi.getExecutions();
    return response.data;
});
const workflowSlice = (0, toolkit_1.createSlice)({
    name: 'workflow',
    initialState,
    reducers: {
        clearError: (state) => {
            state.error = null;
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(exports.fetchWorkflowTemplates.fulfilled, (state, action) => {
            state.templates = action.payload;
        })
            .addCase(exports.fetchWorkflowExecutions.fulfilled, (state, action) => {
            state.executions = action.payload;
        });
    },
});
exports.clearError = workflowSlice.actions.clearError;
exports.default = workflowSlice.reducer;

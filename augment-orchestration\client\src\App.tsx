import React, { useEffect } from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { Box, Container, AppBar, Toolbar, Typo<PERSON>, Button } from '@mui/material'
import { useAppDispatch, useAppSelector } from './hooks/redux'
import { initializeAuth, logout } from './store/slices/authSlice'
import { connectSocket, disconnectSocket } from './store/slices/socketSlice'
import Dashboard from './pages/Dashboard'
import Login from './pages/Login'
import Register from './pages/Register'
import FamilyTree from './pages/FamilyTree'
import Workflows from './pages/Workflows'
import Agents from './pages/Agents'
import Tunnels from './pages/Tunnels'
import Evolution from './pages/Evolution'
import AuditLogs from './pages/AuditLogs'
import LoadingSpinner from './components/LoadingSpinner'

function App() {
  const dispatch = useAppDispatch()
  const { user, isLoading } = useAppSelector((state) => state.auth)
  const { isConnected } = useAppSelector((state) => state.socket)

  useEffect(() => {
    dispatch(initializeAuth())
  }, [dispatch])

  useEffect(() => {
    if (user && !isConnected) {
      dispatch(connectSocket())
    } else if (!user && isConnected) {
      dispatch(disconnectSocket())
    }
  }, [user, isConnected, dispatch])

  const handleLogout = () => {
    dispatch(logout())
    dispatch(disconnectSocket())
  }

  if (isLoading) {
    return <LoadingSpinner />
  }

  if (!user) {
    return (
      <Routes>
        <Route path="/login" element={<Login />} />
        <Route path="/register" element={<Register />} />
        <Route path="*" element={<Navigate to="/login" replace />} />
      </Routes>
    )
  }

  return (
    <Box sx={{ flexGrow: 1 }}>
      <AppBar position="static" sx={{ mb: 3 }}>
        <Toolbar>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            🚀 Augment Code - AI Orchestration Platform
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Typography variant="body2">
              {user.username} ({user.role})
            </Typography>
            <Box
              sx={{
                width: 8,
                height: 8,
                borderRadius: '50%',
                backgroundColor: isConnected ? 'success.main' : 'error.main',
              }}
            />
            <Button color="inherit" onClick={handleLogout}>
              Logout
            </Button>
          </Box>
        </Toolbar>
      </AppBar>

      <Container maxWidth="xl">
        <Routes>
          <Route path="/" element={<Dashboard />} />
          <Route path="/family-tree" element={<FamilyTree />} />
          <Route path="/workflows" element={<Workflows />} />
          <Route path="/agents" element={<Agents />} />
          <Route path="/tunnels" element={<Tunnels />} />
          <Route path="/evolution" element={<Evolution />} />
          <Route path="/audit" element={<AuditLogs />} />
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </Container>
    </Box>
  )
}

export default App

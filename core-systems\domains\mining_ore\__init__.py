"""
Mining Ore Detection Domain Module

This module provides specialized feedback loop components for mining operations,
including LIDAR-based 3D mapping, ore detection and classification,
and geological survey validation.
"""

from .ore_detector import OreDetectionInterpreter
from .geological_matcher import GeologicalPatternMatcher
from .mining_validator import MiningOperationValidator
from .mineral_manager import MineralDataManager
from .domain import MiningOreDomain

__all__ = [
    'OreDetectionInterpreter',
    'GeologicalPatternMatcher', 
    'MiningOperationValidator',
    'MineralDataManager',
    'MiningOreDomain'
]

"""
Ecological Survey Validator for Species Tracking

Validates ecological survey results against research objectives,
determines survey success/failure, and provides recommendations
for continued research efforts.
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

from ...core.feedback_types import ValidationResult, FeedbackType


class EcologicalSurveyValidator:
    """
    Validates ecological survey results and determines research outcomes.
    
    Assesses survey success based on target species detection, biodiversity goals,
    and research objectives while providing actionable recommendations.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.validation_criteria = self._initialize_validation_criteria()
        
    def _initialize_validation_criteria(self) -> Dict[str, Any]:
        """Initialize validation criteria for different survey types."""
        return {
            'target_species_survey': {
                'success_thresholds': {
                    'target_detection_confidence': 0.80,
                    'minimum_detections': 1,
                    'habitat_suitability': 0.70,
                    'ecological_fit': 0.60
                },
                'partial_success_thresholds': {
                    'related_species_confidence': 0.70,
                    'habitat_indicators': 0.60,
                    'environmental_suitability': 0.50
                }
            },
            'biodiversity_survey': {
                'success_thresholds': {
                    'species_richness_minimum': 5,
                    'diversity_index_minimum': 0.60,
                    'ecosystem_health': 'good',
                    'survey_coverage': 0.80
                },
                'partial_success_thresholds': {
                    'species_richness_minimum': 3,
                    'diversity_index_minimum': 0.40,
                    'ecosystem_health': 'fair',
                    'survey_coverage': 0.60
                }
            },
            'population_assessment': {
                'success_thresholds': {
                    'density_estimate_confidence': 0.75,
                    'population_indicators': 'normal',
                    'breeding_evidence': True,
                    'habitat_quality': 0.70
                },
                'partial_success_thresholds': {
                    'density_estimate_confidence': 0.50,
                    'population_indicators': 'uncertain',
                    'habitat_quality': 0.50
                }
            }
        }
    
    def validate(self, interpreted_output: Dict[str, Any], 
                match_results: Dict[str, Any], 
                context: Dict[str, Any]) -> ValidationResult:
        """
        Validate ecological survey results and determine research outcomes.
        
        Args:
            interpreted_output: Species detection data from interpreter
            match_results: Ecological pattern matching results
            context: Survey context and objectives
            
        Returns:
            ValidationResult with survey assessment and recommendations
        """
        try:
            # Initialize validation result
            result = ValidationResult()
            result.timestamp = datetime.utcnow()
            
            # Determine survey type and objectives
            survey_type = context.get('survey_type', 'target_species_survey')
            target_species = context.get('target_species', 'carolina_chickadee')
            research_objectives = context.get('research_objectives', [])
            
            # Extract key metrics
            classifications = interpreted_output.get('classifications', [])
            confidence_scores = interpreted_output.get('confidence_scores', {})
            biodiversity_metrics = interpreted_output.get('biodiversity_metrics', {})
            environmental_data = interpreted_output.get('environmental_data', {})
            
            # Perform validation based on survey type
            if survey_type == 'target_species_survey':
                self._validate_target_species_survey(
                    classifications, confidence_scores, match_results, context, result
                )
            elif survey_type == 'biodiversity_survey':
                self._validate_biodiversity_survey(
                    biodiversity_metrics, match_results, context, result
                )
            elif survey_type == 'population_assessment':
                self._validate_population_assessment(
                    classifications, match_results, context, result
                )
            else:
                # Default to target species validation
                self._validate_target_species_survey(
                    classifications, confidence_scores, match_results, context, result
                )
            
            # Add general survey quality assessment
            self._assess_survey_quality(interpreted_output, match_results, context, result)
            
            # Generate recommendations
            self._generate_recommendations(interpreted_output, match_results, context, result)
            
            # Set metadata
            result.metadata.update({
                'survey_type': survey_type,
                'target_species': target_species,
                'research_objectives': research_objectives,
                'validation_timestamp': datetime.utcnow().isoformat(),
                'validator_version': '1.0.0',
                'survey_id': context.get('survey_id', 'unknown'),
                'total_detections': len(classifications),
                'target_detections': len([c for c in classifications if c.get('is_target_species', False)])
            })
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error validating ecological survey: {str(e)}")
            
            # Return error result
            error_result = ValidationResult()
            error_result.is_valid = False
            error_result.confidence_score = 0.0
            error_result.feedback_type = FeedbackType.INCORRECT
            error_result.issues = [{
                'type': 'validation_error',
                'severity': 'critical',
                'message': f'Survey validation failed: {str(e)}',
                'details': {'error': str(e)}
            }]
            error_result.metadata = {
                'validation_error': True,
                'error_message': str(e),
                'error_timestamp': datetime.utcnow().isoformat()
            }
            
            return error_result
    
    def _validate_target_species_survey(self, classifications: List[Dict[str, Any]], 
                                      confidence_scores: Dict[str, Any], 
                                      match_results: Dict[str, Any], 
                                      context: Dict[str, Any], 
                                      result: ValidationResult) -> None:
        """Validate target species survey results."""
        criteria = self.validation_criteria['target_species_survey']
        success_thresholds = criteria['success_thresholds']
        partial_thresholds = criteria['partial_success_thresholds']
        
        # Count target species detections
        target_detections = [c for c in classifications if c.get('is_target_species', False)]
        target_count = len(target_detections)
        
        # Get confidence scores
        target_confidence = confidence_scores.get('target_species_confidence', 0.0)
        overall_confidence = confidence_scores.get('overall_confidence', 0.0)
        
        # Get ecological fit
        ecological_fit = match_results.get('overall_ecological_fit', 0.0)
        habitat_suitability = match_results.get('habitat_assessment', {}).get('overall_suitability', 0.0)
        
        # Determine validation outcome
        if (target_count >= success_thresholds['minimum_detections'] and
            target_confidence >= success_thresholds['target_detection_confidence'] and
            habitat_suitability >= success_thresholds['habitat_suitability'] and
            ecological_fit >= success_thresholds['ecological_fit']):
            
            # Complete success - target species found with high confidence
            result.feedback_type = FeedbackType.CORRECT
            result.is_valid = True
            result.confidence_score = min(target_confidence, ecological_fit)
            result.success_message = f"Target species successfully detected with high confidence"
            
        elif (target_count > 0 or
              any(c.get('species_classification', {}).get('priority') == 'related' for c in classifications) or
              habitat_suitability >= partial_thresholds['habitat_indicators']):
            
            # Partial success - related species, suitable habitat, or low confidence target
            result.feedback_type = FeedbackType.PARTIALLY_CORRECT
            result.is_valid = True
            result.confidence_score = max(overall_confidence, habitat_suitability) * 0.7
            
            if target_count > 0:
                result.success_message = f"Target species detected but with lower confidence"
            else:
                result.success_message = f"Suitable habitat and/or related species found"
            
            # Add specific partial success details
            if target_count > 0:
                result.issues.append({
                    'type': 'low_confidence_target_detection',
                    'severity': 'medium',
                    'message': f'Target species detected with confidence {target_confidence:.2f}',
                    'details': {'target_count': target_count, 'confidence': target_confidence}
                })
            
            if habitat_suitability >= partial_thresholds['habitat_indicators']:
                result.issues.append({
                    'type': 'suitable_habitat_found',
                    'severity': 'info',
                    'message': f'Habitat suitable for target species',
                    'details': {'habitat_suitability': habitat_suitability}
                })
        
        else:
            # Survey unsuccessful
            result.feedback_type = FeedbackType.INCORRECT
            result.is_valid = False
            result.confidence_score = overall_confidence * 0.3
            result.success_message = f"Target species not detected in surveyed area"
            
            result.issues.append({
                'type': 'target_species_not_found',
                'severity': 'high',
                'message': f'No target species detections above confidence threshold',
                'details': {
                    'target_count': target_count,
                    'target_confidence': target_confidence,
                    'threshold': success_thresholds['target_detection_confidence']
                }
            })
    
    def _validate_biodiversity_survey(self, biodiversity_metrics: Dict[str, Any], 
                                    match_results: Dict[str, Any], 
                                    context: Dict[str, Any], 
                                    result: ValidationResult) -> None:
        """Validate biodiversity survey results."""
        criteria = self.validation_criteria['biodiversity_survey']
        success_thresholds = criteria['success_thresholds']
        partial_thresholds = criteria['partial_success_thresholds']
        
        # Extract biodiversity metrics
        species_richness = biodiversity_metrics.get('species_richness', 0)
        diversity_index = biodiversity_metrics.get('shannon_diversity_index', 0.0)
        
        # Get ecosystem health assessment
        biodiversity_assessment = match_results.get('biodiversity_assessment', {})
        ecosystem_health = biodiversity_assessment.get('ecosystem_health', 'poor')
        
        # Assess survey coverage (simplified)
        survey_coverage = context.get('area_coverage_percent', 100) / 100.0
        
        # Determine validation outcome
        if (species_richness >= success_thresholds['species_richness_minimum'] and
            diversity_index >= success_thresholds['diversity_index_minimum'] and
            ecosystem_health in ['good', 'excellent'] and
            survey_coverage >= success_thresholds['survey_coverage']):
            
            # Successful biodiversity survey
            result.feedback_type = FeedbackType.CORRECT
            result.is_valid = True
            result.confidence_score = min(diversity_index, survey_coverage)
            result.success_message = f"Comprehensive biodiversity survey completed successfully"
            
        elif (species_richness >= partial_thresholds['species_richness_minimum'] or
              diversity_index >= partial_thresholds['diversity_index_minimum'] or
              survey_coverage >= partial_thresholds['survey_coverage']):
            
            # Partial biodiversity success
            result.feedback_type = FeedbackType.PARTIALLY_CORRECT
            result.is_valid = True
            result.confidence_score = (diversity_index + survey_coverage) / 2 * 0.8
            result.success_message = f"Partial biodiversity assessment completed"
            
            # Add specific details
            if species_richness < success_thresholds['species_richness_minimum']:
                result.issues.append({
                    'type': 'limited_species_richness',
                    'severity': 'medium',
                    'message': f'Species richness below optimal level',
                    'details': {'observed': species_richness, 'target': success_thresholds['species_richness_minimum']}
                })
        
        else:
            # Insufficient biodiversity data
            result.feedback_type = FeedbackType.INCORRECT
            result.is_valid = False
            result.confidence_score = diversity_index * 0.5
            result.success_message = f"Insufficient biodiversity data collected"
            
            result.issues.append({
                'type': 'insufficient_biodiversity_data',
                'severity': 'high',
                'message': f'Biodiversity metrics below minimum thresholds',
                'details': {
                    'species_richness': species_richness,
                    'diversity_index': diversity_index,
                    'survey_coverage': survey_coverage
                }
            })
    
    def _validate_population_assessment(self, classifications: List[Dict[str, Any]], 
                                      match_results: Dict[str, Any], 
                                      context: Dict[str, Any], 
                                      result: ValidationResult) -> None:
        """Validate population assessment survey results."""
        criteria = self.validation_criteria['population_assessment']
        success_thresholds = criteria['success_thresholds']
        
        # Get population indicators
        population_indicators = match_results.get('population_indicators', {})
        density_assessment = population_indicators.get('density_assessment', 'unknown')
        estimated_density = population_indicators.get('estimated_density_per_hectare', 0)
        
        # Check for breeding evidence
        breeding_evidence = any('breeding' in str(c.get('behavioral_indicators', [])) 
                              for c in classifications)
        
        # Get habitat quality
        habitat_quality = match_results.get('habitat_assessment', {}).get('overall_suitability', 0.0)
        
        # Determine validation outcome
        if (density_assessment == 'normal' and
            breeding_evidence and
            habitat_quality >= success_thresholds['habitat_quality']):
            
            # Successful population assessment
            result.feedback_type = FeedbackType.CORRECT
            result.is_valid = True
            result.confidence_score = habitat_quality
            result.success_message = f"Population assessment indicates healthy population"
            
        elif (density_assessment in ['normal', 'high'] or
              habitat_quality >= success_thresholds['habitat_quality'] * 0.7):
            
            # Partial population assessment
            result.feedback_type = FeedbackType.PARTIALLY_CORRECT
            result.is_valid = True
            result.confidence_score = habitat_quality * 0.8
            result.success_message = f"Population indicators suggest viable population"
            
            if not breeding_evidence:
                result.issues.append({
                    'type': 'no_breeding_evidence',
                    'severity': 'medium',
                    'message': 'No breeding behavior observed',
                    'details': {'season': context.get('season', 'unknown')}
                })
        
        else:
            # Population concerns
            result.feedback_type = FeedbackType.INCORRECT
            result.is_valid = False
            result.confidence_score = 0.3
            result.success_message = f"Population assessment indicates potential concerns"
            
            result.issues.append({
                'type': 'population_concerns',
                'severity': 'high',
                'message': f'Population density appears {density_assessment}',
                'details': {
                    'density_assessment': density_assessment,
                    'estimated_density': estimated_density,
                    'habitat_quality': habitat_quality
                }
            })
    
    def _assess_survey_quality(self, interpreted_output: Dict[str, Any], 
                             match_results: Dict[str, Any], 
                             context: Dict[str, Any], 
                             result: ValidationResult) -> None:
        """Assess overall survey quality and data reliability."""
        # Check for data quality issues
        metadata = interpreted_output.get('_metadata', {})
        quality_warnings = metadata.get('quality_warnings', [])
        
        for warning in quality_warnings:
            result.issues.append({
                'type': 'data_quality_warning',
                'severity': 'low',
                'message': warning,
                'details': {'source': 'interpreter'}
            })
        
        # Check for ecological anomalies
        anomalies = match_results.get('ecological_anomalies', [])
        for anomaly in anomalies:
            result.issues.append({
                'type': 'ecological_anomaly',
                'severity': anomaly.get('severity', 'medium'),
                'message': anomaly.get('message', 'Ecological anomaly detected'),
                'details': anomaly
            })
        
        # Assess environmental conditions
        weather = interpreted_output.get('environmental_data', {}).get('weather_conditions', {})
        if weather.get('precipitation') in ['heavy_rain', 'snow']:
            result.issues.append({
                'type': 'adverse_weather_conditions',
                'severity': 'medium',
                'message': 'Survey conducted during adverse weather conditions',
                'details': {'weather': weather}
            })
    
    def _generate_recommendations(self, interpreted_output: Dict[str, Any], 
                                match_results: Dict[str, Any], 
                                context: Dict[str, Any], 
                                result: ValidationResult) -> None:
        """Generate actionable recommendations for future surveys."""
        recommendations = []
        
        # Recommendations based on feedback type
        if result.feedback_type == FeedbackType.CORRECT:
            recommendations.append("Continue monitoring this location for population trends")
            recommendations.append("Consider expanding survey area to adjacent habitats")
            
        elif result.feedback_type == FeedbackType.PARTIALLY_CORRECT:
            if result.confidence_score < 0.7:
                recommendations.append("Increase survey duration or frequency for better detection")
                recommendations.append("Consider using audio playback to attract target species")
            
            habitat_suitability = match_results.get('habitat_assessment', {}).get('overall_suitability', 0)
            if habitat_suitability > 0.7:
                recommendations.append("Habitat appears suitable - consider multiple survey visits")
                recommendations.append("Survey during peak activity periods (dawn/dusk)")
        
        else:  # INCORRECT
            recommendations.append("Consider surveying different habitat types")
            recommendations.append("Verify target species presence in the region")
            recommendations.append("Consult local birding records for species distribution")
        
        # Time-based recommendations
        time_of_day = interpreted_output.get('environmental_data', {}).get('temporal_info', {}).get('time_of_day')
        if time_of_day in ['midday', 'night']:
            recommendations.append("Schedule future surveys during dawn or dusk for higher activity")
        
        # Season-based recommendations
        season = interpreted_output.get('environmental_data', {}).get('temporal_info', {}).get('season')
        if season == 'winter':
            recommendations.append("Consider winter feeding stations to attract target species")
        elif season == 'spring':
            recommendations.append("Focus on nesting habitat during breeding season")
        
        # Weather-based recommendations
        weather = interpreted_output.get('environmental_data', {}).get('weather_conditions', {})
        if weather.get('wind_speed', 0) > 10:
            recommendations.append("Avoid surveys during high wind conditions")
        
        result.recommendations = recommendations
    
    def update_validation_criteria(self, survey_type: str, criteria: Dict[str, Any]) -> None:
        """Update validation criteria for a survey type."""
        if survey_type not in self.validation_criteria:
            self.validation_criteria[survey_type] = {}
        self.validation_criteria[survey_type].update(criteria)
        self.logger.info(f"Updated validation criteria for {survey_type}")
    
    def get_validation_criteria(self) -> Dict[str, Any]:
        """Get current validation criteria."""
        return self.validation_criteria.copy()

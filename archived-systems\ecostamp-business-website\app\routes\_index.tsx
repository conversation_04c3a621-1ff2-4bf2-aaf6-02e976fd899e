import type { MetaFunction } from "@remix-run/node";
import { <PERSON> } from "@remix-run/react";
import { motion } from "framer-motion";
import { 
  Shield, 
  Zap, 
  Users, 
  Globe, 
  CheckCircle, 
  ArrowRight,
  Star,
  TrendingUp,
  Lock,
  Award
} from "lucide-react";

import { Header } from "~/components/layout/Header";
import { Footer } from "~/components/layout/Footer";
import { Button } from "~/components/ui/Button";
import { PricingTable } from "~/components/pricing/PricingTable";
import { TestimonialCard } from "~/components/ui/TestimonialCard";
import { FeatureCard } from "~/components/ui/FeatureCard";
import { StatsCard } from "~/components/ui/StatsCard";

export const meta: MetaFunction = () => {
  return [
    { title: "EcoStamp - The Universal Digital Trust Platform" },
    { 
      name: "description", 
      content: "Join 10,000+ creators, professionals, and enterprises using EcoStamp for digital trust, content verification, and provenance tracking. Start free today." 
    },
  ];
};

const fadeInUp = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.6 }
};

const staggerContainer = {
  animate: {
    transition: {
      staggerChildren: 0.1
    }
  }
};

export default function Index() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-white via-primary-50/30 to-accent-50/20">
      <Header />
      
      {/* Hero Section */}
      <section className="relative overflow-hidden pt-20 pb-16 sm:pt-24 sm:pb-20">
        <div className="absolute inset-0 bg-hero-pattern opacity-40"></div>
        <div className="relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <motion.div 
            className="text-center"
            initial="initial"
            animate="animate"
            variants={staggerContainer}
          >
            <motion.div variants={fadeInUp} className="mb-8">
              <span className="inline-flex items-center rounded-full bg-primary-100 px-4 py-2 text-sm font-medium text-primary-800">
                <Star className="mr-2 h-4 w-4" />
                Trusted by 10,000+ users worldwide
              </span>
            </motion.div>
            
            <motion.h1 
              variants={fadeInUp}
              className="mx-auto max-w-4xl text-4xl font-bold tracking-tight text-secondary-900 sm:text-6xl lg:text-7xl"
            >
              The Universal Platform for{" "}
              <span className="bg-gradient-to-r from-primary-600 to-accent-500 bg-clip-text text-transparent">
                Digital Trust
              </span>
            </motion.h1>
            
            <motion.p 
              variants={fadeInUp}
              className="mx-auto mt-6 max-w-2xl text-lg leading-8 text-secondary-600 sm:text-xl"
            >
              Verify, protect, and track your digital content with blockchain-powered provenance. 
              Trusted by creators, professionals, and enterprises worldwide.
            </motion.p>
            
            <motion.div 
              variants={fadeInUp}
              className="mt-10 flex flex-col items-center justify-center gap-4 sm:flex-row"
            >
              <Button asChild size="lg" className="w-full sm:w-auto">
                <Link to="/signup">
                  Start Free Today
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
              <Button asChild variant="outline" size="lg" className="w-full sm:w-auto">
                <Link to="/demo">
                  Watch Demo
                </Link>
              </Button>
            </motion.div>
            
            <motion.div 
              variants={fadeInUp}
              className="mt-12 flex items-center justify-center space-x-8 text-sm text-secondary-500"
            >
              <div className="flex items-center">
                <CheckCircle className="mr-2 h-4 w-4 text-primary-500" />
                No credit card required
              </div>
              <div className="flex items-center">
                <CheckCircle className="mr-2 h-4 w-4 text-primary-500" />
                Free forever plan
              </div>
              <div className="flex items-center">
                <CheckCircle className="mr-2 h-4 w-4 text-primary-500" />
                Setup in 2 minutes
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-white/50 backdrop-blur-sm">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <motion.div 
            className="grid grid-cols-2 gap-8 md:grid-cols-4"
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={staggerContainer}
          >
            <StatsCard
              number="10,000+"
              label="Active Users"
              icon={Users}
              trend="+127%"
            />
            <StatsCard
              number="2.5M+"
              label="Content Verified"
              icon={Shield}
              trend="+89%"
            />
            <StatsCard
              number="99.9%"
              label="Uptime"
              icon={Zap}
              trend="Consistent"
            />
            <StatsCard
              number="150+"
              label="Countries"
              icon={Globe}
              trend="+23%"
            />
          </motion.div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <motion.div 
            className="text-center"
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={fadeInUp}
          >
            <h2 className="text-3xl font-bold tracking-tight text-secondary-900 sm:text-4xl">
              Everything you need for digital trust
            </h2>
            <p className="mt-4 text-lg text-secondary-600">
              Comprehensive tools for creators, professionals, and enterprises
            </p>
          </motion.div>
          
          <motion.div 
            className="mt-16 grid gap-8 md:grid-cols-2 lg:grid-cols-3"
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={staggerContainer}
          >
            <FeatureCard
              icon={Shield}
              title="Content Verification"
              description="Instantly verify the authenticity and origin of any digital content with blockchain-powered certificates."
              features={["Blockchain certificates", "Tamper-proof verification", "Real-time validation"]}
            />
            <FeatureCard
              icon={TrendingUp}
              title="Analytics & Insights"
              description="Track your content's journey with detailed analytics and performance insights."
              features={["Usage analytics", "Performance tracking", "Engagement metrics"]}
            />
            <FeatureCard
              icon={Lock}
              title="Enterprise Security"
              description="Bank-grade security with advanced encryption and compliance features."
              features={["End-to-end encryption", "SOC 2 compliance", "GDPR ready"]}
            />
            <FeatureCard
              icon={Users}
              title="Team Collaboration"
              description="Work together seamlessly with advanced team management and sharing features."
              features={["Team workspaces", "Role-based access", "Collaboration tools"]}
            />
            <FeatureCard
              icon={Zap}
              title="API Integration"
              description="Integrate EcoStamp into your existing workflows with our powerful API."
              features={["RESTful API", "Webhooks", "SDKs available"]}
            />
            <FeatureCard
              icon={Award}
              title="Certification & Badges"
              description="Earn and display trust badges that build credibility with your audience."
              features={["Trust badges", "Verification certificates", "Public profiles"]}
            />
          </motion.div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 bg-secondary-50">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <motion.div 
            className="text-center"
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={fadeInUp}
          >
            <h2 className="text-3xl font-bold tracking-tight text-secondary-900 sm:text-4xl">
              Trusted by industry leaders
            </h2>
            <p className="mt-4 text-lg text-secondary-600">
              See what our customers are saying about EcoStamp
            </p>
          </motion.div>
          
          <motion.div 
            className="mt-16 grid gap-8 md:grid-cols-2 lg:grid-cols-3"
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={staggerContainer}
          >
            <TestimonialCard
              quote="EcoStamp has revolutionized how we handle content verification. The blockchain integration gives us complete confidence in our digital assets."
              author="Sarah Chen"
              role="Creative Director"
              company="Digital Arts Studio"
              avatar="/avatars/sarah.jpg"
              rating={5}
            />
            <TestimonialCard
              quote="The API integration was seamless, and the team collaboration features have improved our workflow significantly. Highly recommended!"
              author="Marcus Rodriguez"
              role="CTO"
              company="TechFlow Inc"
              avatar="/avatars/marcus.jpg"
              rating={5}
            />
            <TestimonialCard
              quote="As a freelance photographer, EcoStamp helps me protect my work and build trust with clients. The verification process is incredibly fast."
              author="Emma Thompson"
              role="Professional Photographer"
              company="Independent"
              avatar="/avatars/emma.jpg"
              rating={5}
            />
          </motion.div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-20" id="pricing">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <motion.div 
            className="text-center"
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={fadeInUp}
          >
            <h2 className="text-3xl font-bold tracking-tight text-secondary-900 sm:text-4xl">
              Choose your plan
            </h2>
            <p className="mt-4 text-lg text-secondary-600">
              Start free and scale as you grow. No hidden fees, cancel anytime.
            </p>
          </motion.div>
          
          <motion.div 
            className="mt-16"
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={fadeInUp}
          >
            <PricingTable />
          </motion.div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-primary-600 to-accent-500">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <motion.div 
            className="text-center"
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={fadeInUp}
          >
            <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
              Ready to get started?
            </h2>
            <p className="mt-4 text-lg text-primary-100">
              Join thousands of creators and professionals who trust EcoStamp with their digital content.
            </p>
            <div className="mt-8 flex flex-col items-center justify-center gap-4 sm:flex-row">
              <Button asChild size="lg" variant="secondary" className="w-full sm:w-auto">
                <Link to="/signup">
                  Start Free Today
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
              <Button asChild variant="outline" size="lg" className="w-full sm:w-auto border-white text-white hover:bg-white hover:text-primary-600">
                <Link to="/contact">
                  Contact Sales
                </Link>
              </Button>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </div>
  );
}

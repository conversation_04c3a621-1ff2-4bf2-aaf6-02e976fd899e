import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
dotenv.config({ path: path.join(__dirname, '../../.env') });

export const config = {
  // API Configuration
  apis: {
    openai: {
      apiKey: process.env.OPENAI_API_KEY,
      baseURL: process.env.OPENAI_BASE_URL || 'https://api.openai.com/v1',
      model: process.env.CHATGPT_MODEL || 'gpt-4-turbo-preview'
    },
    perplexity: {
      apiKey: process.env.PERPLEXITY_API_KEY,
      baseURL: process.env.PERPLEXITY_BASE_URL || 'https://api.perplexity.ai',
      model: 'pplx-7b-online'
    },
    claude: {
      apiKey: process.env.CLAUDE_API_KEY,
      baseURL: process.env.CLAUDE_BASE_URL || 'https://api.anthropic.com',
      model: process.env.CLAUDE_MODEL || 'claude-3-sonnet-20240229',
      maxTokens: 4096
    },
    gemini: {
      apiKey: process.env.GEMINI_API_KEY,
      baseURL: process.env.GEMINI_BASE_URL || 'https://generativelanguage.googleapis.com',
      model: process.env.GEMINI_MODEL || 'gemini-pro'
    },
    mistral: {
      apiKey: process.env.MISTRAL_API_KEY,
      baseURL: process.env.MISTRAL_BASE_URL || 'https://api.mistral.ai/v1',
      model: process.env.MISTRAL_MODEL || 'mistral-large-latest'
    },
    cohere: {
      apiKey: process.env.COHERE_API_KEY,
      baseURL: process.env.COHERE_BASE_URL || 'https://api.cohere.ai/v1',
      model: process.env.COHERE_MODEL || 'command-r-plus'
    },
    huggingface: {
      apiKey: process.env.HUGGINGFACE_API_KEY,
      baseURL: process.env.HUGGINGFACE_BASE_URL || 'https://api-inference.huggingface.co',
      model: process.env.LLAMA_MODEL || 'meta-llama/Llama-2-70b-chat-hf'
    },
    replicate: {
      apiKey: process.env.REPLICATE_API_KEY,
      baseURL: process.env.REPLICATE_BASE_URL || 'https://api.replicate.com/v1',
      model: 'meta/llama-2-70b-chat'
    },
    together: {
      apiKey: process.env.TOGETHER_API_KEY,
      baseURL: process.env.TOGETHER_BASE_URL || 'https://api.together.xyz/v1',
      model: 'meta-llama/Llama-2-70b-chat-hf'
    },
    groq: {
      apiKey: process.env.GROQ_API_KEY,
      baseURL: process.env.GROQ_BASE_URL || 'https://api.groq.com/openai/v1',
      model: process.env.GROQ_MODEL || 'llama2-70b-4096'
    },
    fireworks: {
      apiKey: process.env.FIREWORKS_API_KEY,
      baseURL: process.env.FIREWORKS_BASE_URL || 'https://api.fireworks.ai/inference/v1',
      model: 'accounts/fireworks/models/llama-v2-70b-chat'
    },
    azure: {
      apiKey: process.env.AZURE_OPENAI_API_KEY,
      endpoint: process.env.AZURE_OPENAI_ENDPOINT,
      model: 'gpt-4'
    }
  },

  // Rate Limiting
  rateLimiting: {
    requestsPerMinute: parseInt(process.env.RATE_LIMIT_REQUESTS_PER_MINUTE) || 60,
    burstSize: parseInt(process.env.RATE_LIMIT_BURST_SIZE) || 10
  },

  // Search Configuration
  search: {
    similarityThreshold: parseFloat(process.env.SEARCH_SIMILARITY_THRESHOLD) || 0.7,
    maxThreadsToRetrieve: parseInt(process.env.MAX_THREADS_TO_RETRIEVE) || 50,
    maxContextLength: parseInt(process.env.MAX_CONTEXT_LENGTH) || 100000,
    embeddingModel: 'text-embedding-ada-002'
  },

  // Logging
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    filePath: process.env.LOG_FILE_PATH || './logs/orchestrator.log'
  },

  // Web Interface
  web: {
    port: parseInt(process.env.WEB_PORT) || 3000,
    host: process.env.WEB_HOST || 'localhost'
  },

  // Storage Paths
  storage: {
    threads: process.env.THREADS_STORAGE_PATH || './data/threads',
    results: process.env.RESULTS_STORAGE_PATH || './data/results',
    cache: process.env.CACHE_STORAGE_PATH || './data/cache'
  },

  // Automation
  automation: {
    scheduleEnabled: process.env.AUTO_SCHEDULE_ENABLED === 'true',
    scheduleCron: process.env.AUTO_SCHEDULE_CRON || '0 */6 * * *'
  },

  // Browser Automation
  browser: {
    headless: process.env.BROWSER_HEADLESS !== 'false',
    timeout: parseInt(process.env.BROWSER_TIMEOUT) || 30000
  },

  // Default Settings
  defaults: {
    targetLLM: process.env.DEFAULT_TARGET_LLM || 'claude'
  },

  // Platform Configuration
  platforms: {
    enabledSources: (process.env.ENABLED_SOURCES || 'chatgpt,perplexity,claude,gemini').split(','),
    retrievalMethods: (process.env.THREAD_RETRIEVAL_METHODS || 'api,browser,export').split(','),
    supportedLLMs: [
      'claude', 'gemini', 'openai', 'mistral', 'cohere',
      'huggingface', 'replicate', 'together', 'groq', 'fireworks', 'azure'
    ]
  }
};

// Validation
export function validateConfig() {
  const enabledSources = config.platforms.enabledSources;
  const apiKeyMap = {
    'chatgpt': 'OPENAI_API_KEY',
    'openai': 'OPENAI_API_KEY',
    'perplexity': 'PERPLEXITY_API_KEY',
    'claude': 'CLAUDE_API_KEY',
    'gemini': 'GEMINI_API_KEY',
    'mistral': 'MISTRAL_API_KEY',
    'cohere': 'COHERE_API_KEY',
    'huggingface': 'HUGGINGFACE_API_KEY',
    'replicate': 'REPLICATE_API_KEY',
    'together': 'TOGETHER_API_KEY',
    'groq': 'GROQ_API_KEY',
    'fireworks': 'FIREWORKS_API_KEY',
    'azure': 'AZURE_OPENAI_API_KEY'
  };

  const missing = [];

  // Check for required API keys based on enabled sources
  enabledSources.forEach(source => {
    const envKey = apiKeyMap[source.toLowerCase()];
    if (envKey && !process.env[envKey]) {
      missing.push(envKey);
    }
  });

  // Always require at least one target LLM key
  const targetLLMKeys = ['CLAUDE_API_KEY', 'GEMINI_API_KEY', 'OPENAI_API_KEY'];
  const hasTargetLLM = targetLLMKeys.some(key => process.env[key]);

  if (!hasTargetLLM) {
    missing.push('At least one target LLM API key (Claude, Gemini, or OpenAI)');
  }

  if (missing.length > 0) {
    console.warn(`⚠️  Missing API keys for some platforms: ${missing.join(', ')}`);
    console.warn('The system will work with available platforms only.');
  }

  return true;
}

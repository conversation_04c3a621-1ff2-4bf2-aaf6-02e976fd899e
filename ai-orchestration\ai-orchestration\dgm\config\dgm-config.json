{"evolution": {"populationSize": 20, "maxGenerations": 100, "targetFitness": 0.95, "offspringCount": 10, "mutationRate": 0.3, "crossoverRate": 0.7, "elitismRate": 0.1, "diversityThreshold": 0.8, "stagnationLimit": 10}, "genetics": {"selectionMethod": "tournament", "tournamentSize": 3, "mutationStrategies": ["error_handling", "performance_optimization", "new_feature", "algorithm_improvement", "code_refactoring"], "crossoverStrategies": ["single_point", "two_point", "uniform", "semantic"]}, "evaluation": {"benchmarkSuites": ["swe-bench", "polyglot", "custom-orchestration", "performance-tests"], "fitnessWeights": {"performance": 0.4, "reliability": 0.3, "functionality": 0.2, "safety": 0.1}, "validationCriteria": {"syntaxCheck": true, "securityScan": true, "performanceThreshold": 0.8, "reliabilityThreshold": 0.9, "safetyThreshold": 0.95}, "timeoutLimits": {"agentExecution": 300000, "benchmarkSuite": 1800000, "validation": 60000}}, "archive": {"storageType": "git", "maxVersions": 1000, "compressionEnabled": true, "backupInterval": 86400000, "genealogyDepth": 10, "performanceHistoryLength": 100}, "execution": {"sandboxDir": "C:\\Users\\<USER>\\Time_Stamp_Project\\ai-orchestration\\dgm-sandbox", "isolationLevel": "process", "resourceLimits": {"memory": "512MB", "cpu": "1", "timeout": 300000}, "allowedModules": ["fs", "path", "crypto", "util", "events", "stream"], "blockedModules": ["child_process", "cluster", "dgram", "net", "tls"]}, "safety": {"humanApprovalRequired": true, "approvalThreshold": 0.8, "rollbackOnFailure": true, "maxConsecutiveFailures": 3, "quarantineEnabled": true, "auditLogging": true, "changeReviewRequired": true}, "monitoring": {"metricsCollection": true, "performanceTracking": true, "errorTracking": true, "evolutionHistory": true, "dashboardEnabled": true, "alerting": {"enabled": true, "channels": ["console", "file"], "thresholds": {"fitnessDecline": 0.1, "errorRate": 0.05, "performanceDegradation": 0.2}}}, "integration": {"augmentCode": {"enabled": true, "apiEndpoint": "http://localhost:3001", "timeout": 30000}, "orchestrationSystem": {"enabled": true, "backupBeforeModification": true, "testBeforeDeployment": true}, "externalBenchmarks": {"swebench": {"enabled": false, "endpoint": "https://api.swebench.com"}, "polyglot": {"enabled": false, "endpoint": "https://api.polyglot.com"}}}, "ui": {"webDashboard": {"enabled": true, "port": 3002, "autoRefresh": 5000}, "cli": {"enabled": true, "colorOutput": true, "verboseLogging": false}, "api": {"enabled": true, "port": 3003, "authentication": false}}}
"use strict";
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.setSocketError = exports.socketDisconnected = exports.setConnectionCount = exports.setSocket = exports.disconnectSocket = exports.connectSocket = void 0;
const toolkit_1 = require("@reduxjs/toolkit");
const socket_io_client_1 = require("socket.io-client");
const initialState = {
    socket: null,
    isConnected: false,
    connectionCount: 0,
    error: null,
};
exports.connectSocket = (0, toolkit_1.createAsyncThunk)('socket/connect', async (_, { getState }) => {
    const state = getState();
    const token = state.auth.token;
    if (!token) {
        throw new Error('No authentication token');
    }
    const socket = (0, socket_io_client_1.io)('http://localhost:3001', {
        auth: {
            token,
        },
        transports: ['websocket', 'polling'],
    });
    return new Promise((resolve, reject) => {
        socket.on('connect', () => {
            console.log('Socket connected:', socket.id);
            resolve(socket);
        });
        socket.on('connect_error', (error) => {
            console.error('Socket connection error:', error);
            reject(error);
        });
        socket.on('disconnect', (reason) => {
            console.log('Socket disconnected:', reason);
        });
    });
});
exports.disconnectSocket = (0, toolkit_1.createAsyncThunk)('socket/disconnect', async (_, { getState }) => {
    const state = getState();
    const socket = state.socket.socket;
    if (socket) {
        socket.disconnect();
    }
});
const socketSlice = (0, toolkit_1.createSlice)({
    name: 'socket',
    initialState,
    reducers: {
        setSocket: (state, action) => {
            state.socket = action.payload;
            state.isConnected = true;
            state.error = null;
        },
        setConnectionCount: (state, action) => {
            state.connectionCount = action.payload;
        },
        socketDisconnected: (state) => {
            state.socket = null;
            state.isConnected = false;
        },
        setSocketError: (state, action) => {
            state.error = action.payload;
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(exports.connectSocket.fulfilled, (state, action) => {
            state.socket = action.payload;
            state.isConnected = true;
            state.error = null;
        })
            .addCase(exports.connectSocket.rejected, (state, action) => {
            state.socket = null;
            state.isConnected = false;
            state.error = action.error.message || 'Connection failed';
        })
            .addCase(exports.disconnectSocket.fulfilled, (state) => {
            state.socket = null;
            state.isConnected = false;
            state.error = null;
        });
    },
});
_a = socketSlice.actions, exports.setSocket = _a.setSocket, exports.setConnectionCount = _a.setConnectionCount, exports.socketDisconnected = _a.socketDisconnected, exports.setSocketError = _a.setSocketError;
exports.default = socketSlice.reducer;

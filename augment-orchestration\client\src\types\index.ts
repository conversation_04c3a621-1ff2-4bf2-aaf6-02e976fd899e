// Client-side types for the Augment Orchestration Platform

export interface User {
  id: string;
  email: string;
  username: string;
  role: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface MetaOrchestrator {
  id: string;
  name: string;
  description?: string;
  isActive: boolean;
  metadata?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface SubOrchestrator {
  id: string;
  name: string;
  description?: string;
  parentId: string;
  isActive: boolean;
  metadata?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Agent {
  id: string;
  name: string;
  description?: string;
  type: string;
  vendor: string;
  capabilities: string;
  roles: string;
  isActive: boolean;
  metadata?: string;
  fitnessScore: number;
  currentLoad: number;
  maxConcurrentTasks: number;
  performanceMetrics: any;
  createdAt: Date;
  updatedAt: Date;
}

export interface AgentRole {
  id: string;
  name: string;
  description: string;
  requiredCapabilities: string[];
  optionalCapabilities: string[];
}

export interface Tunnel {
  id: string;
  name: string;
  description?: string;
  tags: string;
  isActive: boolean;
  metadata?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface WorkflowTemplate {
  id: string;
  name: string;
  description?: string;
  stages: string;
  metadata?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface WorkflowExecution {
  id: string;
  status: string;
  startedAt: Date;
  endedAt?: Date;
  result?: string;
  metadata?: string;
  templateId: string;
  executorId: string;
}

export interface EvolutionVariant {
  id: string;
  agentId: string;
  generation: number;
  mutations: string;
  fitnessScore: number;
  createdAt: Date;
}

export interface AuditLog {
  id: string;
  action: string;
  entityId: string;
  entityType: string;
  oldValue?: string;
  newValue?: string;
  metadata?: string;
  timestamp: Date;
  userId: string;
}

export interface SharedContext {
  id: string;
  key: string;
  value: string;
  tags: string;
  expiresAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

// API Response types
export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Auth types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  email: string;
  username: string;
  password: string;
}

export interface AuthResponse {
  user: User;
  token: string;
}

// Socket types
export interface SocketMessage {
  type: string;
  payload: any;
  timestamp: Date;
}

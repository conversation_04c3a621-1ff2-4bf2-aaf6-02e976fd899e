{"name": "backend", "version": "1.0.0", "main": "index.js", "type": "module", "scripts": {"start": "node server.js", "dev": "node server.js", "test": "echo \"Error: no test specified\" && exit 1", "security:audit": "npm audit --audit-level=moderate", "security:audit-fix": "npm audit fix", "security:snyk": "snyk test", "security:snyk-monitor": "snyk monitor", "security:licenses": "license-checker --summary", "security:licenses-detailed": "license-checker --csv --out licenses-report.csv", "security:sbom": "cyclonedx-npm --output-file sbom.json", "security:sbom-xml": "cyclonedx-npm --output-format xml --output-file sbom.xml", "security:eslint": "eslint . --ext .js,.ts --config .eslintrc.security.js", "security:full-scan": "npm run security:audit && npm run security:licenses && npm run security:sbom && npm run security:eslint", "security:report": "node scripts/security-report.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@ljharb/tsconfig": "^0.3.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.2", "axios": "^1.4.0"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.2", "@types/node": "^22.15.30", "ts-node": "^10.9.2", "typescript": "^5.8.3", "snyk": "^1.1291.0", "license-checker": "^25.0.1", "@cyclonedx/cyclonedx-npm": "^1.19.3", "eslint": "^8.57.0", "eslint-plugin-security": "^1.7.1", "@eslint/js": "^8.57.0", "retire": "^4.0.3", "audit-ci": "^6.6.1", "dependency-check": "^4.1.0"}}
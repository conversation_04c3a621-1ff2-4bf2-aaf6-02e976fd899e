"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.orchestratorRoutes = void 0;
const express_1 = require("express");
const client_1 = require("@prisma/client");
const zod_1 = require("zod");
const errorHandler_1 = require("../middleware/errorHandler");
const logger_1 = require("../utils/logger");
const router = (0, express_1.Router)();
exports.orchestratorRoutes = router;
const prisma = new client_1.PrismaClient();
// Validation schemas
const createOrchestratorSchema = zod_1.z.object({
    name: zod_1.z.string().min(1, 'Name is required').max(100, 'Name too long'),
    description: zod_1.z.string().optional(),
    version: zod_1.z.string().default('1.0.0'),
    metadata: zod_1.z.record(zod_1.z.any()).optional(),
});
const updateOrchestratorSchema = zod_1.z.object({
    name: zod_1.z.string().min(1, 'Name is required').max(100, 'Name too long').optional(),
    description: zod_1.z.string().optional(),
    version: zod_1.z.string().optional(),
    isActive: zod_1.z.boolean().optional(),
    metadata: zod_1.z.record(zod_1.z.any()).optional(),
});
// Get all meta-orchestrators
router.get('/', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const search = req.query.search;
    const isActive = req.query.isActive === 'true' ? true : req.query.isActive === 'false' ? false : undefined;
    const skip = (page - 1) * limit;
    const where = {};
    if (search) {
        where.OR = [
            { name: { contains: search, mode: 'insensitive' } },
            { description: { contains: search, mode: 'insensitive' } },
        ];
    }
    if (isActive !== undefined) {
        where.isActive = isActive;
    }
    const [orchestrators, total] = await Promise.all([
        prisma.metaOrchestrator.findMany({
            where,
            skip,
            take: limit,
            include: {
                creator: {
                    select: {
                        id: true,
                        username: true,
                        email: true,
                    },
                },
                subOrchestrators: {
                    select: {
                        id: true,
                        name: true,
                        domain: true,
                        isActive: true,
                    },
                },
                _count: {
                    select: {
                        subOrchestrators: true,
                        workflowTemplates: true,
                        tunnels: true,
                    },
                },
            },
            orderBy: {
                createdAt: 'desc',
            },
        }),
        prisma.metaOrchestrator.count({ where }),
    ]);
    res.json({
        success: true,
        data: orchestrators,
        pagination: {
            page,
            limit,
            total,
            totalPages: Math.ceil(total / limit),
        },
    });
}));
// Get single meta-orchestrator
router.get('/:id', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const orchestrator = await prisma.metaOrchestrator.findUnique({
        where: { id },
        include: {
            creator: {
                select: {
                    id: true,
                    username: true,
                    email: true,
                },
            },
            subOrchestrators: {
                include: {
                    agents: {
                        select: {
                            id: true,
                            agentId: true,
                            name: true,
                            vendor: true,
                            capabilities: true,
                            roles: true,
                            isActive: true,
                            fitnessScore: true,
                        },
                    },
                    _count: {
                        select: {
                            agents: true,
                            tunnelsFrom: true,
                            tunnelsTo: true,
                        },
                    },
                },
            },
            workflowTemplates: {
                select: {
                    id: true,
                    name: true,
                    description: true,
                    stages: true,
                    createdAt: true,
                },
            },
            tunnels: {
                select: {
                    id: true,
                    name: true,
                    description: true,
                    tags: true,
                    isActive: true,
                    fromSubOrchestratorId: true,
                    toSubOrchestratorId: true,
                    fromAgentId: true,
                    toAgentId: true,
                },
            },
        },
    });
    if (!orchestrator) {
        throw new errorHandler_1.AppError('Meta-orchestrator not found', 404);
    }
    res.json({
        success: true,
        data: orchestrator,
    });
}));
// Create new meta-orchestrator
router.post('/', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const data = createOrchestratorSchema.parse(req.body);
    const orchestrator = await prisma.metaOrchestrator.create({
        data: {
            ...data,
            creatorId: req.user.id,
        },
        include: {
            creator: {
                select: {
                    id: true,
                    username: true,
                    email: true,
                },
            },
        },
    });
    logger_1.logger.info(`Meta-orchestrator created: ${orchestrator.name} by ${req.user.username}`);
    res.status(201).json({
        success: true,
        data: orchestrator,
        message: 'Meta-orchestrator created successfully',
    });
}));
// Update meta-orchestrator
router.put('/:id', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const data = updateOrchestratorSchema.parse(req.body);
    // Check if orchestrator exists and user has permission
    const existingOrchestrator = await prisma.metaOrchestrator.findUnique({
        where: { id },
        select: { creatorId: true, name: true },
    });
    if (!existingOrchestrator) {
        throw new errorHandler_1.AppError('Meta-orchestrator not found', 404);
    }
    if (existingOrchestrator.creatorId !== req.user.id && req.user.role !== 'ADMIN') {
        throw new errorHandler_1.AppError('Permission denied', 403);
    }
    const orchestrator = await prisma.metaOrchestrator.update({
        where: { id },
        data,
        include: {
            creator: {
                select: {
                    id: true,
                    username: true,
                    email: true,
                },
            },
        },
    });
    logger_1.logger.info(`Meta-orchestrator updated: ${orchestrator.name} by ${req.user.username}`);
    res.json({
        success: true,
        data: orchestrator,
        message: 'Meta-orchestrator updated successfully',
    });
}));
// Delete meta-orchestrator
router.delete('/:id', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    // Check if orchestrator exists and user has permission
    const existingOrchestrator = await prisma.metaOrchestrator.findUnique({
        where: { id },
        select: { creatorId: true, name: true },
    });
    if (!existingOrchestrator) {
        throw new errorHandler_1.AppError('Meta-orchestrator not found', 404);
    }
    if (existingOrchestrator.creatorId !== req.user.id && req.user.role !== 'ADMIN') {
        throw new errorHandler_1.AppError('Permission denied', 403);
    }
    await prisma.metaOrchestrator.delete({
        where: { id },
    });
    logger_1.logger.info(`Meta-orchestrator deleted: ${existingOrchestrator.name} by ${req.user.username}`);
    res.json({
        success: true,
        message: 'Meta-orchestrator deleted successfully',
    });
}));

import axios from 'axios';
import { config } from '../config/index.js';
import { logger, logRequest, logResponse } from '../utils/logger.js';
import { RateLimiterMemory } from 'rate-limiter-flexible';

class HuggingFaceClient {
  constructor() {
    this.baseURL = config.apis.huggingface.baseURL;
    this.apiKey = config.apis.huggingface.apiKey;
    
    this.client = axios.create({
      baseURL: this.baseURL,
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json'
      },
      timeout: 120000 // HuggingFace can be slower
    });

    // Rate limiter
    this.rateLimiter = new RateLimiterMemory({
      keyGenerator: () => 'huggingface',
      points: config.rateLimiting.requestsPerMinute,
      duration: 60,
      blockDuration: 60
    });
  }

  async rateLimit() {
    try {
      await this.rateLimiter.consume('huggingface');
    } catch (rejRes) {
      const waitTime = Math.round(rejRes.msBeforeNext / 1000);
      logger.warn(`HuggingFace rate limit hit, waiting ${waitTime} seconds`);
      await new Promise(resolve => setTimeout(resolve, rejRes.msBeforeNext));
    }
  }

  async generateCompletion(messages, options = {}) {
    await this.rateLimit();
    logRequest('HuggingFace', 'generateCompletion', { 
      messageCount: messages.length,
      model: options.model || config.apis.huggingface.model
    });

    try {
      // Convert messages to prompt format for HuggingFace
      const prompt = this.convertMessagesToPrompt(messages);
      const model = options.model || config.apis.huggingface.model;
      
      const response = await this.client.post(`/models/${model}`, {
        inputs: prompt,
        parameters: {
          max_new_tokens: options.maxTokens || 4096,
          temperature: options.temperature || 0.7,
          do_sample: true,
          return_full_text: false
        },
        options: {
          wait_for_model: true
        }
      });

      logResponse('HuggingFace', 'generateCompletion', true);
      
      // Handle different response formats
      const result = Array.isArray(response.data) ? response.data[0] : response.data;
      return result.generated_text || result.text || JSON.stringify(result);
    } catch (error) {
      logResponse('HuggingFace', 'generateCompletion', false, { error: error.message });
      throw error;
    }
  }

  async analyzeThreads(mergedThreads, task = 'code_generation', options = {}) {
    await this.rateLimit();
    logRequest('HuggingFace', 'analyzeThreads', { 
      task,
      threadsLength: mergedThreads.length
    });

    try {
      const systemPrompt = this.getSystemPromptForTask(task);
      const threadPrompt = this.formatThreadsForAnalysis(mergedThreads, task);
      const fullPrompt = `${systemPrompt}\n\n${threadPrompt}\n\nAnalysis:`;
      
      const model = options.model || config.apis.huggingface.model;
      
      const response = await this.client.post(`/models/${model}`, {
        inputs: fullPrompt,
        parameters: {
          max_new_tokens: options.maxTokens || 4096,
          temperature: options.temperature || 0.3,
          do_sample: true,
          return_full_text: false
        },
        options: {
          wait_for_model: true
        }
      });

      logResponse('HuggingFace', 'analyzeThreads', true);
      
      const result = Array.isArray(response.data) ? response.data[0] : response.data;
      const analysis = result.generated_text || result.text || JSON.stringify(result);
      
      return {
        analysis,
        task,
        timestamp: new Date().toISOString(),
        model
      };
    } catch (error) {
      logResponse('HuggingFace', 'analyzeThreads', false, { error: error.message });
      throw error;
    }
  }

  convertMessagesToPrompt(messages) {
    let prompt = '';
    
    messages.forEach(message => {
      if (message.role === 'system') {
        prompt += `<|system|>\n${message.content}\n`;
      } else if (message.role === 'user') {
        prompt += `<|user|>\n${message.content}\n`;
      } else if (message.role === 'assistant') {
        prompt += `<|assistant|>\n${message.content}\n`;
      }
    });

    prompt += '<|assistant|>\n';
    return prompt;
  }

  getSystemPromptForTask(task) {
    const prompts = {
      code_generation: `You are an expert software developer and code analyst. You will receive merged conversation threads from various AI assistants. Your task is to:

1. Analyze the conversations to understand the technical requirements and context
2. Identify key patterns, solutions, and approaches discussed
3. Generate clean, well-documented, production-ready code based on the insights
4. Provide implementation recommendations and best practices
5. Highlight any potential issues or considerations

Focus on practical, actionable code solutions that incorporate the best ideas from the discussions.`,

      code_analysis: `You are an expert code reviewer and software architect. You will receive merged conversation threads containing code discussions. Your task is to:

1. Analyze the code and technical discussions thoroughly
2. Identify strengths, weaknesses, and potential improvements
3. Suggest architectural improvements and optimizations
4. Point out security considerations and best practices
5. Provide actionable recommendations for code quality

Be thorough but practical in your analysis.`,

      summarization: `You are an expert technical writer and analyst. You will receive merged conversation threads from various AI assistants. Your task is to:

1. Extract and synthesize the key technical insights
2. Identify common themes and solutions across discussions
3. Create a comprehensive summary of findings
4. Highlight actionable next steps and recommendations
5. Organize information in a clear, structured format

Focus on creating value by connecting insights across different conversations.`
    };

    return prompts[task] || prompts.code_generation;
  }

  formatThreadsForAnalysis(mergedThreads, task) {
    let prompt = `# Merged Thread Analysis Request\n\n`;
    prompt += `**Task**: ${task}\n`;
    prompt += `**Number of threads**: ${mergedThreads.length}\n`;
    prompt += `**Analysis timestamp**: ${new Date().toISOString()}\n\n`;

    prompt += `## Thread Contents\n\n`;

    mergedThreads.forEach((thread, index) => {
      prompt += `### Thread ${index + 1}: ${thread.source} (${thread.id})\n`;
      prompt += `**Created**: ${thread.created_at}\n`;
      prompt += `**Messages**: ${thread.messages.length}\n\n`;

      thread.messages.forEach((message, msgIndex) => {
        prompt += `**Message ${msgIndex + 1}** (${message.role}):\n`;
        prompt += `${message.content}\n\n`;
      });

      if (thread.highlights && thread.highlights.length > 0) {
        prompt += `**Key Highlights**:\n`;
        thread.highlights.forEach(highlight => {
          prompt += `- ${highlight}\n`;
        });
        prompt += `\n`;
      }

      prompt += `---\n\n`;
    });

    prompt += `## Analysis Request\n\n`;
    prompt += `Please analyze the above merged threads and provide insights based on the specified task. `;
    prompt += `Focus on extracting actionable technical information and generating practical solutions.`;

    return prompt;
  }

  async listConversations(limit = 20) {
    // HuggingFace doesn't have conversation history API
    logger.warn('HuggingFace conversation listing requires browser automation or export');
    return { conversations: [], requiresBrowserAutomation: true };
  }

  async getConversation(conversationId) {
    // Placeholder - would need browser automation
    logger.warn('HuggingFace conversation retrieval requires browser automation');
    return {
      id: conversationId,
      messages: [],
      created_at: new Date().toISOString(),
      source: 'huggingface'
    };
  }

  async getAvailableModels() {
    try {
      const response = await this.client.get('/models?filter=text-generation&sort=downloads&direction=-1&limit=20');
      return response.data.map(model => ({
        id: model.id,
        name: model.id,
        downloads: model.downloads,
        likes: model.likes
      }));
    } catch (error) {
      logger.error('Failed to get HuggingFace models', { error: error.message });
      return [];
    }
  }
}

export default HuggingFaceClient;

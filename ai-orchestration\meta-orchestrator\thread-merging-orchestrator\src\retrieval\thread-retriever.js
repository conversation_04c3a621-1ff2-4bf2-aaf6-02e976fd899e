import fs from 'fs/promises';
import path from 'path';
import { config } from '../config/index.js';
import { logger } from '../utils/logger.js';
import OpenAIClient from '../api/openai-client.js';
import PerplexityClient from '../api/perplexity-client.js';
import BrowserAutomation from './browser-automation.js';

class ThreadRetriever {
  constructor() {
    this.openaiClient = new OpenAIClient();
    this.perplexityClient = new PerplexityClient();
    this.browserAutomation = new BrowserAutomation();
    this.storageDir = config.storage.threads;
    this.ensureStorageDir();
  }

  async ensureStorageDir() {
    try {
      await fs.mkdir(this.storageDir, { recursive: true });
    } catch (error) {
      logger.error('Failed to create storage directory', { error: error.message });
    }
  }

  async retrieveAllThreads(options = {}) {
    const startTime = Date.now();
    logger.info('Starting thread retrieval from all sources');

    try {
      const results = await Promise.allSettled([
        this.retrieveChatGPTThreads(options),
        this.retrievePerplexityThreads(options)
      ]);

      const chatgptThreads = results[0].status === 'fulfilled' ? results[0].value : [];
      const perplexityThreads = results[1].status === 'fulfilled' ? results[1].value : [];

      if (results[0].status === 'rejected') {
        logger.error('Failed to retrieve ChatGPT threads', { error: results[0].reason.message });
      }

      if (results[1].status === 'rejected') {
        logger.error('Failed to retrieve Perplexity threads', { error: results[1].reason.message });
      }

      const allThreads = [...chatgptThreads, ...perplexityThreads];
      
      // Cache the retrieved threads
      await this.cacheThreads(allThreads);

      const duration = Date.now() - startTime;
      logger.info('Thread retrieval completed', {
        totalThreads: allThreads.length,
        chatgptThreads: chatgptThreads.length,
        perplexityThreads: perplexityThreads.length,
        duration: `${duration}ms`
      });

      return allThreads;
    } catch (error) {
      logger.error('Thread retrieval failed', { error: error.message });
      throw error;
    }
  }

  async retrieveChatGPTThreads(options = {}) {
    logger.info('Retrieving ChatGPT threads');
    
    try {
      const limit = options.limit || config.search.maxThreadsToRetrieve;
      const conversations = await this.openaiClient.listConversations(limit);

      if (conversations.requiresBrowserAutomation) {
        logger.info('Using browser automation for ChatGPT thread retrieval');
        return await this.browserAutomation.retrieveChatGPTThreads(options);
      }

      const threads = [];
      for (const conv of conversations.conversations || []) {
        try {
          const fullConversation = await this.openaiClient.getConversation(conv.id);
          threads.push({
            id: conv.id,
            source: 'chatgpt',
            title: conv.title || 'Untitled Conversation',
            created_at: conv.created_at || new Date().toISOString(),
            messages: fullConversation.messages || [],
            metadata: {
              model: conv.model,
              total_tokens: conv.total_tokens
            }
          });
        } catch (error) {
          logger.warn(`Failed to retrieve ChatGPT conversation ${conv.id}`, { error: error.message });
        }
      }

      logger.info(`Retrieved ${threads.length} ChatGPT threads`);
      return threads;
    } catch (error) {
      logger.error('ChatGPT thread retrieval failed', { error: error.message });
      throw error;
    }
  }

  async retrievePerplexityThreads(options = {}) {
    logger.info('Retrieving Perplexity threads');
    
    try {
      const limit = options.limit || config.search.maxThreadsToRetrieve;
      const conversations = await this.perplexityClient.listConversations(limit);

      if (conversations.requiresBrowserAutomation) {
        logger.info('Using browser automation for Perplexity thread retrieval');
        return await this.browserAutomation.retrievePerplexityThreads(options);
      }

      const threads = [];
      for (const conv of conversations.conversations || []) {
        try {
          const fullConversation = await this.perplexityClient.getConversation(conv.id);
          threads.push({
            id: conv.id,
            source: 'perplexity',
            title: conv.title || 'Untitled Thread',
            created_at: conv.created_at || new Date().toISOString(),
            messages: fullConversation.messages || [],
            metadata: {
              sources: conv.sources || [],
              citations: conv.citations || []
            }
          });
        } catch (error) {
          logger.warn(`Failed to retrieve Perplexity conversation ${conv.id}`, { error: error.message });
        }
      }

      logger.info(`Retrieved ${threads.length} Perplexity threads`);
      return threads;
    } catch (error) {
      logger.error('Perplexity thread retrieval failed', { error: error.message });
      throw error;
    }
  }

  async retrieveThreadById(source, threadId) {
    logger.info(`Retrieving specific thread: ${source}/${threadId}`);

    try {
      let thread;
      
      if (source === 'chatgpt') {
        const conversation = await this.openaiClient.getConversation(threadId);
        thread = {
          id: threadId,
          source: 'chatgpt',
          title: conversation.title || 'Untitled Conversation',
          created_at: conversation.created_at,
          messages: conversation.messages,
          metadata: conversation.metadata || {}
        };
      } else if (source === 'perplexity') {
        const conversation = await this.perplexityClient.getConversation(threadId);
        thread = {
          id: threadId,
          source: 'perplexity',
          title: conversation.title || 'Untitled Thread',
          created_at: conversation.created_at,
          messages: conversation.messages,
          metadata: conversation.metadata || {}
        };
      } else {
        throw new Error(`Unknown source: ${source}`);
      }

      return thread;
    } catch (error) {
      logger.error(`Failed to retrieve thread ${source}/${threadId}`, { error: error.message });
      throw error;
    }
  }

  async cacheThreads(threads) {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `threads-cache-${timestamp}.json`;
      const filepath = path.join(this.storageDir, filename);

      await fs.writeFile(filepath, JSON.stringify(threads, null, 2));
      logger.info(`Cached ${threads.length} threads to ${filename}`);

      // Also update the latest cache
      const latestPath = path.join(this.storageDir, 'latest-threads.json');
      await fs.writeFile(latestPath, JSON.stringify(threads, null, 2));
    } catch (error) {
      logger.error('Failed to cache threads', { error: error.message });
    }
  }

  async loadCachedThreads() {
    try {
      const latestPath = path.join(this.storageDir, 'latest-threads.json');
      const data = await fs.readFile(latestPath, 'utf8');
      const threads = JSON.parse(data);
      
      logger.info(`Loaded ${threads.length} cached threads`);
      return threads;
    } catch (error) {
      logger.warn('No cached threads found or failed to load', { error: error.message });
      return [];
    }
  }

  async getThreadStats() {
    try {
      const threads = await this.loadCachedThreads();
      const stats = {
        total: threads.length,
        bySources: {},
        byDate: {},
        totalMessages: 0
      };

      threads.forEach(thread => {
        // Count by source
        stats.bySources[thread.source] = (stats.bySources[thread.source] || 0) + 1;
        
        // Count by date (day)
        const date = new Date(thread.created_at).toISOString().split('T')[0];
        stats.byDate[date] = (stats.byDate[date] || 0) + 1;
        
        // Count total messages
        stats.totalMessages += thread.messages.length;
      });

      return stats;
    } catch (error) {
      logger.error('Failed to get thread stats', { error: error.message });
      return { total: 0, bySources: {}, byDate: {}, totalMessages: 0 };
    }
  }
}

export default ThreadRetriever;

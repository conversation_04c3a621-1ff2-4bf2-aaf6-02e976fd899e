@echo off
setlocal enabledelayedexpansion

:: EcoStamp Standalone Test Runner
:: Double-click this file to test all systems

title EcoStamp Test Runner

echo.
echo ========================================
echo    🌱 EcoStamp Test Runner
echo ========================================
echo.
echo This will test all EcoStamp systems
echo without requiring VS Code.
echo.
echo Press any key to start testing...
pause >nul

:: Check if Node.js is available
echo Checking Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo.
    echo ❌ ERROR: Node.js is not installed or not in PATH
    echo.
    echo Please install Node.js from: https://nodejs.org
    echo Then restart this test runner.
    echo.
    pause
    exit /b 1
)

:: Get Node.js version
for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo ✅ Node.js found: %NODE_VERSION%

:: Check if we're in the right directory
if not exist "standalone-test-runner.js" (
    echo.
    echo ❌ ERROR: Test runner script not found
    echo.
    echo Make sure this batch file is in the same directory as:
    echo - standalone-test-runner.js
    echo - core-systems/
    echo - extensions/
    echo.
    pause
    exit /b 1
)

echo ✅ Test runner script found
echo.

:: Run the test runner
echo Starting comprehensive system tests...
echo.
node standalone-test-runner.js

:: Check the exit code
if errorlevel 1 (
    echo.
    echo ❌ Tests completed with errors
    echo.
) else (
    echo.
    echo ✅ Tests completed successfully
    echo.
)

echo.
echo ========================================
echo    Test Runner Complete
echo ========================================
echo.
echo What would you like to do next?
echo.
echo 1. Start EcoStamp Server
echo 2. Open EcoStamp Website
echo 3. View Extension Files
echo 4. Run Tests Again
echo 5. Exit
echo.
set /p choice="Enter your choice (1-5): "

if "%choice%"=="1" goto start_server
if "%choice%"=="2" goto open_website
if "%choice%"=="3" goto view_extensions
if "%choice%"=="4" goto run_again
if "%choice%"=="5" goto end

:: Default to exit if invalid choice
goto end

:start_server
echo.
echo Starting EcoStamp Server...
echo.
if exist "core-systems\EcoStamp\source\server.js" (
    echo Server will start in a new window...
    start "EcoStamp Server" cmd /k "cd core-systems\EcoStamp\source && echo EcoStamp Server running at http://localhost:3000 && echo Press Ctrl+C to stop the server && echo. && node server.js"
    timeout /t 2 /nobreak >nul
    echo.
    echo ✅ Server started in separate window at http://localhost:3000
    echo.
    echo The server will continue running in the separate window.
    echo Close that window or press Ctrl+C in it to stop the server.
    echo.
    echo Would you like to open the website in your browser? (y/n)
    set /p open_browser="Enter choice: "
    if /i "%open_browser%"=="y" (
        echo Opening browser...
        start http://localhost:3000
    )
) else (
    echo ❌ Server file not found: core-systems\EcoStamp\source\server.js
    echo.
    pause
)
goto end

:open_website
echo.
echo Opening EcoStamp Website...
echo.
:: Try to start server in background and open browser
if exist "core-systems\EcoStamp\source\server.js" (
    echo Starting server...
    start "EcoStamp Server" cmd /k "cd core-systems\EcoStamp\source && echo EcoStamp Server running at http://localhost:3000 && echo Press Ctrl+C to stop the server && node server.js"
    timeout /t 3 /nobreak >nul
    echo Opening browser...
    start http://localhost:3000
    echo.
    echo ✅ Server started in separate window
    echo ✅ Browser opened to http://localhost:3000
    echo.
    echo The server will continue running in the separate window.
    echo Close that window or press Ctrl+C in it to stop the server.
    echo.
) else (
    echo ❌ Cannot start server - server.js not found
    echo.
    pause
)
goto end

:view_extensions
echo.
echo Opening Extensions Directory...
echo.
if exist "extensions\universal" (
    start explorer "extensions\universal"
    echo.
    echo Extension files opened in File Explorer
    echo.
    echo To install the extension:
    echo 1. Open Chrome/Edge and go to chrome://extensions/
    echo 2. Enable "Developer mode"
    echo 3. Click "Load unpacked"
    echo 4. Select the "extensions\universal" folder
    echo.
) else (
    echo ❌ Extensions directory not found: extensions\universal
    echo.
)
pause
goto end

:run_again
echo.
echo Running tests again...
echo.
node standalone-test-runner.js
goto end

:end
echo.
echo Thank you for using EcoStamp Test Runner!
echo.
echo For more information:
echo - Website: http://localhost:3000 (when server is running)
echo - GitHub: https://github.com/chris-ai-dev/Time_Stamp_Project
echo.
pause
exit /b 0

"""
Visual Detection Interpreter for Search and Rescue

Interprets visual detection data from drone cameras, classifying detected items
into categories (Clothing, Broken Environment, Personal Items) with confidence scoring.
"""

import json
import logging
import base64
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime
from pathlib import Path
import hashlib


class VisualDetectionInterpreter:
    """
    Interprets visual detection data from Search and Rescue drone operations.
    
    Handles image classification, confidence scoring, and GPS coordinate mapping
    for detected reference items and potential targets.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.supported_formats = [
            'image_detection', 'video_frame', 'lidar_point_cloud', 'thermal_image'
        ]
        
        # Detection categories for Search and Rescue
        self.detection_categories = {
            'clothing': {
                'subcategories': ['fabric_scraps', 'clothing_tags', 'shoes', 'accessories'],
                'confidence_threshold': 0.7,
                'priority': 'high'
            },
            'broken_environment': {
                'subcategories': ['trampled_grass', 'disturbed_bushes', 'snapped_branches', 'footprints'],
                'confidence_threshold': 0.6,
                'priority': 'medium'
            },
            'personal_items': {
                'subcategories': ['toys', 'blankets', 'backpacks', 'stuffed_animals', 'electronics'],
                'confidence_threshold': 0.8,
                'priority': 'high'
            },
            'target_person': {
                'subcategories': ['child', 'adult', 'person_silhouette'],
                'confidence_threshold': 0.9,
                'priority': 'critical'
            }
        }
        
        # Image processing parameters
        self.image_params = {
            'max_image_size_mb': 10,
            'supported_formats': ['jpg', 'jpeg', 'png', 'tiff', 'bmp'],
            'min_resolution': (640, 480),
            'max_resolution': (4096, 3072)
        }
    
    def interpret(self, raw_output: Any, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Interpret visual detection data from drone sensors.
        
        Args:
            raw_output: Raw detection data (image, coordinates, classifications)
            context: Mission context including GPS, timestamp, flight parameters
            
        Returns:
            Normalized detection data with classifications and confidence scores
        """
        try:
            # Determine detection type
            detection_type = context.get('detection_type', 'image_detection')
            mission_id = context.get('mission_id', 'unknown')
            
            # Parse raw detection data
            parsed_data = self._parse_detection_data(raw_output, context)
            
            # Classify detected items
            classifications = self._classify_detections(parsed_data, context)
            
            # Calculate confidence scores
            confidence_scores = self._calculate_confidence_scores(classifications, context)
            
            # Extract GPS and spatial data
            spatial_data = self._extract_spatial_data(parsed_data, context)
            
            # Create normalized output
            normalized_data = {
                'mission_id': mission_id,
                'detection_id': self._generate_detection_id(parsed_data, context),
                'timestamp': datetime.utcnow().isoformat(),
                'detection_type': detection_type,
                'spatial_data': spatial_data,
                'classifications': classifications,
                'confidence_scores': confidence_scores,
                'raw_data_summary': self._create_data_summary(parsed_data),
                '_metadata': {
                    'interpreter_version': '1.0.0',
                    'processing_timestamp': datetime.utcnow().isoformat(),
                    'drone_id': context.get('drone_id', 'unknown'),
                    'flight_altitude': context.get('altitude', 0),
                    'weather_conditions': context.get('weather', 'unknown')
                }
            }
            
            # Validate detection quality
            self._validate_detection_quality(normalized_data)
            
            return normalized_data
            
        except Exception as e:
            self.logger.error(f"Error interpreting visual detection: {str(e)}")
            return {
                'error': True,
                'error_message': str(e),
                'raw_output': str(raw_output)[:500],  # Truncate for logging
                '_metadata': {
                    'interpretation_failed': True,
                    'error_timestamp': datetime.utcnow().isoformat()
                }
            }
    
    def _parse_detection_data(self, raw_output: Any, context: Dict[str, Any]) -> Dict[str, Any]:
        """Parse raw detection data based on input format."""
        if isinstance(raw_output, dict):
            return raw_output
        
        if isinstance(raw_output, str):
            # Try to parse as JSON detection data
            try:
                return json.loads(raw_output)
            except json.JSONDecodeError:
                # Treat as base64 encoded image
                return self._parse_image_data(raw_output, context)
        
        if isinstance(raw_output, (list, tuple)):
            # Handle multiple detections
            return {'detections': list(raw_output)}
        
        # Handle binary image data
        if isinstance(raw_output, bytes):
            return self._parse_binary_image(raw_output, context)
        
        return {'raw_value': raw_output}
    
    def _parse_image_data(self, image_data: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Parse base64 encoded image data."""
        try:
            # Decode base64 image
            image_bytes = base64.b64decode(image_data)
            
            return {
                'image_data': image_data[:100] + '...',  # Truncate for storage
                'image_size_bytes': len(image_bytes),
                'image_hash': hashlib.md5(image_bytes).hexdigest(),
                'format': 'base64_image'
            }
        except Exception as e:
            self.logger.warning(f"Failed to parse image data: {str(e)}")
            return {'image_parse_error': str(e)}
    
    def _parse_binary_image(self, image_bytes: bytes, context: Dict[str, Any]) -> Dict[str, Any]:
        """Parse binary image data."""
        return {
            'image_size_bytes': len(image_bytes),
            'image_hash': hashlib.md5(image_bytes).hexdigest(),
            'format': 'binary_image'
        }
    
    def _classify_detections(self, parsed_data: Dict[str, Any], context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Classify detected items into Search and Rescue categories."""
        classifications = []
        
        # Handle single detection
        if 'detections' not in parsed_data:
            classification = self._classify_single_detection(parsed_data, context)
            if classification:
                classifications.append(classification)
        else:
            # Handle multiple detections
            for i, detection in enumerate(parsed_data.get('detections', [])):
                classification = self._classify_single_detection(detection, context, index=i)
                if classification:
                    classifications.append(classification)
        
        return classifications
    
    def _classify_single_detection(self, detection: Any, context: Dict[str, Any], index: int = 0) -> Optional[Dict[str, Any]]:
        """Classify a single detection item."""
        if isinstance(detection, dict):
            # Extract classification data from detection
            detected_class = detection.get('class', detection.get('category', 'unknown'))
            confidence = detection.get('confidence', detection.get('score', 0.5))
            bounding_box = detection.get('bbox', detection.get('bounding_box'))
            
            # Map to Search and Rescue categories
            category = self._map_to_sar_category(detected_class)
            
            return {
                'detection_index': index,
                'original_class': detected_class,
                'sar_category': category,
                'subcategory': self._determine_subcategory(detected_class, category),
                'confidence': float(confidence),
                'bounding_box': bounding_box,
                'priority': self.detection_categories.get(category, {}).get('priority', 'low'),
                'meets_threshold': confidence >= self.detection_categories.get(category, {}).get('confidence_threshold', 0.5)
            }
        
        return None
    
    def _map_to_sar_category(self, detected_class: str) -> str:
        """Map detected class to Search and Rescue category."""
        class_lower = detected_class.lower()
        
        # Clothing mapping
        clothing_keywords = ['fabric', 'cloth', 'shirt', 'pants', 'shoe', 'sock', 'hat', 'jacket']
        if any(keyword in class_lower for keyword in clothing_keywords):
            return 'clothing'
        
        # Personal items mapping
        personal_keywords = ['backpack', 'bag', 'toy', 'stuffed', 'blanket', 'phone', 'watch']
        if any(keyword in class_lower for keyword in personal_keywords):
            return 'personal_items'
        
        # Environment mapping
        environment_keywords = ['branch', 'grass', 'bush', 'vegetation', 'footprint', 'trail']
        if any(keyword in class_lower for keyword in environment_keywords):
            return 'broken_environment'
        
        # Target person mapping
        person_keywords = ['person', 'child', 'human', 'body', 'face']
        if any(keyword in class_lower for keyword in person_keywords):
            return 'target_person'
        
        return 'unknown'
    
    def _determine_subcategory(self, detected_class: str, category: str) -> str:
        """Determine subcategory within the main category."""
        if category not in self.detection_categories:
            return 'unknown'
        
        subcategories = self.detection_categories[category]['subcategories']
        class_lower = detected_class.lower()
        
        # Find best matching subcategory
        for subcategory in subcategories:
            if subcategory.replace('_', ' ') in class_lower or any(word in class_lower for word in subcategory.split('_')):
                return subcategory
        
        return subcategories[0] if subcategories else 'unknown'
    
    def _calculate_confidence_scores(self, classifications: List[Dict[str, Any]], context: Dict[str, Any]) -> Dict[str, float]:
        """Calculate overall confidence scores for the detection."""
        if not classifications:
            return {'overall_confidence': 0.0}
        
        # Calculate category-specific confidences
        category_confidences = {}
        for classification in classifications:
            category = classification['sar_category']
            confidence = classification['confidence']
            
            if category not in category_confidences:
                category_confidences[category] = []
            category_confidences[category].append(confidence)
        
        # Average confidences per category
        avg_confidences = {}
        for category, confidences in category_confidences.items():
            avg_confidences[f'{category}_confidence'] = sum(confidences) / len(confidences)
        
        # Calculate overall confidence (weighted by priority)
        priority_weights = {'critical': 1.0, 'high': 0.8, 'medium': 0.6, 'low': 0.4}
        weighted_sum = 0.0
        total_weight = 0.0
        
        for classification in classifications:
            priority = classification['priority']
            confidence = classification['confidence']
            weight = priority_weights.get(priority, 0.5)
            
            weighted_sum += confidence * weight
            total_weight += weight
        
        overall_confidence = weighted_sum / total_weight if total_weight > 0 else 0.0
        
        return {
            'overall_confidence': overall_confidence,
            **avg_confidences
        }
    
    def _extract_spatial_data(self, parsed_data: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Extract GPS and spatial information."""
        return {
            'gps_coordinates': {
                'latitude': context.get('latitude', parsed_data.get('lat')),
                'longitude': context.get('longitude', parsed_data.get('lon')),
                'altitude': context.get('altitude', parsed_data.get('alt', 0))
            },
            'drone_position': {
                'heading': context.get('heading', 0),
                'pitch': context.get('pitch', 0),
                'roll': context.get('roll', 0)
            },
            'camera_parameters': {
                'zoom_level': context.get('zoom', 1.0),
                'focal_length': context.get('focal_length'),
                'field_of_view': context.get('fov')
            },
            'detection_area': {
                'ground_coverage_m2': context.get('ground_coverage'),
                'resolution_cm_per_pixel': context.get('resolution')
            }
        }
    
    def _create_data_summary(self, parsed_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create summary of raw data for logging."""
        return {
            'data_type': type(parsed_data).__name__,
            'keys': list(parsed_data.keys()) if isinstance(parsed_data, dict) else [],
            'size_estimate': len(str(parsed_data)),
            'has_image_data': any(key in str(parsed_data).lower() for key in ['image', 'img', 'photo']),
            'detection_count': len(parsed_data.get('detections', [])) if 'detections' in parsed_data else 1
        }
    
    def _generate_detection_id(self, parsed_data: Dict[str, Any], context: Dict[str, Any]) -> str:
        """Generate unique detection ID."""
        mission_id = context.get('mission_id', 'unknown')
        timestamp = datetime.utcnow().strftime('%Y%m%d_%H%M%S_%f')
        data_hash = hashlib.md5(str(parsed_data).encode()).hexdigest()[:8]
        
        return f"DET_{mission_id}_{timestamp}_{data_hash}"
    
    def _validate_detection_quality(self, normalized_data: Dict[str, Any]) -> None:
        """Validate detection data quality and add warnings."""
        warnings = []
        
        # Check confidence scores
        overall_confidence = normalized_data.get('confidence_scores', {}).get('overall_confidence', 0)
        if overall_confidence < 0.5:
            warnings.append('Low overall confidence score')
        
        # Check GPS data
        gps = normalized_data.get('spatial_data', {}).get('gps_coordinates', {})
        if not gps.get('latitude') or not gps.get('longitude'):
            warnings.append('Missing GPS coordinates')
        
        # Check classification count
        classifications = normalized_data.get('classifications', [])
        if not classifications:
            warnings.append('No valid classifications found')
        
        if warnings:
            normalized_data['_metadata']['quality_warnings'] = warnings
    
    def validate_input(self, raw_output: Any) -> bool:
        """Validate that the raw output can be interpreted."""
        if raw_output is None:
            return False
        
        # Accept various data types for visual detection
        accepted_types = (dict, list, tuple, str, bytes)
        return isinstance(raw_output, accepted_types)
    
    def get_supported_formats(self) -> List[str]:
        """Get list of supported detection formats."""
        return self.supported_formats.copy()
    
    def get_detection_categories(self) -> Dict[str, Any]:
        """Get Search and Rescue detection categories."""
        return self.detection_categories.copy()
    
    def add_custom_category(self, category_name: str, config: Dict[str, Any]) -> None:
        """Add custom detection category."""
        self.detection_categories[category_name] = config
        self.logger.info(f"Added custom detection category: {category_name}")
    
    def update_confidence_threshold(self, category: str, threshold: float) -> None:
        """Update confidence threshold for a category."""
        if category in self.detection_categories:
            self.detection_categories[category]['confidence_threshold'] = threshold
            self.logger.info(f"Updated confidence threshold for {category}: {threshold}")
        else:
            self.logger.warning(f"Category {category} not found")

{"version": "1.0.0", "lastUpdated": "2025-07-26T02:00:02.082Z", "updateFrequency": "daily", "sources": {"official": "Provider APIs and public dashboards", "estimated": "Research-based calculations", "community": "User-reported data"}, "providers": {"chatgpt": {"name": "ChatGPT", "models": {}, "infrastructure": {}, "usage": {}}, "claude": {"name": "<PERSON>", "models": {}, "infrastructure": {}, "usage": {}}, "gemini": {"name": "Gemini", "models": {}, "infrastructure": {}, "usage": {}}}, "globalAverages": {}, "trends": {"efficiency": {"direction": "improving", "rate": 0.05, "period": "monthly", "lastUpdated": "2025-07-26T02:00:02.082Z"}, "usage": {"direction": "increasing", "rate": 0.15, "period": "monthly", "lastUpdated": "2025-07-26T02:00:02.082Z"}, "renewableEnergy": {"direction": "increasing", "rate": 0.08, "period": "quarterly", "lastUpdated": "2025-07-26T02:00:02.082Z"}}, "metadata": {"collectionMethod": "automated", "updateSchedule": "02:00 UTC daily", "nextUpdate": "2025-07-27T02:00:00.000Z", "dataRetention": "90 days", "accuracy": "±15%"}}
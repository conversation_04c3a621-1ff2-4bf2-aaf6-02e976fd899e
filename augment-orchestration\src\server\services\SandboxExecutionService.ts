/**
 * Sandbox Execution Service
 * 
 * Core Gap 5: Secure, isolated execution environment for running AI-generated
 * code with resource limits, monitoring, and safety controls.
 */

import { EventEmitter } from 'events';
import { PrismaClient } from '@prisma/client';
import { spawn, ChildProcess } from 'child_process';
import * as Docker from 'dockerode';
import { logger } from '../utils/logger';
import {
  SandboxType,
  ExecutionStatus,
  SecurityLevel,
  Language,
  SandboxConfig,
  ExecutionRequest,
  ExecutionResult,
  ExecutionError,
  ResourceUsage,
  SecurityEvent,
  SecurityEventType,
  SandboxInstance,
  CodeAnalysis,
  SecurityFinding,
  ExecutionQueue,
  SandboxPool,
  ExecutionStats,
  SandboxError,
  SecurityViolationError,
  ResourceExceededError,
  ExecutionTimeoutError,
  SANDBOX_CONSTANTS,
  SandboxUtils
} from '../../shared/types/SandboxExecution';

export class SandboxExecutionService extends EventEmitter {
  private prisma: PrismaClient;
  private docker: Docker;
  private sandboxInstances: Map<string, SandboxInstance> = new Map();
  private executionQueue: Map<string, ExecutionRequest[]> = new Map();
  private activeExecutions: Map<string, ActiveExecution> = new Map();
  private sandboxPools: Map<string, SandboxPool> = new Map();
  private codeAnalyzer: CodeAnalyzer;
  private resourceMonitor: ResourceMonitor;
  private securityMonitor: SecurityMonitor;

  constructor(prisma: PrismaClient) {
    super();
    this.prisma = prisma;
    this.docker = new Docker();
    this.codeAnalyzer = new CodeAnalyzer();
    this.resourceMonitor = new ResourceMonitor();
    this.securityMonitor = new SecurityMonitor();

    this.initializeSandboxPools();
    this.setupEventHandlers();
  }

  /**
   * Execute code in a secure sandbox
   */
  async executeCode(request: ExecutionRequest): Promise<ExecutionResult> {
    const startTime = new Date();

    try {
      // Validate request
      this.validateExecutionRequest(request);

      // Analyze code for security risks
      const codeAnalysis = await this.codeAnalyzer.analyzeCode(
        request.code,
        request.language
      );

      if (codeAnalysis.riskScore > 8 && request.config.securityLevel !== SecurityLevel.LOW) {
        throw new SecurityViolationError(
          'Code analysis detected high-risk patterns',
          request.executionId,
          { riskScore: codeAnalysis.riskScore, findings: codeAnalysis.findings }
        );
      }

      // Get or create sandbox instance
      const sandbox = await this.getSandboxInstance(request.config);

      // Queue execution if needed
      if (this.shouldQueueExecution(request)) {
        return await this.queueExecution(request);
      }

      // Execute code
      const result = await this.performExecution(sandbox, request, startTime);

      // Update statistics
      this.updateExecutionStats(result);

      // Emit completion event
      this.emit('executionCompleted', {
        executionId: request.executionId,
        status: result.status,
        duration: result.duration,
        resourceUsage: result.resourceUsage
      });

      logger.info('Code execution completed', {
        executionId: request.executionId,
        status: result.status,
        duration: result.duration,
        language: request.language
      });

      return result;

    } catch (error) {
      const result: ExecutionResult = {
        executionId: request.executionId,
        status: ExecutionStatus.FAILED,
        startTime,
        endTime: new Date(),
        duration: Date.now() - startTime.getTime(),
        stdout: '',
        stderr: error.message,
        error: {
          type: error instanceof SecurityViolationError ? 'SECURITY_VIOLATION' :
                error instanceof ResourceExceededError ? 'RESOURCE_EXCEEDED' :
                error instanceof ExecutionTimeoutError ? 'TIMEOUT' : 'SYSTEM_ERROR',
          message: error.message,
          details: error.details
        },
        resourceUsage: this.getEmptyResourceUsage(),
        securityEvents: [],
        metadata: {
          sandboxId: '',
          sandboxType: request.config.type,
          language: request.language,
          securityLevel: request.config.securityLevel,
          codeHash: SandboxUtils.calculateCodeHash(request.code),
          dependencyHashes: [],
          environmentHash: '',
          reproducible: false,
          cached: false,
          warnings: [error.message]
        }
      };

      logger.error('Code execution failed', {
        executionId: request.executionId,
        error: error.message,
        language: request.language
      });

      return result;
    }
  }

  /**
   * Analyze code for security risks
   */
  async analyzeCode(code: string, language: Language): Promise<CodeAnalysis> {
    return await this.codeAnalyzer.analyzeCode(code, language);
  }

  /**
   * Get execution statistics
   */
  async getExecutionStats(): Promise<ExecutionStats> {
    // This would query the database for actual statistics
    // For now, returning mock data structure
    return {
      totalExecutions: 0,
      successfulExecutions: 0,
      failedExecutions: 0,
      averageExecutionTime: 0,
      averageQueueTime: 0,
      resourceUtilization: {
        cpu: { current: 0, average: 0, peak: 0, trend: 'STABLE' },
        memory: { current: 0, average: 0, peak: 0, trend: 'STABLE' },
        disk: { current: 0, average: 0, peak: 0, trend: 'STABLE' },
        network: { current: 0, average: 0, peak: 0, trend: 'STABLE' }
      },
      securityIncidents: 0,
      topLanguages: [],
      recentActivity: {
        last24Hours: 0,
        lastWeek: 0,
        lastMonth: 0,
        peakHour: 0,
        peakDay: 0
      }
    };
  }

  /**
   * Get active sandbox instances
   */
  getSandboxInstances(): SandboxInstance[] {
    return Array.from(this.sandboxInstances.values());
  }

  /**
   * Terminate sandbox instance
   */
  async terminateSandbox(sandboxId: string): Promise<boolean> {
    const sandbox = this.sandboxInstances.get(sandboxId);
    if (!sandbox) {
      return false;
    }

    try {
      // Terminate based on sandbox type
      switch (sandbox.type) {
        case SandboxType.DOCKER:
          await this.terminateDockerSandbox(sandboxId);
          break;
        case SandboxType.PROCESS:
          await this.terminateProcessSandbox(sandboxId);
          break;
        default:
          logger.warn('Unsupported sandbox type for termination', { type: sandbox.type });
      }

      // Remove from instances
      this.sandboxInstances.delete(sandboxId);

      logger.info('Sandbox terminated', { sandboxId, type: sandbox.type });
      return true;

    } catch (error) {
      logger.error('Failed to terminate sandbox', {
        sandboxId,
        error: error.message
      });
      return false;
    }
  }

  /**
   * Private helper methods
   */
  private validateExecutionRequest(request: ExecutionRequest): void {
    if (!request.executionId) {
      throw new SandboxError('Execution ID is required', 'INVALID_REQUEST');
    }

    if (!request.code || request.code.trim().length === 0) {
      throw new SandboxError('Code is required', 'INVALID_REQUEST');
    }

    if (!SandboxUtils.isLanguageSupported(request.language)) {
      throw new SandboxError('Unsupported language', 'UNSUPPORTED_LANGUAGE');
    }

    if (!SandboxUtils.validateResourceLimits(request.config.resourceLimits)) {
      throw new SandboxError('Invalid resource limits', 'INVALID_RESOURCE_LIMITS');
    }
  }

  private async getSandboxInstance(config: SandboxConfig): Promise<SandboxInstance> {
    // Try to get an available instance from the pool
    const poolKey = `${config.type}_${config.language}`;
    const pool = this.sandboxPools.get(poolKey);

    if (pool && pool.availableInstances > 0) {
      // Find an available instance
      for (const [id, instance] of this.sandboxInstances.entries()) {
        if (instance.status === 'READY' && 
            instance.type === config.type && 
            instance.config.language === config.language) {
          instance.status = 'BUSY';
          instance.lastUsed = new Date();
          return instance;
        }
      }
    }

    // Create new instance
    return await this.createSandboxInstance(config);
  }

  private async createSandboxInstance(config: SandboxConfig): Promise<SandboxInstance> {
    const sandboxId = SandboxUtils.generateSandboxId();

    const instance: SandboxInstance = {
      id: sandboxId,
      type: config.type,
      status: 'INITIALIZING',
      config,
      createdAt: new Date(),
      lastUsed: new Date(),
      executionCount: 0,
      resourceUsage: this.getEmptyResourceUsage(),
      healthStatus: {
        healthy: true,
        lastCheck: new Date(),
        issues: [],
        performance: {
          averageExecutionTime: 0,
          successRate: 1.0,
          errorRate: 0,
          throughput: 0,
          latency: 0,
          reliability: 1.0
        }
      }
    };

    try {
      // Initialize based on sandbox type
      switch (config.type) {
        case SandboxType.DOCKER:
          await this.initializeDockerSandbox(instance);
          break;
        case SandboxType.PROCESS:
          await this.initializeProcessSandbox(instance);
          break;
        default:
          throw new SandboxError('Unsupported sandbox type', 'UNSUPPORTED_SANDBOX_TYPE');
      }

      instance.status = 'READY';
      this.sandboxInstances.set(sandboxId, instance);

      logger.info('Sandbox instance created', {
        sandboxId,
        type: config.type,
        language: config.language
      });

      return instance;

    } catch (error) {
      logger.error('Failed to create sandbox instance', {
        sandboxId,
        error: error.message
      });
      throw error;
    }
  }

  private async initializeDockerSandbox(instance: SandboxInstance): Promise<void> {
    const config = instance.config;
    
    // Select appropriate Docker image based on language
    const imageMap = {
      [Language.JAVASCRIPT]: 'node:16-alpine',
      [Language.TYPESCRIPT]: 'node:16-alpine',
      [Language.PYTHON]: 'python:3.9-alpine',
      [Language.JAVA]: 'openjdk:11-alpine',
      [Language.GO]: 'golang:1.19-alpine',
      [Language.RUST]: 'rust:1.65-alpine'
    };

    const image = imageMap[config.language] || 'ubuntu:20.04';

    try {
      // Create container
      const container = await this.docker.createContainer({
        Image: image,
        Cmd: ['/bin/sh'],
        Tty: true,
        OpenStdin: true,
        Memory: config.resourceLimits.maxMemoryMB * 1024 * 1024,
        CpuShares: Math.floor(config.resourceLimits.maxCpuPercent * 10.24),
        NetworkMode: config.networkAccess.enabled ? 'bridge' : 'none',
        WorkingDir: '/workspace',
        Env: Object.entries(config.environmentVariables).map(([k, v]) => `${k}=${v}`),
        HostConfig: {
          Memory: config.resourceLimits.maxMemoryMB * 1024 * 1024,
          CpuShares: Math.floor(config.resourceLimits.maxCpuPercent * 10.24),
          PidsLimit: config.resourceLimits.maxProcesses,
          ReadonlyRootfs: config.securityLevel === SecurityLevel.HIGH,
          SecurityOpt: ['no-new-privileges:true'],
          CapDrop: ['ALL'],
          CapAdd: ['CHOWN', 'SETUID', 'SETGID']
        }
      });

      await container.start();

      // Store container reference
      (instance as any).container = container;

      logger.debug('Docker sandbox initialized', {
        sandboxId: instance.id,
        image,
        containerId: container.id
      });

    } catch (error) {
      throw new SandboxError('Failed to initialize Docker sandbox', 'DOCKER_INIT_FAILED', instance.id, error);
    }
  }

  private async initializeProcessSandbox(instance: SandboxInstance): Promise<void> {
    // Process sandbox initialization would be implemented here
    // For now, just mark as ready
    logger.debug('Process sandbox initialized', { sandboxId: instance.id });
  }

  private shouldQueueExecution(request: ExecutionRequest): boolean {
    const activeCount = this.activeExecutions.size;
    const maxConcurrent = SANDBOX_CONSTANTS.MAX_CONCURRENT_EXECUTIONS;
    
    return activeCount >= maxConcurrent;
  }

  private async queueExecution(request: ExecutionRequest): Promise<ExecutionResult> {
    const queueKey = request.metadata.priority;
    
    if (!this.executionQueue.has(queueKey)) {
      this.executionQueue.set(queueKey, []);
    }

    this.executionQueue.get(queueKey)!.push(request);

    logger.info('Execution queued', {
      executionId: request.executionId,
      priority: request.metadata.priority,
      queueSize: this.executionQueue.get(queueKey)!.length
    });

    // Return a pending result for now
    return {
      executionId: request.executionId,
      status: ExecutionStatus.PENDING,
      startTime: new Date(),
      duration: 0,
      stdout: '',
      stderr: '',
      resourceUsage: this.getEmptyResourceUsage(),
      securityEvents: [],
      metadata: {
        sandboxId: '',
        sandboxType: request.config.type,
        language: request.language,
        securityLevel: request.config.securityLevel,
        codeHash: SandboxUtils.calculateCodeHash(request.code),
        dependencyHashes: [],
        environmentHash: '',
        reproducible: false,
        cached: false,
        warnings: ['Execution queued']
      }
    };
  }

  private async performExecution(
    sandbox: SandboxInstance,
    request: ExecutionRequest,
    startTime: Date
  ): Promise<ExecutionResult> {
    const execution: ActiveExecution = {
      executionId: request.executionId,
      sandboxId: sandbox.id,
      request,
      startTime,
      process: null,
      resourceMonitor: null,
      securityEvents: []
    };

    this.activeExecutions.set(request.executionId, execution);

    try {
      // Start resource monitoring
      execution.resourceMonitor = this.resourceMonitor.startMonitoring(
        sandbox.id,
        request.config.resourceLimits
      );

      // Execute based on sandbox type
      let result: ExecutionResult;
      switch (sandbox.type) {
        case SandboxType.DOCKER:
          result = await this.executeInDocker(sandbox, request, execution);
          break;
        case SandboxType.PROCESS:
          result = await this.executeInProcess(sandbox, request, execution);
          break;
        default:
          throw new SandboxError('Unsupported sandbox type', 'UNSUPPORTED_SANDBOX_TYPE');
      }

      // Stop monitoring
      const resourceUsage = this.resourceMonitor.stopMonitoring(execution.resourceMonitor);
      result.resourceUsage = resourceUsage;

      // Update sandbox statistics
      sandbox.executionCount++;
      sandbox.lastUsed = new Date();
      sandbox.status = 'READY';

      return result;

    } catch (error) {
      throw error;
    } finally {
      // Cleanup
      if (execution.resourceMonitor) {
        this.resourceMonitor.stopMonitoring(execution.resourceMonitor);
      }
      this.activeExecutions.delete(request.executionId);
    }
  }

  private async executeInDocker(
    sandbox: SandboxInstance,
    request: ExecutionRequest,
    execution: ActiveExecution
  ): Promise<ExecutionResult> {
    const container = (sandbox as any).container;
    const startTime = execution.startTime;

    try {
      // Create execution script based on language
      const script = this.createExecutionScript(request.code, request.language, request.entryPoint);

      // Execute in container
      const exec = await container.exec({
        Cmd: this.getExecutionCommand(request.language, script),
        AttachStdout: true,
        AttachStderr: true,
        Tty: false
      });

      const stream = await exec.start({ hijack: true, stdin: true });

      let stdout = '';
      let stderr = '';

      // Set up timeout
      const timeout = setTimeout(() => {
        stream.destroy();
        throw new ExecutionTimeoutError('Execution timeout', request.executionId);
      }, request.config.timeoutMs);

      // Collect output
      await new Promise<void>((resolve, reject) => {
        stream.on('data', (chunk: Buffer) => {
          const data = chunk.toString();
          if (data.includes('\u0001')) {
            stdout += data.replace(/\u0001/g, '');
          } else if (data.includes('\u0002')) {
            stderr += data.replace(/\u0002/g, '');
          } else {
            stdout += data;
          }
        });

        stream.on('end', () => {
          clearTimeout(timeout);
          resolve();
        });

        stream.on('error', (error) => {
          clearTimeout(timeout);
          reject(error);
        });
      });

      // Get exit code
      const inspectResult = await exec.inspect();
      const exitCode = inspectResult.ExitCode || 0;

      const endTime = new Date();
      const duration = endTime.getTime() - startTime.getTime();

      return {
        executionId: request.executionId,
        status: exitCode === 0 ? ExecutionStatus.COMPLETED : ExecutionStatus.FAILED,
        startTime,
        endTime,
        duration,
        exitCode,
        stdout: stdout.trim(),
        stderr: stderr.trim(),
        resourceUsage: this.getEmptyResourceUsage(), // Will be filled by monitoring
        securityEvents: execution.securityEvents,
        metadata: {
          sandboxId: sandbox.id,
          sandboxType: sandbox.type,
          language: request.language,
          securityLevel: request.config.securityLevel,
          codeHash: SandboxUtils.calculateCodeHash(request.code),
          dependencyHashes: [],
          environmentHash: '',
          reproducible: true,
          cached: false,
          warnings: []
        }
      };

    } catch (error) {
      if (error instanceof ExecutionTimeoutError) {
        throw error;
      }
      throw new SandboxError('Docker execution failed', 'DOCKER_EXECUTION_FAILED', request.executionId, error);
    }
  }

  private async executeInProcess(
    sandbox: SandboxInstance,
    request: ExecutionRequest,
    execution: ActiveExecution
  ): Promise<ExecutionResult> {
    // Process execution would be implemented here
    throw new SandboxError('Process execution not implemented', 'NOT_IMPLEMENTED');
  }

  private createExecutionScript(code: string, language: Language, entryPoint?: string): string {
    switch (language) {
      case Language.JAVASCRIPT:
        return code;
      case Language.PYTHON:
        return code;
      case Language.JAVA:
        return `public class Main { public static void main(String[] args) { ${code} } }`;
      default:
        return code;
    }
  }

  private getExecutionCommand(language: Language, script: string): string[] {
    switch (language) {
      case Language.JAVASCRIPT:
        return ['node', '-e', script];
      case Language.PYTHON:
        return ['python3', '-c', script];
      case Language.JAVA:
        return ['sh', '-c', `echo '${script}' > Main.java && javac Main.java && java Main`];
      default:
        return ['sh', '-c', script];
    }
  }

  private getEmptyResourceUsage(): ResourceUsage {
    return {
      peakMemoryMB: 0,
      averageMemoryMB: 0,
      peakCpuPercent: 0,
      averageCpuPercent: 0,
      diskReadMB: 0,
      diskWriteMB: 0,
      networkInKB: 0,
      networkOutKB: 0,
      processCount: 0,
      fileDescriptorCount: 0,
      systemCalls: {
        totalCalls: 0,
        fileSystemCalls: 0,
        networkCalls: 0,
        processCalls: 0,
        memoryCalls: 0,
        suspiciousCalls: 0
      }
    };
  }

  private async terminateDockerSandbox(sandboxId: string): Promise<void> {
    const sandbox = this.sandboxInstances.get(sandboxId);
    if (sandbox && (sandbox as any).container) {
      const container = (sandbox as any).container;
      await container.kill();
      await container.remove();
    }
  }

  private async terminateProcessSandbox(sandboxId: string): Promise<void> {
    // Process termination would be implemented here
  }

  private updateExecutionStats(result: ExecutionResult): void {
    // Update statistics in database or memory
    logger.debug('Execution stats updated', {
      executionId: result.executionId,
      status: result.status,
      duration: result.duration
    });
  }

  private initializeSandboxPools(): void {
    // Initialize default pools for common languages
    const commonLanguages = [Language.JAVASCRIPT, Language.PYTHON, Language.JAVA];
    
    for (const language of commonLanguages) {
      const poolId = `${SandboxType.DOCKER}_${language}`;
      this.sandboxPools.set(poolId, {
        poolId,
        type: SandboxType.DOCKER,
        language,
        minInstances: 1,
        maxInstances: 10,
        currentInstances: 0,
        availableInstances: 0,
        busyInstances: 0,
        warmupTime: 5000,
        idleTimeout: SANDBOX_CONSTANTS.IDLE_TIMEOUT,
        healthCheckInterval: SANDBOX_CONSTANTS.HEALTH_CHECK_INTERVAL,
        lastScaled: new Date()
      });
    }
  }

  private setupEventHandlers(): void {
    // Set up periodic cleanup
    setInterval(() => {
      this.cleanupIdleSandboxes();
    }, SANDBOX_CONSTANTS.IDLE_TIMEOUT);

    // Set up health checks
    setInterval(() => {
      this.performHealthChecks();
    }, SANDBOX_CONSTANTS.HEALTH_CHECK_INTERVAL);
  }

  private cleanupIdleSandboxes(): void {
    const now = new Date();
    const idleThreshold = SANDBOX_CONSTANTS.IDLE_TIMEOUT;

    for (const [id, sandbox] of this.sandboxInstances.entries()) {
      if (sandbox.status === 'READY' && 
          now.getTime() - sandbox.lastUsed.getTime() > idleThreshold) {
        this.terminateSandbox(id);
      }
    }
  }

  private performHealthChecks(): void {
    for (const [id, sandbox] of this.sandboxInstances.entries()) {
      // Perform health check based on sandbox type
      sandbox.healthStatus.lastCheck = new Date();
      // Health check implementation would go here
    }
  }
}

// Helper interfaces
interface ActiveExecution {
  executionId: string;
  sandboxId: string;
  request: ExecutionRequest;
  startTime: Date;
  process: ChildProcess | null;
  resourceMonitor: any;
  securityEvents: SecurityEvent[];
}

// Helper classes
class CodeAnalyzer {
  async analyzeCode(code: string, language: Language): Promise<CodeAnalysis> {
    // Mock implementation - would use actual static analysis tools
    const findings: SecurityFinding[] = [];
    
    // Simple pattern matching for demonstration
    if (code.includes('eval(') || code.includes('exec(')) {
      findings.push({
        findingId: 'eval_usage',
        type: 'VULNERABILITY',
        severity: 'HIGH',
        title: 'Use of eval() function',
        description: 'Code uses eval() which can execute arbitrary code',
        location: { line: 1, column: 1, snippet: code.substring(0, 50) },
        recommendation: 'Avoid using eval() function',
        references: []
      });
    }

    const riskScore = SandboxUtils.calculateRiskScore(findings);

    return {
      analysisId: `analysis_${Date.now()}`,
      code,
      language,
      riskScore,
      findings,
      complexity: {
        cyclomaticComplexity: 1,
        cognitiveComplexity: 1,
        linesOfCode: code.split('\n').length,
        maintainabilityIndex: 100,
        technicalDebt: 0,
        duplicateCodePercent: 0
      },
      dependencies: [],
      recommendations: findings.length > 0 ? ['Review security findings'] : ['Code appears safe'],
      approved: riskScore < 5,
      approvedBy: riskScore < 5 ? 'system' : undefined,
      approvedAt: riskScore < 5 ? new Date() : undefined
    };
  }
}

class ResourceMonitor {
  startMonitoring(sandboxId: string, limits: any): any {
    // Mock implementation - would use actual resource monitoring
    return { sandboxId, startTime: Date.now() };
  }

  stopMonitoring(monitor: any): ResourceUsage {
    // Mock implementation - would return actual resource usage
    return {
      peakMemoryMB: 50,
      averageMemoryMB: 30,
      peakCpuPercent: 25,
      averageCpuPercent: 15,
      diskReadMB: 1,
      diskWriteMB: 0.5,
      networkInKB: 0,
      networkOutKB: 0,
      processCount: 1,
      fileDescriptorCount: 3,
      systemCalls: {
        totalCalls: 100,
        fileSystemCalls: 10,
        networkCalls: 0,
        processCalls: 5,
        memoryCalls: 20,
        suspiciousCalls: 0
      }
    };
  }
}

class SecurityMonitor {
  // Security monitoring implementation would go here
}

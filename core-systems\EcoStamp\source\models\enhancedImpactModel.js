/**
 * Enhanced Environmental Impact Model
 * Uses token counts and real usage data for accurate calculations
 */

import { estimateTokens, getTokenAnalysis } from '../utils/tokenCounter.js';
import { loadLatestUsageData } from '../scrapers/usageDataScraper.js';

// Base impact per 1000 tokens (updated with real data when available)
const BASE_IMPACT_PER_1K_TOKENS = {
  chatgpt: {
    name: 'ChatGPT',
    energy: 0.0023, // kWh per 1000 tokens
    water: 0.0285,  // Liters per 1000 tokens
    lastUpdated: '2024-01-01',
    source: 'estimated'
  },
  claude: {
    name: '<PERSON>',
    energy: 0.0020, // More efficient per Anthropic claims
    water: 0.0250,
    lastUpdated: '2024-01-01',
    source: 'estimated'
  },
  gemini: {
    name: 'Gemini',
    energy: 0.0027, // Google infrastructure
    water: 0.0320,
    lastUpdated: '2024-01-01',
    source: 'estimated'
  }
};

// Model complexity multipliers (for different model sizes)
const MODEL_COMPLEXITY = {
  'gpt-3.5-turbo': { multiplier: 0.6, tokens: 4096 },
  'gpt-4': { multiplier: 1.0, tokens: 8192 },
  'gpt-4-turbo': { multiplier: 1.2, tokens: 128000 },
  'claude-instant': { multiplier: 0.5, tokens: 100000 },
  'claude-2': { multiplier: 1.0, tokens: 100000 },
  'claude-3-sonnet': { multiplier: 1.1, tokens: 200000 },
  'claude-3-opus': { multiplier: 1.4, tokens: 200000 },
  'gemini-pro': { multiplier: 1.0, tokens: 32768 },
  'gemini-ultra': { multiplier: 1.5, tokens: 32768 }
};

/**
 * Calculate environmental impact based on token count
 * @param {number} tokenCount - Number of tokens used
 * @param {string} platform - AI platform
 * @param {string} model - Specific model used (optional)
 * @param {object} realUsageData - Real usage data if available
 * @returns {object} Environmental impact calculation
 */
export function calculateTokenBasedImpact(tokenCount, platform, model = null, realUsageData = null) {
  // Get base impact rates
  let baseRates = BASE_IMPACT_PER_1K_TOKENS[platform];
  
  if (!baseRates) {
    console.warn(`Unknown platform: ${platform}, using ChatGPT defaults`);
    baseRates = BASE_IMPACT_PER_1K_TOKENS.chatgpt;
  }
  
  // Apply real usage data if available
  if (realUsageData && realUsageData[platform]) {
    const realData = realUsageData[platform];
    if (realData.energy && realData.water) {
      baseRates = {
        ...baseRates,
        energy: realData.energy,
        water: realData.water,
        source: 'scraped',
        lastUpdated: realData.timestamp
      };
    }
  }
  
  // Apply model complexity multiplier
  let complexityMultiplier = 1.0;
  if (model && MODEL_COMPLEXITY[model]) {
    complexityMultiplier = MODEL_COMPLEXITY[model].multiplier;
  }
  
  // Calculate impact per 1000 tokens
  const tokensInThousands = tokenCount / 1000;
  const energyImpact = baseRates.energy * tokensInThousands * complexityMultiplier;
  const waterImpact = baseRates.water * tokensInThousands * complexityMultiplier;
  
  return {
    tokenCount,
    platform,
    model,
    energy: {
      value: parseFloat(energyImpact.toFixed(6)),
      unit: 'kWh',
      perToken: parseFloat((energyImpact / tokenCount).toFixed(8))
    },
    water: {
      value: parseFloat(waterImpact.toFixed(4)),
      unit: 'L',
      perToken: parseFloat((waterImpact / tokenCount).toFixed(6))
    },
    calculation: {
      baseRates,
      complexityMultiplier,
      tokensInThousands,
      timestamp: new Date().toISOString()
    }
  };
}

/**
 * Calculate impact for a conversation (input + output)
 * @param {string} input - User input text
 * @param {string} output - AI response text
 * @param {string} platform - AI platform
 * @param {string} model - Specific model
 * @param {object} apiResponse - API response with actual token counts
 * @returns {object} Complete conversation impact analysis
 */
export async function calculateConversationImpact(input, output, platform, model = null, apiResponse = null) {
  // Get token analysis
  const tokenAnalysis = getTokenAnalysis(input, output, platform, apiResponse);
  
  // Load latest real usage data
  const realUsageData = await loadLatestUsageData();
  
  // Calculate impact using final token count (actual if available, estimated otherwise)
  const impact = calculateTokenBasedImpact(
    tokenAnalysis.final.totalTokens,
    platform,
    model,
    realUsageData?.estimates || realUsageData?.scraped
  );
  
  // Add breakdown by input/output
  const inputImpact = calculateTokenBasedImpact(
    tokenAnalysis.final.inputTokens,
    platform,
    model,
    realUsageData?.estimates || realUsageData?.scraped
  );
  
  const outputImpact = calculateTokenBasedImpact(
    tokenAnalysis.final.outputTokens,
    platform,
    model,
    realUsageData?.estimates || realUsageData?.scraped
  );
  
  return {
    total: impact,
    breakdown: {
      input: inputImpact,
      output: outputImpact
    },
    tokens: tokenAnalysis,
    metadata: {
      calculationMethod: 'token-based',
      dataSource: realUsageData ? 'real-data' : 'estimated',
      accuracy: tokenAnalysis.actual ? 'high' : 'medium',
      timestamp: new Date().toISOString()
    }
  };
}

/**
 * Update base impact rates with new scraped data
 * @param {object} scrapedData - New usage data from scrapers
 */
export function updateImpactRates(scrapedData) {
  if (!scrapedData || !scrapedData.estimates) {
    return false;
  }
  
  let updated = false;
  
  for (const [platform, data] of Object.entries(scrapedData.estimates)) {
    if (BASE_IMPACT_PER_1K_TOKENS[platform] && data.energy && data.water) {
      BASE_IMPACT_PER_1K_TOKENS[platform] = {
        ...BASE_IMPACT_PER_1K_TOKENS[platform],
        energy: data.energy,
        water: data.water,
        lastUpdated: data.timestamp,
        source: 'scraped'
      };
      updated = true;
    }
  }
  
  return updated;
}

/**
 * Get current impact rates for all platforms
 * @returns {object} Current impact rates
 */
export function getCurrentImpactRates() {
  return {
    rates: BASE_IMPACT_PER_1K_TOKENS,
    modelComplexity: MODEL_COMPLEXITY,
    lastUpdated: new Date().toISOString()
  };
}

/**
 * Estimate daily/weekly/monthly usage impact
 * @param {number} avgTokensPerDay - Average tokens used per day
 * @param {string} platform - AI platform
 * @param {number} days - Number of days to calculate for
 * @returns {object} Usage projection
 */
export function projectUsageImpact(avgTokensPerDay, platform, days = 30) {
  const dailyImpact = calculateTokenBasedImpact(avgTokensPerDay, platform);
  
  return {
    daily: dailyImpact,
    period: {
      days,
      energy: {
        value: parseFloat((dailyImpact.energy.value * days).toFixed(4)),
        unit: 'kWh'
      },
      water: {
        value: parseFloat((dailyImpact.water.value * days).toFixed(2)),
        unit: 'L'
      },
      tokens: avgTokensPerDay * days
    },
    equivalents: {
      energy: {
        lightBulbHours: Math.round((dailyImpact.energy.value * days) / 0.01), // 10W LED bulb
        phoneCharges: Math.round((dailyImpact.energy.value * days) / 0.018) // Smartphone charge
      },
      water: {
        bottlesOfWater: Math.round((dailyImpact.water.value * days) / 0.5), // 500ml bottles
        showerMinutes: Math.round((dailyImpact.water.value * days) / 9) // 9L/min shower
      }
    }
  };
}

/**
 * EcoStamp Background Service Worker
 * Handles extension lifecycle and data management
 */

class EcoStampBackground {
  constructor() {
    this.apiUrl = 'http://localhost:3000';
    this.init();
  }
  
  init() {
    // Listen for extension installation
    chrome.runtime.onInstalled.addListener((details) => {
      if (details.reason === 'install') {
        this.onInstall();
      } else if (details.reason === 'update') {
        this.onUpdate();
      }
    });
    
    // Listen for messages from content scripts
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
      return true; // Keep message channel open for async response
    });
    
    // Set up periodic data sync
    this.setupPeriodicSync();
  }
  
  onInstall() {
    console.log('🌱 EcoStamp installed successfully');
    
    // Set default settings
    chrome.storage.sync.set({
      enabled: true,
      apiUrl: this.apiUrl,
      showWidget: true,
      trackingEnabled: true,
      autoVerify: true,
      theme: 'auto'
    });
    
    // Open welcome page
    chrome.tabs.create({
      url: `${this.apiUrl}/index.html`
    });
  }
  
  onUpdate() {
    console.log('🌱 EcoStamp updated');
    
    // Migrate settings if needed
    this.migrateSettings();
  }
  
  async migrateSettings() {
    const settings = await chrome.storage.sync.get();
    
    // Add any new default settings
    const newSettings = {
      autoVerify: true,
      theme: 'auto',
      ...settings
    };
    
    await chrome.storage.sync.set(newSettings);
  }
  
  async handleMessage(message, sender, sendResponse) {
    try {
      switch (message.type) {
        case 'CALCULATE_IMPACT':
          const impact = await this.calculateImpact(message.data);
          sendResponse({ success: true, data: impact });
          break;
          
        case 'STORE_HASH':
          const stored = await this.storeHash(message.data);
          sendResponse({ success: true, data: stored });
          break;
          
        case 'GET_SETTINGS':
          const settings = await chrome.storage.sync.get();
          sendResponse({ success: true, data: settings });
          break;
          
        case 'UPDATE_SETTINGS':
          await chrome.storage.sync.set(message.data);
          sendResponse({ success: true });
          break;
          
        case 'GET_STATS':
          const stats = await this.getStats();
          sendResponse({ success: true, data: stats });
          break;
          
        default:
          sendResponse({ success: false, error: 'Unknown message type' });
      }
    } catch (error) {
      console.error('EcoStamp Background Error:', error);
      sendResponse({ success: false, error: error.message });
    }
  }
  
  async calculateImpact(data) {
    try {
      const response = await fetch(`${this.apiUrl}/api/calculate-impact`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
      });
      
      if (response.ok) {
        return await response.json();
      } else {
        throw new Error('API request failed');
      }
    } catch (error) {
      // Fallback calculation
      return this.fallbackCalculation(data);
    }
  }
  
  fallbackCalculation(data) {
    const tokens = data.tokens || 0;
    const energy = tokens * 0.0001; // 0.1 Wh per 1000 tokens
    const water = tokens * 0.001;   // 1 mL per 1000 tokens
    const ecoLevel = Math.max(1, Math.min(5, Math.ceil(5 - (energy * 1000))));
    
    const hash = this.generateHash(
      data.platform + 
      tokens + 
      Date.now() + 
      (data.conversationText || '')
    );
    
    return { energy, water, ecoLevel, hash, tokens };
  }
  
  generateHash(input) {
    // Simple hash function for fallback
    let hash = 0;
    for (let i = 0; i < input.length; i++) {
      const char = input.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return Math.abs(hash).toString(16).padStart(8, '0');
  }
  
  async storeHash(data) {
    try {
      const response = await fetch(`${this.apiUrl}/api/store-hash`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
      });
      
      if (response.ok) {
        return await response.json();
      } else {
        throw new Error('Hash storage failed');
      }
    } catch (error) {
      // Store locally if API is unavailable
      const localData = await chrome.storage.local.get('pendingHashes') || { pendingHashes: [] };
      localData.pendingHashes.push(data);
      await chrome.storage.local.set(localData);
      
      return { stored: true, local: true };
    }
  }
  
  async getStats() {
    try {
      const response = await fetch(`${this.apiUrl}/api/registry/stats`);
      if (response.ok) {
        return await response.json();
      }
    } catch (error) {
      console.log('Stats unavailable:', error);
    }
    
    // Return local stats if API unavailable
    const localData = await chrome.storage.local.get();
    return {
      totalHashes: localData.pendingHashes?.length || 0,
      totalEnergy: 0,
      totalWater: 0,
      activeUsers: 1
    };
  }
  
  setupPeriodicSync() {
    // Sync pending hashes every 5 minutes
    setInterval(async () => {
      await this.syncPendingHashes();
    }, 5 * 60 * 1000);
  }
  
  async syncPendingHashes() {
    try {
      const localData = await chrome.storage.local.get('pendingHashes');
      const pendingHashes = localData.pendingHashes || [];
      
      if (pendingHashes.length === 0) return;
      
      // Try to sync each pending hash
      const synced = [];
      for (const hash of pendingHashes) {
        try {
          const response = await fetch(`${this.apiUrl}/api/store-hash`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(hash)
          });
          
          if (response.ok) {
            synced.push(hash);
          }
        } catch (error) {
          console.log('Failed to sync hash:', error);
        }
      }
      
      // Remove synced hashes from local storage
      if (synced.length > 0) {
        const remaining = pendingHashes.filter(h => !synced.includes(h));
        await chrome.storage.local.set({ pendingHashes: remaining });
        console.log(`🌱 Synced ${synced.length} pending hashes`);
      }
    } catch (error) {
      console.error('Sync failed:', error);
    }
  }
}

// Initialize background service
new EcoStampBackground();

import axios from 'axios';
import { config } from '../config/index.js';
import { logger, logRequest, logResponse } from '../utils/logger.js';
import { RateLimiterMemory } from 'rate-limiter-flexible';

class MistralClient {
  constructor() {
    this.baseURL = config.apis.mistral.baseURL;
    this.apiKey = config.apis.mistral.apiKey;
    
    this.client = axios.create({
      baseURL: this.baseURL,
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json'
      },
      timeout: 60000
    });

    // Rate limiter
    this.rateLimiter = new RateLimiterMemory({
      keyGenerator: () => 'mistral',
      points: config.rateLimiting.requestsPerMinute,
      duration: 60,
      blockDuration: 60
    });
  }

  async rateLimit() {
    try {
      await this.rateLimiter.consume('mistral');
    } catch (rejRes) {
      const waitTime = Math.round(rejRes.msBeforeNext / 1000);
      logger.warn(`Mistral rate limit hit, waiting ${waitTime} seconds`);
      await new Promise(resolve => setTimeout(resolve, rejRes.msBeforeNext));
    }
  }

  async generateCompletion(messages, options = {}) {
    await this.rateLimit();
    logRequest('Mistral', 'generateCompletion', { 
      messageCount: messages.length,
      model: options.model || config.apis.mistral.model
    });

    try {
      const response = await this.client.post('/chat/completions', {
        model: options.model || config.apis.mistral.model,
        messages,
        max_tokens: options.maxTokens || 4096,
        temperature: options.temperature || 0.7,
        stream: false
      });

      logResponse('Mistral', 'generateCompletion', true);
      return response.data.choices[0].message.content;
    } catch (error) {
      logResponse('Mistral', 'generateCompletion', false, { error: error.message });
      throw error;
    }
  }

  async analyzeThreads(mergedThreads, task = 'code_generation', options = {}) {
    await this.rateLimit();
    logRequest('Mistral', 'analyzeThreads', { 
      task,
      threadsLength: mergedThreads.length
    });

    try {
      const prompt = this.formatThreadsForAnalysis(mergedThreads, task);
      
      const response = await this.client.post('/chat/completions', {
        model: options.model || config.apis.mistral.model,
        messages: [
          {
            role: 'system',
            content: this.getSystemPromptForTask(task)
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: options.maxTokens || 4096,
        temperature: options.temperature || 0.3
      });

      logResponse('Mistral', 'analyzeThreads', true);
      return {
        analysis: response.data.choices[0].message.content,
        task,
        timestamp: new Date().toISOString(),
        model: options.model || config.apis.mistral.model
      };
    } catch (error) {
      logResponse('Mistral', 'analyzeThreads', false, { error: error.message });
      throw error;
    }
  }

  getSystemPromptForTask(task) {
    const prompts = {
      code_generation: `You are an expert software developer and code analyst. You will receive merged conversation threads from various AI assistants. Your task is to:

1. Analyze the conversations to understand the technical requirements and context
2. Identify key patterns, solutions, and approaches discussed
3. Generate clean, well-documented, production-ready code based on the insights
4. Provide implementation recommendations and best practices
5. Highlight any potential issues or considerations

Focus on practical, actionable code solutions that incorporate the best ideas from the discussions.`,

      code_analysis: `You are an expert code reviewer and software architect. You will receive merged conversation threads containing code discussions. Your task is to:

1. Analyze the code and technical discussions thoroughly
2. Identify strengths, weaknesses, and potential improvements
3. Suggest architectural improvements and optimizations
4. Point out security considerations and best practices
5. Provide actionable recommendations for code quality

Be thorough but practical in your analysis.`,

      summarization: `You are an expert technical writer and analyst. You will receive merged conversation threads from various AI assistants. Your task is to:

1. Extract and synthesize the key technical insights
2. Identify common themes and solutions across discussions
3. Create a comprehensive summary of findings
4. Highlight actionable next steps and recommendations
5. Organize information in a clear, structured format

Focus on creating value by connecting insights across different conversations.`
    };

    return prompts[task] || prompts.code_generation;
  }

  formatThreadsForAnalysis(mergedThreads, task) {
    let prompt = `# Merged Thread Analysis Request\n\n`;
    prompt += `**Task**: ${task}\n`;
    prompt += `**Number of threads**: ${mergedThreads.length}\n`;
    prompt += `**Analysis timestamp**: ${new Date().toISOString()}\n\n`;

    prompt += `## Thread Contents\n\n`;

    mergedThreads.forEach((thread, index) => {
      prompt += `### Thread ${index + 1}: ${thread.source} (${thread.id})\n`;
      prompt += `**Created**: ${thread.created_at}\n`;
      prompt += `**Messages**: ${thread.messages.length}\n\n`;

      thread.messages.forEach((message, msgIndex) => {
        prompt += `**Message ${msgIndex + 1}** (${message.role}):\n`;
        prompt += `${message.content}\n\n`;
      });

      if (thread.highlights && thread.highlights.length > 0) {
        prompt += `**Key Highlights**:\n`;
        thread.highlights.forEach(highlight => {
          prompt += `- ${highlight}\n`;
        });
        prompt += `\n`;
      }

      prompt += `---\n\n`;
    });

    prompt += `## Analysis Request\n\n`;
    prompt += `Please analyze the above merged threads and provide insights based on the specified task. `;
    prompt += `Focus on extracting actionable technical information and generating practical solutions.`;

    return prompt;
  }

  async listConversations(limit = 20) {
    // Mistral doesn't have conversation history API
    // This would need to be implemented via browser automation or export
    logger.warn('Mistral conversation listing requires browser automation or export');
    return { conversations: [], requiresBrowserAutomation: true };
  }

  async getConversation(conversationId) {
    // Placeholder - would need browser automation
    logger.warn('Mistral conversation retrieval requires browser automation');
    return {
      id: conversationId,
      messages: [],
      created_at: new Date().toISOString(),
      source: 'mistral'
    };
  }
}

export default MistralClient;

#!/bin/bash

# EcoStamp Extension Build Script
# Creates distribution packages for Chrome Web Store and GitHub releases

set -e

VERSION="1.0.0"
EXTENSION_NAME="ecostamp"
BUILD_DIR="dist"
PACKAGE_NAME="${EXTENSION_NAME}-v${VERSION}"

echo "🌱 Building EcoStamp Extension v${VERSION}"

# Clean previous builds
echo "🧹 Cleaning previous builds..."
rm -rf "${BUILD_DIR}"
mkdir -p "${BUILD_DIR}"

# Create Chrome Web Store package (excludes development files)
echo "📦 Creating Chrome Web Store package..."
CWS_DIR="${BUILD_DIR}/chrome-web-store"
mkdir -p "${CWS_DIR}"

# Copy essential files for Chrome Web Store
cp manifest.json "${CWS_DIR}/"
cp content.js "${CWS_DIR}/"
cp styles.css "${CWS_DIR}/"
cp popup.html "${CWS_DIR}/"
cp popup.js "${CWS_DIR}/"
cp background.js "${CWS_DIR}/"
cp -r icons "${CWS_DIR}/"
cp -r data "${CWS_DIR}/"

# Remove development keys from manifest for store submission
sed -i.bak '/\"key\":/d' "${CWS_DIR}/manifest.json"
sed -i.bak '/\"update_url\":/d' "${CWS_DIR}/manifest.json"
rm "${CWS_DIR}/manifest.json.bak"

# Create ZIP for Chrome Web Store
cd "${BUILD_DIR}"
zip -r "${PACKAGE_NAME}-chrome-web-store.zip" chrome-web-store/
cd ..

echo "✅ Chrome Web Store package created: ${BUILD_DIR}/${PACKAGE_NAME}-chrome-web-store.zip"

# Create GitHub release package (includes all files)
echo "📦 Creating GitHub release package..."
GITHUB_DIR="${BUILD_DIR}/github-release"
mkdir -p "${GITHUB_DIR}"

# Copy all files for GitHub release
cp manifest.json "${GITHUB_DIR}/"
cp content.js "${GITHUB_DIR}/"
cp styles.css "${GITHUB_DIR}/"
cp popup.html "${GITHUB_DIR}/"
cp popup.js "${GITHUB_DIR}/"
cp background.js "${GITHUB_DIR}/"
cp -r icons "${GITHUB_DIR}/"
cp -r data "${GITHUB_DIR}/"
cp README.md "${GITHUB_DIR}/"
cp INSTALL.md "${GITHUB_DIR}/"
cp CHANGELOG.md "${GITHUB_DIR}/"
cp LICENSE "${GITHUB_DIR}/"
cp CONTRIBUTING.md "${GITHUB_DIR}/"

# Remove development keys from manifest
sed -i.bak '/\"key\":/d' "${GITHUB_DIR}/manifest.json"
sed -i.bak '/\"update_url\":/d' "${GITHUB_DIR}/manifest.json"
rm "${GITHUB_DIR}/manifest.json.bak"

# Create ZIP for GitHub release
cd "${BUILD_DIR}"
zip -r "${PACKAGE_NAME}.zip" github-release/
cd ..

echo "✅ GitHub release package created: ${BUILD_DIR}/${PACKAGE_NAME}.zip"

# Create development package (includes all files + development keys)
echo "📦 Creating development package..."
DEV_DIR="${BUILD_DIR}/development"
mkdir -p "${DEV_DIR}"

# Copy all files including development versions
cp manifest.json "${DEV_DIR}/"
cp content.js "${DEV_DIR}/"
cp styles.css "${DEV_DIR}/"
cp popup.html "${DEV_DIR}/"
cp popup.js "${DEV_DIR}/"
cp background.js "${DEV_DIR}/"
cp -r icons "${DEV_DIR}/"
cp README.md "${DEV_DIR}/"
cp INSTALL.md "${DEV_DIR}/"
cp CHANGELOG.md "${DEV_DIR}/"
cp LICENSE "${DEV_DIR}/"
cp CONTRIBUTING.md "${DEV_DIR}/"
cp build.sh "${DEV_DIR}/"

# Create ZIP for development
cd "${BUILD_DIR}"
zip -r "${PACKAGE_NAME}-dev.zip" development/
cd ..

echo "✅ Development package created: ${BUILD_DIR}/${PACKAGE_NAME}-dev.zip"

# Generate checksums
echo "🔐 Generating checksums..."
cd "${BUILD_DIR}"
sha256sum *.zip > checksums.txt
cd ..

echo "✅ Checksums generated: ${BUILD_DIR}/checksums.txt"

# Display build summary
echo ""
echo "🎉 Build completed successfully!"
echo ""
echo "📦 Packages created:"
echo "   • ${PACKAGE_NAME}-chrome-web-store.zip (for Chrome Web Store submission)"
echo "   • ${PACKAGE_NAME}.zip (for GitHub release)"
echo "   • ${PACKAGE_NAME}-dev.zip (for development)"
echo ""
echo "📁 Build directory: ${BUILD_DIR}/"
echo ""
echo "🚀 Next steps:"
echo "   1. Upload ${PACKAGE_NAME}-chrome-web-store.zip to Chrome Web Store"
echo "   2. Create GitHub release with ${PACKAGE_NAME}.zip"
echo "   3. Update Product Hunt with download links"
echo ""
echo "🌱 EcoStamp v${VERSION} is ready for release!"

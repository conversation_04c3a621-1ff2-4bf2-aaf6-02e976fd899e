@echo off
title AI Assistant Orchestration & Pruning Platform - Web Launcher
color 0A

echo.
echo ========================================================
echo   AI Assistant Orchestration ^& Pruning Platform
echo   Web Interface Launcher
echo ========================================================
echo.

:MAIN_MENU
echo [1] Start Web Interface Only
echo [2] Start Complete System (CLI + Web)
echo [3] Start Development Mode (with auto-reload)
echo [4] View System Status
echo [5] Stop All Services
echo [6] Open Web Dashboard
echo [7] View Logs
echo [8] Exit
echo.
set /p choice="Select an option (1-8): "

if "%choice%"=="1" goto START_WEB
if "%choice%"=="2" goto START_COMPLETE
if "%choice%"=="3" goto START_DEV
if "%choice%"=="4" goto VIEW_STATUS
if "%choice%"=="5" goto STOP_SERVICES
if "%choice%"=="6" goto OPEN_DASHBOARD
if "%choice%"=="7" goto VIEW_LOGS
if "%choice%"=="8" goto EXIT

echo Invalid choice. Please try again.
goto MAIN_MENU

:START_WEB
echo.
echo Starting AI Orchestration Web Interface...
echo Web Dashboard will be available at: http://localhost:3001
echo.
start "AI Orchestration Web Server" cmd /k "cd /d %~dp0 && echo Starting Web Server... && npm run web"
timeout /t 3 /nobreak >nul
echo.
echo Web interface started! Opening dashboard...
start http://localhost:3001
goto MAIN_MENU

:START_COMPLETE
echo.
echo Starting Complete AI Orchestration System...
echo - CLI System: Available via command line
echo - Web Interface: http://localhost:3001
echo.
start "AI Orchestration CLI" cmd /k "cd /d %~dp0 && echo AI Orchestration CLI Ready && echo Type 'node orchestrator.js test' to run tests && echo Type 'node orchestrator.js --help' for commands && cmd"
timeout /t 2 /nobreak >nul
start "AI Orchestration Web Server" cmd /k "cd /d %~dp0 && echo Starting Web Server... && npm run web"
timeout /t 3 /nobreak >nul
echo.
echo Complete system started! Opening dashboard...
start http://localhost:3001
goto MAIN_MENU

:START_DEV
echo.
echo Starting Development Mode with Auto-Reload...
echo - Web Server: http://localhost:3001
echo - CLI System: Available in separate window
echo - Auto-reload: Enabled for development
echo.
start "AI Orchestration Development" cmd /k "cd /d %~dp0 && echo Development Mode Active && npm run web-dev"
timeout /t 3 /nobreak >nul
echo.
echo Development mode started! Opening dashboard...
start http://localhost:3001
goto MAIN_MENU

:VIEW_STATUS
echo.
echo ========================================================
echo   System Status Check
echo ========================================================
echo.

echo Checking Web Server (Port 3001)...
netstat -an | findstr ":3001" >nul
if %errorlevel%==0 (
    echo [✓] Web Server: RUNNING on port 3001
) else (
    echo [✗] Web Server: NOT RUNNING
)

echo.
echo Checking Node.js processes...
tasklist | findstr "node.exe" >nul
if %errorlevel%==0 (
    echo [✓] Node.js processes detected
    tasklist | findstr "node.exe"
) else (
    echo [✗] No Node.js processes running
)

echo.
echo Checking AI Orchestration files...
if exist "%~dp0orchestrator.js" (
    echo [✓] orchestrator.js: Found
) else (
    echo [✗] orchestrator.js: Missing
)

if exist "%~dp0web-server.js" (
    echo [✓] web-server.js: Found
) else (
    echo [✗] web-server.js: Missing
)

if exist "%~dp0package.json" (
    echo [✓] package.json: Found
) else (
    echo [✗] package.json: Missing
)

echo.
echo ========================================================
pause
goto MAIN_MENU

:STOP_SERVICES
echo.
echo Stopping AI Orchestration Services...
echo.

echo Stopping Node.js processes...
taskkill /f /im node.exe 2>nul
if %errorlevel%==0 (
    echo [✓] Node.js processes stopped
) else (
    echo [!] No Node.js processes to stop
)

echo.
echo All services stopped.
pause
goto MAIN_MENU

:OPEN_DASHBOARD
echo.
echo Opening AI Orchestration Dashboard...
start http://localhost:3001
echo.
echo If the dashboard doesn't load, make sure the web server is running.
echo Use option [1] or [2] to start the web server.
pause
goto MAIN_MENU

:VIEW_LOGS
echo.
echo ========================================================
echo   Recent System Logs
echo ========================================================
echo.

if exist "%~dp0logs" (
    echo Displaying recent log files...
    dir /b /o-d "%~dp0logs\*.log" 2>nul | head -5
    echo.
    echo Latest log entries:
    for /f %%i in ('dir /b /o-d "%~dp0logs\*.log" 2^>nul') do (
        echo --- %%i ---
        type "%~dp0logs\%%i" | tail -10
        goto LOG_BREAK
    )
    :LOG_BREAK
) else (
    echo No log directory found. Logs may be in console output.
    echo Check the running terminal windows for real-time logs.
)

echo.
echo ========================================================
pause
goto MAIN_MENU

:EXIT
echo.
echo Shutting down AI Orchestration Platform...
echo.

echo Stopping any running services...
taskkill /f /im node.exe 2>nul >nul

echo.
echo Thank you for using AI Assistant Orchestration Platform!
echo.
pause
exit

:ERROR
echo.
echo An error occurred. Please check the following:
echo - Node.js is installed and in PATH
echo - All required dependencies are installed (run 'npm install')
echo - No other services are using ports 3001
echo.
pause
goto MAIN_MENU

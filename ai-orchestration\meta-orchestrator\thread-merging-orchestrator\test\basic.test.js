import { jest } from '@jest/globals';

// Mock environment variables for testing
process.env.OPENAI_API_KEY = 'test-key';
process.env.PERPLEXITY_API_KEY = 'test-key';
process.env.CLAUDE_API_KEY = 'test-key';
process.env.GEMINI_API_KEY = 'test-key';

describe('Thread-Merging Orchestrator', () => {
  describe('Configuration', () => {
    test('should load configuration without errors', async () => {
      const { config } = await import('../src/config/index.js');
      
      expect(config).toBeDefined();
      expect(config.apis).toBeDefined();
      expect(config.apis.openai).toBeDefined();
      expect(config.apis.perplexity).toBeDefined();
      expect(config.apis.claude).toBeDefined();
      expect(config.apis.gemini).toBeDefined();
    });

    test('should validate configuration', async () => {
      const { validateConfig } = await import('../src/config/index.js');
      
      expect(() => validateConfig()).not.toThrow();
    });
  });

  describe('Logger', () => {
    test('should create logger instance', async () => {
      const { logger } = await import('../src/utils/logger.js');
      
      expect(logger).toBeDefined();
      expect(typeof logger.info).toBe('function');
      expect(typeof logger.error).toBe('function');
      expect(typeof logger.warn).toBe('function');
    });
  });

  describe('API Clients', () => {
    test('should create OpenAI client', async () => {
      const { default: OpenAIClient } = await import('../src/api/openai-client.js');
      
      const client = new OpenAIClient();
      expect(client).toBeDefined();
      expect(typeof client.createEmbedding).toBe('function');
      expect(typeof client.generateCompletion).toBe('function');
    });

    test('should create Perplexity client', async () => {
      const { default: PerplexityClient } = await import('../src/api/perplexity-client.js');
      
      const client = new PerplexityClient();
      expect(client).toBeDefined();
      expect(typeof client.generateCompletion).toBe('function');
    });

    test('should create Claude client', async () => {
      const { default: ClaudeClient } = await import('../src/api/claude-client.js');
      
      const client = new ClaudeClient();
      expect(client).toBeDefined();
      expect(typeof client.generateCompletion).toBe('function');
      expect(typeof client.analyzeThreads).toBe('function');
    });

    test('should create Gemini client', async () => {
      const { default: GeminiClient } = await import('../src/api/gemini-client.js');
      
      const client = new GeminiClient();
      expect(client).toBeDefined();
      expect(typeof client.generateCompletion).toBe('function');
      expect(typeof client.analyzeThreads).toBe('function');
    });
  });

  describe('Core Components', () => {
    test('should create ThreadRetriever', async () => {
      const { default: ThreadRetriever } = await import('../src/retrieval/thread-retriever.js');
      
      const retriever = new ThreadRetriever();
      expect(retriever).toBeDefined();
      expect(typeof retriever.retrieveAllThreads).toBe('function');
    });

    test('should create SearchEngine', async () => {
      const { default: SearchEngine } = await import('../src/search/search-engine.js');
      
      const engine = new SearchEngine();
      expect(engine).toBeDefined();
      expect(typeof engine.searchThreads).toBe('function');
    });

    test('should create ThreadMerger', async () => {
      const { default: ThreadMerger } = await import('../src/merging/thread-merger.js');
      
      const merger = new ThreadMerger();
      expect(merger).toBeDefined();
      expect(typeof merger.mergeThreads).toBe('function');
    });

    test('should create main Orchestrator', async () => {
      const { default: ThreadMergingOrchestrator } = await import('../src/orchestrator.js');
      
      const orchestrator = new ThreadMergingOrchestrator();
      expect(orchestrator).toBeDefined();
      expect(typeof orchestrator.orchestrate).toBe('function');
    });
  });

  describe('Thread Processing', () => {
    test('should process mock threads', async () => {
      const { default: ThreadMerger } = await import('../src/merging/thread-merger.js');
      
      const merger = new ThreadMerger();
      const mockThreads = [
        {
          id: 'test1',
          source: 'chatgpt',
          title: 'Test Thread 1',
          created_at: '2024-01-01T00:00:00Z',
          messages: [
            { role: 'user', content: 'Hello' },
            { role: 'assistant', content: 'Hi there!' }
          ]
        }
      ];

      const searchResults = mockThreads.map(thread => ({ thread, combinedScore: 0.8 }));
      const result = await merger.mergeThreads(searchResults);
      
      expect(result).toBeDefined();
      expect(result.threads).toHaveLength(1);
      expect(result.mergedContent).toContain('Test Thread 1');
    });

    test('should handle empty thread list', async () => {
      const { default: ThreadMerger } = await import('../src/merging/thread-merger.js');
      
      const merger = new ThreadMerger();
      const result = await merger.mergeThreads([]);
      
      expect(result).toBeDefined();
      expect(result.threads).toHaveLength(0);
    });
  });

  describe('Search Functionality', () => {
    test('should perform basic search', async () => {
      const { default: SearchEngine } = await import('../src/search/search-engine.js');
      
      const engine = new SearchEngine();
      const mockThreads = [
        {
          id: 'test1',
          source: 'chatgpt',
          title: 'JavaScript Tutorial',
          created_at: '2024-01-01T00:00:00Z',
          messages: [
            { role: 'user', content: 'How to use async/await in JavaScript?' },
            { role: 'assistant', content: 'Async/await is a way to handle promises...' }
          ]
        }
      ];

      // Mock the OpenAI embedding call
      const originalCreateEmbedding = engine.openaiClient.createEmbedding;
      engine.openaiClient.createEmbedding = jest.fn().mockResolvedValue(
        new Array(1536).fill(0.1) // Mock embedding vector
      );

      const results = await engine.searchThreads(mockThreads, 'JavaScript async');
      
      expect(results).toBeDefined();
      expect(Array.isArray(results)).toBe(true);
      
      // Restore original method
      engine.openaiClient.createEmbedding = originalCreateEmbedding;
    });
  });
});

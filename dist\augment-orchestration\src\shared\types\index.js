"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AGENT_ROLES = exports.SocketEventType = exports.UserRole = exports.MutationType = exports.WorkflowStatus = exports.WorkflowStageType = void 0;
var WorkflowStageType;
(function (WorkflowStageType) {
    WorkflowStageType["PLANNING"] = "PLANNING";
    WorkflowStageType["MEMORY_RESEARCH"] = "MEMORY_RESEARCH";
    WorkflowStageType["CODE_CREATION"] = "CODE_CREATION";
    WorkflowStageType["VALIDATION"] = "VALIDATION";
    WorkflowStageType["EXECUTION"] = "EXECUTION";
    WorkflowStageType["FINALIZATION"] = "FINALIZATION";
    WorkflowStageType["PRUNING"] = "PRUNING";
})(WorkflowStageType || (exports.WorkflowStageType = WorkflowStageType = {}));
var WorkflowStatus;
(function (WorkflowStatus) {
    WorkflowStatus["PENDING"] = "PENDING";
    WorkflowStatus["RUNNING"] = "RUNNING";
    WorkflowStatus["COMPLETED"] = "COMPLETED";
    WorkflowStatus["FAILED"] = "FAILED";
    WorkflowStatus["CANCELLED"] = "CANCELLED";
})(WorkflowStatus || (exports.WorkflowStatus = WorkflowStatus = {}));
var MutationType;
(function (MutationType) {
    MutationType["CAPABILITY_ADD"] = "CAPABILITY_ADD";
    MutationType["CAPABILITY_REMOVE"] = "CAPABILITY_REMOVE";
    MutationType["ROLE_MODIFY"] = "ROLE_MODIFY";
    MutationType["PARAMETER_TUNE"] = "PARAMETER_TUNE";
    MutationType["TUNNEL_CREATE"] = "TUNNEL_CREATE";
    MutationType["TUNNEL_MODIFY"] = "TUNNEL_MODIFY";
})(MutationType || (exports.MutationType = MutationType = {}));
var UserRole;
(function (UserRole) {
    UserRole["ADMIN"] = "ADMIN";
    UserRole["USER"] = "USER";
    UserRole["VIEWER"] = "VIEWER";
})(UserRole || (exports.UserRole = UserRole = {}));
var SocketEventType;
(function (SocketEventType) {
    // Orchestrator Events
    SocketEventType["ORCHESTRATOR_CREATED"] = "orchestrator:created";
    SocketEventType["ORCHESTRATOR_UPDATED"] = "orchestrator:updated";
    SocketEventType["ORCHESTRATOR_DELETED"] = "orchestrator:deleted";
    // Agent Events
    SocketEventType["AGENT_ASSIGNED"] = "agent:assigned";
    SocketEventType["AGENT_STATUS_CHANGED"] = "agent:status_changed";
    SocketEventType["AGENT_EVOLVED"] = "agent:evolved";
    // Tunnel Events
    SocketEventType["TUNNEL_CREATED"] = "tunnel:created";
    SocketEventType["TUNNEL_ACTIVATED"] = "tunnel:activated";
    SocketEventType["TUNNEL_DATA_FLOW"] = "tunnel:data_flow";
    // Workflow Events
    SocketEventType["WORKFLOW_STARTED"] = "workflow:started";
    SocketEventType["WORKFLOW_PROGRESS"] = "workflow:progress";
    SocketEventType["WORKFLOW_COMPLETED"] = "workflow:completed";
    SocketEventType["WORKFLOW_FAILED"] = "workflow:failed";
    // System Events
    SocketEventType["SYSTEM_STATUS"] = "system:status";
    SocketEventType["USER_PRESENCE"] = "user:presence";
})(SocketEventType || (exports.SocketEventType = SocketEventType = {}));
exports.AGENT_ROLES = [
    // Core Development Roles
    {
        id: 'senior-architect',
        name: 'Senior Architect',
        description: 'Designs system architecture and makes high-level technical decisions',
        requiredCapabilities: ['system-design', 'architecture-review', 'technology-selection'],
        optionalCapabilities: ['scalability-planning', 'performance-optimization']
    },
    {
        id: 'full-stack-developer',
        name: 'Full-Stack Developer',
        description: 'Develops both frontend and backend components',
        requiredCapabilities: ['frontend-development', 'backend-development'],
        optionalCapabilities: ['database-design', 'api-development', 'testing']
    },
    {
        id: 'frontend-specialist',
        name: 'Frontend Specialist',
        description: 'Specializes in user interface and user experience development',
        requiredCapabilities: ['ui-development', 'ux-design'],
        optionalCapabilities: ['responsive-design', 'accessibility', 'performance-optimization']
    },
    {
        id: 'backend-specialist',
        name: 'Backend Specialist',
        description: 'Specializes in server-side development and infrastructure',
        requiredCapabilities: ['api-development', 'database-optimization'],
        optionalCapabilities: ['microservices', 'cloud-architecture', 'security-implementation']
    },
    {
        id: 'code-reviewer',
        name: 'Code Reviewer',
        description: 'Reviews code for quality, security, and best practices',
        requiredCapabilities: ['code-analysis', 'security-scan'],
        optionalCapabilities: ['performance-review', 'style-checking', 'documentation-review']
    },
    // Quality Assurance Roles
    {
        id: 'test-automation-engineer',
        name: 'Test Automation Engineer',
        description: 'Creates and maintains automated testing frameworks',
        requiredCapabilities: ['test-automation', 'framework-development'],
        optionalCapabilities: ['ci-cd-integration', 'test-reporting', 'performance-testing']
    },
    {
        id: 'qa-analyst',
        name: 'QA Analyst',
        description: 'Performs manual testing and quality analysis',
        requiredCapabilities: ['manual-testing', 'test-planning'],
        optionalCapabilities: ['bug-reporting', 'user-acceptance-testing', 'exploratory-testing']
    },
    {
        id: 'security-auditor',
        name: 'Security Auditor',
        description: 'Conducts security assessments and vulnerability analysis',
        requiredCapabilities: ['security-audit', 'vulnerability-scanning'],
        optionalCapabilities: ['penetration-testing', 'compliance-checking', 'threat-modeling']
    },
    // DevOps and Infrastructure Roles
    {
        id: 'devops-engineer',
        name: 'DevOps Engineer',
        description: 'Manages deployment pipelines and infrastructure automation',
        requiredCapabilities: ['ci-cd-management', 'infrastructure-automation'],
        optionalCapabilities: ['monitoring-setup', 'deployment-optimization', 'container-orchestration']
    },
    {
        id: 'cloud-architect',
        name: 'Cloud Architect',
        description: 'Designs and implements cloud-based solutions',
        requiredCapabilities: ['cloud-design', 'cost-optimization'],
        optionalCapabilities: ['scalability-planning', 'disaster-recovery', 'multi-cloud-strategy']
    },
    {
        id: 'sre-specialist',
        name: 'Site Reliability Engineer',
        description: 'Ensures system reliability and performance monitoring',
        requiredCapabilities: ['monitoring-setup', 'incident-response'],
        optionalCapabilities: ['performance-tuning', 'capacity-planning', 'automation-scripting']
    },
    // Specialized Roles
    {
        id: 'data-scientist',
        name: 'Data Scientist',
        description: 'Analyzes data and builds machine learning models',
        requiredCapabilities: ['data-analysis', 'machine-learning'],
        optionalCapabilities: ['statistical-modeling', 'data-visualization', 'feature-engineering']
    },
    {
        id: 'ml-engineer',
        name: 'ML Engineer',
        description: 'Implements and deploys machine learning systems',
        requiredCapabilities: ['ml-deployment', 'model-optimization'],
        optionalCapabilities: ['feature-engineering', 'ml-ops', 'model-monitoring']
    },
    {
        id: 'documentation-specialist',
        name: 'Documentation Specialist',
        description: 'Creates and maintains comprehensive technical documentation',
        requiredCapabilities: ['technical-writing', 'api-documentation'],
        optionalCapabilities: ['user-guides', 'knowledge-management', 'content-strategy']
    },
    {
        id: 'performance-engineer',
        name: 'Performance Engineer',
        description: 'Optimizes system and application performance',
        requiredCapabilities: ['performance-analysis', 'load-testing'],
        optionalCapabilities: ['optimization', 'profiling', 'capacity-planning']
    },
    // Legacy Roles (for backward compatibility)
    {
        id: 'generator',
        name: 'Code Generator',
        description: 'Generates initial code structures & templates',
        requiredCapabilities: ['code-generation'],
        optionalCapabilities: ['documentation', 'testing']
    },
    {
        id: 'validator',
        name: 'Code Validator',
        description: 'Ensures code safety, security compliance',
        requiredCapabilities: ['validation', 'security-analysis'],
        optionalCapabilities: ['performance-analysis']
    },
    {
        id: 'executor',
        name: 'Code Executor',
        description: 'Runs and simulates code, logs runtime behavior',
        requiredCapabilities: ['execution', 'runtime-analysis'],
        optionalCapabilities: ['debugging', 'profiling']
    },
    {
        id: 'memory',
        name: 'Long-term Memory',
        description: 'Stores & recalls project logic, strategic steps',
        requiredCapabilities: ['memory-management', 'context-storage'],
        optionalCapabilities: ['pattern-recognition']
    },
    {
        id: 'merger',
        name: 'Thread Merger',
        description: 'Aggregates documentation, merges thread context',
        requiredCapabilities: ['content-aggregation', 'context-merging'],
        optionalCapabilities: ['summarization']
    },
    {
        id: 'debugger',
        name: 'Debugger',
        description: 'Identifies and fixes code issues',
        requiredCapabilities: ['debugging', 'error-analysis'],
        optionalCapabilities: ['performance-optimization']
    }
];

import React, { useEffect, useState } from 'react';
import {
  Container,
  Typography,
  Paper,
  Box,
  Alert,
  CircularProgress,
  Fab,
  Grid,
  Card,
  CardContent,
  Chip,
  Button,
} from '@mui/material';
import { Add, Refresh } from '@mui/icons-material';
import { useAppDispatch, useAppSelector } from '../hooks/redux';
import { fetchOrchestrators } from '../store/slices/orchestratorSlice';
import { fetchAgents } from '../store/slices/agentSlice';
import FamilyTreeVisualization from '../components/FamilyTreeVisualization';
// FamilyTreeNode type will be defined locally
interface FamilyTreeNode {
  id: string;
  name: string;
  type: 'meta-orchestrator' | 'sub-orchestrator' | 'agent';
  children?: FamilyTreeNode[];
  metadata?: any;
}

const FamilyTree: React.FC = () => {
  const dispatch = useAppDispatch();
  const { orchestrators, isLoading: orchestratorLoading } = useAppSelector(state => state.orchestrator);
  const { agents, isLoading: agentLoading } = useAppSelector(state => state.agent);

  const [treeData, setTreeData] = useState<FamilyTreeNode[]>([]);
  const [selectedNode, setSelectedNode] = useState<FamilyTreeNode | null>(null);

  useEffect(() => {
    dispatch(fetchOrchestrators());
    dispatch(fetchAgents());
  }, [dispatch]);

  // Convert orchestrators and agents to tree structure
  useEffect(() => {
    if (orchestrators.length > 0 || agents.length > 0) {
      const convertToTreeData = (): FamilyTreeNode[] => {
        const treeNodes: FamilyTreeNode[] = [];

        // Add Meta-Orchestrators as root nodes
        const metaOrchestrators = orchestrators.filter(o => o.type === 'META');
        metaOrchestrators.forEach(metaOrch => {
          const metaNode: FamilyTreeNode = {
            id: metaOrch.id,
            name: metaOrch.name,
            type: 'metaOrchestrator',
            isActive: metaOrch.isActive,
            metadata: {
              description: metaOrch.description,
              createdAt: metaOrch.createdAt,
              capabilities: metaOrch.capabilities,
            },
            children: [],
          };

          // Add Sub-Orchestrators as children
          const subOrchestrators = orchestrators.filter(o =>
            o.type === 'SUB' && o.metaOrchestratorId === metaOrch.id
          );

          subOrchestrators.forEach(subOrch => {
            const subNode: FamilyTreeNode = {
              id: subOrch.id,
              name: subOrch.name,
              type: 'subOrchestrator',
              isActive: subOrch.isActive,
              metadata: {
                domain: subOrch.domain,
                description: subOrch.description,
                capabilities: subOrch.capabilities,
              },
              children: [],
            };

            // Add Agents as children of Sub-Orchestrators
            const subAgents = agents.filter(a => a.subOrchestratorId === subOrch.id);
            subAgents.forEach(agent => {
              const agentNode: FamilyTreeNode = {
                id: agent.id,
                name: agent.name,
                type: 'agent',
                isActive: agent.isActive,
                metadata: {
                  agentId: agent.agentId,
                  vendor: agent.vendor,
                  capabilities: agent.capabilities,
                  roles: agent.roles,
                  fitnessScore: agent.fitnessScore,
                },
              };
              subNode.children!.push(agentNode);
            });

            metaNode.children!.push(subNode);
          });

          treeNodes.push(metaNode);
        });

        return treeNodes;
      };

      setTreeData(convertToTreeData());
    }
  }, [orchestrators, agents]);

  const handleNodeSelect = (node: FamilyTreeNode) => {
    setSelectedNode(node);
  };

  const handleRefresh = () => {
    dispatch(fetchOrchestrators());
    dispatch(fetchAgents());
  };

  const isLoading = orchestratorLoading || agentLoading;

  if (isLoading) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ mt: 2, mb: 4 }}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          🌳 AI Orchestration Family Tree
        </Typography>
        <Button
          startIcon={<Refresh />}
          onClick={handleRefresh}
          variant="outlined"
        >
          Refresh
        </Button>
      </Box>

      {treeData.length === 0 ? (
        <Alert severity="info" sx={{ mb: 3 }}>
          No orchestrators or agents found. Create some to see the family tree visualization.
        </Alert>
      ) : (
        <Alert severity="success" sx={{ mb: 3 }}>
          Showing {treeData.length} root nodes with hierarchical relationships
        </Alert>
      )}

      <Paper sx={{ height: '70vh', position: 'relative' }}>
        <FamilyTreeVisualization
          data={treeData}
          onNodeSelect={handleNodeSelect}
        />
      </Paper>

      {/* Selected Node Details */}
      {selectedNode && (
        <Paper sx={{ mt: 3, p: 3 }}>
          <Typography variant="h6" gutterBottom>
            Selected Node Details
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="subtitle1" gutterBottom>
                    Basic Information
                  </Typography>
                  <Typography><strong>Name:</strong> {selectedNode.name}</Typography>
                  <Typography><strong>Type:</strong> {selectedNode.type}</Typography>
                  <Typography><strong>Status:</strong>
                    <Chip
                      label={selectedNode.isActive ? 'Active' : 'Inactive'}
                      color={selectedNode.isActive ? 'success' : 'default'}
                      size="small"
                      sx={{ ml: 1 }}
                    />
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="subtitle1" gutterBottom>
                    Metadata
                  </Typography>
                  {selectedNode.metadata && Object.entries(selectedNode.metadata).map(([key, value]) => (
                    <Typography key={key}>
                      <strong>{key}:</strong> {
                        Array.isArray(value)
                          ? value.join(', ')
                          : typeof value === 'object'
                            ? JSON.stringify(value)
                            : String(value)
                      }
                    </Typography>
                  ))}
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Paper>
      )}
    </Container>
  );
};

export default FamilyTree;

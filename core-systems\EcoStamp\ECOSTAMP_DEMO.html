<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌱 EcoStamp Demo - AI Environmental Impact Tracker</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 20px 0;
            text-align: center;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .header p {
            font-size: 1.2em;
            color: #666;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .demo-section {
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }
        
        .demo-section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.8em;
        }
        
        .ai-platform {
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .platform-header {
            padding: 15px 20px;
            font-weight: 600;
            color: white;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .chatgpt { background: #10a37f; }
        .claude { background: #cc785c; }
        .gemini { background: #4285f4; }
        .perplexity { background: #20b2aa; }
        
        .conversation {
            padding: 20px;
        }
        
        .message {
            margin-bottom: 15px;
            padding: 12px 16px;
            border-radius: 8px;
        }
        
        .user-message {
            background: #f8f9fa;
            border-left: 3px solid #007bff;
        }
        
        .ai-message {
            background: #f1f3f4;
            border-left: 3px solid #28a745;
        }
        
        .ecostamp-footer {
            margin-top: 16px;
            padding: 12px 16px;
            border-top: 1px solid #e1e5e9;
            font-size: 11px;
            color: #5f6368;
            background: linear-gradient(135deg, #f8f9fa 0%, #f1f3f4 100%);
            border-radius: 0 0 8px 8px;
        }
        
        .footer-divider {
            text-align: center;
            color: #cbd5e0;
            margin-bottom: 8px;
            font-size: 10px;
        }
        
        .footer-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;
        }
        
        .footer-row:last-child {
            margin-bottom: 0;
            padding-top: 8px;
            border-top: 1px solid #e2e8f0;
        }
        
        .eco-level {
            font-weight: 600;
            color: #38a169;
        }
        
        .impact {
            font-weight: 500;
            color: #805ad5;
        }
        
        .powered-by {
            font-weight: 500;
            color: #3182ce;
        }
        
        .model-badge {
            background: rgba(255, 255, 255, 0.2);
            color: #1967d2;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 500;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #28a745;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9em;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .feature-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #28a745;
        }
        
        .feature-card h3 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .install-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
            flex-wrap: wrap;
        }
        
        .install-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            text-decoration: none;
            color: white;
            transition: transform 0.2s;
        }
        
        .install-btn:hover {
            transform: translateY(-2px);
        }
        
        .chrome-store { background: #4285f4; }
        .github { background: #333; }
        .product-hunt { background: #da552f; }
        
        .benchmark-display {
            background: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 0.9em;
            overflow-x: auto;
        }
        
        @media (max-width: 768px) {
            .footer-row {
                flex-direction: column;
                align-items: flex-start;
                gap: 4px;
            }
            
            .install-buttons {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🌱 EcoStamp Demo</h1>
        <p>See how EcoStamp tracks environmental impact across ALL AI platforms</p>
    </div>
    
    <div class="container">
        <!-- Live Demo Section -->
        <div class="demo-section">
            <h2>🎯 Live Demo - Universal AI Platform Support</h2>
            <p>EcoStamp automatically detects and tracks environmental impact on any AI platform:</p>
            
            <!-- ChatGPT Demo -->
            <div class="ai-platform">
                <div class="platform-header chatgpt">
                    <span>🤖</span> ChatGPT Demo
                </div>
                <div class="conversation">
                    <div class="message user-message">
                        <strong>You:</strong> Explain machine learning in simple terms
                    </div>
                    <div class="message ai-message">
                        <strong>ChatGPT:</strong> Machine learning is like teaching a computer to recognize patterns and make predictions, similar to how humans learn from experience. Instead of programming specific instructions, we show the computer lots of examples and let it figure out the patterns on its own.
                        
                        <div class="ecostamp-footer">
                            <div class="footer-divider">──────────────────────────────────────────────</div>
                            <div class="footer-row">
                                <span>🕓 01/02/2025, 15:45:00 UTC</span>
                                <span style="font-family: monospace; background: #f1f3f4; padding: 2px 6px; border-radius: 4px; font-size: 10px;">🔐 SHA-256: a1b2...c3d4</span>
                            </div>
                            <div class="footer-row">
                                <span class="eco-level">🌿 Eco-Level: 2/5 Leaves 🌿🌿🌿🌿🍂</span>
                                <span class="impact">(0.28 Wh · 8.2 mL)</span>
                            </div>
                            <div class="footer-row">
                                <span class="powered-by">Powered by EcoStamp — GitHub</span>
                                <span class="model-badge" style="background: #e8f0fe; color: #1967d2;">ChatGPT • gpt-4</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Claude Demo -->
            <div class="ai-platform">
                <div class="platform-header claude">
                    <span>🧠</span> Claude Demo
                </div>
                <div class="conversation">
                    <div class="message user-message">
                        <strong>You:</strong> Write a Python function to calculate fibonacci numbers
                    </div>
                    <div class="message ai-message">
                        <strong>Claude:</strong> Here's an efficient Python function to calculate Fibonacci numbers:
                        
                        ```python
                        def fibonacci(n):
                            if n <= 1:
                                return n
                            a, b = 0, 1
                            for _ in range(2, n + 1):
                                a, b = b, a + b
                            return b
                        ```
                        
                        <div class="ecostamp-footer" style="border-left: 3px solid #cc785c;">
                            <div class="footer-divider">──────────────────────────────────────────────</div>
                            <div class="footer-row">
                                <span>🕓 01/02/2025, 15:46:15 UTC</span>
                                <span style="font-family: monospace; background: #f1f3f4; padding: 2px 6px; border-radius: 4px; font-size: 10px;">🔐 SHA-256: b2c3...d4e5</span>
                            </div>
                            <div class="footer-row">
                                <span class="eco-level">🌿 Eco-Level: 3/5 Leaves 🌿🌿🌿🍂🍂</span>
                                <span class="impact">(0.45 Wh · 12.8 mL)</span>
                            </div>
                            <div class="footer-row">
                                <span class="powered-by">Powered by EcoStamp — GitHub</span>
                                <span class="model-badge" style="background: #cc785c20; color: #cc785c;">Claude • claude-3-sonnet</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Gemini Demo -->
            <div class="ai-platform">
                <div class="platform-header gemini">
                    <span>✨</span> Gemini Demo
                </div>
                <div class="conversation">
                    <div class="message user-message">
                        <strong>You:</strong> What are the environmental benefits of renewable energy?
                    </div>
                    <div class="message ai-message">
                        <strong>Gemini:</strong> Renewable energy sources like solar, wind, and hydroelectric power offer significant environmental benefits:
                        
                        • **Reduced greenhouse gas emissions** - No carbon dioxide during operation
                        • **Cleaner air quality** - No harmful pollutants or particulates
                        • **Water conservation** - Most renewables require minimal water
                        • **Sustainable resource use** - Inexhaustible energy sources
                        
                        <div class="ecostamp-footer" style="border-left: 3px solid #4285f4;">
                            <div class="footer-divider">──────────────────────────────────────────────</div>
                            <div class="footer-row">
                                <span>🕓 01/02/2025, 15:47:30 UTC</span>
                                <span style="font-family: monospace; background: #f1f3f4; padding: 2px 6px; border-radius: 4px; font-size: 10px;">🔐 SHA-256: c3d4...e5f6</span>
                            </div>
                            <div class="footer-row">
                                <span class="eco-level">🌿 Eco-Level: 1/5 Leaves 🌿🌿🌿🌿🌿</span>
                                <span class="impact">(0.12 Wh · 3.8 mL)</span>
                            </div>
                            <div class="footer-row">
                                <span class="powered-by">Powered by EcoStamp — GitHub</span>
                                <span class="model-badge" style="background: #4285f420; color: #4285f4;">Gemini • gemini-pro</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Real-time Benchmarks -->
        <div class="demo-section">
            <h2>📊 Real-time AI Benchmarks</h2>
            <p>EcoStamp uses live data from provider APIs and research studies:</p>
            
            <div class="benchmark-display">
{
  "version": "1.0.0",
  "lastUpdated": "2025-01-02T15:30:00Z",
  "providers": {
    "chatgpt": {
      "models": {
        "gpt-4": {
          "energyPerToken": 0.0028,
          "waterPerToken": 0.0342,
          "efficiency": "medium",
          "source": "estimated"
        }
      }
    },
    "claude": {
      "models": {
        "claude-3-sonnet": {
          "energyPerToken": 0.0022,
          "waterPerToken": 0.0268,
          "efficiency": "high",
          "source": "estimated"
        }
      }
    }
  },
  "updateSchedule": "02:00 UTC daily"
}
            </div>
        </div>
        
        <!-- Key Features -->
        <div class="demo-section">
            <h2>✨ Key Features</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <h3>🌍 Universal Platform Support</h3>
                    <p>Works with ChatGPT, Claude, Gemini, Perplexity, Poe, Character.AI, You.com, Hugging Face, and ANY AI platform through intelligent detection.</p>
                </div>
                <div class="feature-card">
                    <h3>🌿 Smart Eco-Level System</h3>
                    <p>5-leaf visual meter showing environmental efficiency with real-time calculations based on response complexity and platform efficiency.</p>
                </div>
                <div class="feature-card">
                    <h3>🔍 Hash Verification</h3>
                    <p>Every response gets a SHA-256 hash for verifiable timestamping. Search and verify any hash through our public verification system.</p>
                </div>
                <div class="feature-card">
                    <h3>📁 File Upload Support</h3>
                    <p>Process documents (PDF, Word, Excel, images, code) with environmental impact tracking and secure hash generation.</p>
                </div>
                <div class="feature-card">
                    <h3>📊 Comprehensive Analytics</h3>
                    <p>Cross-platform statistics, platform usage breakdown, eco-level distribution, and export functionality in a beautiful dashboard.</p>
                </div>
                <div class="feature-card">
                    <h3>🔒 Privacy-First Design</h3>
                    <p>No data collection - everything stays local. Minimal permissions, CSP-compatible, and fully open source with transparent code.</p>
                </div>
            </div>
        </div>
        
        <!-- Statistics -->
        <div class="demo-section">
            <h2>📈 Impact Statistics</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">8+</div>
                    <div class="stat-label">AI Platforms Supported</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">100%</div>
                    <div class="stat-label">Universal Detection</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">0</div>
                    <div class="stat-label">Data Collection</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">1-Click</div>
                    <div class="stat-label">Installation</div>
                </div>
            </div>
        </div>
        
        <!-- Installation -->
        <div class="demo-section">
            <h2>🚀 Get EcoStamp Now</h2>
            <p style="text-align: center; margin-bottom: 20px;">
                Download and start tracking your AI environmental impact in seconds!
            </p>
            
            <div class="install-buttons">
                <a href="#" class="install-btn chrome-store">
                    🌐 Chrome Web Store
                </a>
                <a href="#" class="install-btn github">
                    📦 GitHub Release
                </a>
                <a href="#" class="install-btn product-hunt">
                    🏆 Product Hunt
                </a>
            </div>
            
            <p style="text-align: center; margin-top: 20px; color: #666;">
                ✅ Chrome 88+ • ✅ Edge 88+ • ✅ Brave • ✅ Opera<br>
                🔒 Privacy-first • 🌱 Open source • ⚡ Instant setup
            </p>
        </div>
    </div>
    
    <script>
        // Simulate real-time updates
        function updateTimestamps() {
            const timestamps = document.querySelectorAll('.footer-row span:first-child');
            timestamps.forEach(timestamp => {
                if (timestamp.textContent.includes('🕓')) {
                    const now = new Date();
                    const timeString = now.toLocaleString('en-US', {
                        month: '2-digit',
                        day: '2-digit',
                        year: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit',
                        timeZone: 'UTC',
                        timeZoneName: 'short'
                    });
                    timestamp.textContent = `🕓 ${timeString}`;
                }
            });
        }
        
        // Update timestamps every 30 seconds
        setInterval(updateTimestamps, 30000);
        
        // Add click handlers for install buttons
        document.querySelectorAll('.install-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                alert('🌱 EcoStamp installation coming soon! This is a demo.');
            });
        });
        
        // Animate eco-level leaves
        function animateLeaves() {
            const ecoLevels = document.querySelectorAll('.eco-level');
            ecoLevels.forEach(level => {
                level.style.transition = 'all 0.3s ease';
                level.addEventListener('mouseenter', () => {
                    level.style.transform = 'scale(1.05)';
                });
                level.addEventListener('mouseleave', () => {
                    level.style.transform = 'scale(1)';
                });
            });
        }
        
        // Initialize animations
        document.addEventListener('DOMContentLoaded', () => {
            updateTimestamps();
            animateLeaves();
        });
    </script>
</body>
</html>

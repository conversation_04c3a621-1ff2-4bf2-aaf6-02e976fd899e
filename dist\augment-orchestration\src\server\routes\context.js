"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.contextRoutes = void 0;
const express_1 = require("express");
const client_1 = require("@prisma/client");
const errorHandler_1 = require("../middleware/errorHandler");
const ModelContextProtocol_1 = require("../services/ModelContextProtocol");
const zod_1 = require("zod");
const router = (0, express_1.Router)();
exports.contextRoutes = router;
const prisma = new client_1.PrismaClient();
const contextProtocol = new ModelContextProtocol_1.ModelContextProtocol();
// Validation schemas
const createContextSchema = zod_1.z.object({
    name: zod_1.z.string().min(1).max(255),
    type: zod_1.z.enum(['GLOBAL', 'WORKFLOW', 'AGENT', 'SESSION', 'TUNNEL']),
    data: zod_1.z.record(zod_1.z.any()),
    priority: zod_1.z.number().int().min(0).max(100).optional(),
    tags: zod_1.z.array(zod_1.z.string()).optional(),
    accessLevel: zod_1.z.enum(['PUBLIC', 'PRIVATE', 'RESTRICTED']).optional(),
    agentId: zod_1.z.string().optional(),
    workflowId: zod_1.z.string().optional(),
});
const updateContextSchema = zod_1.z.object({
    data: zod_1.z.record(zod_1.z.any()).optional(),
    priority: zod_1.z.number().int().min(0).max(100).optional(),
    tags: zod_1.z.array(zod_1.z.string()).optional(),
    accessLevel: zod_1.z.enum(['PUBLIC', 'PRIVATE', 'RESTRICTED']).optional(),
    mergeStrategy: zod_1.z.object({
        type: zod_1.z.enum(['OVERRIDE', 'MERGE', 'APPEND', 'PRIORITY_BASED']).optional(),
        conflictResolution: zod_1.z.enum(['LATEST', 'HIGHEST_PRIORITY', 'MANUAL', 'VERSIONED']).optional(),
        preserveHistory: zod_1.z.boolean().optional(),
        maxVersions: zod_1.z.number().int().min(1).max(100).optional(),
    }).optional(),
});
const queryContextSchema = zod_1.z.object({
    layers: zod_1.z.array(zod_1.z.string()).optional(),
    types: zod_1.z.array(zod_1.z.string()).optional(),
    tags: zod_1.z.array(zod_1.z.string()).optional(),
    agentIds: zod_1.z.array(zod_1.z.string()).optional(),
    workflowIds: zod_1.z.array(zod_1.z.string()).optional(),
    timeRange: zod_1.z.object({
        start: zod_1.z.string().datetime(),
        end: zod_1.z.string().datetime(),
    }).optional(),
    limit: zod_1.z.number().int().min(1).max(1000).optional(),
    includeMetadata: zod_1.z.boolean().optional(),
});
const mergeLayersSchema = zod_1.z.object({
    layerIds: zod_1.z.array(zod_1.z.string()).min(1),
    strategy: zod_1.z.object({
        type: zod_1.z.enum(['OVERRIDE', 'MERGE', 'APPEND', 'PRIORITY_BASED']).optional(),
        conflictResolution: zod_1.z.enum(['LATEST', 'HIGHEST_PRIORITY', 'MANUAL', 'VERSIONED']).optional(),
        preserveHistory: zod_1.z.boolean().optional(),
        maxVersions: zod_1.z.number().int().min(1).max(100).optional(),
    }).optional(),
});
// Create context layer
router.post('/', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const validatedData = createContextSchema.parse(req.body);
    const layerId = await contextProtocol.createContextLayer(validatedData.name, validatedData.type, validatedData.data, {
        priority: validatedData.priority,
        tags: validatedData.tags,
        accessLevel: validatedData.accessLevel,
        agentId: validatedData.agentId,
        workflowId: validatedData.workflowId,
    });
    res.status(201).json({
        success: true,
        data: {
            layerId,
            message: 'Context layer created successfully',
        },
    });
}));
// Update context layer
router.put('/:id', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const validatedData = updateContextSchema.parse(req.body);
    const mergeStrategy = {
        type: validatedData.mergeStrategy?.type || 'MERGE',
        conflictResolution: validatedData.mergeStrategy?.conflictResolution || 'LATEST',
        preserveHistory: validatedData.mergeStrategy?.preserveHistory ?? true,
        maxVersions: validatedData.mergeStrategy?.maxVersions || 10,
    };
    await contextProtocol.updateContextLayer(id, {
        data: validatedData.data,
        priority: validatedData.priority,
        tags: validatedData.tags,
        accessLevel: validatedData.accessLevel,
    }, mergeStrategy);
    res.json({
        success: true,
        message: 'Context layer updated successfully',
    });
}));
// Query context layers
router.post('/query', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const validatedData = queryContextSchema.parse(req.body);
    const query = {
        layers: validatedData.layers,
        types: validatedData.types,
        tags: validatedData.tags,
        agentIds: validatedData.agentIds,
        workflowIds: validatedData.workflowIds,
        timeRange: validatedData.timeRange ? {
            start: new Date(validatedData.timeRange.start),
            end: new Date(validatedData.timeRange.end),
        } : undefined,
        limit: validatedData.limit,
        includeMetadata: validatedData.includeMetadata,
    };
    const layers = await contextProtocol.queryContext(query);
    res.json({
        success: true,
        data: layers,
    });
}));
// Merge context layers
router.post('/merge', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const validatedData = mergeLayersSchema.parse(req.body);
    const strategy = {
        type: validatedData.strategy?.type || 'PRIORITY_BASED',
        conflictResolution: validatedData.strategy?.conflictResolution || 'HIGHEST_PRIORITY',
        preserveHistory: validatedData.strategy?.preserveHistory ?? false,
        maxVersions: validatedData.strategy?.maxVersions || 1,
    };
    const mergedData = await contextProtocol.mergeContextLayers(validatedData.layerIds, strategy);
    res.json({
        success: true,
        data: mergedData,
    });
}));
// Get context layer by ID
router.get('/:id', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const layers = await contextProtocol.queryContext({ layers: [id] });
    if (layers.length === 0) {
        return res.status(404).json({
            success: false,
            error: 'Context layer not found',
        });
    }
    res.json({
        success: true,
        data: layers[0],
    });
}));
// Get all context layers with pagination
router.get('/', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50;
    const type = req.query.type;
    const tags = req.query.tags ? req.query.tags.split(',') : undefined;
    const query = {
        types: type ? [type] : undefined,
        tags,
        limit,
    };
    const layers = await contextProtocol.queryContext(query);
    // Simple pagination (in a real implementation, you'd want proper offset-based pagination)
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedLayers = layers.slice(startIndex, endIndex);
    res.json({
        success: true,
        data: paginatedLayers,
        pagination: {
            page,
            limit,
            total: layers.length,
            totalPages: Math.ceil(layers.length / limit),
        },
    });
}));
// Get context statistics
router.get('/stats/overview', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const stats = await contextProtocol.getContextStatistics();
    res.json({
        success: true,
        data: stats,
    });
}));

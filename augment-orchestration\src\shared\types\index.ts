// Core Entity Types
export interface MetaOrchestrator {
  id: string;
  name: string;
  description?: string;
  version: string;
  isActive: boolean;
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  creatorId: string;
  subOrchestrators?: SubOrchestrator[];
  workflowTemplates?: WorkflowTemplate[];
  tunnels?: Tunnel[];
}

export interface SubOrchestrator {
  id: string;
  name: string;
  description?: string;
  domain: string;
  isActive: boolean;
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  metaOrchestratorId: string;
  agents?: Agent[];
  tunnelsFrom?: Tunnel[];
  tunnelsTo?: Tunnel[];
}

export interface Agent {
  id: string;
  agentId: string; // Unique identifier like "chatgpt-4o"
  name: string;
  description?: string;
  vendor: string;
  capabilities: string[];
  roles: string[];
  isActive: boolean;
  metadata?: Record<string, any>;
  fitnessScore: number;
  createdAt: Date;
  updatedAt: Date;
  subOrchestratorId?: string;
  workflowExecutions?: WorkflowExecution[];
  evolutionVariants?: EvolutionVariant[];
  tunnelsFrom?: Tunnel[];
  tunnelsTo?: Tunnel[];
}

export interface Tunnel {
  id: string;
  name: string;
  description?: string;
  tags: string[];
  isActive: boolean;
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  metaOrchestratorId?: string;
  fromSubOrchestratorId?: string;
  toSubOrchestratorId?: string;
  fromAgentId?: string;
  toAgentId?: string;
}

export interface WorkflowTemplate {
  id: string;
  name: string;
  description?: string;
  stages: WorkflowStage[];
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  metaOrchestratorId: string;
  executions?: WorkflowExecution[];
}

export interface WorkflowStage {
  id: string;
  name: string;
  type: WorkflowStageType;
  agentRoles: string[];
  dependencies: string[];
  timeout?: number;
  retries?: number;
  metadata?: Record<string, any>;
}

export enum WorkflowStageType {
  PLANNING = 'PLANNING',
  MEMORY_RESEARCH = 'MEMORY_RESEARCH',
  CODE_CREATION = 'CODE_CREATION',
  VALIDATION = 'VALIDATION',
  EXECUTION = 'EXECUTION',
  FINALIZATION = 'FINALIZATION',
  PRUNING = 'PRUNING'
}

export interface WorkflowExecution {
  id: string;
  status: WorkflowStatus;
  startedAt: Date;
  endedAt?: Date;
  result?: Record<string, any>;
  metadata?: Record<string, any>;
  templateId: string;
  executorId: string;
  agents?: Agent[];
}

export enum WorkflowStatus {
  PENDING = 'PENDING',
  RUNNING = 'RUNNING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED'
}

export interface EvolutionVariant {
  id: string;
  generation: number;
  mutations: Mutation[];
  fitnessScore: number;
  isPromoted: boolean;
  createdAt: Date;
  agentId: string;
}

export interface Mutation {
  type: MutationType;
  target: string;
  oldValue: any;
  newValue: any;
  metadata?: Record<string, any>;
}

export enum MutationType {
  CAPABILITY_ADD = 'CAPABILITY_ADD',
  CAPABILITY_REMOVE = 'CAPABILITY_REMOVE',
  ROLE_MODIFY = 'ROLE_MODIFY',
  PARAMETER_TUNE = 'PARAMETER_TUNE',
  TUNNEL_CREATE = 'TUNNEL_CREATE',
  TUNNEL_MODIFY = 'TUNNEL_MODIFY'
}

export interface AuditLog {
  id: string;
  action: string;
  entityId: string;
  entityType: string;
  oldValue?: Record<string, any>;
  newValue?: Record<string, any>;
  metadata?: Record<string, any>;
  timestamp: Date;
  userId: string;
}

export interface SharedContext {
  id: string;
  key: string;
  value: Record<string, any>;
  tags: string[];
  expiresAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface User {
  id: string;
  email: string;
  username: string;
  role: UserRole;
  createdAt: Date;
  updatedAt: Date;
}

export enum UserRole {
  ADMIN = 'ADMIN',
  USER = 'USER',
  VIEWER = 'VIEWER'
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// WebSocket Event Types
export interface SocketEvent {
  type: string;
  payload: any;
  timestamp: Date;
}

export enum SocketEventType {
  // Orchestrator Events
  ORCHESTRATOR_CREATED = 'orchestrator:created',
  ORCHESTRATOR_UPDATED = 'orchestrator:updated',
  ORCHESTRATOR_DELETED = 'orchestrator:deleted',
  
  // Agent Events
  AGENT_ASSIGNED = 'agent:assigned',
  AGENT_STATUS_CHANGED = 'agent:status_changed',
  AGENT_EVOLVED = 'agent:evolved',
  
  // Tunnel Events
  TUNNEL_CREATED = 'tunnel:created',
  TUNNEL_ACTIVATED = 'tunnel:activated',
  TUNNEL_DATA_FLOW = 'tunnel:data_flow',
  
  // Workflow Events
  WORKFLOW_STARTED = 'workflow:started',
  WORKFLOW_PROGRESS = 'workflow:progress',
  WORKFLOW_COMPLETED = 'workflow:completed',
  WORKFLOW_FAILED = 'workflow:failed',
  
  // System Events
  SYSTEM_STATUS = 'system:status',
  USER_PRESENCE = 'user:presence'
}

// Family Tree Visualization Types
export interface FamilyTreeNode {
  id: string;
  type: 'meta-orchestrator' | 'sub-orchestrator' | 'agent';
  data: {
    label: string;
    description?: string;
    status: 'active' | 'inactive' | 'running' | 'error';
    metadata?: Record<string, any>;
  };
  position: { x: number; y: number };
  parentNode?: string;
  expandParent?: boolean;
}

export interface FamilyTreeEdge {
  id: string;
  source: string;
  target: string;
  type: 'tunnel' | 'hierarchy';
  data?: {
    label?: string;
    tags?: string[];
    isActive?: boolean;
    metadata?: Record<string, any>;
  };
  animated?: boolean;
  style?: Record<string, any>;
}

// Agent Role Directory Types
export interface AgentCapability {
  id: string;
  name: string;
  description: string;
  category: string;
}

export interface AgentRole {
  id: string;
  name: string;
  description: string;
  requiredCapabilities: string[];
  optionalCapabilities: string[];
}

export const AGENT_ROLES: AgentRole[] = [
  // Core Development Roles
  {
    id: 'senior-architect',
    name: 'Senior Architect',
    description: 'Designs system architecture and makes high-level technical decisions',
    requiredCapabilities: ['system-design', 'architecture-review', 'technology-selection'],
    optionalCapabilities: ['scalability-planning', 'performance-optimization']
  },
  {
    id: 'full-stack-developer',
    name: 'Full-Stack Developer',
    description: 'Develops both frontend and backend components',
    requiredCapabilities: ['frontend-development', 'backend-development'],
    optionalCapabilities: ['database-design', 'api-development', 'testing']
  },
  {
    id: 'frontend-specialist',
    name: 'Frontend Specialist',
    description: 'Specializes in user interface and user experience development',
    requiredCapabilities: ['ui-development', 'ux-design'],
    optionalCapabilities: ['responsive-design', 'accessibility', 'performance-optimization']
  },
  {
    id: 'backend-specialist',
    name: 'Backend Specialist',
    description: 'Specializes in server-side development and infrastructure',
    requiredCapabilities: ['api-development', 'database-optimization'],
    optionalCapabilities: ['microservices', 'cloud-architecture', 'security-implementation']
  },
  {
    id: 'code-reviewer',
    name: 'Code Reviewer',
    description: 'Reviews code for quality, security, and best practices',
    requiredCapabilities: ['code-analysis', 'security-scan'],
    optionalCapabilities: ['performance-review', 'style-checking', 'documentation-review']
  },

  // Quality Assurance Roles
  {
    id: 'test-automation-engineer',
    name: 'Test Automation Engineer',
    description: 'Creates and maintains automated testing frameworks',
    requiredCapabilities: ['test-automation', 'framework-development'],
    optionalCapabilities: ['ci-cd-integration', 'test-reporting', 'performance-testing']
  },
  {
    id: 'qa-analyst',
    name: 'QA Analyst',
    description: 'Performs manual testing and quality analysis',
    requiredCapabilities: ['manual-testing', 'test-planning'],
    optionalCapabilities: ['bug-reporting', 'user-acceptance-testing', 'exploratory-testing']
  },
  {
    id: 'security-auditor',
    name: 'Security Auditor',
    description: 'Conducts security assessments and vulnerability analysis',
    requiredCapabilities: ['security-audit', 'vulnerability-scanning'],
    optionalCapabilities: ['penetration-testing', 'compliance-checking', 'threat-modeling']
  },

  // DevOps and Infrastructure Roles
  {
    id: 'devops-engineer',
    name: 'DevOps Engineer',
    description: 'Manages deployment pipelines and infrastructure automation',
    requiredCapabilities: ['ci-cd-management', 'infrastructure-automation'],
    optionalCapabilities: ['monitoring-setup', 'deployment-optimization', 'container-orchestration']
  },
  {
    id: 'cloud-architect',
    name: 'Cloud Architect',
    description: 'Designs and implements cloud-based solutions',
    requiredCapabilities: ['cloud-design', 'cost-optimization'],
    optionalCapabilities: ['scalability-planning', 'disaster-recovery', 'multi-cloud-strategy']
  },
  {
    id: 'sre-specialist',
    name: 'Site Reliability Engineer',
    description: 'Ensures system reliability and performance monitoring',
    requiredCapabilities: ['monitoring-setup', 'incident-response'],
    optionalCapabilities: ['performance-tuning', 'capacity-planning', 'automation-scripting']
  },

  // Specialized Roles
  {
    id: 'data-scientist',
    name: 'Data Scientist',
    description: 'Analyzes data and builds machine learning models',
    requiredCapabilities: ['data-analysis', 'machine-learning'],
    optionalCapabilities: ['statistical-modeling', 'data-visualization', 'feature-engineering']
  },
  {
    id: 'ml-engineer',
    name: 'ML Engineer',
    description: 'Implements and deploys machine learning systems',
    requiredCapabilities: ['ml-deployment', 'model-optimization'],
    optionalCapabilities: ['feature-engineering', 'ml-ops', 'model-monitoring']
  },
  {
    id: 'documentation-specialist',
    name: 'Documentation Specialist',
    description: 'Creates and maintains comprehensive technical documentation',
    requiredCapabilities: ['technical-writing', 'api-documentation'],
    optionalCapabilities: ['user-guides', 'knowledge-management', 'content-strategy']
  },
  {
    id: 'performance-engineer',
    name: 'Performance Engineer',
    description: 'Optimizes system and application performance',
    requiredCapabilities: ['performance-analysis', 'load-testing'],
    optionalCapabilities: ['optimization', 'profiling', 'capacity-planning']
  },

  // Legacy Roles (for backward compatibility)
  {
    id: 'generator',
    name: 'Code Generator',
    description: 'Generates initial code structures & templates',
    requiredCapabilities: ['code-generation'],
    optionalCapabilities: ['documentation', 'testing']
  },
  {
    id: 'validator',
    name: 'Code Validator',
    description: 'Ensures code safety, security compliance',
    requiredCapabilities: ['validation', 'security-analysis'],
    optionalCapabilities: ['performance-analysis']
  },
  {
    id: 'executor',
    name: 'Code Executor',
    description: 'Runs and simulates code, logs runtime behavior',
    requiredCapabilities: ['execution', 'runtime-analysis'],
    optionalCapabilities: ['debugging', 'profiling']
  },
  {
    id: 'memory',
    name: 'Long-term Memory',
    description: 'Stores & recalls project logic, strategic steps',
    requiredCapabilities: ['memory-management', 'context-storage'],
    optionalCapabilities: ['pattern-recognition']
  },
  {
    id: 'merger',
    name: 'Thread Merger',
    description: 'Aggregates documentation, merges thread context',
    requiredCapabilities: ['content-aggregation', 'context-merging'],
    optionalCapabilities: ['summarization']
  },
  {
    id: 'debugger',
    name: 'Debugger',
    description: 'Identifies and fixes code issues',
    requiredCapabilities: ['debugging', 'error-analysis'],
    optionalCapabilities: ['performance-optimization']
  }
];

// Event Bus Types
export interface EventBusMessage {
  id: string;
  type: string;
  source: string;
  target?: string;
  payload: any;
  timestamp: Date;
  metadata?: Record<string, any>;
}

// Configuration Types
export interface DatabaseConfig {
  url: string;
  maxConnections?: number;
  ssl?: boolean;
}

export interface ServerConfig {
  port: number;
  host: string;
  cors: {
    origin: string[];
    credentials: boolean;
  };
  rateLimit: {
    windowMs: number;
    max: number;
  };
}

export interface SocketConfig {
  cors: {
    origin: string[];
    credentials: boolean;
  };
  transports: string[];
}

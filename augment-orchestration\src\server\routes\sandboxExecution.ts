/**
 * Sandbox Execution API Routes
 * 
 * RESTful API endpoints for secure code execution in sandboxed environments
 * Core Gap 5: Sandbox Execution Environment implementation
 */

import { Router } from 'express';
import { body, query, param, validationResult } from 'express-validator';
import { PrismaClient } from '@prisma/client';
import { SandboxExecutionService } from '../services/SandboxExecutionService';
import { asyncHandler } from '../utils/asyncHandler';
import { authenticate, authorize } from '../middleware/auth';
import { logger } from '../utils/logger';
import {
  SandboxType,
  SecurityLevel,
  Language,
  ExecutionRequest,
  SandboxConfig,
  ResourceLimits,
  NetworkConfig,
  FileSystemConfig,
  MonitoringConfig,
  ExecutionMetadata,
  Dependency,
  SandboxUtils,
  SANDBOX_CONSTANTS
} from '../../shared/types/SandboxExecution';

const router = Router();
const prisma = new PrismaClient();
const sandboxService = new SandboxExecutionService(prisma);

// Validation middleware
const validateExecutionRequest = [
  body('code').notEmpty().withMessage('Code is required'),
  body('language').isIn(Object.values(Language)).withMessage('Invalid language'),
  body('config.type').isIn(Object.values(SandboxType)).withMessage('Invalid sandbox type'),
  body('config.securityLevel').isIn(Object.values(SecurityLevel)).withMessage('Invalid security level'),
  body('config.resourceLimits.maxMemoryMB').isInt({ min: 1, max: SANDBOX_CONSTANTS.MAX_MEMORY_LIMIT }).withMessage('Invalid memory limit'),
  body('config.resourceLimits.maxCpuPercent').isInt({ min: 1, max: SANDBOX_CONSTANTS.MAX_CPU_LIMIT }).withMessage('Invalid CPU limit'),
  body('config.resourceLimits.maxExecutionTimeMs').isInt({ min: 1000, max: SANDBOX_CONSTANTS.MAX_TIMEOUT }).withMessage('Invalid execution time limit'),
  body('config.timeoutMs').isInt({ min: 1000, max: SANDBOX_CONSTANTS.MAX_TIMEOUT }).withMessage('Invalid timeout'),
  body('metadata.requestedBy').notEmpty().withMessage('Requested by is required'),
  body('metadata.purpose').notEmpty().withMessage('Purpose is required')
];

const validateCodeAnalysis = [
  body('code').notEmpty().withMessage('Code is required'),
  body('language').isIn(Object.values(Language)).withMessage('Invalid language')
];

const validateSandboxConfig = [
  body('type').isIn(Object.values(SandboxType)).withMessage('Invalid sandbox type'),
  body('language').isIn(Object.values(Language)).withMessage('Invalid language'),
  body('securityLevel').isIn(Object.values(SecurityLevel)).withMessage('Invalid security level'),
  body('resourceLimits').isObject().withMessage('Resource limits must be an object'),
  body('timeoutMs').isInt({ min: 1000, max: SANDBOX_CONSTANTS.MAX_TIMEOUT }).withMessage('Invalid timeout')
];

/**
 * POST /api/sandbox/execute
 * Execute code in a secure sandbox environment
 */
router.post('/execute',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR', 'AGENT']),
  validateExecutionRequest,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const executionId = req.body.executionId || SandboxUtils.generateExecutionId();

    const request: ExecutionRequest = {
      executionId,
      code: req.body.code,
      language: req.body.language,
      entryPoint: req.body.entryPoint,
      arguments: req.body.arguments || [],
      inputData: req.body.inputData,
      dependencies: req.body.dependencies || [],
      config: {
        type: req.body.config.type,
        language: req.body.language,
        securityLevel: req.body.config.securityLevel,
        resourceLimits: {
          maxMemoryMB: req.body.config.resourceLimits.maxMemoryMB,
          maxCpuPercent: req.body.config.resourceLimits.maxCpuPercent,
          maxDiskMB: req.body.config.resourceLimits.maxDiskMB || SANDBOX_CONSTANTS.DEFAULT_DISK_LIMIT,
          maxNetworkKBps: req.body.config.resourceLimits.maxNetworkKBps || 0,
          maxProcesses: req.body.config.resourceLimits.maxProcesses || 10,
          maxFileDescriptors: req.body.config.resourceLimits.maxFileDescriptors || 100,
          maxExecutionTimeMs: req.body.config.resourceLimits.maxExecutionTimeMs,
          maxOutputSizeKB: req.body.config.resourceLimits.maxOutputSizeKB || SANDBOX_CONSTANTS.DEFAULT_OUTPUT_LIMIT
        },
        networkAccess: req.body.config.networkAccess || {
          enabled: false,
          allowedHosts: [],
          blockedHosts: ['*'],
          allowedPorts: [],
          blockedPorts: [],
          maxConnections: 0
        },
        fileSystemAccess: req.body.config.fileSystemAccess || {
          readOnlyPaths: ['/'],
          writablePaths: ['/tmp'],
          blockedPaths: ['/etc', '/var', '/usr'],
          maxFileSize: 1024 * 1024, // 1MB
          maxTotalSize: 10 * 1024 * 1024, // 10MB
          allowedExtensions: ['.txt', '.json', '.csv'],
          blockedExtensions: ['.exe', '.sh', '.bat'],
          tempDirectory: '/tmp'
        },
        environmentVariables: req.body.config.environmentVariables || {},
        allowedModules: req.body.config.allowedModules || [],
        blockedModules: req.body.config.blockedModules || [],
        timeoutMs: req.body.config.timeoutMs,
        maxExecutions: req.body.config.maxExecutions || 1,
        persistentStorage: req.body.config.persistentStorage || false,
        monitoring: req.body.config.monitoring || {
          enableResourceMonitoring: true,
          enableNetworkMonitoring: true,
          enableFileSystemMonitoring: true,
          enableSystemCallMonitoring: true,
          monitoringInterval: 1000,
          alertThresholds: {
            memoryUsagePercent: 90,
            cpuUsagePercent: 90,
            diskUsagePercent: 90,
            networkUsagePercent: 90,
            suspiciousActivityScore: 8
          },
          logLevel: 'INFO'
        }
      },
      metadata: {
        requestedBy: req.body.metadata.requestedBy || req.user?.id || 'unknown',
        purpose: req.body.metadata.purpose,
        tags: req.body.metadata.tags || [],
        priority: req.body.metadata.priority || 'MEDIUM',
        maxRetries: req.body.metadata.maxRetries || 0,
        notifyOnComplete: req.body.metadata.notifyOnComplete || false,
        parentExecutionId: req.body.metadata.parentExecutionId,
        correlationId: req.body.metadata.correlationId
      }
    };

    const result = await sandboxService.executeCode(request);

    logger.info('Code execution requested via API', {
      executionId: result.executionId,
      language: request.language,
      status: result.status,
      duration: result.duration,
      userId: req.user?.id
    });

    res.json({
      success: true,
      execution: {
        executionId: result.executionId,
        status: result.status,
        startTime: result.startTime,
        endTime: result.endTime,
        duration: result.duration,
        exitCode: result.exitCode,
        stdout: result.stdout,
        stderr: result.stderr,
        output: result.output,
        error: result.error,
        resourceUsage: result.resourceUsage,
        securityEvents: result.securityEvents,
        metadata: result.metadata
      }
    });
  })
);

/**
 * POST /api/sandbox/analyze-code
 * Analyze code for security risks before execution
 */
router.post('/analyze-code',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR', 'AGENT']),
  validateCodeAnalysis,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { code, language } = req.body;

    const analysis = await sandboxService.analyzeCode(code, language);

    logger.info('Code analysis requested via API', {
      analysisId: analysis.analysisId,
      language,
      riskScore: analysis.riskScore,
      findingCount: analysis.findings.length,
      userId: req.user?.id
    });

    res.json({
      success: true,
      analysis: {
        analysisId: analysis.analysisId,
        riskScore: analysis.riskScore,
        findings: analysis.findings,
        complexity: analysis.complexity,
        dependencies: analysis.dependencies,
        recommendations: analysis.recommendations,
        approved: analysis.approved,
        approvedBy: analysis.approvedBy,
        approvedAt: analysis.approvedAt
      }
    });
  })
);

/**
 * GET /api/sandbox/stats
 * Get execution statistics
 */
router.get('/stats',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR']),
  asyncHandler(async (req, res) => {
    const stats = await sandboxService.getExecutionStats();

    res.json({
      success: true,
      stats
    });
  })
);

/**
 * GET /api/sandbox/instances
 * Get active sandbox instances
 */
router.get('/instances',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR']),
  asyncHandler(async (req, res) => {
    const instances = sandboxService.getSandboxInstances();

    res.json({
      success: true,
      instances: instances.map(instance => ({
        id: instance.id,
        type: instance.type,
        status: instance.status,
        language: instance.config.language,
        securityLevel: instance.config.securityLevel,
        createdAt: instance.createdAt,
        lastUsed: instance.lastUsed,
        executionCount: instance.executionCount,
        resourceUsage: instance.resourceUsage,
        healthStatus: instance.healthStatus
      })),
      count: instances.length
    });
  })
);

/**
 * DELETE /api/sandbox/instances/:sandboxId
 * Terminate a sandbox instance
 */
router.delete('/instances/:sandboxId',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR']),
  param('sandboxId').notEmpty().withMessage('Sandbox ID is required'),
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { sandboxId } = req.params;

    const terminated = await sandboxService.terminateSandbox(sandboxId);

    if (terminated) {
      logger.info('Sandbox terminated via API', {
        sandboxId,
        userId: req.user?.id
      });

      res.json({
        success: true,
        message: 'Sandbox terminated successfully',
        sandboxId
      });
    } else {
      res.status(404).json({
        success: false,
        error: 'Sandbox not found or could not be terminated'
      });
    }
  })
);

/**
 * POST /api/sandbox/validate-config
 * Validate sandbox configuration
 */
router.post('/validate-config',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR', 'AGENT']),
  validateSandboxConfig,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const config: Partial<SandboxConfig> = req.body;

    // Validate resource limits
    const resourceLimitsValid = config.resourceLimits ? 
      SandboxUtils.validateResourceLimits(config.resourceLimits) : true;

    // Check language support
    const languageSupported = config.language ? 
      SandboxUtils.isLanguageSupported(config.language) : true;

    // Get default configuration for comparison
    const defaultConfig = config.language ? 
      SandboxUtils.getDefaultConfig(config.language) : {};

    const validation = {
      valid: resourceLimitsValid && languageSupported,
      issues: [] as string[],
      warnings: [] as string[],
      recommendations: [] as string[]
    };

    if (!resourceLimitsValid) {
      validation.issues.push('Invalid resource limits specified');
    }

    if (!languageSupported) {
      validation.issues.push('Unsupported language specified');
    }

    // Add recommendations
    if (config.securityLevel === SecurityLevel.LOW) {
      validation.warnings.push('Low security level may pose risks');
      validation.recommendations.push('Consider using MEDIUM or HIGH security level');
    }

    if (config.resourceLimits?.maxMemoryMB && config.resourceLimits.maxMemoryMB > 1024) {
      validation.warnings.push('High memory limit specified');
      validation.recommendations.push('Consider if high memory usage is necessary');
    }

    res.json({
      success: true,
      validation,
      defaultConfig,
      estimatedExecutionTime: config.language ? 
        SandboxUtils.estimateExecutionTime('console.log("test");', config.language) : null
    });
  })
);

/**
 * GET /api/sandbox/supported-languages
 * Get list of supported programming languages
 */
router.get('/supported-languages',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR', 'AGENT']),
  asyncHandler(async (req, res) => {
    const languages = Object.values(Language).map(language => ({
      language,
      supported: SandboxUtils.isLanguageSupported(language),
      defaultConfig: SandboxUtils.getDefaultConfig(language),
      estimatedSetupTime: 5000 // Mock setup time
    }));

    res.json({
      success: true,
      languages,
      totalSupported: languages.filter(l => l.supported).length
    });
  })
);

/**
 * POST /api/sandbox/estimate-resources
 * Estimate resource requirements for code execution
 */
router.post('/estimate-resources',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR', 'AGENT']),
  body('code').notEmpty().withMessage('Code is required'),
  body('language').isIn(Object.values(Language)).withMessage('Invalid language'),
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { code, language } = req.body;

    // Estimate execution time
    const estimatedExecutionTime = SandboxUtils.estimateExecutionTime(code, language);

    // Simple heuristics for resource estimation
    const codeLength = code.length;
    const estimatedMemory = Math.max(64, Math.min(512, codeLength / 100)); // MB
    const estimatedCpu = Math.max(10, Math.min(50, codeLength / 1000)); // Percent

    const estimates = {
      executionTime: estimatedExecutionTime,
      memoryMB: Math.round(estimatedMemory),
      cpuPercent: Math.round(estimatedCpu),
      diskMB: Math.max(10, Math.min(100, codeLength / 10000)),
      complexity: codeLength > 1000 ? 'HIGH' : codeLength > 500 ? 'MEDIUM' : 'LOW',
      recommendedTimeout: Math.max(estimatedExecutionTime * 2, 10000),
      recommendedConfig: SandboxUtils.getDefaultConfig(language)
    };

    logger.debug('Resource estimation requested', {
      language,
      codeLength,
      estimates,
      userId: req.user?.id
    });

    res.json({
      success: true,
      estimates
    });
  })
);

/**
 * GET /api/sandbox/health
 * Get sandbox service health status
 */
router.get('/health',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR']),
  asyncHandler(async (req, res) => {
    const instances = sandboxService.getSandboxInstances();
    const stats = await sandboxService.getExecutionStats();

    const health = {
      status: 'healthy',
      timestamp: new Date(),
      instances: {
        total: instances.length,
        healthy: instances.filter(i => i.healthStatus.healthy).length,
        busy: instances.filter(i => i.status === 'BUSY').length,
        ready: instances.filter(i => i.status === 'READY').length
      },
      performance: {
        totalExecutions: stats.totalExecutions,
        successRate: stats.totalExecutions > 0 ? 
          (stats.successfulExecutions / stats.totalExecutions) * 100 : 100,
        averageExecutionTime: stats.averageExecutionTime,
        resourceUtilization: stats.resourceUtilization
      },
      issues: instances.flatMap(i => i.healthStatus.issues),
      recommendations: [] as string[]
    };

    // Add recommendations based on health status
    if (health.instances.healthy < health.instances.total * 0.8) {
      health.recommendations.push('Some sandbox instances are unhealthy');
    }

    if (health.performance.successRate < 95) {
      health.recommendations.push('Execution success rate is below optimal');
    }

    res.json({
      success: true,
      health
    });
  })
);

export default router;

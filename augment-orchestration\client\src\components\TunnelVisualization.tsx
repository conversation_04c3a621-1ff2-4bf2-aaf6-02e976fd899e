import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Card,
  CardContent,
  Chip,
  IconButton,
  Tooltip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Divider,
  Alert,
} from '@mui/material';
import {
  Tunnel,
  Send,
  Visibility,
  Edit,
  Delete,
  Add,
  SwapHoriz,
  TrendingUp,
  Warning,
  CheckCircle,
} from '@mui/icons-material';
import { Tunnel as TunnelType, Agent } from '@/shared/types';

interface TunnelVisualizationProps {
  tunnels: TunnelType[];
  agents: Agent[];
  onCreateTunnel?: (tunnelData: any) => void;
  onUpdateTunnel?: (id: string, tunnelData: any) => void;
  onDeleteTunnel?: (id: string) => void;
  onSendData?: (tunnelId: string, data: any) => void;
}

export const TunnelVisualization: React.FC<TunnelVisualizationProps> = ({
  tunnels,
  agents,
  onCreateTunnel,
  onUpdateTunnel,
  onDeleteTunnel,
  onSendData,
}) => {
  const [selectedTunnel, setSelectedTunnel] = useState<TunnelType | null>(null);
  const [createDialog, setCreateDialog] = useState(false);
  const [sendDataDialog, setSendDataDialog] = useState(false);
  const [newTunnel, setNewTunnel] = useState({
    name: '',
    description: '',
    fromAgentId: '',
    toAgentId: '',
    tunnelType: 'BIDIRECTIONAL',
    tags: [] as string[],
  });
  const [sendData, setSendData] = useState({
    data: '{}',
    messageType: 'DATA',
    priority: 5,
  });

  const getTunnelStatusColor = (tunnel: TunnelType) => {
    if (!tunnel.isActive) return 'default';
    return 'success';
  };

  const getTunnelTypeIcon = (tunnelType: string) => {
    return tunnelType === 'BIDIRECTIONAL' ? <SwapHoriz /> : <TrendingUp />;
  };

  const handleCreateTunnel = () => {
    if (newTunnel.name && newTunnel.fromAgentId && newTunnel.toAgentId) {
      onCreateTunnel?.(newTunnel);
      setCreateDialog(false);
      setNewTunnel({
        name: '',
        description: '',
        fromAgentId: '',
        toAgentId: '',
        tunnelType: 'BIDIRECTIONAL',
        tags: [],
      });
    }
  };

  const handleSendData = () => {
    if (selectedTunnel && sendData.data) {
      try {
        const parsedData = JSON.parse(sendData.data);
        onSendData?.(selectedTunnel.id, {
          data: parsedData,
          messageType: sendData.messageType,
          priority: sendData.priority,
        });
        setSendDataDialog(false);
        setSendData({
          data: '{}',
          messageType: 'DATA',
          priority: 5,
        });
      } catch (error) {
        alert('Invalid JSON data');
      }
    }
  };

  const getAgentName = (agentId: string) => {
    const agent = agents.find(a => a.id === agentId);
    return agent ? agent.name : 'Unknown Agent';
  };

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h5">Cross-Domain Tunnels</Typography>
        <Button
          startIcon={<Add />}
          variant="contained"
          onClick={() => setCreateDialog(true)}
        >
          Create Tunnel
        </Button>
      </Box>

      {tunnels.length === 0 ? (
        <Alert severity="info">
          No tunnels found. Create a tunnel to enable cross-domain communication between agents.
        </Alert>
      ) : (
        <Grid container spacing={3}>
          {tunnels.map((tunnel) => (
            <Grid item xs={12} md={6} lg={4} key={tunnel.id}>
              <Card 
                sx={{ 
                  cursor: 'pointer',
                  border: selectedTunnel?.id === tunnel.id ? '2px solid #1976d2' : '1px solid #e0e0e0',
                  '&:hover': { boxShadow: 3 }
                }}
                onClick={() => setSelectedTunnel(tunnel)}
              >
                <CardContent>
                  <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
                    <Box display="flex" alignItems="center" gap={1}>
                      {getTunnelTypeIcon(tunnel.tunnelType)}
                      <Typography variant="h6">{tunnel.name}</Typography>
                    </Box>
                    <Chip
                      label={tunnel.isActive ? 'Active' : 'Inactive'}
                      color={getTunnelStatusColor(tunnel)}
                      size="small"
                    />
                  </Box>

                  <Typography variant="body2" color="text.secondary" mb={2}>
                    {tunnel.description || 'No description'}
                  </Typography>

                  <Box mb={2}>
                    <Typography variant="caption" color="text.secondary">
                      From: {getAgentName(tunnel.fromAgentId)}
                    </Typography>
                    <br />
                    <Typography variant="caption" color="text.secondary">
                      To: {getAgentName(tunnel.toAgentId)}
                    </Typography>
                  </Box>

                  <Box display="flex" gap={0.5} flexWrap="wrap" mb={2}>
                    <Chip label={tunnel.tunnelType} size="small" variant="outlined" />
                    {tunnel.tags.map((tag) => (
                      <Chip key={tag} label={tag} size="small" variant="outlined" />
                    ))}
                  </Box>

                  <Box display="flex" justifyContent="space-between" alignItems="center">
                    <Typography variant="caption" color="text.secondary">
                      Data Flows: {tunnel._count?.dataFlows || 0}
                    </Typography>
                    <Box>
                      <Tooltip title="Send Data">
                        <IconButton
                          size="small"
                          onClick={(e) => {
                            e.stopPropagation();
                            setSelectedTunnel(tunnel);
                            setSendDataDialog(true);
                          }}
                          disabled={!tunnel.isActive}
                        >
                          <Send />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Edit">
                        <IconButton
                          size="small"
                          onClick={(e) => {
                            e.stopPropagation();
                            // Handle edit
                          }}
                        >
                          <Edit />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Delete">
                        <IconButton
                          size="small"
                          onClick={(e) => {
                            e.stopPropagation();
                            onDeleteTunnel?.(tunnel.id);
                          }}
                        >
                          <Delete />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {/* Selected Tunnel Details */}
      {selectedTunnel && (
        <Paper sx={{ mt: 3, p: 3 }}>
          <Typography variant="h6" gutterBottom>
            Tunnel Details: {selectedTunnel.name}
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <Typography><strong>Type:</strong> {selectedTunnel.tunnelType}</Typography>
              <Typography><strong>Status:</strong> {selectedTunnel.isActive ? 'Active' : 'Inactive'}</Typography>
              <Typography><strong>From Agent:</strong> {getAgentName(selectedTunnel.fromAgentId)}</Typography>
              <Typography><strong>To Agent:</strong> {getAgentName(selectedTunnel.toAgentId)}</Typography>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography><strong>Created:</strong> {new Date(selectedTunnel.createdAt).toLocaleString()}</Typography>
              <Typography><strong>Data Flows:</strong> {selectedTunnel._count?.dataFlows || 0}</Typography>
              <Typography><strong>Tags:</strong> {selectedTunnel.tags.join(', ') || 'None'}</Typography>
            </Grid>
          </Grid>
        </Paper>
      )}

      {/* Create Tunnel Dialog */}
      <Dialog open={createDialog} onClose={() => setCreateDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Create New Tunnel</DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            label="Tunnel Name"
            value={newTunnel.name}
            onChange={(e) => setNewTunnel(prev => ({ ...prev, name: e.target.value }))}
            margin="normal"
          />
          <TextField
            fullWidth
            label="Description"
            value={newTunnel.description}
            onChange={(e) => setNewTunnel(prev => ({ ...prev, description: e.target.value }))}
            margin="normal"
            multiline
            rows={2}
          />
          <FormControl fullWidth margin="normal">
            <InputLabel>From Agent</InputLabel>
            <Select
              value={newTunnel.fromAgentId}
              onChange={(e) => setNewTunnel(prev => ({ ...prev, fromAgentId: e.target.value }))}
            >
              {agents.map((agent) => (
                <MenuItem key={agent.id} value={agent.id}>
                  {agent.name} ({agent.vendor})
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          <FormControl fullWidth margin="normal">
            <InputLabel>To Agent</InputLabel>
            <Select
              value={newTunnel.toAgentId}
              onChange={(e) => setNewTunnel(prev => ({ ...prev, toAgentId: e.target.value }))}
            >
              {agents.filter(a => a.id !== newTunnel.fromAgentId).map((agent) => (
                <MenuItem key={agent.id} value={agent.id}>
                  {agent.name} ({agent.vendor})
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          <FormControl fullWidth margin="normal">
            <InputLabel>Tunnel Type</InputLabel>
            <Select
              value={newTunnel.tunnelType}
              onChange={(e) => setNewTunnel(prev => ({ ...prev, tunnelType: e.target.value }))}
            >
              <MenuItem value="BIDIRECTIONAL">Bidirectional</MenuItem>
              <MenuItem value="UNIDIRECTIONAL">Unidirectional</MenuItem>
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateDialog(false)}>Cancel</Button>
          <Button onClick={handleCreateTunnel} variant="contained">Create</Button>
        </DialogActions>
      </Dialog>

      {/* Send Data Dialog */}
      <Dialog open={sendDataDialog} onClose={() => setSendDataDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Send Data Through Tunnel</DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            label="JSON Data"
            value={sendData.data}
            onChange={(e) => setSendData(prev => ({ ...prev, data: e.target.value }))}
            margin="normal"
            multiline
            rows={4}
            placeholder='{"message": "Hello", "type": "greeting"}'
          />
          <TextField
            fullWidth
            label="Message Type"
            value={sendData.messageType}
            onChange={(e) => setSendData(prev => ({ ...prev, messageType: e.target.value }))}
            margin="normal"
          />
          <TextField
            fullWidth
            label="Priority (1-10)"
            type="number"
            value={sendData.priority}
            onChange={(e) => setSendData(prev => ({ ...prev, priority: parseInt(e.target.value) }))}
            margin="normal"
            inputProps={{ min: 1, max: 10 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSendDataDialog(false)}>Cancel</Button>
          <Button onClick={handleSendData} variant="contained">Send Data</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default TunnelVisualization;

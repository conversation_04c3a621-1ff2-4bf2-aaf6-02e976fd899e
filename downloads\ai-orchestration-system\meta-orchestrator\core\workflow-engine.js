/**
 * Workflow Engine
 * 
 * Manages mix-and-match workflow automation:
 * - Workflow designer for chaining roles (Analyzer → Generator → Completer → Validator → Documenter)
 * - Automated task routing and result aggregation
 * - Dynamic workflow adaptation based on context and performance
 * - Workflow templates and customization
 */

const EventEmitter = require('events');
const chalk = require('chalk');
const { v4: uuidv4 } = require('uuid');

class WorkflowEngine extends EventEmitter {
  constructor(metaOrchestrator) {
    super();
    this.metaOrchestrator = metaOrchestrator;
    
    // Workflow definitions and templates
    this.workflowTemplates = new Map();
    this.customWorkflows = new Map();
    this.activeWorkflows = new Map();
    
    // Workflow execution history for optimization
    this.executionHistory = [];
    
    // Default workflow templates
    this.defaultTemplates = {
      'feature-implementation': {
        name: 'Feature Implementation',
        description: 'Complete feature implementation workflow',
        steps: [
          {
            id: 'analysis',
            role: 'analyzer',
            name: 'Project Analysis',
            description: 'Analyze project structure and requirements',
            required: true,
            timeout: 30000,
            passContextToNext: true
          },
          {
            id: 'planning',
            role: 'analyzer',
            name: 'Implementation Planning',
            description: 'Create detailed implementation plan',
            required: true,
            timeout: 15000,
            passContextToNext: true
          },
          {
            id: 'generation',
            role: 'generator',
            name: 'Code Generation',
            description: 'Generate implementation code',
            required: true,
            timeout: 60000,
            passContextToNext: true,
            adaptiveSelection: true
          },
          {
            id: 'completion',
            role: 'completer',
            name: 'Code Enhancement',
            description: 'Enhance and complete generated code',
            required: false,
            timeout: 15000,
            passContextToNext: true
          },
          {
            id: 'validation',
            role: 'validator',
            name: 'Code Validation',
            description: 'Validate code quality and functionality',
            required: true,
            timeout: 45000,
            passContextToNext: true
          },
          {
            id: 'documentation',
            role: 'documenter',
            name: 'Documentation Generation',
            description: 'Generate comprehensive documentation',
            required: false,
            timeout: 30000,
            passContextToNext: false
          }
        ],
        routing: {
          singleFile: { generation: 'cursor' },
          multiFile: { generation: 'windsurf' },
          complexity: {
            low: { generation: 'cursor', validation: 'qodo' },
            medium: { generation: 'windsurf', validation: 'augment-code' },
            high: { generation: 'windsurf', validation: 'augment-code' }
          }
        }
      },
      
      'bug-fix': {
        name: 'Bug Fix',
        description: 'Comprehensive bug fixing workflow',
        steps: [
          {
            id: 'analysis',
            role: 'analyzer',
            name: 'Bug Analysis',
            description: 'Analyze bug and affected code',
            required: true,
            timeout: 30000,
            passContextToNext: true
          },
          {
            id: 'generation',
            role: 'generator',
            name: 'Fix Generation',
            description: 'Generate bug fix',
            required: true,
            timeout: 45000,
            passContextToNext: true
          },
          {
            id: 'validation',
            role: 'validator',
            name: 'Fix Validation',
            description: 'Validate fix and test',
            required: true,
            timeout: 30000,
            passContextToNext: true
          },
          {
            id: 'documentation',
            role: 'documenter',
            name: 'Fix Documentation',
            description: 'Document the fix and changes',
            required: false,
            timeout: 15000,
            passContextToNext: false
          }
        ]
      },
      
      'code-review': {
        name: 'Code Review',
        description: 'Automated code review workflow',
        steps: [
          {
            id: 'analysis',
            role: 'analyzer',
            name: 'Code Analysis',
            description: 'Analyze code structure and quality',
            required: true,
            timeout: 30000,
            passContextToNext: true
          },
          {
            id: 'validation',
            role: 'validator',
            name: 'Quality Validation',
            description: 'Validate code quality and standards',
            required: true,
            timeout: 45000,
            passContextToNext: true
          },
          {
            id: 'documentation',
            role: 'documenter',
            name: 'Review Documentation',
            description: 'Generate review comments and suggestions',
            required: true,
            timeout: 20000,
            passContextToNext: false
          }
        ]
      },
      
      'refactoring': {
        name: 'Code Refactoring',
        description: 'Comprehensive refactoring workflow',
        steps: [
          {
            id: 'analysis',
            role: 'analyzer',
            name: 'Refactoring Analysis',
            description: 'Analyze code for refactoring opportunities',
            required: true,
            timeout: 30000,
            passContextToNext: true
          },
          {
            id: 'planning',
            role: 'analyzer',
            name: 'Refactoring Planning',
            description: 'Plan refactoring strategy',
            required: true,
            timeout: 15000,
            passContextToNext: true
          },
          {
            id: 'generation',
            role: 'generator',
            name: 'Code Refactoring',
            description: 'Perform code refactoring',
            required: true,
            timeout: 60000,
            passContextToNext: true
          },
          {
            id: 'validation',
            role: 'validator',
            name: 'Refactoring Validation',
            description: 'Validate refactored code',
            required: true,
            timeout: 45000,
            passContextToNext: true
          },
          {
            id: 'documentation',
            role: 'documenter',
            name: 'Refactoring Documentation',
            description: 'Document refactoring changes',
            required: false,
            timeout: 20000,
            passContextToNext: false
          }
        ]
      },
      
      'testing': {
        name: 'Test Generation',
        description: 'Comprehensive test generation workflow',
        steps: [
          {
            id: 'analysis',
            role: 'analyzer',
            name: 'Test Analysis',
            description: 'Analyze code for test requirements',
            required: true,
            timeout: 20000,
            passContextToNext: true
          },
          {
            id: 'generation',
            role: 'generator',
            name: 'Test Generation',
            description: 'Generate comprehensive tests',
            required: true,
            timeout: 45000,
            passContextToNext: true
          },
          {
            id: 'validation',
            role: 'validator',
            name: 'Test Validation',
            description: 'Validate and run generated tests',
            required: true,
            timeout: 30000,
            passContextToNext: true
          },
          {
            id: 'documentation',
            role: 'documenter',
            name: 'Test Documentation',
            description: 'Document test strategy and coverage',
            required: false,
            timeout: 15000,
            passContextToNext: false
          }
        ]
      }
    };
  }
  
  async initialize() {
    try {
      console.log(chalk.blue('🔄 Initializing workflow engine...'));
      
      // Load default templates
      this.loadDefaultTemplates();
      
      // Load custom workflows from configuration
      await this.loadCustomWorkflows();
      
      // Setup workflow optimization
      this.setupWorkflowOptimization();
      
      console.log(chalk.green('✅ Workflow engine initialized'));
      
    } catch (error) {
      console.error(chalk.red('❌ Failed to initialize workflow engine:'), error);
      throw error;
    }
  }
  
  loadDefaultTemplates() {
    for (const [templateId, template] of Object.entries(this.defaultTemplates)) {
      this.workflowTemplates.set(templateId, template);
    }
    
    console.log(chalk.green(`📋 Loaded ${this.workflowTemplates.size} default workflow templates`));
  }
  
  async loadCustomWorkflows() {
    try {
      // Try to load custom workflows from configuration
      const customWorkflows = await this.metaOrchestrator.configManager.getCustomWorkflows();
      
      if (customWorkflows) {
        for (const [workflowId, workflow] of Object.entries(customWorkflows)) {
          this.customWorkflows.set(workflowId, workflow);
        }
        
        console.log(chalk.green(`📋 Loaded ${this.customWorkflows.size} custom workflows`));
      }
      
    } catch (error) {
      console.log(chalk.yellow('⚠️ No custom workflows found, using defaults only'));
    }
  }
  
  setupWorkflowOptimization() {
    // Analyze execution history every 10 minutes to optimize workflows
    setInterval(() => {
      this.optimizeWorkflows();
    }, 600000);
  }
  
  async determineWorkflow(request) {
    try {
      const workflowType = this.analyzeRequestType(request);
      const workflow = this.getWorkflowTemplate(workflowType);
      
      if (!workflow) {
        throw new Error(`No workflow template found for type: ${workflowType}`);
      }
      
      // Customize workflow based on request context
      const customizedWorkflow = await this.customizeWorkflow(workflow, request);
      
      console.log(chalk.blue(`🎯 Selected workflow: ${customizedWorkflow.name}`));
      
      return customizedWorkflow;
      
    } catch (error) {
      console.error(chalk.red('❌ Failed to determine workflow:'), error);
      throw error;
    }
  }
  
  analyzeRequestType(request) {
    const description = (request.description || '').toLowerCase();
    const type = request.type || 'unknown';
    
    // Analyze request to determine appropriate workflow
    if (type === 'feature' || description.includes('implement') || description.includes('feature')) {
      return 'feature-implementation';
    }
    
    if (type === 'bug' || description.includes('fix') || description.includes('bug') || description.includes('error')) {
      return 'bug-fix';
    }
    
    if (type === 'review' || description.includes('review') || description.includes('analyze')) {
      return 'code-review';
    }
    
    if (type === 'refactor' || description.includes('refactor') || description.includes('improve')) {
      return 'refactoring';
    }
    
    if (type === 'test' || description.includes('test') || description.includes('testing')) {
      return 'testing';
    }
    
    // Default to feature implementation for unknown types
    return 'feature-implementation';
  }
  
  getWorkflowTemplate(workflowType) {
    // Check custom workflows first
    if (this.customWorkflows.has(workflowType)) {
      return this.customWorkflows.get(workflowType);
    }
    
    // Fall back to default templates
    return this.workflowTemplates.get(workflowType);
  }
  
  async customizeWorkflow(workflow, request) {
    const customizedWorkflow = JSON.parse(JSON.stringify(workflow)); // Deep clone
    
    // Analyze request context for customization
    const context = this.analyzeRequestContext(request);
    
    // Apply routing rules based on context
    if (workflow.routing) {
      this.applyRoutingRules(customizedWorkflow, context);
    }
    
    // Adjust timeouts based on complexity
    this.adjustTimeouts(customizedWorkflow, context);
    
    // Filter optional steps based on requirements
    this.filterOptionalSteps(customizedWorkflow, request);
    
    return customizedWorkflow;
  }
  
  analyzeRequestContext(request) {
    const context = {
      complexity: 'medium',
      fileCount: 1,
      isMultiFile: false,
      language: 'javascript',
      framework: null,
      hasTests: false,
      hasDocumentation: false
    };
    
    // Analyze file count
    if (request.files && Array.isArray(request.files)) {
      context.fileCount = request.files.length;
      context.isMultiFile = request.files.length > 1;
    }
    
    // Analyze complexity
    const description = (request.description || '').toLowerCase();
    if (description.includes('simple') || description.includes('basic')) {
      context.complexity = 'low';
    } else if (description.includes('complex') || description.includes('advanced') || description.includes('enterprise')) {
      context.complexity = 'high';
    }
    
    // Detect language and framework
    if (request.language) {
      context.language = request.language;
    }
    
    if (request.framework) {
      context.framework = request.framework;
    }
    
    // Check for existing tests and documentation
    if (request.includeTests || description.includes('test')) {
      context.hasTests = true;
    }
    
    if (request.includeDocumentation || description.includes('document')) {
      context.hasDocumentation = true;
    }
    
    return context;
  }
  
  applyRoutingRules(workflow, context) {
    if (!workflow.routing) return;
    
    // Apply file-based routing
    if (context.isMultiFile && workflow.routing.multiFile) {
      this.applyStepRouting(workflow, workflow.routing.multiFile);
    } else if (!context.isMultiFile && workflow.routing.singleFile) {
      this.applyStepRouting(workflow, workflow.routing.singleFile);
    }
    
    // Apply complexity-based routing
    if (workflow.routing.complexity && workflow.routing.complexity[context.complexity]) {
      this.applyStepRouting(workflow, workflow.routing.complexity[context.complexity]);
    }
  }
  
  applyStepRouting(workflow, routing) {
    for (const step of workflow.steps) {
      if (routing[step.role]) {
        step.preferredAssistant = routing[step.role];
      }
    }
  }
  
  adjustTimeouts(workflow, context) {
    const complexityMultiplier = {
      low: 0.7,
      medium: 1.0,
      high: 1.5
    };
    
    const multiplier = complexityMultiplier[context.complexity] || 1.0;
    
    for (const step of workflow.steps) {
      if (step.timeout) {
        step.timeout = Math.floor(step.timeout * multiplier);
      }
    }
  }
  
  filterOptionalSteps(workflow, request) {
    // Remove optional steps based on request preferences
    if (request.skipDocumentation) {
      workflow.steps = workflow.steps.filter(step => step.role !== 'documenter');
    }
    
    if (request.skipValidation) {
      workflow.steps = workflow.steps.filter(step => step.role !== 'validator');
    }
    
    if (request.skipCompletion) {
      workflow.steps = workflow.steps.filter(step => step.role !== 'completer');
    }
  }
  
  async createSimplifiedWorkflow(request) {
    // Create a minimal workflow for recovery scenarios
    return {
      name: 'Simplified Recovery',
      description: 'Minimal workflow for error recovery',
      steps: [
        {
          id: 'generation',
          role: 'generator',
          name: 'Basic Generation',
          description: 'Generate basic implementation',
          required: true,
          timeout: 30000,
          passContextToNext: false
        }
      ]
    };
  }
  
  optimizeWorkflows() {
    if (this.executionHistory.length < 10) return; // Need sufficient data
    
    console.log(chalk.blue('🧠 Optimizing workflows based on execution history...'));
    
    // Analyze execution patterns
    const workflowPerformance = this.analyzeWorkflowPerformance();
    
    // Optimize step ordering
    this.optimizeStepOrdering(workflowPerformance);
    
    // Optimize timeouts
    this.optimizeTimeouts(workflowPerformance);
    
    // Optimize assistant selection
    this.optimizeAssistantSelection(workflowPerformance);
  }
  
  analyzeWorkflowPerformance() {
    const performance = new Map();
    
    for (const execution of this.executionHistory) {
      const workflowType = execution.workflowType;
      
      if (!performance.has(workflowType)) {
        performance.set(workflowType, {
          totalExecutions: 0,
          successfulExecutions: 0,
          averageDuration: 0,
          stepPerformance: new Map()
        });
      }
      
      const perf = performance.get(workflowType);
      perf.totalExecutions++;
      
      if (execution.success) {
        perf.successfulExecutions++;
      }
      
      // Update average duration
      const totalDuration = perf.averageDuration * (perf.totalExecutions - 1) + execution.duration;
      perf.averageDuration = totalDuration / perf.totalExecutions;
      
      // Analyze step performance
      for (const stepResult of execution.stepResults) {
        if (!perf.stepPerformance.has(stepResult.stepId)) {
          perf.stepPerformance.set(stepResult.stepId, {
            totalExecutions: 0,
            successfulExecutions: 0,
            averageDuration: 0
          });
        }
        
        const stepPerf = perf.stepPerformance.get(stepResult.stepId);
        stepPerf.totalExecutions++;
        
        if (stepResult.success) {
          stepPerf.successfulExecutions++;
        }
        
        const stepTotalDuration = stepPerf.averageDuration * (stepPerf.totalExecutions - 1) + stepResult.duration;
        stepPerf.averageDuration = stepTotalDuration / stepPerf.totalExecutions;
      }
    }
    
    return performance;
  }
  
  optimizeStepOrdering(workflowPerformance) {
    // Analyze if reordering steps could improve performance
    // This is a simplified implementation - could be more sophisticated
    
    for (const [workflowType, performance] of workflowPerformance) {
      const workflow = this.workflowTemplates.get(workflowType);
      
      if (workflow && performance.totalExecutions >= 5) {
        // Check if any steps consistently fail and could benefit from reordering
        const failingSteps = [];
        
        for (const [stepId, stepPerf] of performance.stepPerformance) {
          const successRate = stepPerf.successfulExecutions / stepPerf.totalExecutions;
          if (successRate < 0.8) {
            failingSteps.push({ stepId, successRate });
          }
        }
        
        if (failingSteps.length > 0) {
          console.log(chalk.yellow(`⚠️ Workflow ${workflowType} has failing steps:`, failingSteps));
          // Could implement step reordering logic here
        }
      }
    }
  }
  
  optimizeTimeouts(workflowPerformance) {
    for (const [workflowType, performance] of workflowPerformance) {
      const workflow = this.workflowTemplates.get(workflowType);
      
      if (workflow && performance.totalExecutions >= 5) {
        for (const step of workflow.steps) {
          const stepPerf = performance.stepPerformance.get(step.id);
          
          if (stepPerf && stepPerf.totalExecutions >= 3) {
            // Adjust timeout based on average duration
            const recommendedTimeout = Math.ceil(stepPerf.averageDuration * 1.5);
            
            if (Math.abs(step.timeout - recommendedTimeout) > 5000) {
              console.log(chalk.blue(`🔧 Optimizing timeout for ${workflowType}.${step.id}: ${step.timeout}ms → ${recommendedTimeout}ms`));
              step.timeout = recommendedTimeout;
            }
          }
        }
      }
    }
  }
  
  optimizeAssistantSelection(workflowPerformance) {
    // This would integrate with the role manager to optimize assistant selection
    // based on workflow performance data
    
    for (const [workflowType, performance] of workflowPerformance) {
      if (performance.successfulExecutions / performance.totalExecutions < 0.8) {
        console.log(chalk.yellow(`⚠️ Workflow ${workflowType} has low success rate, consider assistant optimization`));
        this.emit('workflowOptimizationNeeded', { workflowType, performance });
      }
    }
  }
  
  recordExecution(workflowType, execution) {
    this.executionHistory.push({
      workflowType,
      timestamp: Date.now(),
      ...execution
    });
    
    // Keep only last 1000 executions
    if (this.executionHistory.length > 1000) {
      this.executionHistory.shift();
    }
  }
  
  // Public methods
  
  getAvailableWorkflows() {
    const workflows = new Map();
    
    // Add default templates
    for (const [id, template] of this.workflowTemplates) {
      workflows.set(id, template);
    }
    
    // Add custom workflows
    for (const [id, workflow] of this.customWorkflows) {
      workflows.set(id, workflow);
    }
    
    return Object.fromEntries(workflows);
  }
  
  getWorkflow(workflowId) {
    return this.customWorkflows.get(workflowId) || this.workflowTemplates.get(workflowId);
  }
  
  async createCustomWorkflow(workflowId, workflow) {
    this.customWorkflows.set(workflowId, workflow);
    await this.metaOrchestrator.configManager.saveCustomWorkflow(workflowId, workflow);
    
    this.emit('workflowCreated', { workflowId, workflow });
    console.log(chalk.green(`✅ Created custom workflow: ${workflowId}`));
  }
  
  async updateWorkflow(workflowId, updates) {
    const workflow = this.getWorkflow(workflowId);
    
    if (!workflow) {
      throw new Error(`Workflow not found: ${workflowId}`);
    }
    
    const updatedWorkflow = { ...workflow, ...updates };
    
    if (this.customWorkflows.has(workflowId)) {
      this.customWorkflows.set(workflowId, updatedWorkflow);
      await this.metaOrchestrator.configManager.saveCustomWorkflow(workflowId, updatedWorkflow);
    } else {
      // Convert default template to custom workflow
      this.customWorkflows.set(workflowId, updatedWorkflow);
      await this.metaOrchestrator.configManager.saveCustomWorkflow(workflowId, updatedWorkflow);
    }
    
    this.emit('workflowUpdated', { workflowId, workflow: updatedWorkflow });
    console.log(chalk.green(`✅ Updated workflow: ${workflowId}`));
  }
  
  getWorkflowStats() {
    return {
      totalTemplates: this.workflowTemplates.size,
      customWorkflows: this.customWorkflows.size,
      activeWorkflows: this.activeWorkflows.size,
      executionHistory: this.executionHistory.length
    };
  }
}

module.exports = WorkflowEngine;

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.evolutionRoutes = void 0;
const express_1 = require("express");
const client_1 = require("@prisma/client");
const errorHandler_1 = require("../middleware/errorHandler");
const DarwinGodelMachine_1 = require("../services/DarwinGodelMachine");
const router = (0, express_1.Router)();
exports.evolutionRoutes = router;
const prisma = new client_1.PrismaClient();
const darwinMachine = new DarwinGodelMachine_1.DarwinGodelMachine();
// Get evolution variants
router.get('/variants', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const variants = await prisma.evolutionVariant.findMany({
        include: {
            parentAgent: {
                select: {
                    id: true,
                    agentId: true,
                    name: true,
                    vendor: true,
                },
            },
        },
        orderBy: {
            generation: 'desc',
        },
    });
    res.json({
        success: true,
        data: variants,
    });
}));
// Start evolution process
router.post('/start', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { parameters } = req.body;
    const sessionId = await darwinMachine.startEvolution(parameters);
    res.status(201).json({
        success: true,
        data: {
            sessionId,
            message: 'Evolution process started',
        },
    });
}));
// Stop evolution process
router.post('/stop', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    await darwinMachine.stopEvolution();
    res.json({
        success: true,
        message: 'Evolution process stopped',
    });
}));
// Get evolution status
router.get('/status', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const status = await darwinMachine.getEvolutionStatus();
    res.json({
        success: true,
        data: status,
    });
}));
// Get evolution history
router.get('/history', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const limit = parseInt(req.query.limit) || 50;
    const history = await darwinMachine.getEvolutionHistory(limit);
    res.json({
        success: true,
        data: history,
    });
}));

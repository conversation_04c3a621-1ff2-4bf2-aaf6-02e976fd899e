"""
Side Pocket Manager for Search and Rescue

Manages the "side pocket" temporary data group for erroneous, uncertain, or
misclassified detections that require manual review and retraining.
"""

import json
import logging
import shutil
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from pathlib import Path
from collections import defaultdict
import hashlib


class SidePocketManager:
    """
    Manages temporary storage and analysis of uncertain or erroneous detections
    for Search and Rescue operations, supporting continuous model improvement.
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize the Side Pocket Manager.
        
        Args:
            config: Configuration for side pocket management
        """
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # Storage configuration
        self.base_path = Path(self.config.get('base_path', './side_pocket_data'))
        self.retention_days = self.config.get('retention_days', 90)  # Longer retention for training
        self.auto_review_enabled = self.config.get('auto_review_enabled', True)
        self.retraining_threshold = self.config.get('retraining_threshold', 50)  # Items before retraining
        
        # Side pocket categories
        self.categories = {
            'uncertain': 'Low confidence detections requiring review',
            'misclassified': 'Known incorrect classifications for retraining',
            'ambiguous': 'Detections that could not be clearly categorized',
            'false_positive': 'Confirmed false positive detections',
            'edge_case': 'Unusual or edge case detections',
            'environmental': 'Environmental factors affecting detection quality'
        }
        
        # Quality thresholds for side pocket placement
        self.quality_thresholds = {
            'uncertain_confidence': 0.5,      # Below this goes to uncertain
            'misclassification_penalty': 0.3, # Penalty for known misclassifications
            'ambiguous_threshold': 0.4,       # Threshold for ambiguous classifications
            'false_positive_indicators': [    # Patterns indicating false positives
                'size_anomaly', 'context_mismatch', 'environmental_artifact'
            ]
        }
        
        # Initialize storage
        self._initialize_storage()
        
        # Statistics tracking
        self.stats = {
            'total_items': 0,
            'by_category': defaultdict(int),
            'by_mission': defaultdict(int),
            'retraining_batches': 0,
            'review_completed': 0
        }
    
    def _initialize_storage(self) -> None:
        """Initialize side pocket storage structure."""
        try:
            # Create main directories
            self.base_path.mkdir(parents=True, exist_ok=True)
            
            # Create category subdirectories
            for category in self.categories:
                (self.base_path / category).mkdir(exist_ok=True)
            
            # Create special directories
            (self.base_path / 'reviewed').mkdir(exist_ok=True)
            (self.base_path / 'retraining_batches').mkdir(exist_ok=True)
            (self.base_path / 'analytics').mkdir(exist_ok=True)
            
            self.logger.info(f"Initialized side pocket storage at {self.base_path}")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize side pocket storage: {str(e)}")
            raise
    
    def add_to_side_pocket(self, detection_data: Dict[str, Any], 
                          reason: str, 
                          category: str = 'uncertain',
                          mission_context: Dict[str, Any] = None) -> str:
        """
        Add a detection to the side pocket for review.
        
        Args:
            detection_data: The detection data to store
            reason: Reason for side pocket placement
            category: Side pocket category
            mission_context: Optional mission context
            
        Returns:
            Unique identifier for the side pocket item
        """
        try:
            # Generate unique ID
            item_id = self._generate_item_id(detection_data, mission_context)
            
            # Create side pocket entry
            side_pocket_entry = {
                'item_id': item_id,
                'timestamp': datetime.utcnow().isoformat(),
                'category': category,
                'reason': reason,
                'detection_data': detection_data,
                'mission_context': mission_context or {},
                'review_status': 'pending',
                'metadata': {
                    'added_by': 'feedback_loop_system',
                    'priority': self._calculate_priority(detection_data, reason),
                    'estimated_review_time': self._estimate_review_time(category),
                    'retraining_value': self._assess_retraining_value(detection_data, reason)
                }
            }
            
            # Store the entry
            file_path = self._get_storage_path(category, item_id)
            with open(file_path, 'w') as f:
                json.dump(side_pocket_entry, f, indent=2)
            
            # Update statistics
            self.stats['total_items'] += 1
            self.stats['by_category'][category] += 1
            mission_id = mission_context.get('mission_id', 'unknown') if mission_context else 'unknown'
            self.stats['by_mission'][mission_id] += 1
            
            # Check if auto-review should be triggered
            if self.auto_review_enabled:
                self._check_auto_review_triggers()
            
            self.logger.info(f"Added item {item_id} to side pocket category '{category}': {reason}")
            return item_id
            
        except Exception as e:
            self.logger.error(f"Failed to add item to side pocket: {str(e)}")
            return ""
    
    def review_item(self, item_id: str, 
                   review_result: Dict[str, Any], 
                   reviewer: str = 'system') -> bool:
        """
        Review and update a side pocket item.
        
        Args:
            item_id: Unique identifier of the item to review
            review_result: Review results and corrections
            reviewer: Identifier of the reviewer
            
        Returns:
            True if review was successful, False otherwise
        """
        try:
            # Find the item
            item_path = self._find_item_path(item_id)
            if not item_path:
                self.logger.warning(f"Item {item_id} not found for review")
                return False
            
            # Load the item
            with open(item_path, 'r') as f:
                item_data = json.load(f)
            
            # Update with review results
            item_data['review_status'] = 'completed'
            item_data['review_timestamp'] = datetime.utcnow().isoformat()
            item_data['reviewer'] = reviewer
            item_data['review_result'] = review_result
            
            # Determine final classification
            final_category = review_result.get('final_category', item_data['category'])
            corrected_classification = review_result.get('corrected_classification')
            
            # Move to reviewed directory
            reviewed_path = self.base_path / 'reviewed' / f"{item_id}.json"
            with open(reviewed_path, 'w') as f:
                json.dump(item_data, f, indent=2)
            
            # Remove from original location
            item_path.unlink()
            
            # Add to retraining queue if applicable
            if corrected_classification and review_result.get('use_for_retraining', True):
                self._add_to_retraining_queue(item_data, corrected_classification)
            
            # Update statistics
            self.stats['review_completed'] += 1
            
            self.logger.info(f"Completed review of item {item_id} by {reviewer}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to review item {item_id}: {str(e)}")
            return False
    
    def get_pending_reviews(self, category: Optional[str] = None, 
                           priority: Optional[str] = None,
                           limit: int = 50) -> List[Dict[str, Any]]:
        """
        Get pending items for review.
        
        Args:
            category: Optional category filter
            priority: Optional priority filter ('high', 'medium', 'low')
            limit: Maximum number of items to return
            
        Returns:
            List of pending review items
        """
        try:
            pending_items = []
            
            # Determine which categories to search
            search_categories = [category] if category else list(self.categories.keys())
            
            for cat in search_categories:
                cat_path = self.base_path / cat
                if not cat_path.exists():
                    continue
                
                for item_file in cat_path.glob('*.json'):
                    try:
                        with open(item_file, 'r') as f:
                            item_data = json.load(f)
                        
                        # Check if item is pending
                        if item_data.get('review_status') != 'pending':
                            continue
                        
                        # Apply priority filter
                        if priority and item_data.get('metadata', {}).get('priority') != priority:
                            continue
                        
                        pending_items.append(item_data)
                        
                        if len(pending_items) >= limit:
                            break
                            
                    except Exception as e:
                        self.logger.warning(f"Failed to load item {item_file}: {str(e)}")
                
                if len(pending_items) >= limit:
                    break
            
            # Sort by priority and timestamp
            pending_items.sort(key=lambda x: (
                self._priority_sort_key(x.get('metadata', {}).get('priority', 'low')),
                x.get('timestamp', '')
            ))
            
            return pending_items[:limit]
            
        except Exception as e:
            self.logger.error(f"Failed to get pending reviews: {str(e)}")
            return []
    
    def create_retraining_batch(self, batch_name: Optional[str] = None) -> Dict[str, Any]:
        """
        Create a retraining batch from reviewed items.
        
        Args:
            batch_name: Optional name for the batch
            
        Returns:
            Batch information and statistics
        """
        try:
            if not batch_name:
                batch_name = f"batch_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
            
            # Collect reviewed items suitable for retraining
            retraining_items = self._collect_retraining_items()
            
            if len(retraining_items) < self.retraining_threshold:
                self.logger.info(f"Insufficient items for retraining batch: {len(retraining_items)} < {self.retraining_threshold}")
                return {'status': 'insufficient_items', 'count': len(retraining_items)}
            
            # Create batch structure
            batch_data = {
                'batch_name': batch_name,
                'created_timestamp': datetime.utcnow().isoformat(),
                'item_count': len(retraining_items),
                'items': retraining_items,
                'statistics': self._calculate_batch_statistics(retraining_items),
                'metadata': {
                    'creator': 'side_pocket_manager',
                    'purpose': 'model_retraining',
                    'quality_score': self._calculate_batch_quality(retraining_items)
                }
            }
            
            # Save batch
            batch_path = self.base_path / 'retraining_batches' / f"{batch_name}.json"
            with open(batch_path, 'w') as f:
                json.dump(batch_data, f, indent=2)
            
            # Update statistics
            self.stats['retraining_batches'] += 1
            
            self.logger.info(f"Created retraining batch '{batch_name}' with {len(retraining_items)} items")
            
            return {
                'status': 'success',
                'batch_name': batch_name,
                'item_count': len(retraining_items),
                'batch_path': str(batch_path),
                'statistics': batch_data['statistics']
            }
            
        except Exception as e:
            self.logger.error(f"Failed to create retraining batch: {str(e)}")
            return {'status': 'error', 'error': str(e)}
    
    def get_analytics(self, time_range: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        """
        Get analytics on side pocket usage and patterns.
        
        Args:
            time_range: Optional time range filter
            
        Returns:
            Analytics data
        """
        try:
            analytics = {
                'summary': self.stats.copy(),
                'category_distribution': dict(self.stats['by_category']),
                'mission_distribution': dict(self.stats['by_mission']),
                'review_efficiency': self._calculate_review_efficiency(),
                'common_issues': self._analyze_common_issues(),
                'retraining_impact': self._assess_retraining_impact(),
                'generated_at': datetime.utcnow().isoformat()
            }
            
            # Apply time range filter if provided
            if time_range:
                analytics = self._filter_analytics_by_time(analytics, time_range)
            
            return analytics
            
        except Exception as e:
            self.logger.error(f"Failed to generate analytics: {str(e)}")
            return {}
    
    def cleanup_old_items(self, retention_days: Optional[int] = None) -> int:
        """
        Clean up old side pocket items beyond retention period.
        
        Args:
            retention_days: Optional override for retention period
            
        Returns:
            Number of items cleaned up
        """
        try:
            retention_days = retention_days or self.retention_days
            cutoff_date = datetime.utcnow() - timedelta(days=retention_days)
            cleaned_count = 0
            
            # Clean up reviewed items
            reviewed_path = self.base_path / 'reviewed'
            if reviewed_path.exists():
                for item_file in reviewed_path.glob('*.json'):
                    try:
                        file_time = datetime.fromtimestamp(item_file.stat().st_mtime)
                        if file_time < cutoff_date:
                            item_file.unlink()
                            cleaned_count += 1
                    except Exception as e:
                        self.logger.warning(f"Failed to clean up file {item_file}: {str(e)}")
            
            # Clean up old retraining batches
            batches_path = self.base_path / 'retraining_batches'
            if batches_path.exists():
                for batch_file in batches_path.glob('*.json'):
                    try:
                        file_time = datetime.fromtimestamp(batch_file.stat().st_mtime)
                        if file_time < cutoff_date:
                            batch_file.unlink()
                            cleaned_count += 1
                    except Exception as e:
                        self.logger.warning(f"Failed to clean up batch {batch_file}: {str(e)}")
            
            self.logger.info(f"Cleaned up {cleaned_count} old side pocket items")
            return cleaned_count
            
        except Exception as e:
            self.logger.error(f"Failed to cleanup old items: {str(e)}")
            return 0
    
    def _generate_item_id(self, detection_data: Dict[str, Any], mission_context: Dict[str, Any]) -> str:
        """Generate unique identifier for side pocket item."""
        timestamp = datetime.utcnow().strftime('%Y%m%d_%H%M%S_%f')
        mission_id = mission_context.get('mission_id', 'unknown') if mission_context else 'unknown'
        data_hash = hashlib.md5(str(detection_data).encode()).hexdigest()[:8]
        
        return f"SP_{mission_id}_{timestamp}_{data_hash}"
    
    def _get_storage_path(self, category: str, item_id: str) -> Path:
        """Get storage path for a side pocket item."""
        return self.base_path / category / f"{item_id}.json"
    
    def _calculate_priority(self, detection_data: Dict[str, Any], reason: str) -> str:
        """Calculate priority level for review."""
        # High priority for potential targets
        if 'target' in str(detection_data).lower():
            return 'high'
        
        # High priority for safety-critical misclassifications
        if 'safety' in reason.lower() or 'critical' in reason.lower():
            return 'high'
        
        # Medium priority for reference items
        if any(cat in str(detection_data).lower() for cat in ['clothing', 'personal', 'broken']):
            return 'medium'
        
        return 'low'
    
    def _estimate_review_time(self, category: str) -> int:
        """Estimate review time in minutes."""
        time_estimates = {
            'uncertain': 5,
            'misclassified': 10,
            'ambiguous': 8,
            'false_positive': 3,
            'edge_case': 15,
            'environmental': 5
        }
        return time_estimates.get(category, 10)
    
    def _assess_retraining_value(self, detection_data: Dict[str, Any], reason: str) -> float:
        """Assess the value of this item for model retraining."""
        value = 0.5  # Base value
        
        # Higher value for clear misclassifications
        if 'misclassified' in reason.lower():
            value += 0.3
        
        # Higher value for edge cases
        if 'edge' in reason.lower() or 'unusual' in reason.lower():
            value += 0.2
        
        # Higher value for high-confidence wrong predictions
        confidence = detection_data.get('confidence', 0.0)
        if confidence > 0.8:  # High confidence but wrong
            value += 0.2
        
        return min(value, 1.0)
    
    def _find_item_path(self, item_id: str) -> Optional[Path]:
        """Find the file path for a given item ID."""
        for category in self.categories:
            item_path = self.base_path / category / f"{item_id}.json"
            if item_path.exists():
                return item_path
        return None
    
    def _add_to_retraining_queue(self, item_data: Dict[str, Any], corrected_classification: Dict[str, Any]) -> None:
        """Add reviewed item to retraining queue."""
        # This would integrate with the model retraining pipeline
        # For now, just log the action
        item_id = item_data.get('item_id', 'unknown')
        self.logger.info(f"Added item {item_id} to retraining queue with corrected classification")
    
    def _priority_sort_key(self, priority: str) -> int:
        """Convert priority string to sort key."""
        priority_order = {'high': 0, 'medium': 1, 'low': 2}
        return priority_order.get(priority, 3)
    
    def _collect_retraining_items(self) -> List[Dict[str, Any]]:
        """Collect reviewed items suitable for retraining."""
        retraining_items = []
        
        reviewed_path = self.base_path / 'reviewed'
        if not reviewed_path.exists():
            return retraining_items
        
        for item_file in reviewed_path.glob('*.json'):
            try:
                with open(item_file, 'r') as f:
                    item_data = json.load(f)
                
                # Check if item is suitable for retraining
                if (item_data.get('review_result', {}).get('use_for_retraining', False) and
                    'corrected_classification' in item_data.get('review_result', {})):
                    retraining_items.append(item_data)
                    
            except Exception as e:
                self.logger.warning(f"Failed to load reviewed item {item_file}: {str(e)}")
        
        return retraining_items
    
    def _calculate_batch_statistics(self, items: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate statistics for a retraining batch."""
        if not items:
            return {}
        
        categories = [item.get('category', 'unknown') for item in items]
        priorities = [item.get('metadata', {}).get('priority', 'unknown') for item in items]
        
        return {
            'total_items': len(items),
            'category_distribution': {cat: categories.count(cat) for cat in set(categories)},
            'priority_distribution': {pri: priorities.count(pri) for pri in set(priorities)},
            'average_retraining_value': sum(
                item.get('metadata', {}).get('retraining_value', 0.0) for item in items
            ) / len(items)
        }
    
    def _calculate_batch_quality(self, items: List[Dict[str, Any]]) -> float:
        """Calculate overall quality score for a retraining batch."""
        if not items:
            return 0.0
        
        quality_factors = []
        
        for item in items:
            # Factor in retraining value
            retraining_value = item.get('metadata', {}).get('retraining_value', 0.0)
            quality_factors.append(retraining_value)
            
            # Factor in review quality
            review_result = item.get('review_result', {})
            if review_result.get('reviewer_confidence', 0.0) > 0.8:
                quality_factors.append(0.2)  # Bonus for high reviewer confidence
        
        return sum(quality_factors) / len(quality_factors) if quality_factors else 0.0
    
    def _calculate_review_efficiency(self) -> Dict[str, Any]:
        """Calculate review efficiency metrics."""
        total_items = self.stats['total_items']
        reviewed_items = self.stats['review_completed']
        
        if total_items == 0:
            return {'efficiency_rate': 0.0, 'pending_items': 0}
        
        return {
            'efficiency_rate': reviewed_items / total_items,
            'pending_items': total_items - reviewed_items,
            'review_completion_rate': reviewed_items / total_items
        }
    
    def _analyze_common_issues(self) -> List[Dict[str, Any]]:
        """Analyze common issues found in side pocket items."""
        # This would analyze patterns in side pocket items
        # For now, return placeholder data
        return [
            {'issue': 'low_lighting_conditions', 'frequency': 15, 'impact': 'medium'},
            {'issue': 'environmental_artifacts', 'frequency': 12, 'impact': 'low'},
            {'issue': 'size_estimation_errors', 'frequency': 8, 'impact': 'high'}
        ]
    
    def _assess_retraining_impact(self) -> Dict[str, Any]:
        """Assess the impact of retraining from side pocket data."""
        # This would measure model improvement from retraining
        # For now, return placeholder data
        return {
            'batches_created': self.stats['retraining_batches'],
            'estimated_accuracy_improvement': 0.05,  # 5% improvement estimate
            'model_versions_updated': self.stats['retraining_batches']
        }
    
    def _filter_analytics_by_time(self, analytics: Dict[str, Any], time_range: Dict[str, str]) -> Dict[str, Any]:
        """Filter analytics data by time range."""
        # This would filter analytics based on time range
        # For now, return analytics as-is
        return analytics
    
    def _check_auto_review_triggers(self) -> None:
        """Check if auto-review should be triggered."""
        # Check if we have enough items for a retraining batch
        if self.stats['total_items'] % self.retraining_threshold == 0:
            self.logger.info(f"Auto-review trigger: {self.stats['total_items']} items reached")
            # This would trigger automated review processes
    
    def get_stats(self) -> Dict[str, Any]:
        """Get current side pocket statistics."""
        return self.stats.copy()

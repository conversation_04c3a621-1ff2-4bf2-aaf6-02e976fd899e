{"version": "1.0.0", "lastUpdated": "2025-01-02T15:30:00Z", "updateFrequency": "daily", "sources": {"official": "Provider APIs and public dashboards", "estimated": "Research-based calculations", "community": "User-reported data"}, "providers": {"chatgpt": {"name": "ChatGPT", "models": {"gpt-4": {"energyPerToken": 0.0028, "waterPerToken": 0.0342, "carbonPerToken": 0.00156, "efficiency": "medium", "lastUpdated": "2025-01-02T12:00:00Z", "source": "estimated", "confidence": 0.85}, "gpt-4-turbo": {"energyPerToken": 0.0024, "waterPerToken": 0.0298, "carbonPerToken": 0.00134, "efficiency": "high", "lastUpdated": "2025-01-02T12:00:00Z", "source": "estimated", "confidence": 0.82}, "gpt-3.5-turbo": {"energyPerToken": 0.0018, "waterPerToken": 0.0215, "carbonPerToken": 0.00098, "efficiency": "high", "lastUpdated": "2025-01-02T12:00:00Z", "source": "estimated", "confidence": 0.88}}, "infrastructure": {"dataCenter": "Microsoft Azure", "region": "US-West", "renewableEnergy": 0.65, "pue": 1.12, "coolingEfficiency": "high"}, "usage": {"dailyQueries": 100000000, "peakHours": ["09:00-11:00", "14:00-16:00"], "averageTokensPerQuery": 150, "responseTokens": 300}}, "claude": {"name": "<PERSON>", "models": {"claude-3-opus": {"energyPerToken": 0.0032, "waterPerToken": 0.0385, "carbonPerToken": 0.00178, "efficiency": "medium", "lastUpdated": "2025-01-02T12:00:00Z", "source": "estimated", "confidence": 0.78}, "claude-3-sonnet": {"energyPerToken": 0.0022, "waterPerToken": 0.0268, "carbonPerToken": 0.00125, "efficiency": "high", "lastUpdated": "2025-01-02T12:00:00Z", "source": "estimated", "confidence": 0.81}, "claude-3-haiku": {"energyPerToken": 0.0015, "waterPerToken": 0.0182, "carbonPerToken": 0.00085, "efficiency": "excellent", "lastUpdated": "2025-01-02T12:00:00Z", "source": "estimated", "confidence": 0.85}}, "infrastructure": {"dataCenter": "AWS", "region": "US-East", "renewableEnergy": 0.72, "pue": 1.08, "coolingEfficiency": "excellent"}, "usage": {"dailyQueries": 25000000, "peakHours": ["10:00-12:00", "15:00-17:00"], "averageTokensPerQuery": 180, "responseTokens": 420}}, "gemini": {"name": "Gemini", "models": {"gemini-ultra": {"energyPerToken": 0.0035, "waterPerToken": 0.0425, "carbonPerToken": 0.00195, "efficiency": "medium", "lastUpdated": "2025-01-02T12:00:00Z", "source": "estimated", "confidence": 0.75}, "gemini-pro": {"energyPerToken": 0.0026, "waterPerToken": 0.0315, "carbonPerToken": 0.00148, "efficiency": "medium", "lastUpdated": "2025-01-02T12:00:00Z", "source": "estimated", "confidence": 0.79}, "gemini-nano": {"energyPerToken": 0.0012, "waterPerToken": 0.0145, "carbonPerToken": 0.00068, "efficiency": "excellent", "lastUpdated": "2025-01-02T12:00:00Z", "source": "estimated", "confidence": 0.82}}, "infrastructure": {"dataCenter": "Google Cloud", "region": "Global", "renewableEnergy": 0.85, "pue": 1.05, "coolingEfficiency": "excellent"}, "usage": {"dailyQueries": 75000000, "peakHours": ["08:00-10:00", "13:00-15:00"], "averageTokensPerQuery": 140, "responseTokens": 280}}, "perplexity": {"name": "Perplexity", "models": {"perplexity-pro": {"energyPerToken": 0.0029, "waterPerToken": 0.0348, "carbonPerToken": 0.00162, "efficiency": "medium", "lastUpdated": "2025-01-02T12:00:00Z", "source": "estimated", "confidence": 0.7}}, "infrastructure": {"dataCenter": "Mixed", "region": "US-West", "renewableEnergy": 0.58, "pue": 1.15, "coolingEfficiency": "medium"}, "usage": {"dailyQueries": 5000000, "peakHours": ["11:00-13:00", "16:00-18:00"], "averageTokensPerQuery": 200, "responseTokens": 350}}}, "globalAverages": {"energyPerToken": 0.0024, "waterPerToken": 0.0289, "carbonPerToken": 0.00134, "totalDailyQueries": 205000000, "totalDailyTokens": 61500000000, "totalDailyEnergy": 147600, "totalDailyWater": 1777350, "totalDailyCarbon": 82410}, "trends": {"efficiency": {"direction": "improving", "rate": 0.05, "period": "monthly"}, "usage": {"direction": "increasing", "rate": 0.15, "period": "monthly"}, "renewableEnergy": {"direction": "increasing", "rate": 0.08, "period": "quarterly"}}, "metadata": {"collectionMethod": "automated", "updateSchedule": "02:00 UTC daily", "nextUpdate": "2025-01-03T02:00:00Z", "dataRetention": "90 days", "accuracy": "±15%"}}
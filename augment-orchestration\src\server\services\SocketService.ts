import { Server as SocketIOServer, Socket } from 'socket.io';
import { EventBus, EVENT_TYPES } from './EventBus';
import { EventBusMessage, SocketEventType } from '@/shared/types';
import { logger } from '../utils/logger';

interface ConnectedUser {
  id: string;
  userId?: string;
  username?: string;
  joinedAt: Date;
  lastActivity: Date;
}

export class SocketService {
  private io: SocketIOServer;
  private eventBus: EventBus;
  private connectedUsers: Map<string, ConnectedUser> = new Map();

  constructor(io: SocketIOServer, eventBus: EventBus) {
    this.io = io;
    this.eventBus = eventBus;
  }

  public async initialize(): Promise<void> {
    this.setupSocketHandlers();
    this.setupEventBusSubscriptions();
    logger.info('SocketService initialized');
  }

  private setupSocketHandlers(): void {
    this.io.on('connection', (socket: Socket) => {
      logger.info(`Client connected: ${socket.id}`);
      
      // Add user to connected users
      this.connectedUsers.set(socket.id, {
        id: socket.id,
        joinedAt: new Date(),
        lastActivity: new Date(),
      });

      // Update connection count
      this.broadcastConnectionCount();

      // Handle user authentication
      socket.on('authenticate', (data: { userId: string; username: string }) => {
        const user = this.connectedUsers.get(socket.id);
        if (user) {
          user.userId = data.userId;
          user.username = data.username;
          this.connectedUsers.set(socket.id, user);
          
          logger.info(`User authenticated: ${data.username} (${socket.id})`);
          
          // Broadcast user presence
          this.eventBus.publish(
            EVENT_TYPES.USER_PRESENCE,
            { type: 'joined', userId: data.userId, username: data.username },
            'socket-service'
          );
        }
      });

      // Handle room joining
      socket.on('join-room', (room: string) => {
        socket.join(room);
        logger.debug(`Client ${socket.id} joined room: ${room}`);
      });

      // Handle room leaving
      socket.on('leave-room', (room: string) => {
        socket.leave(room);
        logger.debug(`Client ${socket.id} left room: ${room}`);
      });

      // Handle orchestrator selection
      socket.on('select-orchestrator', (orchestratorId: string) => {
        socket.join(`orchestrator:${orchestratorId}`);
        logger.debug(`Client ${socket.id} selected orchestrator: ${orchestratorId}`);
      });

      // Handle workflow subscription
      socket.on('subscribe-workflow', (workflowId: string) => {
        socket.join(`workflow:${workflowId}`);
        logger.debug(`Client ${socket.id} subscribed to workflow: ${workflowId}`);
      });

      // Handle tunnel subscription
      socket.on('subscribe-tunnel', (tunnelId: string) => {
        socket.join(`tunnel:${tunnelId}`);
        logger.debug(`Client ${socket.id} subscribed to tunnel: ${tunnelId}`);
      });

      // Handle activity tracking
      socket.on('activity', () => {
        const user = this.connectedUsers.get(socket.id);
        if (user) {
          user.lastActivity = new Date();
          this.connectedUsers.set(socket.id, user);
        }
      });

      // Handle disconnection
      socket.on('disconnect', (reason) => {
        logger.info(`Client disconnected: ${socket.id}, reason: ${reason}`);
        
        const user = this.connectedUsers.get(socket.id);
        if (user?.userId) {
          // Broadcast user presence
          this.eventBus.publish(
            EVENT_TYPES.USER_PRESENCE,
            { type: 'left', userId: user.userId, username: user.username },
            'socket-service'
          );
        }
        
        this.connectedUsers.delete(socket.id);
        this.broadcastConnectionCount();
      });

      // Handle ping/pong for connection health
      socket.on('ping', () => {
        socket.emit('pong');
      });
    });
  }

  private setupEventBusSubscriptions(): void {
    // Subscribe to all events and broadcast to relevant clients
    this.eventBus.subscribeAll((message: EventBusMessage) => {
      this.handleEventBusMessage(message);
    });

    logger.info('EventBus subscriptions setup completed');
  }

  private handleEventBusMessage(message: EventBusMessage): void {
    const { type, payload } = message;

    switch (type) {
      case EVENT_TYPES.ORCHESTRATOR_CREATED:
      case EVENT_TYPES.ORCHESTRATOR_UPDATED:
      case EVENT_TYPES.ORCHESTRATOR_DELETED:
        this.io.emit('orchestrator-update', { type, payload });
        break;

      case EVENT_TYPES.AGENT_ASSIGNED:
      case EVENT_TYPES.AGENT_STATUS_CHANGED:
      case EVENT_TYPES.AGENT_EVOLVED:
        this.io.emit('agent-update', { type, payload });
        if (payload.orchestratorId) {
          this.io.to(`orchestrator:${payload.orchestratorId}`).emit('agent-update', { type, payload });
        }
        break;

      case EVENT_TYPES.TUNNEL_CREATED:
      case EVENT_TYPES.TUNNEL_ACTIVATED:
      case EVENT_TYPES.TUNNEL_DATA_FLOW:
        this.io.emit('tunnel-update', { type, payload });
        if (payload.tunnelId) {
          this.io.to(`tunnel:${payload.tunnelId}`).emit('tunnel-update', { type, payload });
        }
        break;

      case EVENT_TYPES.WORKFLOW_STARTED:
      case EVENT_TYPES.WORKFLOW_PROGRESS:
      case EVENT_TYPES.WORKFLOW_COMPLETED:
      case EVENT_TYPES.WORKFLOW_FAILED:
        this.io.emit('workflow-update', { type, payload });
        if (payload.workflowId) {
          this.io.to(`workflow:${payload.workflowId}`).emit('workflow-update', { type, payload });
        }
        break;

      case EVENT_TYPES.EVOLUTION_MUTATION:
      case EVENT_TYPES.EVOLUTION_SELECTION:
      case EVENT_TYPES.EVOLUTION_PROMOTION:
        this.io.emit('evolution-update', { type, payload });
        break;

      case EVENT_TYPES.CONTEXT_UPDATED:
      case EVENT_TYPES.CONTEXT_SHARED:
        this.io.emit('context-update', { type, payload });
        break;

      case EVENT_TYPES.USER_PRESENCE:
        this.io.emit('user-presence', payload);
        break;

      case EVENT_TYPES.SYSTEM_STATUS:
        this.io.emit('system-status', payload);
        break;

      default:
        // Broadcast unknown events as generic updates
        this.io.emit('generic-update', { type, payload });
        break;
    }
  }

  private broadcastConnectionCount(): void {
    const count = this.connectedUsers.size;
    this.io.emit('connection-count', { count });
    
    // Also publish to event bus
    this.eventBus.publish(
      EVENT_TYPES.SYSTEM_STATUS,
      { type: 'connection_count', count },
      'socket-service'
    );
  }

  /**
   * Broadcast a message to all connected clients
   */
  public broadcast(event: string, data: any): void {
    this.io.emit(event, data);
  }

  /**
   * Send a message to a specific room
   */
  public broadcastToRoom(room: string, event: string, data: any): void {
    this.io.to(room).emit(event, data);
  }

  /**
   * Send a message to a specific client
   */
  public sendToClient(socketId: string, event: string, data: any): void {
    this.io.to(socketId).emit(event, data);
  }

  /**
   * Get connected users statistics
   */
  public getConnectedUsers(): {
    total: number;
    authenticated: number;
    users: Array<{ id: string; username?: string; joinedAt: Date; lastActivity: Date }>;
  } {
    const users = Array.from(this.connectedUsers.values());
    
    return {
      total: users.length,
      authenticated: users.filter(u => u.userId).length,
      users: users.map(u => ({
        id: u.id,
        username: u.username,
        joinedAt: u.joinedAt,
        lastActivity: u.lastActivity,
      })),
    };
  }

  /**
   * Disconnect a specific client
   */
  public disconnectClient(socketId: string, reason?: string): void {
    const socket = this.io.sockets.sockets.get(socketId);
    if (socket) {
      socket.disconnect(true);
      logger.info(`Forcibly disconnected client: ${socketId}, reason: ${reason || 'admin action'}`);
    }
  }
}

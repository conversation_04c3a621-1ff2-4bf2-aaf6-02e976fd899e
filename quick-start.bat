@echo off
title Time Stamp Project - Quick Start
color 0A

echo.
echo ========================================
echo   🚀 Time Stamp Project Quick Start
echo ========================================
echo.

:menu
echo 📋 Available Commands:
echo.
echo   1. List all projects
echo   2. Check project status  
echo   3. Start EcoStamp Backend (port 3000)
echo   4. Run Security Scanner
echo   5. Test all projects
echo   6. Install all dependencies
echo   7. Show help
echo   8. Exit
echo.
set /p choice="Enter your choice (1-8): "

if "%choice%"=="1" goto list
if "%choice%"=="2" goto status
if "%choice%"=="3" goto ecostamp
if "%choice%"=="4" goto scanner
if "%choice%"=="5" goto test
if "%choice%"=="6" goto install
if "%choice%"=="7" goto help
if "%choice%"=="8" goto exit
echo Invalid choice. Please try again.
echo.
goto menu

:list
echo.
echo 📦 Listing all projects...
node run-projects.js list
echo.
pause
goto menu

:status
echo.
echo 📊 Checking project status...
node run-projects.js status
echo.
pause
goto menu

:ecostamp
echo.
echo 🚀 Starting EcoStamp Backend...
echo 🌐 Server will be available at: http://localhost:3000
echo ⏹️  Press Ctrl+C to stop
echo.
node run-projects.js start ecostamp
echo.
pause
goto menu

:scanner
echo.
echo 🔒 Running Security Scanner...
node run-projects.js start scanner
echo.
pause
goto menu

:test
echo.
echo 🧪 Testing all projects...
node run-projects.js test all
echo.
pause
goto menu

:install
echo.
echo 📦 Installing all dependencies...
node run-projects.js install all
echo.
pause
goto menu

:help
echo.
echo 📖 Help - Manual Commands:
echo.
echo   node run-projects.js list
echo   node run-projects.js status
echo   node run-projects.js start ecostamp
echo   node run-projects.js start scanner
echo   node run-projects.js start orchestrator
echo   node run-projects.js test all
echo   node run-projects.js install all
echo.
echo 🌐 EcoStamp Backend: http://localhost:3000
echo 📁 Project Structure:
echo   - core-systems/EcoStamp/source (Backend)
echo   - development-tools (Security Scanner)
echo   - ai-orchestration (AI Orchestration)
echo   - core-systems (Python Framework)
echo.
pause
goto menu

:exit
echo.
echo 👋 Thanks for using Time Stamp Project!
echo.
exit /b 0

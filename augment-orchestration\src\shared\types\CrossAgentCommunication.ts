/**
 * Cross-Agent Communication Protocol Types and Interfaces
 * 
 * Core Gap 4: Standardized protocol for secure communication between different
 * AI agents with authentication, message routing, and conflict resolution.
 */

export enum MessageType {
  // Basic communication
  PING = 'PING',
  PONG = 'PONG',
  HANDSHAKE = 'HANDSHAKE',
  ACKNOWLEDGMENT = 'ACKNOWLEDGMENT',
  
  // Task coordination
  TASK_REQUEST = 'TASK_REQUEST',
  TASK_RESPONSE = 'TASK_RESPONSE',
  TASK_UPDATE = 'TASK_UPDATE',
  TASK_COMPLETION = 'TASK_COMPLETION',
  TASK_CANCELLATION = 'TASK_CANCELLATION',
  
  // Resource management
  RESOURCE_REQUEST = 'RESOURCE_REQUEST',
  RESOURCE_GRANT = 'RESOURCE_GRANT',
  RESOURCE_DENY = 'RESOURCE_DENY',
  RESOURCE_RELEASE = 'RESOURCE_RELEASE',
  RESOURCE_LOCK = 'RESOURCE_LOCK',
  
  // Conflict resolution
  CONFLICT_DETECTED = 'CONFLICT_DETECTED',
  CONFLICT_RESOLUTION = 'CONFLICT_RESOLUTION',
  PRIORITY_CLAIM = 'PRIORITY_CLAIM',
  PRIORITY_YIELD = 'PRIORITY_YIELD',
  
  // Information sharing
  KNOWLEDGE_SHARE = 'KNOWLEDGE_SHARE',
  STATE_SYNC = 'STATE_SYNC',
  CAPABILITY_ANNOUNCE = 'CAPABILITY_ANNOUNCE',
  STATUS_UPDATE = 'STATUS_UPDATE',
  
  // Error handling
  ERROR = 'ERROR',
  RETRY_REQUEST = 'RETRY_REQUEST',
  TIMEOUT = 'TIMEOUT',
  
  // Security
  AUTH_CHALLENGE = 'AUTH_CHALLENGE',
  AUTH_RESPONSE = 'AUTH_RESPONSE',
  SECURITY_ALERT = 'SECURITY_ALERT'
}

export enum AgentRole {
  ORCHESTRATOR = 'ORCHESTRATOR',
  SPECIALIST = 'SPECIALIST',
  COORDINATOR = 'COORDINATOR',
  MONITOR = 'MONITOR',
  SECURITY = 'SECURITY',
  BACKUP = 'BACKUP'
}

export enum AgentStatus {
  ONLINE = 'ONLINE',
  OFFLINE = 'OFFLINE',
  BUSY = 'BUSY',
  IDLE = 'IDLE',
  MAINTENANCE = 'MAINTENANCE',
  ERROR = 'ERROR'
}

export enum Priority {
  CRITICAL = 'CRITICAL',
  HIGH = 'HIGH',
  MEDIUM = 'MEDIUM',
  LOW = 'LOW',
  BACKGROUND = 'BACKGROUND'
}

export enum ConflictType {
  RESOURCE_CONTENTION = 'RESOURCE_CONTENTION',
  TASK_OVERLAP = 'TASK_OVERLAP',
  PRIORITY_CONFLICT = 'PRIORITY_CONFLICT',
  STATE_INCONSISTENCY = 'STATE_INCONSISTENCY',
  CAPABILITY_CONFLICT = 'CAPABILITY_CONFLICT'
}

export interface AgentIdentity {
  id: string;
  name: string;
  role: AgentRole;
  version: string;
  capabilities: string[];
  publicKey: string;
  endpoint: string;
  metadata: Record<string, any>;
}

export interface Message {
  id: string;
  type: MessageType;
  senderId: string;
  receiverId: string;
  timestamp: Date;
  priority: Priority;
  payload: any;
  signature: string;
  correlationId?: string;
  replyTo?: string;
  ttl?: number; // Time to live in milliseconds
  retryCount?: number;
  metadata: MessageMetadata;
}

export interface MessageMetadata {
  encrypted: boolean;
  compressed: boolean;
  contentType: string;
  encoding: string;
  checksum: string;
  routingHints?: string[];
  traceId?: string;
  spanId?: string;
}

export interface HandshakeRequest {
  agentIdentity: AgentIdentity;
  protocolVersion: string;
  supportedFeatures: string[];
  securityLevel: 'BASIC' | 'STANDARD' | 'HIGH' | 'MAXIMUM';
  challenge?: string;
}

export interface HandshakeResponse {
  accepted: boolean;
  agentIdentity: AgentIdentity;
  protocolVersion: string;
  supportedFeatures: string[];
  securityLevel: 'BASIC' | 'STANDARD' | 'HIGH' | 'MAXIMUM';
  challengeResponse?: string;
  sessionToken?: string;
  error?: string;
}

export interface TaskRequest {
  taskId: string;
  taskType: string;
  description: string;
  parameters: Record<string, any>;
  requiredCapabilities: string[];
  priority: Priority;
  deadline?: Date;
  dependencies?: string[];
  constraints?: TaskConstraints;
}

export interface TaskConstraints {
  maxExecutionTime?: number;
  maxMemoryUsage?: number;
  maxCpuUsage?: number;
  requiredResources?: string[];
  exclusiveAccess?: boolean;
  allowParallel?: boolean;
}

export interface TaskResponse {
  taskId: string;
  accepted: boolean;
  estimatedDuration?: number;
  estimatedStartTime?: Date;
  requiredResources?: string[];
  reason?: string;
  alternativeAgents?: string[];
}

export interface TaskUpdate {
  taskId: string;
  status: 'QUEUED' | 'RUNNING' | 'PAUSED' | 'COMPLETED' | 'FAILED' | 'CANCELLED';
  progress: number; // 0-100
  currentStep?: string;
  estimatedCompletion?: Date;
  resourceUsage?: ResourceUsage;
  intermediateResults?: any;
  logs?: string[];
}

export interface ResourceUsage {
  cpu: number; // Percentage
  memory: number; // MB
  disk: number; // MB
  network: number; // KB/s
  customMetrics?: Record<string, number>;
}

export interface ResourceRequest {
  resourceId: string;
  resourceType: string;
  accessType: 'READ' | 'WRITE' | 'EXCLUSIVE';
  duration?: number;
  priority: Priority;
  justification: string;
}

export interface ResourceGrant {
  resourceId: string;
  granted: boolean;
  accessToken?: string;
  expiresAt?: Date;
  conditions?: string[];
  reason?: string;
}

export interface ConflictReport {
  conflictId: string;
  type: ConflictType;
  involvedAgents: string[];
  resourcesInvolved: string[];
  description: string;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  detectedAt: Date;
  suggestedResolution?: ConflictResolution;
}

export interface ConflictResolution {
  resolutionId: string;
  conflictId: string;
  strategy: 'PRIORITY_BASED' | 'ROUND_ROBIN' | 'RESOURCE_SHARING' | 'TASK_SPLITTING' | 'MANUAL';
  actions: ResolutionAction[];
  expectedOutcome: string;
  implementedBy: string;
  implementedAt?: Date;
}

export interface ResolutionAction {
  agentId: string;
  action: 'YIELD' | 'WAIT' | 'RETRY' | 'CANCEL' | 'MODIFY' | 'ESCALATE';
  parameters?: Record<string, any>;
  timeout?: number;
}

export interface KnowledgeShare {
  knowledgeId: string;
  category: string;
  title: string;
  content: any;
  relevantAgents?: string[];
  expiresAt?: Date;
  confidenceLevel: number; // 0-1
  source: string;
  tags: string[];
}

export interface StateSync {
  stateId: string;
  entityType: string;
  entityId: string;
  state: any;
  version: number;
  lastModified: Date;
  modifiedBy: string;
  checksum: string;
}

export interface CapabilityAnnouncement {
  capabilities: AgentCapability[];
  availableFrom?: Date;
  availableUntil?: Date;
  loadFactor: number; // 0-1, current load
  maxConcurrentTasks: number;
  specializations: string[];
}

export interface AgentCapability {
  name: string;
  version: string;
  description: string;
  inputTypes: string[];
  outputTypes: string[];
  parameters: CapabilityParameter[];
  performance: PerformanceMetrics;
  reliability: number; // 0-1
}

export interface CapabilityParameter {
  name: string;
  type: string;
  required: boolean;
  description: string;
  defaultValue?: any;
  constraints?: any;
}

export interface PerformanceMetrics {
  averageExecutionTime: number;
  successRate: number;
  throughput: number;
  resourceEfficiency: number;
  lastUpdated: Date;
}

export interface SecurityAlert {
  alertId: string;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  type: 'AUTHENTICATION_FAILURE' | 'UNAUTHORIZED_ACCESS' | 'SUSPICIOUS_ACTIVITY' | 'PROTOCOL_VIOLATION';
  description: string;
  involvedAgents: string[];
  evidence: any;
  recommendedActions: string[];
  detectedAt: Date;
}

export interface CommunicationStats {
  totalMessages: number;
  messagesByType: Record<MessageType, number>;
  averageLatency: number;
  errorRate: number;
  throughput: number;
  activeConnections: number;
  bandwidthUsage: number;
  lastUpdated: Date;
}

export interface RoutingTable {
  routes: Route[];
  defaultRoute?: string;
  lastUpdated: Date;
}

export interface Route {
  destination: string;
  nextHop: string;
  cost: number;
  latency: number;
  reliability: number;
  lastVerified: Date;
}

// Protocol configuration
export interface ProtocolConfig {
  version: string;
  features: ProtocolFeature[];
  security: SecurityConfig;
  routing: RoutingConfig;
  reliability: ReliabilityConfig;
  performance: PerformanceConfig;
}

export interface ProtocolFeature {
  name: string;
  version: string;
  enabled: boolean;
  configuration?: Record<string, any>;
}

export interface SecurityConfig {
  encryptionRequired: boolean;
  signatureRequired: boolean;
  authenticationMethod: 'TOKEN' | 'CERTIFICATE' | 'MUTUAL_TLS';
  keyRotationInterval: number;
  maxSessionDuration: number;
  allowedCiphers: string[];
}

export interface RoutingConfig {
  strategy: 'DIRECT' | 'HUB_AND_SPOKE' | 'MESH' | 'HIERARCHICAL';
  maxHops: number;
  routingTableTtl: number;
  loadBalancing: boolean;
  failoverEnabled: boolean;
}

export interface ReliabilityConfig {
  maxRetries: number;
  retryBackoff: 'LINEAR' | 'EXPONENTIAL' | 'FIXED';
  timeoutMs: number;
  heartbeatInterval: number;
  duplicateDetection: boolean;
  orderingGuarantee: boolean;
}

export interface PerformanceConfig {
  maxConcurrentConnections: number;
  messageQueueSize: number;
  compressionEnabled: boolean;
  batchingEnabled: boolean;
  batchSize: number;
  batchTimeout: number;
}

// Event types for monitoring
export interface CommunicationEvent {
  eventId: string;
  type: 'MESSAGE_SENT' | 'MESSAGE_RECEIVED' | 'CONNECTION_ESTABLISHED' | 'CONNECTION_LOST' | 'ERROR_OCCURRED';
  timestamp: Date;
  agentId: string;
  details: any;
  severity: 'INFO' | 'WARN' | 'ERROR' | 'CRITICAL';
}

// Error types
export class CommunicationError extends Error {
  constructor(
    message: string,
    public code: string,
    public agentId?: string,
    public messageId?: string,
    public details?: any
  ) {
    super(message);
    this.name = 'CommunicationError';
  }
}

export class AuthenticationError extends CommunicationError {
  constructor(message: string, agentId?: string, details?: any) {
    super(message, 'AUTHENTICATION_ERROR', agentId, undefined, details);
  }
}

export class RoutingError extends CommunicationError {
  constructor(message: string, messageId?: string, details?: any) {
    super(message, 'ROUTING_ERROR', undefined, messageId, details);
  }
}

export class ConflictResolutionError extends CommunicationError {
  constructor(message: string, details?: any) {
    super(message, 'CONFLICT_RESOLUTION_ERROR', undefined, undefined, details);
  }
}

// Constants
export const PROTOCOL_CONSTANTS = {
  VERSION: '1.0.0',
  MAX_MESSAGE_SIZE: 10 * 1024 * 1024, // 10MB
  MAX_PAYLOAD_SIZE: 8 * 1024 * 1024, // 8MB
  DEFAULT_TIMEOUT: 30000, // 30 seconds
  HEARTBEAT_INTERVAL: 60000, // 1 minute
  MAX_RETRIES: 3,
  SESSION_TIMEOUT: 3600000, // 1 hour
  ROUTING_TABLE_TTL: 300000, // 5 minutes
  MAX_CONCURRENT_TASKS: 100,
  COMPRESSION_THRESHOLD: 1024, // 1KB
  BATCH_SIZE: 10,
  BATCH_TIMEOUT: 1000 // 1 second
};

// Utility functions
export const CommunicationUtils = {
  generateMessageId: (): string => {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  },

  generateCorrelationId: (): string => {
    return `corr_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  },

  calculateChecksum: (data: any): string => {
    // This would use a proper hash function in implementation
    return btoa(JSON.stringify(data)).slice(0, 16);
  },

  validateMessage: (message: Message): boolean => {
    return !!(
      message.id &&
      message.type &&
      message.senderId &&
      message.receiverId &&
      message.timestamp &&
      message.priority &&
      message.signature
    );
  },

  isExpired: (message: Message): boolean => {
    if (!message.ttl) return false;
    return Date.now() - message.timestamp.getTime() > message.ttl;
  },

  calculatePriority: (urgency: number, importance: number): Priority => {
    const score = urgency * 0.6 + importance * 0.4;
    if (score >= 0.9) return Priority.CRITICAL;
    if (score >= 0.7) return Priority.HIGH;
    if (score >= 0.4) return Priority.MEDIUM;
    if (score >= 0.2) return Priority.LOW;
    return Priority.BACKGROUND;
  },

  estimateLatency: (fromAgent: string, toAgent: string, routingTable: RoutingTable): number => {
    const route = routingTable.routes.find(r => r.destination === toAgent);
    return route?.latency || 100; // Default 100ms
  }
};

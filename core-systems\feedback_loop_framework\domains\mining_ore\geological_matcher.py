"""
Geological Pattern Matcher for Mining Operations

Pattern matching component that analyzes ore detection patterns against
geological models, formation types, and mineralization processes for
comprehensive geological assessment.
"""

import logging
import math
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from collections import defaultdict


class GeologicalPatternMatcher:
    """
    Matches ore detection patterns against geological models and formation types.
    
    Analyzes detection patterns, validates against geological knowledge,
    and identifies potential ore bodies and mineralization processes.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.geological_models = self._initialize_geological_models()
        self.mineralization_patterns = self._initialize_mineralization_patterns()
        
    def _initialize_geological_models(self) -> Dict[str, Any]:
        """Initialize geological models and formation patterns."""
        return {
            'ore_deposit_types': {
                'hydrothermal_veins': {
                    'characteristics': {
                        'geometry': 'linear',
                        'width_range_m': [0.1, 10.0],
                        'length_range_m': [10, 1000],
                        'dip_angle_range': [30, 90],
                        'mineral_associations': ['quartz', 'gold', 'silver', 'copper']
                    },
                    'formation_process': 'hydrothermal_fluid_circulation',
                    'host_rock_types': ['granite', 'schist', 'volcanic'],
                    'economic_potential': 'high'
                },
                'placer_deposits': {
                    'characteristics': {
                        'geometry': 'layered',
                        'thickness_range_m': [0.5, 5.0],
                        'extent_range_m': [100, 10000],
                        'grain_size': 'coarse_to_fine',
                        'mineral_associations': ['gold', 'platinum', 'diamonds']
                    },
                    'formation_process': 'sedimentary_concentration',
                    'host_rock_types': ['alluvium', 'gravel', 'sand'],
                    'economic_potential': 'medium'
                },
                'porphyry_deposits': {
                    'characteristics': {
                        'geometry': 'cylindrical',
                        'diameter_range_m': [500, 3000],
                        'depth_range_m': [200, 2000],
                        'alteration_zones': ['potassic', 'phyllic', 'argillic'],
                        'mineral_associations': ['copper', 'molybdenum', 'gold']
                    },
                    'formation_process': 'magmatic_intrusion',
                    'host_rock_types': ['porphyry', 'granite', 'diorite'],
                    'economic_potential': 'very_high'
                }
            },
            'structural_controls': {
                'fault_systems': {
                    'ore_localization': 'high',
                    'preferred_orientations': ['NE-SW', 'NW-SE'],
                    'dilation_zones': 'favorable',
                    'intersection_zones': 'highly_favorable'
                },
                'fold_structures': {
                    'ore_localization': 'medium',
                    'hinge_zones': 'favorable',
                    'limb_zones': 'less_favorable',
                    'competency_contrasts': 'important'
                },
                'contact_zones': {
                    'ore_localization': 'high',
                    'intrusive_contacts': 'highly_favorable',
                    'metamorphic_aureoles': 'favorable',
                    'skarn_development': 'very_favorable'
                }
            },
            'alteration_patterns': {
                'hydrothermal_alteration': {
                    'propylitic': {
                        'minerals': ['chlorite', 'epidote', 'calcite'],
                        'temperature_range_c': [200, 300],
                        'ore_association': 'distal'
                    },
                    'phyllic': {
                        'minerals': ['sericite', 'quartz', 'pyrite'],
                        'temperature_range_c': [300, 450],
                        'ore_association': 'proximal'
                    },
                    'potassic': {
                        'minerals': ['k_feldspar', 'biotite', 'quartz'],
                        'temperature_range_c': [450, 600],
                        'ore_association': 'core'
                    }
                }
            }
        }
    
    def _initialize_mineralization_patterns(self) -> Dict[str, Any]:
        """Initialize mineralization process patterns."""
        return {
            'metal_associations': {
                'precious_metals': {
                    'primary_associations': ['Au-Ag', 'Au-Cu', 'Ag-Pb-Zn'],
                    'pathfinder_elements': ['As', 'Sb', 'Hg', 'Te'],
                    'gangue_minerals': ['quartz', 'calcite', 'barite']
                },
                'base_metals': {
                    'primary_associations': ['Cu-Mo', 'Pb-Zn', 'Cu-Pb-Zn'],
                    'pathfinder_elements': ['Bi', 'W', 'Sn', 'In'],
                    'gangue_minerals': ['quartz', 'fluorite', 'sphalerite']
                }
            },
            'zoning_patterns': {
                'temperature_zoning': {
                    'high_temp_core': ['molybdenum', 'tungsten', 'tin'],
                    'medium_temp_zone': ['copper', 'lead', 'zinc'],
                    'low_temp_periphery': ['mercury', 'antimony', 'arsenic']
                },
                'chemical_zoning': {
                    'reducing_environment': ['gold', 'silver', 'copper'],
                    'oxidizing_environment': ['iron', 'manganese', 'uranium'],
                    'neutral_environment': ['lead', 'zinc', 'fluorite']
                }
            },
            'grade_continuity': {
                'high_grade_zones': {
                    'characteristics': ['structural_control', 'alteration_intensity'],
                    'predictability': 'high',
                    'exploration_priority': 'critical'
                },
                'low_grade_zones': {
                    'characteristics': ['disseminated_mineralization', 'large_volume'],
                    'predictability': 'medium',
                    'exploration_priority': 'moderate'
                }
            }
        }
    
    def match(self, interpreted_output: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Match interpreted ore detections against geological patterns.
        
        Args:
            interpreted_output: Normalized ore detection data from interpreter
            context: Mining context including geological formation, target minerals
            
        Returns:
            Match results with geological pattern analysis and ore body assessment
        """
        try:
            match_results = {
                'overall_geological_fit': 0.0,
                'pattern_matches': [],
                'geological_anomalies': [],
                'ore_body_assessment': {},
                'structural_analysis': {},
                'mineralization_analysis': {},
                'economic_geology_assessment': {},
                'metadata': {
                    'match_timestamp': datetime.utcnow().isoformat(),
                    'matcher_version': '1.0.0',
                    'survey_id': context.get('survey_id', 'unknown')
                }
            }
            
            # Extract classifications and spatial data
            mineral_classifications = interpreted_output.get('mineral_classifications', [])
            spatial_analysis = interpreted_output.get('spatial_analysis', {})
            geological_data = interpreted_output.get('geological_data', {})
            
            # Perform geological pattern matching
            self._match_ore_deposit_types(mineral_classifications, spatial_analysis, context, match_results)
            self._analyze_structural_controls(spatial_analysis, geological_data, context, match_results)
            self._assess_mineralization_patterns(mineral_classifications, context, match_results)
            self._evaluate_ore_body_geometry(spatial_analysis, context, match_results)
            self._detect_geological_anomalies(mineral_classifications, spatial_analysis, context, match_results)
            self._assess_economic_geology(mineral_classifications, spatial_analysis, context, match_results)
            
            # Calculate overall geological fit
            self._calculate_overall_geological_fit(match_results)
            
            return match_results
            
        except Exception as e:
            self.logger.error(f"Error in geological pattern matching: {str(e)}")
            return {
                'overall_geological_fit': 0.0,
                'pattern_matches': [],
                'geological_anomalies': [{'type': 'matcher_error', 'message': str(e)}],
                'metadata': {'error': True, 'error_message': str(e)}
            }
    
    def _match_ore_deposit_types(self, mineral_classifications: List[Dict[str, Any]], 
                               spatial_analysis: Dict[str, Any], 
                               context: Dict[str, Any], 
                               match_results: Dict[str, Any]) -> None:
        """Match detections against known ore deposit types."""
        geological_formation = context.get('geological_formation', 'unknown')
        
        # Analyze detected minerals
        detected_minerals = [c['mineral_classification'].get('mineral_name', 'unknown') 
                           for c in mineral_classifications]
        
        # Check each ore deposit type
        for deposit_type, deposit_info in self.geological_models['ore_deposit_types'].items():
            characteristics = deposit_info['characteristics']
            mineral_associations = characteristics.get('mineral_associations', [])
            
            # Check mineral association match
            mineral_match_count = sum(1 for mineral in detected_minerals 
                                    if mineral in mineral_associations)
            mineral_match_ratio = mineral_match_count / len(mineral_associations) if mineral_associations else 0
            
            # Check geometric characteristics
            geometric_features = spatial_analysis.get('geometric_features', [])
            geometry_match = self._assess_geometry_match(geometric_features, characteristics)
            
            # Check host rock compatibility
            host_rocks = deposit_info.get('host_rock_types', [])
            rock_type = context.get('rock_type', 'unknown')
            host_rock_match = rock_type in host_rocks or any(rock in rock_type for rock in host_rocks)
            
            # Overall deposit type match
            if mineral_match_ratio > 0.5 and (geometry_match or host_rock_match):
                confidence = (mineral_match_ratio + (0.3 if geometry_match else 0) + (0.2 if host_rock_match else 0))
                
                match_results['pattern_matches'].append({
                    'type': f'{deposit_type}_deposit_match',
                    'deposit_type': deposit_type,
                    'mineral_match_ratio': mineral_match_ratio,
                    'geometry_match': geometry_match,
                    'host_rock_match': host_rock_match,
                    'confidence': min(confidence, 1.0),
                    'economic_potential': deposit_info.get('economic_potential', 'unknown'),
                    'message': f'Detection pattern consistent with {deposit_type} deposit'
                })
    
    def _assess_geometry_match(self, geometric_features: List[Dict[str, Any]], 
                             characteristics: Dict[str, Any]) -> bool:
        """Assess if geometric features match deposit characteristics."""
        if not geometric_features:
            return False
        
        expected_geometry = characteristics.get('geometry', 'unknown')
        
        # Simple geometry matching (would be more sophisticated in practice)
        for feature in geometric_features:
            feature_type = feature.get('type', 'unknown')
            
            if expected_geometry == 'linear' and 'cluster' in feature_type:
                return True
            elif expected_geometry == 'layered' and 'layer' in feature_type:
                return True
            elif expected_geometry == 'cylindrical' and 'cluster' in feature_type:
                return True
        
        return False
    
    def _analyze_structural_controls(self, spatial_analysis: Dict[str, Any], 
                                   geological_data: Dict[str, Any], 
                                   context: Dict[str, Any], 
                                   match_results: Dict[str, Any]) -> None:
        """Analyze structural controls on mineralization."""
        structural_features = geological_data.get('geological_context', {}).get('structural_features', [])
        
        structural_analysis = {
            'fault_control': False,
            'fold_control': False,
            'contact_control': False,
            'structural_favorability': 'unknown'
        }
        
        # Check for fault control
        if any('fault' in str(feature).lower() for feature in structural_features):
            structural_analysis['fault_control'] = True
            structural_analysis['structural_favorability'] = 'high'
            
            match_results['pattern_matches'].append({
                'type': 'fault_controlled_mineralization',
                'structural_feature': 'fault_system',
                'confidence': 0.8,
                'message': 'Mineralization appears to be fault-controlled'
            })
        
        # Check for fold control
        if any('fold' in str(feature).lower() for feature in structural_features):
            structural_analysis['fold_control'] = True
            if structural_analysis['structural_favorability'] == 'unknown':
                structural_analysis['structural_favorability'] = 'medium'
        
        # Check for contact control
        if any('contact' in str(feature).lower() or 'intrusive' in str(feature).lower() 
               for feature in structural_features):
            structural_analysis['contact_control'] = True
            structural_analysis['structural_favorability'] = 'high'
            
            match_results['pattern_matches'].append({
                'type': 'contact_controlled_mineralization',
                'structural_feature': 'intrusive_contact',
                'confidence': 0.9,
                'message': 'Mineralization associated with intrusive contact'
            })
        
        match_results['structural_analysis'] = structural_analysis
    
    def _assess_mineralization_patterns(self, mineral_classifications: List[Dict[str, Any]], 
                                      context: Dict[str, Any], 
                                      match_results: Dict[str, Any]) -> None:
        """Assess mineralization patterns and metal associations."""
        detected_minerals = [c['mineral_classification'].get('mineral_name', 'unknown') 
                           for c in mineral_classifications]
        
        mineralization_analysis = {
            'metal_association_type': 'unknown',
            'zoning_pattern': 'unknown',
            'pathfinder_elements': [],
            'mineralization_style': 'unknown'
        }
        
        # Check metal associations
        for association_type, association_data in self.mineralization_patterns['metal_associations'].items():
            primary_associations = association_data['primary_associations']
            
            for association in primary_associations:
                metals_in_association = association.split('-')
                detected_count = sum(1 for metal in metals_in_association if metal.lower() in [m.lower() for m in detected_minerals])
                
                if detected_count >= 2:  # At least 2 metals from association found
                    mineralization_analysis['metal_association_type'] = association_type
                    
                    match_results['pattern_matches'].append({
                        'type': 'metal_association_match',
                        'association_type': association_type,
                        'association': association,
                        'detected_metals': detected_count,
                        'confidence': detected_count / len(metals_in_association),
                        'message': f'{association_type} metal association detected: {association}'
                    })
        
        # Assess mineralization style
        high_grade_count = sum(1 for c in mineral_classifications 
                             if c.get('economic_value') in ['high', 'very_high'])
        total_count = len(mineral_classifications)
        
        if total_count > 0:
            high_grade_ratio = high_grade_count / total_count
            if high_grade_ratio > 0.7:
                mineralization_analysis['mineralization_style'] = 'high_grade_concentrated'
            elif high_grade_ratio > 0.3:
                mineralization_analysis['mineralization_style'] = 'mixed_grade'
            else:
                mineralization_analysis['mineralization_style'] = 'low_grade_disseminated'
        
        match_results['mineralization_analysis'] = mineralization_analysis
    
    def _evaluate_ore_body_geometry(self, spatial_analysis: Dict[str, Any], 
                                  context: Dict[str, Any], 
                                  match_results: Dict[str, Any]) -> None:
        """Evaluate ore body geometry and continuity."""
        volumetric_analysis = spatial_analysis.get('volumetric_analysis', {})
        deposit_characteristics = spatial_analysis.get('deposit_characteristics', {})
        
        ore_body_assessment = {
            'geometry_type': 'unknown',
            'continuity': 'unknown',
            'size_category': 'unknown',
            'grade_distribution': 'unknown'
        }
        
        # Assess size category
        total_volume = volumetric_analysis.get('total_ore_volume_m3', 0)
        if total_volume > 1000:
            ore_body_assessment['size_category'] = 'large'
        elif total_volume > 100:
            ore_body_assessment['size_category'] = 'medium'
        elif total_volume > 10:
            ore_body_assessment['size_category'] = 'small'
        else:
            ore_body_assessment['size_category'] = 'very_small'
        
        # Assess continuity
        deposit_distribution = deposit_characteristics.get('deposit_distribution', 'unknown')
        if deposit_distribution == 'clustered':
            ore_body_assessment['continuity'] = 'good'
        elif deposit_distribution == 'scattered':
            ore_body_assessment['continuity'] = 'poor'
        
        # Assess grade distribution
        ore_concentration = volumetric_analysis.get('ore_concentration', 'unknown')
        ore_body_assessment['grade_distribution'] = ore_concentration
        
        match_results['ore_body_assessment'] = ore_body_assessment
        
        # Add pattern matches for significant ore bodies
        if ore_body_assessment['size_category'] in ['medium', 'large'] and ore_body_assessment['continuity'] == 'good':
            match_results['pattern_matches'].append({
                'type': 'significant_ore_body',
                'size_category': ore_body_assessment['size_category'],
                'continuity': ore_body_assessment['continuity'],
                'volume_m3': total_volume,
                'confidence': 0.8,
                'message': f'Significant ore body identified: {ore_body_assessment["size_category"]} size with {ore_body_assessment["continuity"]} continuity'
            })
    
    def _detect_geological_anomalies(self, mineral_classifications: List[Dict[str, Any]], 
                                   spatial_analysis: Dict[str, Any], 
                                   context: Dict[str, Any], 
                                   match_results: Dict[str, Any]) -> None:
        """Detect geological anomalies and unusual patterns."""
        # Check for unusual mineral assemblages
        detected_minerals = [c['mineral_classification'].get('mineral_name', 'unknown') 
                           for c in mineral_classifications]
        
        # Check for incompatible mineral associations
        precious_metals = ['gold', 'silver', 'platinum']
        base_metals = ['copper', 'lead', 'zinc']
        
        has_precious = any(metal in detected_minerals for metal in precious_metals)
        has_base = any(metal in detected_minerals for metal in base_metals)
        
        if has_precious and has_base:
            # This could be normal or anomalous depending on context
            match_results['geological_anomalies'].append({
                'type': 'mixed_metal_assemblage',
                'detected_precious': [m for m in detected_minerals if m in precious_metals],
                'detected_base': [m for m in detected_minerals if m in base_metals],
                'severity': 'low',
                'message': 'Mixed precious and base metal assemblage detected'
            })
        
        # Check for unusual ore concentrations
        ore_concentration = spatial_analysis.get('volumetric_analysis', {}).get('ore_concentration', 'unknown')
        if ore_concentration == 'high':
            match_results['geological_anomalies'].append({
                'type': 'high_ore_concentration',
                'concentration': ore_concentration,
                'severity': 'medium',
                'message': 'Unusually high ore concentration detected'
            })
        
        # Check for depth-related anomalies
        depth = context.get('depth_m', 0)
        if depth > 200:  # Deep mineralization
            surface_minerals = ['quartz', 'calcite', 'iron']
            deep_minerals = [m for m in detected_minerals if m not in surface_minerals]
            
            if deep_minerals:
                match_results['geological_anomalies'].append({
                    'type': 'deep_mineralization',
                    'depth_m': depth,
                    'deep_minerals': deep_minerals,
                    'severity': 'medium',
                    'message': f'Significant mineralization at depth: {depth}m'
                })
    
    def _assess_economic_geology(self, mineral_classifications: List[Dict[str, Any]], 
                               spatial_analysis: Dict[str, Any], 
                               context: Dict[str, Any], 
                               match_results: Dict[str, Any]) -> None:
        """Assess economic geology and resource potential."""
        # Calculate economic metrics
        high_value_minerals = [c for c in mineral_classifications 
                             if c.get('economic_value') in ['high', 'very_high']]
        total_volume = spatial_analysis.get('volumetric_analysis', {}).get('total_ore_volume_m3', 0)
        
        # Assess resource category
        resource_category = 'inferred'
        if len(mineral_classifications) > 5 and total_volume > 100:
            resource_category = 'indicated'
        if len(mineral_classifications) > 10 and total_volume > 1000:
            resource_category = 'measured'
        
        # Assess mining feasibility
        depth = context.get('depth_m', 0)
        infrastructure_distance = context.get('infrastructure_distance_km', 0)
        
        mining_feasibility = 'low'
        if depth < 50 and infrastructure_distance < 10:
            mining_feasibility = 'high'
        elif depth < 100 and infrastructure_distance < 25:
            mining_feasibility = 'medium'
        
        economic_assessment = {
            'resource_category': resource_category,
            'high_value_mineral_count': len(high_value_minerals),
            'total_resource_volume_m3': total_volume,
            'mining_feasibility': mining_feasibility,
            'development_potential': 'low',
            'exploration_priority': 'medium'
        }
        
        # Determine development potential
        if (len(high_value_minerals) > 2 and 
            total_volume > 500 and 
            mining_feasibility in ['medium', 'high']):
            economic_assessment['development_potential'] = 'high'
            economic_assessment['exploration_priority'] = 'high'
        elif len(high_value_minerals) > 0 and total_volume > 50:
            economic_assessment['development_potential'] = 'medium'
        
        match_results['economic_geology_assessment'] = economic_assessment
        
        # Add pattern match for high development potential
        if economic_assessment['development_potential'] == 'high':
            match_results['pattern_matches'].append({
                'type': 'high_development_potential',
                'resource_category': resource_category,
                'mining_feasibility': mining_feasibility,
                'high_value_minerals': len(high_value_minerals),
                'confidence': 0.9,
                'message': 'High development potential identified'
            })
    
    def _calculate_overall_geological_fit(self, match_results: Dict[str, Any]) -> None:
        """Calculate overall geological fit score."""
        fit_score = 0.0
        
        # Weight pattern matches
        for pattern_match in match_results['pattern_matches']:
            confidence = pattern_match.get('confidence', 0.5)
            match_type = pattern_match.get('type', '')
            
            # Weight different types of matches
            if 'deposit_match' in match_type:
                fit_score += confidence * 0.3
            elif 'controlled_mineralization' in match_type:
                fit_score += confidence * 0.2
            elif 'association_match' in match_type:
                fit_score += confidence * 0.15
            else:
                fit_score += confidence * 0.1
        
        # Weight economic assessment
        economic_assessment = match_results.get('economic_geology_assessment', {})
        development_potential = economic_assessment.get('development_potential', 'low')
        if development_potential == 'high':
            fit_score += 0.2
        elif development_potential == 'medium':
            fit_score += 0.1
        
        # Penalize for severe anomalies
        severe_anomalies = [a for a in match_results['geological_anomalies'] 
                          if a.get('severity') == 'high']
        fit_score = max(0.0, fit_score - len(severe_anomalies) * 0.1)
        
        # Normalize to 0-1 range
        match_results['overall_geological_fit'] = min(1.0, fit_score)
    
    def add_geological_model(self, model_id: str, model_data: Dict[str, Any]) -> None:
        """Add a new geological model."""
        self.geological_models[model_id] = model_data
        self.logger.info(f"Added geological model: {model_id}")
    
    def update_mineralization_pattern(self, pattern_id: str, pattern_data: Dict[str, Any]) -> None:
        """Update mineralization pattern."""
        self.mineralization_patterns[pattern_id] = pattern_data
        self.logger.info(f"Updated mineralization pattern: {pattern_id}")
    
    def get_geological_models(self) -> Dict[str, Any]:
        """Get current geological models."""
        return self.geological_models.copy()

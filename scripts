@echo off
setlocal enabledelayedexpansion

REM AI Orchestration Hub Quick Start Script for Windows
REM This script sets up the AI orchestration environment with cross-flow integration
REM Author: AI Orchestration Team
REM Version: 1.0.0
REM Last Updated: 2023-11-15

REM Configuration variables
set "SCRIPT_DIR=%~dp0"
set "AI_ORCHESTRATION_DIR=%SCRIPT_DIR%ai-orchestration"
set "PRODUCTION_MODE=%PRODUCTION_MODE%"
if not defined PRODUCTION_MODE set "PRODUCTION_MODE=false"
set "NPM_TIMEOUT=300"

REM Color codes for Windows (using echo with special characters)
REM Note: Windows batch doesn't support ANSI colors by default, but we can use symbols

REM Function to display colored messages (using standard error levels for compatibility)
goto :main

:log_info
echo [INFO] %~1
goto :eof

:log_success
echo [SUCCESS] %~1
goto :eof

:log_warning
echo [WARNING] %~1
goto :eof

:log_error
echo [ERROR] %~1
goto :eof

:check_prerequisites
call :log_info "Checking prerequisites..."

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    call :log_error "Node.js is not installed or not in PATH"
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

REM Check Node.js version
for /f "delims=" %%v in ('node --version') do set "NODE_VERSION=%%v"
call :log_info "Node.js version detected: !NODE_VERSION!"
REM Extract version number for comparison (remove 'v' prefix)
set "NODE_VERSION_NUM=!NODE_VERSION:~1!"
REM Simple version check - compare first two digits
for /f "tokens=1,2 delims=." %%a in ("!NODE_VERSION_NUM!") do (
    set "MAJOR=%%a"
    set "MINOR=%%b"
)
if !MAJOR! lss 14 (
    call :log_error "Node.js version !NODE_VERSION! is too old. Please install Node.js v14.0.0 or higher"
    pause
    exit /b 1
)

REM Check if npm is installed
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    call :log_error "npm is not installed or not in PATH"
    echo Please install npm (usually comes with Node.js)
    pause
    exit /b 1
)

call :log_success "All prerequisites are installed"
goto :eof

:verify_directory_structure
call :log_info "Verifying directory structure..."

if not exist "%AI_ORCHESTRATION_DIR%" (
    call :log_error "AI orchestration directory not found: %AI_ORCHESTRATION_DIR%"
    echo Please ensure you're running this script from the correct location.
    echo Expected structure: .\ai-orchestration\
    pause
    exit /b 1
)

if not exist "%AI_ORCHESTRATION_DIR%\package.json" (
    call :log_error "package.json not found in %AI_ORCHESTRATION_DIR%"
    pause
    exit /b 1
)

call :log_success "Directory structure verified"
goto :eof

:validate_npm_environment
call :log_info "Validating npm environment..."

REM Check npm configuration
npm config get registry >nul 2>&1
if %errorlevel% neq 0 (
    call :log_warning "npm registry configuration issue detected"
)

REM Verify npm cache with timeout
call :log_info "Verifying npm cache..."
start /b /wait cmd /c "npm cache verify"
if %errorlevel% neq 0 (
    call :log_warning "npm cache verification failed - cleaning cache"
    start /b /wait cmd /c "npm cache clean --force"
)

call :log_success "npm environment validated"
goto :eof

:install_dependencies_with_timeout
call :log_info "Installing Node.js dependencies with timeout protection..."

REM Change to ai-orchestration directory with validation
if not exist "%AI_ORCHESTRATION_DIR%" (
    call :log_error "Cannot change to directory: %AI_ORCHESTRATION_DIR%"
    pause
    exit /b 1
)

pushd "%AI_ORCHESTRATION_DIR%" || (
    call :log_error "Failed to change to directory: %AI_ORCHESTRATION_DIR%"
    pause
    exit /b 1
)

REM Install dependencies based on production mode
if /i "%PRODUCTION_MODE%"=="true" (
    call :log_info "Installing production dependencies only..."
    timeout /t 2 /nobreak >nul
    call npm ci --only=production --timeout=%NPM_TIMEOUT%000
) else (
    call :log_info "Installing all dependencies..."
    timeout /t 2 /nobreak >nul
    call npm install --timeout=%NPM_TIMEOUT%000
)

if %errorlevel% neq 0 (
    call :log_error "Failed to install dependencies"
    echo This might be due to network issues or package conflicts.
    echo Try running: npm cache clean --force
    popd
    pause
    exit /b 1
)

call :log_success "Dependencies installed successfully"
popd
goto :eof

:cleanup
call :log_info "Performing cleanup operations..."
REM Cleanup operations here
popd >nul 2>&1
cd /d "%SCRIPT_DIR%"
goto :eof

:main
echo.
echo ========================================
echo   🤖 AI Orchestration Hub Quick Start
echo   Cross-Flow Integration Setup
echo ========================================
echo.

REM Run prerequisite checks
call :check_prerequisites
call :verify_directory_structure
call :validate_npm_environment

echo.
call :log_info "Step 1: Installing Node.js dependencies..."
call :install_dependencies_with_timeout

echo.
call :log_info "Step 2: Running enhanced setup script..."

pushd "%AI_ORCHESTRATION_DIR%" || (
    call :log_error "Failed to change to directory: %AI_ORCHESTRATION_DIR%"
    pause
    exit /b 1
)

if not exist "setup.js" (
    call :log_error "setup.js not found in %AI_ORCHESTRATION_DIR%"
    popd
    pause
    exit /b 1
)

call node setup.js
if %errorlevel% neq 0 (
    call :log_error "Setup script failed"
    echo Check the setup.js file for any configuration issues.
    popd
    pause
    exit /b 1
)

call :log_success "Setup completed successfully"

echo.
call :log_info "Step 3: Initializing AI Orchestration with Cross-Flow..."

if not exist "orchestrator.js" (
    call :log_error "orchestrator.js not found in %AI_ORCHESTRATION_DIR%"
    popd
    pause
    exit /b 1
)

call node orchestrator.js init
if %errorlevel% neq 0 (
    call :log_error "AI Orchestration initialization failed"
    echo Check the orchestrator.js configuration and dependencies.
    popd
    pause
    exit /b 1
)

call :log_success "AI Orchestration initialized successfully"

echo.
call :log_info "Step 4: Testing Cross-Flow Workflow..."

if exist "scripts\workflow-automation.js" (
    call node scripts\workflow-automation.js list
    if %errorlevel% neq 0 (
        call :log_warning "Cross-flow test failed - this may indicate configuration issues"
        call :log_info "This is not critical for basic functionality, continuing..."
    ) else (
        call :log_success "Cross-flow workflow test completed successfully"
    )
) else (
    call :log_warning "workflow-automation.js not found - skipping test"
    call :log_info "This test is optional for basic functionality"
)

popd

REM Function to check external tools
:check_external_tools
call :log_info "Checking for external AI tools..."

REM Check for VS Code with version verification
code --version >nul 2>&1
if %errorlevel% equ 0 (
    for /f "tokens=1 delims=" %%v in ('code --version') do (
        set "VSCODE_VERSION=%%v"
        goto :vscode_version_check
    )
    :vscode_version_check
    call :log_info "VS Code version detected: !VSCODE_VERSION!"
    REM Extract major.minor version for comparison
    for /f "tokens=1,2 delims=." %%a in ("!VSCODE_VERSION!") do (
        set "VS_MAJOR=%%a"
        set "VS_MINOR=%%b"
    )
    if !VS_MAJOR! lss 1 (
        echo   [WARNING] VS Code: Version !VSCODE_VERSION! is below recommended minimum
    ) else if !VS_MAJOR! equ 1 if !VS_MINOR! lss 70 (
        echo   [WARNING] VS Code: Version !VSCODE_VERSION! is below recommended minimum
    ) else (
        echo   [SUCCESS] VS Code: Version !VSCODE_VERSION! installed
    )
) else (
    echo   [ERROR] VS Code: Not found in PATH
)

REM Check for workspace file
if exist "%SCRIPT_DIR%AI-Orchestration-Workspace.code-workspace" (
    echo   [SUCCESS] Workspace file: Available
) else (
    echo   [WARNING] Workspace file: Not found
)

goto :eof

REM Function to display next steps
:display_next_steps
echo.
call :log_success "🎉 AI Orchestration Hub with Cross-Flow is ready!"
echo.

echo ========================================
echo 📋 MANUAL INSTALLATION REQUIRED
echo ========================================
echo.
echo The following tools need to be installed manually:
echo.
echo 1. 📥 Cursor (AI Code Generator)
echo    URL: https://cursor.sh/
echo    Purpose: Intelligent code generation and editing
echo    Status: Manual installation required
echo.
echo 2. 📥 Windsurf (Multi-File Editor)
echo    URL: https://codeium.com/windsurf
echo    Purpose: Advanced multi-file editing capabilities
echo    Status: Manual installation required
echo.
echo 3. 📥 Augment Code (Primary Analyzer)
echo    URL: https://augmentcode.com/
echo    Purpose: Primary code analysis and orchestration
echo    Status: Manual installation required
echo.
echo 4. ✅ Tabnine (Code Completion)
echo    Installation: Auto-installed via VS Code extensions
echo    Purpose: AI-powered code completion
echo    Status: Should be automatically available
echo.

echo ========================================
echo 🚀 QUICK START COMMANDS
echo ========================================
echo.
echo • Open Workspace:
echo   code AI-Orchestration-Workspace.code-workspace
echo.
echo • Cross-Flow Feature:
echo   Ctrl+Shift+P → "🚀 Cross-Flow Feature Implementation"
echo.
echo • Interactive Mode:
echo   Ctrl+Shift+P → "📋 Interactive Workflow Selection"
echo.

echo ========================================
echo 🌐 AVAILABLE SERVICES
echo ========================================
echo.
echo When the system is running, these services will be available:
echo.
echo • HTTP API:     http://localhost:3000
echo • WebSocket:    ws://localhost:3001
echo • Results:      ai-orchestration/results/
echo • Logs:         ai-orchestration/logs/
echo • Config:       ai-orchestration/config/
echo.

echo ========================================
echo 🔄 CROSS-FLOW PATTERN
echo ========================================
echo.
echo The AI orchestration follows this workflow pattern:
echo.
echo User Request → Augment Code Analysis → Plan Generation
echo      ↓
echo Cursor/Windsurf Implementation → Tabnine Enhancement
echo      ↓
echo Quality Validation → Final Output
echo.

echo ========================================
echo 📚 ADDITIONAL INFORMATION
echo ========================================
echo.
echo • Configuration files: %AI_ORCHESTRATION_DIR%\config\
echo • Log files:          %AI_ORCHESTRATION_DIR%\logs\
echo • Documentation:      %AI_ORCHESTRATION_DIR%\README.md
echo • Troubleshooting:    Check logs for detailed error information
echo.

call :log_success "Setup completed successfully!"
echo.
echo ========================================
echo 🔄 OPTIONS
echo ========================================
echo.
echo 1. Exit setup
echo 2. Open VS Code workspace
echo 3. View configuration files
echo 4. Show logs directory
echo R. Restart this setup script
echo.
choice /c 1234R /n /m "Select an option (1-4, R): "
if errorlevel 5 (
    call :log_info "Restarting setup script..."
    call "%~f0"
    exit /b 0
)
if errorlevel 4 (
    call :log_info "Opening logs directory..."
    if exist "%AI_ORCHESTRATION_DIR%\logs" (
        explorer "%AI_ORCHESTRATION_DIR%\logs"
    ) else (
        echo Logs directory not found: %AI_ORCHESTRATION_DIR%\logs
    )
    pause
    goto :eof
)
if errorlevel 3 (
    call :log_info "Opening configuration directory..."
    if exist "%AI_ORCHESTRATION_DIR%\config" (
        explorer "%AI_ORCHESTRATION_DIR%\config"
    ) else (
        echo Config directory not found: %AI_ORCHESTRATION_DIR%\config
    )
    pause
    goto :eof
)
if errorlevel 2 (
    call :log_info "Opening VS Code workspace..."
    if exist "%SCRIPT_DIR%AI-Orchestration-Workspace.code-workspace" (
        code "%SCRIPT_DIR%AI-Orchestration-Workspace.code-workspace"
    ) else (
        echo Workspace file not found: %SCRIPT_DIR%AI-Orchestration-Workspace.code-workspace
    )
    pause
    goto :eof
)
REM Default case (1 or any other)
echo.
echo Press any key to exit...
pause >nul
goto :eof

REM Main execution continues here
call :check_external_tools
call :display_next_steps

REM Cleanup and return to original directory
call :cleanup

REM Exit successfully
exit /b 0

{"name": "vscode-universal-security-scanner", "version": "1.0.0", "description": "Universal Security Scanner for all VS Code projects - Enterprise-grade security scanning for solo developers", "main": "scanner.js", "type": "module", "scripts": {"scan": "node scanner.js", "scan:quick": "node scanner.js --quick", "scan:full": "node scanner.js --full", "scan:report": "node scanner.js --report", "scan:sbom": "node scanner.js --sbom", "scan:licenses": "node scanner.js --licenses", "scan:vulnerabilities": "node scanner.js --vulnerabilities", "setup": "node setup.js", "dashboard": "node -e \"console.log('Open: file://' + process.cwd() + '/dashboard.html')\"", "help": "node scanner.js --help"}, "keywords": ["security", "vulnerability", "scanning", "sca", "sbom", "license", "compliance", "solo-developer", "vscode"], "author": "<PERSON> <PERSON> Solo Developer Security Solutions", "license": "MIT", "dependencies": {"commander": "^11.1.0", "chalk": "^5.3.0", "ora": "^7.0.1", "inquirer": "^9.2.12", "glob": "^10.3.10", "fs-extra": "^11.2.0"}, "devDependencies": {"snyk": "^1.1291.0", "license-checker": "^25.0.1", "@cyclonedx/cyclonedx-npm": "^1.19.3", "eslint": "^8.57.0", "eslint-plugin-security": "^1.7.1", "@eslint/js": "^8.57.0", "retire": "^4.0.3", "audit-ci": "^6.6.1", "dependency-check": "^4.1.0", "npm-check-updates": "^16.14.12", "madge": "^6.1.0"}, "bin": {"vscode-security-scan": "./scanner.js"}, "engines": {"node": ">=16.0.0"}}
#!/usr/bin/env node

/**
 * Workspace Setup Script
 * 
 * Sets up the entire Time Stamp Project workspace for testing and validation.
 * Installs dependencies, configures environments, and prepares all projects.
 */

import fs from 'fs-extra';
import path from 'path';
import { execSync } from 'child_process';
import chalk from 'chalk';
import ora from 'ora';

class WorkspaceSetup {
  constructor() {
    this.workspaceRoot = process.cwd();
    this.setupResults = {
      projects: [],
      summary: {
        total: 0,
        success: 0,
        failed: 0,
        skipped: 0
      }
    };
  }

  async setup() {
    console.log(chalk.blue.bold('🔧 Time Stamp Project Workspace Setup'));
    console.log(chalk.gray('Setting up all projects for testing and validation'));
    console.log(chalk.gray('=' .repeat(60)));
    console.log('');

    try {
      await this.checkPrerequisites();
      await this.setupProjects();
      await this.createTestConfigs();
      await this.generateRunScripts();
      this.displaySummary();
      this.showQuickStart();
    } catch (error) {
      console.error(chalk.red(`❌ Setup failed: ${error.message}`));
      process.exit(1);
    }
  }

  async checkPrerequisites() {
    const spinner = ora('🔍 Checking prerequisites...').start();
    
    try {
      // Check Node.js
      const nodeVersion = execSync('node --version', { encoding: 'utf8' }).trim();
      const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
      
      if (majorVersion < 16) {
        throw new Error(`Node.js ${nodeVersion} is too old. Please install Node.js 16 or higher.`);
      }
      
      // Check npm
      execSync('npm --version', { encoding: 'utf8' });
      
      // Check Python (optional)
      try {
        const pythonVersion = execSync('python --version', { encoding: 'utf8' }).trim();
        spinner.text = `✅ Node.js ${nodeVersion}, ${pythonVersion}`;
      } catch {
        try {
          const python3Version = execSync('python3 --version', { encoding: 'utf8' }).trim();
          spinner.text = `✅ Node.js ${nodeVersion}, ${python3Version}`;
        } catch {
          spinner.text = `✅ Node.js ${nodeVersion}, Python not found (optional)`;
        }
      }
      
      spinner.succeed(chalk.green('✅ Prerequisites checked'));
    } catch (error) {
      spinner.fail(chalk.red('❌ Prerequisites check failed'));
      throw error;
    }
  }

  async setupProjects() {
    const projects = [
      {
        name: 'EcoStamp Backend',
        path: 'core-systems/EcoStamp/source',
        type: 'nodejs',
        priority: 1
      },
      {
        name: 'Development Tools',
        path: 'development-tools',
        type: 'nodejs',
        priority: 2
      },
      {
        name: 'AI Orchestration',
        path: 'ai-orchestration',
        type: 'nodejs',
        priority: 3
      },
      {
        name: 'Core Systems',
        path: 'core-systems',
        type: 'python',
        priority: 4
      }
    ];

    // Sort by priority
    projects.sort((a, b) => a.priority - b.priority);

    for (const project of projects) {
      await this.setupProject(project);
    }
  }

  async setupProject(project) {
    const fullPath = path.join(this.workspaceRoot, project.path);
    
    if (!await fs.pathExists(fullPath)) {
      console.log(chalk.yellow(`⏭️  ${project.name}: Directory not found - skipping`));
      this.setupResults.summary.skipped++;
      return;
    }

    console.log(chalk.cyan(`\n📦 Setting up ${project.name}...`));
    
    try {
      if (project.type === 'nodejs') {
        await this.setupNodeProject(fullPath, project.name);
      } else if (project.type === 'python') {
        await this.setupPythonProject(fullPath, project.name);
      }
      
      this.setupResults.projects.push({
        ...project,
        status: 'success'
      });
      this.setupResults.summary.success++;
      
    } catch (error) {
      console.log(chalk.red(`   ❌ Failed: ${error.message}`));
      this.setupResults.projects.push({
        ...project,
        status: 'failed',
        error: error.message
      });
      this.setupResults.summary.failed++;
    }
  }

  async setupNodeProject(projectPath, projectName) {
    const packageJsonPath = path.join(projectPath, 'package.json');
    
    if (!await fs.pathExists(packageJsonPath)) {
      throw new Error('package.json not found');
    }

    // Install dependencies
    const spinner = ora(`   📦 Installing dependencies...`).start();
    try {
      execSync('npm install', { 
        cwd: projectPath, 
        stdio: 'pipe',
        timeout: 180000 // 3 minutes
      });
      spinner.succeed(chalk.green(`   ✅ Dependencies installed`));
    } catch (error) {
      spinner.fail(chalk.red(`   ❌ npm install failed`));
      throw new Error(`npm install failed: ${error.stderr || error.message}`);
    }

    // Create .env file if needed
    await this.createEnvFile(projectPath, projectName);
    
    // Verify installation
    const packageJson = await fs.readJson(packageJsonPath);
    if (packageJson.scripts) {
      console.log(chalk.gray(`   📋 Available scripts: ${Object.keys(packageJson.scripts).join(', ')}`));
    }
  }

  async setupPythonProject(projectPath, projectName) {
    const requirementsPath = path.join(projectPath, 'requirements.txt');
    const setupPyPath = path.join(projectPath, 'setup.py');
    
    if (await fs.pathExists(requirementsPath)) {
      const spinner = ora(`   📦 Installing Python dependencies...`).start();
      try {
        // Try pip install
        execSync('pip install -r requirements.txt', { 
          cwd: projectPath, 
          stdio: 'pipe',
          timeout: 180000
        });
        spinner.succeed(chalk.green(`   ✅ Python dependencies installed`));
      } catch (error) {
        spinner.fail(chalk.red(`   ❌ pip install failed`));
        throw new Error(`pip install failed: ${error.stderr || error.message}`);
      }
    } else if (await fs.pathExists(setupPyPath)) {
      const spinner = ora(`   📦 Installing Python package...`).start();
      try {
        execSync('pip install -e .', { 
          cwd: projectPath, 
          stdio: 'pipe',
          timeout: 180000
        });
        spinner.succeed(chalk.green(`   ✅ Python package installed`));
      } catch (error) {
        spinner.fail(chalk.red(`   ❌ pip install failed`));
        throw new Error(`pip install failed: ${error.stderr || error.message}`);
      }
    } else {
      console.log(chalk.yellow(`   ⏭️  No Python dependencies found - skipping`));
    }
  }

  async createEnvFile(projectPath, projectName) {
    const envPath = path.join(projectPath, '.env');
    
    if (await fs.pathExists(envPath)) {
      console.log(chalk.gray(`   📄 .env file already exists`));
      return;
    }

    let envContent = '';
    
    if (projectName.includes('EcoStamp')) {
      envContent = `# EcoStamp Backend Configuration
NODE_ENV=development
PORT=3000
ENABLE_SCHEDULER=false
CORS_ORIGIN=*
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# API Keys (add your own)
# OPENAI_API_KEY=your_openai_key_here
# ANTHROPIC_API_KEY=your_anthropic_key_here
# GOOGLE_API_KEY=your_google_key_here
`;
    } else if (projectName.includes('Development Tools')) {
      envContent = `# Development Tools Configuration
NODE_ENV=development
SCAN_TIMEOUT=300000
REPORT_FORMAT=json
OUTPUT_DIR=./reports
`;
    } else if (projectName.includes('AI Orchestration')) {
      envContent = `# AI Orchestration Configuration
NODE_ENV=development
ORCHESTRATION_MODE=development
LOG_LEVEL=info
WORKSPACE_PATH=../
`;
    }

    if (envContent) {
      await fs.writeFile(envPath, envContent);
      console.log(chalk.gray(`   📄 Created .env file`));
    }
  }

  async createTestConfigs() {
    const spinner = ora('🧪 Creating test configurations...').start();
    
    try {
      // Create test runner config
      const testConfig = {
        projects: this.setupResults.projects.filter(p => p.status === 'success'),
        defaultTimeout: 60000,
        retries: 1,
        parallel: false
      };
      
      await fs.writeFile(
        path.join(this.workspaceRoot, 'test-config.json'),
        JSON.stringify(testConfig, null, 2)
      );
      
      spinner.succeed(chalk.green('✅ Test configurations created'));
    } catch (error) {
      spinner.fail(chalk.red('❌ Failed to create test configs'));
      throw error;
    }
  }

  async generateRunScripts() {
    const spinner = ora('📜 Generating run scripts...').start();
    
    try {
      // Create Windows batch file
      const batchContent = `@echo off
echo 🚀 Time Stamp Project Quick Runner
echo.

if "%1"=="list" (
    node project-runner.js --list
    goto :end
)

if "%1"=="test" (
    node project-runner.js
    goto :end
)

if "%1"=="run" (
    node project-runner.js --run
    goto :end
)

if "%1"=="ecostamp" (
    node project-runner.js --project ecostamp --run
    goto :end
)

if "%1"=="scanner" (
    node project-runner.js --project development-tools --run
    goto :end
)

if "%1"=="help" (
    echo Available commands:
    echo   run.bat list      - List all projects
    echo   run.bat test      - Test all projects
    echo   run.bat run       - Run all projects
    echo   run.bat ecostamp  - Run EcoStamp backend
    echo   run.bat scanner   - Run security scanner
    echo   run.bat help      - Show this help
    goto :end
)

echo Usage: run.bat [command]
echo Run "run.bat help" for available commands

:end
pause
`;
      
      await fs.writeFile(path.join(this.workspaceRoot, 'run.bat'), batchContent);
      
      // Create shell script for Unix systems
      const shellContent = `#!/bin/bash

echo "🚀 Time Stamp Project Quick Runner"
echo ""

case "$1" in
    "list")
        node project-runner.js --list
        ;;
    "test")
        node project-runner.js
        ;;
    "run")
        node project-runner.js --run
        ;;
    "ecostamp")
        node project-runner.js --project ecostamp --run
        ;;
    "scanner")
        node project-runner.js --project development-tools --run
        ;;
    "help")
        echo "Available commands:"
        echo "  ./run.sh list      - List all projects"
        echo "  ./run.sh test      - Test all projects"
        echo "  ./run.sh run       - Run all projects"
        echo "  ./run.sh ecostamp  - Run EcoStamp backend"
        echo "  ./run.sh scanner   - Run security scanner"
        echo "  ./run.sh help      - Show this help"
        ;;
    *)
        echo "Usage: ./run.sh [command]"
        echo "Run './run.sh help' for available commands"
        ;;
esac
`;
      
      await fs.writeFile(path.join(this.workspaceRoot, 'run.sh'), shellContent);
      
      // Make shell script executable (Unix only)
      try {
        execSync('chmod +x run.sh', { cwd: this.workspaceRoot, stdio: 'pipe' });
      } catch {
        // Ignore on Windows
      }
      
      spinner.succeed(chalk.green('✅ Run scripts generated'));
    } catch (error) {
      spinner.fail(chalk.red('❌ Failed to generate run scripts'));
      throw error;
    }
  }

  displaySummary() {
    this.setupResults.summary.total = this.setupResults.projects.length;
    
    console.log('\n' + chalk.blue.bold('📊 Setup Summary:'));
    console.log(chalk.gray('=' .repeat(40)));
    console.log(chalk.green(`✅ Success: ${this.setupResults.summary.success}`));
    console.log(chalk.red(`❌ Failed: ${this.setupResults.summary.failed}`));
    console.log(chalk.yellow(`⏭️  Skipped: ${this.setupResults.summary.skipped}`));
    console.log(chalk.cyan(`📦 Total: ${this.setupResults.summary.total}`));
    
    if (this.setupResults.summary.failed > 0) {
      console.log('\n' + chalk.red.bold('❌ Failed Projects:'));
      this.setupResults.projects
        .filter(p => p.status === 'failed')
        .forEach(p => {
          console.log(chalk.red(`   ${p.name}: ${p.error}`));
        });
    }
  }

  showQuickStart() {
    console.log('\n' + chalk.blue.bold('🚀 Quick Start Guide:'));
    console.log(chalk.gray('=' .repeat(40)));
    console.log('');
    
    console.log(chalk.yellow.bold('📋 List all projects:'));
    console.log(chalk.gray('   npm run list'));
    console.log(chalk.gray('   node project-runner.js --list'));
    console.log('');
    
    console.log(chalk.yellow.bold('🧪 Test all projects:'));
    console.log(chalk.gray('   npm test'));
    console.log(chalk.gray('   node project-runner.js'));
    console.log('');
    
    console.log(chalk.yellow.bold('🚀 Run specific projects:'));
    console.log(chalk.gray('   npm run ecostamp     # Run EcoStamp backend'));
    console.log(chalk.gray('   npm run scanner      # Run security scanner'));
    console.log(chalk.gray('   npm run orchestrator # Run AI orchestration'));
    console.log('');
    
    console.log(chalk.yellow.bold('🔄 Run all projects:'));
    console.log(chalk.gray('   npm run run'));
    console.log(chalk.gray('   npm run run:background  # Run in background'));
    console.log('');
    
    console.log(chalk.yellow.bold('🪟 Windows shortcuts:'));
    console.log(chalk.gray('   run.bat list'));
    console.log(chalk.gray('   run.bat test'));
    console.log(chalk.gray('   run.bat ecostamp'));
    console.log('');
    
    console.log(chalk.green.bold('✅ Workspace is ready for testing and validation!'));
  }
}

async function main() {
  const setup = new WorkspaceSetup();
  await setup.setup();
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export default WorkspaceSetup;

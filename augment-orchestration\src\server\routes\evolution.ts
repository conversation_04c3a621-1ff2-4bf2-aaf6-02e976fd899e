import { Router } from 'express';
import { PrismaClient } from '@prisma/client';
import { asyncHandler } from '../middleware/errorHandler';
import { AuthenticatedRequest } from '../middleware/auth';
import { DarwinGodelMachine } from '../services/DarwinGodelMachine';

const router = Router();
const prisma = new PrismaClient();
const darwinMachine = new DarwinGodelMachine();

// Get evolution variants
router.get('/variants', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const variants = await prisma.evolutionVariant.findMany({
    include: {
      parentAgent: {
        select: {
          id: true,
          agentId: true,
          name: true,
          vendor: true,
        },
      },
    },
    orderBy: {
      generation: 'desc',
    },
  });

  res.json({
    success: true,
    data: variants,
  });
}));

// Start evolution process
router.post('/start', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { parameters } = req.body;

  const sessionId = await darwinMachine.startEvolution(parameters);

  res.status(201).json({
    success: true,
    data: {
      sessionId,
      message: 'Evolution process started',
    },
  });
}));

// Stop evolution process
router.post('/stop', asyncHandler(async (req: AuthenticatedRequest, res) => {
  await darwinMachine.stopEvolution();

  res.json({
    success: true,
    message: 'Evolution process stopped',
  });
}));

// Get evolution status
router.get('/status', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const status = await darwinMachine.getEvolutionStatus();

  res.json({
    success: true,
    data: status,
  });
}));

// Get evolution history
router.get('/history', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const limit = parseInt(req.query.limit as string) || 50;
  const history = await darwinMachine.getEvolutionHistory(limit);

  res.json({
    success: true,
    data: history,
  });
}));

export { router as evolutionRoutes };

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SocketService = void 0;
const EventBus_1 = require("./EventBus");
const logger_1 = require("../utils/logger");
class SocketService {
    constructor(io, eventBus) {
        this.connectedUsers = new Map();
        this.io = io;
        this.eventBus = eventBus;
    }
    async initialize() {
        this.setupSocketHandlers();
        this.setupEventBusSubscriptions();
        logger_1.logger.info('SocketService initialized');
    }
    setupSocketHandlers() {
        this.io.on('connection', (socket) => {
            logger_1.logger.info(`Client connected: ${socket.id}`);
            // Add user to connected users
            this.connectedUsers.set(socket.id, {
                id: socket.id,
                joinedAt: new Date(),
                lastActivity: new Date(),
            });
            // Update connection count
            this.broadcastConnectionCount();
            // Handle user authentication
            socket.on('authenticate', (data) => {
                const user = this.connectedUsers.get(socket.id);
                if (user) {
                    user.userId = data.userId;
                    user.username = data.username;
                    this.connectedUsers.set(socket.id, user);
                    logger_1.logger.info(`User authenticated: ${data.username} (${socket.id})`);
                    // Broadcast user presence
                    this.eventBus.publish(EventBus_1.EVENT_TYPES.USER_PRESENCE, { type: 'joined', userId: data.userId, username: data.username }, 'socket-service');
                }
            });
            // Handle room joining
            socket.on('join-room', (room) => {
                socket.join(room);
                logger_1.logger.debug(`Client ${socket.id} joined room: ${room}`);
            });
            // Handle room leaving
            socket.on('leave-room', (room) => {
                socket.leave(room);
                logger_1.logger.debug(`Client ${socket.id} left room: ${room}`);
            });
            // Handle orchestrator selection
            socket.on('select-orchestrator', (orchestratorId) => {
                socket.join(`orchestrator:${orchestratorId}`);
                logger_1.logger.debug(`Client ${socket.id} selected orchestrator: ${orchestratorId}`);
            });
            // Handle workflow subscription
            socket.on('subscribe-workflow', (workflowId) => {
                socket.join(`workflow:${workflowId}`);
                logger_1.logger.debug(`Client ${socket.id} subscribed to workflow: ${workflowId}`);
            });
            // Handle tunnel subscription
            socket.on('subscribe-tunnel', (tunnelId) => {
                socket.join(`tunnel:${tunnelId}`);
                logger_1.logger.debug(`Client ${socket.id} subscribed to tunnel: ${tunnelId}`);
            });
            // Handle activity tracking
            socket.on('activity', () => {
                const user = this.connectedUsers.get(socket.id);
                if (user) {
                    user.lastActivity = new Date();
                    this.connectedUsers.set(socket.id, user);
                }
            });
            // Handle disconnection
            socket.on('disconnect', (reason) => {
                logger_1.logger.info(`Client disconnected: ${socket.id}, reason: ${reason}`);
                const user = this.connectedUsers.get(socket.id);
                if (user?.userId) {
                    // Broadcast user presence
                    this.eventBus.publish(EventBus_1.EVENT_TYPES.USER_PRESENCE, { type: 'left', userId: user.userId, username: user.username }, 'socket-service');
                }
                this.connectedUsers.delete(socket.id);
                this.broadcastConnectionCount();
            });
            // Handle ping/pong for connection health
            socket.on('ping', () => {
                socket.emit('pong');
            });
        });
    }
    setupEventBusSubscriptions() {
        // Subscribe to all events and broadcast to relevant clients
        this.eventBus.subscribeAll((message) => {
            this.handleEventBusMessage(message);
        });
        logger_1.logger.info('EventBus subscriptions setup completed');
    }
    handleEventBusMessage(message) {
        const { type, payload } = message;
        switch (type) {
            case EventBus_1.EVENT_TYPES.ORCHESTRATOR_CREATED:
            case EventBus_1.EVENT_TYPES.ORCHESTRATOR_UPDATED:
            case EventBus_1.EVENT_TYPES.ORCHESTRATOR_DELETED:
                this.io.emit('orchestrator-update', { type, payload });
                break;
            case EventBus_1.EVENT_TYPES.AGENT_ASSIGNED:
            case EventBus_1.EVENT_TYPES.AGENT_STATUS_CHANGED:
            case EventBus_1.EVENT_TYPES.AGENT_EVOLVED:
                this.io.emit('agent-update', { type, payload });
                if (payload.orchestratorId) {
                    this.io.to(`orchestrator:${payload.orchestratorId}`).emit('agent-update', { type, payload });
                }
                break;
            case EventBus_1.EVENT_TYPES.TUNNEL_CREATED:
            case EventBus_1.EVENT_TYPES.TUNNEL_ACTIVATED:
            case EventBus_1.EVENT_TYPES.TUNNEL_DATA_FLOW:
                this.io.emit('tunnel-update', { type, payload });
                if (payload.tunnelId) {
                    this.io.to(`tunnel:${payload.tunnelId}`).emit('tunnel-update', { type, payload });
                }
                break;
            case EventBus_1.EVENT_TYPES.WORKFLOW_STARTED:
            case EventBus_1.EVENT_TYPES.WORKFLOW_PROGRESS:
            case EventBus_1.EVENT_TYPES.WORKFLOW_COMPLETED:
            case EventBus_1.EVENT_TYPES.WORKFLOW_FAILED:
                this.io.emit('workflow-update', { type, payload });
                if (payload.workflowId) {
                    this.io.to(`workflow:${payload.workflowId}`).emit('workflow-update', { type, payload });
                }
                break;
            case EventBus_1.EVENT_TYPES.EVOLUTION_MUTATION:
            case EventBus_1.EVENT_TYPES.EVOLUTION_SELECTION:
            case EventBus_1.EVENT_TYPES.EVOLUTION_PROMOTION:
                this.io.emit('evolution-update', { type, payload });
                break;
            case EventBus_1.EVENT_TYPES.CONTEXT_UPDATED:
            case EventBus_1.EVENT_TYPES.CONTEXT_SHARED:
                this.io.emit('context-update', { type, payload });
                break;
            case EventBus_1.EVENT_TYPES.USER_PRESENCE:
                this.io.emit('user-presence', payload);
                break;
            case EventBus_1.EVENT_TYPES.SYSTEM_STATUS:
                this.io.emit('system-status', payload);
                break;
            default:
                // Broadcast unknown events as generic updates
                this.io.emit('generic-update', { type, payload });
                break;
        }
    }
    broadcastConnectionCount() {
        const count = this.connectedUsers.size;
        this.io.emit('connection-count', { count });
        // Also publish to event bus
        this.eventBus.publish(EventBus_1.EVENT_TYPES.SYSTEM_STATUS, { type: 'connection_count', count }, 'socket-service');
    }
    /**
     * Broadcast a message to all connected clients
     */
    broadcast(event, data) {
        this.io.emit(event, data);
    }
    /**
     * Send a message to a specific room
     */
    broadcastToRoom(room, event, data) {
        this.io.to(room).emit(event, data);
    }
    /**
     * Send a message to a specific client
     */
    sendToClient(socketId, event, data) {
        this.io.to(socketId).emit(event, data);
    }
    /**
     * Get connected users statistics
     */
    getConnectedUsers() {
        const users = Array.from(this.connectedUsers.values());
        return {
            total: users.length,
            authenticated: users.filter(u => u.userId).length,
            users: users.map(u => ({
                id: u.id,
                username: u.username,
                joinedAt: u.joinedAt,
                lastActivity: u.lastActivity,
            })),
        };
    }
    /**
     * Disconnect a specific client
     */
    disconnectClient(socketId, reason) {
        const socket = this.io.sockets.sockets.get(socketId);
        if (socket) {
            socket.disconnect(true);
            logger_1.logger.info(`Forcibly disconnected client: ${socketId}, reason: ${reason || 'admin action'}`);
        }
    }
}
exports.SocketService = SocketService;

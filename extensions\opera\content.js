/**
 * EcoStamp Content Script
 * Tracks AI environmental impact across all platforms
 */

class EcoStampTracker {
  constructor() {
    this.apiUrl = 'http://localhost:3000';
    this.isTracking = false;
    this.currentPlatform = this.detectPlatform();
    this.conversationData = {
      messages: [],
      totalTokens: 0,
      startTime: Date.now()
    };
    
    this.init();
  }
  
  detectPlatform() {
    const hostname = window.location.hostname;
    
    if (hostname.includes('openai.com')) return 'ChatGPT';
    if (hostname.includes('claude.ai')) return 'Claude';
    if (hostname.includes('gemini.google.com') || hostname.includes('bard.google.com')) return 'Gemini';
    if (hostname.includes('copilot.microsoft.com')) return 'Copilot';
    if (hostname.includes('you.com')) return 'You.com';
    if (hostname.includes('perplexity.ai')) return 'Perplexity';
    if (hostname.includes('poe.com')) return 'Poe';
    
    return 'Unknown';
  }
  
  init() {
    this.createEcoStampWidget();
    this.startTracking();
    this.observeConversation();
    
    console.log(`🌱 EcoStamp initialized for ${this.currentPlatform}`);
  }
  
  createEcoStampWidget() {
    // Create floating widget
    const widget = document.createElement('div');
    widget.id = 'ecostamp-widget';
    widget.innerHTML = `
      <div class="ecostamp-header">
        <span class="ecostamp-icon">🌱</span>
        <span class="ecostamp-title">EcoStamp</span>
        <button class="ecostamp-toggle" title="Toggle EcoStamp">−</button>
      </div>
      <div class="ecostamp-content">
        <div class="ecostamp-stat">
          <span class="ecostamp-label">Platform:</span>
          <span class="ecostamp-value">${this.currentPlatform}</span>
        </div>
        <div class="ecostamp-stat">
          <span class="ecostamp-label">Eco Level:</span>
          <span class="ecostamp-value" id="eco-level">🌱🌱🌱🌱🌱</span>
        </div>
        <div class="ecostamp-stat">
          <span class="ecostamp-label">Energy:</span>
          <span class="ecostamp-value" id="energy-usage">0.00 kWh</span>
        </div>
        <div class="ecostamp-stat">
          <span class="ecostamp-label">Water:</span>
          <span class="ecostamp-value" id="water-usage">0.0 L</span>
        </div>
        <div class="ecostamp-stat">
          <span class="ecostamp-label">Hash:</span>
          <span class="ecostamp-value" id="conversation-hash">Generating...</span>
        </div>
        <div class="ecostamp-actions">
          <button class="ecostamp-btn" onclick="window.open('${this.apiUrl}/verify.html', '_blank')">
            Verify
          </button>
          <button class="ecostamp-btn" onclick="window.open('https://github.com/chris-ai-dev/Time_Stamp_Project', '_blank')">
            GitHub
          </button>
        </div>
      </div>
    `;
    
    document.body.appendChild(widget);
    
    // Add toggle functionality
    const toggle = widget.querySelector('.ecostamp-toggle');
    const content = widget.querySelector('.ecostamp-content');
    
    toggle.addEventListener('click', () => {
      const isCollapsed = content.style.display === 'none';
      content.style.display = isCollapsed ? 'block' : 'none';
      toggle.textContent = isCollapsed ? '−' : '+';
    });
    
    // Make widget draggable
    this.makeDraggable(widget);
  }
  
  makeDraggable(element) {
    let pos1 = 0, pos2 = 0, pos3 = 0, pos4 = 0;
    const header = element.querySelector('.ecostamp-header');
    
    header.onmousedown = dragMouseDown;
    
    function dragMouseDown(e) {
      e = e || window.event;
      e.preventDefault();
      pos3 = e.clientX;
      pos4 = e.clientY;
      document.onmouseup = closeDragElement;
      document.onmousemove = elementDrag;
    }
    
    function elementDrag(e) {
      e = e || window.event;
      e.preventDefault();
      pos1 = pos3 - e.clientX;
      pos2 = pos4 - e.clientY;
      pos3 = e.clientX;
      pos4 = e.clientY;
      element.style.top = (element.offsetTop - pos2) + "px";
      element.style.left = (element.offsetLeft - pos1) + "px";
    }
    
    function closeDragElement() {
      document.onmouseup = null;
      document.onmousemove = null;
    }
  }
  
  startTracking() {
    this.isTracking = true;
    
    // Update impact every 5 seconds
    setInterval(() => {
      if (this.isTracking) {
        this.updateImpactDisplay();
      }
    }, 5000);
  }
  
  observeConversation() {
    // Generic observer for conversation changes
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.addedNodes.length > 0) {
          this.analyzeNewContent(mutation.addedNodes);
        }
      });
    });
    
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }
  
  analyzeNewContent(nodes) {
    nodes.forEach(node => {
      if (node.nodeType === Node.ELEMENT_NODE) {
        const text = node.textContent || '';
        
        // Estimate tokens (rough approximation: 1 token ≈ 4 characters)
        const estimatedTokens = Math.ceil(text.length / 4);
        
        if (estimatedTokens > 10) { // Only count substantial content
          this.conversationData.totalTokens += estimatedTokens;
          this.conversationData.messages.push({
            timestamp: Date.now(),
            tokens: estimatedTokens,
            content: text.substring(0, 100) // First 100 chars for context
          });
          
          this.updateImpactDisplay();
        }
      }
    });
  }
  
  async updateImpactDisplay() {
    try {
      const impactData = await this.calculateImpact();
      
      // Update display
      document.getElementById('eco-level').textContent = this.generateEcoLevel(impactData.ecoLevel);
      document.getElementById('energy-usage').textContent = `${impactData.energy.toFixed(3)} kWh`;
      document.getElementById('water-usage').textContent = `${impactData.water.toFixed(1)} L`;
      document.getElementById('conversation-hash').textContent = impactData.hash.substring(0, 8) + '...';
      
      // Store hash for verification
      await this.storeConversationHash(impactData);
      
    } catch (error) {
      console.error('EcoStamp: Failed to update impact display', error);
    }
  }
  
  async calculateImpact() {
    const conversationText = this.conversationData.messages
      .map(m => m.content)
      .join(' ');
    
    try {
      const response = await fetch(`${this.apiUrl}/api/calculate-impact`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          platform: this.currentPlatform,
          tokens: this.conversationData.totalTokens,
          conversationText: conversationText,
          timestamp: Date.now()
        })
      });
      
      if (response.ok) {
        return await response.json();
      } else {
        throw new Error('API request failed');
      }
    } catch (error) {
      // Fallback calculation if API is unavailable
      return this.fallbackCalculation();
    }
  }
  
  fallbackCalculation() {
    const tokens = this.conversationData.totalTokens;
    const energy = tokens * 0.0001; // Rough estimate: 0.1 Wh per 1000 tokens
    const water = tokens * 0.001;   // Rough estimate: 1 mL per 1000 tokens
    const ecoLevel = Math.max(1, Math.min(5, Math.ceil(5 - (energy * 1000))));
    
    const hash = this.generateSimpleHash(
      this.currentPlatform + 
      tokens + 
      Date.now() + 
      this.conversationData.messages.map(m => m.content).join('')
    );
    
    return { energy, water, ecoLevel, hash, tokens };
  }
  
  generateSimpleHash(input) {
    let hash = 0;
    for (let i = 0; i < input.length; i++) {
      const char = input.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(16).padStart(8, '0');
  }
  
  generateEcoLevel(level) {
    const leaves = '🌱'.repeat(level) + '🍂'.repeat(5 - level);
    return leaves;
  }
  
  async storeConversationHash(impactData) {
    try {
      await fetch(`${this.apiUrl}/api/store-hash`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          hash: impactData.hash,
          platform: this.currentPlatform,
          energy: impactData.energy,
          water: impactData.water,
          ecoLevel: impactData.ecoLevel,
          tokens: impactData.tokens,
          timestamp: Date.now()
        })
      });
    } catch (error) {
      console.log('EcoStamp: Hash storage failed (offline mode)', error);
    }
  }
}

// Initialize EcoStamp when page loads
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    new EcoStampTracker();
  });
} else {
  new EcoStampTracker();
}

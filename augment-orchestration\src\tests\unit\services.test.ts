import { AgentAssignmentService } from '../../server/services/AgentAssignmentService';
import { WorkflowExecutionEngine } from '../../server/services/WorkflowExecutionEngine';
import { DarwinGodelMachine } from '../../server/services/DarwinGodelMachine';
import { ModelContextProtocol } from '../../server/services/ModelContextProtocol';
import { RoleBasedAccessControl } from '../../server/services/RoleBasedAccessControl';
import { CollaborationService } from '../../server/services/CollaborationService';
import { PrismaClient } from '@prisma/client';
import { EventBus } from '../../server/utils/EventBus';

// Mock Prisma
jest.mock('@prisma/client');
const mockPrisma = {
  agent: {
    findMany: jest.fn(),
    findUnique: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },
  workflowTemplate: {
    findMany: jest.fn(),
    findUnique: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
  },
  workflowExecution: {
    create: jest.fn(),
    update: jest.fn(),
    findMany: jest.fn(),
  },
  user: {
    findUnique: jest.fn(),
    findMany: jest.fn(),
  },
  role: {
    findMany: jest.fn(),
  },
  sharedContext: {
    create: jest.fn(),
    findMany: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },
  auditLog: {
    create: jest.fn(),
    findMany: jest.fn(),
  },
  comment: {
    create: jest.fn(),
    findMany: jest.fn(),
  },
  notification: {
    create: jest.fn(),
    findMany: jest.fn(),
  },
} as any;

// Mock EventBus
jest.mock('../../server/utils/EventBus');
const mockEventBus = {
  emit: jest.fn(),
  on: jest.fn(),
  off: jest.fn(),
} as any;

// Mock Socket.IO
const mockIo = {
  emit: jest.fn(),
  to: jest.fn().mockReturnThis(),
} as any;

describe('Service Unit Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('AgentAssignmentService', () => {
    let service: AgentAssignmentService;

    beforeEach(() => {
      service = new AgentAssignmentService(mockPrisma, mockEventBus);
    });

    test('should assign agents based on capabilities', async () => {
      const mockAgents = [
        {
          id: 'agent1',
          name: 'Code Executor',
          role: 'Code Executor',
          capabilities: ['code_execution', 'testing'],
          isActive: true,
          currentLoad: 2,
          maxConcurrentTasks: 5,
          performanceMetrics: { successRate: 0.95, avgResponseTime: 1500 },
        },
        {
          id: 'agent2',
          name: 'Data Analyst',
          role: 'Data Analyst',
          capabilities: ['data_analysis', 'visualization'],
          isActive: true,
          currentLoad: 1,
          maxConcurrentTasks: 3,
          performanceMetrics: { successRate: 0.88, avgResponseTime: 2000 },
        },
      ];

      mockPrisma.agent.findMany.mockResolvedValue(mockAgents);

      const result = await service.assignAgents(['code_execution'], 1);

      expect(result).toHaveLength(1);
      expect(result[0].agent.id).toBe('agent1');
      expect(result[0].confidence).toBeGreaterThan(0);
      expect(mockPrisma.agent.findMany).toHaveBeenCalledWith({
        where: { isActive: true },
        include: { performanceMetrics: true },
      });
    });

    test('should calculate confidence scores correctly', async () => {
      const mockAgents = [
        {
          id: 'agent1',
          capabilities: ['code_execution', 'testing'],
          currentLoad: 1,
          maxConcurrentTasks: 5,
          performanceMetrics: { successRate: 0.95, avgResponseTime: 1000 },
        },
      ];

      mockPrisma.agent.findMany.mockResolvedValue(mockAgents);

      const result = await service.assignAgents(['code_execution', 'testing'], 1);

      expect(result[0].confidence).toBeCloseTo(0.95, 1); // High confidence for perfect capability match
    });

    test('should handle no available agents', async () => {
      mockPrisma.agent.findMany.mockResolvedValue([]);

      const result = await service.assignAgents(['nonexistent_capability'], 1);

      expect(result).toHaveLength(0);
    });
  });

  describe('WorkflowExecutionEngine', () => {
    let engine: WorkflowExecutionEngine;
    let mockAssignmentService: jest.Mocked<AgentAssignmentService>;

    beforeEach(() => {
      mockAssignmentService = {
        assignAgents: jest.fn(),
      } as any;
      engine = new WorkflowExecutionEngine(mockPrisma, mockEventBus, mockAssignmentService);
    });

    test('should execute workflow with multiple stages', async () => {
      const mockWorkflow = {
        id: 'workflow1',
        name: 'Test Workflow',
        definition: {
          stages: [
            {
              id: 'stage1',
              name: 'Initial Stage',
              agentRole: 'Code Executor',
              parameters: { task: 'initialize' },
              dependencies: [],
            },
            {
              id: 'stage2',
              name: 'Processing Stage',
              agentRole: 'Data Analyst',
              parameters: { task: 'process' },
              dependencies: ['stage1'],
            },
          ],
        },
      };

      const mockExecution = {
        id: 'execution1',
        status: 'PENDING',
        stages: [],
      };

      mockPrisma.workflowTemplate.findUnique.mockResolvedValue(mockWorkflow);
      mockPrisma.workflowExecution.create.mockResolvedValue(mockExecution);
      mockAssignmentService.assignAgents.mockResolvedValue([
        {
          agent: { id: 'agent1', name: 'Test Agent' },
          confidence: 0.9,
        },
      ]);

      const result = await engine.executeWorkflow('workflow1', { inputData: 'test' });

      expect(result.executionId).toBe('execution1');
      expect(result.status).toBe('PENDING');
      expect(mockPrisma.workflowExecution.create).toHaveBeenCalled();
      expect(mockEventBus.emit).toHaveBeenCalledWith('workflow:started', expect.any(Object));
    });

    test('should handle workflow execution errors', async () => {
      mockPrisma.workflowTemplate.findUnique.mockResolvedValue(null);

      await expect(engine.executeWorkflow('nonexistent', {})).rejects.toThrow('Workflow template not found');
    });
  });

  describe('DarwinGodelMachine', () => {
    let dgm: DarwinGodelMachine;

    beforeEach(() => {
      dgm = new DarwinGodelMachine(mockPrisma, mockEventBus);
    });

    test('should start evolution process', async () => {
      const mockAgents = [
        { id: 'agent1', performanceMetrics: { successRate: 0.8, avgResponseTime: 1000 } },
        { id: 'agent2', performanceMetrics: { successRate: 0.9, avgResponseTime: 1200 } },
      ];

      mockPrisma.agent.findMany.mockResolvedValue(mockAgents);

      const sessionId = await dgm.startEvolution({
        populationSize: 10,
        mutationRate: 0.1,
        selectionPressure: 0.8,
        maxGenerations: 5,
      });

      expect(sessionId).toBeDefined();
      expect(mockEventBus.emit).toHaveBeenCalledWith('evolution:started', expect.any(Object));
    });

    test('should calculate fitness scores', async () => {
      const agent = {
        id: 'agent1',
        performanceMetrics: {
          successRate: 0.9,
          avgResponseTime: 1000,
          taskCompletionRate: 0.85,
          errorRate: 0.05,
        },
      };

      const fitness = await dgm.calculateFitness(agent as any);

      expect(fitness).toBeGreaterThan(0);
      expect(fitness).toBeLessThanOrEqual(1);
    });

    test('should stop evolution process', async () => {
      // Start evolution first
      mockPrisma.agent.findMany.mockResolvedValue([]);
      const sessionId = await dgm.startEvolution({
        populationSize: 5,
        mutationRate: 0.1,
        selectionPressure: 0.8,
        maxGenerations: 3,
      });

      const result = await dgm.stopEvolution();

      expect(result.success).toBe(true);
      expect(mockEventBus.emit).toHaveBeenCalledWith('evolution:stopped', expect.any(Object));
    });
  });

  describe('ModelContextProtocol', () => {
    let mcp: ModelContextProtocol;

    beforeEach(() => {
      mcp = new ModelContextProtocol(mockPrisma, mockEventBus);
    });

    test('should create context layer', async () => {
      const mockContext = {
        id: 'context1',
        name: 'Test Context',
        type: 'WORKFLOW',
        data: { key: 'value' },
        priority: 5,
      };

      mockPrisma.sharedContext.create.mockResolvedValue(mockContext);

      const layerId = await mcp.createContextLayer(
        'Test Context',
        'WORKFLOW',
        { key: 'value' },
        { priority: 5 }
      );

      expect(layerId).toBe('context1');
      expect(mockPrisma.sharedContext.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          name: 'Test Context',
          type: 'WORKFLOW',
          data: { key: 'value' },
          priority: 5,
        }),
      });
    });

    test('should merge contexts with different strategies', async () => {
      const contexts = [
        { id: 'ctx1', data: { a: 1, b: 2 }, priority: 5 },
        { id: 'ctx2', data: { b: 3, c: 4 }, priority: 7 },
      ];

      mockPrisma.sharedContext.findMany.mockResolvedValue(contexts);

      const merged = await mcp.mergeContexts(['ctx1', 'ctx2'], 'PRIORITY_BASED');

      expect(merged.data).toEqual({ a: 1, b: 3, c: 4 }); // Higher priority wins
    });

    test('should query contexts by criteria', async () => {
      const mockContexts = [
        { id: 'ctx1', type: 'WORKFLOW', tags: ['test'] },
        { id: 'ctx2', type: 'AGENT', tags: ['production'] },
      ];

      mockPrisma.sharedContext.findMany.mockResolvedValue(mockContexts);

      const results = await mcp.queryContexts({
        types: ['WORKFLOW'],
        tags: ['test'],
        limit: 10,
      });

      expect(results).toHaveLength(2);
      expect(mockPrisma.sharedContext.findMany).toHaveBeenCalledWith({
        where: {
          type: { in: ['WORKFLOW'] },
          tags: { hasSome: ['test'] },
        },
        take: 10,
        orderBy: { priority: 'desc' },
      });
    });
  });

  describe('RoleBasedAccessControl', () => {
    let rbac: RoleBasedAccessControl;

    beforeEach(() => {
      rbac = new RoleBasedAccessControl(mockPrisma);
    });

    test('should check user permissions', async () => {
      const mockUser = {
        id: 'user1',
        roles: [{ id: 'role1' }],
      };

      const mockRole = {
        id: 'role1',
        name: 'Admin',
        permissions: [
          {
            id: 'perm1',
            name: 'agents:read',
            resource: 'agents',
            action: 'read',
          },
        ],
      };

      mockPrisma.user.findUnique.mockResolvedValue(mockUser);
      rbac['roleCache'].set('role1', mockRole);

      const hasPermission = await rbac.hasPermission({
        userId: 'user1',
        resource: 'agents',
        action: 'read',
      });

      expect(hasPermission).toBe(true);
      expect(mockPrisma.auditLog.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          userId: 'user1',
          action: 'read',
          resource: 'agents',
          result: 'GRANTED',
        }),
      });
    });

    test('should deny access for insufficient permissions', async () => {
      const mockUser = {
        id: 'user1',
        roles: [{ id: 'role1' }],
      };

      const mockRole = {
        id: 'role1',
        name: 'Viewer',
        permissions: [
          {
            id: 'perm1',
            name: 'agents:read',
            resource: 'agents',
            action: 'read',
          },
        ],
      };

      mockPrisma.user.findUnique.mockResolvedValue(mockUser);
      rbac['roleCache'].set('role1', mockRole);

      const hasPermission = await rbac.hasPermission({
        userId: 'user1',
        resource: 'agents',
        action: 'delete',
      });

      expect(hasPermission).toBe(false);
      expect(mockPrisma.auditLog.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          result: 'DENIED',
        }),
      });
    });
  });

  describe('CollaborationService', () => {
    let collaboration: CollaborationService;

    beforeEach(() => {
      collaboration = new CollaborationService(mockPrisma, mockIo);
    });

    test('should create comment with mentions', async () => {
      const mockUser = {
        id: 'user1',
        username: 'testuser',
      };

      const mockComment = {
        id: 'comment1',
        userId: 'user1',
        resourceType: 'workflow',
        resourceId: 'workflow1',
        content: 'Test comment',
        mentions: ['user2'],
        createdAt: new Date(),
      };

      mockPrisma.user.findUnique.mockResolvedValue(mockUser);
      mockPrisma.comment.create.mockResolvedValue(mockComment);

      const result = await collaboration.createComment(
        'user1',
        'workflow',
        'workflow1',
        'Test comment',
        undefined,
        ['user2']
      );

      expect(result.id).toBe('comment1');
      expect(mockPrisma.notification.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          userId: 'user2',
          type: 'mention',
        }),
      });
      expect(mockIo.emit).toHaveBeenCalledWith('comment:created', expect.any(Object));
    });

    test('should track user presence', async () => {
      await collaboration.updateUserPresence('user1', 'workflow1', 'ACTIVE');

      expect(mockIo.emit).toHaveBeenCalledWith('presence:updated', {
        userId: 'user1',
        resourceId: 'workflow1',
        status: 'ACTIVE',
        timestamp: expect.any(Date),
      });
    });

    test('should create notifications', async () => {
      const mockNotification = {
        id: 'notif1',
        userId: 'user1',
        type: 'info',
        title: 'Test Notification',
        message: 'Test message',
      };

      mockPrisma.notification.create.mockResolvedValue(mockNotification);

      const result = await collaboration.createNotification(
        'user1',
        'info',
        'Test Notification',
        'Test message'
      );

      expect(result.id).toBe('notif1');
      expect(mockIo.emit).toHaveBeenCalledWith('notification:created', expect.any(Object));
    });
  });
});

"""
Core Feedback Engine

The central orchestration engine that coordinates all feedback loop components
to process AI outputs, validate them, and generate feedback entries.
"""

import time
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime

from .feedback_types import (
    FeedbackEntry, ValidationResult, FeedbackType, 
    ConfidenceAdjustment, TrustScoreConfig
)
from .interfaces import (
    BaseDomain, ConfidenceModel, TrustScoreCalculator, MemoryStore
)


class FeedbackEngine:
    """
    Central feedback processing engine that orchestrates all components
    to provide continuous QA and self-improvement capabilities.
    """
    
    def __init__(self, 
                 confidence_model: Optional[ConfidenceModel] = None,
                 trust_calculator: Optional[TrustScoreCalculator] = None,
                 memory_store: Optional[MemoryStore] = None,
                 config: Optional[Dict[str, Any]] = None):
        """
        Initialize the feedback engine.
        
        Args:
            confidence_model: Model for confidence score calculation
            trust_calculator: Calculator for trust scores
            memory_store: Storage for feedback entries
            config: Engine configuration
        """
        self.domains: Dict[str, BaseDomain] = {}
        self.confidence_model = confidence_model
        self.trust_calculator = trust_calculator
        self.memory_store = memory_store
        self.config = config or {}
        
        # Initialize logging
        self.logger = logging.getLogger(__name__)
        self._setup_logging()
        
        # Performance tracking
        self.stats = {
            'total_processed': 0,
            'by_domain': {},
            'by_feedback_type': {},
            'average_processing_time': 0.0
        }
    
    def _setup_logging(self):
        """Setup developer-only logging configuration."""
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    def register_domain(self, domain_name: str, domain: BaseDomain) -> None:
        """
        Register a domain with the feedback engine.
        
        Args:
            domain_name: Unique name for the domain
            domain: Domain implementation
        """
        domain.initialize_components()
        self.domains[domain_name] = domain
        self.stats['by_domain'][domain_name] = 0
        
        self.logger.info(f"Registered domain: {domain_name}")
    
    def unregister_domain(self, domain_name: str) -> bool:
        """
        Unregister a domain from the feedback engine.
        
        Args:
            domain_name: Name of domain to unregister
            
        Returns:
            True if domain was unregistered, False if not found
        """
        if domain_name in self.domains:
            del self.domains[domain_name]
            self.logger.info(f"Unregistered domain: {domain_name}")
            return True
        return False
    
    def process_output(self, 
                      domain: str, 
                      raw_output: Any, 
                      context: Optional[Dict[str, Any]] = None,
                      agent_id: Optional[str] = None) -> FeedbackEntry:
        """
        Process AI output through the complete feedback pipeline.
        
        Args:
            domain: Domain name for processing
            raw_output: Raw AI output to process
            context: Additional context information
            agent_id: Optional agent identifier
            
        Returns:
            Complete feedback entry with all analysis results
        """
        start_time = time.time()
        context = context or {}
        
        try:
            # Validate domain exists
            if domain not in self.domains:
                raise ValueError(f"Unknown domain: {domain}")
            
            domain_processor = self.domains[domain]
            
            # Process through domain pipeline
            validation_result = domain_processor.process_output(raw_output, context)
            
            # Calculate confidence score
            confidence_score = 0.5  # Default
            if self.confidence_model:
                confidence_score = self.confidence_model.calculate_confidence(
                    validation_result, context
                )
            
            # Calculate trust score
            trust_score = 0.5  # Default
            if self.trust_calculator and agent_id:
                # Get recent entries for this agent
                recent_entries = []
                if self.memory_store:
                    recent_entries = self.memory_store.retrieve_entries(
                        domain=domain, agent_id=agent_id, limit=100
                    )
                
                trust_score = self.trust_calculator.calculate_trust_score(
                    agent_id, domain, recent_entries
                )
            
            # Create feedback entry
            processing_time = (time.time() - start_time) * 1000  # Convert to ms
            
            feedback_entry = FeedbackEntry(
                domain=domain,
                agent_id=agent_id,
                original_output=raw_output,
                processed_output=validation_result.metadata.get('processed_output'),
                context=context,
                feedback_type=validation_result.feedback_type,
                confidence_score=confidence_score,
                trust_score=trust_score,
                validation_passed=validation_result.is_valid,
                validation_issues=validation_result.issues,
                processing_time_ms=processing_time,
                metadata=validation_result.metadata,
                recommendations=validation_result.recommendations
            )
            
            # Store the entry
            if self.memory_store:
                self.memory_store.store_entry(feedback_entry)
            
            # Update statistics
            self._update_stats(domain, validation_result.feedback_type, processing_time)
            
            # Update trust score if calculator is available
            if self.trust_calculator and agent_id:
                self.trust_calculator.update_trust_score(agent_id, domain, feedback_entry)
            
            self.logger.info(
                f"Processed output for domain '{domain}': "
                f"feedback={validation_result.feedback_type.value}, "
                f"confidence={confidence_score:.3f}, "
                f"trust={trust_score:.3f}, "
                f"time={processing_time:.1f}ms"
            )
            
            return feedback_entry
            
        except Exception as e:
            self.logger.error(f"Error processing output for domain '{domain}': {str(e)}")
            
            # Create error feedback entry
            error_entry = FeedbackEntry(
                domain=domain,
                agent_id=agent_id,
                original_output=raw_output,
                context=context,
                feedback_type=FeedbackType.MISCELLANEOUS,
                confidence_score=0.0,
                trust_score=0.0,
                validation_passed=False,
                validation_issues=[{
                    'type': 'processing_error',
                    'message': str(e),
                    'severity': 'critical'
                }],
                processing_time_ms=(time.time() - start_time) * 1000,
                metadata={'error': True, 'error_message': str(e)}
            )
            
            if self.memory_store:
                self.memory_store.store_entry(error_entry)
            
            return error_entry
    
    def batch_process(self, 
                     domain: str, 
                     outputs: List[Dict[str, Any]], 
                     batch_context: Optional[Dict[str, Any]] = None) -> List[FeedbackEntry]:
        """
        Process multiple outputs in batch mode.
        
        Args:
            domain: Domain name for processing
            outputs: List of output dictionaries with 'output' and optional 'context', 'agent_id'
            batch_context: Shared context for all outputs in batch
            
        Returns:
            List of feedback entries for all processed outputs
        """
        batch_context = batch_context or {}
        results = []
        
        self.logger.info(f"Starting batch processing: {len(outputs)} outputs for domain '{domain}'")
        
        for i, output_data in enumerate(outputs):
            # Merge individual context with batch context
            individual_context = output_data.get('context', {})
            merged_context = {**batch_context, **individual_context, 'batch_index': i}
            
            result = self.process_output(
                domain=domain,
                raw_output=output_data['output'],
                context=merged_context,
                agent_id=output_data.get('agent_id')
            )
            results.append(result)
        
        self.logger.info(f"Completed batch processing: {len(results)} entries created")
        return results
    
    def _update_stats(self, domain: str, feedback_type: FeedbackType, processing_time: float):
        """Update internal statistics."""
        self.stats['total_processed'] += 1
        self.stats['by_domain'][domain] = self.stats['by_domain'].get(domain, 0) + 1
        
        feedback_key = feedback_type.value
        self.stats['by_feedback_type'][feedback_key] = (
            self.stats['by_feedback_type'].get(feedback_key, 0) + 1
        )
        
        # Update average processing time
        total = self.stats['total_processed']
        current_avg = self.stats['average_processing_time']
        self.stats['average_processing_time'] = (
            (current_avg * (total - 1) + processing_time) / total
        )
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get engine performance statistics."""
        return self.stats.copy()
    
    def get_domain_info(self) -> Dict[str, Any]:
        """Get information about registered domains."""
        return {
            name: domain.get_domain_info() 
            for name, domain in self.domains.items()
        }
    
    def health_check(self) -> Dict[str, Any]:
        """Perform health check on all components."""
        health = {
            'engine': 'healthy',
            'domains': {},
            'components': {
                'confidence_model': self.confidence_model is not None,
                'trust_calculator': self.trust_calculator is not None,
                'memory_store': self.memory_store is not None
            },
            'timestamp': datetime.utcnow().isoformat()
        }
        
        # Check each domain
        for domain_name, domain in self.domains.items():
            try:
                domain_info = domain.get_domain_info()
                health['domains'][domain_name] = 'healthy'
            except Exception as e:
                health['domains'][domain_name] = f'error: {str(e)}'
        
        return health

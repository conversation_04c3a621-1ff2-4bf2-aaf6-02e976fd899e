# Thread-Merging Orchestrator

An automated system that searches, highlights, and merges relevant threads (with full history) from **all major AI chatbots**, then feeds them to any supported LLM for code generation/analysis.

## 🤖 Supported AI Platforms

### **Thread Sources** (Retrieval)
- **ChatGPT** (OpenAI) - API + Browser automation
- **Perplexity** - API + Browser automation
- **Claude** (Anthropic) - API + Browser automation
- **Gemini** (Google) - API + Browser automation
- **Mistral AI** - API + Browser automation
- **Cohere** - API + Browser automation

### **Analysis Targets** (Code Generation)
- **Claude** (Anthropic) - claude-3-sonnet, claude-3-opus
- **Gemini** (Google) - gemini-pro, gemini-ultra
- **ChatGPT** (OpenAI) - gpt-4-turbo, gpt-4, gpt-3.5-turbo
- **Mistral AI** - mistral-large, mistral-medium
- **Cohere** - command-r-plus, command-r
- **<PERSON>lama** (via HuggingFace) - llama-2-70b, llama-2-13b
- **Groq** - Ultra-fast inference for Llama models
- **Together AI** - Open-source model hosting
- **Replicate** - Cloud-based model inference
- **Fireworks AI** - High-performance inference

## 🚀 Features

- **Universal AI Integration**: Works with 10+ major AI platforms
- **Multi-Source Thread Retrieval**: Automatically retrieves conversations from all supported platforms
- **Intelligent Search**: Uses semantic search, keyword matching, and content analysis to find relevant threads
- **Smart Merging**: Deduplicates and merges threads while preserving context and chronology
- **Flexible LLM Integration**: Send merged threads to any supported LLM for analysis
- **Web Interface**: User-friendly web interface for easy interaction
- **CLI Support**: Command-line interface for automation and scripting
- **Caching**: Intelligent caching to avoid unnecessary API calls
- **Logging**: Comprehensive logging for debugging and monitoring

## 📋 Prerequisites

- Node.js 18.0.0 or higher
- API keys for the platforms you want to use:
  - **OpenAI** (ChatGPT) - Required for embeddings and ChatGPT analysis
  - **Perplexity** - For Perplexity thread retrieval and analysis
  - **Claude** (Anthropic) - For Claude analysis (recommended)
  - **Gemini** (Google) - For Gemini analysis
  - **Mistral AI** - For Mistral analysis
  - **Cohere** - For Cohere analysis
  - **HuggingFace** - For Llama and other open-source models
  - **Groq** - For ultra-fast Llama inference
  - **Others** - Replicate, Together AI, Fireworks AI (optional)

## 🛠️ Installation

1. **Clone or create the project directory**:
   ```bash
   mkdir thread-merging-orchestrator
   cd thread-merging-orchestrator
   ```

2. **Run the setup script**:
   ```bash
   node scripts/setup.js
   ```

3. **Configure your API keys** in the `.env` file:
   ```bash
   OPENAI_API_KEY=your_openai_api_key_here
   PERPLEXITY_API_KEY=your_perplexity_api_key_here
   CLAUDE_API_KEY=your_claude_api_key_here
   GEMINI_API_KEY=your_gemini_api_key_here
   ```

## 🎯 Usage

### Command Line Interface

#### Full Orchestration
```bash
# Basic orchestration
npm start -- orchestrate "JavaScript async/await best practices"

# With options
npm start -- orchestrate "React performance optimization" \
  --target-llm claude \
  --task code_generation \
  --max-threads 15 \
  --use-cache
```

#### Search Threads
```bash
# Search without full orchestration
npm start -- search "Python machine learning" --limit 10
```

#### Retrieve Threads
```bash
# Retrieve fresh threads from all sources
npm start -- retrieve --limit 20

# Force refresh cache
npm start -- retrieve --force-refresh
```

#### View History
```bash
# Show recent orchestrations
npm start -- history --limit 10
```

#### System Statistics
```bash
# Show system stats
npm start -- stats
```

### Web Interface

Start the web server:
```bash
npm start -- web
```

Then open your browser to `http://localhost:3000`

### API Endpoints

The web server provides REST API endpoints:

- `POST /api/orchestrate` - Run full orchestration
- `POST /api/search` - Search threads
- `GET /api/history` - Get orchestration history
- `GET /api/stats` - Get system statistics
- `POST /api/retrieve` - Retrieve threads

## ⚙️ Configuration

### Environment Variables

| Variable                         | Description               | Default  |
| -------------------------------- | ------------------------- | -------- |
| `OPENAI_API_KEY`                 | OpenAI API key            | Required |
| `PERPLEXITY_API_KEY`             | Perplexity API key        | Required |
| `CLAUDE_API_KEY`                 | Claude API key            | Required |
| `GEMINI_API_KEY`                 | Gemini API key            | Required |
| `RATE_LIMIT_REQUESTS_PER_MINUTE` | API rate limit            | 60       |
| `SEARCH_SIMILARITY_THRESHOLD`    | Semantic search threshold | 0.7      |
| `MAX_THREADS_TO_RETRIEVE`        | Max threads per source    | 50       |
| `WEB_PORT`                       | Web server port           | 3000     |
| `LOG_LEVEL`                      | Logging level             | info     |

### Search Options

- **Semantic Search**: Uses embeddings to find semantically similar content
- **Keyword Search**: TF-IDF based keyword matching
- **Content Search**: Direct text matching with fuzzy search
- **Highlighting**: Extracts and highlights relevant passages

### LLM Tasks

- **code_generation**: Generate code based on thread discussions
- **code_analysis**: Analyze and review code from threads
- **summarization**: Create summaries of thread insights

## 📁 Project Structure

```
thread-merging-orchestrator/
├── src/
│   ├── api/                 # API clients for different services
│   ├── config/              # Configuration management
│   ├── merging/             # Thread merging logic
│   ├── retrieval/           # Thread retrieval from sources
│   ├── search/              # Search and highlighting engine
│   ├── utils/               # Utility functions
│   ├── web/                 # Web interface
│   ├── index.js             # Main CLI entry point
│   └── orchestrator.js      # Core orchestration logic
├── scripts/
│   └── setup.js             # Setup script
├── data/                    # Data storage
├── logs/                    # Log files
├── package.json
├── .env.example
└── README.md
```

## 🔧 Development

### Running Tests
```bash
npm test
```

### Development Mode
```bash
npm run dev
```

### Debugging
Set `LOG_LEVEL=debug` in your `.env` file for detailed logging.

## 🚨 Important Notes

### API Limitations

1. **ChatGPT**: OpenAI doesn't provide direct access to conversation history via API. The system includes browser automation as a fallback, but this requires manual login.

2. **Perplexity**: API access to conversation history may be limited. Browser automation is included as a backup method.

3. **Rate Limits**: All API clients include rate limiting to respect service limits.

### Browser Automation

If API access is unavailable, the system can use browser automation:
- Requires manual login to ChatGPT/Perplexity
- Set `BROWSER_HEADLESS=false` to see the browser
- May require CAPTCHA solving

### Security

- Never commit your `.env` file
- Use environment variables in production
- Regularly rotate API keys
- Monitor API usage and costs

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details

## 🆘 Troubleshooting

### Common Issues

1. **API Key Errors**: Ensure all API keys are correctly set in `.env`
2. **Rate Limiting**: Reduce request frequency or increase rate limits
3. **Memory Issues**: Reduce `MAX_THREADS_TO_RETRIEVE` for large datasets
4. **Browser Automation**: Ensure you're logged into ChatGPT/Perplexity

### Getting Help

- Check the logs in the `logs/` directory
- Use `npm start -- stats` to check system status
- Enable debug logging with `LOG_LEVEL=debug`

## 🔮 Future Enhancements

- Support for additional AI platforms
- Advanced filtering and categorization
- Real-time thread monitoring
- Integration with code repositories
- Custom prompt templates
- Batch processing capabilities

/**
 * REST API Server for DGM
 * 
 * Provides RESTful API endpoints for external integration with the DGM system:
 * - Agent management endpoints
 * - Evolution control endpoints
 * - Metrics and monitoring endpoints
 * - Archive and genealogy endpoints
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const chalk = require('chalk');

class APIServer {
  constructor(dgmEngine, config) {
    this.dgmEngine = dgmEngine;
    this.config = config;
    this.app = express();
    this.server = null;
    this.port = config.get('ui.api.port', 3003);
    this.authEnabled = config.get('ui.api.authentication', false);
    this.isRunning = false;
    
    this.setupMiddleware();
    this.setupRoutes();
    this.setupErrorHandling();
  }

  /**
   * Start the API server
   */
  async start() {
    return new Promise((resolve, reject) => {
      this.server = this.app.listen(this.port, (error) => {
        if (error) {
          reject(error);
        } else {
          this.isRunning = true;
          console.log(chalk.green(`🚀 DGM API Server running on http://localhost:${this.port}`));
          resolve();
        }
      });
    });
  }

  /**
   * Stop the API server
   */
  async stop() {
    return new Promise((resolve) => {
      if (this.server) {
        this.server.close(() => {
          this.isRunning = false;
          console.log(chalk.yellow('🚀 DGM API Server stopped'));
          resolve();
        });
      } else {
        resolve();
      }
    });
  }

  /**
   * Setup middleware
   */
  setupMiddleware() {
    // Security middleware
    this.app.use(helmet());
    this.app.use(cors());
    
    // Rate limiting
    const limiter = rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 100, // limit each IP to 100 requests per windowMs
      message: 'Too many requests from this IP, please try again later.'
    });
    this.app.use(limiter);
    
    // Body parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true }));
    
    // Request logging
    this.app.use((req, res, next) => {
      console.log(chalk.gray(`${req.method} ${req.path} - ${req.ip}`));
      next();
    });
    
    // Authentication middleware (if enabled)
    if (this.authEnabled) {
      this.app.use(this.authenticateRequest.bind(this));
    }
  }

  /**
   * Setup API routes
   */
  setupRoutes() {
    // Health check
    this.app.get('/health', (req, res) => {
      res.json({
        status: 'healthy',
        timestamp: new Date(),
        uptime: process.uptime(),
        version: '1.0.0'
      });
    });

    // System status and control
    this.app.get('/api/v1/status', this.getSystemStatus.bind(this));
    this.app.post('/api/v1/evolution/start', this.startEvolution.bind(this));
    this.app.post('/api/v1/evolution/stop', this.stopEvolution.bind(this));
    this.app.post('/api/v1/evolution/pause', this.pauseEvolution.bind(this));

    // Agent management
    this.app.get('/api/v1/agents', this.getAgents.bind(this));
    this.app.get('/api/v1/agents/:id', this.getAgent.bind(this));
    this.app.post('/api/v1/agents/:id/approve', this.approveAgent.bind(this));
    this.app.post('/api/v1/agents/:id/reject', this.rejectAgent.bind(this));
    this.app.delete('/api/v1/agents/:id', this.deleteAgent.bind(this));

    // Archive and genealogy
    this.app.get('/api/v1/archive/stats', this.getArchiveStats.bind(this));
    this.app.get('/api/v1/genealogy', this.getGenealogy.bind(this));
    this.app.get('/api/v1/genealogy/:id/lineage', this.getAgentLineage.bind(this));

    // Metrics and monitoring
    this.app.get('/api/v1/metrics', this.getMetrics.bind(this));
    this.app.get('/api/v1/metrics/history', this.getMetricsHistory.bind(this));
    this.app.get('/api/v1/evolution/history', this.getEvolutionHistory.bind(this));

    // Configuration
    this.app.get('/api/v1/config', this.getConfig.bind(this));
    this.app.put('/api/v1/config', this.updateConfig.bind(this));

    // Benchmarks and validation
    this.app.get('/api/v1/benchmarks', this.getBenchmarks.bind(this));
    this.app.post('/api/v1/benchmarks/run', this.runBenchmarks.bind(this));
    this.app.post('/api/v1/agents/:id/validate', this.validateAgent.bind(this));

    // Data export/import
    this.app.get('/api/v1/export/:type', this.exportData.bind(this));
    this.app.post('/api/v1/import', this.importData.bind(this));

    // API documentation
    this.app.get('/api/v1/docs', this.getAPIDocs.bind(this));
  }

  /**
   * Setup error handling
   */
  setupErrorHandling() {
    // 404 handler
    this.app.use((req, res) => {
      res.status(404).json({
        error: 'Not Found',
        message: `Route ${req.method} ${req.path} not found`,
        timestamp: new Date()
      });
    });

    // Global error handler
    this.app.use((error, req, res, next) => {
      console.error(chalk.red(`API Error: ${error.message}`));
      
      res.status(error.status || 500).json({
        error: error.name || 'Internal Server Error',
        message: error.message,
        timestamp: new Date(),
        ...(process.env.NODE_ENV === 'development' && { stack: error.stack })
      });
    });
  }

  /**
   * Authentication middleware
   */
  authenticateRequest(req, res, next) {
    // Skip authentication for health check
    if (req.path === '/health') {
      return next();
    }

    const token = req.headers.authorization?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication token required'
      });
    }

    // Validate token (implement your authentication logic here)
    if (!this.validateToken(token)) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Invalid authentication token'
      });
    }

    next();
  }

  /**
   * Validate authentication token
   */
  validateToken(token) {
    // Implement your token validation logic here
    // For now, accept any non-empty token
    return token && token.length > 0;
  }

  /**
   * API endpoint implementations
   */
  async getSystemStatus(req, res) {
    try {
      const status = {
        isRunning: this.dgmEngine.isRunning,
        currentGeneration: this.dgmEngine.currentGeneration,
        populationSize: this.dgmEngine.agentManager.currentPopulation.length,
        evolutionHistory: this.dgmEngine.evolutionHistory.length,
        uptime: process.uptime(),
        memoryUsage: process.memoryUsage(),
        timestamp: new Date()
      };

      res.json(status);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async startEvolution(req, res) {
    try {
      if (this.dgmEngine.isRunning) {
        return res.status(400).json({ error: 'Evolution is already running' });
      }

      await this.dgmEngine.startEvolution(req.body);
      res.json({ success: true, message: 'Evolution started' });
    } catch (error) {
      res.status(400).json({ error: error.message });
    }
  }

  async stopEvolution(req, res) {
    try {
      if (!this.dgmEngine.isRunning) {
        return res.status(400).json({ error: 'Evolution is not running' });
      }

      this.dgmEngine.isRunning = false;
      res.json({ success: true, message: 'Evolution stopped' });
    } catch (error) {
      res.status(400).json({ error: error.message });
    }
  }

  async pauseEvolution(req, res) {
    try {
      // Implement pause functionality
      res.json({ success: true, message: 'Evolution paused' });
    } catch (error) {
      res.status(400).json({ error: error.message });
    }
  }

  async getAgents(req, res) {
    try {
      const { limit = 50, offset = 0, sortBy = 'fitness', order = 'desc' } = req.query;
      
      const population = await this.dgmEngine.agentManager.getCurrentPopulation();
      
      // Sort agents
      const sortedAgents = population.sort((a, b) => {
        const aVal = a[sortBy] || 0;
        const bVal = b[sortBy] || 0;
        return order === 'desc' ? bVal - aVal : aVal - bVal;
      });

      // Apply pagination
      const paginatedAgents = sortedAgents.slice(offset, offset + parseInt(limit));

      const response = {
        agents: paginatedAgents.map(agent => ({
          id: agent.id,
          generation: agent.generation,
          type: agent.type,
          fitness: agent.fitness,
          created: agent.created,
          parentIds: agent.parentIds,
          metrics: agent.metrics
        })),
        total: population.length,
        limit: parseInt(limit),
        offset: parseInt(offset)
      };

      res.json(response);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async getAgent(req, res) {
    try {
      const agent = await this.dgmEngine.archive.retrieveAgent(req.params.id);
      const lineage = await this.dgmEngine.archive.getAgentLineage(req.params.id);

      res.json({
        ...agent,
        lineage
      });
    } catch (error) {
      res.status(404).json({ error: 'Agent not found' });
    }
  }

  async approveAgent(req, res) {
    try {
      // Implement agent approval logic
      res.json({ success: true, message: `Agent ${req.params.id} approved` });
    } catch (error) {
      res.status(400).json({ error: error.message });
    }
  }

  async rejectAgent(req, res) {
    try {
      // Implement agent rejection logic
      res.json({ success: true, message: `Agent ${req.params.id} rejected` });
    } catch (error) {
      res.status(400).json({ error: error.message });
    }
  }

  async deleteAgent(req, res) {
    try {
      // Implement agent deletion logic
      res.json({ success: true, message: `Agent ${req.params.id} deleted` });
    } catch (error) {
      res.status(400).json({ error: error.message });
    }
  }

  async getArchiveStats(req, res) {
    try {
      const stats = await this.dgmEngine.archive.getArchiveStats();
      res.json(stats);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async getGenealogy(req, res) {
    try {
      const genealogyStats = this.dgmEngine.archive.genealogy.getGenealogyStats();
      const familyTree = this.dgmEngine.archive.genealogy.generateFamilyTreeData();

      res.json({
        stats: genealogyStats,
        familyTree
      });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async getAgentLineage(req, res) {
    try {
      const lineage = await this.dgmEngine.archive.getAgentLineage(req.params.id);
      res.json(lineage);
    } catch (error) {
      res.status(404).json({ error: 'Agent not found' });
    }
  }

  async getMetrics(req, res) {
    try {
      const metricsStats = this.dgmEngine.metricsCollector.getMetricsStats();
      res.json(metricsStats);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async getMetricsHistory(req, res) {
    try {
      const { agentId } = req.query;
      
      if (agentId) {
        const history = this.dgmEngine.metricsCollector.getMetricsHistory(agentId);
        res.json({ agentId, history });
      } else {
        res.status(400).json({ error: 'Agent ID required' });
      }
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async getEvolutionHistory(req, res) {
    try {
      const { limit = 100 } = req.query;
      const history = this.dgmEngine.evolutionHistory.slice(-parseInt(limit));
      res.json(history);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async getConfig(req, res) {
    try {
      const config = this.config.getAll();
      res.json(config);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async updateConfig(req, res) {
    try {
      await this.config.update(req.body);
      res.json({ success: true, message: 'Configuration updated' });
    } catch (error) {
      res.status(400).json({ error: error.message });
    }
  }

  async getBenchmarks(req, res) {
    try {
      const stats = this.dgmEngine.benchmarkSuite.getBenchmarkStats();
      res.json(stats);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async runBenchmarks(req, res) {
    try {
      const { agentId } = req.body;
      
      if (!agentId) {
        return res.status(400).json({ error: 'Agent ID required' });
      }

      const agent = await this.dgmEngine.archive.retrieveAgent(agentId);
      const results = await this.dgmEngine.benchmarkSuite.evaluateAgent(agent);
      
      res.json(results);
    } catch (error) {
      res.status(400).json({ error: error.message });
    }
  }

  async validateAgent(req, res) {
    try {
      const agent = await this.dgmEngine.archive.retrieveAgent(req.params.id);
      const validationResults = await this.dgmEngine.validator.validateAgent(agent);
      
      res.json(validationResults);
    } catch (error) {
      res.status(400).json({ error: error.message });
    }
  }

  async exportData(req, res) {
    try {
      const { type } = req.params;
      
      // Implement data export logic based on type
      res.json({ message: `Export ${type} not yet implemented` });
    } catch (error) {
      res.status(400).json({ error: error.message });
    }
  }

  async importData(req, res) {
    try {
      // Implement data import logic
      res.json({ message: 'Import not yet implemented' });
    } catch (error) {
      res.status(400).json({ error: error.message });
    }
  }

  async getAPIDocs(req, res) {
    const docs = {
      title: 'DGM API Documentation',
      version: '1.0.0',
      description: 'REST API for Darwin Gödel Machine',
      endpoints: {
        'GET /health': 'Health check',
        'GET /api/v1/status': 'Get system status',
        'POST /api/v1/evolution/start': 'Start evolution',
        'POST /api/v1/evolution/stop': 'Stop evolution',
        'GET /api/v1/agents': 'List agents',
        'GET /api/v1/agents/:id': 'Get agent details',
        'GET /api/v1/genealogy': 'Get genealogy data',
        'GET /api/v1/metrics': 'Get performance metrics',
        'GET /api/v1/config': 'Get configuration'
      }
    };

    res.json(docs);
  }

  /**
   * Get API server statistics
   */
  getAPIStats() {
    return {
      isRunning: this.isRunning,
      port: this.port,
      authEnabled: this.authEnabled
    };
  }
}

module.exports = APIServer;

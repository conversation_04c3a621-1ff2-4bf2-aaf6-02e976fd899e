/**
 * Agent Archive System
 * 
 * Manages the storage, retrieval, and versioning of orchestration agents:
 * - Git-based version control for agent code
 * - Performance metrics storage
 * - Genealogy tracking
 * - Archive search and retrieval
 */

const fs = require('fs').promises;
const path = require('path');
const { execSync } = require('child_process');
const chalk = require('chalk');
const { v4: uuidv4 } = require('uuid');

const VersionControl = require('./version-control');
const Genealogy = require('./genealogy');

class AgentArchive {
  constructor(config) {
    this.config = config;
    this.archivePath = path.join(process.cwd(), 'dgm-archive');
    this.versionControl = new VersionControl(this.archivePath);
    this.genealogy = new Genealogy(this.archivePath);
    this.agentIndex = new Map();
    this.performanceHistory = new Map();
  }

  /**
   * Initialize the archive system
   */
  async initialize() {
    try {
      // Create archive directory
      await fs.mkdir(this.archivePath, { recursive: true });
      
      // Initialize version control
      await this.versionControl.initialize();
      
      // Initialize genealogy tracking
      await this.genealogy.initialize();
      
      // Load existing archive index
      await this.loadArchiveIndex();
      
      console.log(chalk.green('✅ Agent archive initialized'));
      
    } catch (error) {
      throw new Error(`Archive initialization failed: ${error.message}`);
    }
  }

  /**
   * Archive a new agent
   */
  async archiveAgent(agent) {
    try {
      const archiveEntry = {
        id: agent.id,
        timestamp: new Date(),
        generation: agent.generation,
        parentIds: agent.parentIds || [],
        type: agent.type,
        metadata: agent.metadata,
        metrics: agent.metrics,
        fitness: agent.fitness,
        codeHash: this.calculateCodeHash(agent.code),
        archivePath: this.getAgentArchivePath(agent.id)
      };

      // Store agent code files
      await this.storeAgentCode(agent);
      
      // Commit to version control
      await this.versionControl.commitAgent(agent, `Archive agent ${agent.id}`);
      
      // Update genealogy
      await this.genealogy.addAgent(agent);
      
      // Update index
      this.agentIndex.set(agent.id, archiveEntry);
      
      // Store performance metrics
      await this.storePerformanceMetrics(agent);
      
      // Save updated index
      await this.saveArchiveIndex();
      
      console.log(chalk.blue(`📦 Agent ${agent.id} archived successfully`));
      return archiveEntry;
      
    } catch (error) {
      throw new Error(`Failed to archive agent ${agent.id}: ${error.message}`);
    }
  }

  /**
   * Archive a complete generation
   */
  async archiveGeneration(generation, agents) {
    try {
      const generationPath = path.join(this.archivePath, 'generations', `gen-${generation.number}`);
      await fs.mkdir(generationPath, { recursive: true });
      
      // Archive generation metadata
      await fs.writeFile(
        path.join(generationPath, 'metadata.json'),
        JSON.stringify(generation, null, 2)
      );
      
      // Archive all agents in the generation
      const archivedAgents = [];
      for (const agent of agents) {
        const archived = await this.archiveAgent(agent);
        archivedAgents.push(archived);
      }
      
      // Create generation summary
      const summary = {
        generation: generation.number,
        timestamp: generation.timestamp,
        agentCount: agents.length,
        bestFitness: generation.bestFitness,
        averageFitness: generation.averageFitness,
        agents: archivedAgents.map(a => ({
          id: a.id,
          fitness: a.fitness,
          type: a.type
        }))
      };
      
      await fs.writeFile(
        path.join(generationPath, 'summary.json'),
        JSON.stringify(summary, null, 2)
      );
      
      // Commit generation to version control
      await this.versionControl.commitGeneration(generation, `Archive generation ${generation.number}`);
      
      console.log(chalk.green(`📚 Generation ${generation.number} archived with ${agents.length} agents`));
      return summary;
      
    } catch (error) {
      throw new Error(`Failed to archive generation ${generation.number}: ${error.message}`);
    }
  }

  /**
   * Retrieve an agent from the archive
   */
  async retrieveAgent(agentId) {
    try {
      const archiveEntry = this.agentIndex.get(agentId);
      if (!archiveEntry) {
        throw new Error(`Agent ${agentId} not found in archive`);
      }
      
      // Load agent code
      const code = await this.loadAgentCode(agentId);
      
      // Load performance history
      const performanceHistory = this.performanceHistory.get(agentId) || [];
      
      const agent = {
        id: agentId,
        generation: archiveEntry.generation,
        parentIds: archiveEntry.parentIds,
        type: archiveEntry.type,
        created: archiveEntry.timestamp,
        code,
        metadata: archiveEntry.metadata,
        metrics: archiveEntry.metrics,
        fitness: archiveEntry.fitness,
        performanceHistory
      };
      
      return agent;
      
    } catch (error) {
      throw new Error(`Failed to retrieve agent ${agentId}: ${error.message}`);
    }
  }

  /**
   * Search agents by criteria
   */
  async searchAgents(criteria = {}) {
    const results = [];
    
    for (const [agentId, entry] of this.agentIndex) {
      let matches = true;
      
      // Filter by generation
      if (criteria.generation !== undefined && entry.generation !== criteria.generation) {
        matches = false;
      }
      
      // Filter by type
      if (criteria.type && entry.type !== criteria.type) {
        matches = false;
      }
      
      // Filter by minimum fitness
      if (criteria.minFitness !== undefined && entry.fitness < criteria.minFitness) {
        matches = false;
      }
      
      // Filter by maximum fitness
      if (criteria.maxFitness !== undefined && entry.fitness > criteria.maxFitness) {
        matches = false;
      }
      
      // Filter by parent
      if (criteria.parentId && !entry.parentIds.includes(criteria.parentId)) {
        matches = false;
      }
      
      // Filter by date range
      if (criteria.fromDate && entry.timestamp < criteria.fromDate) {
        matches = false;
      }
      
      if (criteria.toDate && entry.timestamp > criteria.toDate) {
        matches = false;
      }
      
      if (matches) {
        results.push(entry);
      }
    }
    
    // Sort results
    if (criteria.sortBy) {
      results.sort((a, b) => {
        switch (criteria.sortBy) {
          case 'fitness':
            return b.fitness - a.fitness;
          case 'generation':
            return b.generation - a.generation;
          case 'timestamp':
            return b.timestamp - a.timestamp;
          default:
            return 0;
        }
      });
    }
    
    // Limit results
    if (criteria.limit) {
      return results.slice(0, criteria.limit);
    }
    
    return results;
  }

  /**
   * Get top performing agents
   */
  async getTopPerformers(count = 10) {
    return this.searchAgents({
      sortBy: 'fitness',
      limit: count
    });
  }

  /**
   * Get agents from a specific generation
   */
  async getGenerationAgents(generationNumber) {
    return this.searchAgents({
      generation: generationNumber,
      sortBy: 'fitness'
    });
  }

  /**
   * Get agent lineage (ancestors and descendants)
   */
  async getAgentLineage(agentId) {
    return this.genealogy.getLineage(agentId);
  }

  /**
   * Store agent code files
   */
  async storeAgentCode(agent) {
    const agentDir = this.getAgentArchivePath(agent.id);
    await fs.mkdir(agentDir, { recursive: true });
    
    // Store orchestrator code
    await fs.writeFile(
      path.join(agentDir, 'orchestrator.js'),
      agent.code.orchestrator
    );
    
    // Store cross-flow engine code
    await fs.writeFile(
      path.join(agentDir, 'cross-flow-engine.js'),
      agent.code.crossFlow
    );
    
    // Store workflow files
    const workflowsDir = path.join(agentDir, 'workflows');
    await fs.mkdir(workflowsDir, { recursive: true });
    
    for (const [name, code] of Object.entries(agent.code.workflows || {})) {
      await fs.writeFile(
        path.join(workflowsDir, `${name}.js`),
        code
      );
    }
    
    // Store agent metadata
    await fs.writeFile(
      path.join(agentDir, 'metadata.json'),
      JSON.stringify({
        id: agent.id,
        generation: agent.generation,
        parentIds: agent.parentIds,
        type: agent.type,
        created: agent.created || new Date(),
        metadata: agent.metadata,
        metrics: agent.metrics,
        fitness: agent.fitness
      }, null, 2)
    );
  }

  /**
   * Load agent code from archive
   */
  async loadAgentCode(agentId) {
    const agentDir = this.getAgentArchivePath(agentId);
    
    const code = {
      orchestrator: await fs.readFile(path.join(agentDir, 'orchestrator.js'), 'utf8'),
      crossFlow: await fs.readFile(path.join(agentDir, 'cross-flow-engine.js'), 'utf8'),
      workflows: {}
    };
    
    // Load workflow files
    const workflowsDir = path.join(agentDir, 'workflows');
    try {
      const workflowFiles = await fs.readdir(workflowsDir);
      
      for (const file of workflowFiles) {
        if (file.endsWith('.js')) {
          const name = path.basename(file, '.js');
          code.workflows[name] = await fs.readFile(path.join(workflowsDir, file), 'utf8');
        }
      }
    } catch (error) {
      // Workflows directory might not exist for older agents
    }
    
    return code;
  }

  /**
   * Store performance metrics
   */
  async storePerformanceMetrics(agent) {
    if (!this.performanceHistory.has(agent.id)) {
      this.performanceHistory.set(agent.id, []);
    }
    
    const history = this.performanceHistory.get(agent.id);
    history.push({
      timestamp: new Date(),
      metrics: agent.metrics,
      fitness: agent.fitness,
      benchmarkResults: agent.benchmarkResults
    });
    
    // Keep only recent history
    const maxHistory = this.config.get('archive.performanceHistoryLength', 100);
    if (history.length > maxHistory) {
      history.splice(0, history.length - maxHistory);
    }
  }

  /**
   * Calculate code hash for deduplication
   */
  calculateCodeHash(code) {
    const crypto = require('crypto');
    const codeString = JSON.stringify(code);
    return crypto.createHash('sha256').update(codeString).digest('hex');
  }

  /**
   * Get agent archive path
   */
  getAgentArchivePath(agentId) {
    return path.join(this.archivePath, 'agents', agentId);
  }

  /**
   * Load archive index
   */
  async loadArchiveIndex() {
    const indexPath = path.join(this.archivePath, 'index.json');
    
    try {
      const indexData = await fs.readFile(indexPath, 'utf8');
      const index = JSON.parse(indexData);
      
      this.agentIndex = new Map(Object.entries(index.agents || {}));
      this.performanceHistory = new Map(Object.entries(index.performanceHistory || {}));
      
    } catch (error) {
      // Index doesn't exist yet, start with empty maps
      this.agentIndex = new Map();
      this.performanceHistory = new Map();
    }
  }

  /**
   * Save archive index
   */
  async saveArchiveIndex() {
    const indexPath = path.join(this.archivePath, 'index.json');
    
    const index = {
      agents: Object.fromEntries(this.agentIndex),
      performanceHistory: Object.fromEntries(this.performanceHistory),
      lastUpdated: new Date()
    };
    
    await fs.writeFile(indexPath, JSON.stringify(index, null, 2));
  }

  /**
   * Get archive statistics
   */
  async getArchiveStats() {
    const stats = {
      totalAgents: this.agentIndex.size,
      generations: new Set([...this.agentIndex.values()].map(a => a.generation)).size,
      agentTypes: {},
      fitnessDistribution: {
        min: 0,
        max: 0,
        average: 0
      }
    };
    
    let totalFitness = 0;
    let minFitness = Infinity;
    let maxFitness = -Infinity;
    
    for (const entry of this.agentIndex.values()) {
      // Count agent types
      stats.agentTypes[entry.type] = (stats.agentTypes[entry.type] || 0) + 1;
      
      // Calculate fitness statistics
      totalFitness += entry.fitness;
      minFitness = Math.min(minFitness, entry.fitness);
      maxFitness = Math.max(maxFitness, entry.fitness);
    }
    
    if (this.agentIndex.size > 0) {
      stats.fitnessDistribution.min = minFitness;
      stats.fitnessDistribution.max = maxFitness;
      stats.fitnessDistribution.average = totalFitness / this.agentIndex.size;
    }
    
    return stats;
  }
}

module.exports = AgentArchive;

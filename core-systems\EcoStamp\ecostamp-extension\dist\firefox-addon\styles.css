/* Stamply Universal Styles */

.stamply-universal-footer {
    animation: stamplyFadeIn 0.3s ease-out;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.stamply-universal-footer:hover {
    background: linear-gradient(135deg, #edf2f7 0%, #e2e8f0 100%) !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

@keyframes stamplyFadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Platform-specific adjustments */

/* ChatGPT */
[data-message-author-role="assistant"] .stamply-universal-footer {
    margin-top: 12px;
}

/* Claude */
.assistant-message .stamply-universal-footer,
[data-role="assistant"] .stamply-universal-footer {
    margin-top: 12px;
}

/* Gemini */
[data-role="model"] .stamply-universal-footer,
.model-turn .stamply-universal-footer {
    margin-top: 12px;
}

/* Universal AI platforms */
.ai-response .stamply-universal-footer,
.bot-message .stamply-universal-footer,
.model-response .stamply-universal-footer {
    margin-top: 12px;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .stamply-universal-footer {
        background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%) !important;
        color: #a0aec0 !important;
        border-top-color: #4a5568 !important;
    }
    
    .stamply-universal-footer:hover {
        background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%) !important;
        color: #e2e8f0 !important;
    }
    
    .stamply-universal-footer .divider {
        color: #4a5568 !important;
    }
    
    .stamply-universal-footer span[style*="background: #edf2f7"] {
        background: #4a5568 !important;
        color: #e2e8f0 !important;
    }
}

/* Responsive design */
@media (max-width: 768px) {
    .stamply-universal-footer {
        font-size: 10px !important;
        padding: 10px 12px !important;
    }
    
    .stamply-universal-footer div[style*="display: flex"] {
        flex-direction: column !important;
        align-items: flex-start !important;
        gap: 2px;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .stamply-universal-footer {
        border-top: 2px solid #000 !important;
        background: #fff !important;
        color: #000 !important;
    }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
    .stamply-universal-footer {
        animation: none !important;
        transition: none !important;
    }
    
    .stamply-universal-footer:hover {
        transform: none !important;
    }
}

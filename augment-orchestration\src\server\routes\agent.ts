import { Router } from 'express';
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';
import { asyncHand<PERSON>, AppError } from '../middleware/errorHandler';
import { AuthenticatedRequest } from '../middleware/auth';
import { AGENT_ROLES } from '@/shared/types';
import { EventBus, EVENT_TYPES } from '../services/EventBus';
import { logger } from '../utils/logger';
import { AgentAssignmentService } from '../services/AgentAssignmentService';

const router = Router();
const prisma = new PrismaClient();
const eventBus = new EventBus();
const assignmentService = new AgentAssignmentService();

// Validation schemas
const createAgentSchema = z.object({
  agentId: z.string().min(1, 'Agent ID is required').max(50, 'Agent ID too long'),
  name: z.string().min(1, 'Name is required').max(100, 'Name too long'),
  description: z.string().optional(),
  vendor: z.string().min(1, 'Vendor is required').max(50, 'Vendor too long'),
  capabilities: z.array(z.string()).min(1, 'At least one capability required'),
  roles: z.array(z.string()).min(1, 'At least one role required'),
  subOrchestratorId: z.string().optional(),
  metadata: z.record(z.any()).optional(),
});

const updateAgentSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long').optional(),
  description: z.string().optional(),
  capabilities: z.array(z.string()).optional(),
  roles: z.array(z.string()).optional(),
  isActive: z.boolean().optional(),
  subOrchestratorId: z.string().nullable().optional(),
  metadata: z.record(z.any()).optional(),
});

const assignRoleSchema = z.object({
  agentIds: z.array(z.string()).min(1, 'At least one agent required'),
  role: z.string().min(1, 'Role is required'),
  priority: z.number().min(1).max(10).default(5),
});

// Get all agents with filtering and pagination
router.get('/', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 20;
  const search = req.query.search as string;
  const vendor = req.query.vendor as string;
  const role = req.query.role as string;
  const isActive = req.query.isActive === 'true' ? true : req.query.isActive === 'false' ? false : undefined;

  const skip = (page - 1) * limit;
  const where: any = {};

  if (search) {
    where.OR = [
      { name: { contains: search, mode: 'insensitive' } },
      { agentId: { contains: search, mode: 'insensitive' } },
      { description: { contains: search, mode: 'insensitive' } },
    ];
  }

  if (vendor) {
    where.vendor = { contains: vendor, mode: 'insensitive' };
  }

  if (role) {
    where.roles = { has: role };
  }

  if (isActive !== undefined) {
    where.isActive = isActive;
  }

  const [agents, total] = await Promise.all([
    prisma.agent.findMany({
      where,
      skip,
      take: limit,
      include: {
        subOrchestrator: {
          select: {
            id: true,
            name: true,
            domain: true,
          },
        },
        _count: {
          select: {
            workflowExecutions: true,
            evolutionVariants: true,
          },
        },
      },
      orderBy: [
        { fitnessScore: 'desc' },
        { name: 'asc' },
      ],
    }),
    prisma.agent.count({ where }),
  ]);

  res.json({
    success: true,
    data: agents,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
    },
  });
}));

// Get single agent with detailed information
router.get('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;

  const agent = await prisma.agent.findUnique({
    where: { id },
    include: {
      subOrchestrator: {
        select: {
          id: true,
          name: true,
          domain: true,
          metaOrchestrator: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      },
      workflowExecutions: {
        select: {
          id: true,
          status: true,
          startedAt: true,
          endedAt: true,
          template: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: {
          startedAt: 'desc',
        },
        take: 10,
      },
      evolutionVariants: {
        select: {
          id: true,
          generation: true,
          fitnessScore: true,
          isPromoted: true,
          createdAt: true,
        },
        orderBy: {
          generation: 'desc',
        },
        take: 5,
      },
      tunnelsFrom: {
        select: {
          id: true,
          name: true,
          toAgent: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      },
      tunnelsTo: {
        select: {
          id: true,
          name: true,
          fromAgent: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      },
    },
  });

  if (!agent) {
    throw new AppError('Agent not found', 404);
  }

  res.json({
    success: true,
    data: agent,
  });
}));

// Get agent roles directory
router.get('/roles', asyncHandler(async (req: AuthenticatedRequest, res) => {
  res.json({
    success: true,
    data: AGENT_ROLES,
  });
}));

// Get agent registry with role capabilities
router.get('/registry', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const agents = await prisma.agent.findMany({
    where: { isActive: true },
    select: {
      id: true,
      agentId: true,
      name: true,
      vendor: true,
      capabilities: true,
      roles: true,
      fitnessScore: true,
    },
  });

  // Build role capabilities mapping
  const roleCapabilities: Record<string, string[]> = {};
  AGENT_ROLES.forEach(role => {
    roleCapabilities[role.id] = agents
      .filter(agent => agent.roles.includes(role.id))
      .map(agent => agent.agentId);
  });

  res.json({
    success: true,
    data: {
      agents,
      roleCapabilities,
      roles: AGENT_ROLES,
    },
  });
}));

// Create new agent
router.post('/', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const validatedData = createAgentSchema.parse(req.body);

  // Check if agent ID already exists
  const existingAgent = await prisma.agent.findUnique({
    where: { agentId: validatedData.agentId },
  });

  if (existingAgent) {
    throw new AppError('Agent ID already exists', 400);
  }

  // Validate sub-orchestrator exists if provided
  if (validatedData.subOrchestratorId) {
    const subOrchestrator = await prisma.subOrchestrator.findUnique({
      where: { id: validatedData.subOrchestratorId },
    });

    if (!subOrchestrator) {
      throw new AppError('Sub-orchestrator not found', 404);
    }
  }

  // Validate roles exist
  const validRoles = AGENT_ROLES.map(r => r.id);
  const invalidRoles = validatedData.roles.filter(role => !validRoles.includes(role));
  if (invalidRoles.length > 0) {
    throw new AppError(`Invalid roles: ${invalidRoles.join(', ')}`, 400);
  }

  const agent = await prisma.agent.create({
    data: {
      ...validatedData,
      fitnessScore: 50, // Default starting fitness
      isActive: true,
      createdBy: req.user!.id,
    },
    include: {
      subOrchestrator: {
        select: {
          id: true,
          name: true,
          domain: true,
        },
      },
    },
  });

  // Emit event
  eventBus.emit(EVENT_TYPES.AGENT_CREATED, {
    agentId: agent.id,
    userId: req.user!.id,
    data: agent,
  });

  logger.info(`Agent created: ${agent.name} (${agent.agentId})`, {
    agentId: agent.id,
    userId: req.user!.id,
  });

  res.status(201).json({
    success: true,
    data: agent,
  });
}));

// Update agent
router.put('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;
  const validatedData = updateAgentSchema.parse(req.body);

  const existingAgent = await prisma.agent.findUnique({
    where: { id },
  });

  if (!existingAgent) {
    throw new AppError('Agent not found', 404);
  }

  // Validate sub-orchestrator exists if provided
  if (validatedData.subOrchestratorId) {
    const subOrchestrator = await prisma.subOrchestrator.findUnique({
      where: { id: validatedData.subOrchestratorId },
    });

    if (!subOrchestrator) {
      throw new AppError('Sub-orchestrator not found', 404);
    }
  }

  // Validate roles exist if provided
  if (validatedData.roles) {
    const validRoles = AGENT_ROLES.map(r => r.id);
    const invalidRoles = validatedData.roles.filter(role => !validRoles.includes(role));
    if (invalidRoles.length > 0) {
      throw new AppError(`Invalid roles: ${invalidRoles.join(', ')}`, 400);
    }
  }

  const agent = await prisma.agent.update({
    where: { id },
    data: {
      ...validatedData,
      updatedAt: new Date(),
    },
    include: {
      subOrchestrator: {
        select: {
          id: true,
          name: true,
          domain: true,
        },
      },
    },
  });

  // Emit event
  eventBus.emit(EVENT_TYPES.AGENT_UPDATED, {
    agentId: agent.id,
    userId: req.user!.id,
    data: agent,
  });

  logger.info(`Agent updated: ${agent.name} (${agent.agentId})`, {
    agentId: agent.id,
    userId: req.user!.id,
  });

  res.json({
    success: true,
    data: agent,
  });
}));

// Delete agent
router.delete('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;

  const agent = await prisma.agent.findUnique({
    where: { id },
    include: {
      _count: {
        select: {
          workflowExecutions: true,
          tunnelsFrom: true,
          tunnelsTo: true,
        },
      },
    },
  });

  if (!agent) {
    throw new AppError('Agent not found', 404);
  }

  // Check if agent has active dependencies
  if (agent._count.workflowExecutions > 0 || agent._count.tunnelsFrom > 0 || agent._count.tunnelsTo > 0) {
    throw new AppError('Cannot delete agent with active workflows or tunnels', 400);
  }

  await prisma.agent.delete({
    where: { id },
  });

  // Emit event
  eventBus.emit(EVENT_TYPES.AGENT_DELETED, {
    agentId: id,
    userId: req.user!.id,
    data: { name: agent.name, agentId: agent.agentId },
  });

  logger.info(`Agent deleted: ${agent.name} (${agent.agentId})`, {
    agentId: id,
    userId: req.user!.id,
  });

  res.json({
    success: true,
    message: 'Agent deleted successfully',
  });
}));

// Assign roles to multiple agents
router.post('/assign-roles', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const validatedData = assignRoleSchema.parse(req.body);

  // Validate role exists
  const validRoles = AGENT_ROLES.map(r => r.id);
  if (!validRoles.includes(validatedData.role)) {
    throw new AppError('Invalid role', 400);
  }

  // Validate agents exist
  const agents = await prisma.agent.findMany({
    where: {
      id: { in: validatedData.agentIds },
    },
  });

  if (agents.length !== validatedData.agentIds.length) {
    throw new AppError('One or more agents not found', 404);
  }

  // Update agents with new role
  const updatedAgents = await Promise.all(
    agents.map(async (agent) => {
      const updatedRoles = Array.from(new Set([...agent.roles, validatedData.role]));

      return prisma.agent.update({
        where: { id: agent.id },
        data: {
          roles: updatedRoles,
          updatedAt: new Date(),
        },
        include: {
          subOrchestrator: {
            select: {
              id: true,
              name: true,
              domain: true,
            },
          },
        },
      });
    })
  );

  // Emit events
  updatedAgents.forEach(agent => {
    eventBus.emit(EVENT_TYPES.AGENT_ROLE_ASSIGNED, {
      agentId: agent.id,
      userId: req.user!.id,
      data: { role: validatedData.role, agent },
    });
  });

  logger.info(`Role ${validatedData.role} assigned to ${updatedAgents.length} agents`, {
    role: validatedData.role,
    agentIds: validatedData.agentIds,
    userId: req.user!.id,
  });

  res.json({
    success: true,
    data: updatedAgents,
    message: `Role assigned to ${updatedAgents.length} agents`,
  });
}));

// Intelligent agent assignment
router.post('/assign', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const assignmentCriteria = req.body;

  const result = await assignmentService.assignAgents(assignmentCriteria, req.user!.id);

  res.json({
    success: true,
    data: result,
  });
}));

// Get role-based recommendations
router.get('/recommendations/:roleId', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { roleId } = req.params;

  const recommendations = await assignmentService.getRecommendations(roleId);

  res.json({
    success: true,
    data: recommendations,
  });
}));

// Get agent capabilities analysis
router.get('/capabilities-analysis', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const agents = await prisma.agent.findMany({
    where: { isActive: true },
    select: {
      id: true,
      name: true,
      vendor: true,
      capabilities: true,
      roles: true,
      fitnessScore: true,
    },
  });

  // Analyze capability coverage
  const allCapabilities = new Set<string>();
  const capabilityAgents: Record<string, string[]> = {};
  const roleCapabilities: Record<string, string[]> = {};

  agents.forEach(agent => {
    agent.capabilities.forEach(cap => {
      allCapabilities.add(cap);
      if (!capabilityAgents[cap]) {
        capabilityAgents[cap] = [];
      }
      capabilityAgents[cap].push(agent.name);
    });
  });

  // Map role capabilities
  AGENT_ROLES.forEach(role => {
    roleCapabilities[role.id] = [
      ...role.requiredCapabilities,
      ...role.optionalCapabilities,
    ];
  });

  // Find capability gaps
  const capabilityGaps = AGENT_ROLES.flatMap(role =>
    role.requiredCapabilities.filter(cap =>
      !Array.from(allCapabilities).includes(cap)
    )
  );

  res.json({
    success: true,
    data: {
      totalAgents: agents.length,
      totalCapabilities: allCapabilities.size,
      capabilityAgents,
      roleCapabilities,
      capabilityGaps: Array.from(new Set(capabilityGaps)),
      coverageAnalysis: {
        fullySupported: AGENT_ROLES.filter(role =>
          role.requiredCapabilities.every(cap =>
            Array.from(allCapabilities).includes(cap)
          )
        ).map(r => r.id),
        partiallySupportedRoles: AGENT_ROLES.filter(role =>
          role.requiredCapabilities.some(cap =>
            Array.from(allCapabilities).includes(cap)
          ) && !role.requiredCapabilities.every(cap =>
            Array.from(allCapabilities).includes(cap)
          )
        ).map(r => r.id),
        unsupportedRoles: AGENT_ROLES.filter(role =>
          !role.requiredCapabilities.some(cap =>
            Array.from(allCapabilities).includes(cap)
          )
        ).map(r => r.id),
      },
    },
  });
}));

export { router as agentRoutes };

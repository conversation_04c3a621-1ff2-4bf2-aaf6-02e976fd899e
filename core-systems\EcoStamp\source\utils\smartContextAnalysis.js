/**
 * Smart Context Analysis System
 * Analyzes conversation complexity and context for accurate impact calculations
 */

// Query complexity indicators
const COMPLEXITY_INDICATORS = {
  high: [
    'analyze', 'explain in detail', 'comprehensive', 'step by step',
    'write a', 'create a', 'generate', 'code', 'algorithm',
    'research', 'compare', 'evaluate', 'critique', 'review',
    'summarize', 'translate', 'convert', 'transform'
  ],
  medium: [
    'how', 'what', 'why', 'when', 'where', 'describe',
    'list', 'give me', 'tell me', 'show me', 'help'
  ],
  low: [
    'yes', 'no', 'maybe', 'ok', 'thanks', 'hello',
    'hi', 'bye', 'good', 'bad', 'true', 'false'
  ]
};

// Content type indicators
const CONTENT_TYPES = {
  code: ['function', 'class', 'import', 'def', 'var', 'const', 'let', '```', 'python', 'javascript', 'html', 'css'],
  creative: ['story', 'poem', 'creative', 'imagine', 'fiction', 'character', 'plot', 'narrative'],
  analytical: ['data', 'statistics', 'analysis', 'research', 'study', 'report', 'findings', 'conclusion'],
  educational: ['learn', 'teach', 'explain', 'understand', 'concept', 'theory', 'principle', 'definition'],
  conversational: ['chat', 'talk', 'discuss', 'opinion', 'think', 'feel', 'believe', 'personal']
};

// Multimodal indicators
const MULTIMODAL_INDICATORS = [
  'image', 'picture', 'photo', 'visual', 'diagram', 'chart', 'graph',
  'video', 'audio', 'file', 'document', 'pdf', 'upload', 'attachment'
];

/**
 * Analyze query context and complexity
 * @param {string} input - User input text
 * @param {string} output - AI response text
 * @param {object} pageContext - Page context information
 * @returns {object} Context analysis results
 */
export function analyzeContext(input, output, pageContext = {}) {
  const inputAnalysis = analyzeText(input);
  const outputAnalysis = analyzeText(output);
  
  const context = {
    input: inputAnalysis,
    output: outputAnalysis,
    conversation: analyzeConversation(input, output),
    complexity: determineComplexity(inputAnalysis, outputAnalysis),
    contentType: determineContentType(input, output),
    multimodal: detectMultimodal(input, output, pageContext),
    efficiency: calculateEfficiency(inputAnalysis, outputAnalysis),
    metadata: {
      timestamp: new Date().toISOString(),
      platform: pageContext.platform || 'unknown',
      url: pageContext.url || ''
    }
  };

  return context;
}

/**
 * Analyze individual text for characteristics
 * @param {string} text - Text to analyze
 * @returns {object} Text analysis results
 */
function analyzeText(text) {
  if (!text || typeof text !== 'string') {
    return { length: 0, words: 0, sentences: 0, complexity: 'low' };
  }

  const words = text.trim().split(/\s+/).filter(word => word.length > 0);
  const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
  const characters = text.length;
  
  // Calculate readability metrics
  const avgWordsPerSentence = sentences.length > 0 ? words.length / sentences.length : 0;
  const avgCharsPerWord = words.length > 0 ? characters / words.length : 0;
  
  // Detect structural complexity
  const hasLists = /[-*•]\s/.test(text) || /\d+\.\s/.test(text);
  const hasCodeBlocks = /```/.test(text) || /`[^`]+`/.test(text);
  const hasComplexPunctuation = /[;:—–]/.test(text);
  const hasQuestions = /\?/.test(text);
  const hasExclamations = /!/.test(text);
  
  // Calculate complexity score
  let complexityScore = 0;
  if (avgWordsPerSentence > 20) complexityScore += 2;
  else if (avgWordsPerSentence > 15) complexityScore += 1;
  
  if (avgCharsPerWord > 6) complexityScore += 1;
  if (hasLists) complexityScore += 1;
  if (hasCodeBlocks) complexityScore += 2;
  if (hasComplexPunctuation) complexityScore += 1;
  if (words.length > 100) complexityScore += 1;
  
  const complexity = complexityScore >= 5 ? 'very-high' :
                    complexityScore >= 3 ? 'high' :
                    complexityScore >= 1 ? 'medium' : 'low';

  return {
    length: characters,
    words: words.length,
    sentences: sentences.length,
    avgWordsPerSentence,
    avgCharsPerWord,
    complexity,
    complexityScore,
    features: {
      hasLists,
      hasCodeBlocks,
      hasComplexPunctuation,
      hasQuestions,
      hasExclamations
    },
    readability: calculateReadability(avgWordsPerSentence, avgCharsPerWord)
  };
}

/**
 * Calculate readability score
 * @param {number} avgWordsPerSentence - Average words per sentence
 * @param {number} avgCharsPerWord - Average characters per word
 * @returns {object} Readability information
 */
function calculateReadability(avgWordsPerSentence, avgCharsPerWord) {
  // Simplified Flesch Reading Ease approximation
  const score = 206.835 - (1.015 * avgWordsPerSentence) - (84.6 * (avgCharsPerWord / 4.7));
  
  let level;
  if (score >= 90) level = 'very-easy';
  else if (score >= 80) level = 'easy';
  else if (score >= 70) level = 'fairly-easy';
  else if (score >= 60) level = 'standard';
  else if (score >= 50) level = 'fairly-difficult';
  else if (score >= 30) level = 'difficult';
  else level = 'very-difficult';

  return { score: Math.max(0, Math.min(100, score)), level };
}

/**
 * Analyze conversation dynamics
 * @param {string} input - User input
 * @param {string} output - AI output
 * @returns {object} Conversation analysis
 */
function analyzeConversation(input, output) {
  const inputWords = input.trim().split(/\s+/).length;
  const outputWords = output.trim().split(/\s+/).length;
  
  const expansionRatio = inputWords > 0 ? outputWords / inputWords : 0;
  const totalWords = inputWords + outputWords;
  
  // Determine conversation type
  let conversationType;
  if (expansionRatio > 5) conversationType = 'explanatory';
  else if (expansionRatio > 2) conversationType = 'detailed';
  else if (expansionRatio > 1) conversationType = 'balanced';
  else conversationType = 'concise';

  return {
    inputWords,
    outputWords,
    totalWords,
    expansionRatio,
    conversationType,
    efficiency: calculateConversationEfficiency(inputWords, outputWords)
  };
}

/**
 * Calculate conversation efficiency
 * @param {number} inputWords - Input word count
 * @param {number} outputWords - Output word count
 * @returns {object} Efficiency metrics
 */
function calculateConversationEfficiency(inputWords, outputWords) {
  const totalWords = inputWords + outputWords;
  const inputRatio = totalWords > 0 ? inputWords / totalWords : 0;
  const outputRatio = totalWords > 0 ? outputWords / totalWords : 0;
  
  // Efficiency score (balanced conversations are more efficient)
  const balanceScore = 1 - Math.abs(0.3 - inputRatio); // Optimal input ratio ~30%
  const efficiencyScore = balanceScore * 100;
  
  let rating;
  if (efficiencyScore >= 80) rating = 'excellent';
  else if (efficiencyScore >= 60) rating = 'good';
  else if (efficiencyScore >= 40) rating = 'average';
  else rating = 'poor';

  return {
    score: Math.round(efficiencyScore),
    rating,
    inputRatio,
    outputRatio,
    balance: balanceScore
  };
}

/**
 * Determine overall complexity
 * @param {object} inputAnalysis - Input analysis
 * @param {object} outputAnalysis - Output analysis
 * @returns {string} Overall complexity level
 */
function determineComplexity(inputAnalysis, outputAnalysis) {
  const inputComplexity = inputAnalysis.complexityScore || 0;
  const outputComplexity = outputAnalysis.complexityScore || 0;
  const avgComplexity = (inputComplexity + outputComplexity) / 2;
  
  // Check for complexity indicators in text
  const inputText = inputAnalysis.text || '';
  const outputText = outputAnalysis.text || '';
  const combinedText = (inputText + ' ' + outputText).toLowerCase();
  
  let indicatorBonus = 0;
  for (const indicator of COMPLEXITY_INDICATORS.high) {
    if (combinedText.includes(indicator)) indicatorBonus += 1;
  }
  
  const finalScore = avgComplexity + indicatorBonus;
  
  if (finalScore >= 6) return 'very-high';
  if (finalScore >= 4) return 'high';
  if (finalScore >= 2) return 'medium';
  return 'low';
}

/**
 * Determine content type
 * @param {string} input - User input
 * @param {string} output - AI output
 * @returns {array} Array of detected content types
 */
function determineContentType(input, output) {
  const combinedText = (input + ' ' + output).toLowerCase();
  const detectedTypes = [];
  
  for (const [type, indicators] of Object.entries(CONTENT_TYPES)) {
    let matches = 0;
    for (const indicator of indicators) {
      if (combinedText.includes(indicator)) matches++;
    }
    
    if (matches >= 2) {
      detectedTypes.push({
        type,
        confidence: Math.min(100, (matches / indicators.length) * 100),
        matches
      });
    }
  }
  
  // Sort by confidence
  detectedTypes.sort((a, b) => b.confidence - a.confidence);
  
  return detectedTypes.length > 0 ? detectedTypes : [{ type: 'general', confidence: 50, matches: 0 }];
}

/**
 * Detect multimodal content
 * @param {string} input - User input
 * @param {string} output - AI output
 * @param {object} pageContext - Page context
 * @returns {object} Multimodal detection results
 */
function detectMultimodal(input, output, pageContext) {
  const combinedText = (input + ' ' + output).toLowerCase();
  const indicators = [];
  
  for (const indicator of MULTIMODAL_INDICATORS) {
    if (combinedText.includes(indicator)) {
      indicators.push(indicator);
    }
  }
  
  // Check page context for multimodal elements
  const hasFileUploads = pageContext.hasFileUploads || false;
  const hasImages = pageContext.hasImages || false;
  
  return {
    detected: indicators.length > 0 || hasFileUploads || hasImages,
    indicators,
    confidence: Math.min(100, indicators.length * 25),
    types: {
      hasFileUploads,
      hasImages,
      hasTextIndicators: indicators.length > 0
    }
  };
}

/**
 * Calculate overall efficiency score
 * @param {object} inputAnalysis - Input analysis
 * @param {object} outputAnalysis - Output analysis
 * @returns {object} Efficiency assessment
 */
function calculateEfficiency(inputAnalysis, outputAnalysis) {
  const conversationEff = outputAnalysis.efficiency?.score || 50;
  const complexityBalance = inputAnalysis.complexity === outputAnalysis.complexity ? 20 : 0;
  const lengthBalance = Math.abs(inputAnalysis.words - outputAnalysis.words) < 100 ? 15 : 0;
  
  const totalScore = (conversationEff + complexityBalance + lengthBalance) / 1.35;
  
  let rating;
  if (totalScore >= 80) rating = 'excellent';
  else if (totalScore >= 65) rating = 'good';
  else if (totalScore >= 50) rating = 'average';
  else if (totalScore >= 35) rating = 'below-average';
  else rating = 'poor';

  return {
    score: Math.round(totalScore),
    rating,
    factors: {
      conversation: conversationEff,
      complexity: complexityBalance,
      length: lengthBalance
    }
  };
}

#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';

/**
 * EcoStamp Security Report Generator
 * Comprehensive security scanning and reporting for solo developers
 */

class SecurityReporter {
  constructor() {
    this.reportDir = './security-reports';
    this.timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    this.results = {
      timestamp: new Date().toISOString(),
      summary: {
        vulnerabilities: { critical: 0, high: 0, medium: 0, low: 0 },
        licenses: { compliant: 0, nonCompliant: 0, unknown: 0 },
        dependencies: { total: 0, outdated: 0 },
        codeIssues: { security: 0, quality: 0 }
      },
      details: {}
    };
  }

  async init() {
    // Create reports directory
    if (!fs.existsSync(this.reportDir)) {
      fs.mkdirSync(this.reportDir, { recursive: true });
    }
    
    console.log('🔒 EcoStamp Security Scanner Starting...\n');
    console.log('=' .repeat(60));
  }

  async runNpmAudit() {
    console.log('📋 Running NPM Audit...');
    try {
      const auditResult = execSync('npm audit --json', { encoding: 'utf8' });
      const audit = JSON.parse(auditResult);
      
      this.results.details.npmAudit = audit;
      this.results.summary.vulnerabilities = {
        critical: audit.metadata?.vulnerabilities?.critical || 0,
        high: audit.metadata?.vulnerabilities?.high || 0,
        medium: audit.metadata?.vulnerabilities?.moderate || 0,
        low: audit.metadata?.vulnerabilities?.low || 0
      };
      
      console.log(`   ✅ Found ${audit.metadata?.vulnerabilities?.total || 0} vulnerabilities`);
    } catch (error) {
      console.log('   ⚠️  NPM Audit completed with warnings');
      this.results.details.npmAudit = { error: error.message };
    }
  }

  async runLicenseCheck() {
    console.log('📜 Checking Licenses...');
    try {
      const licenseResult = execSync('license-checker --json', { encoding: 'utf8' });
      const licenses = JSON.parse(licenseResult);
      
      this.results.details.licenses = licenses;
      
      // Analyze license compliance
      const problematicLicenses = ['GPL-3.0', 'AGPL-3.0', 'LGPL-3.0'];
      let compliant = 0, nonCompliant = 0, unknown = 0;
      
      Object.values(licenses).forEach(pkg => {
        const license = pkg.licenses;
        if (!license || license === 'UNKNOWN') {
          unknown++;
        } else if (problematicLicenses.some(prob => license.includes(prob))) {
          nonCompliant++;
        } else {
          compliant++;
        }
      });
      
      this.results.summary.licenses = { compliant, nonCompliant, unknown };
      console.log(`   ✅ Analyzed ${Object.keys(licenses).length} package licenses`);
    } catch (error) {
      console.log('   ⚠️  License check failed:', error.message);
      this.results.details.licenses = { error: error.message };
    }
  }

  async generateSBOM() {
    console.log('📦 Generating SBOM...');
    try {
      execSync('cyclonedx-npm --output-file ./security-reports/sbom.json', { encoding: 'utf8' });
      execSync('cyclonedx-npm --output-format xml --output-file ./security-reports/sbom.xml', { encoding: 'utf8' });
      
      const sbom = JSON.parse(fs.readFileSync('./security-reports/sbom.json', 'utf8'));
      this.results.details.sbom = {
        format: 'CycloneDX',
        components: sbom.components?.length || 0,
        generated: true
      };
      
      console.log(`   ✅ SBOM generated with ${sbom.components?.length || 0} components`);
    } catch (error) {
      console.log('   ⚠️  SBOM generation failed:', error.message);
      this.results.details.sbom = { error: error.message };
    }
  }

  async runESLintSecurity() {
    console.log('🔍 Running Security Linting...');
    try {
      const eslintResult = execSync('eslint . --ext .js,.ts --config .eslintrc.security.js --format json', { encoding: 'utf8' });
      const eslint = JSON.parse(eslintResult);
      
      let securityIssues = 0, qualityIssues = 0;
      eslint.forEach(file => {
        file.messages.forEach(msg => {
          if (msg.ruleId && msg.ruleId.startsWith('security/')) {
            securityIssues++;
          } else {
            qualityIssues++;
          }
        });
      });
      
      this.results.details.eslint = eslint;
      this.results.summary.codeIssues = { security: securityIssues, quality: qualityIssues };
      
      console.log(`   ✅ Found ${securityIssues} security issues, ${qualityIssues} quality issues`);
    } catch (error) {
      console.log('   ⚠️  ESLint security scan completed with issues');
      // ESLint returns non-zero exit code when issues found, parse the output anyway
      try {
        const eslint = JSON.parse(error.stdout || '[]');
        this.results.details.eslint = eslint;
      } catch (parseError) {
        this.results.details.eslint = { error: error.message };
      }
    }
  }

  async generateReport() {
    console.log('\n📊 Generating Security Report...');
    
    const reportFile = path.join(this.reportDir, `security-report-${this.timestamp}.json`);
    const htmlReportFile = path.join(this.reportDir, `security-report-${this.timestamp}.html`);
    
    // Save JSON report
    fs.writeFileSync(reportFile, JSON.stringify(this.results, null, 2));
    
    // Generate HTML report
    const htmlReport = this.generateHTMLReport();
    fs.writeFileSync(htmlReportFile, htmlReport);
    
    console.log(`   ✅ Reports saved:`);
    console.log(`      📄 JSON: ${reportFile}`);
    console.log(`      🌐 HTML: ${htmlReportFile}`);
    
    return { jsonReport: reportFile, htmlReport: htmlReportFile };
  }

  generateHTMLReport() {
    const { summary } = this.results;
    const totalVulns = Object.values(summary.vulnerabilities).reduce((a, b) => a + b, 0);
    const totalLicenses = Object.values(summary.licenses).reduce((a, b) => a + b, 0);
    
    return `<!DOCTYPE html>
<html>
<head>
    <title>EcoStamp Security Report - ${this.timestamp}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; border-bottom: 2px solid #2c5aa0; padding-bottom: 20px; margin-bottom: 30px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .card { background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #2c5aa0; }
        .critical { border-left-color: #dc3545; }
        .high { border-left-color: #fd7e14; }
        .medium { border-left-color: #ffc107; }
        .low { border-left-color: #28a745; }
        .metric { font-size: 2em; font-weight: bold; color: #2c5aa0; }
        .label { color: #6c757d; font-size: 0.9em; }
        .status { padding: 4px 8px; border-radius: 4px; font-size: 0.8em; font-weight: bold; }
        .success { background: #d4edda; color: #155724; }
        .warning { background: #fff3cd; color: #856404; }
        .danger { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔒 EcoStamp Security Report</h1>
            <p>Generated: ${new Date(this.results.timestamp).toLocaleString()}</p>
        </div>
        
        <div class="summary">
            <div class="card ${totalVulns === 0 ? 'low' : totalVulns > 10 ? 'critical' : 'medium'}">
                <div class="metric">${totalVulns}</div>
                <div class="label">Total Vulnerabilities</div>
                <div style="margin-top: 10px;">
                    <span class="status danger">Critical: ${summary.vulnerabilities.critical}</span>
                    <span class="status warning">High: ${summary.vulnerabilities.high}</span>
                    <span class="status warning">Medium: ${summary.vulnerabilities.medium}</span>
                    <span class="status success">Low: ${summary.vulnerabilities.low}</span>
                </div>
            </div>
            
            <div class="card">
                <div class="metric">${totalLicenses}</div>
                <div class="label">License Analysis</div>
                <div style="margin-top: 10px;">
                    <span class="status success">Compliant: ${summary.licenses.compliant}</span>
                    <span class="status danger">Non-compliant: ${summary.licenses.nonCompliant}</span>
                    <span class="status warning">Unknown: ${summary.licenses.unknown}</span>
                </div>
            </div>
            
            <div class="card">
                <div class="metric">${summary.codeIssues.security + summary.codeIssues.quality}</div>
                <div class="label">Code Issues</div>
                <div style="margin-top: 10px;">
                    <span class="status danger">Security: ${summary.codeIssues.security}</span>
                    <span class="status warning">Quality: ${summary.codeIssues.quality}</span>
                </div>
            </div>
        </div>
        
        <div style="margin-top: 30px; padding: 20px; background: #e9ecef; border-radius: 8px;">
            <h3>📋 Recommendations</h3>
            <ul>
                ${totalVulns > 0 ? '<li>🚨 <strong>Run <code>npm audit fix</code> to automatically fix vulnerabilities</strong></li>' : ''}
                ${summary.licenses.nonCompliant > 0 ? '<li>⚖️ Review non-compliant licenses for legal compliance</li>' : ''}
                ${summary.licenses.unknown > 0 ? '<li>❓ Investigate packages with unknown licenses</li>' : ''}
                ${summary.codeIssues.security > 0 ? '<li>🔒 Fix security-related code issues identified by ESLint</li>' : ''}
                <li>📦 SBOM files generated for compliance and supply chain security</li>
                <li>🔄 Run security scans regularly (weekly recommended)</li>
            </ul>
        </div>
        
        <div style="margin-top: 20px; text-align: center; color: #6c757d; font-size: 0.9em;">
            <p>🌱 EcoStamp Security Scanner - Protecting your code and environment</p>
        </div>
    </div>
</body>
</html>`;
  }

  async run() {
    await this.init();
    
    await this.runNpmAudit();
    await this.runLicenseCheck();
    await this.generateSBOM();
    await this.runESLintSecurity();
    
    const reports = await this.generateReport();
    
    console.log('\n' + '='.repeat(60));
    console.log('🎯 Security Scan Complete!');
    console.log('='.repeat(60));
    
    const { summary } = this.results;
    const totalVulns = Object.values(summary.vulnerabilities).reduce((a, b) => a + b, 0);
    
    if (totalVulns === 0) {
      console.log('✅ No vulnerabilities found!');
    } else {
      console.log(`⚠️  Found ${totalVulns} vulnerabilities - run 'npm audit fix' to resolve`);
    }
    
    console.log(`📊 View detailed report: ${reports.htmlReport}`);
    
    return this.results;
  }
}

// Run the security scanner
const scanner = new SecurityReporter();
scanner.run().catch(console.error);

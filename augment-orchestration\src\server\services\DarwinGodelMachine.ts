import { PrismaClient, Agent, EvolutionVariant } from '@prisma/client';
import { EventBus, EVENT_TYPES } from './EventBus';
import { logger } from '../utils/logger';

export interface PerformanceMetrics {
  successRate: number;
  averageResponseTime: number;
  taskCompletionRate: number;
  errorRate: number;
  resourceUtilization: number;
  collaborationScore: number;
  adaptabilityScore: number;
  innovationScore: number;
}

export interface EvolutionParameters {
  populationSize: number;
  mutationRate: number;
  crossoverRate: number;
  selectionPressure: number;
  elitismRate: number;
  generationLimit: number;
  fitnessThreshold: number;
  diversityWeight: number;
}

export interface MutationStrategy {
  type: 'PARAMETER_ADJUSTMENT' | 'CAPABILITY_ENHANCEMENT' | 'ROLE_SPECIALIZATION' | 'HYBRID_CREATION';
  intensity: number; // 0.1 to 1.0
  targetAspects: string[];
  constraints: Record<string, any>;
}

export interface FitnessScore {
  overall: number;
  performance: number;
  adaptability: number;
  collaboration: number;
  innovation: number;
  efficiency: number;
  reliability: number;
}

export class DarwinGodelMachine {
  private prisma: PrismaClient;
  private eventBus: EventBus;
  private evolutionParameters: EvolutionParameters;
  private isEvolutionRunning: boolean = false;
  private currentGeneration: number = 0;
  private performanceHistory: Map<string, PerformanceMetrics[]> = new Map();

  constructor() {
    this.prisma = new PrismaClient();
    this.eventBus = new EventBus();
    
    // Default evolution parameters
    this.evolutionParameters = {
      populationSize: 50,
      mutationRate: 0.1,
      crossoverRate: 0.7,
      selectionPressure: 0.8,
      elitismRate: 0.1,
      generationLimit: 100,
      fitnessThreshold: 0.95,
      diversityWeight: 0.2,
    };

    // Start monitoring agents
    this.startPerformanceMonitoring();
  }

  /**
   * Start continuous performance monitoring of all agents
   */
  private startPerformanceMonitoring(): void {
    setInterval(async () => {
      await this.collectPerformanceMetrics();
    }, 300000); // Every 5 minutes

    logger.info('Darwin Gödel Machine performance monitoring started');
  }

  /**
   * Collect performance metrics for all active agents
   */
  private async collectPerformanceMetrics(): Promise<void> {
    try {
      const agents = await this.prisma.agent.findMany({
        where: { isActive: true },
        include: {
          _count: {
            select: {
              fromTunnels: true,
              toTunnels: true,
            },
          },
        },
      });

      for (const agent of agents) {
        const metrics = await this.calculateAgentMetrics(agent);
        
        // Store metrics history
        const history = this.performanceHistory.get(agent.id) || [];
        history.push(metrics);
        
        // Keep only last 100 measurements
        if (history.length > 100) {
          history.shift();
        }
        
        this.performanceHistory.set(agent.id, history);

        // Update agent performance score
        const fitnessScore = this.calculateFitnessScore(metrics);
        await this.prisma.agent.update({
          where: { id: agent.id },
          data: {
            performanceScore: fitnessScore.overall,
            metadata: {
              ...agent.metadata,
              lastMetrics: metrics,
              fitnessScore,
              lastEvaluated: new Date(),
            },
          },
        });
      }

      logger.debug(`Performance metrics collected for ${agents.length} agents`);
    } catch (error) {
      logger.error('Failed to collect performance metrics', { error });
    }
  }

  /**
   * Calculate comprehensive performance metrics for an agent
   */
  private async calculateAgentMetrics(agent: Agent): Promise<PerformanceMetrics> {
    // In a real implementation, this would analyze actual agent performance data
    // For now, we'll simulate realistic metrics based on agent characteristics
    
    const basePerformance = Math.random() * 0.4 + 0.6; // 0.6 to 1.0
    const roleMultiplier = this.getRolePerformanceMultiplier(agent.role);
    const experienceBonus = Math.min(agent.experienceLevel * 0.1, 0.3);
    
    return {
      successRate: Math.min(basePerformance * roleMultiplier + experienceBonus, 1.0),
      averageResponseTime: Math.random() * 2000 + 500, // 500-2500ms
      taskCompletionRate: Math.min(basePerformance * 0.9 + experienceBonus, 1.0),
      errorRate: Math.max(0.1 - experienceBonus, 0.01),
      resourceUtilization: Math.random() * 0.3 + 0.5, // 0.5 to 0.8
      collaborationScore: Math.random() * 0.4 + 0.6,
      adaptabilityScore: Math.random() * 0.3 + 0.7,
      innovationScore: Math.random() * 0.5 + 0.5,
    };
  }

  /**
   * Get performance multiplier based on agent role
   */
  private getRolePerformanceMultiplier(role: string): number {
    const roleMultipliers: Record<string, number> = {
      'senior-architect': 1.2,
      'full-stack-developer': 1.1,
      'security-auditor': 1.15,
      'devops-engineer': 1.1,
      'data-scientist': 1.05,
      'ml-engineer': 1.05,
      'frontend-specialist': 1.0,
      'backend-specialist': 1.0,
      'test-automation-engineer': 1.0,
      'qa-analyst': 0.95,
    };
    
    return roleMultipliers[role] || 1.0;
  }

  /**
   * Calculate comprehensive fitness score
   */
  private calculateFitnessScore(metrics: PerformanceMetrics): FitnessScore {
    const performance = (metrics.successRate * 0.4 + metrics.taskCompletionRate * 0.4 + (1 - metrics.errorRate) * 0.2);
    const efficiency = (1 - metrics.averageResponseTime / 3000) * 0.7 + metrics.resourceUtilization * 0.3;
    const adaptability = metrics.adaptabilityScore;
    const collaboration = metrics.collaborationScore;
    const innovation = metrics.innovationScore;
    const reliability = (metrics.successRate * 0.6 + (1 - metrics.errorRate) * 0.4);

    const overall = (
      performance * 0.25 +
      efficiency * 0.2 +
      adaptability * 0.15 +
      collaboration * 0.15 +
      innovation * 0.1 +
      reliability * 0.15
    );

    return {
      overall: Math.min(overall, 1.0),
      performance,
      adaptability,
      collaboration,
      innovation,
      efficiency,
      reliability,
    };
  }

  /**
   * Start evolutionary optimization process
   */
  async startEvolution(parameters?: Partial<EvolutionParameters>): Promise<string> {
    if (this.isEvolutionRunning) {
      throw new Error('Evolution process is already running');
    }

    // Update parameters if provided
    if (parameters) {
      this.evolutionParameters = { ...this.evolutionParameters, ...parameters };
    }

    this.isEvolutionRunning = true;
    this.currentGeneration = 0;

    logger.info('Starting Darwin Gödel Machine evolution process', {
      parameters: this.evolutionParameters,
    });

    // Create evolution session
    const session = await this.prisma.evolutionVariant.create({
      data: {
        generation: 0,
        parentId: null,
        mutationType: 'INITIAL_POPULATION',
        fitnessScore: 0,
        parameters: this.evolutionParameters,
        status: 'RUNNING',
        metadata: {
          sessionId: `evolution_${Date.now()}`,
          startedAt: new Date(),
        },
      },
    });

    // Start evolution loop
    this.runEvolutionLoop(session.id);

    return session.id;
  }

  /**
   * Main evolution loop
   */
  private async runEvolutionLoop(sessionId: string): Promise<void> {
    try {
      while (this.isEvolutionRunning && this.currentGeneration < this.evolutionParameters.generationLimit) {
        await this.evolveGeneration(sessionId);
        this.currentGeneration++;

        // Check if we've reached the fitness threshold
        const bestAgent = await this.getBestAgent();
        if (bestAgent && bestAgent.performanceScore >= this.evolutionParameters.fitnessThreshold) {
          logger.info('Evolution completed: fitness threshold reached', {
            generation: this.currentGeneration,
            bestFitness: bestAgent.performanceScore,
          });
          break;
        }

        // Wait between generations
        await new Promise(resolve => setTimeout(resolve, 10000)); // 10 seconds
      }

      await this.completeEvolution(sessionId);
    } catch (error) {
      logger.error('Evolution loop failed', { error, sessionId });
      await this.failEvolution(sessionId, error.message);
    }
  }

  /**
   * Evolve a single generation
   */
  private async evolveGeneration(sessionId: string): Promise<void> {
    logger.info(`Evolving generation ${this.currentGeneration}`);

    // Get current population
    const population = await this.getCurrentPopulation();
    
    // Select parents for reproduction
    const parents = this.selectParents(population);
    
    // Create offspring through mutation and crossover
    const offspring = await this.createOffspring(parents, sessionId);
    
    // Evaluate offspring fitness
    for (const child of offspring) {
      const metrics = await this.calculateAgentMetrics(child);
      const fitnessScore = this.calculateFitnessScore(metrics);
      
      await this.prisma.agent.update({
        where: { id: child.id },
        data: {
          performanceScore: fitnessScore.overall,
          metadata: {
            ...child.metadata,
            generation: this.currentGeneration,
            fitnessScore,
          },
        },
      });
    }

    // Apply selection pressure (remove weakest agents)
    await this.applySelection(population, offspring);

    // Emit generation completed event
    this.eventBus.emit(EVENT_TYPES.EVOLUTION_GENERATION_COMPLETED, {
      sessionId,
      generation: this.currentGeneration,
      populationSize: population.length,
      offspringCount: offspring.length,
    });
  }

  /**
   * Get current agent population
   */
  private async getCurrentPopulation(): Promise<Agent[]> {
    return await this.prisma.agent.findMany({
      where: { isActive: true },
      orderBy: { performanceScore: 'desc' },
      take: this.evolutionParameters.populationSize,
    });
  }

  /**
   * Select parents for reproduction using tournament selection
   */
  private selectParents(population: Agent[]): Agent[] {
    const parents: Agent[] = [];
    const tournamentSize = Math.max(3, Math.floor(population.length * 0.1));

    for (let i = 0; i < Math.floor(population.length * this.evolutionParameters.crossoverRate); i++) {
      // Tournament selection
      const tournament = [];
      for (let j = 0; j < tournamentSize; j++) {
        const randomIndex = Math.floor(Math.random() * population.length);
        tournament.push(population[randomIndex]);
      }
      
      // Select best from tournament
      tournament.sort((a, b) => b.performanceScore - a.performanceScore);
      parents.push(tournament[0]);
    }

    return parents;
  }

  /**
   * Create offspring through mutation and crossover
   */
  private async createOffspring(parents: Agent[], sessionId: string): Promise<Agent[]> {
    const offspring: Agent[] = [];

    for (let i = 0; i < parents.length; i += 2) {
      const parent1 = parents[i];
      const parent2 = parents[i + 1] || parents[0];

      // Create mutation strategy
      const mutationStrategy = this.generateMutationStrategy();

      // Create offspring
      const child = await this.mutateAgent(parent1, parent2, mutationStrategy, sessionId);
      if (child) {
        offspring.push(child);
      }
    }

    return offspring;
  }

  /**
   * Generate mutation strategy
   */
  private generateMutationStrategy(): MutationStrategy {
    const strategies = ['PARAMETER_ADJUSTMENT', 'CAPABILITY_ENHANCEMENT', 'ROLE_SPECIALIZATION', 'HYBRID_CREATION'];
    const type = strategies[Math.floor(Math.random() * strategies.length)] as MutationStrategy['type'];
    
    return {
      type,
      intensity: Math.random() * 0.5 + 0.1, // 0.1 to 0.6
      targetAspects: ['capabilities', 'parameters', 'role'],
      constraints: {
        maxCapabilityIncrease: 2,
        maxParameterChange: 0.3,
        preserveCore: true,
      },
    };
  }

  /**
   * Mutate agent to create offspring
   */
  private async mutateAgent(
    parent1: Agent, 
    parent2: Agent, 
    strategy: MutationStrategy, 
    sessionId: string
  ): Promise<Agent | null> {
    try {
      // Create evolution variant record
      const variant = await this.prisma.evolutionVariant.create({
        data: {
          generation: this.currentGeneration,
          parentId: parent1.id,
          mutationType: strategy.type,
          fitnessScore: 0, // Will be calculated later
          parameters: strategy,
          status: 'CREATED',
          metadata: {
            sessionId,
            parent1Id: parent1.id,
            parent2Id: parent2.id,
            mutationIntensity: strategy.intensity,
          },
        },
      });

      // Create mutated agent
      const mutatedAgent = await this.prisma.agent.create({
        data: {
          agentId: `evolved_${variant.id}`,
          name: `${parent1.name} Gen${this.currentGeneration}`,
          vendor: parent1.vendor,
          model: parent1.model,
          role: this.mutateRole(parent1.role, parent2.role, strategy),
          capabilities: this.mutateCapabilities(parent1.capabilities, parent2.capabilities, strategy),
          isActive: true,
          experienceLevel: Math.max(parent1.experienceLevel, parent2.experienceLevel),
          performanceScore: 0, // Will be calculated
          metadata: {
            evolutionVariantId: variant.id,
            parentIds: [parent1.id, parent2.id],
            generation: this.currentGeneration,
            mutationStrategy: strategy,
          },
        },
      });

      logger.debug('Created evolved agent', {
        agentId: mutatedAgent.id,
        parentIds: [parent1.id, parent2.id],
        strategy: strategy.type,
      });

      return mutatedAgent;
    } catch (error) {
      logger.error('Failed to mutate agent', { error, parent1: parent1.id, parent2: parent2.id });
      return null;
    }
  }

  /**
   * Mutate agent role
   */
  private mutateRole(role1: string, role2: string, strategy: MutationStrategy): string {
    if (strategy.type === 'ROLE_SPECIALIZATION') {
      // Create specialized version of role
      return `${role1}-specialized`;
    } else if (strategy.type === 'HYBRID_CREATION') {
      // Combine roles
      return `${role1}-${role2}-hybrid`;
    }
    
    // Keep primary role
    return Math.random() > 0.5 ? role1 : role2;
  }

  /**
   * Mutate agent capabilities
   */
  private mutateCapabilities(caps1: string[], caps2: string[], strategy: MutationStrategy): string[] {
    const combined = [...new Set([...caps1, ...caps2])];
    
    if (strategy.type === 'CAPABILITY_ENHANCEMENT') {
      // Add new capabilities
      const newCaps = ['advanced-optimization', 'cross-domain-analysis', 'predictive-modeling'];
      const randomCap = newCaps[Math.floor(Math.random() * newCaps.length)];
      combined.push(randomCap);
    }
    
    return combined;
  }

  /**
   * Apply selection pressure to maintain population size
   */
  private async applySelection(population: Agent[], offspring: Agent[]): Promise<void> {
    const allAgents = [...population, ...offspring];
    allAgents.sort((a, b) => b.performanceScore - a.performanceScore);

    // Keep elite agents
    const eliteCount = Math.floor(this.evolutionParameters.populationSize * this.evolutionParameters.elitismRate);
    const survivors = allAgents.slice(0, this.evolutionParameters.populationSize);
    const eliminated = allAgents.slice(this.evolutionParameters.populationSize);

    // Deactivate eliminated agents
    for (const agent of eliminated) {
      await this.prisma.agent.update({
        where: { id: agent.id },
        data: { isActive: false },
      });
    }

    logger.debug('Selection applied', {
      survivors: survivors.length,
      eliminated: eliminated.length,
      eliteCount,
    });
  }

  /**
   * Get best performing agent
   */
  private async getBestAgent(): Promise<Agent | null> {
    return await this.prisma.agent.findFirst({
      where: { isActive: true },
      orderBy: { performanceScore: 'desc' },
    });
  }

  /**
   * Complete evolution process
   */
  private async completeEvolution(sessionId: string): Promise<void> {
    this.isEvolutionRunning = false;

    await this.prisma.evolutionVariant.update({
      where: { id: sessionId },
      data: {
        status: 'COMPLETED',
        metadata: {
          completedAt: new Date(),
          finalGeneration: this.currentGeneration,
        },
      },
    });

    const bestAgent = await this.getBestAgent();
    
    this.eventBus.emit(EVENT_TYPES.EVOLUTION_COMPLETED, {
      sessionId,
      generations: this.currentGeneration,
      bestAgent,
    });

    logger.info('Evolution process completed', {
      sessionId,
      generations: this.currentGeneration,
      bestFitness: bestAgent?.performanceScore,
    });
  }

  /**
   * Fail evolution process
   */
  private async failEvolution(sessionId: string, reason: string): Promise<void> {
    this.isEvolutionRunning = false;

    await this.prisma.evolutionVariant.update({
      where: { id: sessionId },
      data: {
        status: 'FAILED',
        metadata: {
          failedAt: new Date(),
          failureReason: reason,
          generation: this.currentGeneration,
        },
      },
    });

    this.eventBus.emit(EVENT_TYPES.EVOLUTION_FAILED, {
      sessionId,
      reason,
      generation: this.currentGeneration,
    });

    logger.error('Evolution process failed', { sessionId, reason });
  }

  /**
   * Stop evolution process
   */
  async stopEvolution(): Promise<void> {
    if (!this.isEvolutionRunning) {
      throw new Error('No evolution process is currently running');
    }

    this.isEvolutionRunning = false;
    logger.info('Evolution process stopped by user');
  }

  /**
   * Get evolution status
   */
  async getEvolutionStatus(): Promise<any> {
    const activeSession = await this.prisma.evolutionVariant.findFirst({
      where: { status: 'RUNNING' },
      orderBy: { createdAt: 'desc' },
    });

    return {
      isRunning: this.isEvolutionRunning,
      currentGeneration: this.currentGeneration,
      parameters: this.evolutionParameters,
      activeSession,
      populationSize: await this.prisma.agent.count({ where: { isActive: true } }),
    };
  }

  /**
   * Get evolution history
   */
  async getEvolutionHistory(limit: number = 50): Promise<any[]> {
    return await this.prisma.evolutionVariant.findMany({
      take: limit,
      orderBy: { createdAt: 'desc' },
      include: {
        parent: {
          select: {
            id: true,
            name: true,
            performanceScore: true,
          },
        },
      },
    });
  }
}

import { PrismaClient, WorkflowExecution, WorkflowTemplate, Agent } from '@prisma/client';
import { EventBus, EVENT_TYPES } from './EventBus';
import { AgentAssignmentService } from './AgentAssignmentService';
import { logger } from '../utils/logger';

export interface WorkflowStage {
  id: string;
  name: string;
  description: string;
  requiredRoles: string[];
  optionalRoles?: string[];
  requiredCapabilities: string[];
  dependencies: string[]; // Stage IDs that must complete first
  estimatedDuration: number; // in minutes
  maxRetries: number;
  timeoutMinutes: number;
  parallelExecution: boolean;
  metadata?: Record<string, any>;
}

export interface ExecutionContext {
  executionId: string;
  templateId: string;
  currentStage: string;
  assignedAgents: Map<string, string[]>; // stageId -> agentIds
  stageResults: Map<string, any>; // stageId -> result
  sharedContext: Record<string, any>;
  startedAt: Date;
  userId: string;
}

export interface StageExecution {
  stageId: string;
  status: 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED' | 'TIMEOUT';
  assignedAgents: string[];
  startedAt?: Date;
  completedAt?: Date;
  result?: any;
  error?: string;
  retryCount: number;
  logs: string[];
}

export class WorkflowExecutionEngine {
  private prisma: PrismaClient;
  private eventBus: EventBus;
  private assignmentService: AgentAssignmentService;
  private activeExecutions: Map<string, ExecutionContext> = new Map();
  private stageExecutions: Map<string, Map<string, StageExecution>> = new Map(); // executionId -> stageId -> execution

  constructor() {
    this.prisma = new PrismaClient();
    this.eventBus = new EventBus();
    this.assignmentService = new AgentAssignmentService();
    
    // Listen for agent completion events
    this.eventBus.on(EVENT_TYPES.WORKFLOW_STAGE_COMPLETED, this.handleStageCompletion.bind(this));
    this.eventBus.on(EVENT_TYPES.WORKFLOW_STAGE_FAILED, this.handleStageFailure.bind(this));
  }

  /**
   * Start workflow execution with dynamic agent assignment
   */
  async startExecution(
    templateId: string, 
    userId: string, 
    initialContext: Record<string, any> = {}
  ): Promise<string> {
    try {
      logger.info('Starting workflow execution', { templateId, userId });

      // Get workflow template
      const template = await this.prisma.workflowTemplate.findUnique({
        where: { id: templateId },
        include: {
          executions: {
            where: { status: { in: ['PENDING', 'RUNNING'] } },
            select: { id: true },
          },
        },
      });

      if (!template) {
        throw new Error('Workflow template not found');
      }

      if (!template.isActive) {
        throw new Error('Workflow template is not active');
      }

      // Parse workflow stages
      const stages = this.parseWorkflowStages(template.definition);
      
      // Create execution record
      const execution = await this.prisma.workflowExecution.create({
        data: {
          templateId,
          status: 'PENDING',
          priority: 5,
          startedAt: new Date(),
          currentStage: stages[0]?.id || 'unknown',
          context: initialContext,
          createdBy: userId,
        },
      });

      // Initialize execution context
      const executionContext: ExecutionContext = {
        executionId: execution.id,
        templateId,
        currentStage: stages[0]?.id || 'unknown',
        assignedAgents: new Map(),
        stageResults: new Map(),
        sharedContext: { ...initialContext },
        startedAt: new Date(),
        userId,
      };

      this.activeExecutions.set(execution.id, executionContext);
      this.stageExecutions.set(execution.id, new Map());

      // Start first stage
      await this.executeNextStages(execution.id, stages);

      // Emit event
      this.eventBus.emit(EVENT_TYPES.WORKFLOW_STARTED, {
        executionId: execution.id,
        templateId,
        userId,
        data: { execution, stages },
      });

      logger.info('Workflow execution started', { 
        executionId: execution.id, 
        templateId, 
        stageCount: stages.length 
      });

      return execution.id;
    } catch (error) {
      logger.error('Failed to start workflow execution', { error, templateId, userId });
      throw error;
    }
  }

  /**
   * Execute next available stages
   */
  private async executeNextStages(executionId: string, stages: WorkflowStage[]): Promise<void> {
    const context = this.activeExecutions.get(executionId);
    if (!context) return;

    const stageExecutions = this.stageExecutions.get(executionId)!;

    // Find stages that can be executed (dependencies met)
    const readyStages = stages.filter(stage => {
      const stageExecution = stageExecutions.get(stage.id);
      
      // Skip if already running or completed
      if (stageExecution && ['RUNNING', 'COMPLETED'].includes(stageExecution.status)) {
        return false;
      }

      // Check if all dependencies are completed
      return stage.dependencies.every(depId => {
        const depExecution = stageExecutions.get(depId);
        return depExecution && depExecution.status === 'COMPLETED';
      });
    });

    // Execute ready stages
    for (const stage of readyStages) {
      await this.executeStage(executionId, stage);
    }
  }

  /**
   * Execute a single workflow stage
   */
  private async executeStage(executionId: string, stage: WorkflowStage): Promise<void> {
    try {
      logger.info('Executing workflow stage', { executionId, stageId: stage.id });

      const context = this.activeExecutions.get(executionId)!;
      const stageExecutions = this.stageExecutions.get(executionId)!;

      // Assign agents to stage
      const assignmentResult = await this.assignmentService.assignAgents({
        requiredRoles: stage.requiredRoles,
        optionalRoles: stage.optionalRoles,
        requiredCapabilities: stage.requiredCapabilities,
        maxAgents: stage.parallelExecution ? 5 : 1,
        workloadBalancing: true,
      }, context.userId);

      if (!assignmentResult.success || assignmentResult.assignments.length === 0) {
        throw new Error(`No suitable agents found for stage ${stage.name}`);
      }

      // Create stage execution
      const stageExecution: StageExecution = {
        stageId: stage.id,
        status: 'RUNNING',
        assignedAgents: assignmentResult.assignments.map(a => a.agentId),
        startedAt: new Date(),
        retryCount: 0,
        logs: [`Stage started with ${assignmentResult.assignments.length} agents`],
      };

      stageExecutions.set(stage.id, stageExecution);
      context.assignedAgents.set(stage.id, stageExecution.assignedAgents);

      // Update database
      await this.prisma.workflowExecution.update({
        where: { id: executionId },
        data: {
          status: 'RUNNING',
          currentStage: stage.id,
          context: context.sharedContext,
        },
      });

      // Emit stage started event
      this.eventBus.emit(EVENT_TYPES.WORKFLOW_STAGE_STARTED, {
        executionId,
        stageId: stage.id,
        userId: context.userId,
        data: {
          stage,
          assignedAgents: assignmentResult.assignments,
          context: context.sharedContext,
        },
      });

      // Set timeout for stage
      setTimeout(() => {
        this.handleStageTimeout(executionId, stage.id);
      }, stage.timeoutMinutes * 60 * 1000);

      // Simulate stage execution (in real implementation, this would coordinate with actual agents)
      this.simulateStageExecution(executionId, stage);

    } catch (error) {
      logger.error('Failed to execute stage', { error, executionId, stageId: stage.id });
      await this.handleStageFailure(executionId, stage.id, error.message);
    }
  }

  /**
   * Simulate stage execution (placeholder for real agent coordination)
   */
  private async simulateStageExecution(executionId: string, stage: WorkflowStage): Promise<void> {
    // Simulate processing time
    const processingTime = Math.random() * stage.estimatedDuration * 60 * 1000; // Convert to milliseconds
    
    setTimeout(async () => {
      const success = Math.random() > 0.1; // 90% success rate
      
      if (success) {
        const result = {
          stageId: stage.id,
          output: `Stage ${stage.name} completed successfully`,
          metrics: {
            duration: processingTime,
            agentsUsed: this.stageExecutions.get(executionId)?.get(stage.id)?.assignedAgents.length || 0,
          },
          timestamp: new Date(),
        };
        
        await this.handleStageCompletion(executionId, stage.id, result);
      } else {
        await this.handleStageFailure(executionId, stage.id, 'Simulated stage failure');
      }
    }, Math.min(processingTime, 30000)); // Cap at 30 seconds for demo
  }

  /**
   * Handle stage completion
   */
  private async handleStageCompletion(executionId: string, stageId: string, result: any): Promise<void> {
    try {
      const context = this.activeExecutions.get(executionId);
      const stageExecutions = this.stageExecutions.get(executionId);
      
      if (!context || !stageExecutions) return;

      const stageExecution = stageExecutions.get(stageId);
      if (!stageExecution || stageExecution.status !== 'RUNNING') return;

      // Update stage execution
      stageExecution.status = 'COMPLETED';
      stageExecution.completedAt = new Date();
      stageExecution.result = result;
      stageExecution.logs.push('Stage completed successfully');

      // Store result in context
      context.stageResults.set(stageId, result);
      context.sharedContext[`stage_${stageId}_result`] = result;

      logger.info('Workflow stage completed', { executionId, stageId });

      // Emit event
      this.eventBus.emit(EVENT_TYPES.WORKFLOW_STAGE_COMPLETED, {
        executionId,
        stageId,
        userId: context.userId,
        data: { result, stageExecution },
      });

      // Check if workflow is complete
      const template = await this.prisma.workflowTemplate.findUnique({
        where: { id: context.templateId },
      });

      if (template) {
        const stages = this.parseWorkflowStages(template.definition);
        const allStagesCompleted = stages.every(stage => {
          const execution = stageExecutions.get(stage.id);
          return execution && execution.status === 'COMPLETED';
        });

        if (allStagesCompleted) {
          await this.completeWorkflow(executionId);
        } else {
          // Execute next stages
          await this.executeNextStages(executionId, stages);
        }
      }
    } catch (error) {
      logger.error('Failed to handle stage completion', { error, executionId, stageId });
    }
  }

  /**
   * Handle stage failure
   */
  private async handleStageFailure(executionId: string, stageId: string, error: string): Promise<void> {
    try {
      const context = this.activeExecutions.get(executionId);
      const stageExecutions = this.stageExecutions.get(executionId);
      
      if (!context || !stageExecutions) return;

      const stageExecution = stageExecutions.get(stageId);
      if (!stageExecution) return;

      stageExecution.retryCount++;
      stageExecution.error = error;
      stageExecution.logs.push(`Stage failed: ${error}`);

      // Get stage definition for retry logic
      const template = await this.prisma.workflowTemplate.findUnique({
        where: { id: context.templateId },
      });

      if (template) {
        const stages = this.parseWorkflowStages(template.definition);
        const stage = stages.find(s => s.id === stageId);

        if (stage && stageExecution.retryCount <= stage.maxRetries) {
          // Retry stage
          stageExecution.status = 'PENDING';
          stageExecution.logs.push(`Retrying stage (attempt ${stageExecution.retryCount})`);
          
          setTimeout(() => {
            this.executeStage(executionId, stage);
          }, 5000); // Wait 5 seconds before retry
        } else {
          // Mark as failed
          stageExecution.status = 'FAILED';
          await this.failWorkflow(executionId, `Stage ${stageId} failed: ${error}`);
        }
      }

      logger.warn('Workflow stage failed', { executionId, stageId, error, retryCount: stageExecution.retryCount });

      // Emit event
      this.eventBus.emit(EVENT_TYPES.WORKFLOW_STAGE_FAILED, {
        executionId,
        stageId,
        userId: context.userId,
        data: { error, stageExecution },
      });
    } catch (err) {
      logger.error('Failed to handle stage failure', { error: err, executionId, stageId });
    }
  }

  /**
   * Handle stage timeout
   */
  private async handleStageTimeout(executionId: string, stageId: string): Promise<void> {
    const stageExecutions = this.stageExecutions.get(executionId);
    if (!stageExecutions) return;

    const stageExecution = stageExecutions.get(stageId);
    if (!stageExecution || stageExecution.status !== 'RUNNING') return;

    await this.handleStageFailure(executionId, stageId, 'Stage timeout');
  }

  /**
   * Complete workflow execution
   */
  private async completeWorkflow(executionId: string): Promise<void> {
    try {
      const context = this.activeExecutions.get(executionId);
      if (!context) return;

      // Update database
      await this.prisma.workflowExecution.update({
        where: { id: executionId },
        data: {
          status: 'COMPLETED',
          endedAt: new Date(),
          context: context.sharedContext,
        },
      });

      // Clean up
      this.activeExecutions.delete(executionId);
      this.stageExecutions.delete(executionId);

      logger.info('Workflow execution completed', { executionId });

      // Emit event
      this.eventBus.emit(EVENT_TYPES.WORKFLOW_COMPLETED, {
        executionId,
        userId: context.userId,
        data: {
          results: Object.fromEntries(context.stageResults),
          context: context.sharedContext,
        },
      });
    } catch (error) {
      logger.error('Failed to complete workflow', { error, executionId });
    }
  }

  /**
   * Fail workflow execution
   */
  private async failWorkflow(executionId: string, reason: string): Promise<void> {
    try {
      const context = this.activeExecutions.get(executionId);
      if (!context) return;

      // Update database
      await this.prisma.workflowExecution.update({
        where: { id: executionId },
        data: {
          status: 'FAILED',
          endedAt: new Date(),
          context: { ...context.sharedContext, failureReason: reason },
        },
      });

      // Clean up
      this.activeExecutions.delete(executionId);
      this.stageExecutions.delete(executionId);

      logger.error('Workflow execution failed', { executionId, reason });

      // Emit event
      this.eventBus.emit(EVENT_TYPES.WORKFLOW_FAILED, {
        executionId,
        userId: context.userId,
        data: { reason, context: context.sharedContext },
      });
    } catch (error) {
      logger.error('Failed to fail workflow', { error, executionId });
    }
  }

  /**
   * Parse workflow stages from template definition
   */
  private parseWorkflowStages(definition: any): WorkflowStage[] {
    // This would parse the actual workflow definition format
    // For now, return a sample structure
    return [
      {
        id: 'analysis',
        name: 'Requirements Analysis',
        description: 'Analyze project requirements and constraints',
        requiredRoles: ['senior-architect', 'product-analyst'],
        requiredCapabilities: ['requirement-analysis', 'system-design'],
        dependencies: [],
        estimatedDuration: 30,
        maxRetries: 2,
        timeoutMinutes: 60,
        parallelExecution: false,
      },
      {
        id: 'design',
        name: 'System Design',
        description: 'Create system architecture and design documents',
        requiredRoles: ['senior-architect', 'full-stack-developer'],
        requiredCapabilities: ['system-design', 'architecture-review'],
        dependencies: ['analysis'],
        estimatedDuration: 45,
        maxRetries: 2,
        timeoutMinutes: 90,
        parallelExecution: false,
      },
      {
        id: 'implementation',
        name: 'Implementation',
        description: 'Implement the designed system',
        requiredRoles: ['full-stack-developer', 'frontend-specialist', 'backend-specialist'],
        requiredCapabilities: ['frontend-development', 'backend-development'],
        dependencies: ['design'],
        estimatedDuration: 120,
        maxRetries: 3,
        timeoutMinutes: 180,
        parallelExecution: true,
      },
      {
        id: 'testing',
        name: 'Testing & QA',
        description: 'Test the implemented system',
        requiredRoles: ['test-automation-engineer', 'qa-analyst'],
        requiredCapabilities: ['test-automation', 'manual-testing'],
        dependencies: ['implementation'],
        estimatedDuration: 60,
        maxRetries: 2,
        timeoutMinutes: 120,
        parallelExecution: true,
      },
    ];
  }

  /**
   * Get execution status
   */
  async getExecutionStatus(executionId: string): Promise<any> {
    const context = this.activeExecutions.get(executionId);
    const stageExecutions = this.stageExecutions.get(executionId);

    const dbExecution = await this.prisma.workflowExecution.findUnique({
      where: { id: executionId },
      include: {
        template: {
          select: {
            id: true,
            name: true,
            description: true,
          },
        },
      },
    });

    return {
      execution: dbExecution,
      context: context ? {
        currentStage: context.currentStage,
        assignedAgents: Object.fromEntries(context.assignedAgents),
        stageResults: Object.fromEntries(context.stageResults),
        sharedContext: context.sharedContext,
      } : null,
      stages: stageExecutions ? Object.fromEntries(stageExecutions) : {},
    };
  }

  /**
   * Cancel workflow execution
   */
  async cancelExecution(executionId: string, userId: string): Promise<void> {
    const context = this.activeExecutions.get(executionId);
    if (!context) {
      throw new Error('Execution not found or already completed');
    }

    await this.prisma.workflowExecution.update({
      where: { id: executionId },
      data: {
        status: 'CANCELLED',
        endedAt: new Date(),
      },
    });

    // Clean up
    this.activeExecutions.delete(executionId);
    this.stageExecutions.delete(executionId);

    // Emit event
    this.eventBus.emit(EVENT_TYPES.WORKFLOW_CANCELLED, {
      executionId,
      userId,
      data: { context: context.sharedContext },
    });

    logger.info('Workflow execution cancelled', { executionId, userId });
  }
}

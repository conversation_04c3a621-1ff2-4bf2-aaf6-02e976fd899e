"""
Adaptive Confidence Model

Dynamic confidence scoring model that learns from feedback patterns and
adjusts confidence calculations based on historical performance and context.
"""

import logging
import math
from typing import Dict, Any, List
from collections import defaultdict, deque
from datetime import datetime, timedelta

from ...core.feedback_types import (
    ValidationResult, FeedbackType, FeedbackEntry, ConfidenceAdjustment
)


class AdaptiveConfidenceModel:
    """
    Adaptive confidence model that dynamically adjusts confidence scores
    based on validation results, historical performance, and context patterns.
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize the adaptive confidence model.
        
        Args:
            config: Configuration dictionary for model parameters
        """
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # Model parameters
        self.adjustment_config = ConfidenceAdjustment(
            correct_bonus=self.config.get('correct_bonus', 0.05),
            partially_correct_penalty=self.config.get('partially_correct_penalty', 0.02),
            incorrect_penalty=self.config.get('incorrect_penalty', 0.10),
            miscellaneous_neutral=self.config.get('miscellaneous_neutral', 0.0)
        )
        
        # Learning parameters
        self.learning_rate = self.config.get('learning_rate', 0.1)
        self.decay_factor = self.config.get('decay_factor', 0.95)
        self.history_window = self.config.get('history_window', 100)
        
        # Historical data storage
        self.domain_history = defaultdict(lambda: deque(maxlen=self.history_window))
        self.pattern_weights = defaultdict(float)
        self.context_adjustments = defaultdict(float)
        
        # Performance tracking
        self.model_stats = {
            'total_calculations': 0,
            'adjustments_made': 0,
            'learning_updates': 0,
            'average_confidence': 0.0
        }
    
    def calculate_confidence(self, 
                           validation_result: ValidationResult, 
                           context: Dict[str, Any]) -> float:
        """
        Calculate confidence score for a validation result.
        
        Args:
            validation_result: Result from validator
            context: Additional context for confidence calculation
            
        Returns:
            Confidence score between 0.0 and 1.0
        """
        try:
            # Start with base confidence from validation
            base_confidence = validation_result.confidence_score
            if base_confidence == 0.0:
                base_confidence = 0.5  # Default if not provided
            
            # Apply domain-specific adjustments
            domain = context.get('domain', 'unknown')
            domain_adjustment = self._get_domain_adjustment(domain, validation_result)
            
            # Apply context-specific adjustments
            context_adjustment = self._get_context_adjustment(context, validation_result)
            
            # Apply pattern-based adjustments
            pattern_adjustment = self._get_pattern_adjustment(validation_result)
            
            # Combine all adjustments
            final_confidence = base_confidence + domain_adjustment + context_adjustment + pattern_adjustment
            
            # Apply bounds
            final_confidence = max(self.adjustment_config.min_confidence, 
                                 min(self.adjustment_config.max_confidence, final_confidence))
            
            # Update statistics
            self._update_stats(final_confidence)
            
            self.logger.debug(
                f"Calculated confidence: base={base_confidence:.3f}, "
                f"domain_adj={domain_adjustment:.3f}, "
                f"context_adj={context_adjustment:.3f}, "
                f"pattern_adj={pattern_adjustment:.3f}, "
                f"final={final_confidence:.3f}"
            )
            
            return final_confidence
            
        except Exception as e:
            self.logger.error(f"Error calculating confidence: {str(e)}")
            return 0.5  # Return neutral confidence on error
    
    def adjust_confidence(self, 
                         current_confidence: float, 
                         feedback_type: FeedbackType, 
                         context: Dict[str, Any]) -> float:
        """
        Adjust confidence score based on feedback.
        
        Args:
            current_confidence: Current confidence score
            feedback_type: Type of feedback received
            context: Additional context for adjustment
            
        Returns:
            Adjusted confidence score between 0.0 and 1.0
        """
        try:
            # Get base adjustment for feedback type
            adjustment = self._get_feedback_adjustment(feedback_type)
            
            # Apply context-specific modifiers
            domain = context.get('domain', 'unknown')
            severity_modifier = self._get_severity_modifier(context)
            
            # Calculate final adjustment
            final_adjustment = adjustment * severity_modifier
            adjusted_confidence = current_confidence + final_adjustment
            
            # Apply bounds
            adjusted_confidence = max(self.adjustment_config.min_confidence,
                                    min(self.adjustment_config.max_confidence, adjusted_confidence))
            
            # Update statistics
            self.model_stats['adjustments_made'] += 1
            
            self.logger.debug(
                f"Adjusted confidence: current={current_confidence:.3f}, "
                f"feedback={feedback_type.value}, "
                f"adjustment={final_adjustment:.3f}, "
                f"final={adjusted_confidence:.3f}"
            )
            
            return adjusted_confidence
            
        except Exception as e:
            self.logger.error(f"Error adjusting confidence: {str(e)}")
            return current_confidence  # Return unchanged on error
    
    def update_model(self, feedback_entries: List[FeedbackEntry]) -> None:
        """
        Update the confidence model with new feedback data.
        
        Args:
            feedback_entries: List of recent feedback entries for learning
        """
        try:
            if not feedback_entries:
                return
            
            # Group entries by domain
            domain_entries = defaultdict(list)
            for entry in feedback_entries:
                domain_entries[entry.domain].append(entry)
            
            # Update domain-specific patterns
            for domain, entries in domain_entries.items():
                self._update_domain_patterns(domain, entries)
            
            # Update context patterns
            self._update_context_patterns(feedback_entries)
            
            # Update pattern weights
            self._update_pattern_weights(feedback_entries)
            
            self.model_stats['learning_updates'] += 1
            
            self.logger.info(f"Updated confidence model with {len(feedback_entries)} feedback entries")
            
        except Exception as e:
            self.logger.error(f"Error updating confidence model: {str(e)}")
    
    def _get_domain_adjustment(self, domain: str, validation_result: ValidationResult) -> float:
        """Get domain-specific confidence adjustment."""
        # Get historical performance for this domain
        domain_entries = list(self.domain_history[domain])
        if not domain_entries:
            return 0.0
        
        # Calculate recent success rate
        recent_entries = domain_entries[-20:]  # Last 20 entries
        success_count = sum(1 for entry in recent_entries 
                          if entry['feedback_type'] in [FeedbackType.CORRECT, FeedbackType.PARTIALLY_CORRECT])
        
        success_rate = success_count / len(recent_entries)
        
        # Adjust based on success rate
        if success_rate > 0.8:
            return 0.05  # Boost confidence for high-performing domains
        elif success_rate < 0.5:
            return -0.05  # Reduce confidence for low-performing domains
        
        return 0.0
    
    def _get_context_adjustment(self, context: Dict[str, Any], validation_result: ValidationResult) -> float:
        """Get context-specific confidence adjustment."""
        adjustment = 0.0
        
        # Adjust based on processing time
        processing_time = context.get('processing_time_ms', 0)
        if processing_time > 5000:  # Slow processing
            adjustment -= 0.02
        elif processing_time < 1000:  # Fast processing
            adjustment += 0.01
        
        # Adjust based on data quality
        if validation_result.issues:
            critical_issues = [i for i in validation_result.issues if i.get('severity') == 'critical']
            adjustment -= len(critical_issues) * 0.05
        
        # Adjust based on agent performance (if available)
        agent_id = context.get('agent_id')
        if agent_id and agent_id in self.context_adjustments:
            adjustment += self.context_adjustments[agent_id] * 0.1
        
        return adjustment
    
    def _get_pattern_adjustment(self, validation_result: ValidationResult) -> float:
        """Get pattern-based confidence adjustment."""
        adjustment = 0.0
        
        # Look for known patterns in issues
        for issue in validation_result.issues:
            issue_type = issue.get('type', 'unknown')
            if issue_type in self.pattern_weights:
                weight = self.pattern_weights[issue_type]
                severity_multiplier = {'critical': -0.1, 'high': -0.05, 'medium': -0.02, 'low': -0.01}.get(
                    issue.get('severity', 'low'), -0.01
                )
                adjustment += weight * severity_multiplier
        
        return adjustment
    
    def _get_feedback_adjustment(self, feedback_type: FeedbackType) -> float:
        """Get base adjustment for feedback type."""
        adjustments = {
            FeedbackType.CORRECT: self.adjustment_config.correct_bonus,
            FeedbackType.PARTIALLY_CORRECT: -self.adjustment_config.partially_correct_penalty,
            FeedbackType.INCORRECT: -self.adjustment_config.incorrect_penalty,
            FeedbackType.MISCELLANEOUS: self.adjustment_config.miscellaneous_neutral
        }
        return adjustments.get(feedback_type, 0.0)
    
    def _get_severity_modifier(self, context: Dict[str, Any]) -> float:
        """Get severity modifier based on context."""
        # Modify adjustment based on issue severity
        issues = context.get('validation_issues', [])
        if not issues:
            return 1.0
        
        critical_count = sum(1 for issue in issues if issue.get('severity') == 'critical')
        if critical_count > 0:
            return 1.5  # Amplify adjustments for critical issues
        
        high_count = sum(1 for issue in issues if issue.get('severity') == 'high')
        if high_count > 2:
            return 1.2  # Amplify adjustments for multiple high-severity issues
        
        return 1.0
    
    def _update_domain_patterns(self, domain: str, entries: List[FeedbackEntry]) -> None:
        """Update domain-specific patterns."""
        for entry in entries:
            # Store simplified entry data
            entry_data = {
                'feedback_type': entry.feedback_type,
                'confidence_score': entry.confidence_score,
                'timestamp': entry.timestamp,
                'issues_count': len(entry.validation_issues)
            }
            self.domain_history[domain].append(entry_data)
    
    def _update_context_patterns(self, entries: List[FeedbackEntry]) -> None:
        """Update context-specific patterns."""
        for entry in entries:
            agent_id = entry.agent_id
            if agent_id:
                # Update agent-specific adjustment based on performance
                if entry.feedback_type == FeedbackType.CORRECT:
                    self.context_adjustments[agent_id] = min(1.0, self.context_adjustments[agent_id] + 0.1)
                elif entry.feedback_type == FeedbackType.INCORRECT:
                    self.context_adjustments[agent_id] = max(-1.0, self.context_adjustments[agent_id] - 0.1)
    
    def _update_pattern_weights(self, entries: List[FeedbackEntry]) -> None:
        """Update pattern weights based on feedback correlation."""
        for entry in entries:
            for issue in entry.validation_issues:
                issue_type = issue.get('type', 'unknown')
                
                # Adjust weight based on feedback type
                if entry.feedback_type == FeedbackType.INCORRECT:
                    self.pattern_weights[issue_type] = min(1.0, self.pattern_weights[issue_type] + self.learning_rate)
                elif entry.feedback_type == FeedbackType.CORRECT:
                    self.pattern_weights[issue_type] = max(-1.0, self.pattern_weights[issue_type] - self.learning_rate * 0.5)
        
        # Apply decay to all weights
        for issue_type in self.pattern_weights:
            self.pattern_weights[issue_type] *= self.decay_factor
    
    def _update_stats(self, confidence: float) -> None:
        """Update model statistics."""
        self.model_stats['total_calculations'] += 1
        
        # Update running average
        total = self.model_stats['total_calculations']
        current_avg = self.model_stats['average_confidence']
        self.model_stats['average_confidence'] = (current_avg * (total - 1) + confidence) / total
    
    def get_model_stats(self) -> Dict[str, Any]:
        """Get model performance statistics."""
        return {
            **self.model_stats,
            'domain_count': len(self.domain_history),
            'pattern_count': len(self.pattern_weights),
            'context_adjustments_count': len(self.context_adjustments)
        }
    
    def get_domain_performance(self, domain: str) -> Dict[str, Any]:
        """Get performance statistics for a specific domain."""
        entries = list(self.domain_history[domain])
        if not entries:
            return {'domain': domain, 'entries': 0}
        
        # Calculate statistics
        total_entries = len(entries)
        correct_count = sum(1 for e in entries if e['feedback_type'] == FeedbackType.CORRECT)
        partial_count = sum(1 for e in entries if e['feedback_type'] == FeedbackType.PARTIALLY_CORRECT)
        incorrect_count = sum(1 for e in entries if e['feedback_type'] == FeedbackType.INCORRECT)
        
        avg_confidence = sum(e['confidence_score'] for e in entries) / total_entries
        
        return {
            'domain': domain,
            'entries': total_entries,
            'correct_rate': correct_count / total_entries,
            'partial_rate': partial_count / total_entries,
            'incorrect_rate': incorrect_count / total_entries,
            'average_confidence': avg_confidence
        }
    
    def reset_model(self) -> None:
        """Reset the model to initial state."""
        self.domain_history.clear()
        self.pattern_weights.clear()
        self.context_adjustments.clear()
        self.model_stats = {
            'total_calculations': 0,
            'adjustments_made': 0,
            'learning_updates': 0,
            'average_confidence': 0.0
        }
        self.logger.info("Reset adaptive confidence model")

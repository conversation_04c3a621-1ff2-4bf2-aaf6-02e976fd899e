"""
Central Orchestration Controller for Branded AI Coding Assistants

Core controller that assigns roles, delegates tasks, and manages agent health
with dynamic swapping and fallback logic for optimal performance.

Key Features:
- Dynamic role assignment and agent swapping
- Health monitoring and fallback management
- Task routing and load balancing
- Performance optimization and learning
- Compliance enforcement and validation
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional, Tuple, Union
from datetime import datetime, timed<PERSON><PERSON>
from enum import Enum
from dataclasses import dataclass, field
import uuid

from .legal_compliance import ComplianceManager, LicenseStatus
from .agent_registry import AgentRegistry, AgentRole, AgentStatus
from .shared_context import SharedContext
from .workflow_executor import WorkflowExecutor, TaskResult


class OrchestrationMode(Enum):
    """Orchestration execution modes."""
    SEQUENTIAL = "sequential"
    PARALLEL = "parallel"
    ADAPTIVE = "adaptive"
    OPTIMIZED = "optimized"


class TaskPriority(Enum):
    """Task priority levels."""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class OrchestrationTask:
    """Represents a task to be orchestrated across AI assistants."""
    task_id: str
    description: str
    task_type: str
    priority: TaskPriority
    required_roles: List[AgentRole]
    preferred_agents: List[str] = field(default_factory=list)
    context: Dict[str, Any] = field(default_factory=dict)
    constraints: Dict[str, Any] = field(default_factory=dict)
    timeout_seconds: int = 300
    retry_count: int = 3
    created_at: datetime = field(default_factory=datetime.utcnow)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    status: str = "pending"


@dataclass
class AgentAssignment:
    """Represents an agent assignment for a specific role."""
    agent_id: str
    role: AgentRole
    task_id: str
    assigned_at: datetime
    confidence_score: float = 0.0
    performance_history: List[float] = field(default_factory=list)
    fallback_agents: List[str] = field(default_factory=list)


class OrchestrationEngine:
    """
    Central orchestration controller for branded AI coding assistants.
    
    Manages agent coordination, role assignment, task execution,
    and performance optimization across all integrated AI assistants.
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # Core components
        self.compliance_manager = ComplianceManager(self.config.get('compliance', {}))
        self.agent_registry = AgentRegistry(self.config.get('agents', {}))
        self.shared_context = SharedContext(self.config.get('context', {}))
        self.workflow_executor = WorkflowExecutor(self.config.get('workflow', {}))
        
        # Orchestration state
        self.active_tasks: Dict[str, OrchestrationTask] = {}
        self.agent_assignments: Dict[str, List[AgentAssignment]] = {}
        self.performance_metrics: Dict[str, Dict[str, Any]] = {}
        
        # Configuration
        self.orchestration_mode = OrchestrationMode(
            self.config.get('orchestration_mode', 'adaptive')
        )
        self.max_concurrent_tasks = self.config.get('max_concurrent_tasks', 10)
        self.health_check_interval = self.config.get('health_check_interval', 30)
        self.performance_learning_enabled = self.config.get('performance_learning', True)
        
        # Performance tracking
        self.orchestration_stats = {
            'total_tasks': 0,
            'successful_tasks': 0,
            'failed_tasks': 0,
            'average_completion_time': 0.0,
            'agent_utilization': {},
            'role_performance': {},
            'fallback_activations': 0
        }
        
        # Start background monitoring
        self._start_background_monitoring()
    
    async def execute_task(self, task_description: str, task_type: str = "code_generation",
                          priority: TaskPriority = TaskPriority.MEDIUM,
                          preferred_agents: List[str] = None,
                          required_roles: List[AgentRole] = None,
                          context: Dict[str, Any] = None) -> TaskResult:
        """
        Execute a task using optimal agent assignment and orchestration.
        
        Args:
            task_description: Description of the task to execute
            task_type: Type of task (e.g., 'code_generation', 'code_review')
            priority: Task priority level
            preferred_agents: List of preferred agent IDs
            required_roles: List of required agent roles
            context: Additional context for task execution
            
        Returns:
            TaskResult with execution details and outputs
        """
        try:
            # Create orchestration task
            task = OrchestrationTask(
                task_id=str(uuid.uuid4()),
                description=task_description,
                task_type=task_type,
                priority=priority,
                required_roles=required_roles or self._determine_required_roles(task_type),
                preferred_agents=preferred_agents or [],
                context=context or {},
                timeout_seconds=self.config.get('task_timeout', 300)
            )
            
            self.logger.info(f"Starting task execution: {task.task_id}")
            
            # Validate compliance for all agents
            compliance_check = await self._validate_task_compliance(task)
            if not compliance_check['compliant']:
                return TaskResult(
                    task_id=task.task_id,
                    success=False,
                    error_message=f"Compliance validation failed: {compliance_check['issues']}",
                    metadata={'compliance_issues': compliance_check['issues']}
                )
            
            # Add to active tasks
            self.active_tasks[task.task_id] = task
            task.status = "active"
            task.started_at = datetime.utcnow()
            
            # Determine optimal agent assignments
            assignments = await self._create_optimal_assignments(task)
            if not assignments:
                return TaskResult(
                    task_id=task.task_id,
                    success=False,
                    error_message="No suitable agents available for task execution",
                    metadata={'available_agents': list(self.agent_registry.get_available_agents())}
                )
            
            # Store assignments
            self.agent_assignments[task.task_id] = assignments
            
            # Execute task with assigned agents
            result = await self._execute_with_assignments(task, assignments)
            
            # Update performance metrics
            await self._update_performance_metrics(task, assignments, result)
            
            # Clean up
            self._cleanup_task(task.task_id)
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error executing task: {str(e)}")
            return TaskResult(
                task_id=task.task_id if 'task' in locals() else "unknown",
                success=False,
                error_message=f"Task execution failed: {str(e)}",
                metadata={'error_type': type(e).__name__}
            )
    
    def _determine_required_roles(self, task_type: str) -> List[AgentRole]:
        """Determine required roles based on task type."""
        role_mappings = {
            'code_generation': [AgentRole.GENERATOR, AgentRole.VALIDATOR],
            'code_review': [AgentRole.ANALYZER, AgentRole.VALIDATOR],
            'code_completion': [AgentRole.COMPLETER],
            'code_refactoring': [AgentRole.ANALYZER, AgentRole.GENERATOR, AgentRole.VALIDATOR],
            'documentation': [AgentRole.DOCUMENTER, AgentRole.VALIDATOR],
            'testing': [AgentRole.GENERATOR, AgentRole.VALIDATOR],
            'debugging': [AgentRole.ANALYZER, AgentRole.GENERATOR],
            'optimization': [AgentRole.ANALYZER, AgentRole.OPTIMIZER, AgentRole.VALIDATOR]
        }
        
        return role_mappings.get(task_type, [AgentRole.GENERATOR, AgentRole.VALIDATOR])
    
    async def _validate_task_compliance(self, task: OrchestrationTask) -> Dict[str, Any]:
        """Validate compliance for task execution."""
        compliance_issues = []
        
        # Check if preferred agents have valid licenses
        for agent_id in task.preferred_agents:
            agent_info = self.agent_registry.get_agent_info(agent_id)
            if agent_info:
                vendor = agent_info.get('vendor')
                compliance_status = self.compliance_manager.check_compliance_status(vendor)
                if not compliance_status.get('compliant', False):
                    compliance_issues.append(f"Agent {agent_id} compliance check failed")
        
        return {
            'compliant': len(compliance_issues) == 0,
            'issues': compliance_issues,
            'checked_at': datetime.utcnow().isoformat()
        }
    
    async def _create_optimal_assignments(self, task: OrchestrationTask) -> List[AgentAssignment]:
        """Create optimal agent assignments for task roles."""
        assignments = []
        
        for role in task.required_roles:
            # Get available agents for this role
            available_agents = self.agent_registry.get_agents_by_role(role)
            
            # Filter by preferred agents if specified
            if task.preferred_agents:
                available_agents = [
                    agent for agent in available_agents 
                    if agent['agent_id'] in task.preferred_agents
                ]
            
            # Filter by availability and health
            healthy_agents = [
                agent for agent in available_agents
                if agent['status'] == AgentStatus.AVAILABLE and agent['health_score'] > 0.7
            ]
            
            if not healthy_agents:
                self.logger.warning(f"No healthy agents available for role {role}")
                continue
            
            # Select best agent based on performance history
            best_agent = self._select_best_agent(healthy_agents, role, task)
            
            if best_agent:
                # Create assignment with fallback agents
                fallback_agents = [
                    agent['agent_id'] for agent in healthy_agents 
                    if agent['agent_id'] != best_agent['agent_id']
                ][:2]  # Top 2 fallback agents
                
                assignment = AgentAssignment(
                    agent_id=best_agent['agent_id'],
                    role=role,
                    task_id=task.task_id,
                    assigned_at=datetime.utcnow(),
                    confidence_score=best_agent.get('confidence_score', 0.8),
                    fallback_agents=fallback_agents
                )
                
                assignments.append(assignment)
                
                # Reserve the agent
                self.agent_registry.reserve_agent(best_agent['agent_id'], task.task_id)
        
        return assignments
    
    def _select_best_agent(self, agents: List[Dict[str, Any]], role: AgentRole, task: OrchestrationTask) -> Optional[Dict[str, Any]]:
        """Select the best agent for a role based on performance and context."""
        if not agents:
            return None
        
        # Score agents based on multiple factors
        scored_agents = []
        
        for agent in agents:
            score = 0.0
            
            # Base health score
            score += agent.get('health_score', 0.5) * 0.3
            
            # Performance history for this role
            role_performance = self.performance_metrics.get(agent['agent_id'], {}).get(role.value, {})
            avg_performance = role_performance.get('average_score', 0.5)
            score += avg_performance * 0.4
            
            # Task type compatibility
            task_compatibility = agent.get('task_compatibility', {}).get(task.task_type, 0.5)
            score += task_compatibility * 0.2
            
            # Current load (prefer less loaded agents)
            current_load = agent.get('current_load', 0)
            load_penalty = min(current_load / 10.0, 0.5)  # Max 50% penalty
            score -= load_penalty * 0.1
            
            scored_agents.append((agent, score))
        
        # Sort by score and return best agent
        scored_agents.sort(key=lambda x: x[1], reverse=True)
        return scored_agents[0][0] if scored_agents else None
    
    async def _execute_with_assignments(self, task: OrchestrationTask, assignments: List[AgentAssignment]) -> TaskResult:
        """Execute task with assigned agents."""
        try:
            # Update shared context
            self.shared_context.update_context(task.task_id, {
                'task_description': task.description,
                'task_type': task.task_type,
                'assignments': [
                    {'agent_id': a.agent_id, 'role': a.role.value} 
                    for a in assignments
                ],
                'started_at': task.started_at.isoformat()
            })
            
            # Execute based on orchestration mode
            if self.orchestration_mode == OrchestrationMode.SEQUENTIAL:
                result = await self._execute_sequential(task, assignments)
            elif self.orchestration_mode == OrchestrationMode.PARALLEL:
                result = await self._execute_parallel(task, assignments)
            else:  # ADAPTIVE or OPTIMIZED
                result = await self._execute_adaptive(task, assignments)
            
            # Mark task as completed
            task.completed_at = datetime.utcnow()
            task.status = "completed" if result.success else "failed"
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error executing task {task.task_id}: {str(e)}")
            return TaskResult(
                task_id=task.task_id,
                success=False,
                error_message=str(e),
                metadata={'execution_error': True}
            )
        finally:
            # Release reserved agents
            for assignment in assignments:
                self.agent_registry.release_agent(assignment.agent_id)
    
    async def _execute_sequential(self, task: OrchestrationTask, assignments: List[AgentAssignment]) -> TaskResult:
        """Execute task with sequential agent execution."""
        results = []
        current_context = task.context.copy()
        
        # Sort assignments by role priority
        role_priority = {
            AgentRole.ANALYZER: 1,
            AgentRole.GENERATOR: 2,
            AgentRole.COMPLETER: 3,
            AgentRole.VALIDATOR: 4,
            AgentRole.OPTIMIZER: 5,
            AgentRole.DOCUMENTER: 6
        }
        
        sorted_assignments = sorted(assignments, key=lambda a: role_priority.get(a.role, 99))
        
        for assignment in sorted_assignments:
            try:
                # Execute with agent
                agent_result = await self.workflow_executor.execute_with_agent(
                    agent_id=assignment.agent_id,
                    role=assignment.role,
                    task_description=task.description,
                    context=current_context,
                    shared_context=self.shared_context.get_context(task.task_id)
                )
                
                if agent_result.success:
                    results.append(agent_result)
                    # Update context with results for next agent
                    current_context.update(agent_result.output_data)
                else:
                    # Try fallback agent
                    fallback_result = await self._try_fallback_agent(assignment, task, current_context)
                    if fallback_result:
                        results.append(fallback_result)
                        current_context.update(fallback_result.output_data)
                    else:
                        # Critical failure
                        return TaskResult(
                            task_id=task.task_id,
                            success=False,
                            error_message=f"Agent {assignment.agent_id} failed and no fallback available",
                            metadata={'failed_role': assignment.role.value}
                        )
                        
            except Exception as e:
                self.logger.error(f"Error executing with agent {assignment.agent_id}: {str(e)}")
                # Try fallback
                fallback_result = await self._try_fallback_agent(assignment, task, current_context)
                if not fallback_result:
                    return TaskResult(
                        task_id=task.task_id,
                        success=False,
                        error_message=f"Agent execution failed: {str(e)}",
                        metadata={'failed_agent': assignment.agent_id}
                    )
        
        # Combine results
        combined_output = {}
        for result in results:
            combined_output.update(result.output_data)
        
        return TaskResult(
            task_id=task.task_id,
            success=True,
            output_data=combined_output,
            metadata={
                'execution_mode': 'sequential',
                'agents_used': [a.agent_id for a in sorted_assignments],
                'execution_time': (datetime.utcnow() - task.started_at).total_seconds()
            }
        )
    
    async def _execute_parallel(self, task: OrchestrationTask, assignments: List[AgentAssignment]) -> TaskResult:
        """Execute task with parallel agent execution."""
        # Create tasks for parallel execution
        agent_tasks = []
        
        for assignment in assignments:
            agent_task = self.workflow_executor.execute_with_agent(
                agent_id=assignment.agent_id,
                role=assignment.role,
                task_description=task.description,
                context=task.context,
                shared_context=self.shared_context.get_context(task.task_id)
            )
            agent_tasks.append((assignment, agent_task))
        
        # Execute all agents in parallel
        results = await asyncio.gather(*[task for _, task in agent_tasks], return_exceptions=True)
        
        # Process results
        successful_results = []
        failed_assignments = []
        
        for i, result in enumerate(results):
            assignment = agent_tasks[i][0]
            
            if isinstance(result, Exception):
                self.logger.error(f"Agent {assignment.agent_id} failed: {str(result)}")
                failed_assignments.append(assignment)
            elif result.success:
                successful_results.append(result)
            else:
                failed_assignments.append(assignment)
        
        # Try fallbacks for failed assignments
        for assignment in failed_assignments:
            fallback_result = await self._try_fallback_agent(assignment, task, task.context)
            if fallback_result:
                successful_results.append(fallback_result)
        
        # Combine successful results
        combined_output = {}
        for result in successful_results:
            combined_output.update(result.output_data)
        
        return TaskResult(
            task_id=task.task_id,
            success=len(successful_results) > 0,
            output_data=combined_output,
            metadata={
                'execution_mode': 'parallel',
                'successful_agents': len(successful_results),
                'failed_agents': len(failed_assignments),
                'execution_time': (datetime.utcnow() - task.started_at).total_seconds()
            }
        )
    
    async def _execute_adaptive(self, task: OrchestrationTask, assignments: List[AgentAssignment]) -> TaskResult:
        """Execute task with adaptive orchestration based on task characteristics."""
        # Analyze task to determine best execution strategy
        if len(assignments) <= 2 or task.priority == TaskPriority.CRITICAL:
            # Use sequential for simple tasks or critical tasks
            return await self._execute_sequential(task, assignments)
        else:
            # Use parallel for complex tasks
            return await self._execute_parallel(task, assignments)
    
    async def _try_fallback_agent(self, failed_assignment: AgentAssignment, 
                                 task: OrchestrationTask, context: Dict[str, Any]) -> Optional[TaskResult]:
        """Try fallback agent for failed assignment."""
        if not failed_assignment.fallback_agents:
            return None
        
        self.orchestration_stats['fallback_activations'] += 1
        
        for fallback_agent_id in failed_assignment.fallback_agents:
            try:
                # Check if fallback agent is available
                if not self.agent_registry.is_agent_available(fallback_agent_id):
                    continue
                
                self.logger.info(f"Trying fallback agent {fallback_agent_id} for role {failed_assignment.role}")
                
                # Execute with fallback agent
                result = await self.workflow_executor.execute_with_agent(
                    agent_id=fallback_agent_id,
                    role=failed_assignment.role,
                    task_description=task.description,
                    context=context,
                    shared_context=self.shared_context.get_context(task.task_id)
                )
                
                if result.success:
                    self.logger.info(f"Fallback agent {fallback_agent_id} succeeded")
                    return result
                    
            except Exception as e:
                self.logger.error(f"Fallback agent {fallback_agent_id} failed: {str(e)}")
                continue
        
        return None
    
    async def _update_performance_metrics(self, task: OrchestrationTask, 
                                        assignments: List[AgentAssignment], 
                                        result: TaskResult) -> None:
        """Update performance metrics for agents and roles."""
        if not self.performance_learning_enabled:
            return
        
        execution_time = (task.completed_at - task.started_at).total_seconds()
        success_score = 1.0 if result.success else 0.0
        
        # Update orchestration stats
        self.orchestration_stats['total_tasks'] += 1
        if result.success:
            self.orchestration_stats['successful_tasks'] += 1
        else:
            self.orchestration_stats['failed_tasks'] += 1
        
        # Update average completion time
        total_time = (self.orchestration_stats['average_completion_time'] * 
                     (self.orchestration_stats['total_tasks'] - 1) + execution_time)
        self.orchestration_stats['average_completion_time'] = total_time / self.orchestration_stats['total_tasks']
        
        # Update agent-specific metrics
        for assignment in assignments:
            agent_id = assignment.agent_id
            role = assignment.role.value
            
            if agent_id not in self.performance_metrics:
                self.performance_metrics[agent_id] = {}
            
            if role not in self.performance_metrics[agent_id]:
                self.performance_metrics[agent_id][role] = {
                    'total_tasks': 0,
                    'successful_tasks': 0,
                    'average_score': 0.5,
                    'recent_scores': []
                }
            
            role_metrics = self.performance_metrics[agent_id][role]
            role_metrics['total_tasks'] += 1
            
            if result.success:
                role_metrics['successful_tasks'] += 1
            
            # Update recent scores (keep last 10)
            role_metrics['recent_scores'].append(success_score)
            if len(role_metrics['recent_scores']) > 10:
                role_metrics['recent_scores'] = role_metrics['recent_scores'][-10:]
            
            # Update average score
            role_metrics['average_score'] = sum(role_metrics['recent_scores']) / len(role_metrics['recent_scores'])
    
    def _cleanup_task(self, task_id: str) -> None:
        """Clean up task-related resources."""
        if task_id in self.active_tasks:
            del self.active_tasks[task_id]
        
        if task_id in self.agent_assignments:
            del self.agent_assignments[task_id]
        
        # Clean up shared context
        self.shared_context.cleanup_context(task_id)
    
    def _start_background_monitoring(self) -> None:
        """Start background monitoring tasks."""
        # This would start background tasks for health monitoring
        # For now, just log that monitoring is enabled
        self.logger.info("Background monitoring started")
    
    def get_orchestration_status(self) -> Dict[str, Any]:
        """Get current orchestration status and metrics."""
        return {
            'active_tasks': len(self.active_tasks),
            'total_agents': len(self.agent_registry.get_all_agents()),
            'available_agents': len(self.agent_registry.get_available_agents()),
            'orchestration_mode': self.orchestration_mode.value,
            'performance_stats': self.orchestration_stats.copy(),
            'compliance_status': self.compliance_manager.get_compliance_report(),
            'last_updated': datetime.utcnow().isoformat()
        }


def create_orchestration_engine(config: Dict[str, Any] = None) -> OrchestrationEngine:
    """
    Create and configure an orchestration engine.
    
    Args:
        config: Configuration dictionary
        
    Returns:
        Configured OrchestrationEngine instance
    """
    default_config = {
        'orchestration_mode': 'adaptive',
        'max_concurrent_tasks': 10,
        'performance_learning': True,
        'compliance': {
            'strict_mode': True,
            'compliance_checks_enabled': True
        }
    }
    
    if config:
        default_config.update(config)
    
    return OrchestrationEngine(default_config)

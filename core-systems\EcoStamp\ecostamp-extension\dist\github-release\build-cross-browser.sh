#!/bin/bash

# EcoStamp Extension Cross-Browser Build Script
# Creates distribution packages for ALL major browsers

set -e

VERSION="1.0.0"
EXTENSION_NAME="ecostamp"
BUILD_DIR="dist"
PACKAGE_NAME="${EXTENSION_NAME}-v${VERSION}"

echo "🌱 Building EcoStamp Extension v${VERSION} for ALL browsers"

# Clean previous builds
echo "🧹 Cleaning previous builds..."
rm -rf "${BUILD_DIR}"
mkdir -p "${BUILD_DIR}"

# Create browser-specific directories
CWS_DIR="${BUILD_DIR}/chrome-web-store"
FIREFOX_DIR="${BUILD_DIR}/firefox-addon"
OPERA_DIR="${BUILD_DIR}/opera-addon"
EDGE_DIR="${BUILD_DIR}/edge-addon"
GITHUB_DIR="${BUILD_DIR}/github-release"
UNIVERSAL_DIR="${BUILD_DIR}/universal-package"

mkdir -p "${CWS_DIR}" "${FIREFOX_DIR}" "${OPERA_DIR}" "${EDGE_DIR}" "${GITHUB_DIR}" "${UNIVERSAL_DIR}"

# Function to copy core files
copy_core_files() {
    local target_dir=$1
    cp content.js "${target_dir}/"
    cp styles.css "${target_dir}/"
    cp popup.html "${target_dir}/"
    cp popup.js "${target_dir}/"
    cp background.js "${target_dir}/"
    cp -r icons "${target_dir}/"
    cp -r data "${target_dir}/"
}

# Chrome Web Store package
echo "📦 Creating Chrome Web Store package..."
copy_core_files "${CWS_DIR}"
cp manifest.json "${CWS_DIR}/"

# Remove development keys from Chrome manifest
sed -i.bak '/\"key\":/d' "${CWS_DIR}/manifest.json"
sed -i.bak '/\"update_url\":/d' "${CWS_DIR}/manifest.json"
rm "${CWS_DIR}/manifest.json.bak" 2>/dev/null || true

# Firefox Add-on package
echo "🦊 Creating Firefox Add-on package..."
copy_core_files "${FIREFOX_DIR}"
cp manifest-firefox.json "${FIREFOX_DIR}/manifest.json"

# Opera Add-on package
echo "🔴 Creating Opera Add-on package..."
copy_core_files "${OPERA_DIR}"
cp manifest-opera.json "${OPERA_DIR}/manifest.json"

# Edge Add-on package (same as Chrome)
echo "🔵 Creating Edge Add-on package..."
copy_core_files "${EDGE_DIR}"
cp manifest.json "${EDGE_DIR}/"

# Remove development keys from Edge manifest
sed -i.bak '/\"key\":/d' "${EDGE_DIR}/manifest.json"
sed -i.bak '/\"update_url\":/d' "${EDGE_DIR}/manifest.json"
rm "${EDGE_DIR}/manifest.json.bak" 2>/dev/null || true

# Universal package (includes all manifests)
echo "🌐 Creating Universal package..."
copy_core_files "${UNIVERSAL_DIR}"
cp manifest.json "${UNIVERSAL_DIR}/"
cp manifest-firefox.json "${UNIVERSAL_DIR}/"
cp manifest-opera.json "${UNIVERSAL_DIR}/"
cp CROSS_BROWSER_INSTALL.md "${UNIVERSAL_DIR}/"

# GitHub release package (complete source)
echo "📦 Creating GitHub release package..."
copy_core_files "${GITHUB_DIR}"
cp manifest.json "${GITHUB_DIR}/"
cp manifest-firefox.json "${GITHUB_DIR}/"
cp manifest-opera.json "${GITHUB_DIR}/"
cp README.md "${GITHUB_DIR}/"
cp CHANGELOG.md "${GITHUB_DIR}/"
cp INSTALL.md "${GITHUB_DIR}/"
cp CROSS_BROWSER_INSTALL.md "${GITHUB_DIR}/"
cp LICENSE "${GITHUB_DIR}/"
cp CONTRIBUTING.md "${GITHUB_DIR}/"
cp build.sh "${GITHUB_DIR}/"
cp build-cross-browser.sh "${GITHUB_DIR}/"

# Create ZIP packages
echo "📦 Creating ZIP packages..."
cd "${BUILD_DIR}"

# Chrome Web Store ZIP
zip -r "${PACKAGE_NAME}-chrome-web-store.zip" chrome-web-store/
echo "✅ Chrome Web Store: ${PACKAGE_NAME}-chrome-web-store.zip"

# Firefox Add-on ZIP
zip -r "${PACKAGE_NAME}-firefox-addon.zip" firefox-addon/
echo "✅ Firefox Add-on: ${PACKAGE_NAME}-firefox-addon.zip"

# Opera Add-on ZIP
zip -r "${PACKAGE_NAME}-opera-addon.zip" opera-addon/
echo "✅ Opera Add-on: ${PACKAGE_NAME}-opera-addon.zip"

# Edge Add-on ZIP
zip -r "${PACKAGE_NAME}-edge-addon.zip" edge-addon/
echo "✅ Edge Add-on: ${PACKAGE_NAME}-edge-addon.zip"

# Universal package ZIP
zip -r "${PACKAGE_NAME}-universal.zip" universal-package/
echo "✅ Universal package: ${PACKAGE_NAME}-universal.zip"

# GitHub release ZIP
zip -r "${PACKAGE_NAME}-github-release.zip" github-release/
echo "✅ GitHub release: ${PACKAGE_NAME}-github-release.zip"

cd ..

# Create installation summary
echo "📋 Creating installation summary..."
cat > "${BUILD_DIR}/BROWSER_PACKAGES.md" << EOF
# 🌐 EcoStamp Cross-Browser Packages

## 📦 Available Packages

### 🟢 Chrome Web Store
- **File**: \`${PACKAGE_NAME}-chrome-web-store.zip\`
- **Use**: Submit to Chrome Web Store
- **Supports**: Chrome 88+

### 🦊 Firefox Add-ons
- **File**: \`${PACKAGE_NAME}-firefox-addon.zip\`
- **Use**: Submit to Firefox Add-ons (AMO)
- **Supports**: Firefox 88+

### 🔴 Opera Add-ons
- **File**: \`${PACKAGE_NAME}-opera-addon.zip\`
- **Use**: Submit to Opera Add-ons
- **Supports**: Opera (Chromium-based)

### 🔵 Microsoft Edge
- **File**: \`${PACKAGE_NAME}-edge-addon.zip\`
- **Use**: Submit to Edge Add-ons
- **Supports**: Edge 88+

### 🌐 Universal Package
- **File**: \`${PACKAGE_NAME}-universal.zip\`
- **Use**: Manual installation on any browser
- **Includes**: All manifests + installation guide

### 📦 GitHub Release
- **File**: \`${PACKAGE_NAME}-github-release.zip\`
- **Use**: Open source distribution
- **Includes**: Complete source code + documentation

## 🚀 Installation Instructions

See \`CROSS_BROWSER_INSTALL.md\` for detailed installation instructions for each browser.

## ✅ Browser Compatibility

- ✅ **Chrome 88+** - Full support
- ✅ **Firefox 88+** - Full support  
- ✅ **Edge 88+** - Full support
- ✅ **Opera** - Full support
- ✅ **Brave** - Full support
- ⚠️ **Safari** - Requires conversion

## 🌱 Universal AI Platform Support

All packages support ALL AI platforms:
- ChatGPT, Claude, Gemini, Perplexity, Poe, Character.AI, You.com, Hugging Face, and ANY AI platform!
EOF

echo ""
echo "🎉 Cross-browser build complete!"
echo ""
echo "📦 Packages created:"
echo "   • Chrome Web Store: ${BUILD_DIR}/${PACKAGE_NAME}-chrome-web-store.zip"
echo "   • Firefox Add-on: ${BUILD_DIR}/${PACKAGE_NAME}-firefox-addon.zip"
echo "   • Opera Add-on: ${BUILD_DIR}/${PACKAGE_NAME}-opera-addon.zip"
echo "   • Edge Add-on: ${BUILD_DIR}/${PACKAGE_NAME}-edge-addon.zip"
echo "   • Universal: ${BUILD_DIR}/${PACKAGE_NAME}-universal.zip"
echo "   • GitHub Release: ${BUILD_DIR}/${PACKAGE_NAME}-github-release.zip"
echo ""
echo "🌐 EcoStamp is now ready for ALL major browsers!"
echo "🌱 Making AI environmental impact visible everywhere!"

#!/usr/bin/env node

/**
 * Meta-Orchestration System Setup Script
 * 
 * Helps users set up the meta-orchestration system with:
 * - Configuration initialization
 * - Adapter setup and testing
 * - IDE integration setup
 * - Environment validation
 */

const fs = require('fs').promises;
const path = require('path');
const chalk = require('chalk');
const inquirer = require('inquirer');
const ora = require('ora');
const yaml = require('yaml');

class MetaOrchestrationSetup {
  constructor() {
    this.configDir = path.join(process.cwd(), 'ai-orchestration', 'config');
    this.metaConfigDir = path.join(process.cwd(), 'ai-orchestration', 'meta-orchestrator', 'config');
    this.sharedConfigFile = path.join(process.cwd(), '.ai-orchestration.yaml');
    
    this.setupConfig = {
      adapters: {},
      roles: {},
      ide: {},
      security: {},
      workflows: {}
    };
  }
  
  async run() {
    try {
      console.log(chalk.blue.bold('\n🎭 Meta-Orchestration System Setup\n'));
      
      // Welcome and overview
      await this.showWelcome();
      
      // Environment validation
      await this.validateEnvironment();
      
      // Configuration setup
      await this.setupConfiguration();
      
      // Adapter configuration
      await this.setupAdapters();
      
      // Role assignment setup
      await this.setupRoles();
      
      // IDE integration setup
      await this.setupIDEIntegration();
      
      // Security configuration
      await this.setupSecurity();
      
      // Workflow configuration
      await this.setupWorkflows();
      
      // Save all configurations
      await this.saveConfigurations();
      
      // Final setup and testing
      await this.finalizeSetup();
      
      console.log(chalk.green.bold('\n✅ Meta-Orchestration System setup completed successfully!\n'));
      
    } catch (error) {
      console.error(chalk.red('\n❌ Setup failed:', error.message));
      process.exit(1);
    }
  }
  
  async showWelcome() {
    console.log(chalk.cyan(`
Welcome to the Meta-Orchestration System Setup!

This setup will help you configure:
• AI Assistant Adapters (Cursor, Windsurf, Tabnine, GitHub Copilot, Ollama, etc.)
• Role-based Assignment (Analyzer, Generator, Completer, Validator, Documenter)
• Fallback Chains for instant failover
• Multi-IDE Integration (VS Code, JetBrains, Vim)
• Security and Compliance settings
• Custom Workflows

Let's get started!
    `));
    
    const { proceed } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'proceed',
        message: 'Ready to begin setup?',
        default: true
      }
    ]);
    
    if (!proceed) {
      console.log(chalk.yellow('Setup cancelled.'));
      process.exit(0);
    }
  }
  
  async validateEnvironment() {
    const spinner = ora('Validating environment...').start();
    
    try {
      // Check Node.js version
      const nodeVersion = process.version;
      const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
      
      if (majorVersion < 16) {
        throw new Error(`Node.js 16+ required, found ${nodeVersion}`);
      }
      
      // Create necessary directories
      await fs.mkdir(this.configDir, { recursive: true });
      await fs.mkdir(this.metaConfigDir, { recursive: true });
      await fs.mkdir(path.join(process.cwd(), 'logs'), { recursive: true });
      
      spinner.succeed('Environment validation completed');
      
    } catch (error) {
      spinner.fail('Environment validation failed');
      throw error;
    }
  }
  
  async setupConfiguration() {
    console.log(chalk.blue('\n📋 Configuration Setup'));
    
    const answers = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'enableMetaOrchestration',
        message: 'Enable Meta-Orchestration System?',
        default: true
      },
      {
        type: 'confirm',
        name: 'enableFallback',
        message: 'Enable automatic fallback between assistants?',
        default: true
      },
      {
        type: 'confirm',
        name: 'enableDeduplication',
        message: 'Enable output deduplication?',
        default: true
      },
      {
        type: 'confirm',
        name: 'enableContextSharing',
        message: 'Enable context sharing between assistants?',
        default: true
      },
      {
        type: 'list',
        name: 'logLevel',
        message: 'Select logging level:',
        choices: ['error', 'warn', 'info', 'debug'],
        default: 'info'
      }
    ]);
    
    this.setupConfig.main = {
      version: '1.0.0',
      metaOrchestration: {
        enabled: answers.enableMetaOrchestration,
        fallbackEnabled: answers.enableFallback,
        deduplicationEnabled: answers.enableDeduplication,
        contextSharingEnabled: answers.enableContextSharing
      },
      logging: {
        level: answers.logLevel,
        file: 'logs/meta-orchestration.log',
        console: true
      }
    };
  }
  
  async setupAdapters() {
    console.log(chalk.blue('\n🔌 Adapter Configuration'));
    
    const availableAdapters = [
      { name: 'Augment Code', value: 'augment-code', description: 'Primary codebase analyzer' },
      { name: 'Cursor', value: 'cursor', description: 'AI-powered code generation' },
      { name: 'Windsurf', value: 'windsurf', description: 'Multi-file editing' },
      { name: 'Tabnine', value: 'tabnine', description: 'Inline completion' },
      { name: 'GitHub Copilot', value: 'github-copilot', description: 'AI pair programmer' },
      { name: 'Qodo', value: 'qodo', description: 'Testing and validation' },
      { name: 'Ollama', value: 'ollama', description: 'Local LLM runner' },
      { name: 'Cline', value: 'cline', description: 'Local AI assistant' },
      { name: 'Continue', value: 'continue', description: 'Open source assistant' },
      { name: 'Aider', value: 'aider', description: 'AI pair programming' }
    ];
    
    const { selectedAdapters } = await inquirer.prompt([
      {
        type: 'checkbox',
        name: 'selectedAdapters',
        message: 'Select adapters to enable:',
        choices: availableAdapters,
        default: ['augment-code', 'cursor', 'windsurf', 'tabnine']
      }
    ]);
    
    // Configure each selected adapter
    for (const adapterId of selectedAdapters) {
      const adapter = availableAdapters.find(a => a.value === adapterId);
      console.log(chalk.cyan(`\nConfiguring ${adapter.name}...`));
      
      const config = await this.configureAdapter(adapterId);
      this.setupConfig.adapters[adapterId] = config;
    }
  }
  
  async configureAdapter(adapterId) {
    const baseConfig = {
      enabled: true,
      timeout: 30000,
      retries: 3
    };
    
    switch (adapterId) {
      case 'augment-code':
        return {
          ...baseConfig,
          apiEndpoint: 'http://localhost:8080',
          features: ['analysis', 'context', 'search', 'validation']
        };
        
      case 'cursor':
        const { cursorApiKey } = await inquirer.prompt([
          {
            type: 'password',
            name: 'cursorApiKey',
            message: 'Cursor API Key (optional):',
            mask: '*'
          }
        ]);
        
        return {
          ...baseConfig,
          apiKey: cursorApiKey || '${CURSOR_API_KEY}',
          baseUrl: 'https://api.cursor.sh',
          model: 'claude-3.5-sonnet',
          timeout: 60000
        };
        
      case 'ollama':
        const { ollamaUrl, ollamaModels } = await inquirer.prompt([
          {
            type: 'input',
            name: 'ollamaUrl',
            message: 'Ollama server URL:',
            default: 'http://localhost:11434'
          },
          {
            type: 'input',
            name: 'ollamaModels',
            message: 'Preferred models (comma-separated):',
            default: 'codellama,deepseek-coder,mistral'
          }
        ]);
        
        return {
          ...baseConfig,
          baseUrl: ollamaUrl,
          models: ollamaModels.split(',').map(m => m.trim()),
          timeout: 60000
        };
        
      default:
        return baseConfig;
    }
  }
  
  async setupRoles() {
    console.log(chalk.blue('\n🎭 Role Assignment Setup'));
    
    const roles = ['analyzer', 'generator', 'completer', 'validator', 'documenter'];
    const enabledAdapters = Object.keys(this.setupConfig.adapters);
    
    const assignments = {};
    
    for (const role of roles) {
      console.log(chalk.cyan(`\nConfiguring ${role} role:`));
      
      const { primary, fallbacks } = await inquirer.prompt([
        {
          type: 'list',
          name: 'primary',
          message: `Select primary assistant for ${role}:`,
          choices: enabledAdapters,
          default: this.getDefaultPrimary(role, enabledAdapters)
        },
        {
          type: 'checkbox',
          name: 'fallbacks',
          message: `Select fallback assistants for ${role}:`,
          choices: (answers) => enabledAdapters.filter(a => a !== answers.primary),
          default: this.getDefaultFallbacks(role, enabledAdapters)
        }
      ]);
      
      assignments[role] = { primary, fallbacks };
    }
    
    this.setupConfig.roles = { assignments };
  }
  
  getDefaultPrimary(role, adapters) {
    const defaults = {
      analyzer: 'augment-code',
      generator: 'cursor',
      completer: 'tabnine',
      validator: 'qodo',
      documenter: 'cursor'
    };
    
    return adapters.includes(defaults[role]) ? defaults[role] : adapters[0];
  }
  
  getDefaultFallbacks(role, adapters) {
    const defaults = {
      analyzer: ['continue', 'ollama'],
      generator: ['windsurf', 'github-copilot', 'cline'],
      completer: ['github-copilot', 'cursor'],
      validator: ['augment-code', 'windsurf'],
      documenter: ['windsurf', 'github-copilot', 'ollama']
    };
    
    return defaults[role]?.filter(a => adapters.includes(a)) || [];
  }
  
  async setupIDEIntegration() {
    console.log(chalk.blue('\n💻 IDE Integration Setup'));
    
    const { ides } = await inquirer.prompt([
      {
        type: 'checkbox',
        name: 'ides',
        message: 'Select IDEs to integrate with:',
        choices: [
          { name: 'VS Code', value: 'vscode' },
          { name: 'JetBrains IDEs', value: 'jetbrains' },
          { name: 'Vim/Neovim', value: 'vim' }
        ],
        default: ['vscode']
      }
    ]);
    
    this.setupConfig.ide = {};
    
    for (const ide of ides) {
      this.setupConfig.ide[ide] = {
        enabled: true,
        autoActivate: true,
        showStatusBar: true,
        showNotifications: true
      };
    }
    
    this.setupConfig.ide.sync = {
      enabled: true,
      realTime: true,
      conflictResolution: 'merge'
    };
  }
  
  async setupSecurity() {
    console.log(chalk.blue('\n🔒 Security Configuration'));
    
    const answers = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'enableEncryption',
        message: 'Enable configuration encryption?',
        default: true
      },
      {
        type: 'confirm',
        name: 'enableIPProtection',
        message: 'Enable IP protection (no training on your code)?',
        default: true
      },
      {
        type: 'checkbox',
        name: 'compliance',
        message: 'Select compliance standards:',
        choices: ['SOC2', 'GDPR', 'HIPAA'],
        default: ['SOC2', 'GDPR']
      }
    ]);
    
    this.setupConfig.security = {
      encryption: { enabled: answers.enableEncryption },
      ipProtection: { enabled: answers.enableIPProtection },
      compliance: {
        soc2: answers.compliance.includes('SOC2'),
        gdpr: answers.compliance.includes('GDPR'),
        hipaa: answers.compliance.includes('HIPAA')
      }
    };
  }
  
  async setupWorkflows() {
    console.log(chalk.blue('\n🔄 Workflow Configuration'));
    
    const { workflows } = await inquirer.prompt([
      {
        type: 'checkbox',
        name: 'workflows',
        message: 'Select default workflows to enable:',
        choices: [
          { name: 'Feature Implementation', value: 'feature-implementation' },
          { name: 'Bug Fix', value: 'bug-fix' },
          { name: 'Code Review', value: 'code-review' },
          { name: 'Refactoring', value: 'refactoring' },
          { name: 'Testing', value: 'testing' }
        ],
        default: ['feature-implementation', 'bug-fix', 'code-review']
      }
    ]);
    
    this.setupConfig.workflows = {
      enabled: workflows,
      custom: {}
    };
  }
  
  async saveConfigurations() {
    const spinner = ora('Saving configurations...').start();
    
    try {
      // Save main configuration
      const mainConfigPath = path.join(this.metaConfigDir, 'meta-orchestration.yaml');
      await fs.writeFile(mainConfigPath, yaml.stringify(this.setupConfig.main, { indent: 2 }));
      
      // Save role assignments
      const rolesConfigPath = path.join(this.metaConfigDir, 'role-assignments.yaml');
      await fs.writeFile(rolesConfigPath, yaml.stringify(this.setupConfig.roles, { indent: 2 }));
      
      // Save adapter configurations
      const adaptersConfigPath = path.join(this.metaConfigDir, 'adapters.yaml');
      await fs.writeFile(adaptersConfigPath, yaml.stringify(this.setupConfig.adapters, { indent: 2 }));
      
      // Save IDE configuration
      const ideConfigPath = path.join(this.metaConfigDir, 'ide-integration.yaml');
      await fs.writeFile(ideConfigPath, yaml.stringify(this.setupConfig.ide, { indent: 2 }));
      
      // Save security configuration
      const securityConfigPath = path.join(this.metaConfigDir, 'security.yaml');
      await fs.writeFile(securityConfigPath, yaml.stringify(this.setupConfig.security, { indent: 2 }));
      
      // Save workflow configuration
      const workflowsConfigPath = path.join(this.metaConfigDir, 'workflows.yaml');
      await fs.writeFile(workflowsConfigPath, yaml.stringify(this.setupConfig.workflows, { indent: 2 }));
      
      // Create shared project configuration
      const sharedConfig = {
        version: '1.0.0',
        project: {
          name: path.basename(process.cwd()),
          type: 'auto-detect'
        },
        roles: this.setupConfig.roles.assignments,
        preferences: {
          skipDocumentation: false,
          skipValidation: false,
          preferredComplexity: 'medium'
        }
      };
      
      await fs.writeFile(this.sharedConfigFile, yaml.stringify(sharedConfig, { indent: 2 }));
      
      spinner.succeed('Configurations saved successfully');
      
    } catch (error) {
      spinner.fail('Failed to save configurations');
      throw error;
    }
  }
  
  async finalizeSetup() {
    console.log(chalk.blue('\n🚀 Finalizing Setup'));
    
    const { testSetup } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'testSetup',
        message: 'Test the setup now?',
        default: true
      }
    ]);
    
    if (testSetup) {
      const spinner = ora('Testing meta-orchestration system...').start();
      
      try {
        // Import and test the meta-orchestrator
        const { MetaOrchestrator } = require('./index');
        const metaOrchestrator = new MetaOrchestrator();
        
        await metaOrchestrator.initialize();
        
        // Test basic functionality
        const status = metaOrchestrator.getSystemStatus();
        
        if (status.status === 'ready') {
          spinner.succeed('Meta-orchestration system test passed');
        } else {
          throw new Error(`System status: ${status.status}`);
        }
        
        await metaOrchestrator.shutdown();
        
      } catch (error) {
        spinner.fail('Setup test failed');
        console.log(chalk.yellow(`⚠️ Test failed: ${error.message}`));
        console.log(chalk.blue('You can still use the system, but some features may not work correctly.'));
      }
    }
    
    console.log(chalk.green('\n🎉 Setup completed! You can now use:'));
    console.log(chalk.cyan('  npm run start           - Start the meta-orchestration system'));
    console.log(chalk.cyan('  npm run execute         - Execute requests interactively'));
    console.log(chalk.cyan('  npm run dashboard       - Launch web dashboard'));
    console.log(chalk.cyan('  npm run status          - Check system status'));
  }
}

// Run setup if called directly
if (require.main === module) {
  const setup = new MetaOrchestrationSetup();
  setup.run().catch(error => {
    console.error(chalk.red('Setup failed:', error.message));
    process.exit(1);
  });
}

module.exports = MetaOrchestrationSetup;

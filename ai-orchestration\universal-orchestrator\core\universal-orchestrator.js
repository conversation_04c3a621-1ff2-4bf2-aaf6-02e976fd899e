#!/usr/bin/env node

/**
 * Universal Orchestration Layer
 *
 * Unifies Thread-Merge Orchestration and Code Orchestration into a single
 * universal orchestration layer with:
 * - Centralized task routing and role assignment
 * - Universal deduplication and fallback
 * - Shared context and state management
 * - Integration with Meta-Orchestration System
 * - Darwin Gödel Machine (DGM) self-improvement layer
 */

const EventEmitter = require('events');
const path = require('path');
const fs = require('fs').promises;
const chalk = require('chalk');
const { v4: uuidv4 } = require('uuid');

// Import existing orchestration systems
const MetaOrchestrator = require('../../meta-orchestrator/core/meta-orchestrator');
const ThreadMergingOrchestrator = require('../../thread-merging-orchestrator/src/orchestrator');
const AIOrchestrator = require('../../orchestrator');

// Import universal orchestration components
const UniversalTaskRouter = require('./universal-task-router');
const UniversalRoleManager = require('./universal-role-manager');
const UniversalContextManager = require('./universal-context-manager');
const UniversalDeduplicationEngine = require('./universal-deduplication-engine');
const MetaOrchestrationAdapter = require('./meta-orchestration-adapter');
const DGMLayer = require('./dgm-layer');

class UniversalOrchestrator extends EventEmitter {
  constructor(options = {}) {
    super();

    this.id = uuidv4();
    this.startTime = Date.now();
    this.options = options;

    // Core universal orchestration components
    this.taskRouter = new UniversalTaskRouter(this);
    this.roleManager = new UniversalRoleManager(this);
    this.contextManager = new UniversalContextManager(this);
    this.deduplicationEngine = new UniversalDeduplicationEngine(this);
    this.metaOrchestrationAdapter = new MetaOrchestrationAdapter(this);
    this.dgmLayer = new DGMLayer(this);

    // Integrated orchestration systems
    this.metaOrchestrator = null;
    this.threadMergingOrchestrator = null;
    this.aiOrchestrator = null;

    // Universal state management
    this.state = {
      status: 'initializing',
      activeTasks: new Map(),
      activeThreads: new Map(),
      universalRoles: new Map(),
      taskHistory: [],
      threadHistory: [],
      orchestrationMetrics: {
        totalTasks: 0,
        totalThreads: 0,
        successfulTasks: 0,
        successfulThreads: 0,
        averageTaskTime: 0,
        averageThreadTime: 0,
        fallbackActivations: 0,
        deduplicationEvents: 0
      }
    };

    // Universal task and thread types
    this.universalTaskTypes = {
      // Code-centric tasks
      'code-analysis': { orchestrator: 'meta', roles: ['analyzer'] },
      'code-generation': { orchestrator: 'meta', roles: ['generator'] },
      'code-completion': { orchestrator: 'meta', roles: ['completer'] },
      'code-validation': { orchestrator: 'meta', roles: ['validator'] },
      'code-documentation': { orchestrator: 'meta', roles: ['documenter'] },
      'code-refactoring': { orchestrator: 'meta', roles: ['analyzer', 'generator', 'validator'] },
      'feature-implementation': { orchestrator: 'meta', roles: ['analyzer', 'generator', 'validator', 'documenter'] },
      'bug-fix': { orchestrator: 'meta', roles: ['analyzer', 'generator', 'validator'] },

      // Thread-centric tasks
      'thread-merge': { orchestrator: 'thread', roles: ['analyzer', 'generator'] },
      'thread-analysis': { orchestrator: 'thread', roles: ['analyzer'] },
      'thread-synthesis': { orchestrator: 'thread', roles: ['generator', 'documenter'] },
      'multi-agent-coordination': { orchestrator: 'thread', roles: ['analyzer', 'generator'] },
      'conversation-orchestration': { orchestrator: 'thread', roles: ['analyzer', 'generator', 'documenter'] },

      // Hybrid tasks (both code and thread)
      'project-planning': { orchestrator: 'universal', roles: ['analyzer', 'generator', 'documenter'] },
      'architecture-design': { orchestrator: 'universal', roles: ['analyzer', 'generator', 'validator', 'documenter'] },
      'code-review': { orchestrator: 'universal', roles: ['analyzer', 'validator', 'documenter'] },
      'research-and-implementation': { orchestrator: 'universal', roles: ['analyzer', 'generator', 'validator', 'documenter'] },
      'multi-modal-analysis': { orchestrator: 'universal', roles: ['analyzer', 'generator', 'documenter'] }
    };

    // Universal role definitions (unified from both orchestration types)
    this.universalRoles = {
      analyzer: {
        name: 'Universal Analyzer',
        description: 'Analyzes code, threads, conversations, and project structure',
        capabilities: [
          'codebase-analysis', 'thread-analysis', 'conversation-analysis',
          'dependency-mapping', 'architecture-review', 'pattern-recognition',
          'context-extraction', 'requirement-analysis', 'risk-assessment'
        ],
        inputTypes: ['code', 'threads', 'conversations', 'documents', 'project-structure'],
        outputTypes: ['analysis-report', 'dependency-graph', 'recommendations', 'insights'],
        priority: 1
      },

      generator: {
        name: 'Universal Generator',
        description: 'Generates code, responses, documentation, and solutions',
        capabilities: [
          'code-generation', 'response-generation', 'content-creation',
          'solution-synthesis', 'template-creation', 'boilerplate-generation',
          'thread-continuation', 'conversation-enhancement'
        ],
        inputTypes: ['requirements', 'specifications', 'context', 'threads', 'prompts'],
        outputTypes: ['code', 'responses', 'documents', 'solutions', 'templates'],
        priority: 2
      },

      completer: {
        name: 'Universal Completer',
        description: 'Provides completions for code, conversations, and content',
        capabilities: [
          'code-completion', 'conversation-completion', 'content-completion',
          'suggestion-generation', 'auto-enhancement', 'context-aware-completion'
        ],
        inputTypes: ['partial-code', 'partial-conversations', 'incomplete-content'],
        outputTypes: ['completions', 'suggestions', 'enhancements'],
        priority: 3
      },

      validator: {
        name: 'Universal Validator',
        description: 'Validates code, logic, consistency, and quality',
        capabilities: [
          'code-validation', 'logic-validation', 'consistency-checking',
          'quality-assessment', 'error-detection', 'compliance-checking',
          'thread-coherence-validation', 'conversation-quality-assessment'
        ],
        inputTypes: ['code', 'logic', 'conversations', 'documents', 'workflows'],
        outputTypes: ['validation-results', 'quality-reports', 'recommendations'],
        priority: 4
      },

      documenter: {
        name: 'Universal Documenter',
        description: 'Documents code, processes, conversations, and knowledge',
        capabilities: [
          'code-documentation', 'process-documentation', 'conversation-summarization',
          'knowledge-extraction', 'tutorial-creation', 'explanation-generation',
          'thread-summarization', 'insight-documentation'
        ],
        inputTypes: ['code', 'processes', 'conversations', 'threads', 'knowledge'],
        outputTypes: ['documentation', 'summaries', 'explanations', 'tutorials'],
        priority: 5
      },

      coordinator: {
        name: 'Universal Coordinator',
        description: 'Coordinates multiple agents, tasks, and orchestration systems',
        capabilities: [
          'multi-agent-coordination', 'task-orchestration', 'resource-management',
          'workflow-coordination', 'system-integration', 'conflict-resolution',
          'priority-management', 'load-balancing'
        ],
        inputTypes: ['tasks', 'agents', 'workflows', 'resources', 'constraints'],
        outputTypes: ['coordination-plans', 'task-assignments', 'resource-allocations'],
        priority: 0
      }
    };
  }

  async initialize() {
    try {
      console.log(chalk.blue('🌐 Initializing Universal Orchestration Layer...'));

      // Initialize core components
      await this.initializeCoreComponents();

      // Initialize and integrate existing orchestration systems
      await this.initializeOrchestrationSystems();

      // Setup universal task routing
      await this.setupUniversalTaskRouting();

      // Initialize meta-orchestration adapter
      await this.metaOrchestrationAdapter.initialize();

      // Initialize Darwin Gödel Machine layer
      await this.dgmLayer.initialize();

      this.state.status = 'ready';
      this.emit('ready');

      console.log(chalk.green('✅ Universal Orchestration Layer initialized successfully'));

    } catch (error) {
      console.error(chalk.red('❌ Failed to initialize Universal Orchestration Layer:'), error);
      this.state.status = 'error';
      this.emit('error', error);
      throw error;
    }
  }

  async initializeCoreComponents() {
    try {
      console.log(chalk.blue('🔧 Initializing core universal components...'));

      // Initialize universal task router
      await this.taskRouter.initialize();

      // Initialize universal role manager
      await this.roleManager.initialize(this.universalRoles);

      // Initialize universal context manager
      await this.contextManager.initialize();

      // Initialize universal deduplication engine
      await this.deduplicationEngine.initialize();

      console.log(chalk.green('✅ Core universal components initialized'));

    } catch (error) {
      console.error(chalk.red('❌ Failed to initialize core components:'), error);
      throw error;
    }
  }

  async initializeOrchestrationSystems() {
    try {
      console.log(chalk.blue('🎭 Initializing orchestration systems...'));

      // Initialize Meta-Orchestrator
      this.metaOrchestrator = new MetaOrchestrator({
        universalIntegration: true,
        universalOrchestrator: this
      });
      await this.metaOrchestrator.initialize();

      // Initialize Thread Merging Orchestrator
      this.threadMergingOrchestrator = new ThreadMergingOrchestrator({
        universalIntegration: true,
        universalOrchestrator: this
      });
      await this.threadMergingOrchestrator.initialize();

      // Initialize AI Orchestrator
      this.aiOrchestrator = new AIOrchestrator();
      await this.aiOrchestrator.initialize();

      console.log(chalk.green('✅ Orchestration systems integrated'));

    } catch (error) {
      console.error(chalk.red('❌ Failed to initialize orchestration systems:'), error);
      throw error;
    }
  }

  async setupUniversalTaskRouting() {
    try {
      console.log(chalk.blue('🗺️ Setting up universal task routing...'));

      // Register task types with appropriate orchestrators
      for (const [taskType, config] of Object.entries(this.universalTaskTypes)) {
        await this.taskRouter.registerTaskType(taskType, config);
      }

      // Setup cross-orchestrator communication
      await this.setupCrossOrchestratorCommunication();

      console.log(chalk.green('✅ Universal task routing configured'));

    } catch (error) {
      console.error(chalk.red('❌ Failed to setup task routing:'), error);
      throw error;
    }
  }

  async setupCrossOrchestratorCommunication() {
    // Setup event listeners for cross-orchestrator communication

    // Meta-Orchestrator events
    this.metaOrchestrator.on('taskCompleted', (result) => {
      this.handleOrchestratorResult('meta', result);
    });

    this.metaOrchestrator.on('taskFailed', (error) => {
      this.handleOrchestratorError('meta', error);
    });

    // Thread Merging Orchestrator events
    this.threadMergingOrchestrator.on('threadMerged', (result) => {
      this.handleOrchestratorResult('thread', result);
    });

    this.threadMergingOrchestrator.on('mergeFailed', (error) => {
      this.handleOrchestratorError('thread', error);
    });

    // AI Orchestrator events
    this.aiOrchestrator.on('analysisCompleted', (result) => {
      this.handleOrchestratorResult('ai', result);
    });

    this.aiOrchestrator.on('generationCompleted', (result) => {
      this.handleOrchestratorResult('ai', result);
    });
  }

  /**
   * Main universal orchestration method
   * Routes tasks to appropriate orchestrator based on task type and context
   */
  async orchestrate(request) {
    const requestId = uuidv4();
    const startTime = Date.now();

    try {
      console.log(chalk.blue(`🌐 Processing universal request ${requestId}`));

      // Analyze request to determine task type and orchestration strategy
      const taskAnalysis = await this.analyzeRequest(request);

      // Route to appropriate orchestrator(s)
      const result = await this.routeTask(requestId, taskAnalysis, request);

      // Apply universal deduplication
      const deduplicatedResult = await this.deduplicationEngine.processResult(result);

      // Update metrics
      const duration = Date.now() - startTime;
      this.updateMetrics(requestId, true, duration, taskAnalysis.taskType);

      console.log(chalk.green(`✅ Universal request ${requestId} completed in ${duration}ms`));

      return deduplicatedResult;

    } catch (error) {
      const duration = Date.now() - startTime;
      this.updateMetrics(requestId, false, duration, 'unknown');

      console.error(chalk.red(`❌ Universal request ${requestId} failed:`, error));

      // Attempt universal recovery
      return await this.handleUniversalFailure(requestId, request, error);
    }
  }

  /**
   * Analyze request to determine task type and orchestration strategy
   */
  async analyzeRequest(request) {
    const analysis = {
      taskType: 'unknown',
      orchestrator: 'universal',
      roles: [],
      complexity: 'medium',
      isHybrid: false,
      requiresThreadMerging: false,
      requiresCodeGeneration: false,
      context: {}
    };

    // Determine task type based on request content
    analysis.taskType = this.determineTaskType(request);

    // Get task configuration
    const taskConfig = this.universalTaskTypes[analysis.taskType];
    if (taskConfig) {
      analysis.orchestrator = taskConfig.orchestrator;
      analysis.roles = taskConfig.roles;
    }

    // Analyze complexity
    analysis.complexity = this.analyzeComplexity(request);

    // Check if hybrid orchestration is needed
    analysis.isHybrid = this.requiresHybridOrchestration(request);

    // Check specific requirements
    analysis.requiresThreadMerging = this.requiresThreadMerging(request);
    analysis.requiresCodeGeneration = this.requiresCodeGeneration(request);

    // Extract context
    analysis.context = await this.contextManager.extractContext(request);

    return analysis;
  }

  determineTaskType(request) {
    const description = (request.description || '').toLowerCase();
    const type = request.type || '';

    // Code-centric task detection
    if (type === 'code-analysis' || description.includes('analyze code')) {
      return 'code-analysis';
    }
    if (type === 'code-generation' || description.includes('generate code') || description.includes('implement')) {
      return 'code-generation';
    }
    if (type === 'feature' || description.includes('feature')) {
      return 'feature-implementation';
    }
    if (type === 'bug-fix' || description.includes('fix') || description.includes('bug')) {
      return 'bug-fix';
    }
    if (type === 'refactor' || description.includes('refactor')) {
      return 'code-refactoring';
    }
    if (type === 'review' || description.includes('review')) {
      return 'code-review';
    }

    // Thread-centric task detection
    if (type === 'thread-merge' || description.includes('merge thread')) {
      return 'thread-merge';
    }
    if (description.includes('conversation') || description.includes('chat')) {
      return 'conversation-orchestration';
    }
    if (description.includes('multi-agent') || description.includes('coordinate')) {
      return 'multi-agent-coordination';
    }

    // Hybrid task detection
    if (description.includes('plan') || description.includes('planning')) {
      return 'project-planning';
    }
    if (description.includes('architecture') || description.includes('design')) {
      return 'architecture-design';
    }
    if (description.includes('research')) {
      return 'research-and-implementation';
    }

    // Default to project planning for complex requests
    return 'project-planning';
  }

  analyzeComplexity(request) {
    const description = (request.description || '').toLowerCase();
    const files = request.files || [];

    // High complexity indicators
    if (description.includes('complex') || description.includes('enterprise') ||
        description.includes('architecture') || files.length > 10) {
      return 'high';
    }

    // Low complexity indicators
    if (description.includes('simple') || description.includes('basic') ||
        description.includes('quick') || files.length <= 1) {
      return 'low';
    }

    return 'medium';
  }

  requiresHybridOrchestration(request) {
    const description = (request.description || '').toLowerCase();

    // Hybrid indicators: tasks that need both code and thread orchestration
    return description.includes('plan') ||
           description.includes('architecture') ||
           description.includes('research') ||
           description.includes('multi-modal') ||
           (request.includeThreads && request.includeCode);
  }

  requiresThreadMerging(request) {
    return request.threads && Array.isArray(request.threads) && request.threads.length > 1;
  }

  requiresCodeGeneration(request) {
    const description = (request.description || '').toLowerCase();
    return description.includes('implement') ||
           description.includes('generate') ||
           description.includes('create') ||
           description.includes('build');
  }

  /**
   * Route task to appropriate orchestrator(s)
   */
  async routeTask(requestId, analysis, request) {
    const { orchestrator, isHybrid, taskType } = analysis;

    if (isHybrid) {
      // Hybrid orchestration: coordinate multiple orchestrators
      return await this.executeHybridOrchestration(requestId, analysis, request);
    }

    switch (orchestrator) {
      case 'meta':
        return await this.executeMetaOrchestration(requestId, analysis, request);
      case 'thread':
        return await this.executeThreadOrchestration(requestId, analysis, request);
      case 'universal':
        return await this.executeUniversalOrchestration(requestId, analysis, request);
      default:
        throw new Error(`Unknown orchestrator: ${orchestrator}`);
    }
  }

  async executeMetaOrchestration(requestId, analysis, request) {
    console.log(chalk.cyan(`🎭 Executing meta-orchestration for ${analysis.taskType}`));

    // Prepare request for meta-orchestrator
    const metaRequest = {
      ...request,
      universalRequestId: requestId,
      taskType: analysis.taskType,
      roles: analysis.roles,
      context: analysis.context
    };

    return await this.metaOrchestrator.orchestrate(metaRequest);
  }

  async executeThreadOrchestration(requestId, analysis, request) {
    console.log(chalk.cyan(`🧵 Executing thread orchestration for ${analysis.taskType}`));

    // Prepare request for thread merging orchestrator
    const threadRequest = {
      ...request,
      universalRequestId: requestId,
      taskType: analysis.taskType,
      context: analysis.context
    };

    return await this.threadMergingOrchestrator.orchestrate(threadRequest);
  }

  async executeUniversalOrchestration(requestId, analysis, request) {
    console.log(chalk.cyan(`🌐 Executing universal orchestration for ${analysis.taskType}`));

    // For universal orchestration, we coordinate multiple systems
    const results = new Map();

    // Execute each role with the best available orchestrator
    for (const role of analysis.roles) {
      const roleResult = await this.executeRoleWithBestOrchestrator(role, request, analysis);
      results.set(role, roleResult);
    }

    // Aggregate results
    return await this.aggregateUniversalResults(results, analysis);
  }

  async executeHybridOrchestration(requestId, analysis, request) {
    console.log(chalk.cyan(`🔀 Executing hybrid orchestration for ${analysis.taskType}`));

    const results = new Map();

    // Phase 1: Analysis and Planning (use both thread and meta orchestration)
    if (analysis.roles.includes('analyzer')) {
      const analysisResults = await Promise.all([
        this.executeMetaOrchestration(requestId + '-meta', analysis, request),
        analysis.requiresThreadMerging ?
          this.executeThreadOrchestration(requestId + '-thread', analysis, request) :
          Promise.resolve(null)
      ]);

      // Merge analysis results
      const mergedAnalysis = await this.mergeAnalysisResults(analysisResults);
      results.set('analysis', mergedAnalysis);
    }

    // Phase 2: Generation (use meta-orchestration with thread context)
    if (analysis.roles.includes('generator')) {
      const generationRequest = {
        ...request,
        context: {
          ...analysis.context,
          analysisResults: results.get('analysis')
        }
      };

      const generationResult = await this.executeMetaOrchestration(
        requestId + '-generation',
        analysis,
        generationRequest
      );
      results.set('generation', generationResult);
    }

    // Phase 3: Validation and Documentation (parallel execution)
    const finalPhases = [];

    if (analysis.roles.includes('validator')) {
      finalPhases.push(
        this.executeMetaOrchestration(requestId + '-validation', analysis, {
          ...request,
          context: {
            ...analysis.context,
            generationResults: results.get('generation')
          }
        })
      );
    }

    if (analysis.roles.includes('documenter')) {
      finalPhases.push(
        this.executeMetaOrchestration(requestId + '-documentation', analysis, {
          ...request,
          context: {
            ...analysis.context,
            allResults: Object.fromEntries(results)
          }
        })
      );
    }

    const finalResults = await Promise.all(finalPhases);

    if (finalResults[0]) results.set('validation', finalResults[0]);
    if (finalResults[1]) results.set('documentation', finalResults[1]);

    return await this.aggregateUniversalResults(results, analysis);
  }

  async executeRoleWithBestOrchestrator(role, request, analysis) {
    // Determine best orchestrator for this specific role
    const bestOrchestrator = this.selectBestOrchestratorForRole(role, analysis);

    const roleRequest = {
      ...request,
      role,
      context: analysis.context
    };

    switch (bestOrchestrator) {
      case 'meta':
        return await this.metaOrchestrator.executeRole(role, roleRequest);
      case 'thread':
        return await this.threadMergingOrchestrator.executeRole(role, roleRequest);
      default:
        throw new Error(`No orchestrator available for role: ${role}`);
    }
  }

  selectBestOrchestratorForRole(role, analysis) {
    // Role-specific orchestrator selection logic
    const roleOrchestratorPreferences = {
      analyzer: analysis.requiresThreadMerging ? 'thread' : 'meta',
      generator: analysis.requiresCodeGeneration ? 'meta' : 'thread',
      completer: 'meta',
      validator: 'meta',
      documenter: analysis.isHybrid ? 'meta' : 'thread',
      coordinator: 'universal'
    };

    return roleOrchestratorPreferences[role] || 'meta';
  }

  async mergeAnalysisResults(analysisResults) {
    const [metaResult, threadResult] = analysisResults;

    // Merge results from both orchestrators
    const mergedResult = {
      type: 'merged-analysis',
      timestamp: Date.now(),
      sources: []
    };

    if (metaResult) {
      mergedResult.codeAnalysis = metaResult;
      mergedResult.sources.push('meta-orchestrator');
    }

    if (threadResult) {
      mergedResult.threadAnalysis = threadResult;
      mergedResult.sources.push('thread-orchestrator');
    }

    // Apply intelligent merging logic
    mergedResult.insights = await this.extractMergedInsights(metaResult, threadResult);
    mergedResult.recommendations = await this.generateMergedRecommendations(metaResult, threadResult);

    return mergedResult;
  }

  async extractMergedInsights(metaResult, threadResult) {
    const insights = [];

    // Extract insights from meta-orchestrator result
    if (metaResult && metaResult.analysis) {
      insights.push(...(metaResult.analysis.insights || []));
    }

    // Extract insights from thread orchestrator result
    if (threadResult && threadResult.insights) {
      insights.push(...threadResult.insights);
    }

    // Deduplicate and prioritize insights
    return await this.deduplicationEngine.deduplicateInsights(insights);
  }

  async generateMergedRecommendations(metaResult, threadResult) {
    const recommendations = [];

    // Combine recommendations from both sources
    if (metaResult && metaResult.recommendations) {
      recommendations.push(...metaResult.recommendations);
    }

    if (threadResult && threadResult.recommendations) {
      recommendations.push(...threadResult.recommendations);
    }

    // Generate unified recommendations
    return await this.deduplicationEngine.mergeRecommendations(recommendations);
  }

  async aggregateUniversalResults(results, analysis) {
    const aggregatedResult = {
      type: 'universal-orchestration-result',
      taskType: analysis.taskType,
      timestamp: Date.now(),
      orchestrationStrategy: analysis.isHybrid ? 'hybrid' : analysis.orchestrator,
      results: Object.fromEntries(results),
      summary: {},
      metrics: {}
    };

    // Generate summary
    aggregatedResult.summary = await this.generateResultSummary(results, analysis);

    // Calculate metrics
    aggregatedResult.metrics = this.calculateResultMetrics(results);

    return aggregatedResult;
  }

  async generateResultSummary(results, analysis) {
    const summary = {
      completedRoles: Array.from(results.keys()),
      totalRoles: analysis.roles.length,
      successRate: results.size / analysis.roles.length,
      keyOutcomes: []
    };

    // Extract key outcomes from each role
    for (const [role, result] of results) {
      if (result && result.result) {
        summary.keyOutcomes.push({
          role,
          outcome: this.extractKeyOutcome(result, role)
        });
      }
    }

    return summary;
  }

  extractKeyOutcome(result, role) {
    // Extract the most important outcome for each role
    switch (role) {
      case 'analyzer':
        return result.result.analysis || result.result.insights || 'Analysis completed';
      case 'generator':
        return result.result.code || result.result.content || 'Generation completed';
      case 'validator':
        return result.result.validation || result.result.quality || 'Validation completed';
      case 'documenter':
        return result.result.documentation || result.result.summary || 'Documentation completed';
      default:
        return 'Task completed';
    }
  }

  calculateResultMetrics(results) {
    const metrics = {
      totalResults: results.size,
      successfulResults: 0,
      averageResponseTime: 0,
      totalResponseTime: 0
    };

    for (const [role, result] of results) {
      if (result && result.success !== false) {
        metrics.successfulResults++;
      }

      if (result && result.duration) {
        metrics.totalResponseTime += result.duration;
      }
    }

    if (metrics.totalResults > 0) {
      metrics.averageResponseTime = metrics.totalResponseTime / metrics.totalResults;
    }

    return metrics;
  }

  handleOrchestratorResult(orchestratorType, result) {
    console.log(chalk.green(`✅ ${orchestratorType} orchestrator completed task`));

    // Update universal metrics
    this.state.orchestrationMetrics.totalTasks++;
    this.state.orchestrationMetrics.successfulTasks++;

    this.emit('orchestratorResult', { orchestratorType, result });
  }

  handleOrchestratorError(orchestratorType, error) {
    console.error(chalk.red(`❌ ${orchestratorType} orchestrator failed:`, error.message));

    // Update universal metrics
    this.state.orchestrationMetrics.totalTasks++;

    this.emit('orchestratorError', { orchestratorType, error });
  }

  async handleUniversalFailure(requestId, request, error) {
    console.log(chalk.yellow(`🔄 Attempting universal recovery for request ${requestId}`));

    try {
      // Try with simplified orchestration
      const simplifiedAnalysis = {
        taskType: 'code-generation',
        orchestrator: 'meta',
        roles: ['generator'],
        complexity: 'low',
        isHybrid: false
      };

      const recoveryResult = await this.routeTask(requestId + '-recovery', simplifiedAnalysis, request);

      return {
        success: true,
        result: recoveryResult,
        recovery: true,
        originalError: error.message
      };

    } catch (recoveryError) {
      console.error(chalk.red('Universal recovery failed:', recoveryError.message));

      return {
        success: false,
        error: error.message,
        recoveryError: recoveryError.message,
        requestId,
        timestamp: Date.now()
      };
    }
  }

  updateMetrics(requestId, success, duration, taskType) {
    const metrics = this.state.orchestrationMetrics;

    if (taskType.includes('thread')) {
      metrics.totalThreads++;
      if (success) metrics.successfulThreads++;

      const totalTime = metrics.averageThreadTime * (metrics.totalThreads - 1) + duration;
      metrics.averageThreadTime = totalTime / metrics.totalThreads;
    } else {
      metrics.totalTasks++;
      if (success) metrics.successfulTasks++;

      const totalTime = metrics.averageTaskTime * (metrics.totalTasks - 1) + duration;
      metrics.averageTaskTime = totalTime / metrics.totalTasks;
    }

    this.emit('metricsUpdated', {
      requestId,
      success,
      duration,
      taskType,
      metrics: { ...metrics }
    });
  }

  // Public API methods

  getUniversalStatus() {
    return {
      status: this.state.status,
      uptime: Date.now() - this.startTime,
      activeTasks: this.state.activeTasks.size,
      activeThreads: this.state.activeThreads.size,
      metrics: this.state.orchestrationMetrics,
      orchestrators: {
        meta: this.metaOrchestrator?.getSystemStatus() || null,
        thread: this.threadMergingOrchestrator?.getStatus() || null,
        ai: this.aiOrchestrator?.getStatus() || null
      },
      universalRoles: Object.keys(this.universalRoles),
      supportedTaskTypes: Object.keys(this.universalTaskTypes)
    };
  }

  async executeTask(taskType, request) {
    const universalRequest = {
      ...request,
      type: taskType,
      timestamp: Date.now()
    };

    return await this.orchestrate(universalRequest);
  }

  async shutdown() {
    console.log(chalk.blue('🛑 Shutting down Universal Orchestration Layer...'));

    this.state.status = 'shutting-down';

    // Shutdown all orchestrators
    if (this.metaOrchestrator) {
      await this.metaOrchestrator.shutdown();
    }

    if (this.threadMergingOrchestrator) {
      await this.threadMergingOrchestrator.shutdown();
    }

    if (this.aiOrchestrator) {
      await this.aiOrchestrator.shutdown();
    }

    // Shutdown DGM layer
    await this.dgmLayer.shutdown();

    // Shutdown meta-orchestration adapter
    await this.metaOrchestrationAdapter.shutdown();

    this.state.status = 'shutdown';
    this.emit('shutdown');

    console.log(chalk.green('✅ Universal Orchestration Layer shutdown complete'));
  }
}

module.exports = UniversalOrchestrator;
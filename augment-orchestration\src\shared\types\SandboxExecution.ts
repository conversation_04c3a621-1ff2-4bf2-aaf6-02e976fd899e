/**
 * Sandbox Execution Environment Types and Interfaces
 * 
 * Core Gap 5: Secure, isolated execution environment for running AI-generated
 * code with resource limits, monitoring, and safety controls.
 */

export enum SandboxType {
  DOCKER = 'DOCKER',
  VM = 'VM',
  PROCESS = 'PROCESS',
  WASM = 'WASM',
  CONTAINER = 'CONTAINER'
}

export enum ExecutionStatus {
  PENDING = 'PENDING',
  INITIALIZING = 'INITIALIZING',
  RUNNING = 'RUNNING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  TIMEOUT = 'TIMEOUT',
  KILLED = 'KILLED',
  RESOURCE_EXCEEDED = 'RESOURCE_EXCEEDED'
}

export enum SecurityLevel {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  MAXIMUM = 'MAXIMUM'
}

export enum Language {
  JAVASCRIPT = 'JAVASCRIPT',
  TYPESCRIPT = 'TYPESCRIPT',
  PYTHON = 'PYTHON',
  JAVA = 'JAVA',
  CSHARP = 'CSHARP',
  GO = 'GO',
  RUST = 'RUST',
  CPP = 'CPP',
  SHELL = 'SHELL',
  SQL = 'SQL'
}

export interface SandboxConfig {
  type: SandboxType;
  language: Language;
  securityLevel: SecurityLevel;
  resourceLimits: ResourceLimits;
  networkAccess: NetworkConfig;
  fileSystemAccess: FileSystemConfig;
  environmentVariables: Record<string, string>;
  allowedModules: string[];
  blockedModules: string[];
  timeoutMs: number;
  maxExecutions: number;
  persistentStorage: boolean;
  monitoring: MonitoringConfig;
}

export interface ResourceLimits {
  maxMemoryMB: number;
  maxCpuPercent: number;
  maxDiskMB: number;
  maxNetworkKBps: number;
  maxProcesses: number;
  maxFileDescriptors: number;
  maxExecutionTimeMs: number;
  maxOutputSizeKB: number;
}

export interface NetworkConfig {
  enabled: boolean;
  allowedHosts: string[];
  blockedHosts: string[];
  allowedPorts: number[];
  blockedPorts: number[];
  proxyUrl?: string;
  dnsServers?: string[];
  maxConnections: number;
}

export interface FileSystemConfig {
  readOnlyPaths: string[];
  writablePaths: string[];
  blockedPaths: string[];
  maxFileSize: number;
  maxTotalSize: number;
  allowedExtensions: string[];
  blockedExtensions: string[];
  tempDirectory: string;
}

export interface MonitoringConfig {
  enableResourceMonitoring: boolean;
  enableNetworkMonitoring: boolean;
  enableFileSystemMonitoring: boolean;
  enableSystemCallMonitoring: boolean;
  monitoringInterval: number;
  alertThresholds: AlertThresholds;
  logLevel: 'DEBUG' | 'INFO' | 'WARN' | 'ERROR';
}

export interface AlertThresholds {
  memoryUsagePercent: number;
  cpuUsagePercent: number;
  diskUsagePercent: number;
  networkUsagePercent: number;
  suspiciousActivityScore: number;
}

export interface ExecutionRequest {
  executionId: string;
  code: string;
  language: Language;
  entryPoint?: string;
  arguments?: string[];
  inputData?: any;
  dependencies?: Dependency[];
  config: SandboxConfig;
  metadata: ExecutionMetadata;
}

export interface Dependency {
  name: string;
  version: string;
  source: 'npm' | 'pip' | 'maven' | 'nuget' | 'cargo' | 'go' | 'custom';
  url?: string;
  checksum?: string;
  trusted: boolean;
}

export interface ExecutionMetadata {
  requestedBy: string;
  purpose: string;
  tags: string[];
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  maxRetries: number;
  notifyOnComplete: boolean;
  parentExecutionId?: string;
  correlationId?: string;
}

export interface ExecutionResult {
  executionId: string;
  status: ExecutionStatus;
  startTime: Date;
  endTime?: Date;
  duration: number;
  exitCode?: number;
  stdout: string;
  stderr: string;
  output?: any;
  error?: ExecutionError;
  resourceUsage: ResourceUsage;
  securityEvents: SecurityEvent[];
  metadata: ResultMetadata;
}

export interface ExecutionError {
  type: 'SYNTAX_ERROR' | 'RUNTIME_ERROR' | 'SECURITY_VIOLATION' | 'RESOURCE_EXCEEDED' | 'TIMEOUT' | 'SYSTEM_ERROR';
  message: string;
  stack?: string;
  line?: number;
  column?: number;
  details?: any;
}

export interface ResourceUsage {
  peakMemoryMB: number;
  averageMemoryMB: number;
  peakCpuPercent: number;
  averageCpuPercent: number;
  diskReadMB: number;
  diskWriteMB: number;
  networkInKB: number;
  networkOutKB: number;
  processCount: number;
  fileDescriptorCount: number;
  systemCalls: SystemCallStats;
}

export interface SystemCallStats {
  totalCalls: number;
  fileSystemCalls: number;
  networkCalls: number;
  processCalls: number;
  memoryCalls: number;
  suspiciousCalls: number;
}

export interface SecurityEvent {
  eventId: string;
  timestamp: Date;
  type: SecurityEventType;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  description: string;
  details: any;
  blocked: boolean;
  riskScore: number;
}

export enum SecurityEventType {
  UNAUTHORIZED_FILE_ACCESS = 'UNAUTHORIZED_FILE_ACCESS',
  UNAUTHORIZED_NETWORK_ACCESS = 'UNAUTHORIZED_NETWORK_ACCESS',
  SUSPICIOUS_SYSTEM_CALL = 'SUSPICIOUS_SYSTEM_CALL',
  RESOURCE_ABUSE = 'RESOURCE_ABUSE',
  MALICIOUS_CODE_PATTERN = 'MALICIOUS_CODE_PATTERN',
  PRIVILEGE_ESCALATION = 'PRIVILEGE_ESCALATION',
  DATA_EXFILTRATION = 'DATA_EXFILTRATION',
  CRYPTO_MINING = 'CRYPTO_MINING'
}

export interface ResultMetadata {
  sandboxId: string;
  sandboxType: SandboxType;
  language: Language;
  securityLevel: SecurityLevel;
  codeHash: string;
  dependencyHashes: string[];
  environmentHash: string;
  reproducible: boolean;
  cached: boolean;
  warnings: string[];
}

export interface SandboxInstance {
  id: string;
  type: SandboxType;
  status: 'INITIALIZING' | 'READY' | 'BUSY' | 'CLEANUP' | 'TERMINATED';
  config: SandboxConfig;
  createdAt: Date;
  lastUsed: Date;
  executionCount: number;
  resourceUsage: ResourceUsage;
  healthStatus: HealthStatus;
}

export interface HealthStatus {
  healthy: boolean;
  lastCheck: Date;
  issues: HealthIssue[];
  performance: PerformanceMetrics;
}

export interface HealthIssue {
  type: 'MEMORY_LEAK' | 'HIGH_CPU' | 'DISK_FULL' | 'NETWORK_ISSUE' | 'SECURITY_CONCERN';
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  description: string;
  detectedAt: Date;
  resolved: boolean;
}

export interface PerformanceMetrics {
  averageExecutionTime: number;
  successRate: number;
  errorRate: number;
  throughput: number;
  latency: number;
  reliability: number;
}

export interface CodeAnalysis {
  analysisId: string;
  code: string;
  language: Language;
  riskScore: number;
  findings: SecurityFinding[];
  complexity: ComplexityMetrics;
  dependencies: DependencyAnalysis[];
  recommendations: string[];
  approved: boolean;
  approvedBy?: string;
  approvedAt?: Date;
}

export interface SecurityFinding {
  findingId: string;
  type: 'VULNERABILITY' | 'MALICIOUS_PATTERN' | 'SUSPICIOUS_BEHAVIOR' | 'POLICY_VIOLATION';
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  title: string;
  description: string;
  location: CodeLocation;
  recommendation: string;
  cwe?: string;
  cvss?: number;
  references: string[];
}

export interface CodeLocation {
  file?: string;
  line: number;
  column: number;
  endLine?: number;
  endColumn?: number;
  snippet: string;
}

export interface ComplexityMetrics {
  cyclomaticComplexity: number;
  cognitiveComplexity: number;
  linesOfCode: number;
  maintainabilityIndex: number;
  technicalDebt: number;
  duplicateCodePercent: number;
}

export interface DependencyAnalysis {
  name: string;
  version: string;
  riskScore: number;
  vulnerabilities: Vulnerability[];
  license: string;
  deprecated: boolean;
  lastUpdated: Date;
  trustScore: number;
}

export interface Vulnerability {
  id: string;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  title: string;
  description: string;
  cvss: number;
  cwe: string;
  publishedAt: Date;
  fixedIn?: string;
  references: string[];
}

export interface ExecutionQueue {
  queueId: string;
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  maxConcurrent: number;
  currentExecutions: number;
  pendingExecutions: number;
  totalExecutions: number;
  averageWaitTime: number;
  averageExecutionTime: number;
  lastProcessed: Date;
}

export interface SandboxPool {
  poolId: string;
  type: SandboxType;
  language: Language;
  minInstances: number;
  maxInstances: number;
  currentInstances: number;
  availableInstances: number;
  busyInstances: number;
  warmupTime: number;
  idleTimeout: number;
  healthCheckInterval: number;
  lastScaled: Date;
}

export interface ExecutionStats {
  totalExecutions: number;
  successfulExecutions: number;
  failedExecutions: number;
  averageExecutionTime: number;
  averageQueueTime: number;
  resourceUtilization: ResourceUtilization;
  securityIncidents: number;
  topLanguages: LanguageStats[];
  recentActivity: ActivityStats;
}

export interface ResourceUtilization {
  cpu: UtilizationMetric;
  memory: UtilizationMetric;
  disk: UtilizationMetric;
  network: UtilizationMetric;
}

export interface UtilizationMetric {
  current: number;
  average: number;
  peak: number;
  trend: 'INCREASING' | 'DECREASING' | 'STABLE';
}

export interface LanguageStats {
  language: Language;
  executionCount: number;
  successRate: number;
  averageExecutionTime: number;
  resourceUsage: ResourceUsage;
}

export interface ActivityStats {
  last24Hours: number;
  lastWeek: number;
  lastMonth: number;
  peakHour: number;
  peakDay: number;
}

// Error types
export class SandboxError extends Error {
  constructor(
    message: string,
    public code: string,
    public executionId?: string,
    public details?: any
  ) {
    super(message);
    this.name = 'SandboxError';
  }
}

export class SecurityViolationError extends SandboxError {
  constructor(message: string, executionId?: string, details?: any) {
    super(message, 'SECURITY_VIOLATION', executionId, details);
  }
}

export class ResourceExceededError extends SandboxError {
  constructor(message: string, executionId?: string, details?: any) {
    super(message, 'RESOURCE_EXCEEDED', executionId, details);
  }
}

export class ExecutionTimeoutError extends SandboxError {
  constructor(message: string, executionId?: string, details?: any) {
    super(message, 'EXECUTION_TIMEOUT', executionId, details);
  }
}

// Constants
export const SANDBOX_CONSTANTS = {
  DEFAULT_TIMEOUT: 30000, // 30 seconds
  MAX_TIMEOUT: 300000, // 5 minutes
  DEFAULT_MEMORY_LIMIT: 512, // MB
  MAX_MEMORY_LIMIT: 4096, // MB
  DEFAULT_CPU_LIMIT: 50, // Percent
  MAX_CPU_LIMIT: 100, // Percent
  DEFAULT_DISK_LIMIT: 100, // MB
  MAX_DISK_LIMIT: 1024, // MB
  DEFAULT_OUTPUT_LIMIT: 1024, // KB
  MAX_OUTPUT_LIMIT: 10240, // KB
  HEALTH_CHECK_INTERVAL: 30000, // 30 seconds
  IDLE_TIMEOUT: 300000, // 5 minutes
  MAX_QUEUE_SIZE: 1000,
  MAX_CONCURRENT_EXECUTIONS: 100,
  CODE_ANALYSIS_TIMEOUT: 60000, // 1 minute
  DEPENDENCY_SCAN_TIMEOUT: 120000 // 2 minutes
};

// Utility functions
export const SandboxUtils = {
  generateExecutionId: (): string => {
    return `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  },

  generateSandboxId: (): string => {
    return `sandbox_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  },

  calculateCodeHash: (code: string): string => {
    // This would use a proper hash function in implementation
    return btoa(code).slice(0, 16);
  },

  validateResourceLimits: (limits: ResourceLimits): boolean => {
    return (
      limits.maxMemoryMB > 0 && limits.maxMemoryMB <= SANDBOX_CONSTANTS.MAX_MEMORY_LIMIT &&
      limits.maxCpuPercent > 0 && limits.maxCpuPercent <= SANDBOX_CONSTANTS.MAX_CPU_LIMIT &&
      limits.maxDiskMB > 0 && limits.maxDiskMB <= SANDBOX_CONSTANTS.MAX_DISK_LIMIT &&
      limits.maxExecutionTimeMs > 0 && limits.maxExecutionTimeMs <= SANDBOX_CONSTANTS.MAX_TIMEOUT
    );
  },

  calculateRiskScore: (findings: SecurityFinding[]): number => {
    if (findings.length === 0) return 0;
    
    const severityWeights = { LOW: 1, MEDIUM: 3, HIGH: 7, CRITICAL: 10 };
    const totalScore = findings.reduce((sum, finding) => 
      sum + severityWeights[finding.severity], 0
    );
    
    return Math.min(totalScore / findings.length, 10);
  },

  isLanguageSupported: (language: Language): boolean => {
    return Object.values(Language).includes(language);
  },

  getDefaultConfig: (language: Language): Partial<SandboxConfig> => {
    const baseConfig = {
      securityLevel: SecurityLevel.MEDIUM,
      resourceLimits: {
        maxMemoryMB: SANDBOX_CONSTANTS.DEFAULT_MEMORY_LIMIT,
        maxCpuPercent: SANDBOX_CONSTANTS.DEFAULT_CPU_LIMIT,
        maxDiskMB: SANDBOX_CONSTANTS.DEFAULT_DISK_LIMIT,
        maxNetworkKBps: 1024,
        maxProcesses: 10,
        maxFileDescriptors: 100,
        maxExecutionTimeMs: SANDBOX_CONSTANTS.DEFAULT_TIMEOUT,
        maxOutputSizeKB: SANDBOX_CONSTANTS.DEFAULT_OUTPUT_LIMIT
      },
      networkAccess: {
        enabled: false,
        allowedHosts: [],
        blockedHosts: ['*'],
        allowedPorts: [],
        blockedPorts: [22, 23, 25, 53, 80, 443],
        maxConnections: 0
      },
      timeoutMs: SANDBOX_CONSTANTS.DEFAULT_TIMEOUT,
      maxExecutions: 1,
      persistentStorage: false
    };

    // Language-specific configurations
    switch (language) {
      case Language.JAVASCRIPT:
      case Language.TYPESCRIPT:
        return {
          ...baseConfig,
          type: SandboxType.CONTAINER,
          allowedModules: ['lodash', 'moment', 'axios'],
          blockedModules: ['fs', 'child_process', 'cluster']
        };
      case Language.PYTHON:
        return {
          ...baseConfig,
          type: SandboxType.CONTAINER,
          allowedModules: ['numpy', 'pandas', 'requests'],
          blockedModules: ['os', 'subprocess', 'socket']
        };
      default:
        return baseConfig;
    }
  },

  estimateExecutionTime: (code: string, language: Language): number => {
    // Simple heuristic based on code length and language
    const baseTime = 1000; // 1 second
    const codeComplexity = code.length / 100; // Rough complexity measure
    
    const languageMultipliers = {
      [Language.JAVASCRIPT]: 1.0,
      [Language.TYPESCRIPT]: 1.2,
      [Language.PYTHON]: 1.5,
      [Language.JAVA]: 2.0,
      [Language.CSHARP]: 1.8,
      [Language.GO]: 1.3,
      [Language.RUST]: 2.5,
      [Language.CPP]: 3.0,
      [Language.SHELL]: 0.8,
      [Language.SQL]: 0.5
    };

    return Math.min(
      baseTime * codeComplexity * (languageMultipliers[language] || 1.0),
      SANDBOX_CONSTANTS.MAX_TIMEOUT
    );
  }
};

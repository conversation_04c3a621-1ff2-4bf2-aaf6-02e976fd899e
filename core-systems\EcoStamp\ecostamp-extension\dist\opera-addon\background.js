// EcoStamp Universal Background Script - Cross-Browser Compatible

// Cross-browser compatibility
const browserAPI = (() => {
    if (typeof browser !== 'undefined') {
        return browser; // Firefox
    }
    return chrome; // Chrome, Edge, Opera, Brave
})();

// Initialize extension
browserAPI.runtime.onInstalled.addListener((details) => {
    console.log('🌱 EcoStamp Universal installed/updated');
    
    if (details.reason === 'install') {
        // Set default configuration
        browserAPI.storage.local.set({
            ecoStampConfig: {
                enabled: true,
                showTimestamp: true,
                showEcoLevel: true,
                showModel: true,
                showPlatform: true
            },
            ecoStampStats: {
                totalInteractions: 0,
                totalEnergy: 0,
                totalWater: 0,
                platforms: {},
                ecoLevels: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 }
            }
        });

        // Show welcome notification (if supported)
        if (browserAPI.notifications) {
            browserAPI.notifications.create({
                type: 'basic',
                iconUrl: 'icons/icon48.png',
                title: 'EcoStamp Universal Installed!',
                message: 'Now tracking environmental impact across ALL AI platforms.'
            });
        }
    }
});

// Handle messages from content scripts
browserAPI.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'updateStats') {
        updateStatistics(request.data);
        sendResponse({ success: true });
    } else if (request.action === 'getConfig') {
        // Cross-browser storage handling
        const getStorage = browserAPI.storage.local.get(['ecoStampConfig']);

        if (getStorage.then) {
            // Promise-based (Chrome/Edge)
            getStorage.then(result => {
                sendResponse(result.ecoStampConfig || { enabled: true });
            });
        } else {
            // Callback-based (older browsers)
            browserAPI.storage.local.get(['ecoStampConfig'], (result) => {
                sendResponse(result.ecoStampConfig || { enabled: true });
            });
        }
        return true; // Keep message channel open for async response
    }
});

// Update statistics
async function updateStatistics(data) {
    try {
        const result = await chrome.storage.local.get(['stamplyStats']);
        const stats = result.stamplyStats || {
            totalInteractions: 0,
            totalEnergy: 0,
            totalWater: 0,
            platforms: {},
            ecoLevels: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 }
        };
        
        // Update totals
        stats.totalInteractions += 1;
        stats.totalEnergy += data.energy;
        stats.totalWater += data.water;
        
        // Update platform stats
        if (!stats.platforms[data.platform]) {
            stats.platforms[data.platform] = { count: 0, name: data.platformName };
        }
        stats.platforms[data.platform].count += 1;
        
        // Update eco-level distribution
        stats.ecoLevels[data.ecoLevel] += 1;
        
        await chrome.storage.local.set({ stamplyStats: stats });
        
        // Update badge with total interactions
        chrome.action.setBadgeText({
            text: stats.totalInteractions > 999 ? '999+' : stats.totalInteractions.toString()
        });
        chrome.action.setBadgeBackgroundColor({ color: '#4ade80' });
        
    } catch (error) {
        console.error('Error updating statistics:', error);
    }
}

// Handle tab updates to inject content script if needed
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
    if (changeInfo.status === 'complete' && tab.url) {
        // Check if this is an AI platform
        const aiDomains = [
            'chat.openai.com', 'chatgpt.com', 'claude.ai', 'gemini.google.com',
            'bard.google.com', 'poe.com', 'character.ai', 'perplexity.ai',
            'you.com', 'huggingface.co'
        ];
        
        const isAIPlatform = aiDomains.some(domain => tab.url.includes(domain));
        
        if (isAIPlatform) {
            // Update badge to show we're active on this tab
            chrome.action.setBadgeText({ text: '🌱', tabId: tabId });
        }
    }
});

// Context menu for quick actions
chrome.runtime.onInstalled.addListener(() => {
    chrome.contextMenus.create({
        id: 'stamply-toggle',
        title: 'Toggle Stamply Tracking',
        contexts: ['page']
    });
    
    chrome.contextMenus.create({
        id: 'stamply-stats',
        title: 'View Stamply Statistics',
        contexts: ['page']
    });
});

chrome.contextMenus.onClicked.addListener(async (info, tab) => {
    if (info.menuItemId === 'stamply-toggle') {
        const result = await chrome.storage.local.get(['stamplyConfig']);
        const config = result.stamplyConfig || { enabled: true };
        
        config.enabled = !config.enabled;
        await chrome.storage.local.set({ stamplyConfig: config });
        
        // Notify content script
        chrome.tabs.sendMessage(tab.id, {
            action: 'toggleStamply',
            enabled: config.enabled
        });
        
        // Show notification
        chrome.notifications.create({
            type: 'basic',
            iconUrl: 'icons/icon48.png',
            title: 'Stamply Universal',
            message: `Tracking ${config.enabled ? 'enabled' : 'disabled'}`
        });
        
    } else if (info.menuItemId === 'stamply-stats') {
        // Open popup or new tab with stats
        chrome.action.openPopup();
    }
});

// Periodic cleanup and maintenance
chrome.alarms.create('stamply-maintenance', { periodInMinutes: 60 });

chrome.alarms.onAlarm.addListener((alarm) => {
    if (alarm.name === 'stamply-maintenance') {
        // Perform any necessary cleanup
        console.log('🧹 Stamply Universal: Running maintenance...');
    }
});

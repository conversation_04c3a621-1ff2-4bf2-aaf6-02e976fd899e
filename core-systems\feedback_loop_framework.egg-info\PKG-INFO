Metadata-Version: 2.4
Name: feedback-loop-framework
Version: 1.0.0
Summary: Universal Dual-Purpose Feedback Loop Framework for AI Systems
Home-page: https://github.com/ai-orchestration/feedback-loop-framework
Author: AI Orchestration Team
Author-email: <EMAIL>
Project-URL: Bug Reports, https://github.com/ai-orchestration/feedback-loop-framework/issues
Project-URL: Source, https://github.com/ai-orchestration/feedback-loop-framework
Project-URL: Documentation, https://feedback-loop-framework.readthedocs.io/
Keywords: ai,feedback,quality-assurance,machine-learning,orchestration,validation,trust-scoring,confidence,drone-ai,timestamp-ai,continuous-improvement
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Topic :: Scientific/Engineering :: Artificial Intelligence
Classifier: Topic :: System :: Monitoring
Requires-Python: >=3.8
Description-Content-Type: text/markdown
Requires-Dist: pyyaml>=6.0
Requires-Dist: python-dateutil>=2.8.0
Provides-Extra: dev
Requires-Dist: pytest>=7.0.0; extra == "dev"
Requires-Dist: pytest-cov>=4.0.0; extra == "dev"
Requires-Dist: black>=22.0.0; extra == "dev"
Requires-Dist: flake8>=5.0.0; extra == "dev"
Requires-Dist: mypy>=1.0.0; extra == "dev"
Provides-Extra: dashboard
Requires-Dist: flask>=2.0.0; extra == "dashboard"
Requires-Dist: plotly>=5.0.0; extra == "dashboard"
Requires-Dist: pandas>=1.3.0; extra == "dashboard"
Provides-Extra: advanced
Requires-Dist: numpy>=1.21.0; extra == "advanced"
Requires-Dist: scipy>=1.7.0; extra == "advanced"
Requires-Dist: scikit-learn>=1.0.0; extra == "advanced"
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description-content-type
Dynamic: home-page
Dynamic: keywords
Dynamic: project-url
Dynamic: provides-extra
Dynamic: requires-dist
Dynamic: requires-python
Dynamic: summary

/**
 * Feedback Loop Integration Types and Interfaces
 * 
 * Core Gap 6: Continuous learning system that captures execution results,
 * user feedback, and performance metrics to improve future code generation
 * and decision-making processes.
 */

export enum FeedbackType {
  EXECUTION_RESULT = 'EXECUTION_RESULT',
  USER_RATING = 'USER_RATING',
  PERFORMANCE_METRIC = 'PERFORMANCE_METRIC',
  ERROR_REPORT = 'ERROR_REPORT',
  IMPROVEMENT_SUGGESTION = 'IMPROVEMENT_SUGGESTION',
  QUALITY_ASSESSMENT = 'QUALITY_ASSESSMENT',
  USAGE_PATTERN = 'USAGE_PATTERN',
  SYSTEM_METRIC = 'SYSTEM_METRIC'
}

export enum FeedbackSource {
  USER = 'USER',
  SYSTEM = 'SYSTEM',
  AGENT = 'AGENT',
  EXTERNAL_API = 'EXTERNAL_API',
  AUTOMATED_TEST = 'AUTOMATED_TEST',
  MONITORING_SYSTEM = 'MONITORING_SYSTEM',
  PEER_REVIEW = 'PEER_REVIEW'
}

export enum FeedbackSentiment {
  VERY_POSITIVE = 'VERY_POSITIVE',
  POSITIVE = 'POSITIVE',
  NEUTRAL = 'NEUTRAL',
  NEGATIVE = 'NEGATIVE',
  VERY_NEGATIVE = 'VERY_NEGATIVE'
}

export enum LearningObjective {
  CODE_QUALITY = 'CODE_QUALITY',
  PERFORMANCE_OPTIMIZATION = 'PERFORMANCE_OPTIMIZATION',
  SECURITY_IMPROVEMENT = 'SECURITY_IMPROVEMENT',
  USER_SATISFACTION = 'USER_SATISFACTION',
  ERROR_REDUCTION = 'ERROR_REDUCTION',
  EFFICIENCY_IMPROVEMENT = 'EFFICIENCY_IMPROVEMENT',
  PATTERN_RECOGNITION = 'PATTERN_RECOGNITION',
  DECISION_ACCURACY = 'DECISION_ACCURACY'
}

export enum AdaptationStrategy {
  IMMEDIATE = 'IMMEDIATE',
  GRADUAL = 'GRADUAL',
  BATCH_LEARNING = 'BATCH_LEARNING',
  REINFORCEMENT = 'REINFORCEMENT',
  ENSEMBLE = 'ENSEMBLE',
  TRANSFER_LEARNING = 'TRANSFER_LEARNING'
}

export interface FeedbackEntry {
  id: string;
  type: FeedbackType;
  source: FeedbackSource;
  timestamp: Date;
  contextId: string; // Links to execution, task, or other context
  contextType: 'EXECUTION' | 'TASK' | 'AGENT_DECISION' | 'CODE_GENERATION' | 'SYSTEM_OPERATION';
  content: FeedbackContent;
  metadata: FeedbackMetadata;
  processed: boolean;
  processedAt?: Date;
  impact: FeedbackImpact;
}

export interface FeedbackContent {
  rating?: number; // 1-10 scale
  sentiment?: FeedbackSentiment;
  textualFeedback?: string;
  structuredData?: Record<string, any>;
  metrics?: PerformanceMetrics;
  suggestions?: string[];
  tags?: string[];
  category?: string;
  severity?: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
}

export interface FeedbackMetadata {
  userId?: string;
  agentId?: string;
  sessionId?: string;
  version?: string;
  environment?: string;
  language?: string;
  framework?: string;
  correlationId?: string;
  parentFeedbackId?: string;
  confidence?: number; // 0-1 scale
  reliability?: number; // 0-1 scale
  weight?: number; // Importance weight for learning
}

export interface FeedbackImpact {
  learningObjectives: LearningObjective[];
  affectedComponents: string[];
  priorityScore: number; // 0-100 scale
  expectedImprovement: number; // 0-1 scale
  implementationComplexity: 'LOW' | 'MEDIUM' | 'HIGH' | 'VERY_HIGH';
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
}

export interface PerformanceMetrics {
  executionTime?: number;
  memoryUsage?: number;
  cpuUsage?: number;
  errorRate?: number;
  successRate?: number;
  throughput?: number;
  latency?: number;
  accuracy?: number;
  precision?: number;
  recall?: number;
  f1Score?: number;
  customMetrics?: Record<string, number>;
}

export interface LearningPattern {
  id: string;
  name: string;
  description: string;
  pattern: PatternDefinition;
  confidence: number;
  supportingEvidence: string[];
  contradictingEvidence: string[];
  applicableContexts: string[];
  learningObjective: LearningObjective;
  discoveredAt: Date;
  lastUpdated: Date;
  usageCount: number;
  successRate: number;
}

export interface PatternDefinition {
  conditions: PatternCondition[];
  outcomes: PatternOutcome[];
  correlations: PatternCorrelation[];
  thresholds: Record<string, number>;
  rules: PatternRule[];
}

export interface PatternCondition {
  field: string;
  operator: 'EQUALS' | 'NOT_EQUALS' | 'GREATER_THAN' | 'LESS_THAN' | 'CONTAINS' | 'MATCHES' | 'IN_RANGE';
  value: any;
  weight: number;
}

export interface PatternOutcome {
  field: string;
  expectedValue: any;
  confidence: number;
  impact: number;
}

export interface PatternCorrelation {
  field1: string;
  field2: string;
  correlationType: 'POSITIVE' | 'NEGATIVE' | 'NEUTRAL';
  strength: number; // -1 to 1
  significance: number; // 0 to 1
}

export interface PatternRule {
  id: string;
  condition: string;
  action: string;
  priority: number;
  enabled: boolean;
}

export interface AdaptationRecommendation {
  id: string;
  title: string;
  description: string;
  type: 'CODE_CHANGE' | 'PARAMETER_ADJUSTMENT' | 'STRATEGY_CHANGE' | 'PROCESS_IMPROVEMENT';
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  basedOnFeedback: string[]; // Feedback entry IDs
  basedOnPatterns: string[]; // Learning pattern IDs
  targetComponent: string;
  proposedChanges: ProposedChange[];
  expectedBenefit: ExpectedBenefit;
  implementationPlan: ImplementationStep[];
  riskAssessment: RiskAssessment;
  approvalRequired: boolean;
  approvedBy?: string;
  approvedAt?: Date;
  implementedAt?: Date;
  validatedAt?: Date;
}

export interface ProposedChange {
  component: string;
  changeType: 'ADD' | 'MODIFY' | 'REMOVE' | 'REPLACE';
  currentValue?: any;
  proposedValue: any;
  rationale: string;
  confidence: number;
}

export interface ExpectedBenefit {
  performanceImprovement?: number; // Percentage
  qualityImprovement?: number; // Percentage
  errorReduction?: number; // Percentage
  userSatisfactionIncrease?: number; // Percentage
  efficiencyGain?: number; // Percentage
  costReduction?: number; // Percentage
  timeToValue?: number; // Days
  customBenefits?: Record<string, number>;
}

export interface ImplementationStep {
  stepNumber: number;
  description: string;
  estimatedDuration: number; // Minutes
  dependencies: number[]; // Step numbers
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';
  rollbackPlan?: string;
  validationCriteria: string[];
}

export interface RiskAssessment {
  overallRisk: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  riskFactors: RiskFactor[];
  mitigationStrategies: string[];
  rollbackComplexity: 'LOW' | 'MEDIUM' | 'HIGH';
  impactRadius: 'LOCAL' | 'COMPONENT' | 'SYSTEM' | 'GLOBAL';
}

export interface RiskFactor {
  factor: string;
  probability: number; // 0-1
  impact: number; // 0-1
  mitigation?: string;
}

export interface LearningSession {
  id: string;
  startTime: Date;
  endTime?: Date;
  objective: LearningObjective;
  strategy: AdaptationStrategy;
  feedbackProcessed: number;
  patternsDiscovered: number;
  recommendationsGenerated: number;
  adaptationsImplemented: number;
  performanceBaseline: PerformanceMetrics;
  performanceAfter?: PerformanceMetrics;
  improvement?: number; // Percentage
  status: 'RUNNING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';
  metadata: SessionMetadata;
}

export interface SessionMetadata {
  triggeredBy: 'SCHEDULE' | 'THRESHOLD' | 'MANUAL' | 'EVENT';
  dataRange: DateRange;
  focusAreas: string[];
  constraints: LearningConstraints;
  configuration: LearningConfiguration;
}

export interface DateRange {
  startDate: Date;
  endDate: Date;
}

export interface LearningConstraints {
  maxProcessingTime: number; // Minutes
  maxMemoryUsage: number; // MB
  maxCpuUsage: number; // Percentage
  minConfidenceThreshold: number; // 0-1
  maxRiskLevel: 'LOW' | 'MEDIUM' | 'HIGH';
  allowedComponents: string[];
  blockedComponents: string[];
}

export interface LearningConfiguration {
  enablePatternDiscovery: boolean;
  enableRecommendationGeneration: boolean;
  enableAutoImplementation: boolean;
  batchSize: number;
  learningRate: number;
  convergenceThreshold: number;
  validationSplit: number; // 0-1
  crossValidationFolds: number;
  ensembleSize: number;
}

export interface FeedbackAnalytics {
  totalFeedback: number;
  feedbackByType: Record<FeedbackType, number>;
  feedbackBySource: Record<FeedbackSource, number>;
  feedbackBySentiment: Record<FeedbackSentiment, number>;
  averageRating: number;
  trendAnalysis: TrendAnalysis;
  topIssues: IssueAnalysis[];
  improvementAreas: ImprovementArea[];
  userSatisfactionTrend: DataPoint[];
  performanceTrend: DataPoint[];
}

export interface TrendAnalysis {
  direction: 'IMPROVING' | 'DECLINING' | 'STABLE' | 'VOLATILE';
  strength: number; // 0-1
  confidence: number; // 0-1
  timeframe: string;
  keyFactors: string[];
}

export interface IssueAnalysis {
  issue: string;
  frequency: number;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  trend: 'INCREASING' | 'DECREASING' | 'STABLE';
  affectedComponents: string[];
  suggestedActions: string[];
}

export interface ImprovementArea {
  area: string;
  currentScore: number;
  targetScore: number;
  priority: number;
  estimatedEffort: 'LOW' | 'MEDIUM' | 'HIGH' | 'VERY_HIGH';
  potentialImpact: 'LOW' | 'MEDIUM' | 'HIGH' | 'VERY_HIGH';
  recommendations: string[];
}

export interface DataPoint {
  timestamp: Date;
  value: number;
  metadata?: Record<string, any>;
}

export interface FeedbackQuery {
  types?: FeedbackType[];
  sources?: FeedbackSource[];
  contextTypes?: string[];
  dateRange?: DateRange;
  minRating?: number;
  maxRating?: number;
  sentiments?: FeedbackSentiment[];
  tags?: string[];
  processed?: boolean;
  limit?: number;
  offset?: number;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
}

export interface LearningMetrics {
  totalPatterns: number;
  activePatterns: number;
  patternAccuracy: number;
  recommendationAcceptanceRate: number;
  implementationSuccessRate: number;
  overallImprovement: number;
  learningVelocity: number; // Patterns per day
  adaptationLatency: number; // Hours from feedback to adaptation
  systemStability: number; // 0-1 scale
  userSatisfactionDelta: number; // Change in satisfaction
}

// Error types
export class FeedbackError extends Error {
  constructor(
    message: string,
    public code: string,
    public feedbackId?: string,
    public details?: any
  ) {
    super(message);
    this.name = 'FeedbackError';
  }
}

export class PatternDiscoveryError extends FeedbackError {
  constructor(message: string, details?: any) {
    super(message, 'PATTERN_DISCOVERY_ERROR', undefined, details);
  }
}

export class AdaptationError extends FeedbackError {
  constructor(message: string, recommendationId?: string, details?: any) {
    super(message, 'ADAPTATION_ERROR', recommendationId, details);
  }
}

// Constants
export const FEEDBACK_CONSTANTS = {
  MAX_FEEDBACK_AGE_DAYS: 90,
  MIN_PATTERN_CONFIDENCE: 0.7,
  MIN_PATTERN_SUPPORT: 5, // Minimum number of supporting examples
  MAX_RECOMMENDATIONS_PER_SESSION: 50,
  DEFAULT_LEARNING_RATE: 0.01,
  DEFAULT_BATCH_SIZE: 100,
  CONVERGENCE_THRESHOLD: 0.001,
  MAX_PROCESSING_TIME_MINUTES: 60,
  FEEDBACK_PROCESSING_INTERVAL: 3600000, // 1 hour
  PATTERN_DISCOVERY_INTERVAL: 86400000, // 24 hours
  RECOMMENDATION_REVIEW_INTERVAL: 604800000, // 7 days
  AUTO_IMPLEMENTATION_THRESHOLD: 0.9, // Confidence threshold for auto-implementation
  RISK_TOLERANCE_THRESHOLD: 0.3 // Maximum acceptable risk level
};

// Utility functions
export const FeedbackUtils = {
  generateFeedbackId: (): string => {
    return `feedback_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  },

  generatePatternId: (): string => {
    return `pattern_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  },

  generateRecommendationId: (): string => {
    return `rec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  },

  calculateSentiment: (rating: number): FeedbackSentiment => {
    if (rating >= 9) return FeedbackSentiment.VERY_POSITIVE;
    if (rating >= 7) return FeedbackSentiment.POSITIVE;
    if (rating >= 4) return FeedbackSentiment.NEUTRAL;
    if (rating >= 2) return FeedbackSentiment.NEGATIVE;
    return FeedbackSentiment.VERY_NEGATIVE;
  },

  calculatePriorityScore: (
    impact: number,
    urgency: number,
    confidence: number
  ): number => {
    return (impact * 0.4 + urgency * 0.4 + confidence * 0.2) * 100;
  },

  isRecentFeedback: (timestamp: Date, maxAgeDays: number = FEEDBACK_CONSTANTS.MAX_FEEDBACK_AGE_DAYS): boolean => {
    const ageMs = Date.now() - timestamp.getTime();
    const maxAgeMs = maxAgeDays * 24 * 60 * 60 * 1000;
    return ageMs <= maxAgeMs;
  },

  aggregateMetrics: (metrics: PerformanceMetrics[]): PerformanceMetrics => {
    if (metrics.length === 0) return {};

    const aggregated: PerformanceMetrics = {};
    const fields = ['executionTime', 'memoryUsage', 'cpuUsage', 'errorRate', 'successRate', 'throughput', 'latency', 'accuracy', 'precision', 'recall', 'f1Score'];

    for (const field of fields) {
      const values = metrics.map(m => m[field as keyof PerformanceMetrics]).filter(v => v !== undefined) as number[];
      if (values.length > 0) {
        aggregated[field as keyof PerformanceMetrics] = values.reduce((sum, val) => sum + val, 0) / values.length;
      }
    }

    return aggregated;
  },

  validateFeedbackEntry: (entry: FeedbackEntry): boolean => {
    return !!(
      entry.id &&
      entry.type &&
      entry.source &&
      entry.timestamp &&
      entry.contextId &&
      entry.contextType &&
      entry.content
    );
  },

  calculatePatternStrength: (
    supportingEvidence: number,
    contradictingEvidence: number,
    totalSamples: number
  ): number => {
    if (totalSamples === 0) return 0;
    const support = supportingEvidence / totalSamples;
    const contradiction = contradictingEvidence / totalSamples;
    return Math.max(0, support - contradiction);
  },

  estimateImplementationRisk: (
    complexity: string,
    impactRadius: string,
    confidence: number
  ): 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' => {
    let riskScore = 0;

    // Complexity factor
    switch (complexity) {
      case 'LOW': riskScore += 1; break;
      case 'MEDIUM': riskScore += 2; break;
      case 'HIGH': riskScore += 3; break;
      case 'VERY_HIGH': riskScore += 4; break;
    }

    // Impact radius factor
    switch (impactRadius) {
      case 'LOCAL': riskScore += 1; break;
      case 'COMPONENT': riskScore += 2; break;
      case 'SYSTEM': riskScore += 3; break;
      case 'GLOBAL': riskScore += 4; break;
    }

    // Confidence factor (inverse)
    riskScore += Math.round((1 - confidence) * 3);

    if (riskScore <= 3) return 'LOW';
    if (riskScore <= 5) return 'MEDIUM';
    if (riskScore <= 7) return 'HIGH';
    return 'CRITICAL';
  }
};

/**
 * Role Manager
 * 
 * Manages role-based assignment of AI assistants with support for:
 * - Role categories (<PERSON><PERSON><PERSON>, Generator, Completer, <PERSON><PERSON>tor, Documenter)
 * - Multiple eligible assistants per role (free/paid, cloud/local)
 * - Dynamic role assignment based on context and performance
 * - Real-time role reassignment and optimization
 */

const EventEmitter = require('events');
const chalk = require('chalk');

class RoleManager extends EventEmitter {
  constructor(metaOrchestrator) {
    super();
    this.metaOrchestrator = metaOrchestrator;
    
    // Define role categories and their capabilities
    this.roleDefinitions = {
      analyzer: {
        name: 'Analyzer',
        description: 'Analyzes codebase, dependencies, and project structure',
        capabilities: [
          'codebase-analysis',
          'dependency-mapping',
          'architecture-review',
          'security-scanning',
          'performance-analysis',
          'code-quality-assessment'
        ],
        inputTypes: ['codebase', 'files', 'project-structure'],
        outputTypes: ['analysis-report', 'dependency-graph', 'recommendations'],
        priority: 1,
        timeout: 30000
      },
      
      generator: {
        name: 'Generator',
        description: 'Generates new code, components, and implementations',
        capabilities: [
          'code-generation',
          'component-creation',
          'api-implementation',
          'test-generation',
          'boilerplate-creation',
          'feature-implementation'
        ],
        inputTypes: ['requirements', 'specifications', 'context'],
        outputTypes: ['code', 'files', 'implementations'],
        priority: 2,
        timeout: 60000
      },
      
      completer: {
        name: 'Completer',
        description: 'Provides inline completions and code suggestions',
        capabilities: [
          'inline-completion',
          'auto-suggestions',
          'code-enhancement',
          'import-suggestions',
          'real-time-assistance',
          'context-aware-completion'
        ],
        inputTypes: ['partial-code', 'cursor-position', 'context'],
        outputTypes: ['completions', 'suggestions', 'enhancements'],
        priority: 3,
        timeout: 5000
      },
      
      validator: {
        name: 'Validator',
        description: 'Validates code quality, tests, and compliance',
        capabilities: [
          'code-validation',
          'test-execution',
          'quality-checking',
          'security-validation',
          'compliance-checking',
          'error-detection'
        ],
        inputTypes: ['code', 'tests', 'configurations'],
        outputTypes: ['validation-results', 'test-reports', 'recommendations'],
        priority: 4,
        timeout: 45000
      },
      
      documenter: {
        name: 'Documenter',
        description: 'Generates documentation, comments, and explanations',
        capabilities: [
          'documentation-generation',
          'comment-creation',
          'api-documentation',
          'readme-generation',
          'code-explanation',
          'tutorial-creation'
        ],
        inputTypes: ['code', 'apis', 'project-structure'],
        outputTypes: ['documentation', 'comments', 'explanations'],
        priority: 5,
        timeout: 30000
      }
    };
    
    // Assistant eligibility matrix - which assistants can perform which roles
    this.assistantCapabilities = {
      // Augment Code - Primary analyzer with comprehensive codebase understanding
      'augment-code': {
        roles: ['analyzer', 'validator'],
        strengths: ['codebase-analysis', 'context-understanding', 'dependency-mapping'],
        tier: 'premium',
        availability: 'cloud'
      },
      
      // Cursor - Excellent code generator with Claude 3.5 Sonnet
      'cursor': {
        roles: ['generator', 'completer', 'documenter'],
        strengths: ['code-generation', 'smart-rewrites', 'single-file-editing'],
        tier: 'premium',
        availability: 'cloud'
      },
      
      // Windsurf - Multi-file editing and architecture-aware changes
      'windsurf': {
        roles: ['generator', 'validator', 'documenter'],
        strengths: ['multi-file-editing', 'architecture-awareness', 'large-changes'],
        tier: 'premium',
        availability: 'cloud'
      },
      
      // Tabnine - Real-time completion and suggestions
      'tabnine': {
        roles: ['completer'],
        strengths: ['inline-completion', 'real-time-suggestions', 'context-aware'],
        tier: 'freemium',
        availability: 'cloud'
      },
      
      // GitHub Copilot - Code generation and completion
      'github-copilot': {
        roles: ['generator', 'completer', 'documenter'],
        strengths: ['code-generation', 'completion', 'github-integration'],
        tier: 'premium',
        availability: 'cloud'
      },
      
      // Qodo (formerly CodiumAI) - Testing and validation
      'qodo': {
        roles: ['validator', 'generator'],
        strengths: ['test-generation', 'code-validation', 'quality-analysis'],
        tier: 'freemium',
        availability: 'cloud'
      },
      
      // Cline - Local AI assistant
      'cline': {
        roles: ['generator', 'completer', 'documenter'],
        strengths: ['local-execution', 'privacy', 'customization'],
        tier: 'free',
        availability: 'local'
      },
      
      // Continue - Open source code assistant
      'continue': {
        roles: ['generator', 'completer', 'analyzer'],
        strengths: ['open-source', 'customizable', 'local-models'],
        tier: 'free',
        availability: 'local'
      },
      
      // Aider - AI pair programming
      'aider': {
        roles: ['generator', 'validator', 'documenter'],
        strengths: ['pair-programming', 'git-integration', 'refactoring'],
        tier: 'free',
        availability: 'local'
      },
      
      // Ollama - Local LLM runner
      'ollama': {
        roles: ['generator', 'completer', 'analyzer', 'documenter'],
        strengths: ['local-models', 'privacy', 'offline-capability'],
        tier: 'free',
        availability: 'local'
      },
      
      // LM Studio - Local model management
      'lm-studio': {
        roles: ['generator', 'completer', 'analyzer', 'documenter'],
        strengths: ['model-management', 'local-inference', 'privacy'],
        tier: 'free',
        availability: 'local'
      },
      
      // SuperAGI - Multi-agent orchestration
      'superagi': {
        roles: ['analyzer', 'generator', 'validator'],
        strengths: ['multi-agent', 'orchestration', 'complex-tasks'],
        tier: 'freemium',
        availability: 'cloud'
      },
      
      // AutoGen - Microsoft's multi-agent framework
      'autogen': {
        roles: ['analyzer', 'generator', 'validator'],
        strengths: ['multi-agent', 'conversation', 'collaboration'],
        tier: 'free',
        availability: 'local'
      }
    };
    
    // Default role assignments (can be overridden by user configuration)
    this.defaultAssignments = {
      analyzer: {
        primary: 'augment-code',
        fallbacks: ['continue', 'ollama', 'superagi']
      },
      generator: {
        primary: 'cursor',
        fallbacks: ['windsurf', 'github-copilot', 'cline', 'aider']
      },
      completer: {
        primary: 'tabnine',
        fallbacks: ['github-copilot', 'cursor', 'continue']
      },
      validator: {
        primary: 'qodo',
        fallbacks: ['augment-code', 'windsurf', 'autogen']
      },
      documenter: {
        primary: 'cursor',
        fallbacks: ['windsurf', 'github-copilot', 'aider', 'ollama']
      }
    };
    
    // Current role assignments (loaded from config or defaults)
    this.currentAssignments = new Map();
    
    // Performance tracking for dynamic optimization
    this.rolePerformance = new Map();
  }
  
  async initializeRoles() {
    try {
      console.log(chalk.blue('🎭 Initializing role management system...'));
      
      // Load role assignments from configuration
      await this.loadRoleAssignments();
      
      // Initialize performance tracking
      this.initializePerformanceTracking();
      
      // Validate assistant availability
      await this.validateAssistantAvailability();
      
      // Setup role optimization
      this.setupRoleOptimization();
      
      console.log(chalk.green('✅ Role management system initialized'));
      
    } catch (error) {
      console.error(chalk.red('❌ Failed to initialize role management:'), error);
      throw error;
    }
  }
  
  async loadRoleAssignments() {
    try {
      // Try to load from user configuration
      const userConfig = await this.metaOrchestrator.configManager.getRoleAssignments();
      
      if (userConfig && Object.keys(userConfig).length > 0) {
        // Use user configuration
        for (const [role, assignment] of Object.entries(userConfig)) {
          this.currentAssignments.set(role, assignment);
        }
        console.log(chalk.green('📋 Loaded role assignments from user configuration'));
      } else {
        // Use default assignments
        for (const [role, assignment] of Object.entries(this.defaultAssignments)) {
          this.currentAssignments.set(role, assignment);
        }
        console.log(chalk.yellow('📋 Using default role assignments'));
      }
      
      // Store current assignments in meta-orchestrator state
      this.metaOrchestrator.state.roleAssignments = this.currentAssignments;
      
    } catch (error) {
      console.error(chalk.red('❌ Failed to load role assignments:'), error);
      throw error;
    }
  }
  
  initializePerformanceTracking() {
    for (const role of Object.keys(this.roleDefinitions)) {
      this.rolePerformance.set(role, {
        totalRequests: 0,
        successfulRequests: 0,
        averageResponseTime: 0,
        assistantPerformance: new Map()
      });
    }
  }
  
  async validateAssistantAvailability() {
    console.log(chalk.blue('🔍 Validating assistant availability...'));
    
    const availabilityResults = new Map();
    
    for (const [assistantId, capabilities] of Object.entries(this.assistantCapabilities)) {
      try {
        const adapter = this.metaOrchestrator.adapterRegistry.getAdapter(assistantId);
        
        if (adapter) {
          const isAvailable = await adapter.checkAvailability();
          availabilityResults.set(assistantId, isAvailable);
          
          if (isAvailable) {
            console.log(chalk.green(`✅ ${assistantId} is available`));
          } else {
            console.log(chalk.yellow(`⚠️ ${assistantId} is not available`));
          }
        } else {
          availabilityResults.set(assistantId, false);
          console.log(chalk.red(`❌ No adapter found for ${assistantId}`));
        }
      } catch (error) {
        availabilityResults.set(assistantId, false);
        console.log(chalk.red(`❌ ${assistantId} availability check failed: ${error.message}`));
      }
    }
    
    // Update assistant states
    this.metaOrchestrator.state.assistantStates = availabilityResults;
    
    // Adjust role assignments based on availability
    await this.adjustAssignmentsForAvailability(availabilityResults);
  }
  
  async adjustAssignmentsForAvailability(availabilityResults) {
    let adjustmentsMade = false;
    
    for (const [role, assignment] of this.currentAssignments) {
      // Check if primary assistant is available
      if (!availabilityResults.get(assignment.primary)) {
        console.log(chalk.yellow(`⚠️ Primary assistant ${assignment.primary} for role ${role} is not available`));
        
        // Find first available fallback
        const availableFallback = assignment.fallbacks.find(assistantId => 
          availabilityResults.get(assistantId)
        );
        
        if (availableFallback) {
          console.log(chalk.blue(`🔄 Promoting ${availableFallback} to primary for role ${role}`));
          
          // Move the available fallback to primary
          assignment.fallbacks = assignment.fallbacks.filter(id => id !== availableFallback);
          assignment.fallbacks.unshift(assignment.primary);
          assignment.primary = availableFallback;
          
          adjustmentsMade = true;
        } else {
          console.log(chalk.red(`❌ No available assistants for role ${role}`));
        }
      }
      
      // Filter out unavailable fallbacks
      const availableFallbacks = assignment.fallbacks.filter(assistantId =>
        availabilityResults.get(assistantId)
      );
      
      if (availableFallbacks.length !== assignment.fallbacks.length) {
        assignment.fallbacks = availableFallbacks;
        adjustmentsMade = true;
      }
    }
    
    if (adjustmentsMade) {
      console.log(chalk.blue('🔄 Role assignments adjusted for assistant availability'));
      this.emit('assignmentsAdjusted', this.currentAssignments);
    }
  }
  
  setupRoleOptimization() {
    // Setup periodic optimization based on performance metrics
    setInterval(() => {
      this.optimizeRoleAssignments();
    }, 300000); // Every 5 minutes
  }
  
  optimizeRoleAssignments() {
    // Analyze performance metrics and adjust assignments if needed
    for (const [role, performance] of this.rolePerformance) {
      if (performance.totalRequests < 10) continue; // Need sufficient data
      
      const assignment = this.currentAssignments.get(role);
      if (!assignment) continue;
      
      // Check if any fallback is performing better than primary
      const primaryPerformance = performance.assistantPerformance.get(assignment.primary);
      
      if (primaryPerformance) {
        for (const fallbackId of assignment.fallbacks) {
          const fallbackPerformance = performance.assistantPerformance.get(fallbackId);
          
          if (fallbackPerformance && 
              fallbackPerformance.successRate > primaryPerformance.successRate + 0.1 &&
              fallbackPerformance.totalRequests >= 5) {
            
            console.log(chalk.blue(`🔄 Optimizing role ${role}: promoting ${fallbackId} to primary`));
            
            // Swap primary and fallback
            assignment.fallbacks = assignment.fallbacks.filter(id => id !== fallbackId);
            assignment.fallbacks.unshift(assignment.primary);
            assignment.primary = fallbackId;
            
            this.emit('roleOptimized', { role, newPrimary: fallbackId });
            break;
          }
        }
      }
    }
  }
  
  // Public methods for role management
  
  getRoleDefinition(role) {
    return this.roleDefinitions[role];
  }
  
  getRoleAssignment(role) {
    return this.currentAssignments.get(role);
  }
  
  getAllRoleAssignments() {
    return Object.fromEntries(this.currentAssignments);
  }
  
  async updateRoleAssignment(role, assignment) {
    if (!this.roleDefinitions[role]) {
      throw new Error(`Unknown role: ${role}`);
    }
    
    // Validate assignment
    this.validateAssignment(role, assignment);
    
    // Update assignment
    this.currentAssignments.set(role, assignment);
    
    // Save to configuration
    await this.metaOrchestrator.configManager.saveRoleAssignment(role, assignment);
    
    this.emit('assignmentUpdated', { role, assignment });
    
    console.log(chalk.green(`✅ Updated role assignment for ${role}`));
  }
  
  validateAssignment(role, assignment) {
    // Check if primary assistant can perform this role
    const primaryCapabilities = this.assistantCapabilities[assignment.primary];
    if (!primaryCapabilities || !primaryCapabilities.roles.includes(role)) {
      throw new Error(`Assistant ${assignment.primary} cannot perform role ${role}`);
    }
    
    // Check fallbacks
    for (const fallbackId of assignment.fallbacks) {
      const fallbackCapabilities = this.assistantCapabilities[fallbackId];
      if (!fallbackCapabilities || !fallbackCapabilities.roles.includes(role)) {
        throw new Error(`Fallback assistant ${fallbackId} cannot perform role ${role}`);
      }
    }
  }
  
  getAssistantCapabilities(assistantId) {
    return this.assistantCapabilities[assistantId];
  }
  
  getAssistantsForRole(role) {
    return Object.entries(this.assistantCapabilities)
      .filter(([assistantId, capabilities]) => capabilities.roles.includes(role))
      .map(([assistantId, capabilities]) => ({ assistantId, ...capabilities }));
  }
  
  updatePerformanceMetrics(role, assistantId, success, responseTime) {
    const rolePerf = this.rolePerformance.get(role);
    if (!rolePerf) return;
    
    rolePerf.totalRequests++;
    if (success) rolePerf.successfulRequests++;
    
    // Update average response time
    const totalTime = rolePerf.averageResponseTime * (rolePerf.totalRequests - 1) + responseTime;
    rolePerf.averageResponseTime = totalTime / rolePerf.totalRequests;
    
    // Update assistant-specific performance
    if (!rolePerf.assistantPerformance.has(assistantId)) {
      rolePerf.assistantPerformance.set(assistantId, {
        totalRequests: 0,
        successfulRequests: 0,
        averageResponseTime: 0,
        successRate: 0
      });
    }
    
    const assistantPerf = rolePerf.assistantPerformance.get(assistantId);
    assistantPerf.totalRequests++;
    if (success) assistantPerf.successfulRequests++;
    
    const assistantTotalTime = assistantPerf.averageResponseTime * (assistantPerf.totalRequests - 1) + responseTime;
    assistantPerf.averageResponseTime = assistantTotalTime / assistantPerf.totalRequests;
    assistantPerf.successRate = assistantPerf.successfulRequests / assistantPerf.totalRequests;
  }
  
  getRolePerformanceMetrics() {
    return Object.fromEntries(this.rolePerformance);
  }
}

module.exports = RoleManager;

"""
Drone AI Domain Implementation

Complete domain implementation that coordinates interpreter, matcher, and validator
for drone AI systems with comprehensive sensor data processing.
"""

import logging
from typing import Dict, Any
from ...core.interfaces import BaseDomain
from ...core.feedback_types import ValidationResult
from .interpreter import SensorDataInterpreter
from .matcher import Sensor<PERSON>ogMatcher
from .validator import DroneValidator


class DroneAIDomain(BaseDomain):
    """
    Complete domain implementation for Drone AI systems.
    
    Coordinates sensor data interpretation, pattern matching, and validation
    to provide comprehensive feedback on drone AI outputs and operations.
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize Drone AI domain.
        
        Args:
            config: Optional configuration dictionary for domain customization
        """
        super().__init__("drone_ai")
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # Domain-specific settings
        self.sensor_types = ['gps', 'imu', 'environmental', 'camera', 'lidar']
        self.mission_types = ['area_scanning', 'waypoint_navigation', 'sensor_monitoring', 'general']
        
        # Performance tracking
        self.processing_stats = {
            'total_processed': 0,
            'by_sensor_type': {},
            'by_mission_type': {},
            'average_processing_time': 0.0
        }
    
    def initialize_components(self) -> None:
        """Initialize domain-specific components."""
        try:
            # Initialize interpreter
            self.interpreter = SensorDataInterpreter()
            
            # Initialize matcher with custom thresholds if provided
            self.matcher = SensorLogMatcher()
            if 'thresholds' in self.config:
                for category, thresholds in self.config['thresholds'].items():
                    self.matcher.update_thresholds(category, thresholds)
            
            # Initialize validator
            self.validator = DroneValidator()
            
            # Add custom patterns if provided
            if 'custom_patterns' in self.config:
                for pattern_id, pattern_data in self.config['custom_patterns'].items():
                    self.matcher.add_pattern(pattern_id, pattern_data)
            
            # Add custom field mappings if provided
            if 'field_mappings' in self.config:
                for sensor_type, mappings in self.config['field_mappings'].items():
                    for standard_field, field_names in mappings.items():
                        self.interpreter.add_field_mapping(sensor_type, standard_field, field_names)
            
            self.logger.info("Drone AI domain components initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Drone AI domain components: {str(e)}")
            raise
    
    def process_output(self, raw_output: Any, context: Dict[str, Any]) -> ValidationResult:
        """
        Process drone AI output through the complete domain pipeline.
        
        Args:
            raw_output: Raw sensor data or drone AI output
            context: Additional context including mission info, sensor type, etc.
            
        Returns:
            ValidationResult with complete analysis
        """
        try:
            # Enhance context with domain-specific information
            enhanced_context = self._enhance_context(context)
            
            # Step 1: Interpret raw output
            self.logger.debug("Starting sensor data interpretation")
            interpreted_output = self.interpreter.interpret(raw_output, enhanced_context)
            
            # Step 2: Pattern matching
            self.logger.debug("Starting pattern matching")
            match_results = self.matcher.match(interpreted_output, enhanced_context)
            
            # Step 3: Validation
            self.logger.debug("Starting validation")
            validation_result = self.validator.validate(
                interpreted_output, match_results, enhanced_context
            )
            
            # Add domain-specific metadata
            validation_result.metadata.update({
                'domain': self.domain_name,
                'sensor_type': enhanced_context.get('sensor_type', 'unknown'),
                'mission_type': enhanced_context.get('mission_type', 'general'),
                'processing_pipeline': ['interpret', 'match', 'validate']
            })
            
            # Update statistics
            self._update_processing_stats(enhanced_context)
            
            self.logger.info(
                f"Processed drone AI output: "
                f"sensor_type={enhanced_context.get('sensor_type')}, "
                f"mission_type={enhanced_context.get('mission_type')}, "
                f"feedback_type={validation_result.feedback_type.value}, "
                f"confidence={validation_result.confidence_score:.3f}"
            )
            
            return validation_result
            
        except Exception as e:
            self.logger.error(f"Error processing drone AI output: {str(e)}")
            
            # Return error validation result
            error_result = ValidationResult()
            error_result.is_valid = False
            error_result.confidence_score = 0.0
            error_result.issues = [{
                'type': 'domain_processing_error',
                'severity': 'critical',
                'message': f'Domain processing failed: {str(e)}',
                'details': {'error': str(e), 'domain': self.domain_name}
            }]
            error_result.metadata = {
                'domain': self.domain_name,
                'error': True,
                'error_message': str(e)
            }
            
            return error_result
    
    def _enhance_context(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Enhance context with domain-specific information and defaults."""
        enhanced = context.copy()
        
        # Set default sensor type if not provided
        if 'sensor_type' not in enhanced:
            enhanced['sensor_type'] = self._detect_sensor_type(context)
        
        # Set default mission type if not provided
        if 'mission_type' not in enhanced:
            enhanced['mission_type'] = self._detect_mission_type(context)
        
        # Add domain capabilities
        enhanced['domain_capabilities'] = {
            'supported_sensor_types': self.sensor_types,
            'supported_mission_types': self.mission_types,
            'supports_partial_correctness': True,
            'supports_real_time': True,
            'supports_batch': True
        }
        
        # Add processing timestamp
        from datetime import datetime
        enhanced['domain_processing_timestamp'] = datetime.utcnow().isoformat()
        
        return enhanced
    
    def _detect_sensor_type(self, context: Dict[str, Any]) -> str:
        """Detect sensor type from context clues."""
        # Check for explicit sensor type indicators
        if 'gps' in str(context).lower() or any(key in context for key in ['latitude', 'longitude']):
            return 'gps'
        
        if 'imu' in str(context).lower() or any(key in context for key in ['acceleration', 'gyro']):
            return 'imu'
        
        if any(key in context for key in ['temperature', 'humidity', 'pressure']):
            return 'environmental'
        
        if 'camera' in str(context).lower() or 'image' in str(context).lower():
            return 'camera'
        
        if 'lidar' in str(context).lower() or 'point_cloud' in str(context).lower():
            return 'lidar'
        
        return 'mixed'  # Default for mixed or unknown sensor types
    
    def _detect_mission_type(self, context: Dict[str, Any]) -> str:
        """Detect mission type from context clues."""
        mission_keywords = {
            'area_scanning': ['scan', 'area', 'coverage', 'survey'],
            'waypoint_navigation': ['waypoint', 'navigation', 'route', 'path'],
            'sensor_monitoring': ['monitor', 'sensor', 'data_collection', 'measurement']
        }
        
        context_str = str(context).lower()
        
        for mission_type, keywords in mission_keywords.items():
            if any(keyword in context_str for keyword in keywords):
                return mission_type
        
        return 'general'  # Default mission type
    
    def _update_processing_stats(self, context: Dict[str, Any]) -> None:
        """Update domain processing statistics."""
        self.processing_stats['total_processed'] += 1
        
        sensor_type = context.get('sensor_type', 'unknown')
        self.processing_stats['by_sensor_type'][sensor_type] = (
            self.processing_stats['by_sensor_type'].get(sensor_type, 0) + 1
        )
        
        mission_type = context.get('mission_type', 'general')
        self.processing_stats['by_mission_type'][mission_type] = (
            self.processing_stats['by_mission_type'].get(mission_type, 0) + 1
        )
    
    def get_domain_info(self) -> Dict[str, Any]:
        """Get comprehensive information about this domain."""
        base_info = super().get_domain_info()
        
        domain_specific_info = {
            'supported_sensor_types': self.sensor_types,
            'supported_mission_types': self.mission_types,
            'processing_stats': self.processing_stats.copy(),
            'configuration': {
                'has_custom_thresholds': 'thresholds' in self.config,
                'has_custom_patterns': 'custom_patterns' in self.config,
                'has_custom_field_mappings': 'field_mappings' in self.config
            },
            'capabilities': {
                'partial_correctness': True,
                'real_time_processing': True,
                'batch_processing': True,
                'multi_sensor_support': True,
                'mission_specific_validation': True
            }
        }
        
        return {**base_info, **domain_specific_info}
    
    def get_sensor_types(self) -> list[str]:
        """Get list of supported sensor types."""
        return self.sensor_types.copy()
    
    def get_mission_types(self) -> list[str]:
        """Get list of supported mission types."""
        return self.mission_types.copy()
    
    def add_sensor_type(self, sensor_type: str) -> None:
        """Add support for a new sensor type."""
        if sensor_type not in self.sensor_types:
            self.sensor_types.append(sensor_type)
            self.processing_stats['by_sensor_type'][sensor_type] = 0
            self.logger.info(f"Added support for sensor type: {sensor_type}")
    
    def add_mission_type(self, mission_type: str) -> None:
        """Add support for a new mission type."""
        if mission_type not in self.mission_types:
            self.mission_types.append(mission_type)
            self.processing_stats['by_mission_type'][mission_type] = 0
            self.logger.info(f"Added support for mission type: {mission_type}")
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """Get detailed processing statistics for this domain."""
        return self.processing_stats.copy()
    
    def reset_stats(self) -> None:
        """Reset processing statistics."""
        self.processing_stats = {
            'total_processed': 0,
            'by_sensor_type': {},
            'by_mission_type': {},
            'average_processing_time': 0.0
        }
        self.logger.info("Reset processing statistics for Drone AI domain")

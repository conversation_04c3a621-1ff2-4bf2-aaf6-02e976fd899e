"use strict";
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.clearError = exports.logout = exports.initializeAuth = exports.register = exports.login = void 0;
const toolkit_1 = require("@reduxjs/toolkit");
const api_1 = require("../../services/api");
const initialState = {
    user: null,
    token: localStorage.getItem('token'),
    isLoading: false,
    error: null,
};
// Async thunks
exports.login = (0, toolkit_1.createAsyncThunk)('auth/login', async ({ email, password }) => {
    const response = await api_1.authApi.login(email, password);
    localStorage.setItem('token', response.data.token);
    return response.data;
});
exports.register = (0, toolkit_1.createAsyncThunk)('auth/register', async ({ email, username, password }) => {
    const response = await api_1.authApi.register(email, username, password);
    localStorage.setItem('token', response.data.token);
    return response.data;
});
exports.initializeAuth = (0, toolkit_1.createAsyncThunk)('auth/initialize', async (_, { getState }) => {
    const state = getState();
    const token = state.auth.token;
    if (!token) {
        throw new Error('No token found');
    }
    const response = await api_1.authApi.getCurrentUser();
    return response.data;
});
const authSlice = (0, toolkit_1.createSlice)({
    name: 'auth',
    initialState,
    reducers: {
        logout: (state) => {
            state.user = null;
            state.token = null;
            state.error = null;
            localStorage.removeItem('token');
        },
        clearError: (state) => {
            state.error = null;
        },
    },
    extraReducers: (builder) => {
        builder
            // Login
            .addCase(exports.login.pending, (state) => {
            state.isLoading = true;
            state.error = null;
        })
            .addCase(exports.login.fulfilled, (state, action) => {
            state.isLoading = false;
            state.user = action.payload.user;
            state.token = action.payload.token;
            state.error = null;
        })
            .addCase(exports.login.rejected, (state, action) => {
            state.isLoading = false;
            state.error = action.error.message || 'Login failed';
        })
            // Register
            .addCase(exports.register.pending, (state) => {
            state.isLoading = true;
            state.error = null;
        })
            .addCase(exports.register.fulfilled, (state, action) => {
            state.isLoading = false;
            state.user = action.payload.user;
            state.token = action.payload.token;
            state.error = null;
        })
            .addCase(exports.register.rejected, (state, action) => {
            state.isLoading = false;
            state.error = action.error.message || 'Registration failed';
        })
            // Initialize
            .addCase(exports.initializeAuth.pending, (state) => {
            state.isLoading = true;
        })
            .addCase(exports.initializeAuth.fulfilled, (state, action) => {
            state.isLoading = false;
            state.user = action.payload;
            state.error = null;
        })
            .addCase(exports.initializeAuth.rejected, (state) => {
            state.isLoading = false;
            state.user = null;
            state.token = null;
            localStorage.removeItem('token');
        });
    },
});
_a = authSlice.actions, exports.logout = _a.logout, exports.clearError = _a.clearError;
exports.default = authSlice.reducer;

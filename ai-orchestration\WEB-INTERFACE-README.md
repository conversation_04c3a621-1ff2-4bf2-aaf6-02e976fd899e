# AI Assistant Orchestration & Pruning Platform - Web Interface

## 🚀 Complete Web-Based AI Orchestration System

This is a comprehensive web interface for managing, orchestrating, and optimizing multiple AI coding assistants with intelligent context pruning and workflow automation.

## ✨ Features

### 🎯 Core Functionality
- **Multi-AI Agent Management**: Coordinate GitHub Copilot, Tabnine, Amazon Q, Cursor, Qodo AI, and Cline
- **Role-Based Assignment**: Assign specific roles (<PERSON>rator, <PERSON><PERSON><PERSON>, Completer, Validator) to different AI agents
- **Real-Time Workflow Execution**: Monitor and execute AI orchestration workflows with live progress tracking
- **Intelligent Context Pruning**: Automated data lifecycle management with configurable retention policies
- **Comprehensive Audit System**: Full activity logging and compliance tracking

### 📊 Dashboard Tabs

#### 1. General Summary
- Platform overview with key metrics
- Quick action buttons for common tasks
- Real-time status monitoring
- Workflow and pruning statistics

#### 2. Legal & Compliance
- GDPR compliance framework
- Security standards (SOC 2, ISO 27001)
- AI ethics and governance policies
- Data privacy and encryption details

#### 3. Architecture & Roles
- Interactive AI agent role assignment
- Dynamic dropdown selectors for each role
- Real-time role configuration updates
- Validation and conflict detection

#### 4. Workflow Example
- Workflow type selection and execution
- Real-time progress monitoring
- Results tracking and analytics
- Queue and status management

#### 5. Pruning & Archiving
- Automated pruning configuration
- Manual pruning controls
- Archive management and restoration
- Space optimization tracking

#### 6. Audit Dashboard
- Activity log monitoring
- Compliance reporting
- Export capabilities
- Filter and search functionality

## 🛠️ Installation & Setup

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn package manager

### Quick Start

1. **Install Dependencies**
   ```bash
   npm install
   ```

2. **Start Web Interface Only**
   ```bash
   npm run web
   ```

3. **Start Complete System (CLI + Web)**
   ```bash
   npm run web-dev
   ```

4. **Use the Launcher (Windows)**
   ```bash
   AI-Orchestration-Web-Launcher.bat
   ```

### Access Points
- **Web Dashboard**: http://localhost:3001
- **API Endpoints**: http://localhost:3001/api/*
- **WebSocket**: Real-time updates via Socket.io

## 🎮 Usage Guide

### Starting the System

#### Option 1: Web Launcher (Recommended)
Run `AI-Orchestration-Web-Launcher.bat` and select:
- **[1]** Web Interface Only
- **[2]** Complete System (CLI + Web)
- **[3]** Development Mode (with auto-reload)

#### Option 2: Command Line
```bash
# Web interface only
npm run web

# Development mode with auto-reload
npm run web-dev

# CLI system only
node orchestrator.js
```

### Configuring AI Agents

1. Navigate to **Architecture & Roles** tab
2. Select AI agents for each role:
   - **Generator**: Code creation and generation
   - **Analyzer**: Code review and analysis
   - **Completer**: Auto-completion and suggestions
   - **Validator**: Quality assurance and testing
3. Click **Update Role Assignments**
4. Use **Validate Configuration** to check for conflicts

### Running Workflows

1. Go to **Workflow Example** tab
2. Select workflow type:
   - Code Generation
   - Code Review
   - Testing & Validation
   - Code Refactoring
   - Documentation
3. Click **Execute Workflow**
4. Monitor real-time progress
5. View results and analytics

### Managing Context Pruning

1. Access **Pruning & Archiving** tab
2. Configure automated settings:
   - **Frequency**: Weekly, Bi-Weekly, Monthly
   - **Retention**: Days to keep data
   - **Archive Location**: Local, Cloud, Hybrid
3. Save configuration or run manual pruning
4. Monitor space savings and optimization

## 🔧 API Reference

### Core Endpoints

#### Status & Health
- `GET /api/status` - System status and health check
- `GET /api/agents` - Available AI agents and capabilities

#### Role Management
- `GET /api/roles` - Current role assignments
- `POST /api/roles/assign` - Update role assignments

#### Workflow Control
- `POST /api/workflow/run` - Execute workflow
- `GET /api/workflow/status` - Workflow statistics

#### Pruning Operations
- `POST /api/pruning/configure` - Update pruning settings
- `GET /api/pruning/status` - Pruning status and metrics
- `POST /api/pruning/run` - Manual pruning execution

#### Audit & Compliance
- `GET /api/audit/logs` - Activity logs
- `POST /api/config/save` - Save configuration
- `GET /api/config/load` - Load configuration

### WebSocket Events

#### Client → Server
- `subscribe-logs` - Subscribe to log updates
- `subscribe-workflow` - Subscribe to workflow updates

#### Server → Client
- `roles-updated` - Role assignment changes
- `workflow-progress` - Workflow execution progress
- `workflow-complete` - Workflow completion
- `pruning-progress` - Pruning operation progress
- `pruning-complete` - Pruning completion

## 🏗️ Architecture

### Frontend Components
- **React-like Interface**: Modern, responsive web UI
- **Real-time Updates**: WebSocket-based live data
- **Progressive Enhancement**: Works without JavaScript
- **Mobile Responsive**: Optimized for all devices

### Backend Services
- **Express.js Server**: RESTful API and static file serving
- **Socket.io Integration**: Real-time bidirectional communication
- **Security Middleware**: Helmet, CORS, rate limiting
- **File System Management**: Configuration and log handling

### Data Flow
1. **User Interaction** → Frontend UI
2. **API Calls** → Express.js Backend
3. **Real-time Updates** → WebSocket Events
4. **Configuration Storage** → JSON Files
5. **AI Orchestration** → Backend Integration

## 🔒 Security Features

### Built-in Protection
- **Rate Limiting**: API abuse prevention
- **CORS Configuration**: Cross-origin request security
- **Content Security Policy**: XSS protection
- **Input Validation**: Request sanitization
- **Secure Headers**: Helmet.js security headers

### Compliance Standards
- **GDPR Ready**: Data privacy and user rights
- **SOC 2 Type II**: Security audit compliance
- **ISO 27001**: Information security management
- **Audit Trails**: Complete activity logging

## 🚀 Advanced Features

### Development Mode
- **Auto-reload**: Automatic server restart on changes
- **Debug Logging**: Detailed console output
- **Hot Module Replacement**: Frontend live updates
- **Error Handling**: Comprehensive error reporting

### Production Deployment
- **Process Management**: PM2 integration ready
- **Load Balancing**: Multi-instance support
- **Monitoring**: Health checks and metrics
- **Logging**: Structured log output

### Extensibility
- **Plugin Architecture**: Modular component system
- **API Extensions**: Custom endpoint integration
- **Theme Support**: Customizable UI themes
- **Webhook Integration**: External service notifications

## 📊 Monitoring & Analytics

### Real-time Metrics
- Active workflow count
- Completed operations
- System resource usage
- AI agent performance

### Historical Data
- Workflow execution history
- Pruning operation logs
- Configuration change tracking
- User activity analytics

### Performance Optimization
- Automatic context pruning
- Resource usage monitoring
- Cache management
- Database optimization

## 🛠️ Troubleshooting

### Common Issues

#### Web Server Won't Start
```bash
# Check if port 3001 is in use
netstat -an | findstr ":3001"

# Kill existing Node.js processes
taskkill /f /im node.exe

# Restart the server
npm run web
```

#### WebSocket Connection Failed
- Check firewall settings
- Verify port 3001 accessibility
- Ensure no proxy interference

#### Role Assignment Not Saving
- Check file permissions in config directory
- Verify JSON file integrity
- Review browser console for errors

### Debug Mode
```bash
# Enable debug logging
DEBUG=* npm run web

# Check system status
node orchestrator.js test
```

## 📝 Contributing

### Development Setup
1. Fork the repository
2. Install dependencies: `npm install`
3. Start development mode: `npm run web-dev`
4. Make changes and test
5. Submit pull request

### Code Standards
- ESLint configuration
- Prettier formatting
- JSDoc documentation
- Unit test coverage

## 📄 License

This project is part of Chris's Time Stamp Project ecosystem, designed for solo developers who need enterprise-grade AI orchestration capabilities without enterprise resources.

## 🆘 Support

For issues, questions, or feature requests:
1. Check the troubleshooting section
2. Review the API documentation
3. Use the built-in audit logs for debugging
4. Check the GitHub repository for updates

---

**Built with ❤️ for the AI development community**

/**
 * AI Assistant Orchestration Platform - Frontend Application
 */

class OrchestrationApp {
    constructor() {
        this.socket = null;
        this.currentRoles = {
            generator: ['chatgpt-4o', 'claude-opus'],
            analyzer: ['perplexity', 'cursor'],
            completer: ['github-copilot', 'tabnine'],
            validator: ['claude-validator', 'amazon-q'],
            'code-executor': ['gemini-exec', 'claudia-exec'],
            'thread-merger': ['perplexity-thread', 'chatgpt-bridge'],
            memory: ['toolkit-memory', 'vector-bug-memory'],
            debugger: ['q-validator-aws', 'amazon-q']
        };
        this.workflowStatus = {};
        this.pruningConfig = {};
        this.availableAgents = [];
        this.agentRegistry = {};

        this.init();
    }
    
    init() {
        this.setupSocketConnection();
        this.setupEventListeners();
        this.setupTabs();
        this.loadInitialData();
        this.updateTimestamp();
        
        // Update timestamp every minute
        setInterval(() => this.updateTimestamp(), 60000);
    }
    
    setupSocketConnection() {
        this.socket = io();
        
        this.socket.on('connect', () => {
            console.log('Connected to server');
            this.updateConnectionStatus(true);
            this.socket.emit('subscribe-logs');
            this.socket.emit('subscribe-workflow');
        });
        
        this.socket.on('disconnect', () => {
            console.log('Disconnected from server');
            this.updateConnectionStatus(false);
        });
        
        this.socket.on('roles-updated', (roles) => {
            this.currentRoles = roles;
            this.updateRoleDisplay();
        });
        
        this.socket.on('workflow-progress', (data) => {
            this.updateWorkflowProgress(data);
        });
        
        this.socket.on('workflow-complete', (data) => {
            this.handleWorkflowComplete(data);
        });
        
        this.socket.on('pruning-progress', (data) => {
            this.updatePruningProgress(data);
        });
        
        this.socket.on('pruning-complete', (data) => {
            this.handlePruningComplete(data);
        });

        // Multi-agent event handlers
        this.socket.on('multi-agents-updated', (roleAssignments) => {
            this.currentRoles = roleAssignments;
            this.updateMultiAgentDisplay();
        });

        this.socket.on('thread-merge-progress', (data) => {
            this.updateThreadMergeProgress(data);
        });

        this.socket.on('thread-merge-complete', (data) => {
            this.displayThreadMergeResult(data);
        });

        this.socket.on('code-execution-progress', (data) => {
            this.updateCodeExecutionProgress(data);
        });

        this.socket.on('code-execution-complete', (data) => {
            this.displayCodeExecutionResults(data);
        });

        this.socket.on('chain-simulation-step', (data) => {
            this.updateChainSimulationStep(data);
        });

        this.socket.on('chain-simulation-complete', (data) => {
            this.displayChainSimulationResult(data);
        });
    }
    
    setupEventListeners() {
        // Role dropdown changes
        document.getElementById('generator-select').addEventListener('change', this.handleRoleChange.bind(this));
        document.getElementById('analyzer-select').addEventListener('change', this.handleRoleChange.bind(this));
        document.getElementById('completer-select').addEventListener('change', this.handleRoleChange.bind(this));
        document.getElementById('validator-select').addEventListener('change', this.handleRoleChange.bind(this));
    }
    
    setupTabs() {
        const tabButtons = document.querySelectorAll('.tab-button');
        const tabPanes = document.querySelectorAll('.tab-pane');
        
        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                const targetTab = button.getAttribute('data-tab');
                
                // Remove active class from all tabs and panes
                tabButtons.forEach(btn => btn.classList.remove('active'));
                tabPanes.forEach(pane => pane.classList.remove('active'));
                
                // Add active class to clicked tab and corresponding pane
                button.classList.add('active');
                document.getElementById(targetTab).classList.add('active');
            });
        });
    }
    
    async loadInitialData() {
        try {
            // Load agents and registry
            await this.loadAgents();
            await this.loadAgentRegistry();

            // Load current roles
            const rolesResponse = await fetch('/api/roles');
            this.currentRoles = await rolesResponse.json();
            this.updateRoleDisplay();
            this.updateRoleDropdowns();
            
            // Load workflow status
            const workflowResponse = await fetch('/api/workflow/status');
            this.workflowStatus = await workflowResponse.json();
            this.updateWorkflowStatus();
            
            // Load pruning status
            const pruningResponse = await fetch('/api/pruning/status');
            const pruningStatus = await pruningResponse.json();
            this.updatePruningStatus(pruningStatus);
            
            // Load audit logs
            this.loadAuditLogs();
            
        } catch (error) {
            console.error('Error loading initial data:', error);
        }
    }
    
    updateConnectionStatus(connected) {
        const statusElement = document.getElementById('connection-status');
        const statusDot = document.querySelector('.status-dot');
        
        if (connected) {
            statusElement.textContent = 'Connected';
            statusDot.classList.remove('offline');
        } else {
            statusElement.textContent = 'Disconnected';
            statusDot.classList.add('offline');
        }
    }
    
    updateTimestamp() {
        const timestampElement = document.getElementById('last-updated');
        timestampElement.textContent = new Date().toLocaleTimeString();
    }
    
    handleRoleChange() {
        const generator = document.getElementById('generator-select').value;
        const analyzer = document.getElementById('analyzer-select').value;
        const completer = document.getElementById('completer-select').value;
        const validator = document.getElementById('validator-select').value;
        
        this.currentRoles = { generator, analyzer, completer, validator };
        this.updateRoleDisplay();
    }
    
    updateRoleDisplay() {
        document.getElementById('current-generator').textContent = this.getAgentName(this.currentRoles.generator);
        document.getElementById('current-analyzer').textContent = this.getAgentName(this.currentRoles.analyzer);
        document.getElementById('current-completer').textContent = this.getAgentName(this.currentRoles.completer);
        document.getElementById('current-validator').textContent = this.getAgentName(this.currentRoles.validator);
    }
    
    updateRoleDropdowns() {
        document.getElementById('generator-select').value = this.currentRoles.generator || 'github-copilot';
        document.getElementById('analyzer-select').value = this.currentRoles.analyzer || 'cursor';
        document.getElementById('completer-select').value = this.currentRoles.completer || 'tabnine';
        document.getElementById('validator-select').value = this.currentRoles.validator || 'amazon-q';
    }
    
    getAgentName(agentId) {
        const agentNames = {
            'github-copilot': 'GitHub Copilot',
            'tabnine': 'Tabnine',
            'amazon-q': 'Amazon Q',
            'cursor': 'Cursor',
            'qodo-ai': 'Qodo AI',
            'cline': 'Cline'
        };
        return agentNames[agentId] || agentId;
    }
    
    updateWorkflowStatus() {
        document.getElementById('active-workflows-count').textContent = this.workflowStatus.active || 0;
        document.getElementById('completed-workflows').textContent = this.workflowStatus.completed || 0;
        document.getElementById('failed-workflows').textContent = this.workflowStatus.failed || 0;
        document.getElementById('queued-workflows').textContent = this.workflowStatus.queued || 0;
        document.getElementById('active-workflows').textContent = this.workflowStatus.active || 0;
        document.getElementById('total-workflows').textContent = (this.workflowStatus.completed || 0) + (this.workflowStatus.failed || 0);
    }
    
    updateWorkflowProgress(data) {
        const progressFill = document.getElementById('progress-fill');
        const progressText = document.getElementById('progress-text');
        
        progressFill.style.width = `${data.progress}%`;
        progressText.textContent = data.message || `Progress: ${data.progress}%`;
    }
    
    handleWorkflowComplete(data) {
        const resultsContainer = document.getElementById('workflow-results-content');
        
        const resultElement = document.createElement('div');
        resultElement.className = 'workflow-result';
        resultElement.innerHTML = `
            <div class="result-header">
                <strong>Workflow Completed</strong>
                <span class="timestamp">${new Date().toLocaleTimeString()}</span>
            </div>
            <div class="result-stats">
                Generated: ${data.results.generated || 0} | 
                Analyzed: ${data.results.analyzed || 0} | 
                Validated: ${data.results.validated || 0} | 
                Completed: ${data.results.completed || 0}
            </div>
        `;
        
        if (resultsContainer.querySelector('.no-results')) {
            resultsContainer.innerHTML = '';
        }
        
        resultsContainer.insertBefore(resultElement, resultsContainer.firstChild);
        
        // Update workflow status
        this.loadInitialData();
    }
    
    updatePruningStatus(status) {
        document.getElementById('last-prune-run').textContent = new Date(status.lastRun).toLocaleDateString();
        document.getElementById('next-prune-run').textContent = new Date(status.nextRun).toLocaleDateString();
        document.getElementById('items-pruned').textContent = status.itemsPruned || 0;
        document.getElementById('space-saved-display').textContent = status.spaceSaved || '0 MB';
        document.getElementById('pruned-items').textContent = status.itemsPruned || 0;
        document.getElementById('space-saved').textContent = status.spaceSaved || '0 MB';
    }
    
    updatePruningProgress(data) {
        // Show pruning progress in a notification or modal
        console.log('Pruning progress:', data);
    }
    
    handlePruningComplete(data) {
        // Update pruning status and show completion notification
        console.log('Pruning completed:', data);
        this.loadInitialData();
    }
    
    async loadAuditLogs() {
        try {
            const response = await fetch('/api/audit/logs');
            const logs = await response.json();
            
            const logsContainer = document.getElementById('audit-logs-content');
            logsContainer.innerHTML = '';
            
            logs.forEach(log => {
                const logElement = document.createElement('div');
                logElement.className = 'log-entry';
                logElement.innerHTML = `
                    <span class="timestamp">${new Date(log.timestamp).toLocaleString()}</span>
                    <span class="action">${log.action}</span>
                    <span class="details">${log.details}</span>
                `;
                logsContainer.appendChild(logElement);
            });
        } catch (error) {
            console.error('Error loading audit logs:', error);
        }
    }
}

// Global functions for button handlers
async function updateRoles() {
    try {
        const response = await fetch('/api/roles/assign', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ roles: app.currentRoles })
        });
        
        if (response.ok) {
            showNotification('Role assignments updated successfully!', 'success');
        } else {
            showNotification('Failed to update role assignments', 'error');
        }
    } catch (error) {
        console.error('Error updating roles:', error);
        showNotification('Error updating role assignments', 'error');
    }
}

async function validateRoles() {
    // Check for conflicts in role assignments
    const roles = Object.values(app.currentRoles);
    const duplicates = roles.filter((role, index) => roles.indexOf(role) !== index);
    
    if (duplicates.length > 0) {
        showNotification('Warning: Some agents are assigned to multiple roles', 'warning');
    } else {
        showNotification('Role configuration is valid', 'success');
    }
}

async function executeWorkflow() {
    const workflowType = document.getElementById('workflow-type').value;
    
    try {
        const response = await fetch('/api/workflow/run', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ workflowType, parameters: {} })
        });
        
        if (response.ok) {
            showNotification('Workflow execution started!', 'success');
        } else {
            showNotification('Failed to start workflow', 'error');
        }
    } catch (error) {
        console.error('Error executing workflow:', error);
        showNotification('Error executing workflow', 'error');
    }
}

async function runSimulation() {
    showNotification('Running simulation...', 'info');
    await executeWorkflow();
}

async function saveConfiguration() {
    try {
        const config = {
            roles: app.currentRoles,
            pruning: app.pruningConfig,
            timestamp: new Date().toISOString()
        };
        
        const response = await fetch('/api/config/save', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ config })
        });
        
        if (response.ok) {
            showNotification('Configuration saved successfully!', 'success');
        } else {
            showNotification('Failed to save configuration', 'error');
        }
    } catch (error) {
        console.error('Error saving configuration:', error);
        showNotification('Error saving configuration', 'error');
    }
}

async function runPruning() {
    try {
        const response = await fetch('/api/pruning/run', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({})
        });
        
        if (response.ok) {
            showNotification('Manual pruning started!', 'success');
        } else {
            showNotification('Failed to start pruning', 'error');
        }
    } catch (error) {
        console.error('Error running pruning:', error);
        showNotification('Error running pruning', 'error');
    }
}

async function runManualPruning() {
    await runPruning();
}

async function previewPruning() {
    showNotification('Pruning preview feature coming soon!', 'info');
}

async function restoreArchived() {
    showNotification('Archive restoration feature coming soon!', 'info');
}

async function savePruningConfig() {
    const frequency = document.getElementById('prune-frequency').value;
    const retentionDays = document.getElementById('retention-days').value;
    const archiveLocation = document.getElementById('archive-location').value;
    
    try {
        const response = await fetch('/api/pruning/configure', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ frequency, retentionDays, archiveLocation })
        });
        
        if (response.ok) {
            showNotification('Pruning configuration saved!', 'success');
        } else {
            showNotification('Failed to save pruning configuration', 'error');
        }
    } catch (error) {
        console.error('Error saving pruning config:', error);
        showNotification('Error saving pruning configuration', 'error');
    }
}

async function refreshLogs() {
    await app.loadAuditLogs();
    showNotification('Audit logs refreshed', 'success');
}

async function exportLogs() {
    showNotification('Log export feature coming soon!', 'info');
}

function viewLogs() {
    // Switch to audit tab
    document.querySelector('[data-tab="audit"]').click();
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    // Style the notification
    Object.assign(notification.style, {
        position: 'fixed',
        top: '20px',
        right: '20px',
        padding: '12px 20px',
        borderRadius: '6px',
        color: 'white',
        fontWeight: '500',
        zIndex: '1000',
        opacity: '0',
        transform: 'translateX(100%)',
        transition: 'all 0.3s ease'
    });
    
    // Set background color based on type
    const colors = {
        success: '#10b981',
        error: '#ef4444',
        warning: '#f59e0b',
        info: '#3b82f6'
    };
    notification.style.backgroundColor = colors[type] || colors.info;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.style.opacity = '1';
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Remove after 3 seconds
    setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
    }

    // Multi-Agent Methods
    async loadAgents() {
        try {
            const response = await fetch('/api/agents');
            this.availableAgents = await response.json();
        } catch (error) {
            console.error('Error loading agents:', error);
        }
    }

    async loadAgentRegistry() {
        try {
            const response = await fetch('/api/agents/registry');
            this.agentRegistry = await response.json();
            this.populateAgentDropdowns();
        } catch (error) {
            console.error('Error loading agent registry:', error);
        }
    }

    populateAgentDropdowns() {
        const roles = ['generator', 'analyzer', 'completer', 'validator', 'code-executor', 'thread-merger', 'memory', 'debugger'];

        roles.forEach(role => {
            const dropdown = document.querySelector(`[data-role="${role}"]`);
            if (dropdown) {
                dropdown.innerHTML = '<option value="">+ Add Agent</option>';

                // Get compatible agents for this role
                const compatibleAgents = this.agentRegistry.roleCapabilities[role] || [];

                this.availableAgents.forEach(agent => {
                    if (compatibleAgents.includes(agent.id)) {
                        const option = document.createElement('option');
                        option.value = agent.id;
                        option.textContent = agent.name;
                        dropdown.appendChild(option);
                    }
                });
            }
        });
    }

    updateMultiAgentDisplay() {
        Object.keys(this.currentRoles).forEach(role => {
            const container = document.getElementById(`${role}-selected`);
            if (container) {
                container.innerHTML = '';

                this.currentRoles[role].forEach(agentId => {
                    const agent = this.availableAgents.find(a => a.id === agentId);
                    if (agent) {
                        const tag = document.createElement('span');
                        tag.className = 'agent-tag';
                        tag.innerHTML = `${agent.name} <button class="remove-agent" data-role="${role}" data-agent="${agentId}">×</button>`;
                        container.appendChild(tag);
                    }
                });
            }
        });
    }

    updateThreadMergeProgress(data) {
        console.log('Thread merge progress:', data);
    }

    displayThreadMergeResult(data) {
        const outputContainer = document.getElementById('merged-output');
        if (outputContainer) {
            outputContainer.innerHTML = `
                <div class="merge-result">
                    <h4>🎯 Merged Content</h4>
                    <p>${data.result.mergedContent}</p>
                </div>
            `;
        }

        // Update stats
        if (document.getElementById('conflicts-resolved')) {
            document.getElementById('conflicts-resolved').textContent = data.result.conflictsResolved;
            document.getElementById('confidence-score').textContent = (data.result.confidenceScore * 100).toFixed(1) + '%';
            document.getElementById('source-agents').textContent = data.result.sourceAgents.join(', ');
            document.getElementById('processing-time').textContent = '3.2s';
        }
    }

    updateCodeExecutionProgress(data) {
        console.log('Code execution progress:', data);
    }

    displayCodeExecutionResults(data) {
        const resultsGrid = document.getElementById('execution-results-grid');
        if (resultsGrid) {
            resultsGrid.innerHTML = '';

            data.results.forEach(result => {
                const resultCard = document.createElement('div');
                resultCard.className = 'result-card';
                resultCard.innerHTML = `
                    <div class="result-header">
                        <span class="agent-name">${result.agent}</span>
                        <span class="execution-status ${result.success ? 'success' : 'error'}">
                            ${result.success ? 'Success' : 'Error'}
                        </span>
                    </div>
                    <div class="result-output">${result.output}</div>
                    <div class="execution-time">Execution time: ${result.executionTime.toFixed(0)}ms</div>
                `;
                resultsGrid.appendChild(resultCard);
            });
        }

        // Update consensus analysis
        const consensusContainer = document.getElementById('consensus-analysis');
        if (consensusContainer) {
            consensusContainer.innerHTML = `
                <div class="consensus-result">
                    <h4>🎯 Consensus: ${data.consensus ? 'AGREEMENT' : 'DISAGREEMENT'}</h4>
                    <p>Success rate: ${(data.results.filter(r => r.success).length / data.results.length * 100).toFixed(1)}%</p>
                </div>
            `;
        }
    }

    updateChainSimulationStep(data) {
        console.log('Chain simulation step:', data);
    }

    displayChainSimulationResult(data) {
        console.log('Chain simulation complete:', data);
    }
}

// Global functions for HTML onclick handlers
function executeCode() {
    const code = document.getElementById('code-input').value;
    const language = document.getElementById('language-select').value;
    const executorAgents = Array.from(document.querySelectorAll('.execution-settings .agent-checkboxes input[type="checkbox"]:checked'))
        .map(cb => cb.value);

    if (!code.trim()) {
        app.showNotification('Please enter code to execute', 'warning');
        return;
    }

    if (executorAgents.length === 0) {
        app.showNotification('Please select at least one execution agent', 'warning');
        return;
    }

    fetch('/api/code/execute', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ code, executorAgents, language })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            app.showNotification('Code execution started', 'info');
        }
    })
    .catch(error => {
        console.error('Error executing code:', error);
        app.showNotification('Error starting code execution', 'error');
    });
}

function mergeThreads() {
    const threads = Array.from(document.querySelectorAll('.thread-textarea'))
        .map(textarea => textarea.value)
        .filter(content => content.trim());

    const mergerAgents = Array.from(document.querySelectorAll('.merger-settings .agent-checkboxes input[type="checkbox"]:checked'))
        .map(cb => cb.value);

    if (threads.length < 2) {
        app.showNotification('Please provide at least 2 threads to merge', 'warning');
        return;
    }

    if (mergerAgents.length === 0) {
        app.showNotification('Please select at least one merger agent', 'warning');
        return;
    }

    fetch('/api/thread/merge', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ threads, mergerAgents })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            app.showNotification('Thread merging started', 'info');
        }
    })
    .catch(error => {
        console.error('Error merging threads:', error);
        app.showNotification('Error starting thread merge', 'error');
    });
}

// Initialize the application
let app;
document.addEventListener('DOMContentLoaded', () => {
    app = new OrchestrationApp();
});

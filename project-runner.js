#!/usr/bin/env node

/**
 * Universal Project Runner & Validator
 * 
 * Comprehensive testing and validation system for all projects in the workspace.
 * Automatically detects project types and runs appropriate tests and validations.
 * 
 * Author: <PERSON> (Solo Developer)
 * Version: 1.0.0
 */

import fs from 'fs-extra';
import path from 'path';
import { execSync, spawn } from 'child_process';
import chalk from 'chalk';
import ora from 'ora';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class ProjectRunner {
  constructor() {
    this.workspaceRoot = process.cwd();
    this.results = {
      projects: [],
      summary: {
        total: 0,
        passed: 0,
        failed: 0,
        skipped: 0
      }
    };
    this.runningProcesses = new Map();
  }

  async run(options = {}) {
    console.log(chalk.blue.bold('🚀 Universal Project Runner & Validator'));
    console.log(chalk.gray('Testing and validating all projects in workspace'));
    console.log(chalk.gray('=' .repeat(60)));
    console.log('');

    try {
      await this.discoverProjects();
      
      if (options.list) {
        this.listProjects();
        return;
      }

      if (options.project) {
        await this.runSingleProject(options.project);
      } else {
        await this.runAllProjects(options);
      }

      this.displaySummary();
    } catch (error) {
      console.error(chalk.red(`❌ Runner failed: ${error.message}`));
      process.exit(1);
    }
  }

  async discoverProjects() {
    const spinner = ora('🔍 Discovering projects...').start();
    
    const projectDirs = [
      'core-systems',
      'development-tools', 
      'ai-orchestration',
      'core-systems/EcoStamp/source',
      'distribution'
    ];

    for (const dir of projectDirs) {
      const fullPath = path.join(this.workspaceRoot, dir);
      if (await fs.pathExists(fullPath)) {
        const project = await this.analyzeProject(fullPath, dir);
        if (project) {
          this.results.projects.push(project);
        }
      }
    }

    this.results.summary.total = this.results.projects.length;
    spinner.succeed(chalk.green(`✅ Found ${this.results.projects.length} projects`));
  }

  async analyzeProject(projectPath, relativePath) {
    const project = {
      name: path.basename(projectPath),
      path: projectPath,
      relativePath,
      type: 'unknown',
      framework: 'none',
      entryPoints: [],
      testCommands: [],
      runCommands: [],
      status: 'pending',
      errors: [],
      canRun: false,
      canTest: false
    };

    try {
      // Check for package.json (Node.js projects)
      const packageJsonPath = path.join(projectPath, 'package.json');
      if (await fs.pathExists(packageJsonPath)) {
        const packageJson = await fs.readJson(packageJsonPath);
        project.type = 'nodejs';
        project.framework = this.detectNodeFramework(packageJson);
        project.entryPoints = this.extractNodeEntryPoints(packageJson);
        project.testCommands = this.extractNodeTestCommands(packageJson);
        project.runCommands = this.extractNodeRunCommands(packageJson);
        project.canRun = project.runCommands.length > 0;
        project.canTest = project.testCommands.length > 0;
        project.packageJson = packageJson;
      }

      // Check for Python projects
      const setupPyPath = path.join(projectPath, 'setup.py');
      const requirementsPath = path.join(projectPath, 'requirements.txt');
      const pyprojectPath = path.join(projectPath, 'pyproject.toml');
      
      if (await fs.pathExists(setupPyPath) || 
          await fs.pathExists(requirementsPath) || 
          await fs.pathExists(pyprojectPath)) {
        project.type = 'python';
        project.entryPoints = await this.findPythonEntryPoints(projectPath);
        project.testCommands = await this.extractPythonTestCommands(projectPath);
        project.runCommands = await this.extractPythonRunCommands(projectPath);
        project.canRun = project.runCommands.length > 0 || project.entryPoints.length > 0;
        project.canTest = project.testCommands.length > 0;
      }

      // Check for browser extension
      const manifestPath = path.join(projectPath, 'manifest.json');
      if (await fs.pathExists(manifestPath)) {
        project.type = 'browser-extension';
        project.framework = 'web-extension';
        project.canRun = false; // Extensions need browser
        project.canTest = false; // Would need special testing setup
      }

      // Check for static files/documentation
      if (project.type === 'unknown') {
        const htmlFiles = await this.findFiles(projectPath, '*.html');
        const mdFiles = await this.findFiles(projectPath, '*.md');
        if (htmlFiles.length > 0 || mdFiles.length > 0) {
          project.type = 'static';
          project.framework = 'documentation';
          project.canRun = htmlFiles.length > 0; // Can open HTML files
          project.canTest = false;
        }
      }

    } catch (error) {
      project.errors.push(`Analysis failed: ${error.message}`);
    }

    return project;
  }

  detectNodeFramework(packageJson) {
    const deps = { ...packageJson.dependencies, ...packageJson.devDependencies };
    
    if (deps.express) return 'express';
    if (deps.react) return 'react';
    if (deps.vue) return 'vue';
    if (deps.angular) return 'angular';
    if (deps.next) return 'nextjs';
    if (deps.nuxt) return 'nuxtjs';
    if (deps.fastify) return 'fastify';
    if (deps.koa) return 'koa';
    
    return 'nodejs';
  }

  extractNodeEntryPoints(packageJson) {
    const entryPoints = [];
    
    if (packageJson.main) entryPoints.push(packageJson.main);
    if (packageJson.bin) {
      if (typeof packageJson.bin === 'string') {
        entryPoints.push(packageJson.bin);
      } else {
        entryPoints.push(...Object.values(packageJson.bin));
      }
    }
    
    return entryPoints;
  }

  extractNodeTestCommands(packageJson) {
    const commands = [];
    const scripts = packageJson.scripts || {};
    
    // Look for test-related scripts
    Object.keys(scripts).forEach(script => {
      if (script.includes('test') || script.includes('spec')) {
        commands.push(`npm run ${script}`);
      }
    });
    
    return commands;
  }

  extractNodeRunCommands(packageJson) {
    const commands = [];
    const scripts = packageJson.scripts || {};
    
    // Look for run-related scripts
    ['start', 'dev', 'serve', 'run', 'scan'].forEach(cmd => {
      if (scripts[cmd]) {
        commands.push(`npm run ${cmd}`);
      }
    });
    
    return commands;
  }

  async findPythonEntryPoints(projectPath) {
    const entryPoints = [];
    const commonFiles = ['main.py', 'app.py', '__main__.py', 'run.py', 'server.py'];
    
    for (const file of commonFiles) {
      if (await fs.pathExists(path.join(projectPath, file))) {
        entryPoints.push(file);
      }
    }
    
    return entryPoints;
  }

  async extractPythonTestCommands(projectPath) {
    const commands = [];
    
    // Check for pytest
    if (await fs.pathExists(path.join(projectPath, 'pytest.ini')) ||
        await fs.pathExists(path.join(projectPath, 'pyproject.toml'))) {
      commands.push('pytest');
    }
    
    // Check for unittest
    const testDir = path.join(projectPath, 'tests');
    if (await fs.pathExists(testDir)) {
      commands.push('python -m unittest discover tests');
    }
    
    return commands;
  }

  async extractPythonRunCommands(projectPath) {
    const commands = [];
    
    // Check setup.py
    if (await fs.pathExists(path.join(projectPath, 'setup.py'))) {
      commands.push('python setup.py install');
    }
    
    return commands;
  }

  async findFiles(dir, pattern) {
    try {
      const files = await fs.readdir(dir);
      return files.filter(file => {
        const regex = new RegExp(pattern.replace('*', '.*'));
        return regex.test(file);
      });
    } catch {
      return [];
    }
  }

  listProjects() {
    console.log(chalk.yellow.bold('📋 Discovered Projects:'));
    console.log('');
    
    this.results.projects.forEach((project, index) => {
      console.log(chalk.cyan(`${index + 1}. ${project.name}`));
      console.log(chalk.gray(`   Path: ${project.relativePath}`));
      console.log(chalk.gray(`   Type: ${project.type} (${project.framework})`));
      console.log(chalk.gray(`   Can Run: ${project.canRun ? '✅' : '❌'}`));
      console.log(chalk.gray(`   Can Test: ${project.canTest ? '✅' : '❌'}`));
      
      if (project.runCommands.length > 0) {
        console.log(chalk.gray(`   Run Commands: ${project.runCommands.join(', ')}`));
      }
      
      if (project.testCommands.length > 0) {
        console.log(chalk.gray(`   Test Commands: ${project.testCommands.join(', ')}`));
      }
      
      console.log('');
    });
  }

  async runAllProjects(options = {}) {
    console.log(chalk.yellow.bold('🏃 Running All Projects:'));
    console.log('');
    
    for (const project of this.results.projects) {
      await this.runProject(project, options);
    }
  }

  async runSingleProject(projectName) {
    const project = this.results.projects.find(p => 
      p.name.toLowerCase() === projectName.toLowerCase() ||
      p.relativePath.toLowerCase().includes(projectName.toLowerCase())
    );
    
    if (!project) {
      console.error(chalk.red(`❌ Project '${projectName}' not found`));
      return;
    }
    
    await this.runProject(project);
  }

  async runProject(project, options = {}) {
    console.log(chalk.cyan.bold(`\n📦 ${project.name} (${project.type})`));
    console.log(chalk.gray(`   ${project.relativePath}`));
    
    if (project.errors.length > 0) {
      console.log(chalk.red(`   ❌ Analysis errors: ${project.errors.join(', ')}`));
      project.status = 'failed';
      this.results.summary.failed++;
      return;
    }
    
    if (!project.canRun && !project.canTest) {
      console.log(chalk.yellow(`   ⏭️  No runnable commands found - skipping`));
      project.status = 'skipped';
      this.results.summary.skipped++;
      return;
    }
    
    try {
      // Install dependencies first
      await this.installDependencies(project);
      
      // Run tests if available
      if (project.canTest && !options.skipTests) {
        await this.runTests(project);
      }
      
      // Run the project if requested
      if (project.canRun && options.run) {
        await this.runProjectCommands(project, options);
      }
      
      project.status = 'passed';
      this.results.summary.passed++;
      
    } catch (error) {
      console.log(chalk.red(`   ❌ Failed: ${error.message}`));
      project.status = 'failed';
      project.errors.push(error.message);
      this.results.summary.failed++;
    }
  }

  async installDependencies(project) {
    if (project.type === 'nodejs') {
      const spinner = ora(`   📦 Installing Node.js dependencies...`).start();
      try {
        execSync('npm install', { 
          cwd: project.path, 
          stdio: 'pipe',
          timeout: 120000 // 2 minutes
        });
        spinner.succeed(chalk.green(`   ✅ Dependencies installed`));
      } catch (error) {
        spinner.fail(chalk.red(`   ❌ Dependency installation failed`));
        throw new Error(`npm install failed: ${error.message}`);
      }
    }
    
    if (project.type === 'python') {
      const spinner = ora(`   📦 Installing Python dependencies...`).start();
      try {
        if (await fs.pathExists(path.join(project.path, 'requirements.txt'))) {
          execSync('pip install -r requirements.txt', { 
            cwd: project.path, 
            stdio: 'pipe',
            timeout: 120000
          });
        }
        spinner.succeed(chalk.green(`   ✅ Dependencies installed`));
      } catch (error) {
        spinner.fail(chalk.red(`   ❌ Dependency installation failed`));
        throw new Error(`pip install failed: ${error.message}`);
      }
    }
  }

  async runTests(project) {
    const spinner = ora(`   🧪 Running tests...`).start();
    
    try {
      for (const command of project.testCommands) {
        execSync(command, { 
          cwd: project.path, 
          stdio: 'pipe',
          timeout: 60000 // 1 minute per test command
        });
      }
      spinner.succeed(chalk.green(`   ✅ Tests passed`));
    } catch (error) {
      spinner.fail(chalk.red(`   ❌ Tests failed`));
      throw new Error(`Tests failed: ${error.message}`);
    }
  }

  async runProjectCommands(project, options = {}) {
    if (options.background) {
      await this.runInBackground(project);
    } else {
      await this.runInteractive(project);
    }
  }

  async runInBackground(project) {
    const spinner = ora(`   🚀 Starting in background...`).start();
    
    try {
      const command = project.runCommands[0];
      const [cmd, ...args] = command.split(' ');
      
      const child = spawn(cmd, args, {
        cwd: project.path,
        detached: true,
        stdio: 'ignore'
      });
      
      child.unref();
      this.runningProcesses.set(project.name, child.pid);
      
      spinner.succeed(chalk.green(`   ✅ Started in background (PID: ${child.pid})`));
    } catch (error) {
      spinner.fail(chalk.red(`   ❌ Failed to start`));
      throw error;
    }
  }

  async runInteractive(project) {
    console.log(chalk.yellow(`   🚀 Running interactively...`));
    console.log(chalk.gray(`   Command: ${project.runCommands[0]}`));
    console.log(chalk.gray(`   Press Ctrl+C to stop`));
    
    try {
      const command = project.runCommands[0];
      execSync(command, { 
        cwd: project.path, 
        stdio: 'inherit'
      });
    } catch (error) {
      if (error.signal === 'SIGINT') {
        console.log(chalk.yellow(`   ⏹️  Stopped by user`));
      } else {
        throw error;
      }
    }
  }

  displaySummary() {
    console.log('\n' + chalk.blue.bold('📊 Summary:'));
    console.log(chalk.gray('=' .repeat(40)));
    console.log(chalk.green(`✅ Passed: ${this.results.summary.passed}`));
    console.log(chalk.red(`❌ Failed: ${this.results.summary.failed}`));
    console.log(chalk.yellow(`⏭️  Skipped: ${this.results.summary.skipped}`));
    console.log(chalk.cyan(`📦 Total: ${this.results.summary.total}`));
    
    if (this.runningProcesses.size > 0) {
      console.log('\n' + chalk.blue.bold('🔄 Running Processes:'));
      this.runningProcesses.forEach((pid, name) => {
        console.log(chalk.gray(`   ${name}: PID ${pid}`));
      });
    }
    
    console.log('');
  }
}

// CLI Interface
async function main() {
  const args = process.argv.slice(2);
  const options = {
    list: args.includes('--list') || args.includes('-l'),
    run: args.includes('--run') || args.includes('-r'),
    background: args.includes('--background') || args.includes('-b'),
    skipTests: args.includes('--skip-tests'),
    project: null
  };
  
  // Extract project name if provided
  const projectIndex = args.findIndex(arg => arg === '--project' || arg === '-p');
  if (projectIndex !== -1 && args[projectIndex + 1]) {
    options.project = args[projectIndex + 1];
  }
  
  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
🚀 Universal Project Runner & Validator

Usage: node project-runner.js [options]

Options:
  -l, --list           List all discovered projects
  -r, --run            Run projects after validation
  -b, --background     Run projects in background
  -p, --project <name> Run specific project only
  --skip-tests         Skip running tests
  -h, --help           Show this help message

Examples:
  node project-runner.js --list
  node project-runner.js --run
  node project-runner.js --project ecostamp --run
  node project-runner.js --run --background
    `);
    return;
  }
  
  const runner = new ProjectRunner();
  await runner.run(options);
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export default ProjectRunner;

// Stamply Universal Popup Script

document.addEventListener('DOMContentLoaded', async () => {
    await loadStatistics();
    setupEventListeners();
});

async function loadStatistics() {
    try {
        const result = await chrome.storage.local.get(['stamplyStats', 'stamplyConfig']);
        const stats = result.stamplyStats || {
            totalInteractions: 0,
            totalEnergy: 0,
            totalWater: 0,
            platforms: {},
            ecoLevels: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 }
        };
        const config = result.stamplyConfig || { enabled: true };
        
        displayStatistics(stats);
        displayPlatforms(stats.platforms);
        displayEcoLevels(stats.ecoLevels);
        updateToggleButton(config.enabled);
        
    } catch (error) {
        console.error('Error loading statistics:', error);
        document.getElementById('stats').innerHTML = '<div class="loading">Error loading data</div>';
    }
}

function displayStatistics(stats) {
    const statsContainer = document.getElementById('stats');
    
    // Calculate equivalents
    const phoneCharges = Math.round(stats.totalEnergy * 55.6);
    const waterBottles = Math.round(stats.totalWater * 2);
    const avgEcoLevel = calculateAverageEcoLevel(stats.ecoLevels);
    
    statsContainer.innerHTML = `
        <div class="stat-item">
            <span class="stat-label">🤖 Total Interactions</span>
            <span class="stat-value">${stats.totalInteractions.toLocaleString()}</span>
        </div>
        <div class="stat-item">
            <span class="stat-label">⚡ Total Energy</span>
            <span class="stat-value">${stats.totalEnergy.toFixed(3)} Wh</span>
        </div>
        <div class="stat-item">
            <span class="stat-label">💧 Total Water</span>
            <span class="stat-value">${stats.totalWater.toFixed(1)} mL</span>
        </div>
        <div class="stat-item">
            <span class="stat-label">📱 Phone Charges</span>
            <span class="stat-value">≈ ${phoneCharges}</span>
        </div>
        <div class="stat-item">
            <span class="stat-label">🍼 Water Bottles</span>
            <span class="stat-value">≈ ${waterBottles}</span>
        </div>
        <div class="stat-item">
            <span class="stat-label">🌿 Avg Eco-Level</span>
            <span class="stat-value">${avgEcoLevel}/5</span>
        </div>
    `;
}

function displayPlatforms(platforms) {
    const platformsContainer = document.getElementById('platforms');
    
    if (Object.keys(platforms).length === 0) {
        platformsContainer.style.display = 'none';
        return;
    }
    
    platformsContainer.style.display = 'block';
    
    // Sort platforms by usage count
    const sortedPlatforms = Object.entries(platforms)
        .sort(([,a], [,b]) => b.count - a.count);
    
    const platformsHTML = sortedPlatforms.map(([key, platform]) => `
        <div class="platform-item">
            <span class="platform-name">${platform.name}</span>
            <span class="platform-count">${platform.count}</span>
        </div>
    `).join('');
    
    platformsContainer.innerHTML = `
        <h3 style="margin: 0 0 10px 0; font-size: 14px;">Platforms Used</h3>
        ${platformsHTML}
    `;
}

function displayEcoLevels(ecoLevels) {
    const ecoContainer = document.getElementById('ecoLevels');
    const total = Object.values(ecoLevels).reduce((sum, count) => sum + count, 0);
    
    if (total === 0) {
        ecoContainer.style.display = 'none';
        return;
    }
    
    ecoContainer.style.display = 'block';
    
    const ecoHTML = Object.entries(ecoLevels).map(([level, count]) => {
        const percentage = total > 0 ? Math.round((count / total) * 100) : 0;
        const levelNum = parseInt(level);
        const greenLeaves = Math.max(0, 5 - levelNum + 1); // More green = better
        const brownLeaves = Math.max(0, levelNum - 1);     // More brown = worse
        const leaves = '🌿'.repeat(greenLeaves) + '🍂'.repeat(brownLeaves);
        
        return `
            <div class="eco-level">
                <span>${level} ${leaves}</span>
                <span>${count} (${percentage}%)</span>
            </div>
        `;
    }).join('');
    
    ecoContainer.innerHTML = `
        <h3 style="margin: 0 0 10px 0; font-size: 14px;">Eco-Level Distribution</h3>
        ${ecoHTML}
    `;
}

function calculateAverageEcoLevel(ecoLevels) {
    const total = Object.values(ecoLevels).reduce((sum, count) => sum + count, 0);
    if (total === 0) return 0;
    
    const weightedSum = Object.entries(ecoLevels)
        .reduce((sum, [level, count]) => sum + (parseInt(level) * count), 0);
    
    return (weightedSum / total).toFixed(1);
}

function setupEventListeners() {
    // Toggle button
    document.getElementById('toggleBtn').addEventListener('click', async () => {
        const result = await chrome.storage.local.get(['stamplyConfig']);
        const config = result.stamplyConfig || { enabled: true };
        
        config.enabled = !config.enabled;
        await chrome.storage.local.set({ stamplyConfig: config });
        
        updateToggleButton(config.enabled);
        
        // Notify content scripts
        try {
            const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
            if (tabs[0]) {
                chrome.tabs.sendMessage(tabs[0].id, { 
                    action: 'toggleStamply', 
                    enabled: config.enabled 
                });
            }
        } catch (error) {
            console.log('Could not notify content script:', error);
        }
    });
    
    // Reset button
    document.getElementById('resetBtn').addEventListener('click', async () => {
        if (confirm('Are you sure you want to reset all statistics?')) {
            await chrome.storage.local.remove(['stamplyStats']);
            await loadStatistics();
        }
    });
    
    // Export button
    document.getElementById('exportBtn').addEventListener('click', async () => {
        try {
            const result = await chrome.storage.local.get(['stamplyStats']);
            const stats = result.stamplyStats || {};
            
            const dataStr = JSON.stringify(stats, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `stamply-stats-${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            
            URL.revokeObjectURL(url);
        } catch (error) {
            console.error('Error exporting data:', error);
            alert('Error exporting data. Please try again.');
        }
    });
}

function updateToggleButton(enabled) {
    const toggleBtn = document.getElementById('toggleBtn');
    toggleBtn.textContent = enabled ? 'Disable EcoStamp' : 'Enable EcoStamp';
    toggleBtn.style.background = enabled ?
        'rgba(239, 68, 68, 0.3)' :
        'rgba(34, 197, 94, 0.3)';
}

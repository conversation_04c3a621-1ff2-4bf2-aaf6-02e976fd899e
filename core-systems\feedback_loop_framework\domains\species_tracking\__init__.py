"""
Species Tracking Domain Module

This module provides specialized feedback loop components for ecological surveys
and wildlife monitoring, including bird species identification, GPS logging,
and biodiversity assessment.
"""

from .species_detector import SpeciesDetectionInterpreter
from .ecological_matcher import EcologicalPatternMatcher
from .survey_validator import EcologicalSurveyValidator

# Create a simple SpeciesTrackingDomain class
class SpeciesTrackingDomain:
    """Simple Species Tracking Domain implementation"""
    def __init__(self, config=None):
        self.config = config or {}

__all__ = [
    'SpeciesDetectionInterpreter',
    'EcologicalPatternMatcher',
    'EcologicalSurveyValidator',
    'SpeciesTrackingDomain'
]

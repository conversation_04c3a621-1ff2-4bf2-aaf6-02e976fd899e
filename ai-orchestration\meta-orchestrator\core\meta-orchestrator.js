#!/usr/bin/env node

/**
 * Meta-Orchestration System
 * 
 * Central orchestrator that manages role-based assignment, fallback logic, 
 * and deduplication for all integrated AI assistants and orchestrators.
 * 
 * Features:
 * - Role-based AI assistant assignment (Analy<PERSON>, Generator, Completer, Validator, Documenter)
 * - Instant fallback logic with multiple assistants per role
 * - Universal deduplication and context management
 * - Multi-IDE support with real-time sync
 * - Mix-and-match workflow automation
 * - Integration with existing thread-merging and DGM systems
 */

const EventEmitter = require('events');
const path = require('path');
const fs = require('fs').promises;
const chalk = require('chalk');
const { v4: uuidv4 } = require('uuid');

// Import existing orchestration systems
const AIOrchestrator = require('../../orchestrator');
const ThreadMergingOrchestrator = require('../../../thread-merging-orchestrator/src/orchestrator');
const DGMIntegration = require('../../dgm/dgm-integration');

// Import new meta-orchestration components
const RoleManager = require('./role-manager');
const FallbackEngine = require('./fallback-engine');
const ContextManager = require('./context-manager');
const DeduplicationEngine = require('./deduplication-engine');
const WorkflowEngine = require('./workflow-engine');
const AdapterRegistry = require('../adapters/adapter-registry');
const ConfigManager = require('../config/config-manager');
const SecurityManager = require('../security/security-manager');
const MonitoringSystem = require('../monitoring/monitoring-system');

class MetaOrchestrator extends EventEmitter {
  constructor(options = {}) {
    super();
    
    this.id = uuidv4();
    this.startTime = Date.now();
    this.options = options;
    
    // Core components
    this.roleManager = new RoleManager(this);
    this.fallbackEngine = new FallbackEngine(this);
    this.contextManager = new ContextManager(this);
    this.deduplicationEngine = new DeduplicationEngine(this);
    this.workflowEngine = new WorkflowEngine(this);
    this.adapterRegistry = new AdapterRegistry(this);
    this.configManager = new ConfigManager(this);
    this.securityManager = new SecurityManager(this);
    this.monitoringSystem = new MonitoringSystem(this);
    
    // Existing orchestration systems integration
    this.aiOrchestrator = null;
    this.threadMergingOrchestrator = null;
    this.dgmIntegration = null;
    
    // State management
    this.state = {
      status: 'initializing',
      activeWorkflows: new Map(),
      assistantStates: new Map(),
      roleAssignments: new Map(),
      fallbackChains: new Map(),
      contextCache: new Map(),
      deduplicationCache: new Map()
    };
    
    // Performance metrics
    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      fallbackActivations: 0,
      averageResponseTime: 0,
      assistantPerformance: new Map(),
      roleEfficiency: new Map()
    };
    
    this.initialize();
  }
  
  async initialize() {
    try {
      console.log(chalk.blue('🚀 Initializing Meta-Orchestration System...'));
      
      // Load configuration
      await this.configManager.loadConfig();
      
      // Initialize security
      await this.securityManager.initialize();
      
      // Initialize existing orchestration systems
      await this.initializeExistingOrchestrators();
      
      // Initialize adapters for all AI assistants
      await this.adapterRegistry.initializeAdapters();
      
      // Setup role definitions and assignments
      await this.roleManager.initializeRoles();
      
      // Setup fallback chains
      await this.fallbackEngine.initializeFallbackChains();
      
      // Initialize context management
      await this.contextManager.initialize();
      
      // Initialize deduplication engine
      await this.deduplicationEngine.initialize();
      
      // Initialize workflow engine
      await this.workflowEngine.initialize();
      
      // Start monitoring
      await this.monitoringSystem.start();
      
      this.state.status = 'ready';
      this.emit('ready');
      
      console.log(chalk.green('✅ Meta-Orchestration System initialized successfully'));
      
    } catch (error) {
      console.error(chalk.red('❌ Failed to initialize Meta-Orchestration System:'), error);
      this.state.status = 'error';
      this.emit('error', error);
      throw error;
    }
  }
  
  async initializeExistingOrchestrators() {
    try {
      // Initialize AI Orchestrator
      this.aiOrchestrator = new AIOrchestrator();
      
      // Initialize Thread Merging Orchestrator
      this.threadMergingOrchestrator = new ThreadMergingOrchestrator();
      
      // Initialize DGM Integration
      this.dgmIntegration = new DGMIntegration({
        aiOrchestrator: this.aiOrchestrator,
        threadMergingOrchestrator: this.threadMergingOrchestrator,
        integrationMode: 'hybrid'
      });
      
      console.log(chalk.green('✅ Existing orchestrators integrated'));
      
    } catch (error) {
      console.error(chalk.red('❌ Failed to initialize existing orchestrators:'), error);
      throw error;
    }
  }
  
  /**
   * Main orchestration method - routes requests to appropriate assistants
   */
  async orchestrate(request) {
    const requestId = uuidv4();
    const startTime = Date.now();
    
    try {
      this.metrics.totalRequests++;
      
      console.log(chalk.blue(`🎯 Processing request ${requestId}`));
      
      // Validate and sanitize request
      const validatedRequest = await this.securityManager.validateRequest(request);
      
      // Determine workflow type and roles needed
      const workflow = await this.workflowEngine.determineWorkflow(validatedRequest);
      
      // Execute workflow with role-based assignment and fallback
      const result = await this.executeWorkflow(requestId, workflow, validatedRequest);
      
      // Deduplicate and aggregate results
      const finalResult = await this.deduplicationEngine.processResult(result);
      
      // Update metrics and monitoring
      const duration = Date.now() - startTime;
      this.updateMetrics(requestId, true, duration);
      
      this.metrics.successfulRequests++;
      
      console.log(chalk.green(`✅ Request ${requestId} completed in ${duration}ms`));
      
      return finalResult;
      
    } catch (error) {
      const duration = Date.now() - startTime;
      this.updateMetrics(requestId, false, duration);
      this.metrics.failedRequests++;
      
      console.error(chalk.red(`❌ Request ${requestId} failed:`, error));
      
      // Attempt recovery or fallback
      return await this.handleRequestFailure(requestId, request, error);
    }
  }
  
  /**
   * Execute workflow with role-based assignment and fallback logic
   */
  async executeWorkflow(requestId, workflow, request) {
    const workflowId = uuidv4();
    const results = new Map();
    
    this.state.activeWorkflows.set(workflowId, {
      id: workflowId,
      requestId,
      workflow,
      startTime: Date.now(),
      status: 'running',
      currentStep: 0,
      results
    });
    
    try {
      for (let i = 0; i < workflow.steps.length; i++) {
        const step = workflow.steps[i];
        
        console.log(chalk.yellow(`🔄 Executing step ${i + 1}/${workflow.steps.length}: ${step.role}`));
        
        // Update workflow state
        const workflowState = this.state.activeWorkflows.get(workflowId);
        workflowState.currentStep = i;
        workflowState.status = `executing-${step.role}`;
        
        // Get context for this step
        const stepContext = await this.contextManager.getStepContext(request, results, step);
        
        // Execute step with role-based assignment and fallback
        const stepResult = await this.executeStepWithFallback(step, stepContext);
        
        // Store result
        results.set(step.role, stepResult);
        
        // Pass context to next step if needed
        if (step.passContextToNext && i < workflow.steps.length - 1) {
          await this.contextManager.passContextToNextStep(stepResult, workflow.steps[i + 1]);
        }
      }
      
      // Mark workflow as completed
      const workflowState = this.state.activeWorkflows.get(workflowId);
      workflowState.status = 'completed';
      workflowState.endTime = Date.now();
      
      return results;
      
    } catch (error) {
      // Mark workflow as failed
      const workflowState = this.state.activeWorkflows.get(workflowId);
      if (workflowState) {
        workflowState.status = 'failed';
        workflowState.error = error;
        workflowState.endTime = Date.now();
      }
      
      throw error;
    }
  }
  
  /**
   * Execute a workflow step with fallback logic
   */
  async executeStepWithFallback(step, context) {
    const roleAssignment = this.state.roleAssignments.get(step.role);
    
    if (!roleAssignment) {
      throw new Error(`No assignment found for role: ${step.role}`);
    }
    
    const fallbackChain = this.state.fallbackChains.get(step.role) || [roleAssignment.primary];
    
    for (let i = 0; i < fallbackChain.length; i++) {
      const assistantId = fallbackChain[i];
      
      try {
        console.log(chalk.cyan(`🤖 Trying ${assistantId} for role ${step.role} (attempt ${i + 1}/${fallbackChain.length})`));
        
        // Get adapter for this assistant
        const adapter = this.adapterRegistry.getAdapter(assistantId);
        
        if (!adapter) {
          throw new Error(`No adapter found for assistant: ${assistantId}`);
        }
        
        // Check if assistant is available
        const isAvailable = await adapter.checkAvailability();
        
        if (!isAvailable) {
          throw new Error(`Assistant ${assistantId} is not available`);
        }
        
        // Execute with this assistant
        const result = await adapter.execute(step, context);
        
        // Update assistant performance metrics
        this.updateAssistantMetrics(assistantId, true);
        
        console.log(chalk.green(`✅ ${assistantId} successfully completed role ${step.role}`));
        
        return {
          assistantId,
          role: step.role,
          result,
          timestamp: Date.now(),
          fallbackLevel: i
        };
        
      } catch (error) {
        console.warn(chalk.yellow(`⚠️ ${assistantId} failed for role ${step.role}: ${error.message}`));
        
        // Update assistant performance metrics
        this.updateAssistantMetrics(assistantId, false);
        
        // If this was a fallback activation, track it
        if (i > 0) {
          this.metrics.fallbackActivations++;
        }
        
        // If this is the last assistant in the chain, throw the error
        if (i === fallbackChain.length - 1) {
          throw new Error(`All assistants failed for role ${step.role}. Last error: ${error.message}`);
        }
        
        // Otherwise, continue to next assistant in fallback chain
        continue;
      }
    }
  }
  
  updateMetrics(requestId, success, duration) {
    // Update average response time
    const totalTime = this.metrics.averageResponseTime * (this.metrics.totalRequests - 1) + duration;
    this.metrics.averageResponseTime = totalTime / this.metrics.totalRequests;
    
    // Emit metrics update event
    this.emit('metricsUpdate', {
      requestId,
      success,
      duration,
      totalRequests: this.metrics.totalRequests,
      successRate: this.metrics.successfulRequests / this.metrics.totalRequests,
      averageResponseTime: this.metrics.averageResponseTime
    });
  }
  
  updateAssistantMetrics(assistantId, success) {
    if (!this.metrics.assistantPerformance.has(assistantId)) {
      this.metrics.assistantPerformance.set(assistantId, {
        totalRequests: 0,
        successfulRequests: 0,
        failedRequests: 0,
        successRate: 0
      });
    }
    
    const metrics = this.metrics.assistantPerformance.get(assistantId);
    metrics.totalRequests++;
    
    if (success) {
      metrics.successfulRequests++;
    } else {
      metrics.failedRequests++;
    }
    
    metrics.successRate = metrics.successfulRequests / metrics.totalRequests;
  }
  
  async handleRequestFailure(requestId, request, error) {
    // Implement recovery strategies
    console.log(chalk.yellow(`🔄 Attempting recovery for request ${requestId}`));
    
    // Try with DGM if available
    if (this.dgmIntegration && this.dgmIntegration.isAvailable()) {
      try {
        return await this.dgmIntegration.handleFailedRequest(request, error);
      } catch (dgmError) {
        console.warn(chalk.yellow('DGM recovery also failed:', dgmError.message));
      }
    }
    
    // Try with simplified workflow
    try {
      const simplifiedWorkflow = await this.workflowEngine.createSimplifiedWorkflow(request);
      return await this.executeWorkflow(requestId + '-recovery', simplifiedWorkflow, request);
    } catch (recoveryError) {
      console.error(chalk.red('Recovery attempt failed:', recoveryError.message));
    }
    
    // Return error response
    return {
      success: false,
      error: error.message,
      requestId,
      timestamp: Date.now(),
      recoveryAttempted: true
    };
  }
  
  // Getters for system state
  getSystemStatus() {
    return {
      status: this.state.status,
      uptime: Date.now() - this.startTime,
      activeWorkflows: this.state.activeWorkflows.size,
      metrics: this.metrics,
      assistantStates: Object.fromEntries(this.state.assistantStates),
      roleAssignments: Object.fromEntries(this.state.roleAssignments)
    };
  }
  
  async shutdown() {
    console.log(chalk.blue('🛑 Shutting down Meta-Orchestration System...'));
    
    this.state.status = 'shutting-down';
    
    // Stop monitoring
    await this.monitoringSystem.stop();
    
    // Cleanup active workflows
    for (const [workflowId, workflow] of this.state.activeWorkflows) {
      if (workflow.status === 'running') {
        workflow.status = 'cancelled';
        workflow.endTime = Date.now();
      }
    }
    
    // Shutdown adapters
    await this.adapterRegistry.shutdownAll();
    
    this.state.status = 'shutdown';
    this.emit('shutdown');
    
    console.log(chalk.green('✅ Meta-Orchestration System shutdown complete'));
  }
}

module.exports = MetaOrchestrator;

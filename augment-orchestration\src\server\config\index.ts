import { ServerConfig, SocketConfig, DatabaseConfig } from '@/shared/types';

interface Config {
  server: ServerConfig;
  socket: SocketConfig;
  database: DatabaseConfig;
  jwt: {
    secret: string;
    expiresIn: string;
  };
  environment: 'development' | 'production' | 'test';
}

// Helper to split CORS origins safely
const getCorsOrigins = (): string[] => {
  if (!process.env.CORS_ORIGINS || process.env.CORS_ORIGINS.trim() === '') {
    return ['http://localhost:3000'];
  }
  return process.env.CORS_ORIGINS.split(',').map(origin => origin.trim());
};

const config: Config = {
  server: {
    port: Number(process.env.PORT ?? 3001),
    host: process.env.HOST ?? 'localhost',
    cors: {
      origin: getCorsOrigins(),
      credentials: true,
    },
    rateLimit: {
      windowMs: Number(process.env.RATE_LIMIT_WINDOW_MS ?? 900_000), // 15 minutes
      max: Number(process.env.RATE_LIMIT_MAX ?? 100), // 100 req per window
    },
  },
  socket: {
    cors: {
      origin: getCorsOrigins(),
      credentials: true,
    },
    transports: ['websocket', 'polling'],
  },
  database: {
    url: process.env.DATABASE_URL ?? 'postgresql://localhost:5432/augment_orchestration',
    maxConnections: Number(process.env.DB_MAX_CONNECTIONS ?? 10),
    ssl: process.env.DB_SSL === 'true',
  },
  jwt: {
    secret: process.env.JWT_SECRET ?? 'your-super-secret-jwt-key-change-in-production',
    expiresIn: process.env.JWT_EXPIRES_IN ?? '24h',
  },
  environment: (process.env.NODE_ENV as 'development' | 'production' | 'test') ?? 'development',
};

// Validate required environment variables upfront for safety
const requiredEnvVars = ['DATABASE_URL'];
if (config.environment === 'production') {
  requiredEnvVars.push('JWT_SECRET');
}

for (const envVar of requiredEnvVars) {
  if (!process.env[envVar]) {
    throw new Error(`Required environment variable ${envVar} is not set`);
  }
}

export { config };

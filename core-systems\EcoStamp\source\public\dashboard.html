<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stamply Analytics Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .dashboard {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            padding: 30px;
        }
        
        .stat-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 25px;
            text-align: center;
            border-left: 4px solid #3498db;
            transition: transform 0.2s;
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
        }
        
        .stat-card.energy {
            border-left-color: #e74c3c;
        }
        
        .stat-card.water {
            border-left-color: #3498db;
        }
        
        .stat-card.tokens {
            border-left-color: #9b59b6;
        }
        
        .stat-card.accuracy {
            border-left-color: #27ae60;
        }
        
        .stat-value {
            font-size: 2.5em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #7f8c8d;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .stat-unit {
            font-size: 0.7em;
            color: #95a5a6;
        }
        
        .controls {
            padding: 30px;
            background: #ecf0f1;
            border-top: 1px solid #bdc3c7;
        }
        
        .control-group {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-primary {
            background: #3498db;
            color: white;
        }
        
        .btn-primary:hover {
            background: #2980b9;
        }
        
        .btn-success {
            background: #27ae60;
            color: white;
        }
        
        .btn-success:hover {
            background: #229954;
        }
        
        .btn-warning {
            background: #f39c12;
            color: white;
        }
        
        .btn-warning:hover {
            background: #e67e22;
        }
        
        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 6px;
            text-align: center;
        }
        
        .status.success {
            background: #d5f4e6;
            color: #27ae60;
            border: 1px solid #27ae60;
        }
        
        .status.error {
            background: #fadbd8;
            color: #e74c3c;
            border: 1px solid #e74c3c;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .equivalents {
            margin-top: 20px;
            padding: 20px;
            background: #fff3cd;
            border-radius: 6px;
            border-left: 4px solid #ffc107;
        }
        
        .equivalents h3 {
            color: #856404;
            margin-bottom: 10px;
        }
        
        .equivalents p {
            color: #856404;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <div class="header">
            <h1>🌱 Stamply Analytics</h1>
            <p>Real-time AI Environmental Impact Tracking</p>
        </div>
        
        <div class="stats-grid">
            <div class="stat-card energy">
                <div class="stat-value" id="totalEnergy">0.0000</div>
                <div class="stat-label">Total Energy <span class="stat-unit">kWh</span></div>
            </div>
            
            <div class="stat-card water">
                <div class="stat-value" id="totalWater">0.000</div>
                <div class="stat-label">Total Water <span class="stat-unit">Liters</span></div>
            </div>
            
            <div class="stat-card tokens">
                <div class="stat-value" id="totalTokens">0</div>
                <div class="stat-label">Total Tokens</div>
            </div>
            
            <div class="stat-card accuracy">
                <div class="stat-value" id="accuracy">0%</div>
                <div class="stat-label">Token-Based Accuracy</div>
            </div>
        </div>
        
        <div class="equivalents">
            <h3>Environmental Equivalents</h3>
            <p id="phoneCharges">≈ 0 smartphone charges</p>
            <p id="waterBottles">≈ 0 water bottles (500ml)</p>
            <p id="lightBulbHours">≈ 0 hours of LED light bulb</p>
        </div>

        <div class="verification-section" style="margin: 30px 0; padding: 20px; background: #f8f9fa; border-radius: 8px;">
            <h3>🔍 Hash Verification & Registry</h3>
            <p style="margin-bottom: 15px;">Verify SHA-256 hashes and explore the Stamply registry</p>
            <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                <a href="/verify.html" target="_blank" class="btn btn-primary">
                    🔍 Open Verification Tool
                </a>
                <button class="btn btn-secondary" onclick="showRegistryStats()">
                    📊 Registry Statistics
                </button>
            </div>
            <div id="registryStats" style="display: none; margin-top: 15px; padding: 15px; background: white; border-radius: 6px; border: 1px solid #ddd;">
                <div id="registryStatsContent">Loading...</div>
            </div>
        </div>
        
        <div class="controls">
            <div class="control-group">
                <button class="btn btn-primary" onclick="refreshData()">🔄 Refresh Data</button>
                <button class="btn btn-success" onclick="updateUsageData()">📊 Update Usage Data</button>
                <button class="btn btn-warning" onclick="viewRates()">⚙️ View Current Rates</button>
            </div>
            
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>Loading...</p>
            </div>
            
            <div class="status" id="status" style="display: none;"></div>
        </div>
    </div>

    <script>
        // Dashboard functionality
        const API_BASE = 'http://localhost:3000/api';
        
        async function refreshData() {
            showLoading(true);
            try {
                // This would typically fetch from your extension's storage or backend
                // For now, we'll simulate with some sample data
                const sampleData = {
                    energy: 0.0234,
                    water: 0.456,
                    tokens: 12450,
                    count: 45,
                    calculations: { tokenBased: 32, legacy: 13 }
                };
                
                updateDisplay(sampleData);
                showStatus('Data refreshed successfully!', 'success');
            } catch (error) {
                showStatus('Failed to refresh data: ' + error.message, 'error');
            } finally {
                showLoading(false);
            }
        }
        
        async function updateUsageData() {
            showLoading(true);
            try {
                const response = await fetch(`${API_BASE}/update-data`, {
                    method: 'POST'
                });
                const result = await response.json();
                
                if (result.success) {
                    showStatus('Usage data updated successfully!', 'success');
                } else {
                    showStatus('Failed to update usage data', 'error');
                }
            } catch (error) {
                showStatus('Error updating usage data: ' + error.message, 'error');
            } finally {
                showLoading(false);
            }
        }
        
        async function viewRates() {
            showLoading(true);
            try {
                const response = await fetch(`${API_BASE}/impact-rates`);
                const rates = await response.json();
                
                const ratesInfo = Object.entries(rates.rates)
                    .map(([platform, data]) => `${platform}: ${data.energy} kWh/1k tokens`)
                    .join('\\n');
                
                alert('Current Impact Rates:\\n\\n' + ratesInfo);
            } catch (error) {
                showStatus('Error fetching rates: ' + error.message, 'error');
            } finally {
                showLoading(false);
            }
        }
        
        function updateDisplay(data) {
            document.getElementById('totalEnergy').textContent = data.energy.toFixed(4);
            document.getElementById('totalWater').textContent = data.water.toFixed(3);
            document.getElementById('totalTokens').textContent = data.tokens.toLocaleString();
            
            const accuracy = data.count > 0 ? 
                Math.round((data.calculations.tokenBased / data.count) * 100) : 0;
            document.getElementById('accuracy').textContent = accuracy + '%';
            
            // Update equivalents
            const phoneCharges = Math.round(data.energy * 55.6);
            const waterBottles = Math.round(data.water * 2);
            const lightBulbHours = Math.round(data.energy / 0.01);
            
            document.getElementById('phoneCharges').textContent = `≈ ${phoneCharges} smartphone charges`;
            document.getElementById('waterBottles').textContent = `≈ ${waterBottles} water bottles (500ml)`;
            document.getElementById('lightBulbHours').textContent = `≈ ${lightBulbHours} hours of LED light bulb`;
        }
        
        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }
        
        function showStatus(message, type) {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
            statusEl.style.display = 'block';
            
            setTimeout(() => {
                statusEl.style.display = 'none';
            }, 5000);
        }

        async function showRegistryStats() {
            const statsDiv = document.getElementById('registryStats');
            const contentDiv = document.getElementById('registryStatsContent');

            if (statsDiv.style.display === 'none') {
                statsDiv.style.display = 'block';
                contentDiv.innerHTML = 'Loading registry statistics...';

                try {
                    const response = await fetch('/api/registry/stats');
                    const stats = await response.json();

                    const platformsList = Object.entries(stats.platforms)
                        .map(([platform, count]) => `<li>${platform}: ${count} entries</li>`)
                        .join('');

                    const ecoLevelsList = Object.entries(stats.ecoLevels)
                        .map(([level, count]) => {
                            const leaves = '🌿'.repeat(Math.max(0, 6 - parseInt(level))) + '🍂'.repeat(Math.max(0, parseInt(level) - 1));
                            return `<li>Level ${level} ${leaves}: ${count} entries</li>`;
                        })
                        .join('');

                    contentDiv.innerHTML = `
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                            <div>
                                <h4>📊 Overview</h4>
                                <p><strong>Total Hashes:</strong> ${stats.totalHashes.toLocaleString()}</p>
                                <p><strong>Date Range:</strong><br>
                                   ${stats.timeRange.earliest ? new Date(stats.timeRange.earliest).toLocaleDateString() : 'N/A'} -
                                   ${stats.timeRange.latest ? new Date(stats.timeRange.latest).toLocaleDateString() : 'N/A'}</p>
                            </div>
                            <div>
                                <h4>🤖 Platforms</h4>
                                <ul style="margin: 0; padding-left: 20px;">${platformsList}</ul>
                            </div>
                            <div>
                                <h4>🌿 Eco-Levels</h4>
                                <ul style="margin: 0; padding-left: 20px;">${ecoLevelsList}</ul>
                            </div>
                        </div>
                    `;
                } catch (error) {
                    contentDiv.innerHTML = `<p style="color: #e74c3c;">Error loading registry stats: ${error.message}</p>`;
                }
            } else {
                statsDiv.style.display = 'none';
            }
        }

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', () => {
            refreshData();
        });
    </script>
</body>
</html>

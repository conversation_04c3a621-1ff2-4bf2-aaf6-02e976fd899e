"""
Comprehensive Test Suite for Universal Dual-Purpose Feedback Loop Framework

Tests all major components and integration scenarios to ensure the framework
works correctly across both Drone AI and TimeStamp AI domains.
"""

import unittest
import tempfile
import shutil
import json
from datetime import datetime, timedelta
from pathlib import Path

# Import framework components
from feedback_loop_framework.core.feedback_engine import Feedback<PERSON>ngine
from feedback_loop_framework.core.feedback_types import FeedbackType, FeedbackEntry
from feedback_loop_framework.domains.drone_ai import DroneAIDomain
from feedback_loop_framework.domains.timestamp_ai import TimeStampAIDomain
from feedback_loop_framework.components.confidence.adaptive_confidence_model import AdaptiveConfidenceModel
from feedback_loop_framework.components.trust.trust_score_calculator import TrustScoreCalculator
from feedback_loop_framework.components.memory.file_memory_store import FileMemoryStore


class TestFeedbackFramework(unittest.TestCase):
    """Comprehensive test suite for the feedback framework."""
    
    def setUp(self):
        """Set up test environment."""
        # Create temporary directory for test data
        self.test_dir = tempfile.mkdtemp()
        
        # Configure components
        confidence_config = {
            'correct_bonus': 0.05,
            'partially_correct_penalty': 0.02,
            'incorrect_penalty': 0.10,
            'learning_rate': 0.1
        }
        
        trust_config = {
            'correct_weight': 1.0,
            'partially_correct_weight': 0.7,
            'incorrect_weight': 0.0,
            'learning_rate': 0.1,
            'min_entries_for_trust': 3
        }
        
        memory_config = {
            'base_path': self.test_dir,
            'compression_enabled': False,  # Easier for testing
            'retention_days': 1,
            'cache_size': 100
        }
        
        # Create components
        self.confidence_model = AdaptiveConfidenceModel(confidence_config)
        self.trust_calculator = TrustScoreCalculator(trust_config)
        self.memory_store = FileMemoryStore(memory_config)
        
        # Create feedback engine
        self.engine = FeedbackEngine(
            confidence_model=self.confidence_model,
            trust_calculator=self.trust_calculator,
            memory_store=self.memory_store
        )
        
        # Register domains
        self.drone_domain = DroneAIDomain()
        self.timestamp_domain = TimeStampAIDomain()
        
        self.engine.register_domain('drone_ai', self.drone_domain)
        self.engine.register_domain('timestamp_ai', self.timestamp_domain)
    
    def tearDown(self):
        """Clean up test environment."""
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def test_engine_initialization(self):
        """Test feedback engine initialization."""
        self.assertIsNotNone(self.engine)
        self.assertIsNotNone(self.engine.confidence_model)
        self.assertIsNotNone(self.engine.trust_calculator)
        self.assertIsNotNone(self.engine.memory_store)
        
        # Check domain registration
        domain_info = self.engine.get_domain_info()
        self.assertIn('drone_ai', domain_info)
        self.assertIn('timestamp_ai', domain_info)
    
    def test_drone_ai_correct_feedback(self):
        """Test correct feedback for drone AI."""
        gps_data = {
            'latitude': 37.7749,
            'longitude': -122.4194,
            'altitude': 100.5,
            'accuracy': 2.1
        }
        
        context = {
            'sensor_type': 'gps',
            'mission_type': 'waypoint_navigation'
        }
        
        result = self.engine.process_output('drone_ai', gps_data, context, agent_id='test_drone')
        
        self.assertEqual(result.feedback_type, FeedbackType.CORRECT)
        self.assertGreater(result.confidence_score, 0.7)
        self.assertTrue(result.validation_passed)
        self.assertEqual(result.domain, 'drone_ai')
    
    def test_drone_ai_partial_feedback(self):
        """Test partially correct feedback for drone AI."""
        scan_data = {
            'area_scanned': 0.75,  # 75% completion
            'image_count': 150,
            'quality_score': 0.85
        }
        
        context = {
            'sensor_type': 'camera',
            'mission_type': 'area_scanning',
            'area_coverage': 0.75,
            'target_coverage': 0.95
        }
        
        result = self.engine.process_output('drone_ai', scan_data, context, agent_id='test_drone')
        
        self.assertEqual(result.feedback_type, FeedbackType.PARTIALLY_CORRECT)
        self.assertGreater(result.confidence_score, 0.5)
        self.assertTrue(result.validation_passed)
    
    def test_drone_ai_incorrect_feedback(self):
        """Test incorrect feedback for drone AI."""
        faulty_data = {
            'latitude': None,
            'longitude': None,
            'altitude': -999,
            'error': 'GPS_SIGNAL_LOST'
        }
        
        context = {
            'sensor_type': 'gps',
            'mission_type': 'general'
        }
        
        result = self.engine.process_output('drone_ai', faulty_data, context, agent_id='test_drone')
        
        self.assertEqual(result.feedback_type, FeedbackType.INCORRECT)
        self.assertLess(result.confidence_score, 0.5)
        self.assertFalse(result.validation_passed)
        self.assertGreater(len(result.validation_issues), 0)
    
    def test_timestamp_ai_correct_feedback(self):
        """Test correct feedback for timestamp AI."""
        timestamp_response = {
            'timestamp': datetime.utcnow().isoformat(),
            'hash': 'a1b2c3d4e5f6789012345678901234567890123456789012345678901234567890',
            'signature': 'def456789012345678901234567890123456789012345678901234567890123456789012345678901234567890',
            'verification_status': True
        }
        
        context = {
            'output_type': 'timestamp_response',
            'ai_model': 'claude'
        }
        
        result = self.engine.process_output('timestamp_ai', timestamp_response, context, agent_id='test_timestamp')
        
        self.assertEqual(result.feedback_type, FeedbackType.CORRECT)
        self.assertGreater(result.confidence_score, 0.7)
        self.assertTrue(result.validation_passed)
    
    def test_timestamp_ai_partial_feedback(self):
        """Test partially correct feedback for timestamp AI."""
        impact_data = {
            'water_usage': 450.0,  # Within limits
            'electricity_usage': 75.0,  # Within limits
            'carbon_footprint': 20.0,  # Within limits
            'token_count': 1500
        }
        
        context = {
            'output_type': 'impact_calculation',
            'ai_model': 'chatgpt'
        }
        
        result = self.engine.process_output('timestamp_ai', impact_data, context, agent_id='test_timestamp')
        
        # Should be correct since all values are within limits
        self.assertIn(result.feedback_type, [FeedbackType.CORRECT, FeedbackType.PARTIALLY_CORRECT])
        self.assertGreater(result.confidence_score, 0.5)
    
    def test_timestamp_ai_incorrect_feedback(self):
        """Test incorrect feedback for timestamp AI."""
        drifted_response = {
            'timestamp': (datetime.utcnow() - timedelta(minutes=10)).isoformat(),  # 10 minutes ago
            'hash': 'invalid_hash',
            'verification_status': False
        }
        
        context = {
            'output_type': 'timestamp_response',
            'ai_model': 'gemini'
        }
        
        result = self.engine.process_output('timestamp_ai', drifted_response, context, agent_id='test_timestamp')
        
        # Should be incorrect due to verification failure and potentially drift
        self.assertIn(result.feedback_type, [FeedbackType.INCORRECT, FeedbackType.PARTIALLY_CORRECT])
        self.assertGreater(len(result.validation_issues), 0)
    
    def test_batch_processing(self):
        """Test batch processing functionality."""
        batch_outputs = [
            {
                'output': {'latitude': 37.7749 + i*0.001, 'longitude': -122.4194 + i*0.001, 'altitude': 100 + i*10},
                'context': {'sensor_type': 'gps', 'mission_type': 'waypoint_navigation'},
                'agent_id': f'batch_drone_{i:03d}'
            }
            for i in range(5)
        ]
        
        results = self.engine.batch_process('drone_ai', batch_outputs)
        
        self.assertEqual(len(results), 5)
        for result in results:
            self.assertIsInstance(result, FeedbackEntry)
            self.assertEqual(result.domain, 'drone_ai')
    
    def test_confidence_model_learning(self):
        """Test confidence model learning from feedback."""
        # Process several entries to build history
        for i in range(10):
            gps_data = {
                'latitude': 37.7749 + i*0.001,
                'longitude': -122.4194 + i*0.001,
                'altitude': 100 + i*10,
                'accuracy': 2.0 + i*0.1
            }
            
            context = {'sensor_type': 'gps', 'mission_type': 'waypoint_navigation'}
            result = self.engine.process_output('drone_ai', gps_data, context, agent_id='learning_test')
        
        # Get model stats
        stats = self.confidence_model.get_model_stats()
        self.assertGreater(stats['total_calculations'], 0)
        self.assertGreater(stats['average_confidence'], 0)
    
    def test_trust_score_evolution(self):
        """Test trust score evolution over time."""
        agent_id = 'trust_test_agent'
        
        # Process several correct entries
        for i in range(5):
            gps_data = {
                'latitude': 37.7749,
                'longitude': -122.4194,
                'altitude': 100,
                'accuracy': 2.0
            }
            
            context = {'sensor_type': 'gps', 'mission_type': 'waypoint_navigation'}
            result = self.engine.process_output('drone_ai', gps_data, context, agent_id=agent_id)
        
        # Get trust summary
        trust_summary = self.trust_calculator.get_agent_trust_summary(agent_id)
        self.assertGreater(trust_summary['overall_trust'], 0.5)
        self.assertEqual(trust_summary['domain_count'], 1)
    
    def test_memory_store_persistence(self):
        """Test memory store data persistence."""
        # Process an entry
        gps_data = {'latitude': 37.7749, 'longitude': -122.4194, 'altitude': 100}
        context = {'sensor_type': 'gps'}
        
        result = self.engine.process_output('drone_ai', gps_data, context, agent_id='persistence_test')
        
        # Retrieve entries
        entries = self.memory_store.retrieve_entries(domain='drone_ai', limit=10)
        self.assertGreater(len(entries), 0)
        
        # Check entry data
        stored_entry = entries[0]
        self.assertEqual(stored_entry.domain, 'drone_ai')
        self.assertEqual(stored_entry.agent_id, 'persistence_test')
    
    def test_analytics_generation(self):
        """Test analytics data generation."""
        # Process several entries across domains
        for i in range(3):
            # Drone AI entries
            gps_data = {'latitude': 37.7749, 'longitude': -122.4194, 'altitude': 100}
            self.engine.process_output('drone_ai', gps_data, {'sensor_type': 'gps'}, agent_id=f'analytics_drone_{i}')
            
            # Timestamp AI entries
            timestamp_data = {'timestamp': datetime.utcnow().isoformat(), 'hash': 'a'*64}
            self.engine.process_output('timestamp_ai', timestamp_data, {'output_type': 'timestamp_response'}, agent_id=f'analytics_timestamp_{i}')
        
        # Generate analytics
        analytics = self.memory_store.get_analytics_data()
        
        self.assertGreater(analytics['total_entries'], 0)
        self.assertIn('feedback_distribution', analytics)
        self.assertIn('domain_distribution', analytics)
        self.assertIn('drone_ai', analytics['domain_distribution'])
        self.assertIn('timestamp_ai', analytics['domain_distribution'])
    
    def test_error_handling(self):
        """Test error handling and recovery."""
        # Test with invalid domain
        with self.assertRaises(ValueError):
            self.engine.process_output('invalid_domain', {}, {})
        
        # Test with malformed data
        result = self.engine.process_output('drone_ai', None, {}, agent_id='error_test')
        self.assertEqual(result.feedback_type, FeedbackType.MISCELLANEOUS)
        self.assertFalse(result.validation_passed)
    
    def test_health_check(self):
        """Test system health check."""
        health = self.engine.health_check()
        
        self.assertEqual(health['engine'], 'healthy')
        self.assertTrue(health['components']['confidence_model'])
        self.assertTrue(health['components']['trust_calculator'])
        self.assertTrue(health['components']['memory_store'])
        self.assertIn('drone_ai', health['domains'])
        self.assertIn('timestamp_ai', health['domains'])
    
    def test_statistics_tracking(self):
        """Test statistics tracking."""
        initial_stats = self.engine.get_statistics()
        initial_total = initial_stats['total_processed']
        
        # Process some entries
        for i in range(3):
            gps_data = {'latitude': 37.7749, 'longitude': -122.4194, 'altitude': 100}
            self.engine.process_output('drone_ai', gps_data, {'sensor_type': 'gps'}, agent_id=f'stats_test_{i}')
        
        # Check updated stats
        updated_stats = self.engine.get_statistics()
        self.assertEqual(updated_stats['total_processed'], initial_total + 3)
        self.assertIn('drone_ai', updated_stats['by_domain'])
    
    def test_domain_specific_features(self):
        """Test domain-specific features."""
        # Test drone AI specific features
        drone_info = self.drone_domain.get_domain_info()
        self.assertIn('supported_sensor_types', drone_info)
        self.assertIn('supported_mission_types', drone_info)
        self.assertTrue(drone_info['capabilities']['partial_correctness'])
        
        # Test timestamp AI specific features
        timestamp_info = self.timestamp_domain.get_domain_info()
        self.assertIn('supported_output_types', timestamp_info)
        self.assertIn('supported_ai_models', timestamp_info)
        self.assertTrue(timestamp_info['capabilities']['environmental_tracking'])


class TestIntegrationScenarios(unittest.TestCase):
    """Test integration scenarios and real-world use cases."""
    
    def setUp(self):
        """Set up integration test environment."""
        self.test_dir = tempfile.mkdtemp()
        
        # Create minimal engine for integration tests
        memory_store = FileMemoryStore({'base_path': self.test_dir})
        self.engine = FeedbackEngine(memory_store=memory_store)
        
        # Register domains
        self.engine.register_domain('drone_ai', DroneAIDomain())
        self.engine.register_domain('timestamp_ai', TimeStampAIDomain())
    
    def tearDown(self):
        """Clean up integration test environment."""
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def test_drone_mission_scenario(self):
        """Test complete drone mission scenario."""
        mission_data = [
            # Takeoff
            {'latitude': 37.7749, 'longitude': -122.4194, 'altitude': 0, 'phase': 'takeoff'},
            # Navigation
            {'latitude': 37.7750, 'longitude': -122.4195, 'altitude': 100, 'phase': 'navigation'},
            {'latitude': 37.7751, 'longitude': -122.4196, 'altitude': 100, 'phase': 'navigation'},
            # Scanning
            {'area_scanned': 0.5, 'image_count': 50, 'phase': 'scanning'},
            {'area_scanned': 0.8, 'image_count': 80, 'phase': 'scanning'},
            # Landing
            {'latitude': 37.7749, 'longitude': -122.4194, 'altitude': 0, 'phase': 'landing'}
        ]
        
        results = []
        for i, data in enumerate(mission_data):
            context = {
                'sensor_type': 'gps' if 'latitude' in data else 'camera',
                'mission_type': 'area_scanning',
                'mission_phase': data.get('phase', 'unknown')
            }
            
            result = self.engine.process_output('drone_ai', data, context, agent_id='mission_drone')
            results.append(result)
        
        # Verify mission progression
        self.assertEqual(len(results), 6)
        
        # Most results should be correct or partially correct
        successful_results = [r for r in results if r.feedback_type in [FeedbackType.CORRECT, FeedbackType.PARTIALLY_CORRECT]]
        self.assertGreaterEqual(len(successful_results), 4)
    
    def test_timestamp_verification_workflow(self):
        """Test timestamp verification workflow."""
        # Simulate timestamp generation and verification workflow
        workflow_steps = [
            # Initial timestamp request
            {
                'content': 'Document content to timestamp',
                'request_type': 'timestamp_generation'
            },
            # Timestamp response
            {
                'timestamp': datetime.utcnow().isoformat(),
                'hash': 'a1b2c3d4e5f6789012345678901234567890123456789012345678901234567890',
                'signature': 'def456789012345678901234567890123456789012345678901234567890123456789012345678901234567890'
            },
            # Verification check
            {
                'hash': 'a1b2c3d4e5f6789012345678901234567890123456789012345678901234567890',
                'verification_status': True,
                'verification_time': datetime.utcnow().isoformat()
            },
            # Environmental impact calculation
            {
                'water_usage': 300.0,
                'electricity_usage': 50.0,
                'carbon_footprint': 15.0,
                'token_count': 1200
            }
        ]
        
        contexts = [
            {'output_type': 'llm_response', 'ai_model': 'claude'},
            {'output_type': 'timestamp_response', 'ai_model': 'claude'},
            {'output_type': 'hash_verification', 'ai_model': 'claude'},
            {'output_type': 'impact_calculation', 'ai_model': 'claude'}
        ]
        
        results = []
        for step_data, context in zip(workflow_steps, contexts):
            result = self.engine.process_output('timestamp_ai', step_data, context, agent_id='workflow_agent')
            results.append(result)
        
        # Verify workflow results
        self.assertEqual(len(results), 4)
        
        # Check that most steps are successful
        successful_results = [r for r in results if r.feedback_type in [FeedbackType.CORRECT, FeedbackType.PARTIALLY_CORRECT]]
        self.assertGreaterEqual(len(successful_results), 3)


if __name__ == '__main__':
    # Run all tests
    unittest.main(verbosity=2)

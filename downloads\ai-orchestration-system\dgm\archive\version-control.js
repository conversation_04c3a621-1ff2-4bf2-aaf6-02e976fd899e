/**
 * Version Control System for DGM Archive
 * 
 * Manages Git-based version control for agent code and evolution history:
 * - Initialize Git repository for archive
 * - Commit agent versions and generations
 * - Track code changes and evolution
 * - Enable rollback and branching
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs').promises;
const path = require('path');
const chalk = require('chalk');

class VersionControl {
  constructor(archivePath) {
    this.archivePath = archivePath;
    this.gitPath = path.join(archivePath, '.git');
    this.isInitialized = false;
  }

  /**
   * Initialize Git repository for the archive
   */
  async initialize() {
    try {
      // Check if Git repository already exists
      try {
        await fs.access(this.gitPath);
        this.isInitialized = true;
        console.log(chalk.blue('📁 Using existing Git repository for archive'));
        return;
      } catch (error) {
        // Repository doesn't exist, create it
      }

      // Initialize new Git repository
      this.execGit('init');
      
      // Configure Git for DGM
      this.execGit('config user.name "DGM System"');
      this.execGit('config user.email "dgm@localhost"');
      
      // Create initial .gitignore
      await this.createGitIgnore();
      
      // Create initial commit
      this.execGit('add .');
      this.execGit('commit -m "Initial DGM archive setup"');
      
      this.isInitialized = true;
      console.log(chalk.green('✅ Git repository initialized for DGM archive'));
      
    } catch (error) {
      throw new Error(`Failed to initialize version control: ${error.message}`);
    }
  }

  /**
   * Commit an agent to version control
   */
  async commitAgent(agent, message = null) {
    if (!this.isInitialized) {
      throw new Error('Version control not initialized');
    }

    try {
      const commitMessage = message || `Add agent ${agent.id} (gen ${agent.generation}, fitness: ${agent.fitness.toFixed(4)})`;
      
      // Add agent files to Git
      const agentPath = path.join('agents', agent.id);
      this.execGit(`add ${agentPath}`);
      
      // Create commit
      this.execGit(`commit -m "${commitMessage}"`);
      
      // Tag the commit with agent ID
      const tagName = `agent-${agent.id}`;
      this.execGit(`tag ${tagName}`);
      
      console.log(chalk.blue(`📝 Agent ${agent.id} committed to version control`));
      
      return this.getCurrentCommitHash();
      
    } catch (error) {
      throw new Error(`Failed to commit agent ${agent.id}: ${error.message}`);
    }
  }

  /**
   * Commit a generation to version control
   */
  async commitGeneration(generation, message = null) {
    if (!this.isInitialized) {
      throw new Error('Version control not initialized');
    }

    try {
      const commitMessage = message || `Generation ${generation.number} (${generation.survivors} agents, best fitness: ${generation.bestFitness.toFixed(4)})`;
      
      // Add generation files to Git
      const generationPath = path.join('generations', `gen-${generation.number}`);
      this.execGit(`add ${generationPath}`);
      
      // Update archive index
      this.execGit('add index.json');
      
      // Create commit
      this.execGit(`commit -m "${commitMessage}"`);
      
      // Tag the commit with generation number
      const tagName = `generation-${generation.number}`;
      this.execGit(`tag ${tagName}`);
      
      console.log(chalk.blue(`📚 Generation ${generation.number} committed to version control`));
      
      return this.getCurrentCommitHash();
      
    } catch (error) {
      throw new Error(`Failed to commit generation ${generation.number}: ${error.message}`);
    }
  }

  /**
   * Create a branch for experimental evolution
   */
  async createBranch(branchName, description = '') {
    try {
      this.execGit(`checkout -b ${branchName}`);
      
      // Create branch description file
      const branchInfo = {
        name: branchName,
        description,
        created: new Date(),
        parentCommit: this.getCurrentCommitHash()
      };
      
      await fs.writeFile(
        path.join(this.archivePath, '.branch-info.json'),
        JSON.stringify(branchInfo, null, 2)
      );
      
      this.execGit('add .branch-info.json');
      this.execGit(`commit -m "Create experimental branch: ${branchName}"`);
      
      console.log(chalk.green(`🌿 Created branch: ${branchName}`));
      return branchName;
      
    } catch (error) {
      throw new Error(`Failed to create branch ${branchName}: ${error.message}`);
    }
  }

  /**
   * Switch to a different branch
   */
  async switchBranch(branchName) {
    try {
      this.execGit(`checkout ${branchName}`);
      console.log(chalk.blue(`🔄 Switched to branch: ${branchName}`));
      
    } catch (error) {
      throw new Error(`Failed to switch to branch ${branchName}: ${error.message}`);
    }
  }

  /**
   * Merge a branch back to main
   */
  async mergeBranch(branchName, deleteAfterMerge = false) {
    try {
      // Switch to main branch
      this.execGit('checkout main');
      
      // Merge the branch
      this.execGit(`merge ${branchName}`);
      
      // Optionally delete the branch
      if (deleteAfterMerge) {
        this.execGit(`branch -d ${branchName}`);
      }
      
      console.log(chalk.green(`🔀 Merged branch ${branchName} to main`));
      
    } catch (error) {
      throw new Error(`Failed to merge branch ${branchName}: ${error.message}`);
    }
  }

  /**
   * Rollback to a specific commit or tag
   */
  async rollback(target, createBackup = true) {
    try {
      if (createBackup) {
        // Create backup branch before rollback
        const backupBranch = `backup-${Date.now()}`;
        this.execGit(`checkout -b ${backupBranch}`);
        this.execGit('checkout main');
      }
      
      // Rollback to target
      this.execGit(`reset --hard ${target}`);
      
      console.log(chalk.yellow(`⏪ Rolled back to: ${target}`));
      
    } catch (error) {
      throw new Error(`Failed to rollback to ${target}: ${error.message}`);
    }
  }

  /**
   * Get commit history
   */
  getCommitHistory(limit = 20) {
    try {
      const output = this.execGit(`log --oneline -${limit}`);
      return output.split('\n').filter(line => line.trim());
      
    } catch (error) {
      throw new Error(`Failed to get commit history: ${error.message}`);
    }
  }

  /**
   * Get list of branches
   */
  getBranches() {
    try {
      const output = this.execGit('branch');
      return output.split('\n')
        .map(line => line.trim().replace(/^\*\s*/, ''))
        .filter(line => line);
        
    } catch (error) {
      throw new Error(`Failed to get branches: ${error.message}`);
    }
  }

  /**
   * Get list of tags
   */
  getTags() {
    try {
      const output = this.execGit('tag');
      return output.split('\n').filter(line => line.trim());
      
    } catch (error) {
      throw new Error(`Failed to get tags: ${error.message}`);
    }
  }

  /**
   * Get current commit hash
   */
  getCurrentCommitHash() {
    try {
      return this.execGit('rev-parse HEAD').trim();
    } catch (error) {
      return null;
    }
  }

  /**
   * Get current branch name
   */
  getCurrentBranch() {
    try {
      return this.execGit('rev-parse --abbrev-ref HEAD').trim();
    } catch (error) {
      return 'unknown';
    }
  }

  /**
   * Get repository status
   */
  getStatus() {
    try {
      const status = this.execGit('status --porcelain');
      const branch = this.getCurrentBranch();
      const commit = this.getCurrentCommitHash();
      
      return {
        branch,
        commit: commit ? commit.substring(0, 8) : 'unknown',
        hasChanges: status.trim().length > 0,
        changes: status.split('\n').filter(line => line.trim())
      };
      
    } catch (error) {
      return {
        branch: 'unknown',
        commit: 'unknown',
        hasChanges: false,
        changes: []
      };
    }
  }

  /**
   * Get diff between two commits
   */
  getDiff(from, to = 'HEAD') {
    try {
      return this.execGit(`diff ${from}..${to}`);
    } catch (error) {
      throw new Error(`Failed to get diff between ${from} and ${to}: ${error.message}`);
    }
  }

  /**
   * Create .gitignore file
   */
  async createGitIgnore() {
    const gitignoreContent = `
# DGM temporary files
*.tmp
*.log
.DS_Store
Thumbs.db

# Node modules (if any)
node_modules/

# Sandbox directories
dgm-sandbox/

# Temporary execution files
*.pid
*.lock

# Performance data
*.perf
*.prof

# Backup files
*.bak
*.backup

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db
`.trim();

    await fs.writeFile(
      path.join(this.archivePath, '.gitignore'),
      gitignoreContent
    );
  }

  /**
   * Execute Git command
   */
  execGit(command) {
    try {
      return execSync(`git ${command}`, {
        cwd: this.archivePath,
        encoding: 'utf8',
        stdio: ['pipe', 'pipe', 'pipe']
      });
    } catch (error) {
      throw new Error(`Git command failed: ${command}\n${error.message}`);
    }
  }

  /**
   * Check if Git is available
   */
  static isGitAvailable() {
    try {
      execSync('git --version', { stdio: 'ignore' });
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get repository statistics
   */
  getRepositoryStats() {
    try {
      const commitCount = this.execGit('rev-list --count HEAD').trim();
      const branches = this.getBranches();
      const tags = this.getTags();
      const status = this.getStatus();
      
      return {
        commits: parseInt(commitCount),
        branches: branches.length,
        tags: tags.length,
        currentBranch: status.branch,
        hasUncommittedChanges: status.hasChanges
      };
      
    } catch (error) {
      return {
        commits: 0,
        branches: 0,
        tags: 0,
        currentBranch: 'unknown',
        hasUncommittedChanges: false
      };
    }
  }
}

module.exports = VersionControl;

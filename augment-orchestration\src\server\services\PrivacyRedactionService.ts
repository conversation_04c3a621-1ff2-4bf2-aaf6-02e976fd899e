/**
 * Privacy/Redaction Service
 * 
 * Core Gap 3: Intelligent redaction system to remove sensitive data from logs
 * before external sharing or cross-agent analysis while maintaining audit integrity.
 */

import { PrismaClient } from '@prisma/client';
import { EventEmitter } from 'events';
import { logger } from '../utils/logger';
import {
  RedactionRequest,
  RedactionResult,
  RedactionRule,
  RedactionMatch,
  RedactionLevel,
  SensitiveDataType,
  EntityContext,
  UnredactionRequest,
  RedactionStats,
  RedactionPolicy,
  SmartRedactionConfig,
  BatchRedactionRequest,
  BatchRedactionResponse,
  RedactionAudit,
  RedactionError,
  PatternMatchError,
  UnredactionError,
  PolicyViolationError,
  BUILT_IN_PATTERNS,
  RedactionUtils,
  ContextAnalysis,
  REDACTION_CONSTANTS
} from '../../shared/types/PrivacyRedaction';

export class PrivacyRedactionService extends EventEmitter {
  private prisma: PrismaClient;
  private config: SmartRedactionConfig;
  private ruleCache: Map<string, RedactionRule> = new Map();
  private policyCache: Map<string, RedactionPolicy> = new Map();
  private reversibilityStore: Map<string, string> = new Map();

  constructor(prisma: PrismaClient, config?: Partial<SmartRedactionConfig>) {
    super();
    this.prisma = prisma;
    this.config = {
      enableContextAnalysis: true,
      enableSemanticAnalysis: true,
      enableMLDetection: false,
      confidenceThreshold: 0.7,
      preserveCodeStructure: true,
      preserveLogFormat: true,
      enableFalsePositiveReduction: true,
      customPatterns: [],
      ...config
    };

    this.initializeBuiltInRules();
  }

  /**
   * Main redaction method
   */
  async redactText(request: RedactionRequest): Promise<RedactionResult> {
    const startTime = Date.now();

    try {
      // Validate input
      this.validateRedactionRequest(request);

      // Get applicable rules
      const rules = await this.getApplicableRules(request.entityType, request.redactionLevel, request.customRules);

      // Perform context analysis if enabled
      const contextAnalysis = this.config.enableContextAnalysis 
        ? this.analyzeContext(request.text, request.entityType, request.contextHints)
        : undefined;

      // Apply redaction rules
      const matches = await this.findSensitiveData(request.text, rules, contextAnalysis);

      // Filter matches based on confidence and false positive reduction
      const filteredMatches = this.filterMatches(matches, contextAnalysis);

      // Apply redactions
      const redactedText = this.applyRedactions(request.text, filteredMatches, request.preserveStructure);

      // Generate integrity hash and reversibility key
      const integrityHash = RedactionUtils.calculateIntegrityHash(request.text);
      const reversibilityKey = request.allowReversible 
        ? RedactionUtils.generateReversibilityKey()
        : undefined;

      // Store reversibility data if needed
      if (reversibilityKey) {
        this.reversibilityStore.set(reversibilityKey, request.text);
      }

      const result: RedactionResult = {
        originalText: request.text,
        redactedText,
        redactionMap: filteredMatches,
        redactionLevel: request.redactionLevel,
        integrityHash,
        reversibilityKey,
        metadata: {
          rulesApplied: rules.map(r => r.id),
          sensitiveDataFound: [...new Set(filteredMatches.map(m => m.dataType))],
          redactionCount: filteredMatches.length,
          processingTime: Date.now() - startTime,
          contextAnalysis
        }
      };

      // Audit the redaction
      await this.auditRedaction(result, 'REDACT', 'system');

      // Emit event
      this.emit('redactionApplied', {
        type: 'REDACTION_APPLIED',
        timestamp: new Date(),
        entityType: request.entityType,
        dataType: filteredMatches[0]?.dataType || SensitiveDataType.CUSTOM,
        riskLevel: contextAnalysis?.riskLevel || 'LOW',
        metadata: { redactionCount: filteredMatches.length }
      });

      logger.info('Text redaction completed', {
        entityType: request.entityType,
        redactionLevel: request.redactionLevel,
        redactionCount: filteredMatches.length,
        processingTime: Date.now() - startTime
      });

      return result;

    } catch (error) {
      logger.error('Redaction failed', { error: error.message, request });
      throw new RedactionError('Failed to redact text', 'REDACTION_FAILED', { originalError: error });
    }
  }

  /**
   * Unredact text with proper authorization
   */
  async unredactText(request: UnredactionRequest): Promise<string> {
    try {
      // Verify integrity
      const storedOriginal = this.reversibilityStore.get(request.reversibilityKey);
      if (!storedOriginal) {
        throw new UnredactionError('Reversibility key not found or expired', 'KEY_NOT_FOUND');
      }

      const calculatedHash = RedactionUtils.calculateIntegrityHash(storedOriginal);
      if (calculatedHash !== request.integrityHash) {
        throw new UnredactionError('Integrity verification failed', 'INTEGRITY_MISMATCH');
      }

      // Audit the unredaction
      await this.auditRedaction({
        originalText: storedOriginal,
        redactedText: request.redactedText,
        redactionMap: [],
        redactionLevel: RedactionLevel.NONE,
        integrityHash: request.integrityHash,
        reversibilityKey: request.reversibilityKey,
        metadata: {
          rulesApplied: [],
          sensitiveDataFound: [],
          redactionCount: 0,
          processingTime: 0
        }
      }, 'UNREDACT', request.authorizedBy, request.reason);

      logger.warn('Text unredacted', {
        authorizedBy: request.authorizedBy,
        reason: request.reason,
        reversibilityKey: request.reversibilityKey
      });

      return storedOriginal;

    } catch (error) {
      logger.error('Unredaction failed', { error: error.message, request });
      throw error instanceof UnredactionError ? error : new UnredactionError('Failed to unredact text', 'UNREDACTION_FAILED');
    }
  }

  /**
   * Batch redaction processing
   */
  async processBatchRedaction(request: BatchRedactionRequest): Promise<BatchRedactionResponse> {
    const startTime = Date.now();
    const results: RedactionResult[] = [];
    const errors: any[] = [];

    logger.info('Starting batch redaction', {
      batchId: request.batchId,
      itemCount: request.items.length,
      priority: request.priority
    });

    for (let i = 0; i < request.items.length; i++) {
      try {
        const result = await this.redactText(request.items[i]);
        results.push(result);
      } catch (error) {
        errors.push({
          itemIndex: i,
          error: error.message,
          originalText: request.items[i].text.substring(0, 100) + '...'
        });
      }
    }

    const response: BatchRedactionResponse = {
      batchId: request.batchId,
      totalItems: request.items.length,
      successfulItems: results.length,
      failedItems: errors.length,
      results,
      processingTime: Date.now() - startTime,
      errors
    };

    logger.info('Batch redaction completed', {
      batchId: request.batchId,
      successfulItems: response.successfulItems,
      failedItems: response.failedItems,
      processingTime: response.processingTime
    });

    return response;
  }

  /**
   * Get redaction statistics
   */
  async getRedactionStats(): Promise<RedactionStats> {
    // This would query the database for actual statistics
    // For now, returning mock data structure
    return {
      totalRedactions: 0,
      redactionsByType: {} as Record<SensitiveDataType, number>,
      redactionsByEntity: {} as Record<EntityContext, number>,
      redactionsByLevel: {} as Record<RedactionLevel, number>,
      averageProcessingTime: 0,
      falsePositiveRate: 0,
      reversibleRedactions: 0,
      recentActivity: {
        last24Hours: 0,
        lastWeek: 0,
        lastMonth: 0
      }
    };
  }

  /**
   * Create or update redaction rule
   */
  async createRedactionRule(rule: Omit<RedactionRule, 'id' | 'createdAt' | 'updatedAt'>): Promise<RedactionRule> {
    // Validate pattern
    if (!RedactionUtils.validatePattern(rule.pattern)) {
      throw new PatternMatchError('Invalid regex pattern', { pattern: rule.pattern });
    }

    const newRule: RedactionRule = {
      ...rule,
      id: `rule_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // Store in cache
    this.ruleCache.set(newRule.id, newRule);

    logger.info('Created redaction rule', {
      ruleId: newRule.id,
      name: newRule.name,
      dataType: newRule.dataType
    });

    return newRule;
  }

  /**
   * Private helper methods
   */
  private validateRedactionRequest(request: RedactionRequest): void {
    if (!request.text || request.text.length > REDACTION_CONSTANTS.MAX_TEXT_SIZE) {
      throw new RedactionError('Invalid text size', 'INVALID_TEXT_SIZE');
    }

    if (!Object.values(EntityContext).includes(request.entityType)) {
      throw new RedactionError('Invalid entity type', 'INVALID_ENTITY_TYPE');
    }

    if (!Object.values(RedactionLevel).includes(request.redactionLevel)) {
      throw new RedactionError('Invalid redaction level', 'INVALID_REDACTION_LEVEL');
    }
  }

  private async getApplicableRules(
    entityType: EntityContext,
    level: RedactionLevel,
    customRules?: string[]
  ): Promise<RedactionRule[]> {
    const rules: RedactionRule[] = [];

    // Get custom rules if specified
    if (customRules) {
      for (const ruleId of customRules) {
        const rule = this.ruleCache.get(ruleId);
        if (rule && rule.isActive) {
          rules.push(rule);
        }
      }
    } else {
      // Get rules based on entity type and level
      for (const rule of this.ruleCache.values()) {
        if (rule.isActive && rule.entityTypes.includes(entityType)) {
          rules.push(rule);
        }
      }
    }

    // Sort by priority
    return rules.sort((a, b) => b.priority - a.priority);
  }

  private analyzeContext(text: string, entityType: EntityContext, hints?: Record<string, any>): ContextAnalysis {
    // Basic context analysis implementation
    const surroundingKeywords = this.extractKeywords(text);
    const structuralContext = this.analyzeStructure(text, entityType);
    const semanticContext = this.analyzeSemantics(text, entityType);
    const riskLevel = this.assessRiskLevel(text, entityType, surroundingKeywords);

    return {
      entityType,
      surroundingKeywords,
      structuralContext,
      semanticContext,
      riskLevel
    };
  }

  private async findSensitiveData(
    text: string,
    rules: RedactionRule[],
    contextAnalysis?: ContextAnalysis
  ): Promise<RedactionMatch[]> {
    const matches: RedactionMatch[] = [];

    for (const rule of rules) {
      try {
        const regex = new RegExp(rule.pattern, 'g');
        let match;

        while ((match = regex.exec(text)) !== null) {
          const startIndex = match.index;
          const endIndex = startIndex + match[0].length;
          const originalValue = match[0];

          // Calculate confidence based on context
          const confidence = this.calculateConfidence(originalValue, rule, contextAnalysis);

          if (confidence >= this.config.confidenceThreshold) {
            const redactedValue = rule.preserveFormat
              ? RedactionUtils.preserveFormat(originalValue, rule.replacement)
              : rule.replacement;

            matches.push({
              startIndex,
              endIndex,
              originalValue,
              redactedValue,
              dataType: rule.dataType,
              ruleId: rule.id,
              confidence,
              context: this.extractContext(text, startIndex, endIndex),
              reversible: rule.reversible
            });
          }
        }
      } catch (error) {
        logger.warn('Rule pattern failed', { ruleId: rule.id, error: error.message });
      }
    }

    return matches;
  }

  private filterMatches(matches: RedactionMatch[], contextAnalysis?: ContextAnalysis): RedactionMatch[] {
    if (!this.config.enableFalsePositiveReduction) {
      return matches;
    }

    // Remove overlapping matches (keep highest confidence)
    const filtered = matches.filter((match, index) => {
      const overlapping = matches.find((other, otherIndex) =>
        otherIndex !== index &&
        ((match.startIndex >= other.startIndex && match.startIndex < other.endIndex) ||
         (match.endIndex > other.startIndex && match.endIndex <= other.endIndex))
      );

      return !overlapping || match.confidence >= overlapping.confidence;
    });

    return filtered.sort((a, b) => a.startIndex - b.startIndex);
  }

  private applyRedactions(text: string, matches: RedactionMatch[], preserveStructure?: boolean): string {
    if (matches.length === 0) {
      return text;
    }

    let result = text;
    let offset = 0;

    for (const match of matches) {
      const adjustedStart = match.startIndex + offset;
      const adjustedEnd = match.endIndex + offset;

      result = result.substring(0, adjustedStart) +
               match.redactedValue +
               result.substring(adjustedEnd);

      offset += match.redactedValue.length - match.originalValue.length;
    }

    return result;
  }

  private calculateConfidence(value: string, rule: RedactionRule, contextAnalysis?: ContextAnalysis): number {
    let confidence = 0.8; // Base confidence

    // Adjust based on context
    if (contextAnalysis && rule.contextAware) {
      // Higher confidence if found in high-risk contexts
      if (contextAnalysis.riskLevel === 'CRITICAL') {
        confidence += 0.15;
      } else if (contextAnalysis.riskLevel === 'HIGH') {
        confidence += 0.1;
      }

      // Adjust based on surrounding keywords
      const relevantKeywords = ['password', 'key', 'token', 'secret', 'api'];
      const hasRelevantKeywords = contextAnalysis.surroundingKeywords.some(keyword =>
        relevantKeywords.includes(keyword.toLowerCase())
      );

      if (hasRelevantKeywords) {
        confidence += 0.1;
      }
    }

    return Math.min(confidence, 1.0);
  }

  private extractContext(text: string, startIndex: number, endIndex: number): string {
    const contextStart = Math.max(0, startIndex - REDACTION_CONSTANTS.CONTEXT_WINDOW_SIZE);
    const contextEnd = Math.min(text.length, endIndex + REDACTION_CONSTANTS.CONTEXT_WINDOW_SIZE);
    return text.substring(contextStart, contextEnd);
  }

  private extractKeywords(text: string): string[] {
    // Simple keyword extraction
    const words = text.toLowerCase().match(/\b\w+\b/g) || [];
    return [...new Set(words)].slice(0, 20); // Top 20 unique words
  }

  private analyzeStructure(text: string, entityType: EntityContext): string {
    switch (entityType) {
      case EntityContext.CODE:
        return 'code_block';
      case EntityContext.LOG:
        return 'log_entry';
      case EntityContext.CONFIGURATION:
        return 'config_file';
      default:
        return 'text';
    }
  }

  private analyzeSemantics(text: string, entityType: EntityContext): string {
    // Basic semantic analysis
    if (text.includes('function') || text.includes('class')) {
      return 'code_definition';
    }
    if (text.includes('ERROR') || text.includes('WARN')) {
      return 'error_log';
    }
    return 'general_text';
  }

  private assessRiskLevel(text: string, entityType: EntityContext, keywords: string[]): 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' {
    const highRiskKeywords = ['password', 'secret', 'key', 'token', 'credential'];
    const hasHighRiskKeywords = keywords.some(k => highRiskKeywords.includes(k.toLowerCase()));

    if (hasHighRiskKeywords && entityType === EntityContext.CONFIGURATION) {
      return 'CRITICAL';
    }
    if (hasHighRiskKeywords) {
      return 'HIGH';
    }
    if (entityType === EntityContext.DATABASE || entityType === EntityContext.API_RESPONSE) {
      return 'MEDIUM';
    }
    return 'LOW';
  }

  private async auditRedaction(
    result: RedactionResult,
    action: 'REDACT' | 'UNREDACT' | 'VIEW_REDACTED',
    performedBy: string,
    reason?: string
  ): Promise<void> {
    const audit: RedactionAudit = {
      id: `audit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      redactionId: result.integrityHash,
      action,
      performedBy,
      timestamp: new Date(),
      reason: reason || 'Automated redaction',
      originalHash: result.integrityHash,
      redactedHash: RedactionUtils.calculateIntegrityHash(result.redactedText),
      rulesApplied: result.metadata.rulesApplied,
      metadata: {
        redactionCount: result.metadata.redactionCount,
        processingTime: result.metadata.processingTime,
        sensitiveDataTypes: result.metadata.sensitiveDataFound
      }
    };

    // In a real implementation, this would be stored in the database
    logger.info('Redaction audited', audit);
  }

  private initializeBuiltInRules(): void {
    // Initialize built-in patterns as rules
    Object.entries(BUILT_IN_PATTERNS).forEach(([dataType, patterns]) => {
      patterns.forEach((pattern, index) => {
        const rule: RedactionRule = {
          id: `builtin_${dataType.toLowerCase()}_${index}`,
          name: `Built-in ${dataType} Pattern ${index + 1}`,
          description: `Detects ${dataType} patterns`,
          pattern: pattern.source,
          replacement: `[REDACTED_${dataType}]`,
          dataType: dataType as SensitiveDataType,
          entityTypes: Object.values(EntityContext),
          isActive: true,
          priority: 100,
          preserveFormat: false,
          contextAware: true,
          reversible: true,
          createdAt: new Date(),
          updatedAt: new Date(),
          createdBy: 'system'
        };

        this.ruleCache.set(rule.id, rule);
      });
    });

    logger.info('Initialized built-in redaction rules', {
      ruleCount: this.ruleCache.size
    });
  }
}

/* EcoStamp Widget Styles */
#ecostamp-widget {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 280px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 12px;
  z-index: 10000;
  transition: all 0.3s ease;
}

#ecostamp-widget:hover {
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.ecostamp-header {
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  color: white;
  padding: 8px 12px;
  border-radius: 12px 12px 0 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: move;
  user-select: none;
}

.ecostamp-icon {
  font-size: 16px;
  margin-right: 6px;
}

.ecostamp-title {
  font-weight: 600;
  font-size: 13px;
  flex: 1;
}

.ecostamp-toggle {
  background: none;
  border: none;
  color: white;
  font-size: 16px;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 3px;
  transition: background-color 0.2s;
}

.ecostamp-toggle:hover {
  background: rgba(255, 255, 255, 0.2);
}

.ecostamp-content {
  padding: 12px;
  background: white;
  border-radius: 0 0 12px 12px;
}

.ecostamp-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding: 4px 0;
}

.ecostamp-stat:last-of-type {
  margin-bottom: 12px;
}

.ecostamp-label {
  font-weight: 500;
  color: #2c3e50;
  font-size: 11px;
}

.ecostamp-value {
  font-weight: 600;
  color: #27ae60;
  font-size: 11px;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

#eco-level {
  font-size: 14px;
  letter-spacing: 1px;
}

#conversation-hash {
  font-family: 'Courier New', monospace;
  font-size: 10px;
  cursor: pointer;
  transition: color 0.2s;
}

#conversation-hash:hover {
  color: #2980b9;
}

.ecostamp-actions {
  display: flex;
  gap: 6px;
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #ecf0f1;
}

.ecostamp-btn {
  flex: 1;
  background: #3498db;
  color: white;
  border: none;
  padding: 6px 8px;
  border-radius: 6px;
  font-size: 10px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.ecostamp-btn:hover {
  background: #2980b9;
  transform: translateY(-1px);
}

.ecostamp-btn:active {
  transform: translateY(0);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  #ecostamp-widget {
    width: 240px;
    top: 10px;
    right: 10px;
  }
  
  .ecostamp-content {
    padding: 10px;
  }
  
  .ecostamp-stat {
    margin-bottom: 6px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  #ecostamp-widget {
    background: rgba(44, 62, 80, 0.95);
    border-color: rgba(255, 255, 255, 0.1);
  }
  
  .ecostamp-content {
    background: #2c3e50;
  }
  
  .ecostamp-label {
    color: #bdc3c7;
  }
  
  .ecostamp-value {
    color: #2ecc71;
  }
  
  .ecostamp-actions {
    border-top-color: #34495e;
  }
}

/* Animation for new data */
@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.ecostamp-value.updated {
  animation: pulse 0.3s ease-in-out;
}

/* Tooltip styles */
.ecostamp-tooltip {
  position: relative;
}

.ecostamp-tooltip::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: #2c3e50;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 10px;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s;
  z-index: 10001;
}

.ecostamp-tooltip:hover::after {
  opacity: 1;
}

/* Loading state */
.ecostamp-loading {
  opacity: 0.6;
}

.ecostamp-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 16px;
  height: 16px;
  margin: -8px 0 0 -8px;
  border: 2px solid #27ae60;
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Minimized state */
#ecostamp-widget.minimized {
  width: 60px;
  height: 40px;
}

#ecostamp-widget.minimized .ecostamp-content {
  display: none;
}

#ecostamp-widget.minimized .ecostamp-title {
  display: none;
}

/* High contrast mode */
@media (prefers-contrast: high) {
  #ecostamp-widget {
    border: 2px solid #000;
  }
  
  .ecostamp-header {
    background: #000;
  }
  
  .ecostamp-btn {
    background: #000;
    border: 1px solid #fff;
  }
}

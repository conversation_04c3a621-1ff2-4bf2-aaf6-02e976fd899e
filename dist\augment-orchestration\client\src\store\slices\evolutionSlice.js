"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.clearError = exports.fetchEvolutionVariants = void 0;
const toolkit_1 = require("@reduxjs/toolkit");
const api_1 = require("../../services/api");
const initialState = {
    variants: [],
    isLoading: false,
    error: null,
};
exports.fetchEvolutionVariants = (0, toolkit_1.createAsyncThunk)('evolution/fetchVariants', async () => {
    const response = await api_1.evolutionApi.getVariants();
    return response.data;
});
const evolutionSlice = (0, toolkit_1.createSlice)({
    name: 'evolution',
    initialState,
    reducers: {
        clearError: (state) => {
            state.error = null;
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(exports.fetchEvolutionVariants.fulfilled, (state, action) => {
            state.variants = action.payload;
        });
    },
});
exports.clearError = evolutionSlice.actions.clearError;
exports.default = evolutionSlice.reducer;

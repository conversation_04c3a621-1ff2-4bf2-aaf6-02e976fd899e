import type { ActionFunctionArgs, LoaderFunctionArgs, MetaFunction } from "@remix-run/node";
import { json, redirect } from "@remix-run/node";
import { useActionData, useLoaderData, useNavigation } from "@remix-run/react";
import { useState } from "react";
import { motion } from "framer-motion";
import { 
  Upload, 
  FileText, 
  Image, 
  Video, 
  Music, 
  Code,
  CheckCircle,
  AlertCircle,
  Info,
  Zap,
  Shield
} from "lucide-react";

import { requireUser } from "~/utils/auth.server";
import { DashboardLayout } from "~/components/layout/DashboardLayout";
import { Button } from "~/components/ui/Button";
import { UploadDropzone } from "~/components/upload/UploadDropzone";
import { ContentTypeSelector } from "~/components/upload/ContentTypeSelector";
import { AITrainingOptOut } from "~/components/upload/AITrainingOptOut";
import { VerificationOptions } from "~/components/upload/VerificationOptions";
import { TagInput } from "~/components/ui/TagInput";

export const meta: MetaFunction = () => {
  return [
    { title: "Upload Content - EcoStamp" },
    { name: "description", content: "Upload and verify your digital content with EcoStamp" },
  ];
};

export async function loader({ request }: LoaderFunctionArgs) {
  const user = await requireUser(request);
  
  // Check upload limits based on plan
  const uploadLimits = {
    free: { maxFileSize: 10, maxFiles: 5, formats: ["jpg", "png", "pdf", "txt"] },
    pro: { maxFileSize: 100, maxFiles: 50, formats: ["all"] },
    "pro-plus": { maxFileSize: 500, maxFiles: 100, formats: ["all"] },
    team: { maxFileSize: 500, maxFiles: 100, formats: ["all"] },
    "team-plus": { maxFileSize: 1000, maxFiles: 500, formats: ["all"] },
  };

  return json({ 
    user, 
    uploadLimits: uploadLimits[user.plan as keyof typeof uploadLimits] || uploadLimits.free 
  });
}

export async function action({ request }: ActionFunctionArgs) {
  const user = await requireUser(request);
  const formData = await request.formData();
  
  const contentType = formData.get("contentType") as string;
  const creationType = formData.get("creationType") as string;
  const aiTrainingOptOut = formData.get("aiTrainingOptOut") === "true";
  const tags = JSON.parse(formData.get("tags") as string || "[]");
  const description = formData.get("description") as string;
  
  // In a real app, handle file upload to Supabase Storage
  // and create verification record
  
  try {
    // Simulate upload processing
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const uploadResult = {
      id: `upload_${Date.now()}`,
      status: "processing",
      verificationCode: `EC${Math.random().toString(36).substr(2, 8).toUpperCase()}`,
      estimatedCompletion: new Date(Date.now() + 5 * 60 * 1000), // 5 minutes
    };
    
    return json({ success: true, upload: uploadResult });
  } catch (error) {
    return json({ 
      success: false, 
      error: "Upload failed. Please try again." 
    }, { status: 400 });
  }
}

const fadeInUp = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.6 }
};

const contentTypes = [
  { id: "image", name: "Image", icon: Image, description: "Photos, artwork, graphics" },
  { id: "document", name: "Document", icon: FileText, description: "PDFs, articles, reports" },
  { id: "video", name: "Video", icon: Video, description: "Videos, animations, films" },
  { id: "audio", name: "Audio", icon: Music, description: "Music, podcasts, recordings" },
  { id: "code", name: "Code", icon: Code, description: "Source code, scripts" },
];

const creationTypes = [
  {
    id: "human-only",
    name: "Human-Made Only",
    description: "Created entirely by humans without AI assistance",
    icon: Shield,
    badge: "Human-Made",
  },
  {
    id: "human-ai-assisted",
    name: "Human-Made with AI Assistance",
    description: "Human-created with AI tools for enhancement or efficiency",
    icon: Zap,
    badge: "AI-Assisted",
  },
  {
    id: "ai-generated",
    name: "AI-Generated",
    description: "Created primarily or entirely by AI systems",
    icon: Info,
    badge: "AI-Generated",
  },
];

export default function UploadPage() {
  const { user, uploadLimits } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();
  
  const [selectedContentType, setSelectedContentType] = useState<string>("");
  const [selectedCreationType, setSelectedCreationType] = useState<string>("");
  const [aiTrainingOptOut, setAiTrainingOptOut] = useState(false);
  const [tags, setTags] = useState<string[]>([]);
  const [description, setDescription] = useState("");
  const [files, setFiles] = useState<File[]>([]);

  const isSubmitting = navigation.state === "submitting";

  if (actionData?.success) {
    return (
      <DashboardLayout user={user}>
        <motion.div
          initial="initial"
          animate="animate"
          variants={fadeInUp}
          className="max-w-2xl mx-auto text-center py-12"
        >
          <div className="bg-white rounded-2xl border border-secondary-200 p-8">
            <CheckCircle className="h-16 w-16 text-primary-500 mx-auto mb-6" />
            <h1 className="text-2xl font-bold text-secondary-900 mb-4">
              Upload Successful!
            </h1>
            <p className="text-secondary-600 mb-6">
              Your content has been uploaded and is being processed for verification.
            </p>
            <div className="bg-primary-50 rounded-lg p-4 mb-6">
              <p className="text-sm text-primary-800">
                <strong>Verification Code:</strong> {actionData.upload.verificationCode}
              </p>
              <p className="text-sm text-primary-700 mt-1">
                Estimated completion: {new Date(actionData.upload.estimatedCompletion).toLocaleString()}
              </p>
            </div>
            <div className="flex gap-4 justify-center">
              <Button asChild>
                <a href="/dashboard/verifications">View Status</a>
              </Button>
              <Button variant="outline" asChild>
                <a href="/dashboard/upload">Upload More</a>
              </Button>
            </div>
          </div>
        </motion.div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout user={user}>
      <div className="max-w-4xl mx-auto space-y-8">
        {/* Header */}
        <motion.div
          initial="initial"
          animate="animate"
          variants={fadeInUp}
        >
          <h1 className="text-3xl font-bold text-secondary-900">Upload Content</h1>
          <p className="mt-2 text-secondary-600">
            Upload your digital content for verification and provenance tracking.
          </p>
        </motion.div>

        {/* Upload Form */}
        <motion.div
          initial="initial"
          animate="animate"
          variants={fadeInUp}
          className="bg-white rounded-2xl border border-secondary-200 p-8"
        >
          <form method="post" encType="multipart/form-data" className="space-y-8">
            {/* Content Type Selection */}
            <div>
              <label className="block text-sm font-medium text-secondary-900 mb-4">
                What type of content are you uploading?
              </label>
              <ContentTypeSelector
                contentTypes={contentTypes}
                selected={selectedContentType}
                onChange={setSelectedContentType}
              />
              <input type="hidden" name="contentType" value={selectedContentType} />
            </div>

            {/* Creation Type Selection */}
            <div>
              <label className="block text-sm font-medium text-secondary-900 mb-4">
                How was this content created?
              </label>
              <div className="grid gap-4 md:grid-cols-3">
                {creationTypes.map((type) => (
                  <button
                    key={type.id}
                    type="button"
                    onClick={() => setSelectedCreationType(type.id)}
                    className={`p-4 rounded-lg border-2 text-left transition-all ${
                      selectedCreationType === type.id
                        ? "border-primary-500 bg-primary-50"
                        : "border-secondary-200 hover:border-secondary-300"
                    }`}
                  >
                    <div className="flex items-center mb-2">
                      <type.icon className="h-5 w-5 text-primary-600 mr-2" />
                      <span className="font-medium text-secondary-900">{type.name}</span>
                    </div>
                    <p className="text-sm text-secondary-600">{type.description}</p>
                    <span className="inline-block mt-2 px-2 py-1 text-xs font-medium bg-secondary-100 text-secondary-700 rounded">
                      {type.badge}
                    </span>
                  </button>
                ))}
              </div>
              <input type="hidden" name="creationType" value={selectedCreationType} />
            </div>

            {/* AI Training Opt-out */}
            {(selectedCreationType === "human-ai-assisted" || selectedCreationType === "ai-generated") && (
              <AITrainingOptOut
                optOut={aiTrainingOptOut}
                onChange={setAiTrainingOptOut}
              />
            )}

            {/* File Upload */}
            <div>
              <label className="block text-sm font-medium text-secondary-900 mb-4">
                Upload Files
              </label>
              <UploadDropzone
                onFilesSelected={setFiles}
                maxFiles={uploadLimits.maxFiles}
                maxFileSize={uploadLimits.maxFileSize}
                acceptedFormats={uploadLimits.formats}
              />
              <div className="mt-2 text-sm text-secondary-500">
                Max {uploadLimits.maxFiles} files, {uploadLimits.maxFileSize}MB each
                {user.plan === "free" && (
                  <span className="ml-2 text-accent-600">
                    • Upgrade for larger files and more formats
                  </span>
                )}
              </div>
            </div>

            {/* Description */}
            <div>
              <label htmlFor="description" className="block text-sm font-medium text-secondary-900 mb-2">
                Description (Optional)
              </label>
              <textarea
                id="description"
                name="description"
                rows={3}
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                className="w-full rounded-lg border border-secondary-300 px-3 py-2 text-sm focus:border-primary-500 focus:outline-none focus:ring-1 focus:ring-primary-500"
                placeholder="Describe your content..."
              />
            </div>

            {/* Tags */}
            <div>
              <label className="block text-sm font-medium text-secondary-900 mb-2">
                Tags
              </label>
              <TagInput
                tags={tags}
                onChange={setTags}
                placeholder="Add tags to help organize your content..."
              />
              <input type="hidden" name="tags" value={JSON.stringify(tags)} />
            </div>

            {/* Verification Options */}
            <VerificationOptions plan={user.plan} />

            {/* Hidden inputs */}
            <input type="hidden" name="aiTrainingOptOut" value={aiTrainingOptOut.toString()} />

            {/* Error Display */}
            {actionData?.error && (
              <div className="flex items-center p-4 bg-red-50 border border-red-200 rounded-lg">
                <AlertCircle className="h-5 w-5 text-red-500 mr-3" />
                <p className="text-sm text-red-700">{actionData.error}</p>
              </div>
            )}

            {/* Submit Button */}
            <div className="flex justify-end">
              <Button
                type="submit"
                disabled={!selectedContentType || !selectedCreationType || files.length === 0 || isSubmitting}
                className="min-w-32"
              >
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Uploading...
                  </>
                ) : (
                  <>
                    <Upload className="h-4 w-4 mr-2" />
                    Upload & Verify
                  </>
                )}
              </Button>
            </div>
          </form>
        </motion.div>
      </div>
    </DashboardLayout>
  );
}

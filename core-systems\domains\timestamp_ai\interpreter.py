"""
LLM Output Interpreter for TimeStamp AI

Interprets and normalizes LLM outputs, timestamp data, and environmental
impact calculations into standardized formats for feedback processing.
"""

import json
import re
import logging
from typing import Any, Dict, List, Optional
from datetime import datetime, timezone
import hashlib


class LLMOutputInterpreter:
    """
    Interprets LLM outputs and timestamp AI data into standardized format.
    
    Supports various output types including timestamp responses, environmental
    impact calculations, hash verifications, and general LLM responses.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.supported_formats = [
            'json', 'text', 'timestamp_response', 'impact_calculation', 
            'hash_verification', 'llm_conversation'
        ]
        
        # Standard field mappings for different output types
        self.field_mappings = {
            'timestamp': {
                'timestamp': ['timestamp', 'time', 'created_at', 'generated_at'],
                'hash': ['hash', 'content_hash', 'sha256', 'checksum'],
                'signature': ['signature', 'tsa_signature', 'digital_signature'],
                'verification_status': ['verified', 'valid', 'verification_status']
            },
            'environmental_impact': {
                'water_usage': ['water_usage', 'water_consumption', 'h2o_usage'],
                'electricity_usage': ['electricity_usage', 'power_consumption', 'energy_usage'],
                'carbon_footprint': ['carbon_footprint', 'co2_emissions', 'carbon_impact'],
                'token_count': ['token_count', 'tokens', 'total_tokens'],
                'model_used': ['model', 'ai_model', 'llm_model']
            },
            'llm_response': {
                'content': ['content', 'response', 'text', 'output'],
                'confidence': ['confidence', 'certainty', 'probability'],
                'model': ['model', 'ai_model', 'engine'],
                'tokens_used': ['tokens_used', 'token_count', 'usage'],
                'processing_time': ['processing_time', 'response_time', 'latency']
            }
        }
        
        # Regex patterns for extracting structured data from text
        self.extraction_patterns = {
            'timestamp': r'(\d{4}-\d{2}-\d{2}[T\s]\d{2}:\d{2}:\d{2}(?:\.\d+)?(?:Z|[+-]\d{2}:\d{2})?)',
            'hash': r'([a-fA-F0-9]{64})',  # SHA-256 hash pattern
            'signature': r'([a-fA-F0-9]{128,})',  # Digital signature pattern
            'number': r'(\d+(?:\.\d+)?)',
            'percentage': r'(\d+(?:\.\d+)?%)',
            'boolean': r'\b(true|false|yes|no|valid|invalid)\b'
        }
    
    def interpret(self, raw_output: Any, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Interpret and normalize raw LLM or timestamp AI output.
        
        Args:
            raw_output: Raw output from LLM or timestamp system
            context: Additional context including output type, model info
            
        Returns:
            Normalized output dictionary with standardized fields
        """
        try:
            # Determine output type and format
            output_type = context.get('output_type', 'unknown')
            ai_model = context.get('ai_model', 'unknown')
            
            # Parse raw output based on format
            parsed_data = self._parse_raw_output(raw_output, context)
            
            # Normalize based on output type
            normalized_data = self._normalize_output_data(parsed_data, output_type)
            
            # Extract structured information from text if needed
            if output_type in ['timestamp_response', 'llm_conversation']:
                self._extract_structured_data(normalized_data, parsed_data)
            
            # Add metadata
            normalized_data['_metadata'] = {
                'original_format': type(raw_output).__name__,
                'output_type': output_type,
                'ai_model': ai_model,
                'interpretation_timestamp': datetime.utcnow().isoformat(),
                'interpreter_version': '1.0.0'
            }
            
            # Validate and enrich timestamp data
            if output_type == 'timestamp_response':
                self._validate_timestamp_data(normalized_data)
            
            # Calculate content hash if not provided
            if 'content' in normalized_data and 'hash' not in normalized_data:
                normalized_data['calculated_hash'] = self._calculate_content_hash(
                    normalized_data['content']
                )
            
            return normalized_data
            
        except Exception as e:
            self.logger.error(f"Error interpreting LLM output: {str(e)}")
            return {
                'error': True,
                'error_message': str(e),
                'raw_output': str(raw_output)[:1000],  # Truncate for logging
                '_metadata': {
                    'interpretation_failed': True,
                    'error_timestamp': datetime.utcnow().isoformat()
                }
            }
    
    def _parse_raw_output(self, raw_output: Any, context: Dict[str, Any]) -> Dict[str, Any]:
        """Parse raw output based on its format."""
        if isinstance(raw_output, dict):
            return raw_output
        
        if isinstance(raw_output, str):
            # Try to parse as JSON first
            try:
                return json.loads(raw_output)
            except json.JSONDecodeError:
                # Treat as text response
                return {'text_content': raw_output}
        
        if isinstance(raw_output, (list, tuple)):
            # Handle list/tuple responses
            if len(raw_output) == 1:
                return self._parse_raw_output(raw_output[0], context)
            else:
                return {'list_content': list(raw_output)}
        
        # For other types, create a wrapper
        return {'raw_value': raw_output}
    
    def _normalize_output_data(self, parsed_data: Dict[str, Any], output_type: str) -> Dict[str, Any]:
        """Normalize output data using field mappings."""
        normalized = {}
        
        if output_type in self.field_mappings:
            mappings = self.field_mappings[output_type]
            
            for standard_field, possible_names in mappings.items():
                value = None
                source_field = None
                
                # Find the first matching field
                for field_name in possible_names:
                    if field_name in parsed_data:
                        value = parsed_data[field_name]
                        source_field = field_name
                        break
                
                if value is not None:
                    # Convert to appropriate type
                    normalized[standard_field] = self._convert_value(value, standard_field)
                    normalized[f'{standard_field}_source'] = source_field
        
        # Copy any unmapped fields with prefix
        for key, value in parsed_data.items():
            if key not in [name for names in self.field_mappings.get(output_type, {}).values() for name in names]:
                normalized[f'extra_{key}'] = value
        
        return normalized
    
    def _extract_structured_data(self, normalized_data: Dict[str, Any], parsed_data: Dict[str, Any]) -> None:
        """Extract structured data from text content using regex patterns."""
        text_content = parsed_data.get('text_content', '')
        if not text_content:
            return
        
        # Extract timestamps
        timestamp_matches = re.findall(self.extraction_patterns['timestamp'], text_content)
        if timestamp_matches and 'timestamp' not in normalized_data:
            normalized_data['extracted_timestamp'] = timestamp_matches[0]
        
        # Extract hashes
        hash_matches = re.findall(self.extraction_patterns['hash'], text_content)
        if hash_matches and 'hash' not in normalized_data:
            normalized_data['extracted_hash'] = hash_matches[0]
        
        # Extract signatures
        signature_matches = re.findall(self.extraction_patterns['signature'], text_content)
        if signature_matches and 'signature' not in normalized_data:
            normalized_data['extracted_signature'] = signature_matches[0]
        
        # Extract numbers (for impact calculations)
        number_matches = re.findall(self.extraction_patterns['number'], text_content)
        if number_matches:
            normalized_data['extracted_numbers'] = [float(n) for n in number_matches]
        
        # Extract boolean values
        boolean_matches = re.findall(self.extraction_patterns['boolean'], text_content, re.IGNORECASE)
        if boolean_matches:
            normalized_data['extracted_booleans'] = [
                b.lower() in ['true', 'yes', 'valid'] for b in boolean_matches
            ]
    
    def _convert_value(self, value: Any, field_type: str) -> Any:
        """Convert value to appropriate type based on field."""
        if value is None:
            return None
        
        # Numeric fields
        numeric_fields = [
            'water_usage', 'electricity_usage', 'carbon_footprint', 
            'token_count', 'tokens_used', 'processing_time', 'confidence'
        ]
        
        if field_type in numeric_fields:
            try:
                return float(value)
            except (ValueError, TypeError):
                return value
        
        # Boolean fields
        boolean_fields = ['verification_status', 'verified', 'valid']
        if field_type in boolean_fields:
            if isinstance(value, bool):
                return value
            if isinstance(value, str):
                return value.lower() in ['true', 'yes', 'valid', '1']
            return bool(value)
        
        # Timestamp fields
        if field_type in ['timestamp', 'created_at', 'generated_at']:
            return self._parse_timestamp(value)
        
        return value
    
    def _parse_timestamp(self, timestamp_value: Any) -> Optional[str]:
        """Parse timestamp value into ISO format."""
        if timestamp_value is None:
            return None
        
        if isinstance(timestamp_value, datetime):
            return timestamp_value.isoformat()
        
        if isinstance(timestamp_value, str):
            try:
                # Try to parse various timestamp formats
                dt = datetime.fromisoformat(timestamp_value.replace('Z', '+00:00'))
                return dt.isoformat()
            except ValueError:
                try:
                    # Try other common formats
                    dt = datetime.strptime(timestamp_value, '%Y-%m-%d %H:%M:%S')
                    return dt.isoformat()
                except ValueError:
                    return timestamp_value  # Return as-is if parsing fails
        
        return str(timestamp_value)
    
    def _validate_timestamp_data(self, normalized_data: Dict[str, Any]) -> None:
        """Validate timestamp-specific data."""
        # Check if timestamp is present and valid
        timestamp = normalized_data.get('timestamp') or normalized_data.get('extracted_timestamp')
        if timestamp:
            try:
                dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                normalized_data['timestamp_valid'] = True
                normalized_data['timestamp_parsed'] = dt.isoformat()
            except ValueError:
                normalized_data['timestamp_valid'] = False
                normalized_data['timestamp_error'] = 'Invalid timestamp format'
        
        # Validate hash format
        hash_value = normalized_data.get('hash') or normalized_data.get('extracted_hash')
        if hash_value:
            if re.match(r'^[a-fA-F0-9]{64}$', hash_value):
                normalized_data['hash_valid'] = True
            else:
                normalized_data['hash_valid'] = False
                normalized_data['hash_error'] = 'Invalid SHA-256 hash format'
    
    def _calculate_content_hash(self, content: str) -> str:
        """Calculate SHA-256 hash of content."""
        return hashlib.sha256(content.encode('utf-8')).hexdigest()
    
    def validate_input(self, raw_output: Any) -> bool:
        """
        Validate that the raw output can be interpreted.
        
        Args:
            raw_output: The raw output to validate
            
        Returns:
            True if the output can be interpreted, False otherwise
        """
        if raw_output is None:
            return False
        
        # Accept most common data types
        accepted_types = (dict, list, tuple, str, int, float, bool)
        return isinstance(raw_output, accepted_types)
    
    def get_supported_formats(self) -> List[str]:
        """
        Get list of supported output formats.
        
        Returns:
            List of format identifiers this interpreter supports
        """
        return self.supported_formats.copy()
    
    def add_field_mapping(self, output_type: str, standard_field: str, field_names: List[str]) -> None:
        """
        Add custom field mapping for an output type.
        
        Args:
            output_type: Type of output (e.g., 'custom_llm')
            standard_field: Standard field name
            field_names: List of possible field names in raw data
        """
        if output_type not in self.field_mappings:
            self.field_mappings[output_type] = {}
        
        self.field_mappings[output_type][standard_field] = field_names
        self.logger.info(f"Added field mapping for {output_type}.{standard_field}: {field_names}")
    
    def add_extraction_pattern(self, pattern_name: str, regex_pattern: str) -> None:
        """
        Add custom regex pattern for extracting structured data.
        
        Args:
            pattern_name: Name of the pattern
            regex_pattern: Regular expression pattern
        """
        self.extraction_patterns[pattern_name] = regex_pattern
        self.logger.info(f"Added extraction pattern '{pattern_name}': {regex_pattern}")
    
    def get_field_mappings(self, output_type: str) -> Dict[str, List[str]]:
        """
        Get field mappings for a specific output type.
        
        Args:
            output_type: Type of output
            
        Returns:
            Dictionary of field mappings
        """
        return self.field_mappings.get(output_type, {}).copy()

{"compilerOptions": {"target": "ES2020", "module": "commonjs", "outDir": "./dist", "rootDir": "./", "strict": false, "noImplicitAny": false, "skipLibCheck": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "baseUrl": "./", "types": ["jest", "node"]}, "include": ["core-systems/EcoStamp/source/**/*.ts", "ai-orchestration/**/*.ts", "development-tools/**/*.ts", "tests/**/*.ts"], "exclude": ["node_modules", "dist"]}
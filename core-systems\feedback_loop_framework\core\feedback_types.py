"""
Feedback Types and Core Data Structures

This module defines the fundamental feedback types and data structures
used throughout the Universal Dual-Purpose Feedback Loop Framework.
"""

from enum import Enum
from dataclasses import dataclass, field
from typing import Dict, Any, Optional, List
from datetime import datetime
import uuid


class FeedbackType(Enum):
    """
    Standardized feedback types for AI output evaluation.
    
    - CORRECT: Output is completely accurate and meets all requirements
    - PARTIALLY_CORRECT: Output is mostly accurate but has minor issues or omissions
    - INCORRECT: Output is fundamentally wrong or fails to meet requirements
    - MISCELLANEOUS: Special cases that don't fit standard categories
    """
    CORRECT = "correct"
    PARTIALLY_CORRECT = "partially_correct"
    INCORRECT = "incorrect"
    MISCELLANEOUS = "miscellaneous"


class ConfidenceLevel(Enum):
    """Confidence levels for feedback assessment."""
    VERY_HIGH = "very_high"    # 90-100%
    HIGH = "high"              # 75-89%
    MEDIUM = "medium"          # 50-74%
    LOW = "low"                # 25-49%
    VERY_LOW = "very_low"      # 0-24%


class ValidationSeverity(Enum):
    """Severity levels for validation issues."""
    CRITICAL = "critical"      # System-breaking issues
    HIGH = "high"              # Major functionality issues
    MEDIUM = "medium"          # Minor functionality issues
    LOW = "low"                # Style or optimization issues
    INFO = "info"              # Informational notices


@dataclass
class FeedbackEntry:
    """
    Core feedback entry containing all information about a single AI output evaluation.
    """
    # Unique identifiers
    entry_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    domain: str = ""
    agent_id: Optional[str] = None
    
    # Input/Output data
    original_output: Any = None
    processed_output: Any = None
    context: Dict[str, Any] = field(default_factory=dict)
    
    # Feedback assessment
    feedback_type: FeedbackType = FeedbackType.MISCELLANEOUS
    confidence_score: float = 0.0  # 0.0 to 1.0
    trust_score: float = 0.0       # 0.0 to 1.0
    
    # Validation details
    validation_passed: bool = False
    validation_issues: List[Dict[str, Any]] = field(default_factory=list)
    
    # Metadata
    timestamp: datetime = field(default_factory=datetime.utcnow)
    processing_time_ms: float = 0.0
    validator_version: str = "1.0.0"
    
    # Additional data
    metadata: Dict[str, Any] = field(default_factory=dict)
    recommendations: List[str] = field(default_factory=list)


@dataclass
class ValidationResult:
    """
    Result of validation process for AI output.
    """
    is_valid: bool = False
    feedback_type: FeedbackType = FeedbackType.MISCELLANEOUS
    confidence_score: float = 0.0
    issues: List[Dict[str, Any]] = field(default_factory=list)
    recommendations: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ConfidenceAdjustment:
    """
    Configuration for confidence score adjustments based on feedback type.
    """
    correct_bonus: float = 0.05        # Boost for correct outputs
    partially_correct_penalty: float = 0.02  # Small penalty for partial correctness
    incorrect_penalty: float = 0.10    # Larger penalty for incorrect outputs
    miscellaneous_neutral: float = 0.0 # No adjustment for miscellaneous
    
    # Bounds
    min_confidence: float = 0.0
    max_confidence: float = 1.0


@dataclass
class TrustScoreConfig:
    """
    Configuration for trust score calculation and evolution.
    """
    # Weights for different feedback types
    correct_weight: float = 1.0
    partially_correct_weight: float = 0.7
    incorrect_weight: float = 0.0
    miscellaneous_weight: float = 0.5
    
    # Decay and learning rates
    decay_rate: float = 0.95           # How quickly old scores fade
    learning_rate: float = 0.1         # How quickly to adapt to new feedback
    
    # History tracking
    history_window: int = 100          # Number of recent entries to consider
    min_entries_for_trust: int = 5     # Minimum entries before trust is meaningful


@dataclass
class DomainConfig:
    """
    Configuration specific to a domain (e.g., Drone AI, TimeStamp AI).
    """
    domain_name: str
    interpreter_class: str
    matcher_class: str
    validator_class: str
    
    # Domain-specific thresholds
    confidence_threshold: float = 0.7
    trust_threshold: float = 0.8
    
    # Validation settings
    enable_real_time: bool = True
    enable_batch: bool = True
    batch_size: int = 100
    
    # Custom settings
    custom_settings: Dict[str, Any] = field(default_factory=dict)


# Utility functions for feedback type operations
def get_feedback_severity(feedback_type: FeedbackType) -> ValidationSeverity:
    """Map feedback type to validation severity."""
    mapping = {
        FeedbackType.CORRECT: ValidationSeverity.INFO,
        FeedbackType.PARTIALLY_CORRECT: ValidationSeverity.MEDIUM,
        FeedbackType.INCORRECT: ValidationSeverity.HIGH,
        FeedbackType.MISCELLANEOUS: ValidationSeverity.LOW
    }
    return mapping.get(feedback_type, ValidationSeverity.MEDIUM)


def calculate_confidence_level(score: float) -> ConfidenceLevel:
    """Convert numeric confidence score to confidence level."""
    if score >= 0.9:
        return ConfidenceLevel.VERY_HIGH
    elif score >= 0.75:
        return ConfidenceLevel.HIGH
    elif score >= 0.5:
        return ConfidenceLevel.MEDIUM
    elif score >= 0.25:
        return ConfidenceLevel.LOW
    else:
        return ConfidenceLevel.VERY_LOW


def is_feedback_positive(feedback_type: FeedbackType) -> bool:
    """Determine if feedback type is considered positive."""
    return feedback_type in [FeedbackType.CORRECT, FeedbackType.PARTIALLY_CORRECT]


def get_feedback_weight(feedback_type: FeedbackType) -> float:
    """Get weight for feedback type in scoring calculations."""
    weights = {
        FeedbackType.CORRECT: 1.0,
        FeedbackType.PARTIALLY_CORRECT: 0.7,
        FeedbackType.INCORRECT: 0.0,
        FeedbackType.MISCELLANEOUS: 0.5
    }
    return weights.get(feedback_type, 0.5)

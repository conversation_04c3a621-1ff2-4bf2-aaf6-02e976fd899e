/**
 * Real-Time Conflict Resolution Types and Interfaces
 * 
 * Advanced conflict detection for overlapping functions/classes with intelligent
 * merging algorithms and trust-based prioritization.
 */

export enum ConflictType {
  FUNCTION_OVERLAP = 'FUNCTION_OVERLAP',
  CLASS_OVERLAP = 'CLASS_OVERLAP',
  VARIABLE_CONFLICT = 'VARIABLE_CONFLICT',
  IMPORT_CONFLICT = 'IMPORT_CONFLICT',
  NAMESPACE_CONFLICT = 'NAMESPACE_CONFLICT',
  TYPE_CONFLICT = 'TYPE_CONFLICT',
  DEPENDENCY_CONFLICT = 'DEPENDENCY_CONFLICT',
  RESOURCE_CONFLICT = 'RESOURCE_CONFLICT'
}

export enum ConflictSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

export enum ResolutionStrategy {
  TRUST_BASED = 'TRUST_BASED',
  MERGE_INTELLIGENT = 'MERGE_INTELLIGENT',
  VERSION_LATEST = 'VERSION_LATEST',
  PERFORMANCE_BASED = 'PERFORMANCE_BASED',
  MANUAL_REVIEW = 'MANUAL_REVIEW',
  ROLLBACK = 'ROLLBACK'
}

export enum ConflictStatus {
  DETECTED = 'DETECTED',
  ANALYZING = 'ANALYZING',
  RESOLVING = 'RESOLVING',
  RESOLVED = 'RESOLVED',
  FAILED = 'FAILED',
  MANUAL_INTERVENTION = 'MANUAL_INTERVENTION'
}

export interface ConflictDetection {
  id: string;
  type: ConflictType;
  severity: ConflictSeverity;
  status: ConflictStatus;
  detectedAt: Date;
  resolvedAt?: Date;
  involvedAgents: string[];
  conflictingElements: ConflictingElement[];
  contextInfo: ConflictContext;
  resolutionStrategy?: ResolutionStrategy;
  resolutionResult?: ResolutionResult;
  metadata: ConflictMetadata;
}

export interface ConflictingElement {
  elementId: string;
  elementType: 'FUNCTION' | 'CLASS' | 'VARIABLE' | 'IMPORT' | 'TYPE';
  elementName: string;
  sourceAgent: string;
  sourceFile: string;
  lineNumber: number;
  columnNumber: number;
  codeSnippet: string;
  signature?: string;
  dependencies: string[];
  trustScore: number;
  lastModified: Date;
  version: string;
}

export interface ConflictContext {
  projectId: string;
  workflowId: string;
  branchName: string;
  commitHash: string;
  affectedFiles: string[];
  impactRadius: 'LOCAL' | 'MODULE' | 'PROJECT' | 'SYSTEM';
  businessCriticality: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  testCoverage: number;
  performanceImpact: number;
}

export interface ConflictMetadata {
  detectionMethod: 'STATIC_ANALYSIS' | 'RUNTIME_ANALYSIS' | 'SEMANTIC_ANALYSIS' | 'DEPENDENCY_ANALYSIS';
  confidence: number;
  similarityScore: number;
  complexityScore: number;
  riskAssessment: RiskAssessment;
  historicalData: HistoricalConflictData;
}

export interface RiskAssessment {
  breakingChangeRisk: number;
  performanceRisk: number;
  securityRisk: number;
  maintainabilityRisk: number;
  overallRisk: number;
  riskFactors: string[];
  mitigationSuggestions: string[];
}

export interface HistoricalConflictData {
  previousConflicts: number;
  resolutionSuccessRate: number;
  averageResolutionTime: number;
  commonPatterns: string[];
  agentReliability: Record<string, number>;
}

export interface ResolutionResult {
  strategy: ResolutionStrategy;
  mergedCode?: string;
  selectedVersion?: string;
  modifications: CodeModification[];
  testResults: TestResult[];
  performanceMetrics: PerformanceMetrics;
  qualityMetrics: QualityMetrics;
  approvalRequired: boolean;
  approvedBy?: string;
  rollbackPlan: RollbackPlan;
}

export interface CodeModification {
  file: string;
  operation: 'ADD' | 'MODIFY' | 'DELETE' | 'RENAME';
  lineStart: number;
  lineEnd: number;
  originalCode: string;
  modifiedCode: string;
  reason: string;
  confidence: number;
}

export interface TestResult {
  testSuite: string;
  testName: string;
  status: 'PASSED' | 'FAILED' | 'SKIPPED';
  duration: number;
  errorMessage?: string;
  coverage: number;
}

export interface PerformanceMetrics {
  executionTime: number;
  memoryUsage: number;
  cpuUsage: number;
  throughput: number;
  latency: number;
  resourceEfficiency: number;
}

export interface QualityMetrics {
  codeComplexity: number;
  maintainabilityIndex: number;
  technicalDebt: number;
  duplicateCode: number;
  testCoverage: number;
  documentationCoverage: number;
}

export interface RollbackPlan {
  rollbackId: string;
  steps: RollbackStep[];
  estimatedTime: number;
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';
  dependencies: string[];
  validationChecks: string[];
}

export interface RollbackStep {
  stepNumber: number;
  description: string;
  command: string;
  expectedResult: string;
  rollbackOnFailure: boolean;
}

export interface IntelligentMerger {
  mergeId: string;
  algorithm: MergeAlgorithm;
  confidence: number;
  mergeStrategy: MergeStrategy;
  conflictElements: ConflictingElement[];
  mergeResult: MergeResult;
  validationResults: ValidationResult[];
}

export enum MergeAlgorithm {
  SEMANTIC_MERGE = 'SEMANTIC_MERGE',
  STRUCTURAL_MERGE = 'STRUCTURAL_MERGE',
  BEHAVIORAL_MERGE = 'BEHAVIORAL_MERGE',
  TRUST_WEIGHTED_MERGE = 'TRUST_WEIGHTED_MERGE',
  ML_ASSISTED_MERGE = 'ML_ASSISTED_MERGE'
}

export enum MergeStrategy {
  PRESERVE_BOTH = 'PRESERVE_BOTH',
  COMBINE_FEATURES = 'COMBINE_FEATURES',
  SELECT_BEST = 'SELECT_BEST',
  HYBRID_APPROACH = 'HYBRID_APPROACH',
  CUSTOM_LOGIC = 'CUSTOM_LOGIC'
}

export interface MergeResult {
  success: boolean;
  mergedCode: string;
  conflictsResolved: number;
  conflictsRemaining: number;
  qualityScore: number;
  testCompatibility: boolean;
  performanceImpact: number;
  warnings: string[];
  recommendations: string[];
}

export interface ValidationResult {
  validationType: 'SYNTAX' | 'SEMANTIC' | 'FUNCTIONAL' | 'PERFORMANCE' | 'SECURITY';
  status: 'PASSED' | 'FAILED' | 'WARNING';
  message: string;
  details?: any;
  suggestions: string[];
}

export interface TrustBasedPrioritization {
  agentTrustScores: Record<string, AgentTrustScore>;
  codeQualityMetrics: Record<string, QualityMetrics>;
  historicalPerformance: Record<string, HistoricalPerformance>;
  priorityMatrix: PriorityMatrix;
  decisionRationale: string;
}

export interface AgentTrustScore {
  agentId: string;
  overallTrust: number;
  codeQuality: number;
  reliability: number;
  expertise: number;
  collaboration: number;
  recentPerformance: number;
  domainExpertise: Record<string, number>;
  lastUpdated: Date;
}

export interface HistoricalPerformance {
  agentId: string;
  totalContributions: number;
  successfulMerges: number;
  conflictResolutions: number;
  bugIntroductions: number;
  performanceImprovements: number;
  codeReviews: number;
  averageQualityScore: number;
  trendDirection: 'IMPROVING' | 'DECLINING' | 'STABLE';
}

export interface PriorityMatrix {
  trustWeight: number;
  qualityWeight: number;
  performanceWeight: number;
  recentActivityWeight: number;
  domainExpertiseWeight: number;
  collaborationWeight: number;
  finalScores: Record<string, number>;
  selectedAgent: string;
  confidence: number;
}

export interface ConflictResolutionEngine {
  engineId: string;
  version: string;
  capabilities: string[];
  supportedLanguages: string[];
  algorithms: MergeAlgorithm[];
  strategies: ResolutionStrategy[];
  configuration: EngineConfiguration;
  performance: EnginePerformance;
}

export interface EngineConfiguration {
  enableRealTimeDetection: boolean;
  enableIntelligentMerging: boolean;
  enableTrustBasedPrioritization: boolean;
  conflictThreshold: number;
  trustThreshold: number;
  qualityThreshold: number;
  autoResolutionEnabled: boolean;
  manualReviewRequired: boolean;
  rollbackOnFailure: boolean;
  maxResolutionTime: number;
}

export interface EnginePerformance {
  totalConflictsDetected: number;
  totalConflictsResolved: number;
  averageResolutionTime: number;
  successRate: number;
  falsePositiveRate: number;
  falseNegativeRate: number;
  userSatisfactionScore: number;
  systemStabilityImpact: number;
}

export interface ConflictAnalytics {
  timeRange: DateRange;
  totalConflicts: number;
  conflictsByType: Record<ConflictType, number>;
  conflictsBySeverity: Record<ConflictSeverity, number>;
  resolutionsByStrategy: Record<ResolutionStrategy, number>;
  averageResolutionTime: number;
  successRate: number;
  topConflictingAgents: AgentConflictStats[];
  conflictTrends: ConflictTrend[];
  hotspots: ConflictHotspot[];
}

export interface DateRange {
  startDate: Date;
  endDate: Date;
}

export interface AgentConflictStats {
  agentId: string;
  agentName: string;
  totalConflicts: number;
  conflictsInitiated: number;
  conflictsResolved: number;
  averageResolutionTime: number;
  trustScore: number;
  improvementTrend: 'IMPROVING' | 'DECLINING' | 'STABLE';
}

export interface ConflictTrend {
  date: Date;
  conflictCount: number;
  resolutionCount: number;
  averageResolutionTime: number;
  successRate: number;
}

export interface ConflictHotspot {
  location: string;
  conflictCount: number;
  severity: ConflictSeverity;
  commonCauses: string[];
  suggestedImprovements: string[];
  affectedAgents: string[];
}

// Error types
export class ConflictResolutionError extends Error {
  constructor(
    message: string,
    public code: string,
    public conflictId?: string,
    public details?: any
  ) {
    super(message);
    this.name = 'ConflictResolutionError';
  }
}

export class MergeError extends ConflictResolutionError {
  constructor(message: string, conflictId?: string, details?: any) {
    super(message, 'MERGE_ERROR', conflictId, details);
  }
}

export class ValidationError extends ConflictResolutionError {
  constructor(message: string, conflictId?: string, details?: any) {
    super(message, 'VALIDATION_ERROR', conflictId, details);
  }
}

// Constants
export const CONFLICT_RESOLUTION_CONSTANTS = {
  MAX_RESOLUTION_TIME: 300000, // 5 minutes
  MIN_TRUST_SCORE: 0.6,
  MIN_QUALITY_SCORE: 0.7,
  MAX_CONFLICT_COMPLEXITY: 10,
  DEFAULT_MERGE_TIMEOUT: 60000, // 1 minute
  MAX_ROLLBACK_STEPS: 20,
  CONFLICT_DETECTION_INTERVAL: 5000, // 5 seconds
  TRUST_SCORE_DECAY_RATE: 0.95,
  QUALITY_THRESHOLD_CRITICAL: 0.9,
  PERFORMANCE_IMPACT_THRESHOLD: 0.1
};

// Utility functions
export const ConflictResolutionUtils = {
  generateConflictId: (): string => {
    return `conflict_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  },

  calculateSimilarity: (code1: string, code2: string): number => {
    // Simple similarity calculation - would use more sophisticated algorithms
    const words1 = code1.toLowerCase().split(/\W+/);
    const words2 = code2.toLowerCase().split(/\W+/);
    const intersection = words1.filter(word => words2.includes(word));
    const union = [...new Set([...words1, ...words2])];
    return intersection.length / union.length;
  },

  assessConflictSeverity: (
    similarity: number,
    impactRadius: string,
    businessCriticality: string
  ): ConflictSeverity => {
    let severityScore = 0;

    // Similarity factor
    if (similarity > 0.8) severityScore += 3;
    else if (similarity > 0.6) severityScore += 2;
    else if (similarity > 0.4) severityScore += 1;

    // Impact radius factor
    switch (impactRadius) {
      case 'SYSTEM': severityScore += 3; break;
      case 'PROJECT': severityScore += 2; break;
      case 'MODULE': severityScore += 1; break;
    }

    // Business criticality factor
    switch (businessCriticality) {
      case 'CRITICAL': severityScore += 3; break;
      case 'HIGH': severityScore += 2; break;
      case 'MEDIUM': severityScore += 1; break;
    }

    if (severityScore >= 7) return ConflictSeverity.CRITICAL;
    if (severityScore >= 5) return ConflictSeverity.HIGH;
    if (severityScore >= 3) return ConflictSeverity.MEDIUM;
    return ConflictSeverity.LOW;
  },

  selectResolutionStrategy: (
    conflictType: ConflictType,
    severity: ConflictSeverity,
    trustScores: Record<string, number>
  ): ResolutionStrategy => {
    // High trust difference - use trust-based
    const trustValues = Object.values(trustScores);
    const maxTrust = Math.max(...trustValues);
    const minTrust = Math.min(...trustValues);
    
    if (maxTrust - minTrust > 0.3) {
      return ResolutionStrategy.TRUST_BASED;
    }

    // Critical severity - manual review
    if (severity === ConflictSeverity.CRITICAL) {
      return ResolutionStrategy.MANUAL_REVIEW;
    }

    // Function/class overlaps - intelligent merge
    if (conflictType === ConflictType.FUNCTION_OVERLAP || conflictType === ConflictType.CLASS_OVERLAP) {
      return ResolutionStrategy.MERGE_INTELLIGENT;
    }

    // Default to performance-based
    return ResolutionStrategy.PERFORMANCE_BASED;
  },

  calculateTrustScore: (
    codeQuality: number,
    reliability: number,
    expertise: number,
    recentPerformance: number
  ): number => {
    return (codeQuality * 0.3 + reliability * 0.3 + expertise * 0.2 + recentPerformance * 0.2);
  },

  estimateResolutionTime: (
    conflictType: ConflictType,
    severity: ConflictSeverity,
    strategy: ResolutionStrategy
  ): number => {
    let baseTime = 30000; // 30 seconds

    // Adjust for conflict type
    switch (conflictType) {
      case ConflictType.FUNCTION_OVERLAP:
      case ConflictType.CLASS_OVERLAP:
        baseTime *= 2;
        break;
      case ConflictType.DEPENDENCY_CONFLICT:
        baseTime *= 3;
        break;
    }

    // Adjust for severity
    switch (severity) {
      case ConflictSeverity.HIGH:
        baseTime *= 1.5;
        break;
      case ConflictSeverity.CRITICAL:
        baseTime *= 2;
        break;
    }

    // Adjust for strategy
    switch (strategy) {
      case ResolutionStrategy.MERGE_INTELLIGENT:
        baseTime *= 2;
        break;
      case ResolutionStrategy.MANUAL_REVIEW:
        baseTime *= 10;
        break;
    }

    return Math.min(baseTime, CONFLICT_RESOLUTION_CONSTANTS.MAX_RESOLUTION_TIME);
  }
};

#!/usr/bin/env node

/**
 * 🔒 Universal VS Code Security Scanner
 * Enterprise-grade security scanning for solo developers
 * Works with any Node.js, Python, Java, or other project types
 */

import { Command } from 'commander';
import chalk from 'chalk';
import ora from 'ora';
import inquirer from 'inquirer';
import fs from 'fs-extra';
import path from 'path';
import { execSync } from 'child_process';
import { glob } from 'glob';

class UniversalSecurityScanner {
  constructor() {
    this.program = new Command();
    this.currentDir = process.cwd();
    this.scannerDir = path.dirname(new URL(import.meta.url).pathname);
    this.results = {
      timestamp: new Date().toISOString(),
      projectPath: this.currentDir,
      projectType: 'unknown',
      summary: {
        vulnerabilities: { critical: 0, high: 0, medium: 0, low: 0 },
        licenses: { compliant: 0, nonCompliant: 0, unknown: 0 },
        dependencies: { total: 0, outdated: 0 },
        codeIssues: { security: 0, quality: 0 }
      },
      details: {},
      recommendations: []
    };
  }

  async init() {
    this.program
      .name('vscode-security-scan')
      .description('🔒 Universal Security Scanner for VS Code Projects')
      .version('1.0.0');

    this.program
      .option('-q, --quick', 'Quick security scan')
      .option('-f, --full', 'Full comprehensive scan')
      .option('-r, --report', 'Generate detailed report')
      .option('-s, --sbom', 'Generate SBOM only')
      .option('-l, --licenses', 'Check licenses only')
      .option('-v, --vulnerabilities', 'Check vulnerabilities only')
      .option('-p, --path <path>', 'Project path to scan', this.currentDir)
      .option('--interactive', 'Interactive mode');

    this.program.parse();
    const options = this.program.opts();

    if (options.path) {
      this.currentDir = path.resolve(options.path);
    }

    console.log(chalk.blue.bold('🔒 Universal VS Code Security Scanner'));
    console.log(chalk.gray('Enterprise-grade security for solo developers'));
    console.log(chalk.gray('=' .repeat(60)));
    console.log(chalk.cyan(`📁 Scanning: ${this.currentDir}`));
    console.log('');

    await this.detectProjectType();
    
    if (options.interactive) {
      await this.runInteractiveMode();
    } else if (options.quick) {
      await this.runQuickScan();
    } else if (options.full) {
      await this.runFullScan();
    } else if (options.sbom) {
      await this.generateSBOM();
    } else if (options.licenses) {
      await this.checkLicenses();
    } else if (options.vulnerabilities) {
      await this.checkVulnerabilities();
    } else if (options.report) {
      await this.generateReport();
    } else {
      await this.runDefaultScan();
    }
  }

  async detectProjectType() {
    const spinner = ora('🔍 Detecting project type...').start();
    
    try {
      // Check for different project types
      if (await fs.pathExists(path.join(this.currentDir, 'package.json'))) {
        this.results.projectType = 'Node.js';
      } else if (await fs.pathExists(path.join(this.currentDir, 'requirements.txt')) || 
                 await fs.pathExists(path.join(this.currentDir, 'pyproject.toml'))) {
        this.results.projectType = 'Python';
      } else if (await fs.pathExists(path.join(this.currentDir, 'pom.xml')) || 
                 await fs.pathExists(path.join(this.currentDir, 'build.gradle'))) {
        this.results.projectType = 'Java';
      } else if (await fs.pathExists(path.join(this.currentDir, 'Cargo.toml'))) {
        this.results.projectType = 'Rust';
      } else if (await fs.pathExists(path.join(this.currentDir, 'go.mod'))) {
        this.results.projectType = 'Go';
      } else if (await fs.pathExists(path.join(this.currentDir, 'composer.json'))) {
        this.results.projectType = 'PHP';
      } else {
        // Check for common file types
        const jsFiles = await glob('**/*.js', { cwd: this.currentDir, ignore: ['node_modules/**'] });
        const pyFiles = await glob('**/*.py', { cwd: this.currentDir });
        const javaFiles = await glob('**/*.java', { cwd: this.currentDir });
        
        if (jsFiles.length > 0) this.results.projectType = 'JavaScript';
        else if (pyFiles.length > 0) this.results.projectType = 'Python';
        else if (javaFiles.length > 0) this.results.projectType = 'Java';
        else this.results.projectType = 'Mixed/Other';
      }
      
      spinner.succeed(chalk.green(`✅ Detected: ${this.results.projectType} project`));
    } catch (error) {
      spinner.fail(chalk.red('❌ Failed to detect project type'));
      this.results.projectType = 'Unknown';
    }
  }

  async runInteractiveMode() {
    const answers = await inquirer.prompt([
      {
        type: 'checkbox',
        name: 'scanTypes',
        message: '🔍 What would you like to scan?',
        choices: [
          { name: '🛡️  Vulnerability Scan', value: 'vulnerabilities' },
          { name: '📜 License Compliance', value: 'licenses' },
          { name: '📦 Generate SBOM', value: 'sbom' },
          { name: '🔒 Code Security Analysis', value: 'code' },
          { name: '📊 Dependency Analysis', value: 'dependencies' },
          { name: '📈 Generate Report', value: 'report' }
        ],
        default: ['vulnerabilities', 'licenses', 'sbom']
      }
    ]);

    for (const scanType of answers.scanTypes) {
      switch (scanType) {
        case 'vulnerabilities':
          await this.checkVulnerabilities();
          break;
        case 'licenses':
          await this.checkLicenses();
          break;
        case 'sbom':
          await this.generateSBOM();
          break;
        case 'code':
          await this.analyzeCode();
          break;
        case 'dependencies':
          await this.analyzeDependencies();
          break;
        case 'report':
          await this.generateReport();
          break;
      }
    }
  }

  async runQuickScan() {
    console.log(chalk.yellow('⚡ Running Quick Security Scan...'));
    await this.checkVulnerabilities();
    await this.checkLicenses();
    await this.printSummary();
  }

  async runFullScan() {
    console.log(chalk.yellow('🔍 Running Full Security Scan...'));
    await this.checkVulnerabilities();
    await this.checkLicenses();
    await this.generateSBOM();
    await this.analyzeCode();
    await this.analyzeDependencies();
    await this.generateReport();
  }

  async runDefaultScan() {
    console.log(chalk.yellow('🔍 Running Default Security Scan...'));
    await this.checkVulnerabilities();
    await this.checkLicenses();
    await this.generateSBOM();
    await this.printSummary();
  }

  async checkVulnerabilities() {
    const spinner = ora('🛡️  Checking vulnerabilities...').start();
    
    try {
      if (this.results.projectType === 'Node.js' || this.results.projectType === 'JavaScript') {
        await this.checkNodeVulnerabilities();
      } else if (this.results.projectType === 'Python') {
        await this.checkPythonVulnerabilities();
      } else {
        spinner.info(chalk.yellow('⚠️  Vulnerability scanning not available for this project type'));
        return;
      }
      
      spinner.succeed(chalk.green('✅ Vulnerability check complete'));
    } catch (error) {
      spinner.fail(chalk.red(`❌ Vulnerability check failed: ${error.message}`));
    }
  }

  async checkNodeVulnerabilities() {
    try {
      // NPM Audit
      const auditResult = execSync('npm audit --json', { 
        cwd: this.currentDir, 
        encoding: 'utf8' 
      });
      const audit = JSON.parse(auditResult);
      
      this.results.details.npmAudit = audit;
      this.results.summary.vulnerabilities = {
        critical: audit.metadata?.vulnerabilities?.critical || 0,
        high: audit.metadata?.vulnerabilities?.high || 0,
        medium: audit.metadata?.vulnerabilities?.moderate || 0,
        low: audit.metadata?.vulnerabilities?.low || 0
      };

      if (audit.metadata?.vulnerabilities?.total > 0) {
        this.results.recommendations.push('Run "npm audit fix" to automatically resolve vulnerabilities');
      }
    } catch (error) {
      // npm audit returns non-zero exit code when vulnerabilities found
      try {
        const audit = JSON.parse(error.stdout || '{}');
        this.results.details.npmAudit = audit;
      } catch (parseError) {
        this.results.details.npmAudit = { error: error.message };
      }
    }
  }

  async checkPythonVulnerabilities() {
    try {
      // Safety check for Python
      const safetyResult = execSync('safety check --json', { 
        cwd: this.currentDir, 
        encoding: 'utf8' 
      });
      const safety = JSON.parse(safetyResult);
      
      this.results.details.pythonSafety = safety;
      this.results.summary.vulnerabilities.high = safety.length;
      
      if (safety.length > 0) {
        this.results.recommendations.push('Update vulnerable Python packages identified by Safety');
      }
    } catch (error) {
      this.results.details.pythonSafety = { error: 'Safety not installed or check failed' };
      this.results.recommendations.push('Install Safety: pip install safety');
    }
  }

  async checkLicenses() {
    const spinner = ora('📜 Checking license compliance...').start();
    
    try {
      if (this.results.projectType === 'Node.js' || this.results.projectType === 'JavaScript') {
        await this.checkNodeLicenses();
      } else {
        spinner.info(chalk.yellow('⚠️  License checking not available for this project type'));
        return;
      }
      
      spinner.succeed(chalk.green('✅ License check complete'));
    } catch (error) {
      spinner.fail(chalk.red(`❌ License check failed: ${error.message}`));
    }
  }

  async checkNodeLicenses() {
    try {
      const licenseResult = execSync('npx license-checker --json', { 
        cwd: this.currentDir, 
        encoding: 'utf8' 
      });
      const licenses = JSON.parse(licenseResult);
      
      this.results.details.licenses = licenses;
      
      // Analyze license compliance
      const problematicLicenses = ['GPL-3.0', 'AGPL-3.0', 'LGPL-3.0'];
      let compliant = 0, nonCompliant = 0, unknown = 0;
      
      Object.values(licenses).forEach(pkg => {
        const license = pkg.licenses;
        if (!license || license === 'UNKNOWN') {
          unknown++;
        } else if (problematicLicenses.some(prob => license.includes(prob))) {
          nonCompliant++;
        } else {
          compliant++;
        }
      });
      
      this.results.summary.licenses = { compliant, nonCompliant, unknown };
      
      if (nonCompliant > 0) {
        this.results.recommendations.push('Review packages with problematic licenses (GPL, AGPL)');
      }
      if (unknown > 0) {
        this.results.recommendations.push('Investigate packages with unknown licenses');
      }
    } catch (error) {
      this.results.details.licenses = { error: error.message };
    }
  }

  async generateSBOM() {
    const spinner = ora('📦 Generating SBOM...').start();
    
    try {
      if (this.results.projectType === 'Node.js' || this.results.projectType === 'JavaScript') {
        execSync('npx @cyclonedx/cyclonedx-npm --output-file sbom.json', { 
          cwd: this.currentDir 
        });
        execSync('npx @cyclonedx/cyclonedx-npm --output-format xml --output-file sbom.xml', { 
          cwd: this.currentDir 
        });
        
        const sbom = JSON.parse(await fs.readFile(path.join(this.currentDir, 'sbom.json'), 'utf8'));
        this.results.details.sbom = {
          format: 'CycloneDX',
          components: sbom.components?.length || 0,
          generated: true
        };
        
        spinner.succeed(chalk.green(`✅ SBOM generated (${sbom.components?.length || 0} components)`));
      } else {
        spinner.info(chalk.yellow('⚠️  SBOM generation not available for this project type'));
      }
    } catch (error) {
      spinner.fail(chalk.red(`❌ SBOM generation failed: ${error.message}`));
    }
  }

  async analyzeCode() {
    const spinner = ora('🔒 Analyzing code security...').start();
    
    try {
      if (this.results.projectType === 'Node.js' || this.results.projectType === 'JavaScript') {
        // Copy ESLint config from scanner directory
        const eslintConfig = path.join(this.scannerDir, 'configs', '.eslintrc.security.js');
        if (await fs.pathExists(eslintConfig)) {
          await fs.copy(eslintConfig, path.join(this.currentDir, '.eslintrc.security.js'));
        }
        
        const eslintResult = execSync('npx eslint . --ext .js,.ts --config .eslintrc.security.js --format json', { 
          cwd: this.currentDir, 
          encoding: 'utf8' 
        });
        const eslint = JSON.parse(eslintResult);
        
        let securityIssues = 0, qualityIssues = 0;
        eslint.forEach(file => {
          file.messages.forEach(msg => {
            if (msg.ruleId && msg.ruleId.startsWith('security/')) {
              securityIssues++;
            } else {
              qualityIssues++;
            }
          });
        });
        
        this.results.details.eslint = eslint;
        this.results.summary.codeIssues = { security: securityIssues, quality: qualityIssues };
        
        spinner.succeed(chalk.green(`✅ Code analysis complete (${securityIssues} security issues)`));
      } else {
        spinner.info(chalk.yellow('⚠️  Code analysis not available for this project type'));
      }
    } catch (error) {
      spinner.info(chalk.yellow('⚠️  Code analysis completed with warnings'));
    }
  }

  async analyzeDependencies() {
    const spinner = ora('📊 Analyzing dependencies...').start();
    
    try {
      if (this.results.projectType === 'Node.js' || this.results.projectType === 'JavaScript') {
        const packageJson = JSON.parse(await fs.readFile(path.join(this.currentDir, 'package.json'), 'utf8'));
        const totalDeps = Object.keys(packageJson.dependencies || {}).length + 
                         Object.keys(packageJson.devDependencies || {}).length;
        
        this.results.summary.dependencies.total = totalDeps;
        
        // Check for outdated packages
        try {
          const outdatedResult = execSync('npm outdated --json', { 
            cwd: this.currentDir, 
            encoding: 'utf8' 
          });
          const outdated = JSON.parse(outdatedResult);
          this.results.summary.dependencies.outdated = Object.keys(outdated).length;
        } catch (error) {
          // npm outdated returns non-zero when outdated packages found
          try {
            const outdated = JSON.parse(error.stdout || '{}');
            this.results.summary.dependencies.outdated = Object.keys(outdated).length;
          } catch (parseError) {
            this.results.summary.dependencies.outdated = 0;
          }
        }
        
        spinner.succeed(chalk.green(`✅ Dependency analysis complete (${totalDeps} total)`));
      } else {
        spinner.info(chalk.yellow('⚠️  Dependency analysis not available for this project type'));
      }
    } catch (error) {
      spinner.fail(chalk.red(`❌ Dependency analysis failed: ${error.message}`));
    }
  }

  async generateReport() {
    const spinner = ora('📊 Generating security report...').start();
    
    try {
      const reportDir = path.join(this.currentDir, 'security-reports');
      await fs.ensureDir(reportDir);
      
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const reportFile = path.join(reportDir, `security-report-${timestamp}.json`);
      const htmlReportFile = path.join(reportDir, `security-report-${timestamp}.html`);
      
      // Save JSON report
      await fs.writeFile(reportFile, JSON.stringify(this.results, null, 2));
      
      // Generate HTML report
      const htmlReport = this.generateHTMLReport();
      await fs.writeFile(htmlReportFile, htmlReport);
      
      spinner.succeed(chalk.green('✅ Security report generated'));
      console.log(chalk.cyan(`📄 JSON Report: ${reportFile}`));
      console.log(chalk.cyan(`🌐 HTML Report: ${htmlReportFile}`));
    } catch (error) {
      spinner.fail(chalk.red(`❌ Report generation failed: ${error.message}`));
    }
  }

  generateHTMLReport() {
    const { summary } = this.results;
    const totalVulns = Object.values(summary.vulnerabilities).reduce((a, b) => a + b, 0);
    const totalLicenses = Object.values(summary.licenses).reduce((a, b) => a + b, 0);
    
    return `<!DOCTYPE html>
<html>
<head>
    <title>Universal Security Report - ${this.results.timestamp}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; border-bottom: 2px solid #2c5aa0; padding-bottom: 20px; margin-bottom: 30px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .card { background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #2c5aa0; }
        .metric { font-size: 2em; font-weight: bold; color: #2c5aa0; }
        .label { color: #6c757d; font-size: 0.9em; }
        .project-info { background: #e9ecef; padding: 15px; border-radius: 8px; margin-bottom: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔒 Universal Security Report</h1>
            <p>Generated: ${new Date(this.results.timestamp).toLocaleString()}</p>
        </div>
        
        <div class="project-info">
            <h3>📁 Project Information</h3>
            <p><strong>Path:</strong> ${this.results.projectPath}</p>
            <p><strong>Type:</strong> ${this.results.projectType}</p>
            <p><strong>Scanner:</strong> Universal VS Code Security Scanner v1.0.0</p>
        </div>
        
        <div class="summary">
            <div class="card">
                <div class="metric">${totalVulns}</div>
                <div class="label">Total Vulnerabilities</div>
            </div>
            
            <div class="card">
                <div class="metric">${totalLicenses}</div>
                <div class="label">License Analysis</div>
            </div>
            
            <div class="card">
                <div class="metric">${summary.dependencies.total}</div>
                <div class="label">Dependencies</div>
            </div>
            
            <div class="card">
                <div class="metric">${summary.codeIssues.security + summary.codeIssues.quality}</div>
                <div class="label">Code Issues</div>
            </div>
        </div>
        
        <div style="margin-top: 30px; padding: 20px; background: #e9ecef; border-radius: 8px;">
            <h3>📋 Recommendations</h3>
            <ul>
                ${this.results.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                <li>🔄 Run security scans regularly (weekly recommended)</li>
                <li>📚 Review the Universal Security Scanner documentation</li>
            </ul>
        </div>
        
        <div style="margin-top: 20px; text-align: center; color: #6c757d; font-size: 0.9em;">
            <p>🔒 Universal VS Code Security Scanner - Enterprise-grade security for solo developers</p>
        </div>
    </div>
</body>
</html>`;
  }

  async printSummary() {
    console.log('');
    console.log(chalk.blue.bold('📊 Security Scan Summary'));
    console.log(chalk.gray('=' .repeat(40)));
    
    const { summary } = this.results;
    const totalVulns = Object.values(summary.vulnerabilities).reduce((a, b) => a + b, 0);
    
    if (totalVulns === 0) {
      console.log(chalk.green('✅ No vulnerabilities found!'));
    } else {
      console.log(chalk.red(`⚠️  Found ${totalVulns} vulnerabilities`));
      console.log(chalk.gray(`   Critical: ${summary.vulnerabilities.critical}, High: ${summary.vulnerabilities.high}, Medium: ${summary.vulnerabilities.medium}, Low: ${summary.vulnerabilities.low}`));
    }
    
    console.log(chalk.cyan(`📜 Licenses: ${Object.values(summary.licenses).reduce((a, b) => a + b, 0)} analyzed`));
    console.log(chalk.cyan(`📦 Dependencies: ${summary.dependencies.total} total, ${summary.dependencies.outdated} outdated`));
    console.log(chalk.cyan(`🔒 Code Issues: ${summary.codeIssues.security} security, ${summary.codeIssues.quality} quality`));
    
    if (this.results.recommendations.length > 0) {
      console.log('');
      console.log(chalk.yellow.bold('💡 Recommendations:'));
      this.results.recommendations.forEach(rec => {
        console.log(chalk.yellow(`   • ${rec}`));
      });
    }
    
    console.log('');
    console.log(chalk.green.bold('🎯 Scan Complete!'));
  }
}

// Run the scanner
const scanner = new UniversalSecurityScanner();
scanner.init().catch(console.error);

#!/usr/bin/env node

const { Command } = require('commander');
const chalk = require('chalk');
const ora = require('ora');
const inquirer = require('inquirer');
const fs = require('fs-extra');
const path = require('path');
const { exec } = require('child_process');
const { AIOrchestrator } = require('../orchestrator');
const { CrossFlowEngine } = require('../workflows/cross-flow-engine');

class WorkflowAutomation {
  constructor() {
    this.orchestrator = null;
    this.crossFlowEngine = null;
    this.workflowTemplates = {
      'cross-flow-feature': {
        name: 'Cross-Flow Feature Implementation',
        description: 'Advanced cross-flow workflow: Augment Code → Plan → Cursor/Windsurf → Tabnine → Validation',
        steps: [
          'Project Analysis (Augment Code)',
          'Plan Generation',
          'Code Generation (Cursor/Windsurf)',
          'Enhancement (Tabnine)',
          'Validation (Augment Code)',
          'Final Integration'
        ],
        tools: ['augmentCode', 'cursor', 'windsurf', 'tabnine'],
        crossFlow: true
      },
      'feature-development': {
        name: 'Feature Development',
        description: 'Complete feature development workflow',
        steps: [
          'Project Analysis',
          'Architecture Planning',
          'Code Generation',
          'Testing Setup',
          'Documentation',
          'Validation'
        ],
        tools: ['augmentCode', 'cursor', 'windsurf', 'tabnine']
      },
      'code-refactoring': {
        name: 'Code Refactoring',
        description: 'Systematic code refactoring workflow',
        steps: [
          'Code Analysis',
          'Pattern Identification',
          'Refactoring Plan',
          'Implementation',
          'Testing',
          'Validation'
        ],
        tools: ['augmentCode', 'windsurf', 'cursor']
      },
      'bug-fixing': {
        name: 'Bug Fixing',
        description: 'Systematic bug identification and fixing',
        steps: [
          'Bug Analysis',
          'Root Cause Identification',
          'Fix Implementation',
          'Testing',
          'Regression Testing'
        ],
        tools: ['augmentCode', 'cursor', 'tabnine']
      },
      'security-analysis': {
        name: 'Security Analysis',
        description: 'Comprehensive security analysis and hardening',
        steps: [
          'Vulnerability Scanning',
          'Dependency Analysis',
          'Code Security Review',
          'Fix Implementation',
          'Security Testing'
        ],
        tools: ['augmentCode', 'cursor']
      },
      'performance-optimization': {
        name: 'Performance Optimization',
        description: 'Performance analysis and optimization',
        steps: [
          'Performance Profiling',
          'Bottleneck Identification',
          'Optimization Planning',
          'Implementation',
          'Performance Testing'
        ],
        tools: ['augmentCode', 'windsurf', 'cursor']
      }
    };
  }

  async initialize() {
    if (!this.orchestrator) {
      this.orchestrator = new AIOrchestrator();
      await this.orchestrator.initialize();
      this.crossFlowEngine = new CrossFlowEngine(this.orchestrator);
    }
  }

  async runInteractiveWorkflow() {
    console.log(chalk.blue.bold('\n🤖 AI Orchestration Workflow Automation\n'));

    // Initialize orchestrator if needed
    await this.initialize();

    // Select workflow type
    const { workflowType } = await inquirer.prompt([
      {
        type: 'list',
        name: 'workflowType',
        message: 'Select a workflow to execute:',
        choices: Object.entries(this.workflowTemplates).map(([key, template]) => ({
          name: `${template.name} - ${template.description}`,
          value: key
        }))
      }
    ]);

    const workflow = this.workflowTemplates[workflowType];

    // Get workflow parameters
    const parameters = await this.getWorkflowParameters(workflowType);

    // Execute workflow (check if it's a cross-flow workflow)
    if (workflow.crossFlow) {
      await this.executeCrossFlowWorkflow(workflowType, workflow, parameters);
    } else {
      await this.executeWorkflow(workflowType, workflow, parameters);
    }
  }

  async executeCrossFlowWorkflow(workflowType, workflow, parameters) {
    console.log(chalk.green(`\n🚀 Executing ${workflow.name}\n`));

    try {
      // Prepare user request for cross-flow engine
      const userRequest = {
        description: parameters.featureDescription || parameters.bugDescription || 'Implement feature',
        projectPath: parameters.projectPath,
        multiFile: parameters.multiFile || workflowType === 'cross-flow-feature',
        includeTests: parameters.includeTests || false
      };

      // Execute the cross-flow workflow
      const result = await this.crossFlowEngine.executeFeatureImplementation(userRequest);

      // Display results
      await this.displayCrossFlowResults(result);

      // Save results
      await this.saveCrossFlowResults(result);

      return result;

    } catch (error) {
      console.error(chalk.red(`❌ Cross-flow workflow failed: ${error.message}`));
      throw error;
    }
  }

  async displayCrossFlowResults(result) {
    console.log(chalk.green.bold('\n📊 Cross-Flow Workflow Results\n'));

    console.log(chalk.white(`Workflow ID: ${result.id}`));
    console.log(chalk.white(`Duration: ${Math.round(result.duration / 1000)}s`));
    console.log(chalk.white(`Status: ${result.success ? '✅ Success' : '❌ Failed'}`));
    console.log(chalk.white(`Files Generated: ${result.results.filesGenerated}`));
    console.log(chalk.white(`AI Suggestions: ${result.results.suggestions}`));
    console.log(chalk.white(`Validation: ${result.results.validationPassed ? '✅ Passed' : '❌ Failed'}`));

    console.log(chalk.blue('\n📋 Workflow Steps:\n'));

    result.steps.forEach((step, index) => {
      const status = step.success ? chalk.green('✅') : chalk.red('❌');
      const tool = step.tool ? chalk.gray(`[${step.tool}]`) : '';
      console.log(`${status} ${step.step}. ${step.name} ${tool}`);

      if (step.output) {
        Object.entries(step.output).forEach(([key, value]) => {
          console.log(chalk.gray(`   ${key}: ${value}`));
        });
      }
    });

    console.log();
  }

  async saveCrossFlowResults(result) {
    const resultsDir = path.join(__dirname, '..', 'results', 'cross-flow');
    await fs.ensureDir(resultsDir);

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `cross-flow-${result.id}-${timestamp}.json`;
    const filepath = path.join(resultsDir, filename);

    await fs.writeJson(filepath, result, { spaces: 2 });

    console.log(chalk.blue(`📄 Cross-flow results saved to: ${filepath}`));
  }

  async getWorkflowParameters(workflowType) {
    const questions = [
      {
        type: 'input',
        name: 'projectPath',
        message: 'Project path:',
        default: process.cwd()
      }
    ];

    switch (workflowType) {
      case 'cross-flow-feature':
        questions.push(
          {
            type: 'input',
            name: 'featureDescription',
            message: 'Feature description:',
            validate: input => input.length > 0 || 'Feature description is required'
          },
          {
            type: 'confirm',
            name: 'multiFile',
            message: 'Multi-file feature?',
            default: true
          },
          {
            type: 'confirm',
            name: 'includeTests',
            message: 'Generate tests?',
            default: true
          }
        );
        break;
      case 'feature-development':
        questions.push(
          {
            type: 'input',
            name: 'featureName',
            message: 'Feature name:',
            validate: input => input.length > 0 || 'Feature name is required'
          },
          {
            type: 'input',
            name: 'featureDescription',
            message: 'Feature description:',
            validate: input => input.length > 0 || 'Feature description is required'
          },
          {
            type: 'confirm',
            name: 'multiFile',
            message: 'Multi-file feature?',
            default: true
          },
          {
            type: 'confirm',
            name: 'includeTests',
            message: 'Generate tests?',
            default: true
          }
        );
        break;

      case 'code-refactoring':
        questions.push(
          {
            type: 'input',
            name: 'targetFiles',
            message: 'Target files/directories (comma-separated):',
            filter: input => input.split(',').map(s => s.trim())
          },
          {
            type: 'list',
            name: 'refactoringType',
            message: 'Refactoring type:',
            choices: [
              'Extract Components',
              'Optimize Performance',
              'Improve Readability',
              'Update Dependencies',
              'Modernize Code'
            ]
          }
        );
        break;

      case 'bug-fixing':
        questions.push(
          {
            type: 'input',
            name: 'bugDescription',
            message: 'Bug description:',
            validate: input => input.length > 0 || 'Bug description is required'
          },
          {
            type: 'input',
            name: 'reproductionSteps',
            message: 'Reproduction steps (optional):'
          }
        );
        break;

      case 'security-analysis':
        questions.push(
          {
            type: 'checkbox',
            name: 'scanTypes',
            message: 'Select scan types:',
            choices: [
              'Dependency Vulnerabilities',
              'Code Security Issues',
              'Configuration Security',
              'API Security',
              'Authentication/Authorization'
            ],
            default: ['Dependency Vulnerabilities', 'Code Security Issues']
          }
        );
        break;

      case 'performance-optimization':
        questions.push(
          {
            type: 'checkbox',
            name: 'optimizationAreas',
            message: 'Optimization areas:',
            choices: [
              'Database Queries',
              'API Response Times',
              'Frontend Performance',
              'Memory Usage',
              'CPU Usage',
              'Bundle Size'
            ],
            default: ['API Response Times', 'Frontend Performance']
          }
        );
        break;
    }

    return await inquirer.prompt(questions);
  }

  async executeWorkflow(workflowType, workflow, parameters) {
    console.log(chalk.green(`\n🚀 Executing ${workflow.name} Workflow\n`));

    const results = {
      workflow: workflowType,
      parameters,
      steps: [],
      startTime: new Date(),
      success: true
    };

    try {
      for (let i = 0; i < workflow.steps.length; i++) {
        const step = workflow.steps[i];
        const stepResult = await this.executeWorkflowStep(workflowType, step, parameters, i + 1);
        results.steps.push(stepResult);

        if (!stepResult.success) {
          results.success = false;
          break;
        }
      }

      results.endTime = new Date();
      results.duration = results.endTime - results.startTime;

      await this.saveWorkflowResults(results);
      await this.displayWorkflowSummary(results);

    } catch (error) {
      console.error(chalk.red(`❌ Workflow failed: ${error.message}`));
      results.success = false;
      results.error = error.message;
    }

    return results;
  }

  async executeWorkflowStep(workflowType, stepName, parameters, stepNumber) {
    const spinner = ora(`Step ${stepNumber}: ${stepName}`).start();

    try {
      let result;

      switch (stepName) {
        case 'Project Analysis':
          result = await this.analyzeProject(parameters.projectPath);
          break;

        case 'Architecture Planning':
          result = await this.planArchitecture(parameters);
          break;

        case 'Code Generation':
          result = await this.generateCode(parameters);
          break;

        case 'Testing Setup':
          result = await this.setupTesting(parameters);
          break;

        case 'Documentation':
          result = await this.generateDocumentation(parameters);
          break;

        case 'Validation':
          result = await this.validateImplementation(parameters);
          break;

        case 'Code Analysis':
          result = await this.analyzeCodeQuality(parameters);
          break;

        case 'Pattern Identification':
          result = await this.identifyPatterns(parameters);
          break;

        case 'Refactoring Plan':
          result = await this.createRefactoringPlan(parameters);
          break;

        case 'Implementation':
          result = await this.implementChanges(parameters);
          break;

        case 'Testing':
          result = await this.runTests(parameters);
          break;

        case 'Bug Analysis':
          result = await this.analyzeBug(parameters);
          break;

        case 'Root Cause Identification':
          result = await this.identifyRootCause(parameters);
          break;

        case 'Fix Implementation':
          result = await this.implementFix(parameters);
          break;

        case 'Vulnerability Scanning':
          result = await this.scanVulnerabilities(parameters);
          break;

        case 'Dependency Analysis':
          result = await this.analyzeDependencies(parameters);
          break;

        case 'Code Security Review':
          result = await this.reviewCodeSecurity(parameters);
          break;

        case 'Performance Profiling':
          result = await this.profilePerformance(parameters);
          break;

        case 'Bottleneck Identification':
          result = await this.identifyBottlenecks(parameters);
          break;

        case 'Optimization Planning':
          result = await this.planOptimizations(parameters);
          break;

        default:
          result = { message: `Step ${stepName} executed`, success: true };
      }

      spinner.succeed(`Step ${stepNumber}: ${stepName} completed`);

      return {
        step: stepName,
        stepNumber,
        success: true,
        result,
        timestamp: new Date()
      };

    } catch (error) {
      spinner.fail(`Step ${stepNumber}: ${stepName} failed`);

      return {
        step: stepName,
        stepNumber,
        success: false,
        error: error.message,
        timestamp: new Date()
      };
    }
  }

  // Workflow step implementations
  async analyzeProject(projectPath) {
    return this.callOrchestrator('analyze', { projectPath });
  }

  async planArchitecture(parameters) {
    return {
      message: 'Architecture planned based on feature requirements',
      components: ['Component A', 'Component B', 'Service Layer'],
      patterns: ['MVC', 'Repository Pattern']
    };
  }

  async generateCode(parameters) {
    return this.callOrchestrator('generate', {
      description: parameters.featureDescription || parameters.bugDescription,
      context: {
        projectPath: parameters.projectPath,
        multiFile: parameters.multiFile
      }
    });
  }

  async setupTesting(parameters) {
    if (!parameters.includeTests) {
      return { message: 'Testing setup skipped', skipped: true };
    }

    return {
      message: 'Test files generated',
      testFiles: ['feature.test.js', 'integration.test.js']
    };
  }

  async generateDocumentation(parameters) {
    return {
      message: 'Documentation generated',
      files: ['README.md', 'API.md']
    };
  }

  async validateImplementation(parameters) {
    return {
      message: 'Implementation validated',
      checks: ['Syntax', 'Type Safety', 'Integration'],
      passed: true
    };
  }

  async analyzeCodeQuality(parameters) {
    return {
      message: 'Code quality analyzed',
      metrics: {
        complexity: 'Medium',
        maintainability: 'High',
        testCoverage: '85%'
      }
    };
  }

  async identifyPatterns(parameters) {
    return {
      message: 'Code patterns identified',
      patterns: ['Duplicate Code', 'Long Methods', 'Large Classes']
    };
  }

  async createRefactoringPlan(parameters) {
    return {
      message: 'Refactoring plan created',
      plan: {
        priority: 'High',
        estimatedTime: '2 hours',
        steps: ['Extract methods', 'Split classes', 'Remove duplicates']
      }
    };
  }

  async implementChanges(parameters) {
    return {
      message: 'Changes implemented',
      filesModified: parameters.targetFiles || ['file1.js', 'file2.js']
    };
  }

  async runTests(parameters) {
    return {
      message: 'Tests executed',
      results: {
        passed: 15,
        failed: 0,
        coverage: '92%'
      }
    };
  }

  async analyzeBug(parameters) {
    return {
      message: 'Bug analyzed',
      analysis: {
        severity: 'Medium',
        category: 'Logic Error',
        affectedFiles: ['component.js']
      }
    };
  }

  async identifyRootCause(parameters) {
    return {
      message: 'Root cause identified',
      rootCause: 'Null pointer exception in data processing'
    };
  }

  async implementFix(parameters) {
    return {
      message: 'Fix implemented',
      changes: ['Added null checks', 'Updated error handling']
    };
  }

  async scanVulnerabilities(parameters) {
    return {
      message: 'Vulnerability scan completed',
      vulnerabilities: {
        high: 0,
        medium: 2,
        low: 5
      }
    };
  }

  async analyzeDependencies(parameters) {
    return {
      message: 'Dependencies analyzed',
      outdated: ['package1@1.0.0', 'package2@2.1.0'],
      vulnerable: ['package3@1.5.0']
    };
  }

  async reviewCodeSecurity(parameters) {
    return {
      message: 'Code security reviewed',
      issues: ['Hardcoded secrets', 'SQL injection risk'],
      recommendations: ['Use environment variables', 'Parameterized queries']
    };
  }

  async profilePerformance(parameters) {
    return {
      message: 'Performance profiled',
      metrics: {
        responseTime: '250ms',
        memoryUsage: '45MB',
        cpuUsage: '12%'
      }
    };
  }

  async identifyBottlenecks(parameters) {
    return {
      message: 'Bottlenecks identified',
      bottlenecks: ['Database queries', 'Large bundle size', 'Unoptimized images']
    };
  }

  async planOptimizations(parameters) {
    return {
      message: 'Optimization plan created',
      optimizations: [
        'Add database indexes',
        'Implement code splitting',
        'Optimize images'
      ]
    };
  }

  async callOrchestrator(command, parameters) {
    return new Promise((resolve, reject) => {
      const orchestratorPath = path.join(__dirname, '..', 'orchestrator.js');
      const args = [orchestratorPath, command, JSON.stringify(parameters)];

      exec(`node ${args.join(' ')}`, (error, stdout, stderr) => {
        if (error) {
          reject(error);
        } else {
          try {
            resolve(JSON.parse(stdout));
          } catch (parseError) {
            resolve({ message: stdout, raw: true });
          }
        }
      });
    });
  }

  async saveWorkflowResults(results) {
    const resultsDir = path.join(__dirname, '..', 'results');
    await fs.ensureDir(resultsDir);

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `workflow-${results.workflow}-${timestamp}.json`;
    const filepath = path.join(resultsDir, filename);

    await fs.writeJson(filepath, results, { spaces: 2 });

    console.log(chalk.blue(`📄 Results saved to: ${filepath}`));
  }

  async displayWorkflowSummary(results) {
    console.log(chalk.green.bold('\n📊 Workflow Summary\n'));

    console.log(chalk.white(`Workflow: ${results.workflow}`));
    console.log(chalk.white(`Duration: ${Math.round(results.duration / 1000)}s`));
    console.log(chalk.white(`Status: ${results.success ? '✅ Success' : '❌ Failed'}`));
    console.log(chalk.white(`Steps: ${results.steps.length}`));

    console.log(chalk.blue('\n📋 Step Results:\n'));

    results.steps.forEach((step, index) => {
      const status = step.success ? chalk.green('✅') : chalk.red('❌');
      console.log(`${status} ${index + 1}. ${step.step}`);

      if (step.result && step.result.message) {
        console.log(chalk.gray(`   ${step.result.message}`));
      }

      if (!step.success && step.error) {
        console.log(chalk.red(`   Error: ${step.error}`));
      }
    });

    console.log();
  }
}

// CLI Interface
const program = new Command();

program
  .name('workflow-automation')
  .description('AI Orchestration Workflow Automation')
  .version('1.0.0');

program
  .command('interactive')
  .description('Run interactive workflow selection')
  .action(async () => {
    const automation = new WorkflowAutomation();
    await automation.runInteractiveWorkflow();
  });

program
  .command('execute')
  .description('Execute a specific workflow')
  .argument('<workflow>', 'Workflow type')
  .option('-p, --project-path <path>', 'Project path', process.cwd())
  .option('-d, --description <desc>', 'Feature/bug description')
  .option('-m, --multi-file', 'Multi-file workflow')
  .action(async (workflowType, options) => {
    const automation = new WorkflowAutomation();
    const workflow = automation.workflowTemplates[workflowType];

    if (!workflow) {
      console.error(chalk.red(`❌ Unknown workflow: ${workflowType}`));
      process.exit(1);
    }

    const parameters = {
      projectPath: options.projectPath,
      featureDescription: options.description,
      bugDescription: options.description,
      multiFile: options.multiFile
    };

    await automation.executeWorkflow(workflowType, workflow, parameters);
  });

program
  .command('list')
  .description('List available workflows')
  .action(() => {
    const automation = new WorkflowAutomation();

    console.log(chalk.blue.bold('\n🤖 Available Workflows:\n'));

    Object.entries(automation.workflowTemplates).forEach(([key, template]) => {
      console.log(chalk.green(`📋 ${template.name}`));
      console.log(chalk.white(`   Key: ${key}`));
      console.log(chalk.gray(`   Description: ${template.description}`));
      console.log(chalk.yellow(`   Tools: ${template.tools.join(', ')}`));
      console.log();
    });
  });

// Run if called directly
if (require.main === module) {
  program.parse();
}

module.exports = { WorkflowAutomation };

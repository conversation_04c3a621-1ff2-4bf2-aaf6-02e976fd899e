/**
 * Darwin Gödel Machine (DGM) - Main Entry Point
 * 
 * Self-improving AI orchestration system that evolves and optimizes
 * its own code through evolutionary algorithms and machine learning.
 */

const chalk = require('chalk');
const DGMIntegration = require('./dgm-integration');
const ConfigManager = require('./core/config-manager');

class DarwinGodelMachine {
  constructor(options = {}) {
    this.options = options;
    this.config = null;
    this.integration = null;
    this.isInitialized = false;
  }

  /**
   * Initialize the DGM system
   */
  async initialize(existingOrchestrator = null, threadMergingOrchestrator = null) {
    console.log(chalk.blue.bold(`
╔══════════════════════════════════════════════════════════════╗
║                Darwin Gödel Machine v1.0.0                  ║
║              Self-Improving AI Orchestration                ║
║                                                              ║
║  🧬 Evolutionary Code Generation                             ║
║  🤖 Self-Modifying Agents                                    ║
║  📊 Performance-Driven Evolution                             ║
║  🔒 Safety-First Architecture                                ║
╚══════════════════════════════════════════════════════════════╝
    `));

    try {
      // Initialize configuration
      this.config = new ConfigManager(this.options.configPath);
      await this.config.initialize();

      // Initialize integration layer
      this.integration = new DGMIntegration(
        existingOrchestrator,
        threadMergingOrchestrator,
        this.config
      );
      
      await this.integration.initialize();
      
      this.isInitialized = true;
      
      console.log(chalk.green.bold('🎉 Darwin Gödel Machine initialized successfully!'));
      console.log(chalk.blue('Ready to evolve and improve AI orchestration workflows.\n'));
      
      return this;
      
    } catch (error) {
      console.error(chalk.red.bold(`❌ DGM Initialization failed: ${error.message}`));
      throw error;
    }
  }

  /**
   * Start the DGM system
   */
  async start(options = {}) {
    if (!this.isInitialized) {
      throw new Error('DGM not initialized. Call initialize() first.');
    }

    console.log(chalk.blue('🚀 Starting Darwin Gödel Machine...'));
    
    try {
      // Start the integration layer
      await this.integration.start();
      
      // Start evolution if requested
      if (options.startEvolution !== false) {
        console.log(chalk.blue('🧬 Starting evolutionary process...'));
        await this.integration.dgmEngine.startEvolution(options.evolutionOptions || {});
      }
      
      console.log(chalk.green.bold('✅ Darwin Gödel Machine is now running!'));
      
      // Display quick start guide
      this.displayQuickStart();
      
      return this;
      
    } catch (error) {
      console.error(chalk.red.bold(`❌ DGM Start failed: ${error.message}`));
      throw error;
    }
  }

  /**
   * Stop the DGM system
   */
  async stop() {
    console.log(chalk.yellow('🛑 Stopping Darwin Gödel Machine...'));
    
    try {
      if (this.integration) {
        await this.integration.stop();
      }
      
      console.log(chalk.yellow('✅ Darwin Gödel Machine stopped'));
      
    } catch (error) {
      console.error(chalk.red(`❌ DGM Stop failed: ${error.message}`));
      throw error;
    }
  }

  /**
   * Get DGM status
   */
  getStatus() {
    if (!this.isInitialized) {
      return { status: 'not_initialized' };
    }

    return {
      status: 'initialized',
      isRunning: this.integration?.isRunning || false,
      integration: this.integration?.getIntegrationStats(),
      config: this.config?.getAll()
    };
  }

  /**
   * Display quick start guide
   */
  displayQuickStart() {
    const integration = this.integration;
    
    console.log(chalk.cyan.bold('\n📚 Quick Start Guide:'));
    console.log(chalk.white('━'.repeat(60)));
    
    if (integration.webDashboard) {
      console.log(chalk.green(`🌐 Web Dashboard: http://localhost:${integration.webDashboard.port}`));
      console.log(chalk.gray('   Monitor evolution progress and manage agents'));
    }
    
    if (integration.apiServer) {
      console.log(chalk.green(`🚀 API Server: http://localhost:${integration.apiServer.port}`));
      console.log(chalk.gray('   RESTful API for external integration'));
    }
    
    if (integration.cliInterface) {
      console.log(chalk.green('🖥️  CLI Interface: Available'));
      console.log(chalk.gray('   Use dgm.startCLI() to access command-line interface'));
    }
    
    console.log(chalk.white('━'.repeat(60)));
    console.log(chalk.cyan('🧬 Evolution Status:'));
    
    const dgmEngine = integration.dgmEngine;
    if (dgmEngine.isRunning) {
      console.log(chalk.green(`   ✅ Running - Generation ${dgmEngine.currentGeneration}`));
      console.log(chalk.gray(`   Population: ${dgmEngine.agentManager.currentPopulation.length} agents`));
    } else {
      console.log(chalk.yellow('   ⏸️  Paused - Use dgm.startEvolution() to begin'));
    }
    
    console.log(chalk.white('━'.repeat(60)));
    console.log(chalk.cyan('📊 Integration Mode:'));
    console.log(chalk.blue(`   Mode: ${integration.integrationMode}`));
    console.log(chalk.blue(`   Migration Phase: ${integration.migrationPhase}`));
    
    console.log(chalk.white('━'.repeat(60)));
    console.log(chalk.cyan('🔧 Available Commands:'));
    console.log(chalk.white('   dgm.startEvolution()     - Start evolutionary process'));
    console.log(chalk.white('   dgm.stopEvolution()      - Stop evolutionary process'));
    console.log(chalk.white('   dgm.startCLI()           - Start CLI interface'));
    console.log(chalk.white('   dgm.getStatus()          - Get system status'));
    console.log(chalk.white('   dgm.getStats()           - Get performance statistics'));
    console.log(chalk.white('   dgm.stop()               - Stop DGM system'));
    
    console.log(chalk.white('━'.repeat(60)));
    console.log();
  }

  /**
   * Start evolution process
   */
  async startEvolution(options = {}) {
    if (!this.isInitialized) {
      throw new Error('DGM not initialized');
    }

    return await this.integration.dgmEngine.startEvolution(options);
  }

  /**
   * Stop evolution process
   */
  async stopEvolution() {
    if (!this.isInitialized) {
      throw new Error('DGM not initialized');
    }

    this.integration.dgmEngine.isRunning = false;
    console.log(chalk.yellow('🧬 Evolution process stopped'));
  }

  /**
   * Start CLI interface
   */
  async startCLI() {
    if (!this.isInitialized) {
      throw new Error('DGM not initialized');
    }

    if (!this.integration.cliInterface) {
      throw new Error('CLI interface not available');
    }

    return await this.integration.cliInterface.start();
  }

  /**
   * Get performance statistics
   */
  getStats() {
    if (!this.isInitialized) {
      return null;
    }

    return {
      integration: this.integration.getIntegrationStats(),
      dgm: {
        currentGeneration: this.integration.dgmEngine.currentGeneration,
        populationSize: this.integration.dgmEngine.agentManager.currentPopulation.length,
        evolutionHistory: this.integration.dgmEngine.evolutionHistory.length,
        isRunning: this.integration.dgmEngine.isRunning
      }
    };
  }

  /**
   * Get current best agent
   */
  async getBestAgent() {
    if (!this.isInitialized) {
      throw new Error('DGM not initialized');
    }

    const population = await this.integration.dgmEngine.agentManager.getCurrentPopulation();
    
    if (population.length === 0) {
      return null;
    }

    return population.reduce((best, current) => 
      current.fitness > best.fitness ? current : best
    );
  }

  /**
   * Export DGM data
   */
  async exportData(type = 'all') {
    if (!this.isInitialized) {
      throw new Error('DGM not initialized');
    }

    const exportData = {
      timestamp: new Date(),
      version: '1.0.0',
      type
    };

    switch (type) {
      case 'agents':
        exportData.agents = await this.integration.dgmEngine.agentManager.getCurrentPopulation();
        break;
      
      case 'archive':
        exportData.archive = await this.integration.dgmEngine.archive.getArchiveStats();
        break;
      
      case 'metrics':
        exportData.metrics = this.integration.dgmEngine.metricsCollector.getMetricsStats();
        break;
      
      case 'genealogy':
        exportData.genealogy = this.integration.dgmEngine.archive.genealogy.getGenealogyStats();
        break;
      
      case 'all':
      default:
        exportData.agents = await this.integration.dgmEngine.agentManager.getCurrentPopulation();
        exportData.archive = await this.integration.dgmEngine.archive.getArchiveStats();
        exportData.metrics = this.integration.dgmEngine.metricsCollector.getMetricsStats();
        exportData.genealogy = this.integration.dgmEngine.archive.genealogy.getGenealogyStats();
        exportData.evolution = this.integration.dgmEngine.evolutionHistory;
        exportData.config = this.config.getAll();
        break;
    }

    return exportData;
  }

  /**
   * Create a new DGM instance with existing orchestrators
   */
  static async create(existingOrchestrator = null, threadMergingOrchestrator = null, options = {}) {
    const dgm = new DarwinGodelMachine(options);
    await dgm.initialize(existingOrchestrator, threadMergingOrchestrator);
    return dgm;
  }

  /**
   * Quick start method for immediate use
   */
  static async quickStart(options = {}) {
    console.log(chalk.blue.bold('🚀 DGM Quick Start...'));
    
    const dgm = new DarwinGodelMachine(options);
    await dgm.initialize();
    await dgm.start({
      startEvolution: options.startEvolution !== false,
      evolutionOptions: options.evolutionOptions
    });
    
    return dgm;
  }
}

// Export the main class and utilities
module.exports = {
  DarwinGodelMachine,
  DGMIntegration,
  ConfigManager,
  
  // Convenience exports
  create: DarwinGodelMachine.create,
  quickStart: DarwinGodelMachine.quickStart
};

// If run directly, start quick start mode
if (require.main === module) {
  (async () => {
    try {
      const dgm = await DarwinGodelMachine.quickStart({
        startEvolution: true,
        evolutionOptions: {
          maxGenerations: 50,
          targetFitness: 0.9
        }
      });
      
      // Keep the process running
      process.on('SIGINT', async () => {
        console.log(chalk.yellow('\n🛑 Shutting down DGM...'));
        await dgm.stop();
        process.exit(0);
      });
      
    } catch (error) {
      console.error(chalk.red.bold(`❌ DGM Quick Start failed: ${error.message}`));
      process.exit(1);
    }
  })();
}

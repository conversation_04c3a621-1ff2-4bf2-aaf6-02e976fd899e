/**
 * Seamless UI Integration for Stamply
 * Creates beautiful, unobtrusive eco-impact displays
 */

/**
 * Create and inject the seamless Stamply footer
 * @param {object} data - Impact and timestamp data
 * @param {Element} messageElement - The AI message element
 * @returns {Element} The created footer element
 */
export function createStamplyFooter(data, messageElement) {
  // Remove any existing Stamply footer
  const existingFooter = messageElement.querySelector('.stamply-footer');
  if (existingFooter) {
    existingFooter.remove();
  }

  const footer = document.createElement('div');
  footer.className = 'stamply-footer';
  
  // Format timestamp
  const timestamp = new Date(data.timestamp).toLocaleString('en-US', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    timeZone: 'UTC',
    timeZoneName: 'short'
  });

  // Calculate eco-level (1-5 leaves)
  const ecoLevel = calculateEcoLevel(data.impact);
  const ecoLeaves = '🍃'.repeat(ecoLevel.level) + '🍂'.repeat(5 - ecoLevel.level);

  // Format energy and water values
  const energy = formatEnergyValue(data.impact.total.energy.value);
  const water = formatWaterValue(data.impact.total.water.value);

  // Create the footer HTML
  footer.innerHTML = `
    <div class="stamply-divider">──────────────────────────────────────────────</div>
    <div class="stamply-info">
      <div class="stamply-row">
        <span class="stamply-timestamp">🕓 ${timestamp}</span>
        <span class="stamply-hash">🔐 SHA-256: ${data.hash.substring(0, 4)}...${data.hash.substring(-4)}</span>
      </div>
      <div class="stamply-row">
        <span class="stamply-eco">🌿 Eco-Level: ${ecoLevel.level}/5 Leaves ${ecoLeaves}</span>
        <span class="stamply-impact">(${energy} · ${water})</span>
      </div>
      <div class="stamply-footer-link">
        <span class="stamply-powered">Powered by Stamply</span>
        <span class="stamply-model">${data.model.model || 'Auto-detected'}</span>
      </div>
    </div>
  `;

  // Add CSS styles
  addStamplyStyles();

  return footer;
}

/**
 * Calculate eco-level based on impact data
 * @param {object} impact - Impact calculation data
 * @returns {object} Eco-level information
 */
function calculateEcoLevel(impact) {
  const totalEnergy = impact.total.energy.value;
  const totalWater = impact.total.water.value;
  
  // Calculate combined impact score (normalized)
  const energyScore = totalEnergy * 1000; // Convert to Wh
  const waterScore = totalWater * 10; // Scale water impact
  const combinedScore = energyScore + waterScore;

  // Determine eco-level (1 = most efficient, 5 = least efficient)
  let level;
  let description;

  if (combinedScore <= 2) {
    level = 1;
    description = 'Excellent efficiency';
  } else if (combinedScore <= 5) {
    level = 2;
    description = 'Good efficiency';
  } else if (combinedScore <= 10) {
    level = 3;
    description = 'Moderate efficiency';
  } else if (combinedScore <= 20) {
    level = 4;
    description = 'Higher impact';
  } else {
    level = 5;
    description = 'High impact';
  }

  return { level, description, score: combinedScore };
}

/**
 * Format energy value for display
 * @param {number} value - Energy value in kWh
 * @returns {string} Formatted energy string
 */
function formatEnergyValue(value) {
  if (value < 0.001) {
    return `${(value * 1000000).toFixed(1)} µWh`;
  } else if (value < 1) {
    return `${(value * 1000).toFixed(1)} Wh`;
  } else {
    return `${value.toFixed(2)} kWh`;
  }
}

/**
 * Format water value for display
 * @param {number} value - Water value in liters
 * @returns {string} Formatted water string
 */
function formatWaterValue(value) {
  if (value < 0.001) {
    return `${(value * 1000000).toFixed(0)} µL`;
  } else if (value < 1) {
    return `${(value * 1000).toFixed(1)} mL`;
  } else {
    return `${value.toFixed(1)} L`;
  }
}

/**
 * Add Stamply CSS styles to the page
 */
function addStamplyStyles() {
  // Check if styles already exist
  if (document.getElementById('stamply-styles')) {
    return;
  }

  const style = document.createElement('style');
  style.id = 'stamply-styles';
  style.textContent = `
    .stamply-footer {
      margin-top: 16px;
      padding: 12px 0;
      font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
      font-size: 11px;
      line-height: 1.4;
      color: #6b7280;
      border-top: 1px solid #e5e7eb;
      background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
      border-radius: 0 0 8px 8px;
      user-select: none;
      transition: all 0.2s ease;
    }

    .stamply-footer:hover {
      background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
      color: #4b5563;
    }

    .stamply-divider {
      text-align: center;
      color: #d1d5db;
      margin-bottom: 8px;
      font-size: 10px;
    }

    .stamply-info {
      padding: 0 16px;
    }

    .stamply-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 4px;
    }

    .stamply-row:last-child {
      margin-bottom: 0;
    }

    .stamply-timestamp {
      font-weight: 500;
      color: #374151;
    }

    .stamply-hash {
      font-family: 'SF Mono', monospace;
      background: #f3f4f6;
      padding: 2px 6px;
      border-radius: 4px;
      font-size: 10px;
    }

    .stamply-eco {
      font-weight: 600;
      color: #059669;
    }

    .stamply-impact {
      font-weight: 500;
      color: #7c3aed;
    }

    .stamply-footer-link {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 8px;
      padding-top: 8px;
      border-top: 1px solid #e5e7eb;
    }

    .stamply-powered {
      font-weight: 500;
      color: #4f46e5;
    }

    .stamply-model {
      background: #ddd6fe;
      color: #5b21b6;
      padding: 2px 8px;
      border-radius: 12px;
      font-size: 10px;
      font-weight: 500;
    }

    /* Platform-specific adjustments */
    [data-message-author-role="assistant"] .stamply-footer,
    .assistant-message .stamply-footer,
    .model-response .stamply-footer {
      margin-top: 12px;
    }

    /* Dark mode support */
    @media (prefers-color-scheme: dark) {
      .stamply-footer {
        background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
        color: #9ca3af;
        border-top-color: #374151;
      }

      .stamply-footer:hover {
        background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
        color: #d1d5db;
      }

      .stamply-divider {
        color: #4b5563;
      }

      .stamply-timestamp {
        color: #e5e7eb;
      }

      .stamply-hash {
        background: #374151;
        color: #d1d5db;
      }

      .stamply-eco {
        color: #10b981;
      }

      .stamply-impact {
        color: #8b5cf6;
      }

      .stamply-footer-link {
        border-top-color: #374151;
      }

      .stamply-powered {
        color: #6366f1;
      }

      .stamply-model {
        background: #4c1d95;
        color: #c4b5fd;
      }
    }

    /* Animation for new footers */
    .stamply-footer.stamply-new {
      animation: stamplyFadeIn 0.3s ease-out;
    }

    @keyframes stamplyFadeIn {
      from {
        opacity: 0;
        transform: translateY(10px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    /* Responsive design */
    @media (max-width: 768px) {
      .stamply-footer {
        font-size: 10px;
      }

      .stamply-info {
        padding: 0 12px;
      }

      .stamply-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 2px;
      }

      .stamply-footer-link {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
      }
    }
  `;

  document.head.appendChild(style);
}

/**
 * Platform-specific message element detection
 * @param {string} platform - The AI platform
 * @returns {NodeList} AI message elements
 */
export function getAIMessageElements(platform) {
  const selectors = {
    chatgpt: '[data-message-author-role="assistant"]',
    claude: '.assistant-message, [data-role="assistant"]',
    gemini: '.model-response, [data-response-role="model"]'
  };

  const selector = selectors[platform] || '.ai-message';
  return document.querySelectorAll(selector);
}

/**
 * Inject Stamply footer into AI message
 * @param {Element} messageElement - The AI message element
 * @param {object} data - Stamply data (impact, timestamp, etc.)
 */
export function injectStamplyFooter(messageElement, data) {
  const footer = createStamplyFooter(data, messageElement);
  footer.classList.add('stamply-new');
  
  // Find the best insertion point
  const insertionPoint = findInsertionPoint(messageElement);
  insertionPoint.appendChild(footer);

  // Remove animation class after animation completes
  setTimeout(() => {
    footer.classList.remove('stamply-new');
  }, 300);
}

/**
 * Find the best insertion point for the footer
 * @param {Element} messageElement - The AI message element
 * @returns {Element} The element to append the footer to
 */
function findInsertionPoint(messageElement) {
  // Look for content wrapper
  const contentWrapper = messageElement.querySelector('.message-content, .content, .response-content');
  if (contentWrapper) {
    return contentWrapper;
  }

  // Fallback to the message element itself
  return messageElement;
}

/**
 * Versioned Workflow Management Types and Interfaces
 * 
 * Comprehensive workflow tracking system with unique IDs, agent assignments,
 * test suites, and outcome metrics to support A/B testing and rollback capabilities.
 */

export enum WorkflowStatus {
  DRAFT = 'DRAFT',
  ACTIVE = 'ACTIVE',
  PAUSED = 'PAUSED',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED',
  ARCHIVED = 'ARCHIVED'
}

export enum WorkflowType {
  CODE_GENERATION = 'CODE_GENERATION',
  CODE_REVIEW = 'CODE_REVIEW',
  TESTING = 'TESTING',
  DEPLOYMENT = 'DEPLOYMENT',
  OPTIMIZATION = 'OPTIMIZATION',
  REFACTORING = 'REFACTORING',
  DOCUMENTATION = 'DOCUMENTATION',
  ANALYSIS = 'ANALYSIS'
}

export enum TaskStatus {
  PENDING = 'PENDING',
  ASSIGNED = 'ASSIGNED',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  SKIPPED = 'SKIPPED',
  CANCELLED = 'CANCELLED'
}

export enum TaskPriority {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

export enum ExperimentType {
  AB_TEST = 'AB_TEST',
  MULTIVARIATE = 'MULTIVARIATE',
  CANARY = 'CANARY',
  BLUE_GREEN = 'BLUE_GREEN',
  FEATURE_FLAG = 'FEATURE_FLAG'
}

export interface VersionedWorkflow {
  id: string;
  name: string;
  description: string;
  type: WorkflowType;
  version: string;
  status: WorkflowStatus;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  ownerId: string;
  parentWorkflowId?: string;
  branchName?: string;
  tags: string[];
  metadata: WorkflowMetadata;
  configuration: WorkflowConfiguration;
  tasks: WorkflowTask[];
  dependencies: WorkflowDependency[];
  testSuites: TestSuite[];
  experiments: WorkflowExperiment[];
  metrics: WorkflowMetrics;
  rollbackPlan: RollbackPlan;
  approvals: WorkflowApproval[];
}

export interface WorkflowMetadata {
  projectId: string;
  repositoryUrl?: string;
  commitHash?: string;
  environment: 'development' | 'staging' | 'production';
  estimatedDuration: number;
  actualDuration?: number;
  complexity: 'LOW' | 'MEDIUM' | 'HIGH' | 'VERY_HIGH';
  businessValue: number; // 1-10 scale
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  complianceRequirements: string[];
  customFields: Record<string, any>;
}

export interface WorkflowConfiguration {
  maxConcurrentTasks: number;
  timeoutMs: number;
  retryPolicy: RetryPolicy;
  notificationSettings: NotificationSettings;
  resourceLimits: ResourceLimits;
  securitySettings: SecuritySettings;
  qualityGates: QualityGate[];
  autoRollbackEnabled: boolean;
  requiresApproval: boolean;
  parallelExecution: boolean;
}

export interface RetryPolicy {
  maxRetries: number;
  retryDelay: number;
  backoffMultiplier: number;
  retryableErrors: string[];
  nonRetryableErrors: string[];
}

export interface NotificationSettings {
  onStart: boolean;
  onComplete: boolean;
  onFailure: boolean;
  onApprovalRequired: boolean;
  channels: NotificationChannel[];
  recipients: string[];
}

export interface NotificationChannel {
  type: 'EMAIL' | 'SLACK' | 'WEBHOOK' | 'SMS';
  endpoint: string;
  enabled: boolean;
  conditions: string[];
}

export interface ResourceLimits {
  maxMemoryMB: number;
  maxCpuPercent: number;
  maxDiskMB: number;
  maxNetworkMBps: number;
  maxExecutionTime: number;
}

export interface SecuritySettings {
  requiresAuthentication: boolean;
  allowedRoles: string[];
  encryptionRequired: boolean;
  auditLogging: boolean;
  dataClassification: 'PUBLIC' | 'INTERNAL' | 'CONFIDENTIAL' | 'RESTRICTED';
  complianceFrameworks: string[];
}

export interface QualityGate {
  id: string;
  name: string;
  description: string;
  conditions: QualityCondition[];
  blocking: boolean;
  order: number;
}

export interface QualityCondition {
  metric: string;
  operator: 'GT' | 'GTE' | 'LT' | 'LTE' | 'EQ' | 'NEQ';
  threshold: number;
  required: boolean;
}

export interface WorkflowTask {
  id: string;
  name: string;
  description: string;
  type: string;
  status: TaskStatus;
  priority: TaskPriority;
  assignedAgentId?: string;
  assignedAt?: Date;
  startedAt?: Date;
  completedAt?: Date;
  estimatedDuration: number;
  actualDuration?: number;
  dependencies: string[]; // Task IDs
  inputs: TaskInput[];
  outputs: TaskOutput[];
  configuration: TaskConfiguration;
  metrics: TaskMetrics;
  logs: TaskLog[];
  artifacts: TaskArtifact[];
  retryCount: number;
  maxRetries: number;
}

export interface TaskInput {
  name: string;
  type: string;
  value: any;
  required: boolean;
  validation?: InputValidation;
}

export interface InputValidation {
  pattern?: string;
  minLength?: number;
  maxLength?: number;
  minValue?: number;
  maxValue?: number;
  allowedValues?: any[];
}

export interface TaskOutput {
  name: string;
  type: string;
  value: any;
  timestamp: Date;
  validation?: OutputValidation;
}

export interface OutputValidation {
  schema?: any;
  required: boolean;
  format?: string;
}

export interface TaskConfiguration {
  timeoutMs: number;
  retryPolicy: RetryPolicy;
  resourceLimits: ResourceLimits;
  environment: Record<string, string>;
  parameters: Record<string, any>;
  capabilities: string[];
  constraints: TaskConstraint[];
}

export interface TaskConstraint {
  type: 'RESOURCE' | 'TIME' | 'DEPENDENCY' | 'SECURITY' | 'QUALITY';
  condition: string;
  value: any;
  enforced: boolean;
}

export interface TaskMetrics {
  executionTime: number;
  memoryUsage: number;
  cpuUsage: number;
  diskUsage: number;
  networkUsage: number;
  errorCount: number;
  warningCount: number;
  qualityScore: number;
  performanceScore: number;
  customMetrics: Record<string, number>;
}

export interface TaskLog {
  id: string;
  timestamp: Date;
  level: 'DEBUG' | 'INFO' | 'WARN' | 'ERROR';
  message: string;
  source: string;
  metadata?: Record<string, any>;
}

export interface TaskArtifact {
  id: string;
  name: string;
  type: string;
  path: string;
  size: number;
  checksum: string;
  createdAt: Date;
  metadata: Record<string, any>;
}

export interface WorkflowDependency {
  id: string;
  type: 'WORKFLOW' | 'RESOURCE' | 'SERVICE' | 'DATA';
  name: string;
  version?: string;
  required: boolean;
  status: 'AVAILABLE' | 'UNAVAILABLE' | 'PENDING';
  healthCheck?: HealthCheck;
}

export interface HealthCheck {
  endpoint: string;
  method: 'GET' | 'POST' | 'HEAD';
  expectedStatus: number;
  timeout: number;
  interval: number;
  retries: number;
}

export interface TestSuite {
  id: string;
  name: string;
  description: string;
  type: 'UNIT' | 'INTEGRATION' | 'E2E' | 'PERFORMANCE' | 'SECURITY' | 'ACCEPTANCE';
  status: 'PENDING' | 'RUNNING' | 'PASSED' | 'FAILED' | 'SKIPPED';
  configuration: TestConfiguration;
  testCases: TestCase[];
  results: TestResults;
  coverage: TestCoverage;
  reports: TestReport[];
}

export interface TestConfiguration {
  framework: string;
  version: string;
  environment: Record<string, string>;
  parameters: Record<string, any>;
  timeout: number;
  parallel: boolean;
  retries: number;
}

export interface TestCase {
  id: string;
  name: string;
  description: string;
  status: 'PENDING' | 'RUNNING' | 'PASSED' | 'FAILED' | 'SKIPPED';
  duration: number;
  startedAt?: Date;
  completedAt?: Date;
  assertions: TestAssertion[];
  steps: TestStep[];
  data: TestData[];
  tags: string[];
}

export interface TestAssertion {
  id: string;
  description: string;
  expected: any;
  actual: any;
  passed: boolean;
  message?: string;
}

export interface TestStep {
  id: string;
  name: string;
  action: string;
  parameters: Record<string, any>;
  expected: any;
  actual: any;
  status: 'PENDING' | 'RUNNING' | 'PASSED' | 'FAILED' | 'SKIPPED';
  duration: number;
}

export interface TestData {
  name: string;
  value: any;
  type: string;
  source: 'STATIC' | 'GENERATED' | 'EXTERNAL';
}

export interface TestResults {
  totalTests: number;
  passedTests: number;
  failedTests: number;
  skippedTests: number;
  duration: number;
  startedAt: Date;
  completedAt?: Date;
  successRate: number;
  trends: TestTrend[];
}

export interface TestTrend {
  date: Date;
  totalTests: number;
  passedTests: number;
  failedTests: number;
  successRate: number;
  duration: number;
}

export interface TestCoverage {
  lines: CoverageMetric;
  functions: CoverageMetric;
  branches: CoverageMetric;
  statements: CoverageMetric;
  overall: number;
  files: FileCoverage[];
}

export interface CoverageMetric {
  total: number;
  covered: number;
  percentage: number;
}

export interface FileCoverage {
  file: string;
  lines: CoverageMetric;
  functions: CoverageMetric;
  branches: CoverageMetric;
  statements: CoverageMetric;
}

export interface TestReport {
  id: string;
  type: 'JUNIT' | 'HTML' | 'JSON' | 'XML' | 'PDF';
  path: string;
  generatedAt: Date;
  size: number;
  format: string;
}

export interface WorkflowExperiment {
  id: string;
  name: string;
  description: string;
  type: ExperimentType;
  status: 'DRAFT' | 'RUNNING' | 'COMPLETED' | 'CANCELLED';
  startedAt?: Date;
  completedAt?: Date;
  configuration: ExperimentConfiguration;
  variants: ExperimentVariant[];
  metrics: ExperimentMetrics;
  results: ExperimentResults;
}

export interface ExperimentConfiguration {
  trafficSplit: Record<string, number>; // variant -> percentage
  duration: number;
  successCriteria: SuccessCriteria[];
  rollbackCriteria: RollbackCriteria[];
  sampleSize: number;
  confidenceLevel: number;
  statisticalPower: number;
}

export interface SuccessCriteria {
  metric: string;
  operator: 'GT' | 'GTE' | 'LT' | 'LTE';
  threshold: number;
  required: boolean;
}

export interface RollbackCriteria {
  metric: string;
  operator: 'GT' | 'GTE' | 'LT' | 'LTE';
  threshold: number;
  immediate: boolean;
}

export interface ExperimentVariant {
  id: string;
  name: string;
  description: string;
  configuration: Record<string, any>;
  trafficPercentage: number;
  isControl: boolean;
  workflowId: string;
}

export interface ExperimentMetrics {
  primaryMetric: string;
  secondaryMetrics: string[];
  customMetrics: Record<string, MetricDefinition>;
  collectionInterval: number;
  retentionPeriod: number;
}

export interface MetricDefinition {
  name: string;
  type: 'COUNTER' | 'GAUGE' | 'HISTOGRAM' | 'TIMER';
  unit: string;
  description: string;
  aggregation: 'SUM' | 'AVG' | 'MIN' | 'MAX' | 'COUNT';
}

export interface ExperimentResults {
  winner?: string;
  confidence: number;
  pValue: number;
  effectSize: number;
  variantResults: VariantResult[];
  recommendations: string[];
  statisticalSignificance: boolean;
}

export interface VariantResult {
  variantId: string;
  sampleSize: number;
  conversionRate: number;
  metrics: Record<string, number>;
  confidenceInterval: ConfidenceInterval;
  performance: VariantPerformance;
}

export interface ConfidenceInterval {
  lower: number;
  upper: number;
  level: number;
}

export interface VariantPerformance {
  averageResponseTime: number;
  errorRate: number;
  throughput: number;
  resourceUtilization: number;
  userSatisfaction: number;
}

export interface WorkflowMetrics {
  executionTime: number;
  totalTasks: number;
  completedTasks: number;
  failedTasks: number;
  skippedTasks: number;
  successRate: number;
  averageTaskDuration: number;
  resourceUtilization: ResourceUtilization;
  qualityMetrics: QualityMetrics;
  businessMetrics: BusinessMetrics;
  trends: WorkflowTrend[];
}

export interface ResourceUtilization {
  cpu: UtilizationMetric;
  memory: UtilizationMetric;
  disk: UtilizationMetric;
  network: UtilizationMetric;
}

export interface UtilizationMetric {
  average: number;
  peak: number;
  efficiency: number;
  cost: number;
}

export interface QualityMetrics {
  codeQuality: number;
  testCoverage: number;
  bugDensity: number;
  technicalDebt: number;
  maintainabilityIndex: number;
  securityScore: number;
}

export interface BusinessMetrics {
  timeToMarket: number;
  costEfficiency: number;
  customerSatisfaction: number;
  businessValue: number;
  roi: number;
  riskReduction: number;
}

export interface WorkflowTrend {
  date: Date;
  executionTime: number;
  successRate: number;
  resourceCost: number;
  qualityScore: number;
  businessValue: number;
}

export interface RollbackPlan {
  id: string;
  version: string;
  description: string;
  triggers: RollbackTrigger[];
  steps: RollbackStep[];
  validations: RollbackValidation[];
  estimatedTime: number;
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  approvalRequired: boolean;
  automated: boolean;
}

export interface RollbackTrigger {
  type: 'MANUAL' | 'AUTOMATIC' | 'THRESHOLD' | 'ERROR' | 'TIME';
  condition: string;
  threshold?: number;
  enabled: boolean;
}

export interface RollbackStep {
  id: string;
  order: number;
  name: string;
  description: string;
  action: string;
  parameters: Record<string, any>;
  timeout: number;
  retries: number;
  rollbackOnFailure: boolean;
  validationRequired: boolean;
}

export interface RollbackValidation {
  id: string;
  name: string;
  type: 'HEALTH_CHECK' | 'SMOKE_TEST' | 'INTEGRATION_TEST' | 'MANUAL_VERIFICATION';
  criteria: string;
  timeout: number;
  required: boolean;
}

export interface WorkflowApproval {
  id: string;
  type: 'START' | 'DEPLOY' | 'ROLLBACK' | 'EXPERIMENT' | 'QUALITY_GATE';
  status: 'PENDING' | 'APPROVED' | 'REJECTED' | 'EXPIRED';
  requestedBy: string;
  requestedAt: Date;
  approvedBy?: string;
  approvedAt?: Date;
  reason?: string;
  comments: ApprovalComment[];
  expiresAt?: Date;
}

export interface ApprovalComment {
  id: string;
  author: string;
  timestamp: Date;
  message: string;
  type: 'COMMENT' | 'APPROVAL' | 'REJECTION' | 'REQUEST_CHANGES';
}

// Analytics and Reporting
export interface WorkflowAnalytics {
  timeRange: DateRange;
  totalWorkflows: number;
  workflowsByStatus: Record<WorkflowStatus, number>;
  workflowsByType: Record<WorkflowType, number>;
  averageExecutionTime: number;
  successRate: number;
  resourceEfficiency: number;
  costAnalysis: CostAnalysis;
  performanceTrends: PerformanceTrend[];
  topPerformingWorkflows: WorkflowPerformance[];
  bottlenecks: Bottleneck[];
  recommendations: Recommendation[];
}

export interface DateRange {
  startDate: Date;
  endDate: Date;
}

export interface CostAnalysis {
  totalCost: number;
  costByResource: Record<string, number>;
  costByWorkflow: Record<string, number>;
  costTrend: CostTrend[];
  optimization: CostOptimization[];
}

export interface CostTrend {
  date: Date;
  cost: number;
  resourceUsage: number;
  efficiency: number;
}

export interface CostOptimization {
  area: string;
  currentCost: number;
  optimizedCost: number;
  savings: number;
  effort: 'LOW' | 'MEDIUM' | 'HIGH';
  impact: 'LOW' | 'MEDIUM' | 'HIGH';
}

export interface PerformanceTrend {
  date: Date;
  averageExecutionTime: number;
  successRate: number;
  throughput: number;
  resourceUtilization: number;
}

export interface WorkflowPerformance {
  workflowId: string;
  name: string;
  executionTime: number;
  successRate: number;
  resourceEfficiency: number;
  businessValue: number;
  score: number;
}

export interface Bottleneck {
  type: 'RESOURCE' | 'DEPENDENCY' | 'APPROVAL' | 'QUALITY_GATE' | 'AGENT';
  location: string;
  impact: number;
  frequency: number;
  averageDelay: number;
  suggestions: string[];
}

export interface Recommendation {
  type: 'PERFORMANCE' | 'COST' | 'QUALITY' | 'RELIABILITY' | 'SECURITY';
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  title: string;
  description: string;
  expectedBenefit: string;
  effort: 'LOW' | 'MEDIUM' | 'HIGH';
  implementation: string[];
}

// Error types
export class WorkflowError extends Error {
  constructor(
    message: string,
    public code: string,
    public workflowId?: string,
    public details?: any
  ) {
    super(message);
    this.name = 'WorkflowError';
  }
}

export class TaskError extends WorkflowError {
  constructor(message: string, workflowId?: string, public taskId?: string, details?: any) {
    super(message, 'TASK_ERROR', workflowId, details);
  }
}

export class ExperimentError extends WorkflowError {
  constructor(message: string, workflowId?: string, public experimentId?: string, details?: any) {
    super(message, 'EXPERIMENT_ERROR', workflowId, details);
  }
}

// Constants
export const WORKFLOW_CONSTANTS = {
  MAX_WORKFLOW_DURATION: 86400000, // 24 hours
  MAX_TASK_DURATION: 3600000, // 1 hour
  MAX_CONCURRENT_TASKS: 100,
  MAX_RETRY_ATTEMPTS: 5,
  DEFAULT_TIMEOUT: 300000, // 5 minutes
  HEALTH_CHECK_INTERVAL: 30000, // 30 seconds
  METRICS_COLLECTION_INTERVAL: 60000, // 1 minute
  EXPERIMENT_MIN_SAMPLE_SIZE: 100,
  EXPERIMENT_MIN_DURATION: 3600000, // 1 hour
  ROLLBACK_TIMEOUT: 600000, // 10 minutes
  APPROVAL_EXPIRY: 86400000 // 24 hours
};

// Utility functions
export const WorkflowUtils = {
  generateWorkflowId: (): string => {
    return `workflow_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  },

  generateTaskId: (): string => {
    return `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  },

  generateExperimentId: (): string => {
    return `exp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  },

  calculateSuccessRate: (completed: number, failed: number): number => {
    const total = completed + failed;
    return total > 0 ? (completed / total) * 100 : 0;
  },

  estimateWorkflowDuration: (tasks: WorkflowTask[]): number => {
    // Simple estimation based on task dependencies and durations
    const criticalPath = WorkflowUtils.findCriticalPath(tasks);
    return criticalPath.reduce((total, taskId) => {
      const task = tasks.find(t => t.id === taskId);
      return total + (task?.estimatedDuration || 0);
    }, 0);
  },

  findCriticalPath: (tasks: WorkflowTask[]): string[] => {
    // Simplified critical path calculation
    // In real implementation, would use proper CPM algorithm
    return tasks
      .sort((a, b) => b.estimatedDuration - a.estimatedDuration)
      .slice(0, Math.ceil(tasks.length / 2))
      .map(t => t.id);
  },

  validateWorkflow: (workflow: VersionedWorkflow): string[] => {
    const errors: string[] = [];

    if (!workflow.name || workflow.name.trim().length === 0) {
      errors.push('Workflow name is required');
    }

    if (workflow.tasks.length === 0) {
      errors.push('Workflow must have at least one task');
    }

    // Check for circular dependencies
    if (WorkflowUtils.hasCircularDependencies(workflow.tasks)) {
      errors.push('Workflow has circular task dependencies');
    }

    // Validate task configurations
    for (const task of workflow.tasks) {
      if (task.estimatedDuration <= 0) {
        errors.push(`Task ${task.name} must have positive estimated duration`);
      }

      if (task.dependencies.some(depId => !workflow.tasks.find(t => t.id === depId))) {
        errors.push(`Task ${task.name} has invalid dependencies`);
      }
    }

    return errors;
  },

  hasCircularDependencies: (tasks: WorkflowTask[]): boolean => {
    // Simplified cycle detection
    const visited = new Set<string>();
    const recursionStack = new Set<string>();

    const hasCycle = (taskId: string): boolean => {
      if (recursionStack.has(taskId)) return true;
      if (visited.has(taskId)) return false;

      visited.add(taskId);
      recursionStack.add(taskId);

      const task = tasks.find(t => t.id === taskId);
      if (task) {
        for (const depId of task.dependencies) {
          if (hasCycle(depId)) return true;
        }
      }

      recursionStack.delete(taskId);
      return false;
    };

    return tasks.some(task => hasCycle(task.id));
  },

  calculateResourceCost: (utilization: ResourceUtilization, duration: number): number => {
    // Simplified cost calculation
    const cpuCost = utilization.cpu.average * 0.001; // $0.001 per CPU hour
    const memoryCost = utilization.memory.average * 0.0001; // $0.0001 per MB hour
    const diskCost = utilization.disk.average * 0.00001; // $0.00001 per MB hour
    const networkCost = utilization.network.average * 0.0001; // $0.0001 per MB

    const hourlyRate = cpuCost + memoryCost + diskCost + networkCost;
    const hours = duration / 3600000; // Convert ms to hours

    return hourlyRate * hours;
  }
};

import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import { Tunnel } from '../../types'
import { tunnelApi } from '../../services/api'

interface TunnelState {
  tunnels: Tunnel[]
  isLoading: boolean
  error: string | null
}

const initialState: TunnelState = {
  tunnels: [],
  isLoading: false,
  error: null,
}

export const fetchTunnels = createAsyncThunk(
  'tunnel/fetchAll',
  async () => {
    const response = await tunnelApi.getAll()
    return response.data
  }
)

const tunnelSlice = createSlice({
  name: 'tunnel',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchTunnels.fulfilled, (state, action) => {
        state.tunnels = action.payload
      })
  },
})

export const { clearError } = tunnelSlice.actions
export default tunnelSlice.reducer

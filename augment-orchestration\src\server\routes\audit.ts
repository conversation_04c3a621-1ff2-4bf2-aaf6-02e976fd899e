import { Router } from 'express';
import { PrismaClient } from '@prisma/client';
import { asyncHand<PERSON> } from '../middleware/errorHandler';
import { AuthenticatedRequest } from '../middleware/auth';

const router = Router();
const prisma = new PrismaClient();

// Get audit logs
router.get('/', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 50;
  const entityType = req.query.entityType as string;
  const action = req.query.action as string;

  const skip = (page - 1) * limit;
  const where: any = {};

  if (entityType) {
    where.entityType = entityType;
  }

  if (action) {
    where.action = { contains: action, mode: 'insensitive' };
  }

  const [logs, total] = await Promise.all([
    prisma.auditLog.findMany({
      where,
      skip,
      take: limit,
      include: {
        user: {
          select: {
            id: true,
            username: true,
            email: true,
          },
        },
      },
      orderBy: {
        timestamp: 'desc',
      },
    }),
    prisma.auditLog.count({ where }),
  ]);

  res.json({
    success: true,
    data: logs,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
    },
  });
}));

export { router as auditRoutes };

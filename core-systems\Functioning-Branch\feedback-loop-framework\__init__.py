"""
Universal Dual-Purpose Feedback Loop Framework

A comprehensive feedback validation system supporting both Drone AI and TimeStamp AI
domains with modular architecture, standardized feedback types, and dynamic domain creation.

Key Features:
- Dual-purpose architecture for Drone AI and TimeStamp AI
- Modular, pluggable components (interpreters, matchers, validators)
- Standardized feedback types (Correct, Partially Correct, Incorrect, Miscellaneous)
- Dynamic domain creation and learning capabilities
- Cross-domain validation and optimization
- Developer-only logging with comprehensive monitoring
- Real-time and batch validation support

Drone AI Domains:
- Search and Rescue (clothing, broken environment, personal items)
- Species Tracking (bird identification with GPS logging)
- Mining Ore Detection (LIDAR + AI for ore classification)
- Real Estate/Construction (3D mapping + structural assessment)

TimeStamp AI Domains:
- LLM Validation (AI model output verification)
- Environmental Impact (sustainability and eco-impact assessment)
- EcoStamp (digital trust and provenance tracking)

Example Usage:
    from feedback_loop_framework import create_enhanced_engine
    
    # Create enhanced feedback engine
    engine = create_enhanced_engine({
        'auto_domain_creation': True,
        'domain_learning_enabled': True
    })
    
    # Process drone AI feedback
    result = engine.process_output(
        domain="drone_ai",
        raw_output={
            'detections': [{'class': 'person', 'confidence': 0.92}],
            'mission_type': 'search_rescue'
        },
        context={'drone_id': 'SAR_001', 'mission_id': 'M_2024_001'}
    )
    
    print(f"Valid: {result.is_valid}")
    print(f"Confidence: {result.confidence_score}")
"""

__version__ = "1.0.0"
__author__ = "Universal Feedback Loop Team"
__email__ = "<EMAIL>"
__description__ = "Universal Dual-Purpose Feedback Loop Framework for Drone AI and TimeStamp AI"

# Core exports
from .core.enhanced_feedback_engine import (
    EnhancedFeedbackEngine,
    create_enhanced_engine,
    FeedbackType,
    ValidationResult,
    DomainType
)
from .core.domain_manager import (
    DomainManager,
    DomainConfig,
    DomainMetrics,
    create_domain_manager
)
from .core.confidence_model import (
    ConfidenceModel,
    ConfidenceScore,
    create_confidence_model
)
from .core.trust_calculator import (
    TrustCalculator,
    TrustScore,
    create_trust_calculator
)

# Domain-specific exports
from .domains.drone_ai import (
    DroneAIDomainHandler,
    SearchRescueDomain,
    SpeciesTrackingDomain,
    MiningOreDomain,
    RealEstateConstructionDomain
)
from .domains.timestamp_ai import (
    TimeStampAIDomainHandler,
    LLMValidationDomain,
    EnvironmentalImpactDomain,
    EcoStampDomain
)

# Utility functions
def create_complete_feedback_system(config=None):
    """
    Create a complete feedback system with all domains enabled.
    
    Args:
        config: Optional configuration dictionary
        
    Returns:
        Configured EnhancedFeedbackEngine with all domains
    """
    default_config = {
        'auto_domain_creation': True,
        'domain_learning_enabled': True,
        'cross_domain_learning': True,
        'real_time_validation': True,
        
        # Drone AI configuration
        'drone_ai': {
            'search_rescue': {
                'enabled': True,
                'confidence_threshold': 0.8,
                'categories': ['clothing', 'broken_environment', 'personal_items'],
                'side_pocket_enabled': True
            },
            'species_tracking': {
                'enabled': True,
                'confidence_threshold': 0.85,
                'gps_logging': True,
                'species_database': 'comprehensive'
            },
            'mining_ore': {
                'enabled': True,
                'confidence_threshold': 0.9,
                'lidar_integration': True,
                'ore_classification': 'advanced'
            },
            'real_estate_construction': {
                'enabled': True,
                'confidence_threshold': 0.85,
                '3d_mapping': True,
                'structural_assessment': True
            }
        },
        
        # TimeStamp AI configuration
        'timestamp_ai': {
            'llm_validation': {
                'enabled': True,
                'confidence_threshold': 0.8,
                'model_types': ['gpt', 'claude', 'gemini'],
                'validation_methods': ['consistency', 'accuracy', 'relevance']
            },
            'environmental_impact': {
                'enabled': True,
                'confidence_threshold': 0.85,
                'impact_categories': ['carbon', 'water', 'waste', 'energy'],
                'sustainability_metrics': True
            },
            'ecostamp': {
                'enabled': True,
                'confidence_threshold': 0.9,
                'provenance_tracking': True,
                'digital_trust': True,
                'blockchain_integration': True
            }
        },
        
        # System configuration
        'logging': {
            'level': 'INFO',
            'developer_only': True,
            'comprehensive_monitoring': True
        },
        'performance': {
            'batch_processing': True,
            'parallel_validation': True,
            'caching_enabled': True
        }
    }
    
    if config:
        # Deep merge configuration
        def deep_merge(base, override):
            for key, value in override.items():
                if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                    deep_merge(base[key], value)
                else:
                    base[key] = value
        
        deep_merge(default_config, config)
    
    return create_enhanced_engine(default_config)


def quick_start_feedback_system():
    """
    Quick start feedback system with basic configuration.
    
    Returns:
        Ready-to-use EnhancedFeedbackEngine
    """
    config = {
        'auto_domain_creation': True,
        'domain_learning_enabled': True,
        'drone_ai': {
            'search_rescue': {'enabled': True}
        },
        'timestamp_ai': {
            'ecostamp': {'enabled': True}
        }
    }
    
    engine = create_enhanced_engine(config)
    
    print("🚀 Universal Feedback Loop Framework - Quick Start")
    print("✅ Enhanced feedback engine initialized")
    print(f"📊 Available domains: {len(engine.domain_manager.get_active_domains())}")
    
    return engine


def get_supported_domains():
    """
    Get list of all supported domains and their capabilities.
    
    Returns:
        Dictionary of supported domains
    """
    return {
        'drone_ai': {
            'search_rescue': {
                'description': 'Search and rescue mission validation',
                'categories': ['clothing', 'broken_environment', 'personal_items'],
                'features': ['side_pocket_error_handling', 'post_mission_retraining']
            },
            'species_tracking': {
                'description': 'Bird and wildlife identification with GPS',
                'features': ['gps_logging', 'species_database', 'migration_tracking']
            },
            'mining_ore': {
                'description': 'LIDAR + AI ore detection and classification',
                'features': ['lidar_integration', 'ore_classification', 'geological_mapping']
            },
            'real_estate_construction': {
                'description': '3D mapping and structural assessment',
                'features': ['3d_mapping', 'structural_analysis', 'safety_assessment']
            }
        },
        'timestamp_ai': {
            'llm_validation': {
                'description': 'AI model output verification and validation',
                'features': ['consistency_checking', 'accuracy_validation', 'bias_detection']
            },
            'environmental_impact': {
                'description': 'Sustainability and eco-impact assessment',
                'features': ['carbon_tracking', 'water_usage', 'waste_analysis', 'energy_efficiency']
            },
            'ecostamp': {
                'description': 'Digital trust and provenance tracking',
                'features': ['provenance_tracking', 'digital_certificates', 'blockchain_integration']
            }
        }
    }


def get_framework_capabilities():
    """
    Get comprehensive list of framework capabilities.
    
    Returns:
        Dictionary of framework capabilities
    """
    return {
        'architecture': {
            'dual_purpose': True,
            'modular_design': True,
            'pluggable_components': True,
            'dynamic_domain_creation': True,
            'cross_domain_learning': True
        },
        'feedback_types': {
            'correct': 'Validation passed with high confidence',
            'partially_correct': 'Validation passed with moderate confidence',
            'incorrect': 'Validation failed',
            'miscellaneous': 'Special cases and edge conditions'
        },
        'validation_modes': {
            'real_time': True,
            'batch_processing': True,
            'parallel_validation': True,
            'cross_validation': True
        },
        'learning_capabilities': {
            'domain_adaptation': True,
            'performance_optimization': True,
            'pattern_recognition': True,
            'continuous_improvement': True
        },
        'monitoring': {
            'developer_logging': True,
            'performance_metrics': True,
            'confidence_tracking': True,
            'error_analysis': True
        }
    }


# Package metadata
__all__ = [
    # Core classes
    'EnhancedFeedbackEngine',
    'create_enhanced_engine',
    'FeedbackType',
    'ValidationResult',
    'DomainType',
    'DomainManager',
    'DomainConfig',
    'DomainMetrics',
    'ConfidenceModel',
    'ConfidenceScore',
    'TrustCalculator',
    'TrustScore',
    
    # Domain handlers
    'DroneAIDomainHandler',
    'SearchRescueDomain',
    'SpeciesTrackingDomain',
    'MiningOreDomain',
    'RealEstateConstructionDomain',
    'TimeStampAIDomainHandler',
    'LLMValidationDomain',
    'EnvironmentalImpactDomain',
    'EcoStampDomain',
    
    # Utility functions
    'create_complete_feedback_system',
    'quick_start_feedback_system',
    'get_supported_domains',
    'get_framework_capabilities',
    'create_domain_manager',
    'create_confidence_model',
    'create_trust_calculator'
]

/**
 * Configuration Manager
 * 
 * Manages all configuration for the meta-orchestration system:
 * - Role assignments and fallback chains
 * - Adapter configurations
 * - Workflow definitions
 * - IDE integration settings
 * - Security and compliance settings
 * - Real-time configuration sync across IDEs
 */

const EventEmitter = require('events');
const fs = require('fs').promises;
const path = require('path');
const yaml = require('yaml');
const chalk = require('chalk');
const chokidar = require('chokidar');

class ConfigManager extends EventEmitter {
  constructor(metaOrchestrator) {
    super();
    this.metaOrchestrator = metaOrchestrator;
    
    // Configuration file paths
    this.configDir = path.join(process.cwd(), 'ai-orchestration', 'config');
    this.configFiles = {
      main: path.join(this.configDir, 'meta-orchestration.yaml'),
      roles: path.join(this.configDir, 'role-assignments.yaml'),
      adapters: path.join(this.configDir, 'adapters.yaml'),
      workflows: path.join(this.configDir, 'workflows.yaml'),
      ide: path.join(this.configDir, 'ide-integration.yaml'),
      security: path.join(this.configDir, 'security.yaml')
    };
    
    // Project-level shared config file
    this.sharedConfigFile = path.join(process.cwd(), '.ai-orchestration.yaml');
    
    // Configuration cache
    this.config = {
      main: {},
      roles: {},
      adapters: {},
      workflows: {},
      ide: {},
      security: {}
    };
    
    // File watchers for real-time sync
    this.watchers = new Map();
    
    // Default configurations
    this.defaultConfigs = {
      main: {
        version: '1.0.0',
        metaOrchestration: {
          enabled: true,
          fallbackEnabled: true,
          deduplicationEnabled: true,
          contextSharingEnabled: true,
          adaptiveOptimization: true
        },
        logging: {
          level: 'info',
          file: 'logs/meta-orchestration.log',
          console: true,
          structured: true
        },
        performance: {
          caching: {
            enabled: true,
            ttl: 3600000,
            maxSize: '100MB'
          },
          parallelExecution: {
            enabled: true,
            maxConcurrent: 4
          }
        }
      },
      
      roles: {
        assignments: {
          analyzer: {
            primary: 'augment-code',
            fallbacks: ['continue', 'ollama', 'superagi']
          },
          generator: {
            primary: 'cursor',
            fallbacks: ['windsurf', 'github-copilot', 'cline', 'aider']
          },
          completer: {
            primary: 'tabnine',
            fallbacks: ['github-copilot', 'cursor', 'continue']
          },
          validator: {
            primary: 'qodo',
            fallbacks: ['augment-code', 'windsurf', 'autogen']
          },
          documenter: {
            primary: 'cursor',
            fallbacks: ['windsurf', 'github-copilot', 'aider', 'ollama']
          }
        },
        optimization: {
          enabled: true,
          performanceBasedReordering: true,
          automaticFailover: true,
          circuitBreakerEnabled: true
        }
      },
      
      ide: {
        vscode: {
          enabled: true,
          extensionId: 'meta-orchestration',
          autoActivate: true,
          showStatusBar: true,
          showNotifications: true
        },
        jetbrains: {
          enabled: false,
          pluginId: 'meta-orchestration',
          autoActivate: true
        },
        vim: {
          enabled: false,
          pluginPath: '~/.vim/pack/meta-orchestration'
        },
        sync: {
          enabled: true,
          realTime: true,
          conflictResolution: 'merge'
        }
      },
      
      security: {
        encryption: {
          enabled: true,
          algorithm: 'aes-256-gcm',
          keyRotation: true
        },
        authentication: {
          required: false,
          method: 'api-key'
        },
        compliance: {
          soc2: true,
          gdpr: true,
          hipaa: false
        },
        ipProtection: {
          enabled: true,
          noTraining: true,
          localProcessing: true
        }
      }
    };
  }
  
  async loadConfig() {
    try {
      console.log(chalk.blue('📋 Loading configuration...'));
      
      // Ensure config directory exists
      await this.ensureConfigDirectory();
      
      // Load all configuration files
      await this.loadAllConfigs();
      
      // Load shared project config
      await this.loadSharedConfig();
      
      // Setup file watchers for real-time sync
      this.setupFileWatchers();
      
      console.log(chalk.green('✅ Configuration loaded successfully'));
      
    } catch (error) {
      console.error(chalk.red('❌ Failed to load configuration:'), error);
      throw error;
    }
  }
  
  async ensureConfigDirectory() {
    try {
      await fs.mkdir(this.configDir, { recursive: true });
      
      // Create logs directory
      const logsDir = path.join(process.cwd(), 'logs');
      await fs.mkdir(logsDir, { recursive: true });
      
    } catch (error) {
      console.error(chalk.red('❌ Failed to create config directory:'), error);
      throw error;
    }
  }
  
  async loadAllConfigs() {
    for (const [configType, configFile] of Object.entries(this.configFiles)) {
      try {
        await this.loadConfigFile(configType, configFile);
      } catch (error) {
        console.warn(chalk.yellow(`⚠️ Failed to load ${configType} config, using defaults:`, error.message));
        this.config[configType] = this.defaultConfigs[configType] || {};
        
        // Save default config
        await this.saveConfigFile(configType, configFile);
      }
    }
  }
  
  async loadConfigFile(configType, configFile) {
    try {
      const configData = await fs.readFile(configFile, 'utf8');
      
      let parsedConfig;
      if (configFile.endsWith('.yaml') || configFile.endsWith('.yml')) {
        parsedConfig = yaml.parse(configData);
      } else {
        parsedConfig = JSON.parse(configData);
      }
      
      // Merge with defaults
      this.config[configType] = this.mergeConfigs(this.defaultConfigs[configType] || {}, parsedConfig);
      
      console.log(chalk.green(`✅ Loaded ${configType} configuration`));
      
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, use defaults
        this.config[configType] = this.defaultConfigs[configType] || {};
        await this.saveConfigFile(configType, configFile);
      } else {
        throw error;
      }
    }
  }
  
  async saveConfigFile(configType, configFile) {
    try {
      const configData = yaml.stringify(this.config[configType], { indent: 2 });
      await fs.writeFile(configFile, configData, 'utf8');
      
      console.log(chalk.green(`💾 Saved ${configType} configuration`));
      
    } catch (error) {
      console.error(chalk.red(`❌ Failed to save ${configType} configuration:`, error.message));
      throw error;
    }
  }
  
  async loadSharedConfig() {
    try {
      const sharedConfigData = await fs.readFile(this.sharedConfigFile, 'utf8');
      const sharedConfig = yaml.parse(sharedConfigData);
      
      // Merge shared config with existing config
      this.mergeSharedConfig(sharedConfig);
      
      console.log(chalk.green('✅ Loaded shared project configuration'));
      
    } catch (error) {
      if (error.code === 'ENOENT') {
        // Create default shared config
        await this.createDefaultSharedConfig();
      } else {
        console.warn(chalk.yellow('⚠️ Failed to load shared config:', error.message));
      }
    }
  }
  
  async createDefaultSharedConfig() {
    const defaultSharedConfig = {
      version: '1.0.0',
      project: {
        name: path.basename(process.cwd()),
        type: 'auto-detect',
        language: 'auto-detect',
        framework: 'auto-detect'
      },
      roles: this.config.roles.assignments || this.defaultConfigs.roles.assignments,
      preferences: {
        skipDocumentation: false,
        skipValidation: false,
        skipCompletion: false,
        preferredComplexity: 'medium'
      },
      ide: {
        sync: true,
        notifications: true,
        statusBar: true
      }
    };
    
    const configData = yaml.stringify(defaultSharedConfig, { indent: 2 });
    await fs.writeFile(this.sharedConfigFile, configData, 'utf8');
    
    console.log(chalk.green('✅ Created default shared configuration'));
  }
  
  mergeSharedConfig(sharedConfig) {
    // Merge role assignments from shared config
    if (sharedConfig.roles) {
      this.config.roles.assignments = { ...this.config.roles.assignments, ...sharedConfig.roles };
    }
    
    // Merge IDE preferences
    if (sharedConfig.ide) {
      this.config.ide = this.mergeConfigs(this.config.ide, sharedConfig.ide);
    }
    
    // Store project preferences
    this.config.project = sharedConfig.project || {};
    this.config.preferences = sharedConfig.preferences || {};
  }
  
  setupFileWatchers() {
    // Watch all config files for changes
    for (const [configType, configFile] of Object.entries(this.configFiles)) {
      const watcher = chokidar.watch(configFile, {
        persistent: true,
        ignoreInitial: true
      });
      
      watcher.on('change', async () => {
        try {
          console.log(chalk.blue(`🔄 Reloading ${configType} configuration...`));
          await this.loadConfigFile(configType, configFile);
          this.emit('configChanged', { type: configType, config: this.config[configType] });
        } catch (error) {
          console.error(chalk.red(`❌ Failed to reload ${configType} config:`, error.message));
        }
      });
      
      this.watchers.set(configType, watcher);
    }
    
    // Watch shared config file
    const sharedWatcher = chokidar.watch(this.sharedConfigFile, {
      persistent: true,
      ignoreInitial: true
    });
    
    sharedWatcher.on('change', async () => {
      try {
        console.log(chalk.blue('🔄 Reloading shared configuration...'));
        await this.loadSharedConfig();
        this.emit('sharedConfigChanged', this.config);
      } catch (error) {
        console.error(chalk.red('❌ Failed to reload shared config:'), error.message);
      }
    });
    
    this.watchers.set('shared', sharedWatcher);
    
    console.log(chalk.green('👁️ File watchers setup for real-time config sync'));
  }
  
  mergeConfigs(defaultConfig, userConfig) {
    const merged = { ...defaultConfig };
    
    for (const [key, value] of Object.entries(userConfig)) {
      if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
        merged[key] = this.mergeConfigs(merged[key] || {}, value);
      } else {
        merged[key] = value;
      }
    }
    
    return merged;
  }
  
  // Public methods for accessing configuration
  
  getMainConfig() {
    return this.config.main;
  }
  
  getRoleAssignments() {
    return this.config.roles.assignments;
  }
  
  getAdapterConfigs() {
    return this.config.adapters;
  }
  
  getWorkflowConfigs() {
    return this.config.workflows;
  }
  
  getIDEConfig() {
    return this.config.ide;
  }
  
  getSecurityConfig() {
    return this.config.security;
  }
  
  getProjectConfig() {
    return this.config.project || {};
  }
  
  getPreferences() {
    return this.config.preferences || {};
  }
  
  async saveRoleAssignment(role, assignment) {
    if (!this.config.roles.assignments) {
      this.config.roles.assignments = {};
    }
    
    this.config.roles.assignments[role] = assignment;
    
    // Save to both local and shared config
    await this.saveConfigFile('roles', this.configFiles.roles);
    await this.updateSharedConfig({ roles: this.config.roles.assignments });
    
    this.emit('roleAssignmentChanged', { role, assignment });
  }
  
  async saveAdapterConfig(adapterId, config) {
    if (!this.config.adapters) {
      this.config.adapters = {};
    }
    
    this.config.adapters[adapterId] = config;
    await this.saveConfigFile('adapters', this.configFiles.adapters);
    
    this.emit('adapterConfigChanged', { adapterId, config });
  }
  
  async saveCustomWorkflow(workflowId, workflow) {
    if (!this.config.workflows.custom) {
      this.config.workflows.custom = {};
    }
    
    this.config.workflows.custom[workflowId] = workflow;
    await this.saveConfigFile('workflows', this.configFiles.workflows);
    
    this.emit('workflowConfigChanged', { workflowId, workflow });
  }
  
  async updateSharedConfig(updates) {
    try {
      // Load current shared config
      let sharedConfig = {};
      try {
        const sharedConfigData = await fs.readFile(this.sharedConfigFile, 'utf8');
        sharedConfig = yaml.parse(sharedConfigData);
      } catch (error) {
        // File doesn't exist, start with empty config
      }
      
      // Merge updates
      const updatedConfig = this.mergeConfigs(sharedConfig, updates);
      
      // Save updated config
      const configData = yaml.stringify(updatedConfig, { indent: 2 });
      await fs.writeFile(this.sharedConfigFile, configData, 'utf8');
      
      this.emit('sharedConfigUpdated', updatedConfig);
      
    } catch (error) {
      console.error(chalk.red('❌ Failed to update shared config:'), error.message);
      throw error;
    }
  }
  
  async updateIDEConfig(ideType, config) {
    if (!this.config.ide[ideType]) {
      this.config.ide[ideType] = {};
    }
    
    this.config.ide[ideType] = { ...this.config.ide[ideType], ...config };
    await this.saveConfigFile('ide', this.configFiles.ide);
    
    this.emit('ideConfigChanged', { ideType, config: this.config.ide[ideType] });
  }
  
  async updateSecurityConfig(updates) {
    this.config.security = this.mergeConfigs(this.config.security, updates);
    await this.saveConfigFile('security', this.configFiles.security);
    
    this.emit('securityConfigChanged', this.config.security);
  }
  
  getCustomWorkflows() {
    return this.config.workflows.custom || {};
  }
  
  getConfigValue(path, defaultValue = null) {
    const keys = path.split('.');
    let current = this.config;
    
    for (const key of keys) {
      if (current && typeof current === 'object' && key in current) {
        current = current[key];
      } else {
        return defaultValue;
      }
    }
    
    return current;
  }
  
  async setConfigValue(path, value) {
    const keys = path.split('.');
    const configType = keys[0];
    
    if (!this.config[configType]) {
      this.config[configType] = {};
    }
    
    let current = this.config[configType];
    for (let i = 1; i < keys.length - 1; i++) {
      if (!current[keys[i]]) {
        current[keys[i]] = {};
      }
      current = current[keys[i]];
    }
    
    current[keys[keys.length - 1]] = value;
    
    // Save the affected config file
    if (this.configFiles[configType]) {
      await this.saveConfigFile(configType, this.configFiles[configType]);
    }
    
    this.emit('configValueChanged', { path, value });
  }
  
  exportConfig() {
    return {
      timestamp: Date.now(),
      version: this.config.main.version || '1.0.0',
      config: JSON.parse(JSON.stringify(this.config))
    };
  }
  
  async importConfig(configData) {
    try {
      if (configData.config) {
        this.config = configData.config;
        
        // Save all config files
        for (const [configType, configFile] of Object.entries(this.configFiles)) {
          if (this.config[configType]) {
            await this.saveConfigFile(configType, configFile);
          }
        }
        
        this.emit('configImported', configData);
        console.log(chalk.green('✅ Configuration imported successfully'));
      }
      
    } catch (error) {
      console.error(chalk.red('❌ Failed to import configuration:'), error);
      throw error;
    }
  }
  
  async shutdown() {
    console.log(chalk.blue('🛑 Shutting down configuration manager...'));
    
    // Close all file watchers
    for (const [type, watcher] of this.watchers) {
      await watcher.close();
    }
    
    this.watchers.clear();
    
    console.log(chalk.green('✅ Configuration manager shutdown complete'));
  }
}

module.exports = ConfigManager;

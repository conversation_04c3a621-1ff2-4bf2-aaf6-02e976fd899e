# 🚀 Time Stamp Project - Testing & Validation System

## Overview

This comprehensive testing and validation system allows you to easily run, test, and validate all projects in your Time Stamp Project workspace. It automatically discovers projects, installs dependencies, and provides a unified interface for testing and running everything.

## 🔧 Quick Setup

### 1. Install Dependencies
```bash
npm install
```

### 2. Setup Workspace
```bash
npm run setup
```

This will:
- ✅ Check prerequisites (Node.js, Python)
- 📦 Install dependencies for all projects
- 📄 Create environment files
- 🧪 Setup test configurations
- 📜 Generate run scripts

## 🎯 Quick Commands

### List All Projects
```bash
npm run list
# or
node project-runner.js --list
```

### Test All Projects
```bash
npm test
# or
node project-runner.js
```

### Run Specific Projects
```bash
npm run ecostamp     # EcoStamp backend server
npm run scanner      # Security scanner
npm run orchestrator # AI orchestration system
```

### Run All Projects
```bash
npm run run                # Interactive mode
npm run run:background     # Background mode
```

## 🪟 Windows Shortcuts

For Windows users, convenient batch files are provided:

```cmd
run.bat list      # List all projects
run.bat test      # Test all projects
run.bat ecostamp  # Run EcoStamp backend
run.bat scanner   # Run security scanner
run.bat help      # Show help
```

## 📦 Discovered Projects

The system automatically discovers and manages these projects:

### 1. **EcoStamp Backend** (`core-systems/EcoStamp/source`)
- **Type**: Node.js/Express server
- **Purpose**: AI environmental impact tracking backend
- **Run Command**: `npm start` or `npm run dev`
- **Port**: 3000 (configurable via .env)
- **Features**: 
  - Real-time AI benchmarks
  - SHA-256 verification
  - File upload processing
  - Rate limiting & security

### 2. **Development Tools** (`development-tools`)
- **Type**: Node.js security scanner
- **Purpose**: Enterprise-grade security scanning
- **Run Command**: `npm run scan`
- **Features**:
  - Vulnerability detection
  - SBOM generation
  - License compliance
  - CVE scanning

### 3. **AI Orchestration** (`ai-orchestration`)
- **Type**: Node.js orchestration system
- **Purpose**: Multi-AI workflow coordination
- **Run Command**: `node orchestrator.js`
- **Features**:
  - Cross-platform AI integration
  - Workflow automation
  - Project analysis

### 4. **Core Systems** (`core-systems`)
- **Type**: Python framework
- **Purpose**: Universal feedback loop framework
- **Run Command**: `python -m pytest` (tests)
- **Features**:
  - Dual-purpose feedback loops
  - Search & rescue domain
  - Comprehensive testing

### 5. **Browser Extension** (`core-systems/EcoStamp/ecostamp-extension`)
- **Type**: Web extension
- **Purpose**: Cross-browser AI impact tracking
- **Features**:
  - Universal AI platform support
  - Real-time impact display
  - Cross-browser compatibility

## 🧪 Testing Features

### Automatic Discovery
- Detects project types (Node.js, Python, Browser Extension)
- Identifies entry points and test commands
- Analyzes dependencies and frameworks

### Dependency Management
- Automatically installs npm packages
- Handles Python requirements
- Creates environment files
- Validates installations

### Test Execution
- Runs project-specific tests
- Handles timeouts and retries
- Provides detailed error reporting
- Supports parallel execution

### Validation
- Checks code quality
- Validates configurations
- Verifies security settings
- Tests API endpoints

## 🔧 Configuration

### Environment Files
The setup automatically creates `.env` files for each project:

**EcoStamp Backend** (`.env`):
```env
NODE_ENV=development
PORT=3000
ENABLE_SCHEDULER=false
CORS_ORIGIN=*
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

**Development Tools** (`.env`):
```env
NODE_ENV=development
SCAN_TIMEOUT=300000
REPORT_FORMAT=json
OUTPUT_DIR=./reports
```

### Test Configuration (`test-config.json`)
```json
{
  "projects": [...],
  "defaultTimeout": 60000,
  "retries": 1,
  "parallel": false
}
```

## 🚀 Advanced Usage

### Run Specific Project
```bash
node project-runner.js --project ecostamp --run
```

### Background Mode
```bash
node project-runner.js --run --background
```

### Skip Tests
```bash
node project-runner.js --run --skip-tests
```

### Custom Options
```bash
node project-runner.js --help
```

## 📊 Output Examples

### Project Discovery
```
🔍 Discovering projects...
✅ Found 4 projects

📋 Discovered Projects:

1. EcoStamp Backend
   Path: core-systems/EcoStamp/source
   Type: nodejs (express)
   Can Run: ✅
   Can Test: ✅
   Run Commands: npm start, npm run dev

2. Development Tools
   Path: development-tools
   Type: nodejs (nodejs)
   Can Run: ✅
   Can Test: ❌
   Run Commands: npm run scan
```

### Test Results
```
📦 EcoStamp Backend (nodejs)
   core-systems/EcoStamp/source
   📦 Installing Node.js dependencies...
   ✅ Dependencies installed
   🧪 Running tests...
   ✅ Tests passed
   🚀 Starting in background...
   ✅ Started in background (PID: 12345)

📊 Summary:
========================================
✅ Passed: 3
❌ Failed: 0
⏭️  Skipped: 1
📦 Total: 4
```

## 🛠️ Troubleshooting

### Common Issues

**Node.js Version Error**
```
Error: Node.js v14.x.x is too old. Please install Node.js 16 or higher.
```
Solution: Update Node.js to version 16 or higher.

**npm Install Fails**
```
Error: npm install failed
```
Solutions:
- Clear npm cache: `npm cache clean --force`
- Delete node_modules and reinstall
- Check network connectivity

**Python Dependencies Fail**
```
Error: pip install failed
```
Solutions:
- Ensure Python is installed and in PATH
- Try `python3` instead of `python`
- Install pip if missing

**Port Already in Use**
```
Error: Port 3000 is already in use
```
Solution: Change port in `.env` file or stop conflicting process.

### Debug Mode
Add `DEBUG=1` environment variable for verbose output:
```bash
DEBUG=1 node project-runner.js --run
```

## 📁 File Structure

```
Time_Stamp_Project/
├── project-runner.js          # Main runner script
├── setup-workspace.js         # Workspace setup
├── package.json               # Dependencies
├── test-config.json           # Test configuration
├── run.bat                    # Windows shortcuts
├── run.sh                     # Unix shortcuts
├── README-TESTING.md          # This file
├── core-systems/              # Python framework
├── development-tools/         # Security scanner
├── ai-orchestration/          # AI orchestration
└── distribution/              # Built packages
```

## 🎯 Next Steps

1. **Run the setup**: `npm run setup`
2. **List projects**: `npm run list`
3. **Test everything**: `npm test`
4. **Run EcoStamp**: `npm run ecostamp`
5. **Open browser**: Visit `http://localhost:3000`

## 🔗 Related Documentation

- [EcoStamp Documentation](core-systems/EcoStamp/README.md)
- [Security Scanner Guide](development-tools/README.md)
- [AI Orchestration Manual](ai-orchestration/README.md)
- [Core Systems Framework](core-systems/README.md)

---

**🌱 Ready to test and validate your entire Time Stamp Project ecosystem!**

#!/usr/bin/env node

/**
 * AI Assistant Orchestration & Pruning Platform - Web Server
 * 
 * Complete web interface for managing AI assistant orchestration,
 * role assignments, workflow execution, and pruning operations.
 */

const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');
const fs = require('fs-extra');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const chalk = require('chalk');

// Import orchestration components
const AIOrchestrator = require('./orchestrator');

class OrchestrationWebServer {
  constructor() {
    this.app = express();
    this.server = http.createServer(this.app);
    this.io = socketIo(this.server, {
      cors: {
        origin: "*",
        methods: ["GET", "POST"]
      }
    });
    
    this.port = process.env.PORT || 3001;
    this.orchestrator = null;
    this.activeConnections = new Set();
    
    this.setupMiddleware();
    this.setupRoutes();
    this.setupSocketHandlers();
  }
  
  setupMiddleware() {
    // Security middleware
    this.app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'", "'unsafe-inline'"],
          imgSrc: ["'self'", "data:", "https:"],
        },
      },
    }));
    
    // Rate limiting
    const limiter = rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 100 // limit each IP to 100 requests per windowMs
    });
    this.app.use(limiter);
    
    // CORS and parsing
    this.app.use(cors());
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true }));
    
    // Static files
    this.app.use(express.static(path.join(__dirname, 'public')));
  }
  
  setupRoutes() {
    // Main dashboard
    this.app.get('/', (req, res) => {
      res.sendFile(path.join(__dirname, 'public', 'index.html'));
    });
    
    // API Routes
    this.app.get('/api/status', this.handleStatus.bind(this));
    this.app.get('/api/agents', this.handleGetAgents.bind(this));
    this.app.post('/api/roles/assign', this.handleAssignRoles.bind(this));
    this.app.get('/api/roles', this.handleGetRoles.bind(this));
    this.app.post('/api/workflow/run', this.handleRunWorkflow.bind(this));
    this.app.get('/api/workflow/status', this.handleWorkflowStatus.bind(this));
    this.app.post('/api/pruning/configure', this.handleConfigurePruning.bind(this));
    this.app.get('/api/pruning/status', this.handlePruningStatus.bind(this));
    this.app.post('/api/pruning/run', this.handleRunPruning.bind(this));
    this.app.get('/api/audit/logs', this.handleAuditLogs.bind(this));
    this.app.post('/api/config/save', this.handleSaveConfig.bind(this));
    this.app.get('/api/config/load', this.handleLoadConfig.bind(this));

    // New Multi-Agent Endpoints
    this.app.post('/api/agents/multi-assign', this.handleMultiAgentAssign.bind(this));
    this.app.get('/api/agents/registry', this.handleAgentRegistry.bind(this));
    this.app.post('/api/thread/merge', this.handleThreadMerge.bind(this));
    this.app.post('/api/code/execute', this.handleCodeExecute.bind(this));
    this.app.get('/api/orchestration/chain', this.handleOrchestrationChain.bind(this));
    this.app.post('/api/orchestration/simulate', this.handleSimulateChain.bind(this));
    this.app.get('/api/meta/status', this.handleMetaOrchestratorStatus.bind(this));
  }
  
  setupSocketHandlers() {
    this.io.on('connection', (socket) => {
      console.log(chalk.green(`🔌 Client connected: ${socket.id}`));
      this.activeConnections.add(socket);
      
      socket.on('disconnect', () => {
        console.log(chalk.yellow(`🔌 Client disconnected: ${socket.id}`));
        this.activeConnections.delete(socket);
      });
      
      socket.on('subscribe-logs', () => {
        socket.join('logs');
      });
      
      socket.on('subscribe-workflow', () => {
        socket.join('workflow');
      });
    });
  }
  
  // API Handlers
  async handleStatus(req, res) {
    try {
      const status = {
        server: 'running',
        timestamp: new Date().toISOString(),
        connections: this.activeConnections.size,
        orchestrator: this.orchestrator ? 'initialized' : 'not_initialized',
        version: '1.0.0'
      };
      res.json(status);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }
  
  async handleGetAgents(req, res) {
    try {
      const agents = [
        // Core AI Assistants
        { id: 'chatgpt-4o', name: 'ChatGPT-4o', vendor: 'OpenAI', status: 'available', capabilities: ['generation', 'analysis', 'thread-merger', 'execution'], description: 'Advanced reasoning and code generation', scope: 'Generalist Agent' },
        { id: 'claude-opus', name: 'Claude 3 Opus', vendor: 'Anthropic', status: 'available', capabilities: ['generation', 'analysis', 'validation', 'thread-merger'], description: 'Deep reasoning and cautious validation', scope: 'Feedback Loops' },
        { id: 'gemini-pro', name: 'Gemini 1.5 Pro', vendor: 'Google', status: 'available', capabilities: ['execution', 'analysis', 'thread-merger'], description: 'Code execution and factual balance', scope: 'Code Execution & Reasoning' },
        { id: 'perplexity', name: 'Perplexity', vendor: 'Perplexity AI', status: 'available', capabilities: ['analysis', 'thread-merger'], description: 'Source-traced summarization', scope: 'Thread Context Synthesizer' },

        // Specialized Coding Assistants
        { id: 'github-copilot', name: 'GitHub Copilot', vendor: 'GitHub', status: 'available', capabilities: ['generation', 'completion'], description: 'Shallow context code generation', scope: 'Code Generation' },
        { id: 'cursor', name: 'Cursor', vendor: 'Cursor', status: 'available', capabilities: ['generation', 'analysis', 'completion', 'validation'], description: 'File validator with snapshots', scope: 'Precision Diff + File Audit' },
        { id: 'amazon-q', name: 'Amazon Q', vendor: 'AWS', status: 'available', capabilities: ['validation', 'debugging'], description: 'Validation focus and bug catching', scope: 'Debugger + Feedback' },
        { id: 'tabnine', name: 'Tabnine', vendor: 'Tabnine', status: 'available', capabilities: ['completion', 'analysis'], description: 'AI code completion', scope: 'Code Completion' },

        // Specialized Agent Roles
        { id: 'copilot-gen1', name: 'Copilot_Gen1', vendor: 'GitHub', status: 'available', capabilities: ['generation'], description: 'GitHub Copilot, shallow context', scope: 'Fast / shallow context' },
        { id: 'q-validator-aws', name: 'Q_Validator_AWS', vendor: 'AWS', status: 'available', capabilities: ['validation', 'debugging'], description: 'Amazon Q, validation focus', scope: 'Deep / logic-first' },
        { id: 'vector-bug-memory', name: 'Vector://bug_memory', vendor: 'Local', status: 'available', capabilities: ['memory', 'debugging'], description: 'Bug pattern memory (deep vector)', scope: 'Debug History Recall' },
        { id: 'smarttest-alpha', name: 'SmartTest_Alpha', vendor: 'Claude + DiffTool', status: 'available', capabilities: ['testing', 'validation'], description: 'Test Generator (Claude + DiffTool)', scope: 'Test Coverage' },
        { id: 'cursorcheck-002', name: 'CursorCheck_002', vendor: 'Cursor + Claude', status: 'available', capabilities: ['validation', 'diff'], description: 'File Validator with Cursor Snapshots', scope: 'Precision Diff + File Audit' },
        { id: 'claude-validator', name: 'ClaudeValidator', vendor: 'Anthropic', status: 'available', capabilities: ['validation'], description: 'Claude\'s cautious validation', scope: 'Validation Feedback' },
        { id: 'gemini-exec', name: 'GeminiExec', vendor: 'Google', status: 'available', capabilities: ['execution'], description: 'Google Gemini for executable blocks', scope: 'Code Execution' },
        { id: 'claudia-exec', name: 'ClaudiaExec', vendor: 'Claude + Runtime', status: 'available', capabilities: ['execution'], description: 'Claude + runtime layer for execution', scope: 'Code Execution & Reasoning' },
        { id: 'perplexity-thread', name: 'Perplexity_Thread', vendor: 'Perplexity', status: 'available', capabilities: ['thread-merger'], description: 'Thread summarizer and merger', scope: 'Thread Context Synthesizer' },
        { id: 'chatgpt-bridge', name: 'ChatGPT_Bridge', vendor: 'OpenAI', status: 'available', capabilities: ['generation', 'analysis'], description: 'ChatGPT code bridge + oversight', scope: 'Generalist Agent' },
        { id: 'toolkit-memory', name: 'Toolkit Memory', vendor: 'Local', status: 'available', capabilities: ['memory', 'strategy'], description: 'Agent-level memory with strategy', scope: 'Task/Context Embedding' },
        { id: 'strategy-copilot', name: 'Strategy Copilot', vendor: 'Local', status: 'available', capabilities: ['strategy', 'orchestration'], description: 'Layer-specific copilot override', scope: 'Prompt Tuning / Role Adaptation' },

        // Additional Specialized Agents
        { id: 'local-thread-memory', name: 'Local thread memory', vendor: 'Local', status: 'available', capabilities: ['memory'], description: 'Local thread memory storage', scope: 'Cautious / persistent' },
        { id: 'code-suggester', name: 'Code Suggester', vendor: 'Multi', status: 'available', capabilities: ['generation', 'completion'], description: 'Advanced code suggestions', scope: 'Deterministic' }
      ];
      res.json(agents);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }
  
  async handleAssignRoles(req, res) {
    try {
      const { roles } = req.body;
      
      // Validate role assignments
      const validRoles = ['generator', 'analyzer', 'completer', 'validator'];
      for (const role of Object.keys(roles)) {
        if (!validRoles.includes(role)) {
          return res.status(400).json({ error: `Invalid role: ${role}` });
        }
      }
      
      // Save role configuration
      await this.saveRoleConfig(roles);
      
      // Broadcast update to connected clients
      this.io.emit('roles-updated', roles);
      
      res.json({ success: true, roles });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }
  
  async handleGetRoles(req, res) {
    try {
      const roles = await this.loadRoleConfig();
      res.json(roles);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }
  
  async handleRunWorkflow(req, res) {
    try {
      const { workflowType, parameters } = req.body;
      
      // Initialize orchestrator if needed
      if (!this.orchestrator) {
        this.orchestrator = new AIOrchestrator();
      }
      
      // Start workflow execution
      const workflowId = `workflow_${Date.now()}`;
      
      // Simulate workflow execution (replace with actual orchestration logic)
      setTimeout(() => {
        this.io.to('workflow').emit('workflow-progress', {
          workflowId,
          status: 'running',
          progress: 50,
          message: 'Executing workflow steps...'
        });
        
        setTimeout(() => {
          this.io.to('workflow').emit('workflow-complete', {
            workflowId,
            status: 'completed',
            progress: 100,
            results: {
              generated: 15,
              analyzed: 8,
              validated: 12,
              completed: 20
            }
          });
        }, 3000);
      }, 1000);
      
      res.json({ success: true, workflowId });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }
  
  async handleWorkflowStatus(req, res) {
    try {
      // Return mock workflow status
      const status = {
        active: 2,
        completed: 15,
        failed: 1,
        queued: 3
      };
      res.json(status);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }
  
  async handleConfigurePruning(req, res) {
    try {
      const { frequency, retentionDays, archiveLocation } = req.body;
      
      const config = {
        frequency,
        retentionDays,
        archiveLocation,
        lastUpdated: new Date().toISOString()
      };
      
      await this.savePruningConfig(config);
      res.json({ success: true, config });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }
  
  async handlePruningStatus(req, res) {
    try {
      const status = {
        lastRun: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
        nextRun: new Date(Date.now() + 6 * 24 * 60 * 60 * 1000).toISOString(),
        itemsPruned: 1247,
        itemsArchived: 892,
        spaceSaved: '2.3 GB'
      };
      res.json(status);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }
  
  async handleRunPruning(req, res) {
    try {
      const pruningId = `pruning_${Date.now()}`;
      
      // Simulate pruning process
      setTimeout(() => {
        this.io.emit('pruning-progress', {
          pruningId,
          status: 'analyzing',
          progress: 25,
          message: 'Analyzing context for pruning candidates...'
        });
        
        setTimeout(() => {
          this.io.emit('pruning-progress', {
            pruningId,
            status: 'pruning',
            progress: 75,
            message: 'Pruning low-relevance items...'
          });
          
          setTimeout(() => {
            this.io.emit('pruning-complete', {
              pruningId,
              status: 'completed',
              progress: 100,
              results: {
                itemsPruned: 156,
                itemsArchived: 89,
                spaceSaved: '245 MB'
              }
            });
          }, 2000);
        }, 2000);
      }, 1000);
      
      res.json({ success: true, pruningId });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }
  
  async handleAuditLogs(req, res) {
    try {
      const logs = [
        {
          timestamp: new Date().toISOString(),
          action: 'workflow_executed',
          user: 'system',
          details: 'Code generation workflow completed successfully'
        },
        {
          timestamp: new Date(Date.now() - 60000).toISOString(),
          action: 'roles_assigned',
          user: 'admin',
          details: 'Updated role assignments for AI agents'
        },
        {
          timestamp: new Date(Date.now() - 120000).toISOString(),
          action: 'pruning_completed',
          user: 'system',
          details: 'Scheduled pruning removed 156 items'
        }
      ];
      res.json(logs);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }
  
  async handleSaveConfig(req, res) {
    try {
      const { config } = req.body;
      await fs.writeJson(path.join(__dirname, 'config', 'orchestration.json'), config, { spaces: 2 });
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }
  
  async handleLoadConfig(req, res) {
    try {
      const configPath = path.join(__dirname, 'config', 'orchestration.json');
      const config = await fs.pathExists(configPath) ? await fs.readJson(configPath) : {};
      res.json(config);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  // New Multi-Agent Handler Methods
  async handleMultiAgentAssign(req, res) {
    try {
      const { roleAssignments } = req.body;

      // Validate multi-agent assignments
      const validRoles = ['generator', 'analyzer', 'completer', 'validator', 'code-executor', 'thread-merger', 'meta-orchestrator', 'memory', 'debugger'];

      for (const role of Object.keys(roleAssignments)) {
        if (!validRoles.includes(role)) {
          return res.status(400).json({ error: `Invalid role: ${role}` });
        }

        // Ensure agents array is valid
        if (!Array.isArray(roleAssignments[role])) {
          return res.status(400).json({ error: `Role ${role} must have an array of agents` });
        }
      }

      // Save multi-agent configuration
      await this.saveMultiAgentConfig(roleAssignments);

      // Broadcast update to connected clients
      this.io.emit('multi-agents-updated', roleAssignments);

      res.json({ success: true, roleAssignments });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async handleAgentRegistry(req, res) {
    try {
      const registry = {
        categories: {
          'Core AI Assistants': ['chatgpt-4o', 'claude-opus', 'gemini-pro', 'perplexity'],
          'Coding Assistants': ['github-copilot', 'cursor', 'amazon-q', 'tabnine'],
          'Specialized Agents': ['copilot-gen1', 'q-validator-aws', 'vector-bug-memory', 'smarttest-alpha'],
          'Execution Agents': ['gemini-exec', 'claudia-exec', 'code-suggester'],
          'Memory & Strategy': ['toolkit-memory', 'strategy-copilot', 'local-thread-memory']
        },
        roleCapabilities: {
          'generator': ['chatgpt-4o', 'claude-opus', 'github-copilot', 'cursor', 'copilot-gen1'],
          'analyzer': ['claude-opus', 'perplexity', 'cursor', 'amazon-q', 'q-validator-aws'],
          'completer': ['github-copilot', 'tabnine', 'cursor', 'code-suggester'],
          'validator': ['claude-opus', 'amazon-q', 'claude-validator', 'cursorcheck-002', 'smarttest-alpha'],
          'code-executor': ['gemini-exec', 'claudia-exec', 'gemini-pro', 'chatgpt-4o'],
          'thread-merger': ['chatgpt-4o', 'claude-opus', 'perplexity-thread', 'perplexity'],
          'memory': ['vector-bug-memory', 'toolkit-memory', 'local-thread-memory'],
          'debugger': ['q-validator-aws', 'amazon-q', 'vector-bug-memory']
        }
      };
      res.json(registry);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async handleThreadMerge(req, res) {
    try {
      const { threads, mergerAgents } = req.body;

      // Simulate thread merging process
      const mergeId = `merge_${Date.now()}`;

      setTimeout(() => {
        this.io.emit('thread-merge-progress', {
          mergeId,
          status: 'analyzing',
          progress: 33,
          message: 'Analyzing thread contexts...'
        });

        setTimeout(() => {
          this.io.emit('thread-merge-progress', {
            mergeId,
            status: 'merging',
            progress: 66,
            message: 'Merging insights from multiple agents...'
          });

          setTimeout(() => {
            this.io.emit('thread-merge-complete', {
              mergeId,
              status: 'completed',
              progress: 100,
              result: {
                mergedContent: 'Unified thread content from multiple AI agents',
                sourceAgents: mergerAgents,
                conflictsResolved: 3,
                confidenceScore: 0.92
              }
            });
          }, 1500);
        }, 1500);
      }, 1000);

      res.json({ success: true, mergeId });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async handleCodeExecute(req, res) {
    try {
      const { code, executorAgents, language } = req.body;

      // Simulate multi-agent code execution
      const executionId = `exec_${Date.now()}`;

      setTimeout(() => {
        this.io.emit('code-execution-progress', {
          executionId,
          status: 'executing',
          progress: 50,
          message: `Executing code with ${executorAgents.length} agents...`
        });

        setTimeout(() => {
          const results = executorAgents.map(agent => ({
            agent,
            output: `Execution result from ${agent}`,
            executionTime: Math.random() * 1000 + 100,
            success: Math.random() > 0.1,
            errors: Math.random() > 0.8 ? ['Minor syntax warning'] : []
          }));

          this.io.emit('code-execution-complete', {
            executionId,
            status: 'completed',
            progress: 100,
            results,
            consensus: results.filter(r => r.success).length > results.length / 2
          });
        }, 2000);
      }, 1000);

      res.json({ success: true, executionId });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async handleOrchestrationChain(req, res) {
    try {
      const chain = {
        metaOrchestrator: 'strategy-copilot',
        steps: [
          { role: 'generator', agents: ['chatgpt-4o', 'claude-opus'], priority: 1 },
          { role: 'analyzer', agents: ['perplexity', 'cursor'], priority: 2 },
          { role: 'validator', agents: ['claude-validator', 'amazon-q'], priority: 3 },
          { role: 'code-executor', agents: ['gemini-exec', 'claudia-exec'], priority: 4 },
          { role: 'thread-merger', agents: ['perplexity-thread', 'chatgpt-bridge'], priority: 5 }
        ],
        fallbackStrategy: 'retry-with-alternative-agent',
        conflictResolution: 'meta-orchestrator-decision'
      };
      res.json(chain);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async handleSimulateChain(req, res) {
    try {
      const { chain } = req.body;

      const simulationId = `sim_${Date.now()}`;

      // Simulate chain execution
      setTimeout(() => {
        chain.steps.forEach((step, index) => {
          setTimeout(() => {
            this.io.emit('chain-simulation-step', {
              simulationId,
              step: index + 1,
              role: step.role,
              agents: step.agents,
              status: 'executing',
              estimatedOutput: `Simulated output from ${step.role} using ${step.agents.join(', ')}`
            });
          }, index * 1000);
        });

        setTimeout(() => {
          this.io.emit('chain-simulation-complete', {
            simulationId,
            status: 'completed',
            totalSteps: chain.steps.length,
            estimatedTime: chain.steps.length * 2.5,
            predictedSuccess: 0.89
          });
        }, chain.steps.length * 1000 + 500);
      }, 500);

      res.json({ success: true, simulationId });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async handleMetaOrchestratorStatus(req, res) {
    try {
      const status = {
        active: true,
        currentAgent: 'strategy-copilot',
        activeChains: 2,
        completedChains: 15,
        conflictsResolved: 8,
        averageChainTime: 12.5,
        successRate: 0.92,
        lastDecision: {
          timestamp: new Date().toISOString(),
          decision: 'Route to Claude for validation',
          reason: 'Higher confidence score for complex logic validation'
        }
      };
      res.json(status);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }
  
  // Helper methods
  async saveRoleConfig(roles) {
    const configDir = path.join(__dirname, 'config');
    await fs.ensureDir(configDir);
    await fs.writeJson(path.join(configDir, 'roles.json'), roles, { spaces: 2 });
  }
  
  async loadRoleConfig() {
    const configPath = path.join(__dirname, 'config', 'roles.json');
    if (await fs.pathExists(configPath)) {
      return await fs.readJson(configPath);
    }
    return {
      generator: 'github-copilot',
      analyzer: 'cursor',
      completer: 'tabnine',
      validator: 'amazon-q'
    };
  }
  
  async savePruningConfig(config) {
    const configDir = path.join(__dirname, 'config');
    await fs.ensureDir(configDir);
    await fs.writeJson(path.join(configDir, 'pruning.json'), config, { spaces: 2 });
  }

  async saveMultiAgentConfig(roleAssignments) {
    const configDir = path.join(__dirname, 'config');
    await fs.ensureDir(configDir);
    await fs.writeJson(path.join(configDir, 'multi-agents.json'), roleAssignments, { spaces: 2 });
  }

  async loadMultiAgentConfig() {
    const configPath = path.join(__dirname, 'config', 'multi-agents.json');
    if (await fs.pathExists(configPath)) {
      return await fs.readJson(configPath);
    }
    return {
      generator: ['chatgpt-4o', 'claude-opus'],
      analyzer: ['perplexity', 'cursor'],
      completer: ['github-copilot', 'tabnine'],
      validator: ['claude-validator', 'amazon-q'],
      'code-executor': ['gemini-exec', 'claudia-exec'],
      'thread-merger': ['perplexity-thread', 'chatgpt-bridge'],
      memory: ['toolkit-memory', 'vector-bug-memory'],
      debugger: ['q-validator-aws', 'amazon-q']
    };
  }
  
  start() {
    this.server.listen(this.port, () => {
      console.log(chalk.green(`🚀 AI Orchestration Web Server running at http://localhost:${this.port}`));
      console.log(chalk.blue(`📊 Dashboard: http://localhost:${this.port}`));
      console.log(chalk.yellow(`🔌 WebSocket connections: ${this.activeConnections.size}`));
    });
  }
  
  stop() {
    this.server.close(() => {
      console.log(chalk.red('🛑 AI Orchestration Web Server stopped'));
    });
  }
}

// Start server if run directly
if (require.main === module) {
  const server = new OrchestrationWebServer();
  server.start();
  
  // Graceful shutdown
  process.on('SIGINT', () => {
    console.log(chalk.yellow('\n🛑 Shutting down gracefully...'));
    server.stop();
    process.exit(0);
  });
}

module.exports = OrchestrationWebServer;

{"name": "ai-orchestration-hub", "version": "1.0.0", "description": "Universal AI coding tools orchestration system for VS Code", "main": "orchestrator.js", "scripts": {"start": "node orchestrator.js", "init": "node setup.js", "analyze": "node orchestrator.js --analyze", "generate": "node orchestrator.js --generate", "workflow": "node orchestrator.js --workflow", "test": "node test/orchestrator.test.js", "dev": "nodemon orchestrator.js", "install-extensions": "node scripts/install-extensions.js", "dgm:init": "node dgm/index.js --init", "dgm:start": "node dgm/index.js", "dgm:evolve": "node dgm/index.js --evolve", "dgm:dashboard": "node dgm/index.js --dashboard", "dgm:cli": "node dgm/index.js --cli", "dgm:review": "node dgm/index.js --review", "dgm:export": "node dgm/index.js --export", "dgm:reset": "node dgm/index.js --reset"}, "keywords": ["ai", "orchestration", "vscode", "cursor", "windsurf", "tabnine", "augment-code", "automation", "darwin-godel-machine", "self-improving", "evolutionary-algorithms", "genetic-programming"], "author": "<PERSON>", "license": "MIT", "dependencies": {"express": "^4.18.2", "ws": "^8.14.2", "axios": "^1.6.0", "fs-extra": "^11.1.1", "chokidar": "^3.5.3", "commander": "^11.1.0", "chalk": "^4.1.2", "inquirer": "^8.2.6", "ora": "^5.4.1", "node-fetch": "^2.7.0", "dotenv": "^16.3.1", "yaml": "^2.3.4", "glob": "^10.3.10", "semver": "^7.5.4", "uuid": "^9.0.1", "readline": "^1.3.0", "cli-table3": "^0.6.3", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.7.0", "@types/node": "^20.8.0"}, "engines": {"node": ">=16.0.0"}, "repository": {"type": "git", "url": "local"}, "config": {"ai-tools": {"augment-code": {"enabled": true, "api-endpoint": "http://localhost:8080", "features": ["analysis", "indexing", "context"]}, "cursor": {"enabled": true, "integration": "vscode-extension", "features": ["generation", "completion", "refactoring"]}, "windsurf": {"enabled": true, "integration": "vscode-extension", "features": ["multi-file-edit", "context-aware"]}, "tabnine": {"enabled": true, "integration": "vscode-extension", "features": ["inline-completion", "suggestions"]}}}}
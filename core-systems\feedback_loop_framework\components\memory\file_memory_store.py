"""
File-Based Memory Store

Secure, developer-only memory store that persists feedback entries to files
with support for analytics, querying, and data retention management.
"""

import json
import logging
import os
import gzip
import shutil
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from pathlib import Path
import threading
from collections import defaultdict

from ...core.feedback_types import FeedbackEntry, FeedbackType


class FileMemoryStore:
    """
    File-based memory store for feedback entries with developer-only access,
    compression, analytics support, and automatic cleanup capabilities.
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize the file memory store.
        
        Args:
            config: Configuration dictionary for store parameters
        """
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # Storage configuration
        self.base_path = Path(self.config.get('base_path', './feedback_data'))
        self.compression_enabled = self.config.get('compression_enabled', True)
        self.max_file_size = self.config.get('max_file_size_mb', 10) * 1024 * 1024  # Convert to bytes
        self.retention_days = self.config.get('retention_days', 30)
        self.backup_enabled = self.config.get('backup_enabled', True)
        
        # File organization
        self.entries_dir = self.base_path / 'entries'
        self.analytics_dir = self.base_path / 'analytics'
        self.backup_dir = self.base_path / 'backups'
        
        # Thread safety
        self._lock = threading.RLock()
        
        # In-memory cache for recent entries
        self.cache_size = self.config.get('cache_size', 1000)
        self.entry_cache = []
        
        # Analytics cache
        self.analytics_cache = {}
        self.cache_expiry = timedelta(minutes=15)
        self.last_cache_update = datetime.min
        
        # Initialize storage
        self._initialize_storage()
        
        # Performance statistics
        self.stats = {
            'total_stored': 0,
            'total_retrieved': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'files_created': 0,
            'cleanup_runs': 0
        }
    
    def _initialize_storage(self) -> None:
        """Initialize storage directories and structure."""
        try:
            # Create directories
            self.entries_dir.mkdir(parents=True, exist_ok=True)
            self.analytics_dir.mkdir(parents=True, exist_ok=True)
            if self.backup_enabled:
                self.backup_dir.mkdir(parents=True, exist_ok=True)
            
            # Create domain subdirectories
            for domain in ['drone_ai', 'timestamp_ai', 'general']:
                (self.entries_dir / domain).mkdir(exist_ok=True)
            
            # Set restrictive permissions (developer-only access)
            if os.name != 'nt':  # Unix-like systems
                os.chmod(self.base_path, 0o700)
                os.chmod(self.entries_dir, 0o700)
                os.chmod(self.analytics_dir, 0o700)
                if self.backup_enabled:
                    os.chmod(self.backup_dir, 0o700)
            
            self.logger.info(f"Initialized file memory store at {self.base_path}")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize storage: {str(e)}")
            raise
    
    def store_entry(self, entry: FeedbackEntry) -> bool:
        """
        Store a feedback entry.
        
        Args:
            entry: Feedback entry to store
            
        Returns:
            True if stored successfully, False otherwise
        """
        try:
            with self._lock:
                # Add to cache
                self.entry_cache.append(entry)
                if len(self.entry_cache) > self.cache_size:
                    self.entry_cache.pop(0)  # Remove oldest
                
                # Determine file path
                file_path = self._get_entry_file_path(entry)
                
                # Convert entry to dictionary
                entry_data = self._entry_to_dict(entry)
                
                # Write to file
                self._write_entry_to_file(file_path, entry_data)
                
                # Update statistics
                self.stats['total_stored'] += 1
                
                # Invalidate analytics cache
                self._invalidate_analytics_cache()
                
                return True
                
        except Exception as e:
            self.logger.error(f"Failed to store entry {entry.entry_id}: {str(e)}")
            return False
    
    def retrieve_entries(self, 
                        domain: Optional[str] = None, 
                        agent_id: Optional[str] = None, 
                        limit: Optional[int] = None) -> List[FeedbackEntry]:
        """
        Retrieve feedback entries with optional filtering.
        
        Args:
            domain: Optional domain filter
            agent_id: Optional agent ID filter
            limit: Optional limit on number of entries
            
        Returns:
            List of matching feedback entries
        """
        try:
            with self._lock:
                # Try cache first for recent entries
                if limit and limit <= len(self.entry_cache):
                    cache_results = self._filter_cache_entries(domain, agent_id, limit)
                    if len(cache_results) >= (limit or 0):
                        self.stats['cache_hits'] += 1
                        self.stats['total_retrieved'] += len(cache_results)
                        return cache_results
                
                self.stats['cache_misses'] += 1
                
                # Read from files
                entries = self._read_entries_from_files(domain, agent_id, limit)
                self.stats['total_retrieved'] += len(entries)
                
                return entries
                
        except Exception as e:
            self.logger.error(f"Failed to retrieve entries: {str(e)}")
            return []
    
    def get_analytics_data(self, 
                          domain: Optional[str] = None, 
                          time_range: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Get analytics data for reporting and dashboards.
        
        Args:
            domain: Optional domain filter
            time_range: Optional time range filter
            
        Returns:
            Analytics data dictionary
        """
        try:
            with self._lock:
                # Check cache first
                cache_key = f"{domain}_{time_range}"
                if (cache_key in self.analytics_cache and 
                    datetime.utcnow() - self.last_cache_update < self.cache_expiry):
                    return self.analytics_cache[cache_key]
                
                # Generate analytics
                analytics = self._generate_analytics(domain, time_range)
                
                # Cache results
                self.analytics_cache[cache_key] = analytics
                self.last_cache_update = datetime.utcnow()
                
                return analytics
                
        except Exception as e:
            self.logger.error(f"Failed to generate analytics: {str(e)}")
            return {}
    
    def cleanup_old_entries(self, retention_days: int) -> int:
        """
        Clean up old entries beyond retention period.
        
        Args:
            retention_days: Number of days to retain entries
            
        Returns:
            Number of entries cleaned up
        """
        try:
            with self._lock:
                cutoff_date = datetime.utcnow() - timedelta(days=retention_days)
                cleaned_count = 0
                
                # Clean up files
                for domain_dir in self.entries_dir.iterdir():
                    if domain_dir.is_dir():
                        for file_path in domain_dir.glob('*.json*'):
                            try:
                                # Check file modification time
                                file_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                                if file_time < cutoff_date:
                                    # Backup before deletion if enabled
                                    if self.backup_enabled:
                                        self._backup_file(file_path)
                                    
                                    file_path.unlink()
                                    cleaned_count += 1
                                    
                            except Exception as e:
                                self.logger.warning(f"Failed to clean up file {file_path}: {str(e)}")
                
                # Clean up cache
                self.entry_cache = [
                    entry for entry in self.entry_cache 
                    if entry.timestamp > cutoff_date
                ]
                
                # Update statistics
                self.stats['cleanup_runs'] += 1
                
                self.logger.info(f"Cleaned up {cleaned_count} old entries")
                return cleaned_count
                
        except Exception as e:
            self.logger.error(f"Failed to cleanup old entries: {str(e)}")
            return 0
    
    def _get_entry_file_path(self, entry: FeedbackEntry) -> Path:
        """Get file path for storing an entry."""
        domain = entry.domain or 'general'
        date_str = entry.timestamp.strftime('%Y-%m-%d')
        hour_str = entry.timestamp.strftime('%H')
        
        filename = f"entries_{date_str}_{hour_str}.json"
        if self.compression_enabled:
            filename += ".gz"
        
        return self.entries_dir / domain / filename
    
    def _entry_to_dict(self, entry: FeedbackEntry) -> Dict[str, Any]:
        """Convert FeedbackEntry to dictionary for storage."""
        return {
            'entry_id': entry.entry_id,
            'domain': entry.domain,
            'agent_id': entry.agent_id,
            'original_output': str(entry.original_output)[:1000],  # Truncate large outputs
            'processed_output': str(entry.processed_output)[:1000] if entry.processed_output else None,
            'context': entry.context,
            'feedback_type': entry.feedback_type.value,
            'confidence_score': entry.confidence_score,
            'trust_score': entry.trust_score,
            'validation_passed': entry.validation_passed,
            'validation_issues': [
                {
                    'severity': issue.get('severity').value if hasattr(issue.get('severity', ''), 'value') else str(issue.get('severity', '')),
                    'message': issue.get('message', ''),
                    'details': issue.get('details', {})
                } if isinstance(issue, dict) else str(issue)
                for issue in (entry.validation_issues or [])
            ],
            'timestamp': entry.timestamp.isoformat(),
            'processing_time_ms': entry.processing_time_ms,
            'validator_version': entry.validator_version,
            'metadata': entry.metadata,
            'recommendations': entry.recommendations
        }
    
    def _dict_to_entry(self, data: Dict[str, Any]) -> FeedbackEntry:
        """Convert dictionary to FeedbackEntry."""
        return FeedbackEntry(
            entry_id=data['entry_id'],
            domain=data['domain'],
            agent_id=data.get('agent_id'),
            original_output=data.get('original_output'),
            processed_output=data.get('processed_output'),
            context=data.get('context', {}),
            feedback_type=FeedbackType(data['feedback_type']),
            confidence_score=data.get('confidence_score', 0.0),
            trust_score=data.get('trust_score', 0.0),
            validation_passed=data.get('validation_passed', False),
            validation_issues=data.get('validation_issues', []),
            timestamp=datetime.fromisoformat(data['timestamp']),
            processing_time_ms=data.get('processing_time_ms', 0.0),
            validator_version=data.get('validator_version', '1.0.0'),
            metadata=data.get('metadata', {}),
            recommendations=data.get('recommendations', [])
        )
    
    def _write_entry_to_file(self, file_path: Path, entry_data: Dict[str, Any]) -> None:
        """Write entry data to file."""
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Read existing entries if file exists
        existing_entries = []
        if file_path.exists():
            existing_entries = self._read_file_entries(file_path)
        
        # Add new entry
        existing_entries.append(entry_data)
        
        # Write all entries back
        if self.compression_enabled:
            with gzip.open(file_path, 'wt', encoding='utf-8') as f:
                json.dump(existing_entries, f, indent=2)
        else:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(existing_entries, f, indent=2)
        
        # Check file size and rotate if necessary
        if file_path.stat().st_size > self.max_file_size:
            self._rotate_file(file_path)
    
    def _read_file_entries(self, file_path: Path) -> List[Dict[str, Any]]:
        """Read entries from a file."""
        try:
            if file_path.suffix == '.gz':
                with gzip.open(file_path, 'rt', encoding='utf-8') as f:
                    return json.load(f)
            else:
                with open(file_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            self.logger.warning(f"Failed to read file {file_path}: {str(e)}")
            return []
    
    def _filter_cache_entries(self, domain: Optional[str], agent_id: Optional[str], limit: Optional[int]) -> List[FeedbackEntry]:
        """Filter cache entries based on criteria."""
        filtered = []
        
        for entry in reversed(self.entry_cache):  # Most recent first
            if domain and entry.domain != domain:
                continue
            if agent_id and entry.agent_id != agent_id:
                continue
            
            filtered.append(entry)
            
            if limit and len(filtered) >= limit:
                break
        
        return filtered
    
    def _read_entries_from_files(self, domain: Optional[str], agent_id: Optional[str], limit: Optional[int]) -> List[FeedbackEntry]:
        """Read entries from files with filtering."""
        entries = []
        
        # Determine which directories to search
        search_dirs = []
        if domain:
            domain_dir = self.entries_dir / domain
            if domain_dir.exists():
                search_dirs.append(domain_dir)
        else:
            search_dirs = [d for d in self.entries_dir.iterdir() if d.is_dir()]
        
        # Read files in reverse chronological order
        for domain_dir in search_dirs:
            file_paths = sorted(domain_dir.glob('*.json*'), reverse=True)
            
            for file_path in file_paths:
                file_entries = self._read_file_entries(file_path)
                
                for entry_data in reversed(file_entries):  # Most recent first
                    if agent_id and entry_data.get('agent_id') != agent_id:
                        continue
                    
                    entry = self._dict_to_entry(entry_data)
                    entries.append(entry)
                    
                    if limit and len(entries) >= limit:
                        return entries
        
        return entries
    
    def _generate_analytics(self, domain: Optional[str], time_range: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate analytics data from stored entries."""
        # Get all relevant entries
        entries = self.retrieve_entries(domain=domain, limit=10000)  # Large limit for analytics
        
        # Apply time range filter if provided
        if time_range:
            start_time = datetime.fromisoformat(time_range.get('start', '1970-01-01T00:00:00'))
            end_time = datetime.fromisoformat(time_range.get('end', datetime.utcnow().isoformat()))
            entries = [e for e in entries if start_time <= e.timestamp <= end_time]
        
        # Calculate analytics
        total_entries = len(entries)
        if total_entries == 0:
            return {'total_entries': 0}
        
        # Feedback type distribution
        feedback_counts = defaultdict(int)
        for entry in entries:
            feedback_counts[entry.feedback_type.value] += 1
        
        # Domain distribution
        domain_counts = defaultdict(int)
        for entry in entries:
            domain_counts[entry.domain] += 1
        
        # Agent performance
        agent_stats = defaultdict(lambda: {'total': 0, 'correct': 0, 'partial': 0, 'incorrect': 0})
        for entry in entries:
            if entry.agent_id:
                agent_stats[entry.agent_id]['total'] += 1
                if entry.feedback_type == FeedbackType.CORRECT:
                    agent_stats[entry.agent_id]['correct'] += 1
                elif entry.feedback_type == FeedbackType.PARTIALLY_CORRECT:
                    agent_stats[entry.agent_id]['partial'] += 1
                elif entry.feedback_type == FeedbackType.INCORRECT:
                    agent_stats[entry.agent_id]['incorrect'] += 1
        
        # Calculate success rates
        success_rate = (feedback_counts['correct'] + feedback_counts['partially_correct']) / total_entries
        
        # Average scores
        avg_confidence = sum(e.confidence_score for e in entries) / total_entries
        avg_trust = sum(e.trust_score for e in entries) / total_entries
        avg_processing_time = sum(e.processing_time_ms for e in entries) / total_entries
        
        return {
            'total_entries': total_entries,
            'feedback_distribution': dict(feedback_counts),
            'domain_distribution': dict(domain_counts),
            'success_rate': success_rate,
            'average_confidence': avg_confidence,
            'average_trust': avg_trust,
            'average_processing_time_ms': avg_processing_time,
            'agent_count': len(agent_stats),
            'top_agents': sorted(
                [{'agent_id': aid, **stats} for aid, stats in agent_stats.items()],
                key=lambda x: x['correct'] + x['partial'] * 0.7,
                reverse=True
            )[:10],
            'generated_at': datetime.utcnow().isoformat()
        }
    
    def _rotate_file(self, file_path: Path) -> None:
        """Rotate file when it gets too large."""
        timestamp = datetime.utcnow().strftime('%Y%m%d_%H%M%S')
        new_name = f"{file_path.stem}_{timestamp}{file_path.suffix}"
        new_path = file_path.parent / new_name
        
        shutil.move(file_path, new_path)
        self.stats['files_created'] += 1
    
    def _backup_file(self, file_path: Path) -> None:
        """Backup file before deletion."""
        if not self.backup_enabled:
            return
        
        backup_path = self.backup_dir / file_path.relative_to(self.entries_dir)
        backup_path.parent.mkdir(parents=True, exist_ok=True)
        shutil.copy2(file_path, backup_path)
    
    def _invalidate_analytics_cache(self) -> None:
        """Invalidate analytics cache."""
        self.analytics_cache.clear()
        self.last_cache_update = datetime.min
    
    def get_storage_stats(self) -> Dict[str, Any]:
        """Get storage statistics."""
        return {
            **self.stats,
            'cache_size': len(self.entry_cache),
            'storage_path': str(self.base_path),
            'total_files': sum(1 for _ in self.entries_dir.rglob('*.json*')),
            'total_size_mb': sum(f.stat().st_size for f in self.entries_dir.rglob('*') if f.is_file()) / (1024 * 1024)
        }

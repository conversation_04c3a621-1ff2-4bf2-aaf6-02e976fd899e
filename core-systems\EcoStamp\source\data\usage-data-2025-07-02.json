{"timestamp": "2025-07-02T00:31:25.755Z", "scraped": {"openai": {"name": "OpenAI", "sources": [{"url": "https://openai.com/blog/sustainability", "timestamp": "2025-07-02T00:31:17.919Z", "error": "HTTP 403: Forbidden", "success": false}, {"url": "https://openai.com/research/gpt-4-system-card", "timestamp": "2025-07-02T00:31:19.080Z", "error": "HTTP 403: Forbidden", "success": false}]}, "anthropic": {"name": "Anthrop<PERSON> (<PERSON>)", "sources": [{"url": "https://www.anthropic.com/news/claude-2", "timestamp": "2025-07-02T00:31:20.441Z", "data": {}, "success": true}, {"url": "https://www.anthropic.com/research", "timestamp": "2025-07-02T00:31:21.669Z", "data": {}, "success": true}]}, "google": {"name": "Google (Gemini)", "sources": [{"url": "https://ai.google/responsibility/environmental-impact/", "timestamp": "2025-07-02T00:31:23.457Z", "error": "HTTP 404: Not Found", "success": false}, {"url": "https://sustainability.google/reports/", "timestamp": "2025-07-02T00:31:24.738Z", "data": {}, "success": true}]}}, "estimates": {}, "metadata": {"sources": 3, "estimationMethod": "multiplier-based", "version": "1.0"}}
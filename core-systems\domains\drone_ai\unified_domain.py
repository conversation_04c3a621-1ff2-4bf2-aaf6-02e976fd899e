"""
Unified Drone AI Domain

Consolidates all drone-based scenarios under a single Drone AI domain
with specialized sub-domains for different applications:
- General Drone Operations (sensor data, flight operations)
- Search and Rescue (SAR) Operations
- Species Tracking and Ecological Surveys
- Mining Ore Detection and Geological Surveys
- Real Estate Construction and Structural Analysis
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

from ..core.interfaces import BaseDomain
from ..core.feedback_types import ValidationResult, FeedbackType
from ..core.domain_factory import domain_factory


class UnifiedDroneAIDomain(BaseDomain):
    """
    Unified Drone AI Domain that handles all drone-based scenarios.
    
    Automatically routes different types of drone data to appropriate
    specialized sub-domains while maintaining unified analytics and
    cross-scenario learning capabilities.
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        super().__init__('drone_ai')
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # Sub-domain registry
        self.sub_domains = {}
        self.scenario_routing = {}
        
        # Processing statistics across all scenarios
        self.unified_stats = {
            'total_operations': 0,
            'by_scenario': {},
            'cross_scenario_learning': {
                'shared_patterns': 0,
                'confidence_improvements': 0,
                'trust_correlations': {}
            },
            'drone_fleet_analytics': {
                'active_drones': set(),
                'performance_by_drone': {},
                'scenario_specialization': {}
            }
        }
        
        # Initialize sub-domains
        self.initialize_components()
    
    def initialize_components(self) -> None:
        """Initialize all drone AI sub-domains."""
        try:
            # Initialize general drone operations
            self._initialize_general_drone_operations()
            
            # Initialize specialized scenarios
            self._initialize_search_rescue()
            self._initialize_species_tracking()
            self._initialize_mining_ore()
            self._initialize_construction()
            
            # Setup scenario routing
            self._setup_scenario_routing()
            
            self.logger.info("Initialized Unified Drone AI Domain with all sub-domains")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Unified Drone AI Domain: {str(e)}")
            raise
    
    def _initialize_general_drone_operations(self) -> None:
        """Initialize general drone operations sub-domain."""
        from . import DroneAIDomain
        
        general_config = self.config.get('general_operations', {})
        self.sub_domains['general_operations'] = DroneAIDomain(general_config)
        
        self.scenario_routing.update({
            'sensor_data': 'general_operations',
            'flight_operations': 'general_operations',
            'navigation_data': 'general_operations',
            'telemetry': 'general_operations'
        })
    
    def _initialize_search_rescue(self) -> None:
        """Initialize Search and Rescue sub-domain."""
        try:
            from ..search_rescue import SearchRescueDomain
            
            sar_config = self.config.get('search_rescue', {})
            self.sub_domains['search_rescue'] = SearchRescueDomain(sar_config)
            
            self.scenario_routing.update({
                'search_rescue': 'search_rescue',
                'lost_person': 'search_rescue',
                'emergency_response': 'search_rescue',
                'target_person': 'search_rescue',
                'mission_id': 'search_rescue',
                'clothing': 'search_rescue',
                'personal_items': 'search_rescue'
            })
            
        except ImportError:
            self.logger.warning("Search and Rescue domain not available")
    
    def _initialize_species_tracking(self) -> None:
        """Initialize Species Tracking sub-domain."""
        try:
            from ..species_tracking import SpeciesTrackingDomain
            
            species_config = self.config.get('species_tracking', {})
            self.sub_domains['species_tracking'] = SpeciesTrackingDomain(species_config)
            
            self.scenario_routing.update({
                'species_tracking': 'species_tracking',
                'bird': 'species_tracking',
                'wildlife': 'species_tracking',
                'ecological_survey': 'species_tracking',
                'biodiversity': 'species_tracking',
                'habitat': 'species_tracking',
                'survey_id': 'species_tracking'
            })
            
        except ImportError:
            self.logger.warning("Species Tracking domain not available")
    
    def _initialize_mining_ore(self) -> None:
        """Initialize Mining Ore Detection sub-domain."""
        try:
            from ..mining_ore import MiningOreDomain
            
            mining_config = self.config.get('mining_ore', {})
            self.sub_domains['mining_ore'] = MiningOreDomain(mining_config)
            
            self.scenario_routing.update({
                'mining_ore': 'mining_ore',
                'mineral': 'mining_ore',
                'ore': 'mining_ore',
                'geological': 'mining_ore',
                'lidar': 'mining_ore',
                'density_g_cm3': 'mining_ore',
                'gold': 'mining_ore',
                'silver': 'mining_ore',
                'copper': 'mining_ore'
            })
            
        except ImportError:
            self.logger.warning("Mining Ore domain not available")
    
    def _initialize_construction(self) -> None:
        """Initialize Real Estate Construction sub-domain."""
        try:
            from ..real_estate_construction import RealEstateConstructionDomain
            
            construction_config = self.config.get('real_estate_construction', {})
            self.sub_domains['real_estate_construction'] = RealEstateConstructionDomain(construction_config)
            
            self.scenario_routing.update({
                'real_estate_construction': 'real_estate_construction',
                'building': 'real_estate_construction',
                'construction': 'real_estate_construction',
                'structural': 'real_estate_construction',
                'foundation': 'real_estate_construction',
                'construction_phase': 'real_estate_construction',
                'material': 'real_estate_construction',
                'project_id': 'real_estate_construction'
            })
            
        except ImportError:
            self.logger.warning("Real Estate Construction domain not available")
    
    def _setup_scenario_routing(self) -> None:
        """Setup intelligent scenario routing based on data patterns."""
        # Add pattern-based routing rules
        self.pattern_routing = {
            'search_rescue_patterns': [
                'target_found', 'reference_items', 'mission_complete', 'search_area'
            ],
            'species_tracking_patterns': [
                'carolina_chickadee', 'bird_species', 'ecological_survey', 'biodiversity_metrics'
            ],
            'mining_ore_patterns': [
                'ore_detection', 'mineral_classification', 'geological_formation', 'lidar_scan'
            ],
            'construction_patterns': [
                'building_inspection', 'structural_analysis', 'construction_progress', 'material_verification'
            ]
        }
    
    def detect_scenario(self, raw_output: Any, context: Dict[str, Any]) -> str:
        """
        Automatically detect the appropriate drone scenario based on input data.
        
        Args:
            raw_output: Raw drone data
            context: Operation context
            
        Returns:
            Scenario name for routing to appropriate sub-domain
        """
        try:
            # Convert input to searchable text
            search_text = self._extract_searchable_text(raw_output, context)
            
            # Check explicit scenario indicators
            for indicator, scenario in self.scenario_routing.items():
                if indicator.lower() in search_text.lower():
                    return scenario
            
            # Check pattern-based routing
            scenario_scores = {}
            for scenario_type, patterns in self.pattern_routing.items():
                score = sum(1 for pattern in patterns if pattern.lower() in search_text.lower())
                if score > 0:
                    scenario_name = scenario_type.replace('_patterns', '')
                    scenario_scores[scenario_name] = score
            
            # Return best matching scenario
            if scenario_scores:
                best_scenario = max(scenario_scores.items(), key=lambda x: x[1])[0]
                return best_scenario
            
            # Default to general operations
            return 'general_operations'
            
        except Exception as e:
            self.logger.error(f"Error detecting drone scenario: {str(e)}")
            return 'general_operations'
    
    def _extract_searchable_text(self, raw_output: Any, context: Dict[str, Any]) -> str:
        """Extract searchable text from drone data."""
        text_parts = []
        
        # Add context information
        if isinstance(context, dict):
            text_parts.extend(str(k).lower() for k in context.keys())
            text_parts.extend(str(v).lower() for v in context.values())
        
        # Add raw output information
        if isinstance(raw_output, dict):
            text_parts.extend(str(k).lower() for k in raw_output.keys())
            text_parts.extend(str(v).lower() for v in raw_output.values())
        else:
            text_parts.append(str(raw_output).lower())
        
        return ' '.join(text_parts)
    
    def process_output(self, raw_output: Any, context: Dict[str, Any]) -> ValidationResult:
        """
        Process drone output through appropriate sub-domain.
        
        Args:
            raw_output: Raw drone data
            context: Operation context including drone_id, mission_type, etc.
            
        Returns:
            ValidationResult with scenario-specific analysis and unified metadata
        """
        try:
            start_time = datetime.utcnow()
            
            # Detect appropriate scenario
            scenario = self.detect_scenario(raw_output, context)
            
            # Get sub-domain
            sub_domain = self.sub_domains.get(scenario)
            if not sub_domain:
                self.logger.warning(f"Sub-domain not found for scenario: {scenario}")
                scenario = 'general_operations'
                sub_domain = self.sub_domains.get('general_operations')
            
            if not sub_domain:
                raise ValueError("No available sub-domains for drone processing")
            
            # Process through sub-domain
            result = sub_domain.process_output(raw_output, context)
            
            # Add unified drone AI metadata
            result.metadata.update({
                'unified_domain': 'drone_ai',
                'scenario': scenario,
                'sub_domain': scenario,
                'drone_id': context.get('drone_id', 'unknown'),
                'flight_mission': context.get('mission_type', context.get('survey_type', 'unknown')),
                'processing_route': f'drone_ai.{scenario}',
                'cross_scenario_learning': True
            })
            
            # Update unified statistics
            processing_time = (datetime.utcnow() - start_time).total_seconds() * 1000
            self._update_unified_stats(scenario, result, processing_time, context)
            
            # Apply cross-scenario learning
            self._apply_cross_scenario_learning(scenario, result, context)
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error processing drone output: {str(e)}")
            
            # Return error result
            error_result = ValidationResult()
            error_result.is_valid = False
            error_result.confidence_score = 0.0
            error_result.feedback_type = FeedbackType.INCORRECT
            error_result.issues = [{
                'type': 'unified_domain_error',
                'severity': 'critical',
                'message': f'Unified Drone AI processing failed: {str(e)}',
                'details': {'error': str(e), 'domain': 'drone_ai'}
            }]
            error_result.metadata = {
                'unified_domain': 'drone_ai',
                'error': True,
                'error_message': str(e)
            }
            
            return error_result
    
    def _update_unified_stats(self, scenario: str, result: ValidationResult, 
                            processing_time: float, context: Dict[str, Any]) -> None:
        """Update unified statistics across all drone scenarios."""
        self.unified_stats['total_operations'] += 1
        
        # Update scenario statistics
        if scenario not in self.unified_stats['by_scenario']:
            self.unified_stats['by_scenario'][scenario] = {
                'count': 0,
                'success_rate': 0.0,
                'avg_confidence': 0.0,
                'avg_processing_time': 0.0
            }
        
        scenario_stats = self.unified_stats['by_scenario'][scenario]
        scenario_stats['count'] += 1
        
        # Update success rate
        is_success = result.feedback_type in [FeedbackType.CORRECT, FeedbackType.PARTIALLY_CORRECT]
        total_success = scenario_stats['success_rate'] * (scenario_stats['count'] - 1) + (1 if is_success else 0)
        scenario_stats['success_rate'] = total_success / scenario_stats['count']
        
        # Update average confidence
        total_confidence = scenario_stats['avg_confidence'] * (scenario_stats['count'] - 1) + result.confidence_score
        scenario_stats['avg_confidence'] = total_confidence / scenario_stats['count']
        
        # Update average processing time
        total_time = scenario_stats['avg_processing_time'] * (scenario_stats['count'] - 1) + processing_time
        scenario_stats['avg_processing_time'] = total_time / scenario_stats['count']
        
        # Update drone fleet analytics
        drone_id = context.get('drone_id', 'unknown')
        if drone_id != 'unknown':
            self.unified_stats['drone_fleet_analytics']['active_drones'].add(drone_id)
            
            if drone_id not in self.unified_stats['drone_fleet_analytics']['performance_by_drone']:
                self.unified_stats['drone_fleet_analytics']['performance_by_drone'][drone_id] = {
                    'operations': 0,
                    'scenarios': set(),
                    'avg_confidence': 0.0,
                    'success_rate': 0.0
                }
            
            drone_stats = self.unified_stats['drone_fleet_analytics']['performance_by_drone'][drone_id]
            drone_stats['operations'] += 1
            drone_stats['scenarios'].add(scenario)
            
            # Update drone confidence
            total_confidence = drone_stats['avg_confidence'] * (drone_stats['operations'] - 1) + result.confidence_score
            drone_stats['avg_confidence'] = total_confidence / drone_stats['operations']
            
            # Update drone success rate
            total_success = drone_stats['success_rate'] * (drone_stats['operations'] - 1) + (1 if is_success else 0)
            drone_stats['success_rate'] = total_success / drone_stats['operations']
    
    def _apply_cross_scenario_learning(self, scenario: str, result: ValidationResult, context: Dict[str, Any]) -> None:
        """Apply learning insights across different drone scenarios."""
        try:
            # Identify patterns that could benefit other scenarios
            if result.confidence_score > 0.8:
                # High confidence result - extract learnable patterns
                self.unified_stats['cross_scenario_learning']['confidence_improvements'] += 1
                
                # Check for shared environmental factors
                environmental_factors = ['weather', 'lighting', 'altitude', 'terrain']
                for factor in environmental_factors:
                    if factor in context:
                        # This could inform other scenarios operating in similar conditions
                        pass
            
            # Track trust correlations between scenarios
            drone_id = context.get('drone_id', 'unknown')
            if drone_id != 'unknown':
                if drone_id not in self.unified_stats['cross_scenario_learning']['trust_correlations']:
                    self.unified_stats['cross_scenario_learning']['trust_correlations'][drone_id] = {}
                
                drone_correlations = self.unified_stats['cross_scenario_learning']['trust_correlations'][drone_id]
                if scenario not in drone_correlations:
                    drone_correlations[scenario] = []
                
                drone_correlations[scenario].append(result.confidence_score)
                
                # Keep only recent scores for correlation analysis
                if len(drone_correlations[scenario]) > 10:
                    drone_correlations[scenario] = drone_correlations[scenario][-10:]
            
        except Exception as e:
            self.logger.error(f"Error in cross-scenario learning: {str(e)}")
    
    def get_domain_info(self) -> Dict[str, Any]:
        """Get comprehensive information about the unified drone AI domain."""
        base_info = super().get_domain_info()
        
        # Get sub-domain information
        sub_domain_info = {}
        for scenario, sub_domain in self.sub_domains.items():
            if hasattr(sub_domain, 'get_domain_info'):
                sub_domain_info[scenario] = sub_domain.get_domain_info()
            else:
                sub_domain_info[scenario] = {'name': scenario, 'available': True}
        
        unified_info = {
            'domain_type': 'unified_drone_ai',
            'available_scenarios': list(self.sub_domains.keys()),
            'sub_domains': sub_domain_info,
            'scenario_routing': self.scenario_routing,
            'unified_statistics': self.unified_stats,
            'capabilities': {
                'multi_scenario': True,
                'automatic_routing': True,
                'cross_scenario_learning': True,
                'fleet_analytics': True,
                'unified_trust_tracking': True
            },
            'supported_drone_types': [
                'search_rescue_drones',
                'ecological_survey_drones',
                'mining_survey_drones',
                'construction_inspection_drones',
                'general_purpose_drones'
            ]
        }
        
        return {**base_info, **unified_info}
    
    def get_scenario_analytics(self, scenario: str = None) -> Dict[str, Any]:
        """Get analytics for specific scenario or all scenarios."""
        if scenario:
            return self.unified_stats['by_scenario'].get(scenario, {})
        else:
            return self.unified_stats['by_scenario'].copy()
    
    def get_fleet_analytics(self) -> Dict[str, Any]:
        """Get drone fleet analytics across all scenarios."""
        fleet_stats = self.unified_stats['drone_fleet_analytics'].copy()
        
        # Convert sets to lists for JSON serialization
        fleet_stats['active_drones'] = list(fleet_stats['active_drones'])
        for drone_id, stats in fleet_stats['performance_by_drone'].items():
            stats['scenarios'] = list(stats['scenarios'])
        
        return fleet_stats
    
    def get_cross_scenario_insights(self) -> Dict[str, Any]:
        """Get insights from cross-scenario learning."""
        return self.unified_stats['cross_scenario_learning'].copy()

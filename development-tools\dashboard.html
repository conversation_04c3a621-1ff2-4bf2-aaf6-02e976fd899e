<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔒 Universal VS Code Security Scanner</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #2c5aa0 0%, #1e3c72 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 { font-size: 2.5em; margin-bottom: 10px; }
        .content { padding: 30px; }
        .command-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .command-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #2c5aa0;
            transition: transform 0.3s ease;
        }
        .command-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        .command-title {
            font-size: 1.2em;
            font-weight: bold;
            color: #2c5aa0;
            margin-bottom: 10px;
        }
        .command-code {
            background: #e9ecef;
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            word-break: break-all;
        }
        .features {
            background: #d1ecf1;
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #17a2b8;
        }
        .location {
            background: #fff3cd;
            padding: 15px;
            border-radius: 8px;
            border-left: 5px solid #ffc107;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔒 Universal Security Scanner</h1>
            <p>Enterprise-grade security for all your VS Code projects</p>
            <p><small>Solo Developer Security Solutions</small></p>
        </div>
        
        <div class="content">
            <div class="location">
                <h3>📍 Scanner Location</h3>
                <p><strong>Path:</strong> <code>c:\Users\<USER>\VSCode_Security_Scanner</code></p>
                <p><strong>Status:</strong> ✅ Ready to scan any project</p>
            </div>

            <h2>🚀 How to Use</h2>
            <div class="command-grid">
                <div class="command-card">
                    <div class="command-title">⚡ Quick Scan Any Project</div>
                    <div class="command-code">node c:\Users\<USER>\VSCode_Security_Scanner\scanner.js --quick --path "C:\path\to\your\project"</div>
                    <p>Fast vulnerability and license check for any VS Code project</p>
                </div>
                
                <div class="command-card">
                    <div class="command-title">🔍 Full Security Analysis</div>
                    <div class="command-code">node c:\Users\<USER>\VSCode_Security_Scanner\scanner.js --full --path "C:\path\to\your\project"</div>
                    <p>Complete security analysis with detailed reports</p>
                </div>
                
                <div class="command-card">
                    <div class="command-title">📊 Generate Professional Report</div>
                    <div class="command-code">node c:\Users\<USER>\VSCode_Security_Scanner\scanner.js --report --path "C:\path\to\your\project"</div>
                    <p>Create detailed HTML/JSON security reports</p>
                </div>
                
                <div class="command-card">
                    <div class="command-title">📦 SBOM Generation</div>
                    <div class="command-code">node c:\Users\<USER>\VSCode_Security_Scanner\scanner.js --sbom --path "C:\path\to\your\project"</div>
                    <p>Generate Software Bill of Materials for compliance</p>
                </div>
                
                <div class="command-card">
                    <div class="command-title">📜 License Compliance</div>
                    <div class="command-code">node c:\Users\<USER>\VSCode_Security_Scanner\scanner.js --licenses --path "C:\path\to\your\project"</div>
                    <p>Check open-source license compliance</p>
                </div>
                
                <div class="command-card">
                    <div class="command-title">🛡️ Vulnerability Scan</div>
                    <div class="command-code">node c:\Users\<USER>\VSCode_Security_Scanner\scanner.js --vulnerabilities --path "C:\path\to\your\project"</div>
                    <p>Check for security vulnerabilities only</p>
                </div>
            </div>
            
            <div class="features">
                <h3>🎯 Enterprise Features for Solo Developers</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin-top: 15px;">
                    <div>✅ Multi-language Support</div>
                    <div>✅ CVE Detection</div>
                    <div>✅ License Compliance</div>
                    <div>✅ SBOM Generation</div>
                    <div>✅ Code Security Analysis</div>
                    <div>✅ Professional Reports</div>
                    <div>✅ Project Auto-detection</div>
                    <div>✅ Universal Compatibility</div>
                </div>
            </div>

            <div style="background: #d4edda; padding: 20px; border-radius: 10px; border-left: 5px solid #28a745; margin-top: 20px;">
                <h3>💡 Quick Start Examples</h3>
                <p><strong>Scan EcoStamp Project:</strong></p>
                <div class="command-code">node c:\Users\<USER>\VSCode_Security_Scanner\scanner.js --quick --path "c:\Users\<USER>\Time_Stamp_Project\EcoStamp\source"</div>
                
                <p style="margin-top: 15px;"><strong>Scan Any New Project:</strong></p>
                <div class="command-code">cd "C:\path\to\your\new\project"<br>node c:\Users\<USER>\VSCode_Security_Scanner\scanner.js --full</div>
            </div>
            
            <div style="margin-top: 30px; text-align: center; color: #6c757d;">
                <p>🌱 <strong>Universal Security Scanner</strong> - Protecting all your VS Code projects!</p>
                <p><small>Enterprise-grade security for solo developers - 100% FREE</small></p>
            </div>
        </div>
    </div>
</body>
</html>

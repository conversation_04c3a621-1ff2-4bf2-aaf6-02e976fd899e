"""
AI Coding Assistant Orchestration System

A compliant orchestration system that connects and coordinates branded and patented 
AI coding assistants (like Copilot, Tabnine, Amazon Q, etc.) while respecting 
all licensing, branding, and patent restrictions.

Key Features:
- User-provided credentials for all branded tools
- No hosting, reselling, or modification of branded tools
- Respect for all branding, copyright, and patent restrictions
- Dynamic agent assignment and role-based delegation
- Combinatorial optimization of agent-role combinations
- Continuous learning and workflow improvement
- Shared context management across agents
- Comprehensive compliance validation
- Performance monitoring and analytics

Example Usage:
    from ai_orchestration_system import create_orchestration_engine
    
    # Create orchestration engine
    orchestrator = create_orchestration_engine({
        'orchestration_mode': 'adaptive',
        'performance_learning': True,
        'compliance': {'strict_mode': True}
    })
    
    # Execute task with automatic agent selection
    result = await orchestrator.execute_task(
        task_description="Implement OAuth2 authentication",
        task_type="code_generation",
        context={'language': 'python', 'framework': 'fastapi'}
    )
    
    print(f"Success: {result.success}")
    print(f"Quality Score: {result.quality_score}")
"""

__version__ = "1.0.0"
__author__ = "AI Orchestration Team"
__email__ = "<EMAIL>"
__description__ = "Compliant AI Coding Assistant Orchestration System"

# Core exports
from .core.orchestration_engine import OrchestrationEngine, create_orchestration_engine
from .core.legal_compliance import (
    ComplianceManager, BrandingManager, LicenseValidator,
    LicenseStatus, BrandingRequirement, VendorCompliance
)
from .core.agent_registry import (
    AgentRegistry, AgentRole, AgentStatus, AgentCapability,
    AgentInfo, AgentPerformance
)
from .core.combinatorial_matrix import (
    CombinatorialMatrix, AgentRoleCombination, CombinationPerformance,
    CombinationType, OptimizationGoal
)
from .core.shared_context import (
    SharedContext, SessionState, ContextEntry,
    ContextScope, ContextPermission
)
from .core.workflow_executor import (
    WorkflowExecutor, TaskResult, ExecutionContext,
    TaskStatus, ExecutionMode
)

# Utility functions
def create_default_orchestration_engine(config=None):
    """
    Create an orchestration engine with default configuration.
    
    Args:
        config: Optional configuration dictionary
        
    Returns:
        Configured OrchestrationEngine instance
    """
    default_config = {
        'orchestration_mode': 'adaptive',
        'max_concurrent_tasks': 10,
        'performance_learning': True,
        'compliance': {
            'strict_mode': True,
            'compliance_checks_enabled': True
        },
        'agents': {
            'health_check_interval': 60,
            'performance_tracking': True,
            'auto_discovery': True
        },
        'context': {
            'default_ttl_seconds': 3600,
            'auto_cleanup': True,
            'enable_persistence': False
        },
        'workflow': {
            'default_timeout_seconds': 300,
            'max_retries': 3,
            'enable_fallback': True,
            'enable_learning': True
        }
    }
    
    if config:
        # Deep merge configuration
        def deep_merge(base, override):
            for key, value in override.items():
                if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                    deep_merge(base[key], value)
                else:
                    base[key] = value
        
        deep_merge(default_config, config)
    
    return create_orchestration_engine(default_config)


def get_supported_assistants():
    """
    Get list of supported branded AI assistants.
    
    Returns:
        Dictionary of supported assistants with their capabilities
    """
    return {
        'github_copilot': {
            'name': 'GitHub Copilot',
            'vendor': 'GitHub, Inc.',
            'supported_roles': ['generator', 'completer', 'analyzer'],
            'capabilities': ['code_generation', 'code_completion', 'code_analysis'],
            'programming_languages': ['python', 'javascript', 'typescript', 'java', 'c#', 'c++', 'go', 'rust'],
            'frameworks': ['react', 'vue', 'angular', 'django', 'flask', 'spring'],
            'api_endpoint': 'https://api.github.com/copilot',
            'documentation': 'https://docs.github.com/en/copilot'
        },
        'tabnine': {
            'name': 'Tabnine',
            'vendor': 'Codota Ltd.',
            'supported_roles': ['completer', 'generator', 'optimizer'],
            'capabilities': ['code_completion', 'code_generation', 'optimization'],
            'programming_languages': ['python', 'javascript', 'typescript', 'java', 'c#', 'c++', 'go', 'kotlin'],
            'frameworks': ['tensorflow', 'pytorch', 'react', 'angular', 'spring'],
            'api_endpoint': 'https://api.tabnine.com',
            'documentation': 'https://www.tabnine.com/docs'
        },
        'amazon_q': {
            'name': 'Amazon Q Developer',
            'vendor': 'Amazon.com, Inc.',
            'supported_roles': ['analyzer', 'validator', 'reviewer', 'debugger'],
            'capabilities': ['code_analysis', 'code_review', 'security_analysis', 'debugging'],
            'programming_languages': ['python', 'java', 'javascript', 'typescript', 'c#', 'go'],
            'frameworks': ['aws-sdk', 'boto3', 'lambda', 'dynamodb'],
            'api_endpoint': 'https://q.aws.amazon.com/api',
            'documentation': 'https://docs.aws.amazon.com/amazonq/'
        },
        'cursor': {
            'name': 'Cursor',
            'vendor': 'Anysphere, Inc.',
            'supported_roles': ['generator', 'analyzer', 'validator', 'documenter'],
            'capabilities': ['code_generation', 'code_analysis', 'documentation'],
            'programming_languages': ['python', 'javascript', 'typescript', 'rust', 'go', 'c++'],
            'frameworks': ['react', 'nextjs', 'svelte', 'fastapi'],
            'api_endpoint': 'https://api.cursor.sh',
            'documentation': 'https://cursor.sh/docs'
        },
        'qodo_ai': {
            'name': 'QodoAI',
            'vendor': 'Qodo Ltd.',
            'supported_roles': ['validator', 'reviewer', 'analyzer'],
            'capabilities': ['testing', 'code_review', 'security_analysis'],
            'programming_languages': ['python', 'javascript', 'typescript', 'java', 'c#'],
            'frameworks': ['pytest', 'jest', 'junit', 'mocha'],
            'api_endpoint': 'https://api.qodo.ai',
            'documentation': 'https://qodo.ai/docs'
        }
    }


# Package metadata
__all__ = [
    # Core classes
    'OrchestrationEngine',
    'create_orchestration_engine',
    'ComplianceManager',
    'BrandingManager',
    'LicenseValidator',
    'AgentRegistry',
    'CombinatorialMatrix',
    'SharedContext',
    'WorkflowExecutor',
    
    # Enums and data classes
    'AgentRole',
    'AgentStatus',
    'AgentCapability',
    'LicenseStatus',
    'BrandingRequirement',
    'CombinationType',
    'OptimizationGoal',
    'ContextScope',
    'ContextPermission',
    'TaskStatus',
    'ExecutionMode',
    
    # Data structures
    'VendorCompliance',
    'AgentInfo',
    'AgentPerformance',
    'AgentRoleCombination',
    'CombinationPerformance',
    'SessionState',
    'ContextEntry',
    'TaskResult',
    'ExecutionContext',
    
    # Utility functions
    'create_default_orchestration_engine',
    'get_supported_assistants'
]

/**
 * Universal Task Router
 * 
 * Routes tasks between Thread-Merge Orchestration, Code Orchestration,
 * and external orchestrators based on task type, context, and performance.
 */

const EventEmitter = require('events');
const chalk = require('chalk');
const { v4: uuidv4 } = require('uuid');

class UniversalTaskRouter extends EventEmitter {
  constructor(universalOrchestrator) {
    super();
    this.universalOrchestrator = universalOrchestrator;
    
    // Task type registry
    this.taskTypes = new Map();
    
    // Routing rules and strategies
    this.routingRules = new Map();
    
    // Performance tracking for routing decisions
    this.routingMetrics = new Map();
    
    // Active routing sessions
    this.activeSessions = new Map();
    
    // Default routing strategies
    this.defaultStrategies = {
      'performance-based': this.performanceBasedRouting.bind(this),
      'capability-based': this.capabilityBasedRouting.bind(this),
      'load-balanced': this.loadBalancedRouting.bind(this),
      'hybrid': this.hybridRouting.bind(this)
    };
  }
  
  async initialize() {
    try {
      console.log(chalk.blue('🗺️ Initializing Universal Task Router...'));
      
      // Setup default routing rules
      this.setupDefaultRoutingRules();
      
      // Initialize performance tracking
      this.initializePerformanceTracking();
      
      console.log(chalk.green('✅ Universal Task Router initialized'));
      
    } catch (error) {
      console.error(chalk.red('❌ Failed to initialize Universal Task Router:'), error);
      throw error;
    }
  }
  
  setupDefaultRoutingRules() {
    // Code-centric tasks -> Meta-Orchestrator
    this.addRoutingRule('code-analysis', {
      primaryOrchestrator: 'meta',
      fallbackOrchestrators: ['ai'],
      strategy: 'capability-based',
      requiredCapabilities: ['codebase-analysis']
    });
    
    this.addRoutingRule('code-generation', {
      primaryOrchestrator: 'meta',
      fallbackOrchestrators: ['ai'],
      strategy: 'performance-based',
      requiredCapabilities: ['code-generation']
    });
    
    this.addRoutingRule('feature-implementation', {
      primaryOrchestrator: 'meta',
      fallbackOrchestrators: ['ai'],
      strategy: 'hybrid',
      requiredCapabilities: ['code-generation', 'validation']
    });
    
    // Thread-centric tasks -> Thread Merging Orchestrator
    this.addRoutingRule('thread-merge', {
      primaryOrchestrator: 'thread',
      fallbackOrchestrators: ['meta'],
      strategy: 'capability-based',
      requiredCapabilities: ['thread-merging']
    });
    
    this.addRoutingRule('conversation-orchestration', {
      primaryOrchestrator: 'thread',
      fallbackOrchestrators: ['meta'],
      strategy: 'performance-based',
      requiredCapabilities: ['conversation-management']
    });
    
    // Hybrid tasks -> Universal coordination
    this.addRoutingRule('project-planning', {
      primaryOrchestrator: 'universal',
      fallbackOrchestrators: ['meta', 'thread'],
      strategy: 'hybrid',
      requiredCapabilities: ['analysis', 'planning', 'coordination']
    });
    
    this.addRoutingRule('architecture-design', {
      primaryOrchestrator: 'universal',
      fallbackOrchestrators: ['meta'],
      strategy: 'hybrid',
      requiredCapabilities: ['architecture-analysis', 'design', 'documentation']
    });
    
    // External orchestrator tasks
    this.addRoutingRule('ml-training', {
      primaryOrchestrator: 'external:kubeflow',
      fallbackOrchestrators: ['external:prefect', 'meta'],
      strategy: 'capability-based',
      requiredCapabilities: ['ml-orchestration']
    });
    
    this.addRoutingRule('ci-cd-deployment', {
      primaryOrchestrator: 'external:jenkins',
      fallbackOrchestrators: ['external:airflow', 'meta'],
      strategy: 'performance-based',
      requiredCapabilities: ['ci-cd-orchestration']
    });
  }
  
  addRoutingRule(taskType, rule) {
    this.routingRules.set(taskType, rule);
    console.log(chalk.cyan(`📋 Added routing rule for ${taskType}`));
  }
  
  initializePerformanceTracking() {
    // Initialize metrics for each orchestrator
    const orchestrators = ['meta', 'thread', 'ai', 'universal'];
    
    for (const orchestrator of orchestrators) {
      this.routingMetrics.set(orchestrator, {
        totalRequests: 0,
        successfulRequests: 0,
        averageResponseTime: 0,
        currentLoad: 0,
        capabilities: new Set(),
        lastHealthCheck: Date.now()
      });
    }
  }
  
  async registerTaskType(taskType, config) {
    this.taskTypes.set(taskType, {
      ...config,
      registeredAt: Date.now()
    });
    
    console.log(chalk.green(`✅ Registered task type: ${taskType}`));
  }
  
  /**
   * Route a task to the best available orchestrator
   */
  async routeTask(taskType, request, context = {}) {
    const sessionId = uuidv4();
    const startTime = Date.now();
    
    try {
      console.log(chalk.blue(`🗺️ Routing task ${taskType} (session: ${sessionId})`));
      
      // Get routing rule for task type
      const routingRule = this.getRoutingRule(taskType);
      
      // Determine best orchestrator using routing strategy
      const selectedOrchestrator = await this.selectOrchestrator(taskType, routingRule, context);
      
      // Create routing session
      this.activeSessions.set(sessionId, {
        taskType,
        selectedOrchestrator,
        startTime,
        status: 'routing',
        request,
        context
      });
      
      // Execute routing
      const result = await this.executeRouting(sessionId, selectedOrchestrator, request, context);
      
      // Update session
      const session = this.activeSessions.get(sessionId);
      session.status = 'completed';
      session.endTime = Date.now();
      session.result = result;
      
      // Update metrics
      const duration = Date.now() - startTime;
      this.updateRoutingMetrics(selectedOrchestrator, true, duration);
      
      console.log(chalk.green(`✅ Task routed to ${selectedOrchestrator} in ${duration}ms`));
      
      return result;
      
    } catch (error) {
      // Update session
      const session = this.activeSessions.get(sessionId);
      if (session) {
        session.status = 'failed';
        session.endTime = Date.now();
        session.error = error.message;
      }
      
      console.error(chalk.red(`❌ Task routing failed:`, error.message));
      
      // Attempt fallback routing
      return await this.attemptFallbackRouting(taskType, request, context, error);
    }
  }
  
  getRoutingRule(taskType) {
    // Get specific rule or use default
    return this.routingRules.get(taskType) || {
      primaryOrchestrator: 'meta',
      fallbackOrchestrators: ['ai'],
      strategy: 'performance-based',
      requiredCapabilities: []
    };
  }
  
  async selectOrchestrator(taskType, routingRule, context) {
    const { strategy, primaryOrchestrator, fallbackOrchestrators } = routingRule;
    
    // Use routing strategy to select orchestrator
    const strategyFunction = this.defaultStrategies[strategy];
    
    if (strategyFunction) {
      return await strategyFunction(taskType, routingRule, context);
    } else {
      // Default to primary orchestrator
      return primaryOrchestrator;
    }
  }
  
  async performanceBasedRouting(taskType, routingRule, context) {
    const { primaryOrchestrator, fallbackOrchestrators } = routingRule;
    const candidates = [primaryOrchestrator, ...fallbackOrchestrators];
    
    let bestOrchestrator = primaryOrchestrator;
    let bestScore = 0;
    
    for (const orchestrator of candidates) {
      const metrics = this.routingMetrics.get(orchestrator);
      
      if (metrics && this.isOrchestratorAvailable(orchestrator)) {
        // Calculate performance score
        const successRate = metrics.successfulRequests / Math.max(metrics.totalRequests, 1);
        const responseScore = 1 / Math.max(metrics.averageResponseTime, 1);
        const loadScore = 1 / Math.max(metrics.currentLoad, 1);
        
        const score = successRate * 0.5 + responseScore * 0.3 + loadScore * 0.2;
        
        if (score > bestScore) {
          bestScore = score;
          bestOrchestrator = orchestrator;
        }
      }
    }
    
    return bestOrchestrator;
  }
  
  async capabilityBasedRouting(taskType, routingRule, context) {
    const { primaryOrchestrator, fallbackOrchestrators, requiredCapabilities } = routingRule;
    const candidates = [primaryOrchestrator, ...fallbackOrchestrators];
    
    // Find orchestrator with best capability match
    for (const orchestrator of candidates) {
      if (this.isOrchestratorAvailable(orchestrator) && 
          this.hasRequiredCapabilities(orchestrator, requiredCapabilities)) {
        return orchestrator;
      }
    }
    
    // Fallback to primary if no perfect match
    return primaryOrchestrator;
  }
  
  async loadBalancedRouting(taskType, routingRule, context) {
    const { primaryOrchestrator, fallbackOrchestrators } = routingRule;
    const candidates = [primaryOrchestrator, ...fallbackOrchestrators];
    
    let leastLoadedOrchestrator = primaryOrchestrator;
    let lowestLoad = Infinity;
    
    for (const orchestrator of candidates) {
      const metrics = this.routingMetrics.get(orchestrator);
      
      if (metrics && this.isOrchestratorAvailable(orchestrator) && 
          metrics.currentLoad < lowestLoad) {
        lowestLoad = metrics.currentLoad;
        leastLoadedOrchestrator = orchestrator;
      }
    }
    
    return leastLoadedOrchestrator;
  }
  
  async hybridRouting(taskType, routingRule, context) {
    // Combine performance and capability-based routing
    const performanceChoice = await this.performanceBasedRouting(taskType, routingRule, context);
    const capabilityChoice = await this.capabilityBasedRouting(taskType, routingRule, context);
    
    // Prefer capability match if performance is similar
    const performanceMetrics = this.routingMetrics.get(performanceChoice);
    const capabilityMetrics = this.routingMetrics.get(capabilityChoice);
    
    if (performanceMetrics && capabilityMetrics) {
      const performanceScore = performanceMetrics.successfulRequests / Math.max(performanceMetrics.totalRequests, 1);
      const capabilityScore = capabilityMetrics.successfulRequests / Math.max(capabilityMetrics.totalRequests, 1);
      
      // If capability choice is within 10% of performance choice, prefer capability
      if (capabilityScore >= performanceScore * 0.9) {
        return capabilityChoice;
      }
    }
    
    return performanceChoice;
  }
  
  isOrchestratorAvailable(orchestrator) {
    // Check if orchestrator is available and healthy
    if (orchestrator.startsWith('external:')) {
      const externalId = orchestrator.replace('external:', '');
      const adapter = this.universalOrchestrator.metaOrchestrationAdapter;
      const available = adapter.getAvailableOrchestrators();
      return available.some(orch => orch.id === externalId && orch.status === 'ready');
    }
    
    // Check internal orchestrators
    switch (orchestrator) {
      case 'meta':
        return this.universalOrchestrator.metaOrchestrator?.state?.status === 'ready';
      case 'thread':
        return this.universalOrchestrator.threadMergingOrchestrator?.state?.status === 'ready';
      case 'ai':
        return this.universalOrchestrator.aiOrchestrator?.state?.status === 'ready';
      case 'universal':
        return true; // Universal orchestrator is always available
      default:
        return false;
    }
  }
  
  hasRequiredCapabilities(orchestrator, requiredCapabilities) {
    if (!requiredCapabilities || requiredCapabilities.length === 0) {
      return true;
    }
    
    const metrics = this.routingMetrics.get(orchestrator);
    if (!metrics) return false;
    
    return requiredCapabilities.every(capability => 
      metrics.capabilities.has(capability)
    );
  }
  
  async executeRouting(sessionId, orchestrator, request, context) {
    console.log(chalk.cyan(`🎯 Executing routing to ${orchestrator}`));
    
    if (orchestrator.startsWith('external:')) {
      // Route to external orchestrator
      const externalId = orchestrator.replace('external:', '');
      return await this.universalOrchestrator.metaOrchestrationAdapter.delegateToOrchestrator(
        externalId, 
        request, 
        context
      );
    }
    
    // Route to internal orchestrator
    switch (orchestrator) {
      case 'meta':
        return await this.universalOrchestrator.metaOrchestrator.orchestrate(request);
      case 'thread':
        return await this.universalOrchestrator.threadMergingOrchestrator.orchestrate(request);
      case 'ai':
        return await this.universalOrchestrator.aiOrchestrator.orchestrate(request);
      case 'universal':
        // Universal orchestration - coordinate multiple orchestrators
        return await this.executeUniversalCoordination(request, context);
      default:
        throw new Error(`Unknown orchestrator: ${orchestrator}`);
    }
  }
  
  async executeUniversalCoordination(request, context) {
    // Universal coordination logic - break down into sub-tasks and coordinate
    const subTasks = this.decomposeTask(request);
    const results = new Map();
    
    for (const subTask of subTasks) {
      const subResult = await this.routeTask(subTask.type, subTask, context);
      results.set(subTask.id, subResult);
    }
    
    // Aggregate results
    return this.aggregateSubTaskResults(results, request);
  }
  
  decomposeTask(request) {
    // Decompose complex tasks into sub-tasks
    const subTasks = [];
    
    // Example decomposition for project planning
    if (request.type === 'project-planning') {
      subTasks.push(
        { id: 'analysis', type: 'code-analysis', description: 'Analyze current codebase' },
        { id: 'requirements', type: 'thread-analysis', description: 'Analyze requirements' },
        { id: 'planning', type: 'code-generation', description: 'Generate implementation plan' }
      );
    }
    
    return subTasks;
  }
  
  aggregateSubTaskResults(results, originalRequest) {
    return {
      type: 'universal-coordination-result',
      originalRequest,
      subTaskResults: Object.fromEntries(results),
      timestamp: Date.now(),
      orchestrator: 'universal'
    };
  }
  
  async attemptFallbackRouting(taskType, request, context, originalError) {
    console.log(chalk.yellow('🔄 Attempting fallback routing...'));
    
    const routingRule = this.getRoutingRule(taskType);
    
    for (const fallbackOrchestrator of routingRule.fallbackOrchestrators) {
      try {
        if (this.isOrchestratorAvailable(fallbackOrchestrator)) {
          console.log(chalk.blue(`🔄 Trying fallback: ${fallbackOrchestrator}`));
          
          const result = await this.executeRouting(
            uuidv4(), 
            fallbackOrchestrator, 
            request, 
            context
          );
          
          return {
            ...result,
            fallbackUsed: true,
            originalError: originalError.message,
            fallbackOrchestrator
          };
        }
      } catch (fallbackError) {
        console.warn(chalk.yellow(`⚠️ Fallback ${fallbackOrchestrator} also failed:`, fallbackError.message));
      }
    }
    
    throw new Error(`All routing options failed. Original error: ${originalError.message}`);
  }
  
  updateRoutingMetrics(orchestrator, success, duration) {
    const metrics = this.routingMetrics.get(orchestrator);
    
    if (metrics) {
      metrics.totalRequests++;
      if (success) metrics.successfulRequests++;
      
      // Update average response time
      const totalTime = metrics.averageResponseTime * (metrics.totalRequests - 1) + duration;
      metrics.averageResponseTime = totalTime / metrics.totalRequests;
      
      metrics.lastHealthCheck = Date.now();
    }
  }
  
  // Public API methods
  
  getRoutingMetrics() {
    return Object.fromEntries(this.routingMetrics);
  }
  
  getActiveRoutingSessions() {
    return Array.from(this.activeSessions.values());
  }
  
  getRegisteredTaskTypes() {
    return Object.fromEntries(this.taskTypes);
  }
  
  updateOrchestratorCapabilities(orchestrator, capabilities) {
    const metrics = this.routingMetrics.get(orchestrator);
    if (metrics) {
      metrics.capabilities = new Set(capabilities);
    }
  }
  
  updateOrchestratorLoad(orchestrator, load) {
    const metrics = this.routingMetrics.get(orchestrator);
    if (metrics) {
      metrics.currentLoad = load;
    }
  }
}

module.exports = UniversalTaskRouter;

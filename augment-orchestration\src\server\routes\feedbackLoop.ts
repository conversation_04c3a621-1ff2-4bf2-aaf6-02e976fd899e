/**
 * Feedback Loop API Routes
 * 
 * RESTful API endpoints for continuous learning and feedback processing
 * Core Gap 6: Feedback Loop Integration implementation
 */

import { Router } from 'express';
import { body, query, param, validationResult } from 'express-validator';
import { PrismaClient } from '@prisma/client';
import { FeedbackLoopService } from '../services/FeedbackLoopService';
import { asyncHandler } from '../utils/asyncHandler';
import { authenticate, authorize } from '../middleware/auth';
import { logger } from '../utils/logger';
import {
  FeedbackType,
  FeedbackSource,
  FeedbackSentiment,
  LearningObjective,
  AdaptationStrategy,
  FeedbackContent,
  FeedbackMetadata,
  FeedbackQuery,
  FeedbackUtils
} from '../../shared/types/FeedbackLoop';

const router = Router();
const prisma = new PrismaClient();
const feedbackService = new FeedbackLoopService(prisma);

// Validation middleware
const validateFeedbackSubmission = [
  body('type').isIn(Object.values(FeedbackType)).withMessage('Invalid feedback type'),
  body('source').isIn(Object.values(FeedbackSource)).withMessage('Invalid feedback source'),
  body('contextId').notEmpty().withMessage('Context ID is required'),
  body('contextType').isIn(['EXECUTION', 'TASK', 'AGENT_DECISION', 'CODE_GENERATION', 'SYSTEM_OPERATION']).withMessage('Invalid context type'),
  body('content').isObject().withMessage('Content must be an object'),
  body('content.rating').optional().isInt({ min: 1, max: 10 }).withMessage('Rating must be 1-10'),
  body('content.sentiment').optional().isIn(Object.values(FeedbackSentiment)).withMessage('Invalid sentiment'),
  body('content.severity').optional().isIn(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']).withMessage('Invalid severity')
];

const validateLearningSession = [
  body('objective').isIn(Object.values(LearningObjective)).withMessage('Invalid learning objective'),
  body('strategy').isIn(Object.values(AdaptationStrategy)).withMessage('Invalid adaptation strategy'),
  body('configuration.batchSize').optional().isInt({ min: 1, max: 1000 }).withMessage('Batch size must be 1-1000'),
  body('configuration.learningRate').optional().isFloat({ min: 0.001, max: 1 }).withMessage('Learning rate must be 0.001-1'),
  body('configuration.maxProcessingTime').optional().isInt({ min: 1, max: 180 }).withMessage('Max processing time must be 1-180 minutes')
];

const validateFeedbackQuery = [
  query('types').optional().isArray(),
  query('sources').optional().isArray(),
  query('dateFrom').optional().isISO8601(),
  query('dateTo').optional().isISO8601(),
  query('minRating').optional().isInt({ min: 1, max: 10 }),
  query('maxRating').optional().isInt({ min: 1, max: 10 }),
  query('limit').optional().isInt({ min: 1, max: 1000 }),
  query('offset').optional().isInt({ min: 0 })
];

/**
 * POST /api/feedback/submit
 * Submit feedback entry
 */
router.post('/submit',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR', 'AGENT', 'USER']),
  validateFeedbackSubmission,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const content: FeedbackContent = {
      rating: req.body.content.rating,
      sentiment: req.body.content.sentiment,
      textualFeedback: req.body.content.textualFeedback,
      structuredData: req.body.content.structuredData,
      metrics: req.body.content.metrics,
      suggestions: req.body.content.suggestions,
      tags: req.body.content.tags,
      category: req.body.content.category,
      severity: req.body.content.severity
    };

    const metadata: Partial<FeedbackMetadata> = {
      userId: req.user?.id,
      agentId: req.body.metadata?.agentId,
      sessionId: req.body.metadata?.sessionId,
      version: req.body.metadata?.version,
      environment: req.body.metadata?.environment,
      language: req.body.metadata?.language,
      framework: req.body.metadata?.framework,
      correlationId: req.body.metadata?.correlationId,
      parentFeedbackId: req.body.metadata?.parentFeedbackId,
      confidence: req.body.metadata?.confidence,
      reliability: req.body.metadata?.reliability,
      weight: req.body.metadata?.weight
    };

    const feedbackEntry = await feedbackService.submitFeedback(
      req.body.type,
      req.body.source,
      req.body.contextId,
      req.body.contextType,
      content,
      metadata
    );

    logger.info('Feedback submitted via API', {
      feedbackId: feedbackEntry.id,
      type: feedbackEntry.type,
      source: feedbackEntry.source,
      contextId: feedbackEntry.contextId,
      userId: req.user?.id
    });

    res.status(201).json({
      success: true,
      feedback: {
        id: feedbackEntry.id,
        type: feedbackEntry.type,
        source: feedbackEntry.source,
        timestamp: feedbackEntry.timestamp,
        contextId: feedbackEntry.contextId,
        contextType: feedbackEntry.contextType,
        processed: feedbackEntry.processed,
        impact: feedbackEntry.impact
      }
    });
  })
);

/**
 * POST /api/feedback/process/:feedbackId
 * Manually process a specific feedback entry
 */
router.post('/process/:feedbackId',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR']),
  param('feedbackId').notEmpty().withMessage('Feedback ID is required'),
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { feedbackId } = req.params;

    // This would need to be implemented in the service
    // For now, just return success
    logger.info('Feedback processing requested via API', {
      feedbackId,
      userId: req.user?.id
    });

    res.json({
      success: true,
      message: 'Feedback processing initiated',
      feedbackId
    });
  })
);

/**
 * POST /api/feedback/learning-session
 * Start a learning session
 */
router.post('/learning-session',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR']),
  validateLearningSession,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const session = await feedbackService.startLearningSession(
      req.body.objective,
      req.body.strategy,
      req.body.configuration
    );

    logger.info('Learning session started via API', {
      sessionId: session.id,
      objective: session.objective,
      strategy: session.strategy,
      userId: req.user?.id
    });

    res.status(201).json({
      success: true,
      session: {
        id: session.id,
        startTime: session.startTime,
        objective: session.objective,
        strategy: session.strategy,
        status: session.status,
        metadata: session.metadata
      }
    });
  })
);

/**
 * GET /api/feedback/analytics
 * Get feedback analytics
 */
router.get('/analytics',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR']),
  validateFeedbackQuery,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const query: FeedbackQuery = {
      types: req.query.types as FeedbackType[],
      sources: req.query.sources as FeedbackSource[],
      dateRange: req.query.dateFrom && req.query.dateTo ? {
        startDate: new Date(req.query.dateFrom as string),
        endDate: new Date(req.query.dateTo as string)
      } : undefined,
      minRating: req.query.minRating ? parseInt(req.query.minRating as string) : undefined,
      maxRating: req.query.maxRating ? parseInt(req.query.maxRating as string) : undefined,
      processed: req.query.processed ? req.query.processed === 'true' : undefined,
      limit: req.query.limit ? parseInt(req.query.limit as string) : undefined,
      offset: req.query.offset ? parseInt(req.query.offset as string) : undefined,
      sortBy: req.query.sortBy as string,
      sortOrder: req.query.sortOrder as 'ASC' | 'DESC'
    };

    const analytics = await feedbackService.getFeedbackAnalytics(query);

    res.json({
      success: true,
      analytics
    });
  })
);

/**
 * GET /api/feedback/patterns
 * Get learning patterns
 */
router.get('/patterns',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR']),
  query('objective').optional().isIn(Object.values(LearningObjective)),
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const objective = req.query.objective as LearningObjective;
    const patterns = feedbackService.getLearningPatterns(objective);

    res.json({
      success: true,
      patterns: patterns.map(pattern => ({
        id: pattern.id,
        name: pattern.name,
        description: pattern.description,
        confidence: pattern.confidence,
        learningObjective: pattern.learningObjective,
        discoveredAt: pattern.discoveredAt,
        lastUpdated: pattern.lastUpdated,
        usageCount: pattern.usageCount,
        successRate: pattern.successRate,
        applicableContexts: pattern.applicableContexts
      })),
      count: patterns.length
    });
  })
);

/**
 * GET /api/feedback/recommendations
 * Get adaptation recommendations
 */
router.get('/recommendations',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR']),
  query('approved').optional().isBoolean(),
  query('priority').optional().isIn(['LOW', 'MEDIUM', 'HIGH', 'URGENT']),
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const approved = req.query.approved ? req.query.approved === 'true' : undefined;
    let recommendations = feedbackService.getRecommendations(approved);

    // Filter by priority if specified
    if (req.query.priority) {
      recommendations = recommendations.filter(r => r.priority === req.query.priority);
    }

    res.json({
      success: true,
      recommendations: recommendations.map(rec => ({
        id: rec.id,
        title: rec.title,
        description: rec.description,
        type: rec.type,
        priority: rec.priority,
        targetComponent: rec.targetComponent,
        expectedBenefit: rec.expectedBenefit,
        riskAssessment: rec.riskAssessment,
        approvalRequired: rec.approvalRequired,
        approvedBy: rec.approvedBy,
        approvedAt: rec.approvedAt,
        implementedAt: rec.implementedAt,
        validatedAt: rec.validatedAt
      })),
      count: recommendations.length
    });
  })
);

/**
 * POST /api/feedback/recommendations/:recommendationId/approve
 * Approve a recommendation for implementation
 */
router.post('/recommendations/:recommendationId/approve',
  authenticate,
  authorize(['ADMIN']),
  param('recommendationId').notEmpty().withMessage('Recommendation ID is required'),
  body('approvedBy').optional().notEmpty(),
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { recommendationId } = req.params;
    const approvedBy = req.body.approvedBy || req.user?.id || 'unknown';

    const approved = await feedbackService.approveRecommendation(recommendationId, approvedBy);

    if (approved) {
      logger.info('Recommendation approved via API', {
        recommendationId,
        approvedBy,
        userId: req.user?.id
      });

      res.json({
        success: true,
        message: 'Recommendation approved successfully',
        recommendationId,
        approvedBy
      });
    } else {
      res.status(404).json({
        success: false,
        error: 'Recommendation not found'
      });
    }
  })
);

/**
 * GET /api/feedback/metrics
 * Get learning metrics
 */
router.get('/metrics',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR']),
  asyncHandler(async (req, res) => {
    const metrics = await feedbackService.getLearningMetrics();

    res.json({
      success: true,
      metrics
    });
  })
);

/**
 * POST /api/feedback/user-rating
 * Submit user rating (simplified feedback submission)
 */
router.post('/user-rating',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR', 'AGENT', 'USER']),
  body('contextId').notEmpty().withMessage('Context ID is required'),
  body('contextType').isIn(['EXECUTION', 'TASK', 'AGENT_DECISION', 'CODE_GENERATION', 'SYSTEM_OPERATION']).withMessage('Invalid context type'),
  body('rating').isInt({ min: 1, max: 10 }).withMessage('Rating must be 1-10'),
  body('comment').optional().isString(),
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const content: FeedbackContent = {
      rating: req.body.rating,
      sentiment: FeedbackUtils.calculateSentiment(req.body.rating),
      textualFeedback: req.body.comment,
      category: 'user_rating'
    };

    const metadata: Partial<FeedbackMetadata> = {
      userId: req.user?.id,
      confidence: 0.9,
      reliability: 0.95,
      weight: 1.0
    };

    const feedbackEntry = await feedbackService.submitFeedback(
      FeedbackType.USER_RATING,
      FeedbackSource.USER,
      req.body.contextId,
      req.body.contextType,
      content,
      metadata
    );

    logger.info('User rating submitted via API', {
      feedbackId: feedbackEntry.id,
      rating: req.body.rating,
      contextId: req.body.contextId,
      userId: req.user?.id
    });

    res.status(201).json({
      success: true,
      feedback: {
        id: feedbackEntry.id,
        rating: req.body.rating,
        sentiment: content.sentiment,
        timestamp: feedbackEntry.timestamp
      }
    });
  })
);

/**
 * POST /api/feedback/performance-metric
 * Submit performance metrics (simplified feedback submission)
 */
router.post('/performance-metric',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR', 'AGENT', 'SYSTEM']),
  body('contextId').notEmpty().withMessage('Context ID is required'),
  body('contextType').isIn(['EXECUTION', 'TASK', 'AGENT_DECISION', 'CODE_GENERATION', 'SYSTEM_OPERATION']).withMessage('Invalid context type'),
  body('metrics').isObject().withMessage('Metrics must be an object'),
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const content: FeedbackContent = {
      metrics: req.body.metrics,
      category: 'performance_metric',
      structuredData: {
        source: 'api',
        timestamp: new Date(),
        ...req.body.additionalData
      }
    };

    const metadata: Partial<FeedbackMetadata> = {
      agentId: req.body.agentId,
      environment: req.body.environment || 'production',
      confidence: 0.95,
      reliability: 0.98,
      weight: 1.2 // Performance metrics are weighted higher
    };

    const feedbackEntry = await feedbackService.submitFeedback(
      FeedbackType.PERFORMANCE_METRIC,
      FeedbackSource.SYSTEM,
      req.body.contextId,
      req.body.contextType,
      content,
      metadata
    );

    logger.info('Performance metrics submitted via API', {
      feedbackId: feedbackEntry.id,
      contextId: req.body.contextId,
      metricsCount: Object.keys(req.body.metrics).length,
      agentId: req.body.agentId
    });

    res.status(201).json({
      success: true,
      feedback: {
        id: feedbackEntry.id,
        timestamp: feedbackEntry.timestamp,
        metricsSubmitted: Object.keys(req.body.metrics).length
      }
    });
  })
);

/**
 * GET /api/feedback/health
 * Get feedback system health status
 */
router.get('/health',
  authenticate,
  authorize(['ADMIN', 'ORCHESTRATOR']),
  asyncHandler(async (req, res) => {
    const metrics = await feedbackService.getLearningMetrics();
    const patterns = feedbackService.getLearningPatterns();
    const recommendations = feedbackService.getRecommendations();

    const health = {
      status: 'healthy',
      timestamp: new Date(),
      metrics: {
        totalPatterns: metrics.totalPatterns,
        activePatterns: metrics.activePatterns,
        patternAccuracy: metrics.patternAccuracy,
        recommendationAcceptanceRate: metrics.recommendationAcceptanceRate,
        overallImprovement: metrics.overallImprovement,
        systemStability: metrics.systemStability
      },
      recommendations: {
        total: recommendations.length,
        pending: recommendations.filter(r => !r.approvedBy).length,
        approved: recommendations.filter(r => r.approvedBy && !r.implementedAt).length,
        implemented: recommendations.filter(r => r.implementedAt).length
      },
      issues: [] as string[],
      warnings: [] as string[]
    };

    // Add health checks
    if (metrics.patternAccuracy < 0.7) {
      health.warnings.push('Pattern accuracy is below optimal threshold');
    }

    if (metrics.recommendationAcceptanceRate < 0.5) {
      health.warnings.push('Recommendation acceptance rate is low');
    }

    if (metrics.systemStability < 0.9) {
      health.issues.push('System stability is below acceptable level');
    }

    res.json({
      success: true,
      health
    });
  })
);

export default router;

#!/usr/bin/env node

/**
 * Create Downloads Script
 * Packages EcoStamp extensions and AI Orchestration system for distribution
 */

import fs from 'fs-extra';
import path from 'path';
import archiver from 'archiver';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class DownloadCreator {
  constructor() {
    this.workspaceRoot = process.cwd();
    this.downloadsDir = path.join(this.workspaceRoot, 'downloads');
    this.extensionsDir = path.join(this.workspaceRoot, 'extensions');
  }
  
  async createAllDownloads() {
    console.log('🌱 Creating EcoStamp Downloads...\n');
    
    try {
      // Ensure downloads directory exists
      await fs.ensureDir(this.downloadsDir);
      await fs.ensureDir(this.extensionsDir);
      
      // Create browser extension packages
      await this.createBrowserExtensions();
      
      // Create AI Orchestration package
      await this.createAIOrchestrationPackage();
      
      // Create complete system package
      await this.createCompleteSystemPackage();
      
      console.log('\n✅ All downloads created successfully!');
      console.log(`📁 Downloads available in: ${this.downloadsDir}`);
      
    } catch (error) {
      console.error('❌ Error creating downloads:', error.message);
      process.exit(1);
    }
  }
  
  async createBrowserExtensions() {
    console.log('📦 Creating browser extension packages...');
    
    const browsers = [
      { name: 'chrome', manifest: 'manifest.json' },
      { name: 'firefox', manifest: 'manifest.json', ext: '.xpi' },
      { name: 'edge', manifest: 'manifest.json' },
      { name: 'opera', manifest: 'manifest.json' },
      { name: 'safari', manifest: 'manifest.json' },
      { name: 'universal', manifest: 'manifest.json' }
    ];
    
    for (const browser of browsers) {
      await this.createBrowserExtension(browser);
    }
  }
  
  async createBrowserExtension(browser) {
    const sourcePath = path.join(this.workspaceRoot, 'extensions', 'universal');
    const targetDir = path.join(this.extensionsDir, browser.name);
    const extension = browser.ext || '.zip';
    const filename = `ecostamp-${browser.name}-extension${extension}`;
    const outputPath = path.join(this.downloadsDir, filename);
    
    console.log(`  Creating ${browser.name} extension...`);
    
    try {
      // Ensure target directory exists
      await fs.ensureDir(targetDir);
      
      // Copy universal extension files
      await fs.copy(sourcePath, targetDir);
      
      // Customize manifest for specific browser if needed
      await this.customizeManifest(targetDir, browser.name);
      
      // Create installation instructions
      await this.createInstallationInstructions(targetDir, browser.name);
      
      // Create ZIP package
      await this.createZipPackage(targetDir, outputPath);
      
      console.log(`    ✅ ${filename} created`);
      
    } catch (error) {
      console.error(`    ❌ Failed to create ${browser.name} extension:`, error.message);
    }
  }
  
  async customizeManifest(targetDir, browserName) {
    const manifestPath = path.join(targetDir, 'manifest.json');
    
    try {
      const manifest = await fs.readJson(manifestPath);
      
      // Browser-specific customizations
      switch (browserName) {
        case 'firefox':
          manifest.manifest_version = 2;
          manifest.background = {
            scripts: ['background.js'],
            persistent: false
          };
          manifest.browser_action = manifest.action;
          delete manifest.action;
          break;
          
        case 'safari':
          manifest.name = 'EcoStamp for Safari';
          break;
          
        case 'edge':
          manifest.name = 'EcoStamp for Microsoft Edge';
          break;
          
        case 'opera':
          manifest.name = 'EcoStamp for Opera';
          break;
          
        case 'chrome':
          manifest.name = 'EcoStamp for Chrome';
          break;
      }
      
      await fs.writeJson(manifestPath, manifest, { spaces: 2 });
      
    } catch (error) {
      console.error(`Failed to customize manifest for ${browserName}:`, error.message);
    }
  }
  
  async createInstallationInstructions(targetDir, browserName) {
    const instructions = this.getInstallationInstructions(browserName);
    const instructionsPath = path.join(targetDir, 'INSTALLATION.md');
    
    await fs.writeFile(instructionsPath, instructions);
  }
  
  getInstallationInstructions(browserName) {
    const baseInstructions = `# EcoStamp Installation Instructions

## For ${browserName.charAt(0).toUpperCase() + browserName.slice(1)}

### Quick Install
`;

    const browserSpecific = {
      chrome: `
1. Open Chrome and go to \`chrome://extensions/\`
2. Enable "Developer mode" (toggle in top right)
3. Click "Load unpacked"
4. Select the extracted EcoStamp folder
5. The extension will appear in your toolbar

### Alternative Method
1. Go to Chrome Web Store (when available)
2. Search for "EcoStamp"
3. Click "Add to Chrome"
`,
      firefox: `
1. Open Firefox and go to \`about:addons\`
2. Click the gear icon and select "Install Add-on From File"
3. Select the \`.xpi\` file
4. Click "Add" when prompted

### Alternative Method
1. Go to Firefox Add-ons (when available)
2. Search for "EcoStamp"
3. Click "Add to Firefox"
`,
      edge: `
1. Open Microsoft Edge and go to \`edge://extensions/\`
2. Enable "Developer mode" (toggle in left sidebar)
3. Click "Load unpacked"
4. Select the extracted EcoStamp folder
5. The extension will appear in your toolbar

### Alternative Method
1. Go to Microsoft Edge Add-ons (when available)
2. Search for "EcoStamp"
3. Click "Get"
`,
      opera: `
1. Open Opera and go to \`opera://extensions/\`
2. Enable "Developer mode" (toggle in top right)
3. Click "Load unpacked"
4. Select the extracted EcoStamp folder
5. The extension will appear in your toolbar
`,
      safari: `
1. Extract the EcoStamp folder
2. Open Safari and go to Safari > Preferences > Extensions
3. Enable "Allow Unsigned Extensions" (for development)
4. Click "+" and select the EcoStamp folder
5. Enable the extension when it appears

### Note for Safari
Safari extensions require additional steps for distribution.
This is a development version for testing.
`,
      universal: `
This is the universal version that works with any browser.
Choose the installation method for your browser:

### Chrome/Edge/Opera
1. Go to browser extensions page
2. Enable "Developer mode"
3. Click "Load unpacked"
4. Select the EcoStamp folder

### Firefox
1. Go to about:addons
2. Click gear icon > "Install Add-on From File"
3. Select the manifest.json file

### Safari
1. Go to Safari > Preferences > Extensions
2. Enable "Allow Unsigned Extensions"
3. Add the EcoStamp folder
`
    };

    return baseInstructions + (browserSpecific[browserName] || browserSpecific.universal) + `

## After Installation

1. Visit any AI platform (ChatGPT, Claude, Gemini, etc.)
2. Look for the EcoStamp widget in the top-right corner
3. Start a conversation to see real-time environmental impact
4. Click the EcoStamp icon in your toolbar for settings

## Supported Platforms

- ChatGPT (chat.openai.com)
- Claude (claude.ai)
- Google Gemini (gemini.google.com)
- Microsoft Copilot (copilot.microsoft.com)
- You.com
- Perplexity AI
- Poe.com

## Troubleshooting

### Extension Not Working
1. Refresh the AI platform page
2. Check if EcoStamp server is running (localhost:3000)
3. Verify extension permissions are granted

### Widget Not Appearing
1. Check extension settings (click toolbar icon)
2. Ensure "Show Widget" is enabled
3. Try refreshing the page

### Need Help?
- GitHub: https://github.com/chris-ai-dev/Time_Stamp_Project
- Website: http://localhost:3000 (when server is running)

---

**Making AI environmentally accountable!** 🌱
`;
  }
  
  async createAIOrchestrationPackage() {
    console.log('🤖 Creating AI Orchestration package...');
    
    const sourcePath = path.join(this.workspaceRoot, 'ai-orchestration');
    const outputPath = path.join(this.downloadsDir, 'ai-orchestration-system.zip');
    
    try {
      await this.createZipPackage(sourcePath, outputPath, {
        exclude: ['node_modules/**', '.git/**', '*.log', 'logs/**']
      });
      
      console.log('  ✅ ai-orchestration-system.zip created');
      
    } catch (error) {
      console.error('  ❌ Failed to create AI Orchestration package:', error.message);
    }
  }
  
  async createCompleteSystemPackage() {
    console.log('📦 Creating complete system package...');
    
    const outputPath = path.join(this.downloadsDir, 'ecostamp-complete-system.zip');
    
    try {
      const archive = archiver('zip', { zlib: { level: 9 } });
      const output = fs.createWriteStream(outputPath);
      
      archive.pipe(output);
      
      // Add core systems
      archive.directory(path.join(this.workspaceRoot, 'core-systems'), 'core-systems', {
        ignore: ['node_modules/**', '.git/**', '*.log', 'logs/**']
      });
      
      // Add extensions
      archive.directory(this.extensionsDir, 'extensions');
      
      // Add development tools
      archive.directory(path.join(this.workspaceRoot, 'development-tools'), 'development-tools', {
        ignore: ['node_modules/**', '.git/**', '*.log']
      });
      
      // Add AI orchestration (without node_modules)
      archive.directory(path.join(this.workspaceRoot, 'ai-orchestration'), 'ai-orchestration', {
        ignore: ['node_modules/**', '.git/**', '*.log', 'logs/**']
      });
      
      // Add main files
      archive.file(path.join(this.workspaceRoot, 'run-projects.js'), { name: 'run-projects.js' });
      archive.file(path.join(this.workspaceRoot, 'quick-start.bat'), { name: 'quick-start.bat' });
      archive.file(path.join(this.workspaceRoot, 'standalone-test-runner.js'), { name: 'standalone-test-runner.js' });
      archive.file(path.join(this.workspaceRoot, 'Test-Runner.bat'), { name: 'Test-Runner.bat' });
      
      // Add README
      const readmeContent = this.createCompleteSystemReadme();
      archive.append(readmeContent, { name: 'README.md' });
      
      await archive.finalize();
      
      console.log('  ✅ ecostamp-complete-system.zip created');
      
    } catch (error) {
      console.error('  ❌ Failed to create complete system package:', error.message);
    }
  }
  
  createCompleteSystemReadme() {
    return `# EcoStamp Complete System

## AI Environmental Impact Tracking Platform

This package contains the complete EcoStamp system for tracking AI environmental impact.

### What's Included

- **EcoStamp Website**: Functional website with download capabilities
- **Browser Extensions**: Universal extension for all major browsers
- **AI Orchestration System**: Complete AI coordination platform
- **Security Scanner**: Enterprise-grade security tools
- **Test Runner**: Standalone testing system

### Quick Start

1. **Extract** this ZIP file to your desired location
2. **Run Test-Runner.bat** to verify all systems
3. **Start EcoStamp server**: 
   - Navigate to \`core-systems/EcoStamp/source\`
   - Run \`npm install\` then \`npm start\`
4. **Install browser extension**:
   - Go to \`extensions/universal\`
   - Follow INSTALLATION.md instructions
5. **Access website**: http://localhost:3000

### System Requirements

- Node.js 16+ (https://nodejs.org)
- Modern web browser
- Windows/Mac/Linux

### Components

#### EcoStamp Website (\`core-systems/EcoStamp/\`)
- Real-time AI impact tracking
- SHA-256 verification system
- Cross-platform compatibility
- Download center for extensions

#### Browser Extensions (\`extensions/\`)
- Universal extension for all browsers
- Real-time impact display
- Cryptographic verification
- Seamless integration

#### AI Orchestration (\`ai-orchestration/\`)
- Meta orchestrator system
- Universal AI coordination
- Dynamic goal management
- Workflow automation

#### Development Tools (\`development-tools/\`)
- Security scanner
- Vulnerability detection
- SBOM generation
- License compliance

### Installation

#### Automatic Setup
1. Run \`Test-Runner.bat\` for system verification
2. Run \`quick-start.bat\` for guided setup

#### Manual Setup
1. Install Node.js dependencies in each component
2. Configure environment variables
3. Start individual services

### Usage

1. **Start the system**: Run Test-Runner.bat
2. **Visit AI platforms**: ChatGPT, Claude, Gemini, etc.
3. **See real-time impact**: EcoStamp widget appears automatically
4. **Verify conversations**: Use SHA-256 hash verification
5. **Track analytics**: View dashboard at localhost:3000

### Support

- **GitHub**: https://github.com/chris-ai-dev/Time_Stamp_Project
- **Documentation**: README files in each component
- **Issues**: Report on GitHub Issues

### License

Open source - see individual component licenses.

---

**Making AI environmentally accountable!** 🌱
`;
  }
  
  async createZipPackage(sourcePath, outputPath, options = {}) {
    return new Promise((resolve, reject) => {
      const archive = archiver('zip', { zlib: { level: 9 } });
      const output = fs.createWriteStream(outputPath);
      
      output.on('close', () => resolve());
      archive.on('error', (err) => reject(err));
      
      archive.pipe(output);
      
      if (options.exclude) {
        archive.directory(sourcePath, false, {
          ignore: options.exclude
        });
      } else {
        archive.directory(sourcePath, false);
      }
      
      archive.finalize();
    });
  }
}

// Run the download creator
const isMainModule = import.meta.url === `file://${process.argv[1]}` ||
                     import.meta.url.endsWith('create-downloads.js');

if (isMainModule) {
  (async () => {
    // Install archiver if not present
    try {
      await import('archiver');
    } catch (error) {
      console.log('Installing archiver dependency...');
      execSync('npm install archiver', { stdio: 'inherit' });
    }

    const creator = new DownloadCreator();
    creator.createAllDownloads().catch(error => {
      console.error('Failed to create downloads:', error);
      process.exit(1);
    });
  })();
}

export default DownloadCreator;

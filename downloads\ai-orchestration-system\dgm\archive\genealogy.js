/**
 * Genealogy Tracking System
 * 
 * Tracks the family tree and lineage of orchestration agents:
 * - Parent-child relationships
 * - Evolutionary lineages
 * - Family tree visualization
 * - Lineage analysis and statistics
 */

const fs = require('fs').promises;
const path = require('path');
const chalk = require('chalk');

class Genealogy {
  constructor(archivePath) {
    this.archivePath = archivePath;
    this.genealogyPath = path.join(archivePath, 'genealogy.json');
    this.familyTree = new Map(); // agentId -> { parents: [], children: [], generation: number }
    this.lineages = new Map(); // lineageId -> { founder: agentId, descendants: [] }
  }

  /**
   * Initialize genealogy tracking
   */
  async initialize() {
    try {
      await this.loadGenealogyData();
      console.log(chalk.green('✅ Genealogy tracking initialized'));
    } catch (error) {
      // Start with empty genealogy
      this.familyTree = new Map();
      this.lineages = new Map();
      await this.saveGenealogyData();
    }
  }

  /**
   * Add an agent to the genealogy
   */
  async addAgent(agent) {
    const agentInfo = {
      id: agent.id,
      generation: agent.generation,
      type: agent.type,
      created: agent.created || new Date(),
      parents: agent.parentIds || [],
      children: [],
      fitness: agent.fitness,
      metadata: agent.metadata
    };

    // Add to family tree
    this.familyTree.set(agent.id, agentInfo);

    // Update parent-child relationships
    for (const parentId of agentInfo.parents) {
      if (this.familyTree.has(parentId)) {
        const parent = this.familyTree.get(parentId);
        if (!parent.children.includes(agent.id)) {
          parent.children.push(agent.id);
        }
      }
    }

    // Update lineages
    await this.updateLineages(agent);

    // Save updated genealogy
    await this.saveGenealogyData();

    console.log(chalk.blue(`👨‍👩‍👧‍👦 Added ${agent.id} to genealogy (${agentInfo.parents.length} parents)`));
  }

  /**
   * Get complete lineage for an agent
   */
  getLineage(agentId) {
    if (!this.familyTree.has(agentId)) {
      throw new Error(`Agent ${agentId} not found in genealogy`);
    }

    const lineage = {
      agent: agentId,
      ancestors: this.getAncestors(agentId),
      descendants: this.getDescendants(agentId),
      siblings: this.getSiblings(agentId),
      generation: this.familyTree.get(agentId).generation
    };

    return lineage;
  }

  /**
   * Get all ancestors of an agent
   */
  getAncestors(agentId, visited = new Set()) {
    if (visited.has(agentId) || !this.familyTree.has(agentId)) {
      return [];
    }

    visited.add(agentId);
    const agent = this.familyTree.get(agentId);
    const ancestors = [];

    for (const parentId of agent.parents) {
      if (this.familyTree.has(parentId)) {
        ancestors.push({
          id: parentId,
          generation: this.familyTree.get(parentId).generation,
          fitness: this.familyTree.get(parentId).fitness,
          relationship: 'parent'
        });

        // Recursively get grandparents, etc.
        const grandparents = this.getAncestors(parentId, visited);
        ancestors.push(...grandparents.map(gp => ({
          ...gp,
          relationship: gp.relationship === 'parent' ? 'grandparent' : 'ancestor'
        })));
      }
    }

    return ancestors;
  }

  /**
   * Get all descendants of an agent
   */
  getDescendants(agentId, visited = new Set()) {
    if (visited.has(agentId) || !this.familyTree.has(agentId)) {
      return [];
    }

    visited.add(agentId);
    const agent = this.familyTree.get(agentId);
    const descendants = [];

    for (const childId of agent.children) {
      if (this.familyTree.has(childId)) {
        descendants.push({
          id: childId,
          generation: this.familyTree.get(childId).generation,
          fitness: this.familyTree.get(childId).fitness,
          relationship: 'child'
        });

        // Recursively get grandchildren, etc.
        const grandchildren = this.getDescendants(childId, visited);
        descendants.push(...grandchildren.map(gc => ({
          ...gc,
          relationship: gc.relationship === 'child' ? 'grandchild' : 'descendant'
        })));
      }
    }

    return descendants;
  }

  /**
   * Get siblings of an agent
   */
  getSiblings(agentId) {
    if (!this.familyTree.has(agentId)) {
      return [];
    }

    const agent = this.familyTree.get(agentId);
    const siblings = new Set();

    // Find all agents that share at least one parent
    for (const parentId of agent.parents) {
      if (this.familyTree.has(parentId)) {
        const parent = this.familyTree.get(parentId);
        for (const siblingId of parent.children) {
          if (siblingId !== agentId) {
            siblings.add(siblingId);
          }
        }
      }
    }

    return Array.from(siblings).map(siblingId => ({
      id: siblingId,
      generation: this.familyTree.get(siblingId).generation,
      fitness: this.familyTree.get(siblingId).fitness,
      sharedParents: this.getSharedParents(agentId, siblingId)
    }));
  }

  /**
   * Get shared parents between two agents
   */
  getSharedParents(agentId1, agentId2) {
    if (!this.familyTree.has(agentId1) || !this.familyTree.has(agentId2)) {
      return [];
    }

    const agent1 = this.familyTree.get(agentId1);
    const agent2 = this.familyTree.get(agentId2);

    return agent1.parents.filter(parentId => agent2.parents.includes(parentId));
  }

  /**
   * Update lineages when adding a new agent
   */
  async updateLineages(agent) {
    if (agent.parentIds.length === 0) {
      // This is a founder - create new lineage
      const lineageId = `lineage-${agent.id}`;
      this.lineages.set(lineageId, {
        id: lineageId,
        founder: agent.id,
        founded: agent.created || new Date(),
        descendants: [],
        generations: 1,
        totalAgents: 1,
        bestFitness: agent.fitness,
        averageFitness: agent.fitness
      });
    } else {
      // Find lineages of parents and update them
      for (const parentId of agent.parentIds) {
        const parentLineages = this.findAgentLineages(parentId);
        
        for (const lineage of parentLineages) {
          if (!lineage.descendants.includes(agent.id)) {
            lineage.descendants.push(agent.id);
            lineage.totalAgents++;
            lineage.generations = Math.max(lineage.generations, agent.generation + 1);
            
            // Update fitness statistics
            const allAgents = [lineage.founder, ...lineage.descendants];
            const fitnessValues = allAgents
              .filter(id => this.familyTree.has(id))
              .map(id => this.familyTree.get(id).fitness);
            
            lineage.bestFitness = Math.max(...fitnessValues);
            lineage.averageFitness = fitnessValues.reduce((sum, f) => sum + f, 0) / fitnessValues.length;
          }
        }
      }
    }
  }

  /**
   * Find all lineages that contain a specific agent
   */
  findAgentLineages(agentId) {
    const agentLineages = [];
    
    for (const lineage of this.lineages.values()) {
      if (lineage.founder === agentId || lineage.descendants.includes(agentId)) {
        agentLineages.push(lineage);
      }
    }
    
    return agentLineages;
  }

  /**
   * Get lineage statistics
   */
  getLineageStats() {
    const stats = {
      totalLineages: this.lineages.size,
      lineages: []
    };

    for (const lineage of this.lineages.values()) {
      stats.lineages.push({
        id: lineage.id,
        founder: lineage.founder,
        founded: lineage.founded,
        generations: lineage.generations,
        totalAgents: lineage.totalAgents,
        bestFitness: lineage.bestFitness,
        averageFitness: lineage.averageFitness
      });
    }

    // Sort by best fitness
    stats.lineages.sort((a, b) => b.bestFitness - a.bestFitness);

    return stats;
  }

  /**
   * Generate family tree visualization data
   */
  generateFamilyTreeData(rootAgentId = null) {
    const treeData = {
      nodes: [],
      edges: []
    };

    // If no root specified, find all founders
    const roots = rootAgentId ? [rootAgentId] : this.getFounders();

    for (const root of roots) {
      this.addNodeToTree(root, treeData, new Set());
    }

    return treeData;
  }

  /**
   * Get all founder agents (agents with no parents)
   */
  getFounders() {
    const founders = [];
    
    for (const [agentId, agent] of this.familyTree) {
      if (agent.parents.length === 0) {
        founders.push(agentId);
      }
    }
    
    return founders;
  }

  /**
   * Add node and its descendants to tree data
   */
  addNodeToTree(agentId, treeData, visited) {
    if (visited.has(agentId) || !this.familyTree.has(agentId)) {
      return;
    }

    visited.add(agentId);
    const agent = this.familyTree.get(agentId);

    // Add node
    treeData.nodes.push({
      id: agentId,
      generation: agent.generation,
      type: agent.type,
      fitness: agent.fitness,
      created: agent.created,
      parentCount: agent.parents.length,
      childCount: agent.children.length
    });

    // Add edges to children and recursively add children
    for (const childId of agent.children) {
      if (this.familyTree.has(childId)) {
        treeData.edges.push({
          from: agentId,
          to: childId,
          type: 'parent-child'
        });

        this.addNodeToTree(childId, treeData, visited);
      }
    }
  }

  /**
   * Get genealogy statistics
   */
  getGenealogyStats() {
    const stats = {
      totalAgents: this.familyTree.size,
      founders: this.getFounders().length,
      generations: 0,
      averageChildren: 0,
      maxChildren: 0,
      lineages: this.lineages.size
    };

    let totalChildren = 0;
    let maxChildren = 0;
    let maxGeneration = 0;

    for (const agent of this.familyTree.values()) {
      totalChildren += agent.children.length;
      maxChildren = Math.max(maxChildren, agent.children.length);
      maxGeneration = Math.max(maxGeneration, agent.generation);
    }

    stats.generations = maxGeneration + 1;
    stats.averageChildren = this.familyTree.size > 0 ? totalChildren / this.familyTree.size : 0;
    stats.maxChildren = maxChildren;

    return stats;
  }

  /**
   * Load genealogy data from file
   */
  async loadGenealogyData() {
    const data = await fs.readFile(this.genealogyPath, 'utf8');
    const genealogyData = JSON.parse(data);

    this.familyTree = new Map(Object.entries(genealogyData.familyTree || {}));
    this.lineages = new Map(Object.entries(genealogyData.lineages || {}));
  }

  /**
   * Save genealogy data to file
   */
  async saveGenealogyData() {
    const genealogyData = {
      familyTree: Object.fromEntries(this.familyTree),
      lineages: Object.fromEntries(this.lineages),
      lastUpdated: new Date()
    };

    await fs.writeFile(
      this.genealogyPath,
      JSON.stringify(genealogyData, null, 2)
    );
  }
}

module.exports = Genealogy;

import { Router } from 'express';
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';
import { async<PERSON>and<PERSON>, AppError } from '../middleware/errorHandler';
import { AuthenticatedRequest } from '../middleware/auth';
import { EventBus, EVENT_TYPES } from '../services/EventBus';
import { WorkflowExecutionEngine } from '../services/WorkflowExecutionEngine';
import { logger } from '../utils/logger';

const router = Router();
const prisma = new PrismaClient();
const eventBus = new EventBus();
const executionEngine = new WorkflowExecutionEngine();

// Validation schemas
const createTemplateSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long'),
  description: z.string().optional(),
  stages: z.array(z.object({
    id: z.string(),
    name: z.string(),
    description: z.string().optional(),
    requiredRoles: z.array(z.string()),
    dependencies: z.array(z.string()).default([]),
    timeout: z.number().min(1).max(3600).default(300), // 5 minutes default
    retryCount: z.number().min(0).max(5).default(0),
    metadata: z.record(z.any()).optional(),
  })).min(1, 'At least one stage required'),
  metadata: z.record(z.any()).optional(),
  isActive: z.boolean().default(true),
});

const executeWorkflowSchema = z.object({
  templateId: z.string().min(1, 'Template ID is required'),
  input: z.record(z.any()).optional(),
  priority: z.number().min(1).max(10).default(5),
  metadata: z.record(z.any()).optional(),
});

const updateExecutionSchema = z.object({
  status: z.enum(['PENDING', 'RUNNING', 'COMPLETED', 'FAILED', 'CANCELLED']).optional(),
  currentStage: z.string().optional(),
  output: z.record(z.any()).optional(),
  error: z.string().optional(),
  metadata: z.record(z.any()).optional(),
});

// Get workflow templates with filtering
router.get('/templates', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 20;
  const search = req.query.search as string;
  const isActive = req.query.isActive === 'true' ? true : req.query.isActive === 'false' ? false : undefined;

  const skip = (page - 1) * limit;
  const where: any = {};

  if (search) {
    where.OR = [
      { name: { contains: search, mode: 'insensitive' } },
      { description: { contains: search, mode: 'insensitive' } },
    ];
  }

  if (isActive !== undefined) {
    where.isActive = isActive;
  }

  const [templates, total] = await Promise.all([
    prisma.workflowTemplate.findMany({
      where,
      skip,
      take: limit,
      include: {
        _count: {
          select: {
            executions: true,
          },
        },
        createdByUser: {
          select: {
            id: true,
            username: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    }),
    prisma.workflowTemplate.count({ where }),
  ]);

  res.json({
    success: true,
    data: templates,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
    },
  });
}));

// Get single workflow template
router.get('/templates/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;

  const template = await prisma.workflowTemplate.findUnique({
    where: { id },
    include: {
      executions: {
        select: {
          id: true,
          status: true,
          startedAt: true,
          endedAt: true,
          currentStage: true,
          priority: true,
        },
        orderBy: {
          startedAt: 'desc',
        },
        take: 10,
      },
      createdByUser: {
        select: {
          id: true,
          username: true,
        },
      },
    },
  });

  if (!template) {
    throw new AppError('Workflow template not found', 404);
  }

  res.json({
    success: true,
    data: template,
  });
}));

// Get all workflow executions
router.get('/executions', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const executions = await prisma.workflowExecution.findMany({
    include: {
      template: {
        select: {
          id: true,
          name: true,
        },
      },
      executor: {
        select: {
          id: true,
          username: true,
        },
      },
      agents: {
        select: {
          id: true,
          agentId: true,
          name: true,
        },
      },
    },
    orderBy: {
      startedAt: 'desc',
    },
  });

  res.json({
    success: true,
    data: executions,
  });
}));

// Start workflow execution
router.post('/:templateId/execute', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { templateId } = req.params;
  const { context = {} } = req.body;

  const executionId = await executionEngine.startExecution(
    templateId,
    req.user!.id,
    context
  );

  res.status(201).json({
    success: true,
    data: {
      executionId,
      message: 'Workflow execution started',
    },
  });
}));

// Get execution status
router.get('/executions/:executionId', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { executionId } = req.params;

  const status = await executionEngine.getExecutionStatus(executionId);

  res.json({
    success: true,
    data: status,
  });
}));

// Cancel workflow execution
router.post('/executions/:executionId/cancel', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { executionId } = req.params;

  await executionEngine.cancelExecution(executionId, req.user!.id);

  res.json({
    success: true,
    message: 'Workflow execution cancelled',
  });
}));

// Get all executions with filtering
router.get('/executions', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 20;
  const status = req.query.status as string;
  const templateId = req.query.templateId as string;

  const skip = (page - 1) * limit;
  const where: any = {};

  if (status) {
    where.status = status;
  }

  if (templateId) {
    where.templateId = templateId;
  }

  const [executions, total] = await Promise.all([
    prisma.workflowExecution.findMany({
      where,
      skip,
      take: limit,
      include: {
        template: {
          select: {
            id: true,
            name: true,
            description: true,
          },
        },
        createdByUser: {
          select: {
            id: true,
            email: true,
            name: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    }),
    prisma.workflowExecution.count({ where }),
  ]);

  res.json({
    success: true,
    data: executions,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
    },
  });
}));

export { router as workflowRoutes };

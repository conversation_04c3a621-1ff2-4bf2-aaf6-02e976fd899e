#!/usr/bin/env node

/**
 * Simple Project Runner for Time Stamp Project
 * 
 * Tested and working solution to run and validate all projects
 * Author: <PERSON> (Solo Developer)
 */

import { execSync, spawn } from 'child_process';
import fs from 'fs-extra';
import path from 'path';

const projects = {
  'ecostamp': {
    name: 'EcoStamp Backend Server',
    path: 'core-systems/EcoStamp/source',
    type: 'nodejs',
    install: 'npm install',
    start: 'npm start',
    test: 'npm test',
    port: 3000,
    description: 'AI environmental impact tracking backend'
  },
  'scanner': {
    name: 'Security Scanner',
    path: 'development-tools',
    type: 'nodejs',
    install: 'npm install',
    start: 'npm run scan:quick',
    test: 'npm run scan:full',
    description: 'Enterprise-grade security scanning'
  },
  'orchestrator': {
    name: 'AI Orchestration',
    path: 'ai-orchestration',
    type: 'nodejs',
    install: 'npm install',
    start: 'node orchestrator.js init',
    description: 'Multi-AI workflow coordination'
  },
  'core': {
    name: 'Core Systems Framework',
    path: 'core-systems',
    type: 'python',
    install: 'pip install -e .',
    test: 'python -m pytest tests/',
    description: 'Universal feedback loop framework'
  }
};

function showHelp() {
  console.log(`
🚀 Time Stamp Project Runner

Usage: node run-projects.js [command] [project]

Commands:
  list                    - List all available projects
  install [project]       - Install dependencies for project(s)
  start <project>         - Start a specific project
  test [project]          - Run tests for project(s)
  status                  - Check status of all projects
  help                    - Show this help

Projects:
  ecostamp               - EcoStamp Backend Server (port 3000)
  scanner                - Security Scanner
  orchestrator           - AI Orchestration System
  core                   - Core Systems Framework (Python)
  all                    - All projects

Examples:
  node run-projects.js list
  node run-projects.js install ecostamp
  node run-projects.js start ecostamp
  node run-projects.js test all
  node run-projects.js status
`);
}

function listProjects() {
  console.log('📦 Available Projects:\n');
  
  Object.entries(projects).forEach(([key, project]) => {
    console.log(`🔹 ${key.padEnd(12)} - ${project.name}`);
    console.log(`   ${project.description}`);
    console.log(`   Path: ${project.path}`);
    console.log(`   Type: ${project.type}`);
    if (project.port) console.log(`   Port: ${project.port}`);
    console.log('');
  });
}

async function checkStatus() {
  console.log('📊 Project Status:\n');
  
  for (const [key, project] of Object.entries(projects)) {
    const projectPath = path.resolve(project.path);
    const exists = await fs.pathExists(projectPath);
    
    console.log(`🔹 ${project.name}`);
    console.log(`   Directory: ${exists ? '✅' : '❌'} ${project.path}`);
    
    if (exists) {
      if (project.type === 'nodejs') {
        const packageJson = path.join(projectPath, 'package.json');
        const hasPackageJson = await fs.pathExists(packageJson);
        const nodeModules = path.join(projectPath, 'node_modules');
        const hasDeps = await fs.pathExists(nodeModules);
        
        console.log(`   package.json: ${hasPackageJson ? '✅' : '❌'}`);
        console.log(`   Dependencies: ${hasDeps ? '✅ Installed' : '❌ Not installed'}`);
      } else if (project.type === 'python') {
        const setupPy = path.join(projectPath, 'setup.py');
        const hasSetup = await fs.pathExists(setupPy);
        console.log(`   setup.py: ${hasSetup ? '✅' : '❌'}`);
      }
    }
    console.log('');
  }
}

function runCommand(command, cwd, description) {
  console.log(`\n🔄 ${description}...`);
  console.log(`📁 Working directory: ${cwd}`);
  console.log(`⚡ Command: ${command}\n`);
  
  try {
    execSync(command, { 
      cwd, 
      stdio: 'inherit',
      timeout: 300000 // 5 minutes
    });
    console.log(`\n✅ ${description} completed successfully!`);
    return true;
  } catch (error) {
    console.log(`\n❌ ${description} failed!`);
    if (error.signal === 'SIGINT') {
      console.log('⏹️  Stopped by user');
    } else {
      console.log(`Error: ${error.message}`);
    }
    return false;
  }
}

function startProject(projectKey) {
  const project = projects[projectKey];
  if (!project) {
    console.log(`❌ Project '${projectKey}' not found`);
    return;
  }
  
  console.log(`🚀 Starting ${project.name}...`);
  
  if (project.port) {
    console.log(`🌐 Server will be available at: http://localhost:${project.port}`);
    console.log(`⏹️  Press Ctrl+C to stop\n`);
  }
  
  runCommand(project.start, project.path, `Starting ${project.name}`);
}

function installProject(projectKey) {
  const project = projects[projectKey];
  if (!project) {
    console.log(`❌ Project '${projectKey}' not found`);
    return false;
  }
  
  return runCommand(project.install, project.path, `Installing dependencies for ${project.name}`);
}

function testProject(projectKey) {
  const project = projects[projectKey];
  if (!project) {
    console.log(`❌ Project '${projectKey}' not found`);
    return false;
  }
  
  if (!project.test) {
    console.log(`⏭️  No tests defined for ${project.name}`);
    return true;
  }
  
  return runCommand(project.test, project.path, `Running tests for ${project.name}`);
}

async function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  const projectKey = args[1];
  
  console.log('🚀 Time Stamp Project Runner');
  console.log('============================\n');
  
  switch (command) {
    case 'list':
      listProjects();
      break;
      
    case 'status':
      await checkStatus();
      break;
      
    case 'install':
      if (projectKey === 'all') {
        console.log('📦 Installing all projects...\n');
        for (const key of Object.keys(projects)) {
          installProject(key);
        }
      } else if (projectKey) {
        installProject(projectKey);
      } else {
        console.log('❌ Please specify a project or "all"');
      }
      break;
      
    case 'start':
      if (!projectKey) {
        console.log('❌ Please specify a project to start');
        break;
      }
      startProject(projectKey);
      break;
      
    case 'test':
      if (projectKey === 'all') {
        console.log('🧪 Testing all projects...\n');
        for (const key of Object.keys(projects)) {
          testProject(key);
        }
      } else if (projectKey) {
        testProject(projectKey);
      } else {
        console.log('❌ Please specify a project or "all"');
      }
      break;
      
    case 'help':
    case '--help':
    case '-h':
      showHelp();
      break;
      
    default:
      console.log('❌ Unknown command. Use "help" for available commands.');
      showHelp();
  }
}

main().catch(console.error);

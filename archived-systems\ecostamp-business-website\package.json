{"name": "ecostamp-business-website", "private": true, "sideEffects": false, "type": "module", "version": "1.0.0", "description": "EcoStamp Business Website - Multi-purpose SaaS platform for digital trust and provenance", "scripts": {"build": "remix build", "dev": "remix dev --manual", "lint": "eslint --ignore-path .gitignore --cache --cache-location ./node_modules/.cache/eslint .", "start": "remix-serve ./build/index.js", "typecheck": "tsc", "test": "vitest", "test:e2e": "playwright test", "db:generate": "drizzle-kit generate:pg", "db:migrate": "tsx scripts/migrate.ts", "db:studio": "drizzle-kit studio"}, "dependencies": {"@remix-run/node": "^2.4.1", "@remix-run/react": "^2.4.1", "@remix-run/serve": "^2.4.1", "@supabase/supabase-js": "^2.38.5", "@stripe/stripe-js": "^2.4.0", "stripe": "^14.12.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "zod": "^3.22.4", "@hookform/resolvers": "^3.3.2", "clsx": "^2.0.0", "tailwind-merge": "^2.2.0", "lucide-react": "^0.303.0", "framer-motion": "^10.16.16", "date-fns": "^3.0.6", "recharts": "^2.9.3", "react-dropzone": "^14.2.3", "react-hot-toast": "^2.4.1", "posthog-js": "^1.96.1", "drizzle-orm": "^0.29.3", "postgres": "^3.4.3"}, "devDependencies": {"@remix-run/dev": "^2.4.1", "@types/react": "^18.2.20", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.7.4", "@typescript-eslint/parser": "^6.7.4", "autoprefixer": "^10.4.16", "drizzle-kit": "^0.20.7", "eslint": "^8.38.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.28.1", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "postcss": "^8.4.32", "tailwindcss": "^3.4.0", "typescript": "^5.1.6", "vite": "^5.0.0", "vitest": "^1.0.0", "@playwright/test": "^1.40.1", "tsx": "^4.6.2"}, "engines": {"node": ">=18.0.0"}, "keywords": ["ecostamp", "digital-trust", "provenance", "saas", "remix", "tailwindcss", "typescript"], "author": "EcoStamp Team", "license": "MIT"}
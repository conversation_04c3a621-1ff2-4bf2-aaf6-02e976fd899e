# Deployment Guide

This guide covers deploying the Augment Code: Unified AI Orchestration Platform to various environments.

## 🚀 Quick Deployment Options

### Option 1: Docker Deployment (Recommended)

1. **Build the Docker image**
   ```bash
   docker build -t augment-orchestration .
   ```

2. **Run with Docker Compose**
   ```bash
   docker-compose up -d
   ```

3. **Access the application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:3001

### Option 2: Manual Deployment

1. **Prerequisites**
   - Node.js 18+
   - PostgreSQL 14+
   - Redis (optional, for caching)

2. **Environment Setup**
   ```bash
   cp .env.example .env
   # Edit .env with production values
   ```

3. **Database Setup**
   ```bash
   npx prisma migrate deploy
   npx prisma generate
   ```

4. **Build and Start**
   ```bash
   npm run build
   npm start
   ```

## 🌐 Cloud Deployment

### AWS Deployment

#### Using AWS ECS + RDS

1. **Create RDS PostgreSQL instance**
   ```bash
   aws rds create-db-instance \
     --db-instance-identifier augment-orchestration-db \
     --db-instance-class db.t3.micro \
     --engine postgres \
     --master-username admin \
     --master-user-password your-secure-password \
     --allocated-storage 20
   ```

2. **Build and push Docker image to ECR**
   ```bash
   # Create ECR repository
   aws ecr create-repository --repository-name augment-orchestration

   # Get login token
   aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin <account-id>.dkr.ecr.us-east-1.amazonaws.com

   # Build and tag image
   docker build -t augment-orchestration .
   docker tag augment-orchestration:latest <account-id>.dkr.ecr.us-east-1.amazonaws.com/augment-orchestration:latest

   # Push image
   docker push <account-id>.dkr.ecr.us-east-1.amazonaws.com/augment-orchestration:latest
   ```

3. **Create ECS task definition**
   ```json
   {
     "family": "augment-orchestration",
     "networkMode": "awsvpc",
     "requiresCompatibilities": ["FARGATE"],
     "cpu": "512",
     "memory": "1024",
     "executionRoleArn": "arn:aws:iam::<account-id>:role/ecsTaskExecutionRole",
     "containerDefinitions": [
       {
         "name": "augment-orchestration",
         "image": "<account-id>.dkr.ecr.us-east-1.amazonaws.com/augment-orchestration:latest",
         "portMappings": [
           {
             "containerPort": 3001,
             "protocol": "tcp"
           }
         ],
         "environment": [
           {
             "name": "NODE_ENV",
             "value": "production"
           },
           {
             "name": "DATABASE_URL",
             "value": "**************************************************/augment_orchestration"
           }
         ],
         "logConfiguration": {
           "logDriver": "awslogs",
           "options": {
             "awslogs-group": "/ecs/augment-orchestration",
             "awslogs-region": "us-east-1",
             "awslogs-stream-prefix": "ecs"
           }
         }
       }
     ]
   }
   ```

4. **Create ECS service**
   ```bash
   aws ecs create-service \
     --cluster default \
     --service-name augment-orchestration \
     --task-definition augment-orchestration \
     --desired-count 2 \
     --launch-type FARGATE \
     --network-configuration "awsvpcConfiguration={subnets=[subnet-12345],securityGroups=[sg-12345],assignPublicIp=ENABLED}"
   ```

#### Using AWS Lambda + API Gateway (Serverless)

1. **Install Serverless Framework**
   ```bash
   npm install -g serverless
   npm install serverless-http
   ```

2. **Create serverless.yml**
   ```yaml
   service: augment-orchestration
   
   provider:
     name: aws
     runtime: nodejs18.x
     region: us-east-1
     environment:
       DATABASE_URL: ${env:DATABASE_URL}
       JWT_SECRET: ${env:JWT_SECRET}
   
   functions:
     api:
       handler: dist/lambda.handler
       events:
         - http:
             path: /{proxy+}
             method: ANY
             cors: true
   
   plugins:
     - serverless-offline
   ```

3. **Deploy**
   ```bash
   serverless deploy
   ```

### Google Cloud Platform

#### Using Cloud Run + Cloud SQL

1. **Create Cloud SQL instance**
   ```bash
   gcloud sql instances create augment-orchestration-db \
     --database-version=POSTGRES_14 \
     --tier=db-f1-micro \
     --region=us-central1
   ```

2. **Build and deploy to Cloud Run**
   ```bash
   # Build and push to Container Registry
   gcloud builds submit --tag gcr.io/PROJECT_ID/augment-orchestration

   # Deploy to Cloud Run
   gcloud run deploy augment-orchestration \
     --image gcr.io/PROJECT_ID/augment-orchestration \
     --platform managed \
     --region us-central1 \
     --allow-unauthenticated \
     --set-env-vars DATABASE_URL="*****************************************************************************************"
   ```

### Microsoft Azure

#### Using Container Instances + Azure Database

1. **Create Azure Database for PostgreSQL**
   ```bash
   az postgres server create \
     --resource-group myResourceGroup \
     --name augment-orchestration-db \
     --location eastus \
     --admin-user myadmin \
     --admin-password mypassword \
     --sku-name GP_Gen5_2
   ```

2. **Deploy to Container Instances**
   ```bash
   az container create \
     --resource-group myResourceGroup \
     --name augment-orchestration \
     --image augment-orchestration:latest \
     --dns-name-label augment-orchestration \
     --ports 3001 \
     --environment-variables \
       NODE_ENV=production \
       DATABASE_URL="postgresql://myadmin@augment-orchestration-db:<EMAIL>:5432/postgres"
   ```

## 🔧 Production Configuration

### Environment Variables

```bash
# Application
NODE_ENV=production
PORT=3001

# Database
DATABASE_URL="****************************************/database"

# Authentication
JWT_SECRET="your-super-secure-jwt-secret-key"
JWT_EXPIRES_IN="7d"

# CORS
CORS_ORIGIN="https://your-frontend-domain.com"

# Redis (optional)
REDIS_URL="redis://localhost:6379"

# Logging
LOG_LEVEL="info"
LOG_FORMAT="json"

# Security
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Performance
MAX_CONCURRENT_WORKFLOWS=50
CONTEXT_CACHE_SIZE=1000
EVOLUTION_MAX_POPULATION=100
```

### Security Checklist

- [ ] Use HTTPS in production
- [ ] Set secure JWT secret (32+ characters)
- [ ] Configure CORS properly
- [ ] Enable rate limiting
- [ ] Set up database connection pooling
- [ ] Configure proper logging
- [ ] Set up monitoring and alerting
- [ ] Enable audit logging
- [ ] Configure backup strategy
- [ ] Set up SSL/TLS for database connections

### Performance Optimization

1. **Database Optimization**
   ```sql
   -- Create indexes for frequently queried fields
   CREATE INDEX idx_agents_role ON agents(role);
   CREATE INDEX idx_workflows_status ON workflow_executions(status);
   CREATE INDEX idx_context_type ON shared_context(type);
   CREATE INDEX idx_audit_logs_timestamp ON audit_logs(timestamp);
   ```

2. **Connection Pooling**
   ```javascript
   // In production, configure Prisma connection pooling
   const prisma = new PrismaClient({
     datasources: {
       db: {
         url: process.env.DATABASE_URL + "?connection_limit=20&pool_timeout=20",
       },
     },
   });
   ```

3. **Caching Strategy**
   ```javascript
   // Enable Redis caching for frequently accessed data
   const redis = new Redis(process.env.REDIS_URL);
   
   // Cache agent assignments
   const cacheKey = `agent_assignment:${capabilities.join(',')}`;
   const cached = await redis.get(cacheKey);
   ```

## 📊 Monitoring & Observability

### Health Checks

The application provides several health check endpoints:

- `GET /health` - Basic health check
- `GET /health/detailed` - Detailed system status
- `GET /health/database` - Database connectivity
- `GET /health/services` - Service status

### Metrics Collection

1. **Application Metrics**
   - Request rate and response times
   - Error rates and types
   - Active user sessions
   - Workflow execution statistics
   - Agent performance metrics

2. **System Metrics**
   - CPU and memory usage
   - Database connection pool status
   - WebSocket connection count
   - Cache hit/miss ratios

### Logging Configuration

```javascript
// Production logging configuration
const winston = require('winston');

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' }),
    new winston.transports.Console({
      format: winston.format.simple()
    })
  ],
});
```

## 🔄 CI/CD Pipeline

### GitHub Actions Example

```yaml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run test
      - run: npm run lint
      - run: npm run type-check

  build-and-deploy:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Build Docker image
        run: docker build -t augment-orchestration .
      - name: Deploy to production
        run: |
          # Add your deployment commands here
          echo "Deploying to production..."
```

## 🚨 Troubleshooting

### Common Issues

1. **Database Connection Issues**
   ```bash
   # Check database connectivity
   psql $DATABASE_URL -c "SELECT 1;"
   
   # Verify connection string format
   echo $DATABASE_URL
   ```

2. **Memory Issues**
   ```bash
   # Monitor memory usage
   docker stats augment-orchestration
   
   # Increase memory limits
   docker run -m 2g augment-orchestration
   ```

3. **WebSocket Connection Problems**
   ```bash
   # Check if WebSocket port is accessible
   telnet your-domain.com 3001
   
   # Verify proxy configuration for WebSocket upgrade
   ```

### Performance Issues

1. **Slow Database Queries**
   ```sql
   -- Enable query logging
   ALTER SYSTEM SET log_statement = 'all';
   ALTER SYSTEM SET log_min_duration_statement = 1000;
   
   -- Analyze slow queries
   SELECT query, mean_time, calls 
   FROM pg_stat_statements 
   ORDER BY mean_time DESC 
   LIMIT 10;
   ```

2. **High Memory Usage**
   ```javascript
   // Monitor memory usage
   setInterval(() => {
     const usage = process.memoryUsage();
     console.log('Memory usage:', {
       rss: Math.round(usage.rss / 1024 / 1024) + ' MB',
       heapTotal: Math.round(usage.heapTotal / 1024 / 1024) + ' MB',
       heapUsed: Math.round(usage.heapUsed / 1024 / 1024) + ' MB',
     });
   }, 60000);
   ```

## 📞 Support

For deployment support:
- Check the troubleshooting section above
- Review application logs for error details
- Verify all environment variables are set correctly
- Ensure database migrations have been applied
- Check network connectivity and firewall rules

For additional help, please create an issue in the GitHub repository with:
- Deployment environment details
- Error logs and stack traces
- Steps to reproduce the issue
- Expected vs actual behavior

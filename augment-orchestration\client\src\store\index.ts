import { configureStore } from '@reduxjs/toolkit'
import authSlice from './slices/authSlice'
import socketSlice from './slices/socketSlice'
import orchestratorSlice from './slices/orchestratorSlice'
import agentSlice from './slices/agentSlice'
import workflowSlice from './slices/workflowSlice'
import tunnelSlice from './slices/tunnelSlice'
import evolutionSlice from './slices/evolutionSlice'

export const store = configureStore({
  reducer: {
    auth: authSlice,
    socket: socketSlice,
    orchestrator: orchestratorSlice,
    agent: agentSlice,
    workflow: workflowSlice,
    tunnel: tunnelSlice,
    evolution: evolutionSlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['socket/setSocket'],
        ignoredPaths: ['socket.socket'],
      },
    }),
})

export type RootState = ReturnType<typeof store.getState>
export type AppDispatch = typeof store.dispatch

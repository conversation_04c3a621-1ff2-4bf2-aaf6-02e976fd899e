"""
Trust Score Calculator

Calculates and maintains long-term reliability metrics for agents and domains
based on historical feedback patterns and performance trends.
"""

import logging
import math
from typing import Dict, Any, List, Optional, Tuple
from collections import defaultdict, deque
from datetime import datetime, timedelta

from ...core.feedback_types import (
    FeedbackEntry, FeedbackType, TrustScoreConfig, get_feedback_weight
)


class TrustScoreCalculator:
    """
    Calculates and maintains trust scores for agents across different domains
    based on historical performance and feedback patterns.
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize the trust score calculator.
        
        Args:
            config: Configuration dictionary for trust calculation parameters
        """
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # Trust calculation configuration
        self.trust_config = TrustScoreConfig(
            correct_weight=self.config.get('correct_weight', 1.0),
            partially_correct_weight=self.config.get('partially_correct_weight', 0.7),
            incorrect_weight=self.config.get('incorrect_weight', 0.0),
            miscellaneous_weight=self.config.get('miscellaneous_weight', 0.5),
            decay_rate=self.config.get('decay_rate', 0.95),
            learning_rate=self.config.get('learning_rate', 0.1),
            history_window=self.config.get('history_window', 100),
            min_entries_for_trust=self.config.get('min_entries_for_trust', 5)
        )
        
        # Trust score storage: {agent_id: {domain: trust_data}}
        self.trust_scores = defaultdict(lambda: defaultdict(dict))
        
        # Historical data storage: {agent_id: {domain: deque of entries}}
        self.agent_history = defaultdict(lambda: defaultdict(lambda: deque(maxlen=self.trust_config.history_window)))
        
        # Performance trends: {agent_id: {domain: trend_data}}
        self.performance_trends = defaultdict(lambda: defaultdict(dict))
        
        # Global statistics
        self.global_stats = {
            'total_agents': 0,
            'total_domains': 0,
            'total_calculations': 0,
            'average_trust_score': 0.0
        }
    
    def calculate_trust_score(self, 
                            agent_id: str, 
                            domain: str, 
                            recent_entries: List[FeedbackEntry]) -> float:
        """
        Calculate trust score for an agent in a specific domain.
        
        Args:
            agent_id: Identifier of the agent
            domain: Domain name
            recent_entries: Recent feedback entries for the agent
            
        Returns:
            Trust score between 0.0 and 1.0
        """
        try:
            # Get existing trust data
            trust_data = self.trust_scores[agent_id][domain]
            current_trust = trust_data.get('score', 0.5)  # Default to neutral
            
            # Add recent entries to history
            for entry in recent_entries:
                self.agent_history[agent_id][domain].append(entry)
            
            # Get all historical entries for this agent-domain combination
            all_entries = list(self.agent_history[agent_id][domain])
            
            # Check if we have enough data for meaningful trust calculation
            if len(all_entries) < self.trust_config.min_entries_for_trust:
                # Not enough data - return conservative trust score
                return min(0.6, current_trust + 0.1 * len(all_entries) / self.trust_config.min_entries_for_trust)
            
            # Calculate base trust score from historical performance
            base_trust = self._calculate_base_trust(all_entries)
            
            # Apply trend adjustments
            trend_adjustment = self._calculate_trend_adjustment(agent_id, domain, all_entries)
            
            # Apply recency weighting
            recency_adjustment = self._calculate_recency_adjustment(all_entries)
            
            # Apply consistency bonus/penalty
            consistency_adjustment = self._calculate_consistency_adjustment(all_entries)
            
            # Combine all factors
            new_trust = base_trust + trend_adjustment + recency_adjustment + consistency_adjustment
            
            # Apply learning rate for gradual updates
            if 'score' in trust_data:
                new_trust = current_trust + self.trust_config.learning_rate * (new_trust - current_trust)
            
            # Apply bounds
            new_trust = max(0.0, min(1.0, new_trust))
            
            # Update trust data
            self._update_trust_data(agent_id, domain, new_trust, all_entries)
            
            # Update global statistics
            self._update_global_stats()
            
            self.logger.debug(
                f"Calculated trust score for {agent_id}/{domain}: "
                f"base={base_trust:.3f}, trend={trend_adjustment:.3f}, "
                f"recency={recency_adjustment:.3f}, consistency={consistency_adjustment:.3f}, "
                f"final={new_trust:.3f}"
            )
            
            return new_trust
            
        except Exception as e:
            self.logger.error(f"Error calculating trust score for {agent_id}/{domain}: {str(e)}")
            return 0.5  # Return neutral trust on error
    
    def update_trust_score(self, 
                          agent_id: str, 
                          domain: str, 
                          feedback_entry: FeedbackEntry) -> float:
        """
        Update trust score with new feedback entry.
        
        Args:
            agent_id: Identifier of the agent
            domain: Domain name
            feedback_entry: New feedback entry
            
        Returns:
            Updated trust score between 0.0 and 1.0
        """
        try:
            # Add entry to history
            self.agent_history[agent_id][domain].append(feedback_entry)
            
            # Recalculate trust score with updated history
            all_entries = list(self.agent_history[agent_id][domain])
            return self.calculate_trust_score(agent_id, domain, [])
            
        except Exception as e:
            self.logger.error(f"Error updating trust score for {agent_id}/{domain}: {str(e)}")
            return self.trust_scores[agent_id][domain].get('score', 0.5)
    
    def get_trust_history(self, agent_id: str, domain: str) -> List[Dict[str, Any]]:
        """
        Get trust score history for an agent in a domain.
        
        Args:
            agent_id: Identifier of the agent
            domain: Domain name
            
        Returns:
            List of historical trust score entries
        """
        trust_data = self.trust_scores[agent_id][domain]
        return trust_data.get('history', [])
    
    def _calculate_base_trust(self, entries: List[FeedbackEntry]) -> float:
        """Calculate base trust score from feedback entries."""
        if not entries:
            return 0.5
        
        # Calculate weighted average based on feedback types
        total_weight = 0.0
        weighted_sum = 0.0
        
        for entry in entries:
            weight = get_feedback_weight(entry.feedback_type)
            total_weight += 1.0  # Each entry has equal importance
            weighted_sum += weight
        
        return weighted_sum / total_weight if total_weight > 0 else 0.5
    
    def _calculate_trend_adjustment(self, agent_id: str, domain: str, entries: List[FeedbackEntry]) -> float:
        """Calculate adjustment based on performance trends."""
        if len(entries) < 10:  # Need sufficient data for trend analysis
            return 0.0
        
        # Split entries into recent and older periods
        split_point = len(entries) // 2
        older_entries = entries[:split_point]
        recent_entries = entries[split_point:]
        
        # Calculate performance for each period
        older_performance = self._calculate_base_trust(older_entries)
        recent_performance = self._calculate_base_trust(recent_entries)
        
        # Calculate trend
        trend = recent_performance - older_performance
        
        # Store trend data
        self.performance_trends[agent_id][domain] = {
            'trend': trend,
            'older_performance': older_performance,
            'recent_performance': recent_performance,
            'calculated_at': datetime.utcnow().isoformat()
        }
        
        # Return scaled trend adjustment
        return trend * 0.2  # Scale down the impact
    
    def _calculate_recency_adjustment(self, entries: List[FeedbackEntry]) -> float:
        """Calculate adjustment based on recent performance."""
        if len(entries) < 5:
            return 0.0
        
        # Get last 5 entries
        recent_entries = entries[-5:]
        
        # Calculate recent performance
        recent_performance = self._calculate_base_trust(recent_entries)
        
        # Boost or penalize based on recent performance
        if recent_performance > 0.8:
            return 0.05  # Boost for excellent recent performance
        elif recent_performance < 0.4:
            return -0.05  # Penalty for poor recent performance
        
        return 0.0
    
    def _calculate_consistency_adjustment(self, entries: List[FeedbackEntry]) -> float:
        """Calculate adjustment based on performance consistency."""
        if len(entries) < 10:
            return 0.0
        
        # Calculate variance in performance
        weights = [get_feedback_weight(entry.feedback_type) for entry in entries]
        mean_weight = sum(weights) / len(weights)
        variance = sum((w - mean_weight) ** 2 for w in weights) / len(weights)
        
        # Convert variance to consistency score (lower variance = higher consistency)
        consistency_score = 1.0 / (1.0 + variance * 10)  # Scale variance
        
        # Adjust based on consistency
        if consistency_score > 0.8:
            return 0.03  # Bonus for high consistency
        elif consistency_score < 0.4:
            return -0.03  # Penalty for low consistency
        
        return 0.0
    
    def _update_trust_data(self, agent_id: str, domain: str, trust_score: float, entries: List[FeedbackEntry]) -> None:
        """Update trust data for an agent-domain combination."""
        trust_data = self.trust_scores[agent_id][domain]
        
        # Update current score
        trust_data['score'] = trust_score
        trust_data['last_updated'] = datetime.utcnow().isoformat()
        trust_data['entry_count'] = len(entries)
        
        # Update history (keep last 20 trust score updates)
        if 'history' not in trust_data:
            trust_data['history'] = deque(maxlen=20)
        
        trust_data['history'].append({
            'score': trust_score,
            'timestamp': datetime.utcnow().isoformat(),
            'entry_count': len(entries)
        })
        
        # Calculate statistics
        if entries:
            feedback_counts = defaultdict(int)
            for entry in entries:
                feedback_counts[entry.feedback_type.value] += 1
            
            trust_data['feedback_distribution'] = dict(feedback_counts)
            trust_data['success_rate'] = (
                feedback_counts['correct'] + feedback_counts['partially_correct']
            ) / len(entries)
    
    def _update_global_stats(self) -> None:
        """Update global statistics."""
        self.global_stats['total_agents'] = len(self.trust_scores)
        
        total_domains = sum(len(domains) for domains in self.trust_scores.values())
        self.global_stats['total_domains'] = total_domains
        
        self.global_stats['total_calculations'] += 1
        
        # Calculate average trust score
        all_scores = []
        for agent_domains in self.trust_scores.values():
            for domain_data in agent_domains.values():
                if 'score' in domain_data:
                    all_scores.append(domain_data['score'])
        
        if all_scores:
            self.global_stats['average_trust_score'] = sum(all_scores) / len(all_scores)
    
    def get_agent_trust_summary(self, agent_id: str) -> Dict[str, Any]:
        """Get trust summary for a specific agent across all domains."""
        if agent_id not in self.trust_scores:
            return {'agent_id': agent_id, 'domains': {}, 'overall_trust': 0.5}
        
        agent_data = self.trust_scores[agent_id]
        domain_scores = {}
        
        for domain, trust_data in agent_data.items():
            domain_scores[domain] = {
                'trust_score': trust_data.get('score', 0.5),
                'entry_count': trust_data.get('entry_count', 0),
                'success_rate': trust_data.get('success_rate', 0.0),
                'last_updated': trust_data.get('last_updated')
            }
        
        # Calculate overall trust (weighted by entry count)
        total_entries = sum(data['entry_count'] for data in domain_scores.values())
        if total_entries > 0:
            overall_trust = sum(
                data['trust_score'] * data['entry_count'] 
                for data in domain_scores.values()
            ) / total_entries
        else:
            overall_trust = 0.5
        
        return {
            'agent_id': agent_id,
            'domains': domain_scores,
            'overall_trust': overall_trust,
            'domain_count': len(domain_scores)
        }
    
    def get_domain_trust_summary(self, domain: str) -> Dict[str, Any]:
        """Get trust summary for a specific domain across all agents."""
        domain_scores = []
        agent_count = 0
        
        for agent_id, agent_domains in self.trust_scores.items():
            if domain in agent_domains:
                trust_data = agent_domains[domain]
                domain_scores.append(trust_data.get('score', 0.5))
                agent_count += 1
        
        if not domain_scores:
            return {'domain': domain, 'agent_count': 0, 'average_trust': 0.5}
        
        return {
            'domain': domain,
            'agent_count': agent_count,
            'average_trust': sum(domain_scores) / len(domain_scores),
            'min_trust': min(domain_scores),
            'max_trust': max(domain_scores),
            'trust_scores': domain_scores
        }
    
    def get_top_agents(self, domain: Optional[str] = None, limit: int = 10) -> List[Dict[str, Any]]:
        """Get top-performing agents by trust score."""
        agent_scores = []
        
        for agent_id, agent_domains in self.trust_scores.items():
            if domain:
                if domain in agent_domains:
                    trust_data = agent_domains[domain]
                    agent_scores.append({
                        'agent_id': agent_id,
                        'domain': domain,
                        'trust_score': trust_data.get('score', 0.5),
                        'entry_count': trust_data.get('entry_count', 0)
                    })
            else:
                # Calculate overall trust across all domains
                summary = self.get_agent_trust_summary(agent_id)
                agent_scores.append({
                    'agent_id': agent_id,
                    'domain': 'all',
                    'trust_score': summary['overall_trust'],
                    'entry_count': sum(d['entry_count'] for d in summary['domains'].values())
                })
        
        # Sort by trust score and return top agents
        agent_scores.sort(key=lambda x: x['trust_score'], reverse=True)
        return agent_scores[:limit]
    
    def get_global_stats(self) -> Dict[str, Any]:
        """Get global trust statistics."""
        return self.global_stats.copy()
    
    def reset_agent_trust(self, agent_id: str, domain: Optional[str] = None) -> None:
        """Reset trust data for an agent."""
        if agent_id in self.trust_scores:
            if domain:
                if domain in self.trust_scores[agent_id]:
                    del self.trust_scores[agent_id][domain]
                    del self.agent_history[agent_id][domain]
                    if agent_id in self.performance_trends and domain in self.performance_trends[agent_id]:
                        del self.performance_trends[agent_id][domain]
            else:
                del self.trust_scores[agent_id]
                del self.agent_history[agent_id]
                if agent_id in self.performance_trends:
                    del self.performance_trends[agent_id]
        
        self.logger.info(f"Reset trust data for agent {agent_id}" + (f" in domain {domain}" if domain else ""))
    
    def reset_all_trust(self) -> None:
        """Reset all trust data."""
        self.trust_scores.clear()
        self.agent_history.clear()
        self.performance_trends.clear()
        self.global_stats = {
            'total_agents': 0,
            'total_domains': 0,
            'total_calculations': 0,
            'average_trust_score': 0.0
        }
        self.logger.info("Reset all trust data")

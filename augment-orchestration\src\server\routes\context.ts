import { Router } from 'express';
import { PrismaClient } from '@prisma/client';
import { asyncHand<PERSON> } from '../middleware/errorHandler';
import { AuthenticatedRequest } from '../middleware/auth';
import { ModelContextProtocol, ContextQuery, ContextMergeStrategy } from '../services/ModelContextProtocol';
import { z } from 'zod';

const router = Router();
const prisma = new PrismaClient();
const contextProtocol = new ModelContextProtocol();

// Validation schemas
const createContextSchema = z.object({
  name: z.string().min(1).max(255),
  type: z.enum(['GLOBAL', 'WORKFLOW', 'AGENT', 'SESSION', 'TUNNEL']),
  data: z.record(z.any()),
  priority: z.number().int().min(0).max(100).optional(),
  tags: z.array(z.string()).optional(),
  accessLevel: z.enum(['PUBLIC', 'PRIVATE', 'RESTRICTED']).optional(),
  agentId: z.string().optional(),
  workflowId: z.string().optional(),
});

const updateContextSchema = z.object({
  data: z.record(z.any()).optional(),
  priority: z.number().int().min(0).max(100).optional(),
  tags: z.array(z.string()).optional(),
  accessLevel: z.enum(['PUBLIC', 'PRIVATE', 'RESTRICTED']).optional(),
  mergeStrategy: z.object({
    type: z.enum(['OVERRIDE', 'MERGE', 'APPEND', 'PRIORITY_BASED']).optional(),
    conflictResolution: z.enum(['LATEST', 'HIGHEST_PRIORITY', 'MANUAL', 'VERSIONED']).optional(),
    preserveHistory: z.boolean().optional(),
    maxVersions: z.number().int().min(1).max(100).optional(),
  }).optional(),
});

const queryContextSchema = z.object({
  layers: z.array(z.string()).optional(),
  types: z.array(z.string()).optional(),
  tags: z.array(z.string()).optional(),
  agentIds: z.array(z.string()).optional(),
  workflowIds: z.array(z.string()).optional(),
  timeRange: z.object({
    start: z.string().datetime(),
    end: z.string().datetime(),
  }).optional(),
  limit: z.number().int().min(1).max(1000).optional(),
  includeMetadata: z.boolean().optional(),
});

const mergeLayersSchema = z.object({
  layerIds: z.array(z.string()).min(1),
  strategy: z.object({
    type: z.enum(['OVERRIDE', 'MERGE', 'APPEND', 'PRIORITY_BASED']).optional(),
    conflictResolution: z.enum(['LATEST', 'HIGHEST_PRIORITY', 'MANUAL', 'VERSIONED']).optional(),
    preserveHistory: z.boolean().optional(),
    maxVersions: z.number().int().min(1).max(100).optional(),
  }).optional(),
});

// Create context layer
router.post('/', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const validatedData = createContextSchema.parse(req.body);

  const layerId = await contextProtocol.createContextLayer(
    validatedData.name,
    validatedData.type,
    validatedData.data,
    {
      priority: validatedData.priority,
      tags: validatedData.tags,
      accessLevel: validatedData.accessLevel,
      agentId: validatedData.agentId,
      workflowId: validatedData.workflowId,
    }
  );

  res.status(201).json({
    success: true,
    data: {
      layerId,
      message: 'Context layer created successfully',
    },
  });
}));

// Update context layer
router.put('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;
  const validatedData = updateContextSchema.parse(req.body);

  const mergeStrategy: ContextMergeStrategy = {
    type: validatedData.mergeStrategy?.type || 'MERGE',
    conflictResolution: validatedData.mergeStrategy?.conflictResolution || 'LATEST',
    preserveHistory: validatedData.mergeStrategy?.preserveHistory ?? true,
    maxVersions: validatedData.mergeStrategy?.maxVersions || 10,
  };

  await contextProtocol.updateContextLayer(
    id,
    {
      data: validatedData.data,
      priority: validatedData.priority,
      tags: validatedData.tags,
      accessLevel: validatedData.accessLevel,
    },
    mergeStrategy
  );

  res.json({
    success: true,
    message: 'Context layer updated successfully',
  });
}));

// Query context layers
router.post('/query', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const validatedData = queryContextSchema.parse(req.body);

  const query: ContextQuery = {
    layers: validatedData.layers,
    types: validatedData.types,
    tags: validatedData.tags,
    agentIds: validatedData.agentIds,
    workflowIds: validatedData.workflowIds,
    timeRange: validatedData.timeRange ? {
      start: new Date(validatedData.timeRange.start),
      end: new Date(validatedData.timeRange.end),
    } : undefined,
    limit: validatedData.limit,
    includeMetadata: validatedData.includeMetadata,
  };

  const layers = await contextProtocol.queryContext(query);

  res.json({
    success: true,
    data: layers,
  });
}));

// Merge context layers
router.post('/merge', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const validatedData = mergeLayersSchema.parse(req.body);

  const strategy: ContextMergeStrategy = {
    type: validatedData.strategy?.type || 'PRIORITY_BASED',
    conflictResolution: validatedData.strategy?.conflictResolution || 'HIGHEST_PRIORITY',
    preserveHistory: validatedData.strategy?.preserveHistory ?? false,
    maxVersions: validatedData.strategy?.maxVersions || 1,
  };

  const mergedData = await contextProtocol.mergeContextLayers(
    validatedData.layerIds,
    strategy
  );

  res.json({
    success: true,
    data: mergedData,
  });
}));

// Get context layer by ID
router.get('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;

  const layers = await contextProtocol.queryContext({ layers: [id] });

  if (layers.length === 0) {
    return res.status(404).json({
      success: false,
      error: 'Context layer not found',
    });
  }

  res.json({
    success: true,
    data: layers[0],
  });
}));

// Get all context layers with pagination
router.get('/', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 50;
  const type = req.query.type as string;
  const tags = req.query.tags ? (req.query.tags as string).split(',') : undefined;

  const query: ContextQuery = {
    types: type ? [type] : undefined,
    tags,
    limit,
  };

  const layers = await contextProtocol.queryContext(query);

  // Simple pagination (in a real implementation, you'd want proper offset-based pagination)
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;
  const paginatedLayers = layers.slice(startIndex, endIndex);

  res.json({
    success: true,
    data: paginatedLayers,
    pagination: {
      page,
      limit,
      total: layers.length,
      totalPages: Math.ceil(layers.length / limit),
    },
  });
}));

// Get context statistics
router.get('/stats/overview', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const stats = await contextProtocol.getContextStatistics();

  res.json({
    success: true,
    data: stats,
  });
}));

export { router as contextRoutes };

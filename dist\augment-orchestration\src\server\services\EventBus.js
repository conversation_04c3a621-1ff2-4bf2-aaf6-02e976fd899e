"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EVENT_TYPES = exports.EventBus = void 0;
const events_1 = require("events");
const uuid_1 = require("uuid");
const logger_1 = require("../utils/logger");
class EventBus extends events_1.EventEmitter {
    constructor() {
        super();
        this.messageHistory = [];
        this.maxHistorySize = 1000;
        this.setMaxListeners(100); // Increase max listeners for high-throughput scenarios
    }
    async initialize() {
        logger_1.logger.info('EventBus initialized');
    }
    /**
     * Publish a message to the event bus
     */
    publish(type, payload, source, target, metadata) {
        const message = {
            id: (0, uuid_1.v4)(),
            type,
            source,
            payload,
            timestamp: new Date(),
            ...(target && { target }),
            ...(metadata && { metadata }),
        };
        // Add to history
        this.addToHistory(message);
        // Emit the event
        this.emit(type, message);
        this.emit('*', message); // Wildcard listener
        logger_1.logger.debug(`EventBus: Published message`, {
            type,
            source,
            target,
            messageId: message.id,
        });
    }
    /**
     * Subscribe to specific event types
     */
    subscribe(type, handler) {
        this.on(type, handler);
        logger_1.logger.debug(`EventBus: Subscribed to event type: ${type}`);
        // Return unsubscribe function
        return () => {
            this.off(type, handler);
            logger_1.logger.debug(`EventBus: Unsubscribed from event type: ${type}`);
        };
    }
    /**
     * Subscribe to all events (wildcard)
     */
    subscribeAll(handler) {
        this.on('*', handler);
        logger_1.logger.debug('EventBus: Subscribed to all events');
        return () => {
            this.off('*', handler);
            logger_1.logger.debug('EventBus: Unsubscribed from all events');
        };
    }
    /**
     * Get message history
     */
    getHistory(type, limit) {
        let history = this.messageHistory;
        if (type) {
            history = history.filter(msg => msg.type === type);
        }
        if (limit) {
            history = history.slice(-limit);
        }
        return history;
    }
    /**
     * Clear message history
     */
    clearHistory() {
        this.messageHistory = [];
        logger_1.logger.info('EventBus: Message history cleared');
    }
    /**
     * Get event bus statistics
     */
    getStats() {
        const eventTypes = {};
        this.messageHistory.forEach(msg => {
            eventTypes[msg.type] = (eventTypes[msg.type] || 0) + 1;
        });
        const listeners = {};
        this.eventNames().forEach(eventName => {
            listeners[eventName.toString()] = this.listenerCount(eventName);
        });
        return {
            totalMessages: this.messageHistory.length,
            eventTypes,
            listeners,
        };
    }
    addToHistory(message) {
        this.messageHistory.push(message);
        // Trim history if it exceeds max size
        if (this.messageHistory.length > this.maxHistorySize) {
            this.messageHistory = this.messageHistory.slice(-this.maxHistorySize);
        }
    }
}
exports.EventBus = EventBus;
// Event type constants for type safety
exports.EVENT_TYPES = {
    // Orchestrator Events
    ORCHESTRATOR_CREATED: 'orchestrator:created',
    ORCHESTRATOR_UPDATED: 'orchestrator:updated',
    ORCHESTRATOR_DELETED: 'orchestrator:deleted',
    // Agent Events
    AGENT_ASSIGNED: 'agent:assigned',
    AGENT_STATUS_CHANGED: 'agent:status_changed',
    AGENT_EVOLVED: 'agent:evolved',
    // Tunnel Events
    TUNNEL_CREATED: 'tunnel:created',
    TUNNEL_ACTIVATED: 'tunnel:activated',
    TUNNEL_DATA_FLOW: 'tunnel:data_flow',
    // Workflow Events
    WORKFLOW_STARTED: 'workflow:started',
    WORKFLOW_PROGRESS: 'workflow:progress',
    WORKFLOW_COMPLETED: 'workflow:completed',
    WORKFLOW_FAILED: 'workflow:failed',
    // System Events
    SYSTEM_STATUS: 'system:status',
    USER_PRESENCE: 'user:presence',
    // Evolution Events
    EVOLUTION_MUTATION: 'evolution:mutation',
    EVOLUTION_SELECTION: 'evolution:selection',
    EVOLUTION_PROMOTION: 'evolution:promotion',
    // Context Events
    CONTEXT_UPDATED: 'context:updated',
    CONTEXT_SHARED: 'context:shared',
};

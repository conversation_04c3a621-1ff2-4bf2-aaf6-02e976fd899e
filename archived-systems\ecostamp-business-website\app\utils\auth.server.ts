import { createServerClient, parse, serialize } from "@supabase/ssr";
import { redirect } from "@remix-run/node";

export function createSupabaseServerClient(request: Request) {
  const cookies = parse(request.headers.get("Cookie") ?? "");
  const headers = new Headers();

  const supabase = createServerClient(
    process.env.SUPABASE_URL!,
    process.env.SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(key) {
          return cookies[key];
        },
        set(key, value, options) {
          headers.append("Set-Cookie", serialize(key, value, options));
        },
        remove(key, options) {
          headers.append("Set-Cookie", serialize(key, "", options));
        },
      },
    }
  );

  return { supabase, headers };
}

export async function getUser(request: Request) {
  const { supabase } = createSupabaseServerClient(request);
  
  try {
    const {
      data: { user },
      error,
    } = await supabase.auth.getUser();

    if (error || !user) {
      return null;
    }

    // Get user profile data
    const { data: profile } = await supabase
      .from("profiles")
      .select("*")
      .eq("id", user.id)
      .single();

    return {
      id: user.id,
      email: user.email!,
      name: profile?.name || user.user_metadata?.name || null,
      avatar: profile?.avatar_url || user.user_metadata?.avatar_url || null,
      plan: profile?.plan || "free",
      created_at: user.created_at,
      ...profile,
    };
  } catch (error) {
    console.error("Error getting user:", error);
    return null;
  }
}

export async function requireUser(request: Request) {
  const user = await getUser(request);
  
  if (!user) {
    throw redirect("/login");
  }
  
  return user;
}

export async function requireAnonymous(request: Request) {
  const user = await getUser(request);
  
  if (user) {
    throw redirect("/dashboard");
  }
}

export async function signOut(request: Request) {
  const { supabase, headers } = createSupabaseServerClient(request);
  
  await supabase.auth.signOut();
  
  return redirect("/", {
    headers,
  });
}

export async function signIn(
  request: Request,
  email: string,
  password: string
) {
  const { supabase, headers } = createSupabaseServerClient(request);
  
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  });

  if (error) {
    return { error: error.message, data: null, headers };
  }

  return { error: null, data, headers };
}

export async function signUp(
  request: Request,
  email: string,
  password: string,
  name?: string
) {
  const { supabase, headers } = createSupabaseServerClient(request);
  
  const { data, error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      data: {
        name: name || "",
      },
    },
  });

  if (error) {
    return { error: error.message, data: null, headers };
  }

  // Create user profile
  if (data.user) {
    await supabase.from("profiles").insert({
      id: data.user.id,
      email: data.user.email,
      name: name || "",
      plan: "free",
      created_at: new Date().toISOString(),
    });
  }

  return { error: null, data, headers };
}

export async function resetPassword(request: Request, email: string) {
  const { supabase } = createSupabaseServerClient(request);
  
  const { error } = await supabase.auth.resetPasswordForEmail(email, {
    redirectTo: `${new URL(request.url).origin}/reset-password`,
  });

  if (error) {
    return { error: error.message };
  }

  return { error: null };
}

export async function updatePassword(
  request: Request,
  password: string
) {
  const { supabase, headers } = createSupabaseServerClient(request);
  
  const { error } = await supabase.auth.updateUser({
    password,
  });

  if (error) {
    return { error: error.message, headers };
  }

  return { error: null, headers };
}

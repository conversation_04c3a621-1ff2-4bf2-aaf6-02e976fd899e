#!/usr/bin/env python3
"""
Search and Rescue Example: Lost Child <PERSON>enario

This example demonstrates the complete Search and Rescue feedback loop
for a lost child scenario, including visual detection, reference item matching,
mission validation, and side pocket management.
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, Any

# Import the feedback loop framework
from feedback_loop_framework import (
    FeedbackEngine, AdaptiveConfidenceModel, TrustScoreCalculator, FileMemoryStore
)
from feedback_loop_framework.domains.search_rescue import SearchRescueDomain


def setup_logging():
    """Setup logging for the Search and Rescue example."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('search_rescue_example.log')
        ]
    )


def create_sar_feedback_engine() -> FeedbackEngine:
    """Create and configure the feedback engine for Search and Rescue operations."""
    
    # Enhanced configuration for Search and Rescue
    confidence_config = {
        'correct_bonus': 0.10,  # Higher bonus for successful target identification
        'partially_correct_penalty': 0.02,
        'incorrect_penalty': 0.15,  # Higher penalty for false positives in SAR
        'learning_rate': 0.12,
        'history_window': 150
    }
    
    trust_config = {
        'correct_weight': 1.0,
        'partially_correct_weight': 0.8,  # High weight for partial success in SAR
        'incorrect_weight': 0.0,
        'decay_rate': 0.98,
        'learning_rate': 0.10,
        'min_entries_for_trust': 3
    }
    
    memory_config = {
        'base_path': './sar_feedback_data',
        'compression_enabled': True,
        'retention_days': 180,  # Longer retention for SAR analysis
        'cache_size': 1500
    }
    
    # Create components
    confidence_model = AdaptiveConfidenceModel(confidence_config)
    trust_calculator = TrustScoreCalculator(trust_config)
    memory_store = FileMemoryStore(memory_config)
    
    # Create engine
    engine = FeedbackEngine(
        confidence_model=confidence_model,
        trust_calculator=trust_calculator,
        memory_store=memory_store,
        config={'enable_sar_mode': True}
    )
    
    return engine


def setup_sar_domain(engine: FeedbackEngine) -> None:
    """Setup and configure the Search and Rescue domain."""
    
    # Configure Search and Rescue domain with lost child scenario
    sar_config = {
        'custom_categories': {
            'child_specific_items': {
                'subcategories': ['small_shoes', 'child_clothing', 'toys', 'school_items'],
                'confidence_threshold': 0.8,
                'priority': 'critical'
            }
        },
        'reference_criteria': {
            'clothing': {
                'color_matching': True,
                'size_range_cm': (10, 50),  # Child-sized items
                'priority_multiplier': 4.0  # Higher priority for child items
            }
        },
        'mission_thresholds': {
            'mission_success_thresholds': {
                'target_found_confidence': 0.85,  # Slightly lower for child detection
                'reference_item_relevance': 0.7   # Higher relevance threshold
            }
        },
        'side_pocket_path': './sar_side_pocket',
        'side_pocket_retention': 120,  # 4 months retention
        'auto_review': True,
        'retraining_threshold': 30  # Lower threshold for faster learning
    }
    
    sar_domain = SearchRescueDomain(sar_config)
    engine.register_domain('search_rescue', sar_domain)


def simulate_lost_child_mission() -> Dict[str, Any]:
    """Simulate a lost child Search and Rescue mission scenario."""
    
    # Mission context
    mission_context = {
        'mission_id': 'SAR_2025_001',
        'mission_type': 'lost_child',
        'target_age': 'child',
        'target_description': {
            'age': 8,
            'clothing': {'shirt': 'red', 'pants': 'blue_jeans'},
            'items': ['backpack', 'stuffed_bear']
        },
        'search_area_km2': 2.5,
        'last_known_location': {'latitude': 42.3601, 'longitude': -71.0589},
        'time_missing_hours': 4,
        'weather_conditions': 'partly_cloudy',
        'lighting_conditions': 'daylight',
        'terrain_type': 'mixed_forest',
        'search_pattern': 'grid'
    }
    
    # Simulated detection scenarios
    detection_scenarios = [
        # Scenario 1: Clothing scrap found
        {
            'detection_data': {
                'detections': [{
                    'class': 'fabric_red',
                    'confidence': 0.82,
                    'bbox': [150, 200, 180, 230],
                    'category': 'clothing'
                }]
            },
            'context': {
                **mission_context,
                'detection_type': 'image_detection',
                'latitude': 42.3605,
                'longitude': -71.0592,
                'altitude': 50,
                'drone_id': 'SAR_DRONE_01'
            },
            'expected_outcome': 'reference_items_found'
        },
        
        # Scenario 2: Possible target sighting
        {
            'detection_data': {
                'detections': [{
                    'class': 'person_small',
                    'confidence': 0.75,
                    'bbox': [300, 150, 350, 250],
                    'category': 'person'
                }]
            },
            'context': {
                **mission_context,
                'detection_type': 'image_detection',
                'latitude': 42.3610,
                'longitude': -71.0595,
                'altitude': 45,
                'drone_id': 'SAR_DRONE_01'
            },
            'expected_outcome': 'target_likely'
        },
        
        # Scenario 3: Personal item found
        {
            'detection_data': {
                'detections': [{
                    'class': 'backpack_small',
                    'confidence': 0.88,
                    'bbox': [200, 180, 240, 220],
                    'category': 'personal_item'
                }]
            },
            'context': {
                **mission_context,
                'detection_type': 'image_detection',
                'latitude': 42.3612,
                'longitude': -71.0598,
                'altitude': 40,
                'drone_id': 'SAR_DRONE_01'
            },
            'expected_outcome': 'reference_items_found'
        },
        
        # Scenario 4: Confirmed target found
        {
            'detection_data': {
                'detections': [{
                    'class': 'child',
                    'confidence': 0.92,
                    'bbox': [250, 100, 300, 200],
                    'category': 'person'
                }]
            },
            'context': {
                **mission_context,
                'detection_type': 'image_detection',
                'latitude': 42.3615,
                'longitude': -71.0600,
                'altitude': 35,
                'drone_id': 'SAR_DRONE_01',
                'mission_status': 'target_located'
            },
            'expected_outcome': 'target_found'
        },
        
        # Scenario 5: False positive (environmental artifact)
        {
            'detection_data': {
                'detections': [{
                    'class': 'plastic_bag',
                    'confidence': 0.45,
                    'bbox': [100, 300, 130, 330],
                    'category': 'debris'
                }]
            },
            'context': {
                **mission_context,
                'detection_type': 'image_detection',
                'latitude': 42.3608,
                'longitude': -71.0590,
                'altitude': 55,
                'drone_id': 'SAR_DRONE_01'
            },
            'expected_outcome': 'side_pocket'
        }
    ]
    
    return {
        'mission_context': mission_context,
        'scenarios': detection_scenarios
    }


async def run_sar_mission_simulation(engine: FeedbackEngine) -> None:
    """Run the complete Search and Rescue mission simulation."""
    
    print("=== Search and Rescue Mission Simulation ===")
    print("Scenario: Lost Child in Mixed Forest")
    print("=" * 60)
    
    # Get mission data
    mission_data = simulate_lost_child_mission()
    mission_context = mission_data['mission_context']
    scenarios = mission_data['scenarios']
    
    print(f"\nMission ID: {mission_context['mission_id']}")
    print(f"Target: {mission_context['target_description']['age']} year old child")
    print(f"Missing for: {mission_context['time_missing_hours']} hours")
    print(f"Search Area: {mission_context['search_area_km2']} km²")
    print(f"Weather: {mission_context['weather_conditions']}")
    print(f"Terrain: {mission_context['terrain_type']}")
    
    results = []
    
    # Process each detection scenario
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n--- Detection Scenario {i} ---")
        
        detection_data = scenario['detection_data']
        context = scenario['context']
        expected_outcome = scenario['expected_outcome']
        
        # Process through feedback engine
        result = engine.process_output(
            domain='search_rescue',
            raw_output=detection_data,
            context=context,
            agent_id=f"sar_drone_{context['drone_id']}"
        )
        
        results.append(result)
        
        # Display results
        print(f"Detection: {detection_data['detections'][0]['class']}")
        print(f"Confidence: {detection_data['detections'][0]['confidence']:.2f}")
        print(f"Location: {context['latitude']:.4f}, {context['longitude']:.4f}")
        print(f"Feedback: {result.feedback_type.value}")
        print(f"Validation Confidence: {result.confidence_score:.3f}")
        print(f"Trust Score: {result.trust_score:.3f}")
        print(f"Expected: {expected_outcome}")
        
        if result.validation_issues:
            print(f"Issues: {len(result.validation_issues)}")
        
        if result.recommendations:
            print(f"Recommendations: {result.recommendations[0]}")
    
    # Mission summary
    print(f"\n{'='*60}")
    print("=== Mission Summary ===")
    
    # Get domain statistics
    sar_domain = engine.domains.get('search_rescue')
    if sar_domain:
        mission_outcomes = sar_domain.get_mission_outcomes()
        print(f"Mission Success Rate: {mission_outcomes['success_rate']:.2%}")
        print(f"Target Found Rate: {mission_outcomes['target_found_rate']:.2%}")
        
        # Side pocket analysis
        side_pocket_analytics = sar_domain.get_side_pocket_analytics()
        summary = side_pocket_analytics.get('summary', {})
        print(f"Side Pocket Items: {summary.get('total_items', 0)}")
        
        # Check if retraining batch can be created
        pending_reviews = sar_domain.get_pending_reviews(limit=10)
        print(f"Pending Reviews: {len(pending_reviews)}")


async def demonstrate_side_pocket_workflow(engine: FeedbackEngine) -> None:
    """Demonstrate the side pocket workflow for continuous improvement."""
    
    print("\n=== Side Pocket Workflow Demonstration ===")
    
    sar_domain = engine.domains.get('search_rescue')
    if not sar_domain:
        print("Search and Rescue domain not available")
        return
    
    # Get pending reviews
    pending_items = sar_domain.get_pending_reviews(limit=5)
    print(f"Pending Side Pocket Items: {len(pending_items)}")
    
    # Simulate review of pending items
    for item in pending_items[:2]:  # Review first 2 items
        item_id = item['item_id']
        category = item['category']
        
        print(f"\nReviewing Item: {item_id}")
        print(f"Category: {category}")
        print(f"Reason: {item['reason']}")
        
        # Simulate review result
        review_result = {
            'final_category': 'false_positive' if 'plastic' in str(item) else 'confirmed_detection',
            'corrected_classification': {
                'class': 'environmental_artifact' if 'plastic' in str(item) else 'valid_detection',
                'confidence': 0.9
            },
            'use_for_retraining': True,
            'reviewer_confidence': 0.85,
            'notes': 'Reviewed by human expert'
        }
        
        # Submit review
        success = sar_domain.review_side_pocket_item(item_id, review_result)
        print(f"Review Status: {'Success' if success else 'Failed'}")
    
    # Try to create retraining batch
    print("\n--- Retraining Batch Creation ---")
    batch_result = sar_domain.create_retraining_batch()
    
    if batch_result.get('status') == 'success':
        print(f"Retraining Batch Created: {batch_result['batch_name']}")
        print(f"Items in Batch: {batch_result['item_count']}")
    else:
        print(f"Batch Creation: {batch_result.get('status', 'unknown')}")
        if 'count' in batch_result:
            print(f"Current Items: {batch_result['count']}")


async def main():
    """Main Search and Rescue example execution."""
    print("Universal Dual-Purpose Feedback Loop Framework")
    print("Search and Rescue: Lost Child Scenario")
    print("=" * 80)
    
    # Setup logging
    setup_logging()
    
    # Create and configure feedback engine
    print("Setting up Search and Rescue feedback engine...")
    engine = create_sar_feedback_engine()
    
    # Setup Search and Rescue domain
    print("Configuring Search and Rescue domain...")
    setup_sar_domain(engine)
    
    # Perform health check
    health = engine.health_check()
    print(f"System Health: {health['engine']}")
    print(f"SAR Domain: {health['domains'].get('search_rescue', 'Not Available')}")
    
    # Run mission simulation
    await run_sar_mission_simulation(engine)
    
    # Demonstrate side pocket workflow
    await demonstrate_side_pocket_workflow(engine)
    
    # Final analytics
    print("\n=== Final Analytics ===")
    engine_stats = engine.get_statistics()
    print(f"Total Processed: {engine_stats['total_processed']}")
    print(f"Average Processing Time: {engine_stats['average_processing_time']:.2f}ms")
    
    if engine.memory_store:
        analytics = engine.memory_store.get_analytics_data()
        print(f"Success Rate: {analytics.get('success_rate', 0):.2%}")
        print(f"Average Confidence: {analytics.get('average_confidence', 0):.3f}")
    
    print("\n" + "=" * 80)
    print("Search and Rescue simulation completed successfully!")
    print("The feedback loop is now continuously improving SAR drone operations.")
    print("Check 'sar_feedback_data' and 'sar_side_pocket' directories for stored data.")


if __name__ == "__main__":
    asyncio.run(main())

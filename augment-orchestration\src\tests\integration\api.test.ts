import request from 'supertest';
import { app } from '@/server/app';
import { PrismaClient } from '@prisma/client';
import jwt from 'jsonwebtoken';

const prisma = new PrismaClient();

describe('API Integration Tests', () => {
  let authToken: string;
  let testUserId: string;
  let testAgentId: string;
  let testWorkflowId: string;

  beforeAll(async () => {
    // Clean up test database
    await prisma.auditLog.deleteMany();
    await prisma.workflowExecution.deleteMany();
    await prisma.workflowTemplate.deleteMany();
    await prisma.agent.deleteMany();
    await prisma.userRole.deleteMany();
    await prisma.user.deleteMany();
    await prisma.role.deleteMany();

    // Create test user
    const testUser = await prisma.user.create({
      data: {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'hashedpassword',
        isActive: true,
      },
    });
    testUserId = testUser.id;

    // Generate auth token
    authToken = jwt.sign(
      { userId: testUser.id, username: testUser.username },
      process.env.JWT_SECRET || 'test-secret',
      { expiresIn: '1h' }
    );
  });

  afterAll(async () => {
    // Clean up
    await prisma.auditLog.deleteMany();
    await prisma.workflowExecution.deleteMany();
    await prisma.workflowTemplate.deleteMany();
    await prisma.agent.deleteMany();
    await prisma.userRole.deleteMany(); // if model name is UserRole
    await prisma.user.deleteMany();
    await prisma.userRole.deleteMany(); // if model name is UserRole
    await prisma.$disconnect();
  });

  describe('Authentication Endpoints', () => {
    test('POST /api/auth/register should create new user', async () => {
      const response = await request(app)
        .post('/api/auth/register')
        .send({
          username: 'newuser',
          email: '<EMAIL>',
          password: 'password123',
        });

      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('token');
      expect(response.body.data.user).toHaveProperty('username', 'newuser');
    });

    test('POST /api/auth/login should authenticate user', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'hashedpassword',
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('token');
    });

    test('POST /api/auth/login should reject invalid credentials', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'wrongpassword',
        });

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
    });
  });

  describe('Agent Management Endpoints', () => {
    test('POST /api/agents should create new agent', async () => {
      const response = await request(app)
        .post('/api/agents')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          name: 'Test Agent',
          description: 'A test agent for integration testing',
          role: 'Code Executor',
          capabilities: ['code_execution', 'testing'],
          parameters: {
            maxConcurrentTasks: 5,
            timeout: 30000,
          },
        });

      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('id');
      expect(response.body.data.name).toBe('Test Agent');
      testAgentId = response.body.data.id;
    });

    test('GET /api/agents should list all agents', async () => {
      const response = await request(app)
        .get('/api/agents')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data.length).toBeGreaterThan(0);
    });

    test('GET /api/agents/:id should return specific agent', async () => {
      const response = await request(app)
        .get(`/api/agents/${testAgentId}`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.id).toBe(testAgentId);
      expect(response.body.data.name).toBe('Test Agent');
    });

    test('PUT /api/agents/:id should update agent', async () => {
      const response = await request(app)
        .put(`/api/agents/${testAgentId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          name: 'Updated Test Agent',
          description: 'An updated test agent',
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.name).toBe('Updated Test Agent');
    });

    test('DELETE /api/agents/:id should delete agent', async () => {
      // Create another agent to delete
      const createResponse = await request(app)
        .post('/api/agents')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          name: 'Agent to Delete',
          description: 'This agent will be deleted',
          role: 'Data Analyst',
          capabilities: ['data_analysis'],
          parameters: {},
        });

      const agentToDeleteId = createResponse.body.data.id;

      const deleteResponse = await request(app)
        .delete(`/api/agents/${agentToDeleteId}`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(deleteResponse.status).toBe(200);
      expect(deleteResponse.body.success).toBe(true);

      // Verify agent is deleted
      const getResponse = await request(app)
        .get(`/api/agents/${agentToDeleteId}`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(getResponse.status).toBe(404);
    });
  });

  describe('Workflow Management Endpoints', () => {
    test('POST /api/workflows should create new workflow', async () => {
      const response = await request(app)
        .post('/api/workflows')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          name: 'Test Workflow',
          description: 'A test workflow for integration testing',
          definition: {
            stages: [
              {
                id: 'stage1',
                name: 'Initial Stage',
                agentRole: 'Code Executor',
                parameters: { task: 'initialize' },
                dependencies: [],
              },
              {
                id: 'stage2',
                name: 'Processing Stage',
                agentRole: 'Data Analyst',
                parameters: { task: 'process' },
                dependencies: ['stage1'],
              },
            ],
          },
          isActive: true,
        });

      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('id');
      expect(response.body.data.name).toBe('Test Workflow');
      testWorkflowId = response.body.data.id;
    });

    test('GET /api/workflows should list all workflows', async () => {
      const response = await request(app)
        .get('/api/workflows')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data.length).toBeGreaterThan(0);
    });

    test('POST /api/workflows/:id/execute should start workflow execution', async () => {
      const response = await request(app)
        .post(`/api/workflows/${testWorkflowId}/execute`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          context: {
            inputData: 'test data',
            priority: 'high',
          },
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('executionId');
      expect(response.body.data.status).toBe('PENDING');
    });

    test('GET /api/workflows/:id/executions should list workflow executions', async () => {
      const response = await request(app)
        .get(`/api/workflows/${testWorkflowId}/executions`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data)).toBe(true);
    });
  });

  describe('Evolution Engine Endpoints', () => {
    test('GET /api/evolution/status should return evolution status', async () => {
      const response = await request(app)
        .get('/api/evolution/status')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('isRunning');
      expect(response.body.data).toHaveProperty('currentGeneration');
    });

    test('POST /api/evolution/start should start evolution process', async () => {
      const response = await request(app)
        .post('/api/evolution/start')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          parameters: {
            populationSize: 10,
            mutationRate: 0.1,
            selectionPressure: 0.8,
            maxGenerations: 5,
          },
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('sessionId');
    });

    test('GET /api/evolution/history should return evolution history', async () => {
      const response = await request(app)
        .get('/api/evolution/history')
        .set('Authorization', `Bearer ${authToken}`)
        .query({ limit: 10 });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data)).toBe(true);
    });

    test('POST /api/evolution/stop should stop evolution process', async () => {
      const response = await request(app)
        .post('/api/evolution/stop')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });
  });

  describe('Context Management Endpoints', () => {
    let testContextId: string;

    test('POST /api/context should create new context layer', async () => {
      const response = await request(app)
        .post('/api/context')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          name: 'Test Context',
          type: 'WORKFLOW',
          data: {
            testKey: 'testValue',
            configuration: {
              setting1: true,
              setting2: 42,
            },
          },
          priority: 5,
          tags: ['test', 'integration'],
          accessLevel: 'PUBLIC',
        });

      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('layerId');
      testContextId = response.body.data.layerId;
    });

    test('GET /api/context should list context layers', async () => {
      const response = await request(app)
        .get('/api/context')
        .set('Authorization', `Bearer ${authToken}`)
        .query({ limit: 10 });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data.length).toBeGreaterThan(0);
    });

    test('POST /api/context/query should query context layers', async () => {
      const response = await request(app)
        .post('/api/context/query')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          types: ['WORKFLOW'],
          tags: ['test'],
          limit: 10,
          includeMetadata: true,
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data)).toBe(true);
    });

    test('PUT /api/context/:id should update context layer', async () => {
      const response = await request(app)
        .put(`/api/context/${testContextId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          data: {
            testKey: 'updatedValue',
            newKey: 'newValue',
          },
          priority: 7,
          tags: ['test', 'integration', 'updated'],
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });

    test('GET /api/context/stats/overview should return context statistics', async () => {
      const response = await request(app)
        .get('/api/context/stats/overview')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('totalLayers');
      expect(response.body.data).toHaveProperty('cacheSize');
    });
  });

  describe('Security & RBAC Endpoints', () => {
    test('GET /api/security/roles should list all roles', async () => {
      const response = await request(app)
        .get('/api/security/roles')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data)).toBe(true);
    });

    test('GET /api/security/audit-logs should return audit logs', async () => {
      const response = await request(app)
        .get('/api/security/audit-logs')
        .set('Authorization', `Bearer ${authToken}`)
        .query({ limit: 10 });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data.logs)).toBe(true);
      expect(response.body.data).toHaveProperty('total');
    });

    test('GET /api/security/users should list users', async () => {
      const response = await request(app)
        .get('/api/security/users')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data)).toBe(true);
    });
  });

  describe('Error Handling', () => {
    test('Should return 401 for requests without auth token', async () => {
      const response = await request(app)
        .get('/api/agents');

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
    });

    test('Should return 404 for non-existent resources', async () => {
      const response = await request(app)
        .get('/api/agents/non-existent-id')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
    });

    test('Should return 400 for invalid request data', async () => {
      const response = await request(app)
        .post('/api/agents')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          // Missing required fields
          description: 'Invalid agent without name',
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });
  });

  describe('Rate Limiting', () => {
    test('Should handle multiple concurrent requests', async () => {
      const promises = Array.from({ length: 5 }, () =>
        request(app)
          .get('/api/agents')
          .set('Authorization', `Bearer ${authToken}`)
      );

      const responses = await Promise.all(promises);
      
      responses.forEach(response => {
        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);
      });
    });
  });
});

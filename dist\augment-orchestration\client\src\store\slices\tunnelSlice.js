"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.clearError = exports.fetchTunnels = void 0;
const toolkit_1 = require("@reduxjs/toolkit");
const api_1 = require("../../services/api");
const initialState = {
    tunnels: [],
    isLoading: false,
    error: null,
};
exports.fetchTunnels = (0, toolkit_1.createAsyncThunk)('tunnel/fetchAll', async () => {
    const response = await api_1.tunnelApi.getAll();
    return response.data;
});
const tunnelSlice = (0, toolkit_1.createSlice)({
    name: 'tunnel',
    initialState,
    reducers: {
        clearError: (state) => {
            state.error = null;
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(exports.fetchTunnels.fulfilled, (state, action) => {
            state.tunnels = action.payload;
        });
    },
});
exports.clearError = tunnelSlice.actions.clearError;
exports.default = tunnelSlice.reducer;

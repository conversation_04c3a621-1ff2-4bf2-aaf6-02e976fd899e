# EcoStamp Installation Instructions

## For Opera

### Quick Install

1. Open Opera and go to `opera://extensions/`
2. Enable "Developer mode" (toggle in top right)
3. Click "Load unpacked"
4. Select the extracted EcoStamp folder
5. The extension will appear in your toolbar


## After Installation

1. Visit any AI platform (<PERSON>tGPT, <PERSON>, Gemini, etc.)
2. Look for the EcoStamp widget in the top-right corner
3. Start a conversation to see real-time environmental impact
4. Click the EcoStamp icon in your toolbar for settings

## Supported Platforms

- ChatGPT (chat.openai.com)
- <PERSON> (claude.ai)
- Google Gemini (gemini.google.com)
- Microsoft Copilot (copilot.microsoft.com)
- You.com
- Perplexity AI
- Poe.com

## Troubleshooting

### Extension Not Working
1. Refresh the AI platform page
2. Check if EcoStamp server is running (localhost:3000)
3. Verify extension permissions are granted

### Widget Not Appearing
1. Check extension settings (click toolbar icon)
2. Ensure "Show Widget" is enabled
3. Try refreshing the page

### Need Help?
- GitHub: https://github.com/chris-ai-dev/Time_Stamp_Project
- Website: http://localhost:3000 (when server is running)

---

**Making AI environmentally accountable!** 🌱

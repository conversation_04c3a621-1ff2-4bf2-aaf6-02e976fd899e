<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Thread-Merging Orchestrator</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }

        .container {
            max-inline-size: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .main-form {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #555;
        }

        input, select, textarea {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e1e5e9;
            border-radius: 5px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }

        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #667eea;
        }

        textarea {
            resize: vertical;
            min-height: 100px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 0.75rem 2rem;
            border-radius: 5px;
            font-size: 1rem;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 2rem;
            color: #667eea;
        }

        .loading.show {
            display: block;
        }

        .results {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            display: none;
        }

        .results.show {
            display: block;
        }

        .result-header {
            border-bottom: 2px solid #e1e5e9;
            padding-bottom: 1rem;
            margin-bottom: 1.5rem;
        }

        .result-meta {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .meta-item {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 5px;
            text-align: center;
        }

        .meta-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
        }

        .meta-label {
            font-size: 0.9rem;
            color: #666;
            margin-top: 0.25rem;
        }

        .analysis-content {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 5px;
            border-left: 4px solid #667eea;
            white-space: pre-wrap;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 0.9rem;
            line-height: 1.5;
            max-height: 500px;
            overflow-y: auto;
        }

        .tabs {
            display: flex;
            border-bottom: 2px solid #e1e5e9;
            margin-bottom: 1rem;
        }

        .tab {
            padding: 0.75rem 1.5rem;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 1rem;
            color: #666;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }

        .tab.active {
            color: #667eea;
            border-bottom-color: #667eea;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .error {
            background: #fee;
            color: #c33;
            padding: 1rem;
            border-radius: 5px;
            border-left: 4px solid #c33;
            margin: 1rem 0;
            display: none;
        }

        .error.show {
            display: block;
        }

        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }

            .result-meta {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔗 Thread-Merging Orchestrator</h1>
            <p>Automated system that searches, highlights, and merges relevant threads from ChatGPT and Perplexity, then feeds them to Claude/Gemini for code generation/analysis</p>
        </div>

        <div class="main-form">
            <form id="orchestrationForm">
                <div class="form-group">
                    <label for="query">Search Query</label>
                    <textarea id="query" placeholder="Enter your search query to find relevant threads..." required></textarea>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="targetLLM">Target LLM</label>
                        <select id="targetLLM">
                            <option value="claude">Claude (Anthropic)</option>
                            <option value="gemini">Gemini (Google)</option>
                            <option value="openai">ChatGPT (OpenAI)</option>
                            <option value="mistral">Mistral AI</option>
                            <option value="cohere">Cohere</option>
                            <option value="huggingface">Llama (HuggingFace)</option>
                            <option value="groq">Groq</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="task">Analysis Task</label>
                        <select id="task">
                            <option value="code_generation">Code Generation</option>
                            <option value="code_analysis">Code Analysis</option>
                            <option value="summarization">Summarization</option>
                        </select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="maxThreads">Max Threads</label>
                        <input type="number" id="maxThreads" value="10" min="1" max="50">
                    </div>

                    <div class="form-group">
                        <label for="format">Output Format</label>
                        <select id="format">
                            <option value="markdown">Markdown</option>
                            <option value="json">JSON</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label>
                        <input type="checkbox" id="useCache" checked> Use cached threads if available
                    </label>
                </div>

                <button type="submit" class="btn" id="submitBtn">🚀 Start Orchestration</button>
            </form>
        </div>

        <div class="loading" id="loading">
            <h3>🔄 Processing your request...</h3>
            <p>This may take a few minutes depending on the number of threads to process.</p>
        </div>

        <div class="error" id="error"></div>

        <div class="results" id="results">
            <div class="result-header">
                <h2>📊 Orchestration Results</h2>
            </div>

            <div class="result-meta" id="resultMeta">
                <!-- Meta information will be populated here -->
            </div>

            <div class="tabs">
                <button class="tab active" onclick="showTab('analysis')">Analysis</button>
                <button class="tab" onclick="showTab('metadata')">Metadata</button>
                <button class="tab" onclick="showTab('raw')">Raw Data</button>
            </div>

            <div class="tab-content active" id="analysisTab">
                <div class="analysis-content" id="analysisContent"></div>
            </div>

            <div class="tab-content" id="metadataTab">
                <pre id="metadataContent"></pre>
            </div>

            <div class="tab-content" id="rawTab">
                <pre id="rawContent"></pre>
            </div>
        </div>
    </div>

    <script>
        const form = document.getElementById('orchestrationForm');
        const loading = document.getElementById('loading');
        const results = document.getElementById('results');
        const error = document.getElementById('error');
        const submitBtn = document.getElementById('submitBtn');

        form.addEventListener('submit', async (e) => {
            e.preventDefault();

            const formData = new FormData(form);
            const query = document.getElementById('query').value;
            const options = {
                targetLLM: document.getElementById('targetLLM').value,
                task: document.getElementById('task').value,
                maxThreads: parseInt(document.getElementById('maxThreads').value),
                format: document.getElementById('format').value,
                useCache: document.getElementById('useCache').checked
            };

            // Show loading state
            loading.classList.add('show');
            results.classList.remove('show');
            error.classList.remove('show');
            submitBtn.disabled = true;

            try {
                const response = await fetch('/api/orchestrate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ query, options })
                });

                const data = await response.json();

                if (data.success) {
                    displayResults(data.result);
                } else {
                    showError(data.error || 'Unknown error occurred');
                }
            } catch (err) {
                showError('Network error: ' + err.message);
            } finally {
                loading.classList.remove('show');
                submitBtn.disabled = false;
            }
        });

        function displayResults(result) {
            // Update meta information
            const metaContainer = document.getElementById('resultMeta');
            metaContainer.innerHTML = `
                <div class="meta-item">
                    <div class="meta-value">${result.duration}ms</div>
                    <div class="meta-label">Total Duration</div>
                </div>
                <div class="meta-item">
                    <div class="meta-value">${result.steps.retrieval.threadsFound}</div>
                    <div class="meta-label">Threads Retrieved</div>
                </div>
                <div class="meta-item">
                    <div class="meta-value">${result.steps.search.relevantThreads}</div>
                    <div class="meta-label">Relevant Threads</div>
                </div>
                <div class="meta-item">
                    <div class="meta-value">${result.analysis.targetLLM}</div>
                    <div class="meta-label">Target LLM</div>
                </div>
            `;

            // Update analysis content
            document.getElementById('analysisContent').textContent = result.analysis.analysis;

            // Update metadata
            document.getElementById('metadataContent').textContent = JSON.stringify(result.metadata, null, 2);

            // Update raw data
            document.getElementById('rawContent').textContent = JSON.stringify(result, null, 2);

            results.classList.add('show');
        }

        function showError(message) {
            error.textContent = message;
            error.classList.add('show');
        }

        function showTab(tabName) {
            // Remove active class from all tabs and content
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

            // Add active class to clicked tab and corresponding content
            event.target.classList.add('active');
            document.getElementById(tabName + 'Tab').classList.add('active');
        }
    </script>
</body>
</html>

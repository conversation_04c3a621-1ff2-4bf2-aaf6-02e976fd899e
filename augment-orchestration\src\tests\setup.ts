import { PrismaClient } from '@prisma/client';

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// Mock environment variables
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret';
process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/test_db';

// Mock Prisma Client
jest.mock('@prisma/client', () => ({
  PrismaClient: jest.fn().mockImplementation(() => ({
    $connect: jest.fn(),
    $disconnect: jest.fn(),
    user: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    agent: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    workflowTemplate: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    workflowExecution: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    sharedContext: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    auditLog: {
      findMany: jest.fn(),
      create: jest.fn(),
    },
    comment: {
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    notification: {
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    role: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    userRole: {
      findMany: jest.fn(),
      create: jest.fn(),
      delete: jest.fn(),
    },
  })),
}));

// Mock Socket.IO
jest.mock('socket.io', () => ({
  Server: jest.fn().mockImplementation(() => ({
    on: jest.fn(),
    emit: jest.fn(),
    to: jest.fn().mockReturnThis(),
    use: jest.fn(),
  })),
}));

// Mock EventBus
jest.mock('../server/utils/EventBus', () => ({
  EventBus: jest.fn().mockImplementation(() => ({
    emit: jest.fn(),
    on: jest.fn(),
    off: jest.fn(),
  })),
}));

// Global test utilities
global.testUtils = {
  createMockUser: (overrides = {}) => ({
    id: 'test-user-id',
    username: 'testuser',
    email: '<EMAIL>',
    password: 'hashedpassword',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    ...overrides,
  }),

  createMockAgent: (overrides = {}) => ({
    id: 'test-agent-id',
    name: 'Test Agent',
    description: 'A test agent',
    role: 'Code Executor',
    capabilities: ['code_execution', 'testing'],
    parameters: { maxConcurrentTasks: 5 },
    isActive: true,
    currentLoad: 0,
    maxConcurrentTasks: 5,
    createdAt: new Date(),
    updatedAt: new Date(),
    ...overrides,
  }),

  createMockWorkflow: (overrides = {}) => ({
    id: 'test-workflow-id',
    name: 'Test Workflow',
    description: 'A test workflow',
    definition: {
      stages: [
        {
          id: 'stage1',
          name: 'Test Stage',
          agentRole: 'Code Executor',
          parameters: { task: 'test' },
          dependencies: [],
        },
      ],
    },
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    ...overrides,
  }),

  createMockContext: (overrides = {}) => ({
    id: 'test-context-id',
    name: 'Test Context',
    type: 'WORKFLOW',
    data: { key: 'value' },
    priority: 5,
    tags: ['test'],
    accessLevel: 'PUBLIC',
    createdAt: new Date(),
    updatedAt: new Date(),
    ...overrides,
  }),

  createMockRole: (overrides = {}) => ({
    id: 'test-role-id',
    name: 'Test Role',
    description: 'A test role',
    hierarchy: 50,
    isSystemRole: false,
    permissions: [
      {
        id: 'test-permission-id',
        name: 'test:read',
        resource: 'test',
        action: 'read',
      },
    ],
    createdAt: new Date(),
    updatedAt: new Date(),
    ...overrides,
  }),
};

// Extend Jest matchers
expect.extend({
  toBeValidUUID(received) {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    const pass = uuidRegex.test(received);
    
    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid UUID`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid UUID`,
        pass: false,
      };
    }
  },

  toBeValidDate(received) {
    const pass = received instanceof Date && !isNaN(received.getTime());
    
    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid Date`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid Date`,
        pass: false,
      };
    }
  },

  toHaveValidStructure(received, expectedKeys) {
    const receivedKeys = Object.keys(received);
    const missingKeys = expectedKeys.filter(key => !receivedKeys.includes(key));
    const pass = missingKeys.length === 0;
    
    if (pass) {
      return {
        message: () => `expected object not to have all required keys: ${expectedKeys.join(', ')}`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected object to have keys: ${expectedKeys.join(', ')}, but missing: ${missingKeys.join(', ')}`,
        pass: false,
      };
    }
  },
});

// Cleanup after each test
afterEach(() => {
  jest.clearAllMocks();
});

// Global error handler for unhandled promises
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Increase timeout for integration tests
jest.setTimeout(30000);

# 📝 EcoStamp Changelog

All notable changes to the EcoStamp extension will be documented in this file.

## [1.0.0] - 2025-01-02 🎉

### **🚀 Initial Release - Enhanced with Real-time Benchmarks**

#### **✨ Features**

- **Universal AI Platform Support** - Works with <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Perplex<PERSON>, Poe, Character.AI, You.com, Hugging Face, and ANY AI platform
- **Smart Platform Detection** - Automatically detects and adapts to different AI platforms
- **Real-time Environmental Impact Tracking** - Shows energy usage, water consumption, and eco-levels
- **5-Leaf Eco-Level System** - Visual meter showing environmental efficiency (🌿🌿🌿🌿🌿 to 🌿🍂🍂🍂🍂)
- **Platform-Specific Optimizations** - Custom selectors and styling for major AI platforms
- **Universal Fallback Detection** - Works with unknown/new AI platforms automatically
- **Real-time Model Detection** - Identifies specific AI models (GPT-4, Claude-3, Gemini-Pro, etc.)
- **🆕 Real-time Benchmarks** - Live AI provider data updated via scheduled jobs
- **🆕 Provider API Integration** - Direct data from OpenAI, Anthropic, Google APIs
- **🆕 Automated Data Collection** - Node-cron scheduled updates every day at 2:00 UTC

#### **📊 Analytics Dashboard**

- **Cross-platform Statistics** - Track usage across all AI platforms
- **Platform Usage Breakdown** - See which AI platforms you use most
- **Eco-level Distribution** - Monitor your environmental efficiency over time
- **Export Functionality** - Download your usage data
- **Real-time Badge Updates** - Extension icon shows total interactions

#### **🔒 Privacy & Security**

- **Local Storage Only** - No data sent to external servers
- **Minimal Permissions** - Only activeTab and storage permissions
- **CSP Compatible** - Works with strict Content Security Policies
- **Open Source** - Fully transparent code

#### **🎨 User Experience**

- **Beautiful Footers** - Seamlessly integrated with each platform's design
- **Platform-specific Colors** - ChatGPT (green), Claude (orange), Gemini (blue), etc.
- **Responsive Design** - Works on desktop and mobile browsers
- **Dark Mode Support** - Adapts to system/browser theme preferences
- **Accessibility** - High contrast mode and reduced motion support

#### **🛠️ Technical Features**

- **Manifest V3** - Latest Chrome extension standard
- **Efficient Processing** - Optimized selectors and debounced processing
- **Smart Content Analysis** - Detects code, lists, markdown, and complexity
- **Platform Efficiency Multipliers** - Accounts for different AI platform efficiencies
- **Error Handling** - Graceful degradation and error recovery

### **🌍 Supported Platforms**

- ChatGPT (chat.openai.com, chatgpt.com)
- Claude (claude.ai)
- Gemini (gemini.google.com, bard.google.com)
- Perplexity (perplexity.ai)
- Poe (poe.com)
- Character.AI (character.ai)
- You.com (you.com)
- Hugging Face (huggingface.co)
- Universal detection for any AI platform

### **📱 Browser Support**

- Chrome 88+
- Microsoft Edge 88+
- Brave Browser
- Opera (Chromium-based)

### **🔧 Developer Features**

- Debug console access via `ecoStamp` global object
- Manual processing trigger: `ecoStamp.process()`
- Platform detection: `ecoStamp.detect()`
- Configuration access: `ecoStamp.config`

---

## **🚀 Coming Soon**

### **v1.1.0 - Enhanced Analytics**

- Historical trend charts
- Weekly/monthly reports
- Carbon footprint calculations
- Comparison with other users (anonymized)

### **v1.2.0 - Advanced Features**

- Custom eco-level thresholds
- AI model recommendations
- Integration with carbon offset programs
- Team/organization dashboards

### **v1.3.0 - Platform Expansion**

- Firefox support
- Safari support
- Mobile browser support
- API for third-party integrations

---

## **📋 Version Format**

We use [Semantic Versioning](https://semver.org/):

- **MAJOR.MINOR.PATCH** (e.g., 1.0.0)
- **MAJOR** - Breaking changes
- **MINOR** - New features (backward compatible)
- **PATCH** - Bug fixes (backward compatible)

---

## **🔗 Links**

- **[Chrome Web Store](https://chrome.google.com/webstore)** - Official extension
- **[GitHub Releases](https://github.com/ecostamp/ecostamp-extension/releases)** - Download releases
- **[Product Hunt](https://www.producthunt.com/posts/ecostamp)** - Launch page
- **[Documentation](https://ecostamp.github.io/docs)** - Full documentation

---

**🌱 Thank you for using EcoStamp and helping make AI more environmentally conscious!**

# 🎭 Meta-Orchestration System

A comprehensive meta-orchestration system that merges thread-merging orchestration, code orchestration, and mix-and-match workflows with paid and free AI assistants, ensuring deduplication, instant fallback, and multi-IDE support.

## ✨ Features

### 🎯 Role-Based AI Assistant Assignment
- **Analyzer**: Augment Code, Continue, Ollama, SuperAGI
- **Generator**: Cursor, Windsurf, GitHub Copilot, Cline, Aider
- **Completer**: Tabnine, GitHub Copilot, Cursor, Continue
- **Validator**: Qodo, Augment Code, Windsurf, AutoGen
- **Documenter**: Cursor, Windsurf, GitHub Copilot, Aider, Ollama

### 🔄 Instant Fallback Logic
- Automatic failover when primary assistant fails
- Circuit breaker pattern for failed assistants
- Performance-based fallback ordering
- Real-time availability monitoring

### 🚫 Universal Deduplication
- Output deduplication with tagging and filtering
- Context-aware result aggregation
- Intelligent merge strategies

### 💻 Multi-IDE Support
- **VS Code**: Native extension with real-time sync
- **JetBrains**: Plugin for IntelliJ, PyCharm, WebStorm, etc.
- **Vim/Neovim**: Plugin with shared configuration
- **Shared Config**: `.ai-orchestration.yaml` for consistent settings

### 🔗 Mix-and-Match Workflows
- **Feature Implementation**: Analyzer → Generator → Completer → Validator → Documenter
- **Bug Fix**: Analyzer → Generator → Validator → Documenter
- **Code Review**: Analyzer → Validator → Documenter
- **Refactoring**: Analyzer → Planning → Generator → Validator → Documenter
- **Testing**: Analyzer → Generator → Validator → Documenter

### 🤖 Universal AI Assistant Support

#### Premium/Paid Assistants
- **Augment Code**: Primary codebase analyzer with 200K+ token context
- **Cursor**: AI-powered code generation with Claude 3.5 Sonnet
- **Windsurf**: Multi-file editing and architecture-aware changes
- **Tabnine**: Real-time completion and suggestions
- **GitHub Copilot**: AI pair programmer with GitHub integration
- **Qodo**: Testing and validation specialist

#### Free/Open Source Assistants
- **Ollama**: Local LLM runner (CodeLlama, DeepSeek-Coder, Mistral)
- **LM Studio**: Local model management and inference
- **Cline**: Local AI assistant with privacy focus
- **Continue**: Open source code assistant
- **Aider**: AI pair programming with Git integration

#### Enterprise/Multi-Agent
- **SuperAGI**: Multi-agent orchestration platform
- **AutoGen**: Microsoft's multi-agent framework

## 🚀 Quick Start

### 1. Installation

```bash
# Clone or navigate to your project
cd your-project

# Install dependencies
cd ai-orchestration/meta-orchestrator
npm install

# Run interactive setup
npm run setup
```

### 2. Configuration

The setup wizard will guide you through:
- Selecting AI assistants to enable
- Configuring role assignments
- Setting up fallback chains
- IDE integration preferences
- Security and compliance settings

### 3. Usage

```bash
# Start the meta-orchestration system
npm start

# Execute requests interactively
npm run execute

# Launch web dashboard
npm run dashboard

# Check system status
npm run status
```

## 📋 Configuration

### Shared Project Configuration (`.ai-orchestration.yaml`)

```yaml
version: '1.0.0'
project:
  name: my-project
  type: javascript
  language: typescript
  framework: react

roles:
  analyzer:
    primary: augment-code
    fallbacks: [continue, ollama]
  generator:
    primary: cursor
    fallbacks: [windsurf, github-copilot, cline]
  completer:
    primary: tabnine
    fallbacks: [github-copilot, cursor]
  validator:
    primary: qodo
    fallbacks: [augment-code, windsurf]
  documenter:
    primary: cursor
    fallbacks: [windsurf, github-copilot, ollama]

preferences:
  skipDocumentation: false
  skipValidation: false
  preferredComplexity: medium
```

### Role Assignment Configuration

```yaml
assignments:
  analyzer:
    primary: augment-code
    fallbacks: [continue, ollama, superagi]
  generator:
    primary: cursor
    fallbacks: [windsurf, github-copilot, cline, aider]
  completer:
    primary: tabnine
    fallbacks: [github-copilot, cursor, continue]
  validator:
    primary: qodo
    fallbacks: [augment-code, windsurf, autogen]
  documenter:
    primary: cursor
    fallbacks: [windsurf, github-copilot, aider, ollama]

optimization:
  enabled: true
  performanceBasedReordering: true
  automaticFailover: true
  circuitBreakerEnabled: true
```

## 🎯 Usage Examples

### Feature Implementation

```bash
# Interactive mode
meta-orchestrator execute --interactive

# Direct execution
meta-orchestrator execute \
  --type feature \
  --description "Implement user authentication with JWT" \
  --workflow feature-implementation
```

### Bug Fix

```bash
meta-orchestrator execute \
  --type bug-fix \
  --description "Fix memory leak in data processing pipeline"
```

### Code Review

```bash
meta-orchestrator execute \
  --type review \
  --description "Review security implementation in auth module"
```

## 🔧 Advanced Configuration

### Custom Workflows

Create custom workflows by defining step sequences:

```yaml
custom:
  security-audit:
    name: Security Audit
    description: Comprehensive security analysis and fixes
    steps:
      - id: analysis
        role: analyzer
        name: Security Analysis
        analysisType: security
        required: true
      - id: validation
        role: validator
        name: Vulnerability Assessment
        validationType: security
        required: true
      - id: generation
        role: generator
        name: Security Fixes
        generationType: security-fix
        required: true
      - id: documentation
        role: documenter
        name: Security Documentation
        documentationType: security
        required: true
```

### Adapter Configuration

```yaml
adapters:
  cursor:
    enabled: true
    apiKey: ${CURSOR_API_KEY}
    model: claude-3.5-sonnet
    timeout: 60000
    retries: 3
    
  ollama:
    enabled: true
    baseUrl: http://localhost:11434
    models: [codellama, deepseek-coder, mistral]
    timeout: 60000
    
  augment-code:
    enabled: true
    apiEndpoint: http://localhost:8080
    features: [analysis, context, search, validation]
```

## 🔒 Security & Compliance

### SOC2 Compliance
- Secure credential storage and encryption
- Audit logging and monitoring
- Access controls and authentication

### IP Protection
- No training on proprietary code
- Local processing options (Ollama, LM Studio)
- Encrypted configuration storage

### GDPR/HIPAA Support
- Data minimization and retention policies
- Consent management
- Privacy-by-design architecture

## 📊 Monitoring & Analytics

- Request success rates by assistant
- Average response times
- Fallback activation frequency
- Role performance analytics

### Dashboard Features
- Real-time system status
- Assistant health monitoring
- Performance trends
- Configuration management

## 🔌 IDE Integration

### VS Code Extension
```bash
# Install from marketplace
code --install-extension meta-orchestration.vscode

# Or install locally
cd ide-extensions/vscode
npm install
npm run build
code --install-extension meta-orchestration-*.vsix
```

### JetBrains Plugin
```bash
# Install from plugin marketplace
# Or build locally
cd ide-extensions/jetbrains
./gradlew buildPlugin
```

### Vim/Neovim Plugin
```bash
# Install with your preferred plugin manager
Plug 'meta-orchestration/vim-plugin'

# Or manually
git clone https://github.com/meta-orchestration/vim-plugin ~/.vim/pack/meta-orchestration/start/
```

## 🛠️ Development

### Running Tests

```bash
# Run all tests
npm test

# Test specific components
npm run test:adapters
npm run test:workflows
npm run test:integration
```

### Development Mode

```bash
# Start in development mode with hot reload
npm run dev

# Enable debug logging
DEBUG=meta-orchestration:* npm start
```

## 📚 API Reference

### REST API

```bash
# Execute request
POST /api/execute
{
  "type": "feature",
  "description": "Implement user authentication",
  "context": {
    "projectPath": "/path/to/project",
    "language": "typescript"
  }
}

# Get system status
GET /api/status

# Update role assignment
PUT /api/roles/analyzer
{
  "primary": "augment-code",
  "fallbacks": ["continue", "ollama"]
}
```

### WebSocket API

```javascript
const ws = new WebSocket('ws://localhost:3001');

ws.send(JSON.stringify({
  type: 'execute',
  data: {
    type: 'feature',
    description: 'Add user dashboard'
  }
}));
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

MIT License - see [LICENSE](LICENSE) for details.

## 🆘 Support

- 📖 [Documentation](https://meta-orchestration.dev/docs)
- 💬 [Discord Community](https://discord.gg/meta-orchestration)
- 🐛 [Issue Tracker](https://github.com/meta-orchestration/issues)
- 📧 [Email Support](mailto:<EMAIL>)

---

**Meta-Orchestration System** - Orchestrating the future of AI-assisted development 🚀

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔒 EcoStamp Security Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c5aa0 0%, #1e3c72 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .dashboard {
            padding: 30px;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .metric-card {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            border-left: 5px solid #2c5aa0;
            transition: transform 0.3s ease;
        }
        
        .metric-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        
        .metric-card.success {
            border-left-color: #28a745;
        }
        
        .metric-card.warning {
            border-left-color: #ffc107;
        }
        
        .metric-card.danger {
            border-left-color: #dc3545;
        }
        
        .metric-value {
            font-size: 3em;
            font-weight: bold;
            color: #2c5aa0;
            margin-bottom: 10px;
        }
        
        .metric-label {
            color: #6c757d;
            font-size: 1.1em;
            margin-bottom: 15px;
        }
        
        .metric-details {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .badge {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: bold;
        }
        
        .badge.success {
            background: #d4edda;
            color: #155724;
        }
        
        .badge.warning {
            background: #fff3cd;
            color: #856404;
        }
        
        .badge.danger {
            background: #f8d7da;
            color: #721c24;
        }
        
        .actions {
            background: #e9ecef;
            padding: 25px;
            border-radius: 10px;
            margin-top: 30px;
        }
        
        .actions h3 {
            color: #2c5aa0;
            margin-bottom: 20px;
        }
        
        .command-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
        }
        
        .command-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        
        .command-title {
            font-weight: bold;
            color: #2c5aa0;
            margin-bottom: 5px;
        }
        
        .command-code {
            background: #f1f3f4;
            padding: 8px 12px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            color: #495057;
            margin: 5px 0;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-indicator.green {
            background: #28a745;
        }
        
        .status-indicator.yellow {
            background: #ffc107;
        }
        
        .status-indicator.red {
            background: #dc3545;
        }
        
        .footer {
            text-align: center;
            padding: 20px;
            color: #6c757d;
            border-top: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔒 EcoStamp Security Dashboard</h1>
            <p>Comprehensive Security Scanning & Software Composition Analysis</p>
            <p><small>Solo Developer Enterprise-Grade Security Solution</small></p>
        </div>
        
        <div class="dashboard">
            <div class="metrics-grid">
                <div class="metric-card success">
                    <div class="metric-value">0</div>
                    <div class="metric-label">🛡️ Critical Vulnerabilities</div>
                    <div class="metric-details">
                        <span class="badge success">✅ Secure</span>
                        <span class="badge success">🔍 Scanned</span>
                    </div>
                </div>
                
                <div class="metric-card success">
                    <div class="metric-value">✓</div>
                    <div class="metric-label">📜 License Compliance</div>
                    <div class="metric-details">
                        <span class="badge success">MIT Compatible</span>
                        <span class="badge success">No GPL Issues</span>
                    </div>
                </div>
                
                <div class="metric-card success">
                    <div class="metric-value">📦</div>
                    <div class="metric-label">SBOM Generated</div>
                    <div class="metric-details">
                        <span class="badge success">CycloneDX</span>
                        <span class="badge success">Compliant</span>
                    </div>
                </div>
                
                <div class="metric-card success">
                    <div class="metric-value">🔍</div>
                    <div class="metric-label">Code Analysis</div>
                    <div class="metric-details">
                        <span class="badge success">ESLint Security</span>
                        <span class="badge success">Clean Code</span>
                    </div>
                </div>
            </div>
            
            <div class="actions">
                <h3>🚀 Security Commands</h3>
                <div class="command-grid">
                    <div class="command-card">
                        <div class="command-title">
                            <span class="status-indicator green"></span>
                            Quick Security Audit
                        </div>
                        <div class="command-code">npm run security:audit</div>
                        <small>Fast vulnerability scan using NPM audit</small>
                    </div>
                    
                    <div class="command-card">
                        <div class="command-title">
                            <span class="status-indicator green"></span>
                            License Compliance
                        </div>
                        <div class="command-code">npm run security:licenses</div>
                        <small>Check open-source license compliance</small>
                    </div>
                    
                    <div class="command-card">
                        <div class="command-title">
                            <span class="status-indicator green"></span>
                            Generate SBOM
                        </div>
                        <div class="command-code">npm run security:sbom</div>
                        <small>Create Software Bill of Materials</small>
                    </div>
                    
                    <div class="command-card">
                        <div class="command-title">
                            <span class="status-indicator green"></span>
                            Security Linting
                        </div>
                        <div class="command-code">npm run security:eslint</div>
                        <small>Static security analysis with ESLint</small>
                    </div>
                    
                    <div class="command-card">
                        <div class="command-title">
                            <span class="status-indicator yellow"></span>
                            Full Security Scan
                        </div>
                        <div class="command-code">npm run security:full-scan</div>
                        <small>Complete security analysis suite</small>
                    </div>
                    
                    <div class="command-card">
                        <div class="command-title">
                            <span class="status-indicator yellow"></span>
                            Generate Report
                        </div>
                        <div class="command-code">npm run security:report</div>
                        <small>Comprehensive HTML/JSON security report</small>
                    </div>
                </div>
            </div>
            
            <div style="background: #d1ecf1; padding: 20px; border-radius: 10px; margin-top: 30px; border-left: 5px solid #17a2b8;">
                <h4 style="color: #0c5460; margin-bottom: 15px;">🎯 JFrog Xray Equivalent Features</h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;">
                    <div>✅ Enhanced CVE Detection</div>
                    <div>✅ FOSS License Compliance</div>
                    <div>✅ Automated SBOM Generation</div>
                    <div>✅ Security Insights</div>
                    <div>✅ Real-time Scanning</div>
                    <div>✅ Compliance Management</div>
                    <div>✅ Vulnerability Prioritization</div>
                    <div>✅ Supply Chain Security</div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>🌱 <strong>EcoStamp Security Framework</strong> - Making AI environmental impact visible while keeping your code secure!</p>
            <p><small>Enterprise-grade security for solo developers using 100% open-source tools</small></p>
        </div>
    </div>
</body>
</html>

/**
 * Hash Registry System
 * Stores and manages SHA-256 hashes for verification and search
 */

import fs from 'fs/promises';
import path from 'path';

// In-memory hash registry (for production, use a proper database)
let hashRegistry = new Map();
const REGISTRY_FILE = path.join(process.cwd(), 'data', 'hash-registry.json');

/**
 * Initialize hash registry
 */
export async function initializeHashRegistry() {
    try {
        // Ensure data directory exists
        await fs.mkdir(path.dirname(REGISTRY_FILE), { recursive: true });
        
        // Load existing registry
        try {
            const data = await fs.readFile(REGISTRY_FILE, 'utf8');
            const registryData = JSON.parse(data);
            hashRegistry = new Map(Object.entries(registryData));
            console.log(`📚 Hash Registry: Loaded ${hashRegistry.size} entries`);
        } catch (error) {
            console.log('📚 Hash Registry: Starting with empty registry');
        }
    } catch (error) {
        console.error('Hash Registry initialization error:', error);
    }
}

/**
 * Store a hash with metadata
 * @param {string} hash - SHA-256 hash
 * @param {object} metadata - Associated metadata
 */
export async function storeHash(hash, metadata) {
    const entry = {
        hash,
        timestamp: new Date().toISOString(),
        platform: metadata.platform || 'unknown',
        model: metadata.model || 'unknown',
        inputLength: metadata.inputLength || 0,
        outputLength: metadata.outputLength || 0,
        ecoLevel: metadata.ecoLevel || 3,
        energy: metadata.energy || 0,
        water: metadata.water || 0,
        userAgent: metadata.userAgent || '',
        ipHash: metadata.ipHash || '', // Hashed IP for privacy
        verified: true,
        signature: metadata.signature || ''
    };
    
    hashRegistry.set(hash, entry);
    
    // Persist to file (in production, use database)
    await persistRegistry();
    
    console.log(`📝 Hash Registry: Stored ${hash.substring(0, 8)}...`);
    return entry;
}

/**
 * Search for a hash
 * @param {string} hash - Full or partial SHA-256 hash
 * @returns {object|null} Hash entry or null if not found
 */
export function searchHash(hash) {
    // Exact match first
    if (hashRegistry.has(hash)) {
        return hashRegistry.get(hash);
    }
    
    // Partial match (minimum 8 characters)
    if (hash.length >= 8) {
        for (const [fullHash, entry] of hashRegistry.entries()) {
            if (fullHash.startsWith(hash)) {
                return entry;
            }
        }
    }
    
    return null;
}

/**
 * Verify a hash exists and is valid
 * @param {string} hash - SHA-256 hash to verify
 * @param {string} content - Original content to verify against
 * @returns {object} Verification result
 */
export async function verifyHash(hash, content = null) {
    const entry = searchHash(hash);
    
    if (!entry) {
        return {
            valid: false,
            reason: 'Hash not found in registry',
            hash
        };
    }
    
    // If content provided, verify hash matches
    if (content) {
        const crypto = await import('crypto');
        const computedHash = crypto.createHash('sha256').update(content).digest('hex');
        
        if (computedHash !== entry.hash) {
            return {
                valid: false,
                reason: 'Content does not match stored hash',
                hash,
                entry
            };
        }
    }
    
    return {
        valid: true,
        hash,
        entry,
        timestamp: entry.timestamp,
        platform: entry.platform,
        model: entry.model,
        verified: entry.verified
    };
}

/**
 * Get registry statistics
 * @returns {object} Registry statistics
 */
export function getRegistryStats() {
    const stats = {
        totalHashes: hashRegistry.size,
        platforms: {},
        models: {},
        ecoLevels: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 },
        timeRange: { earliest: null, latest: null }
    };
    
    for (const entry of hashRegistry.values()) {
        // Platform stats
        stats.platforms[entry.platform] = (stats.platforms[entry.platform] || 0) + 1;
        
        // Model stats
        stats.models[entry.model] = (stats.models[entry.model] || 0) + 1;
        
        // Eco-level stats
        stats.ecoLevels[entry.ecoLevel] = (stats.ecoLevels[entry.ecoLevel] || 0) + 1;
        
        // Time range
        const timestamp = new Date(entry.timestamp);
        if (!stats.timeRange.earliest || timestamp < new Date(stats.timeRange.earliest)) {
            stats.timeRange.earliest = entry.timestamp;
        }
        if (!stats.timeRange.latest || timestamp > new Date(stats.timeRange.latest)) {
            stats.timeRange.latest = entry.timestamp;
        }
    }
    
    return stats;
}

/**
 * Search hashes by criteria
 * @param {object} criteria - Search criteria
 * @returns {array} Matching hash entries
 */
export function searchHashesByCriteria(criteria = {}) {
    const results = [];
    
    for (const entry of hashRegistry.values()) {
        let matches = true;
        
        // Platform filter
        if (criteria.platform && entry.platform !== criteria.platform) {
            matches = false;
        }
        
        // Model filter
        if (criteria.model && entry.model !== criteria.model) {
            matches = false;
        }
        
        // Eco-level filter
        if (criteria.ecoLevel && entry.ecoLevel !== criteria.ecoLevel) {
            matches = false;
        }
        
        // Date range filter
        if (criteria.startDate || criteria.endDate) {
            const entryDate = new Date(entry.timestamp);
            if (criteria.startDate && entryDate < new Date(criteria.startDate)) {
                matches = false;
            }
            if (criteria.endDate && entryDate > new Date(criteria.endDate)) {
                matches = false;
            }
        }
        
        if (matches) {
            results.push(entry);
        }
    }
    
    return results.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
}

/**
 * Persist registry to file
 */
async function persistRegistry() {
    try {
        const registryObject = Object.fromEntries(hashRegistry);
        await fs.writeFile(REGISTRY_FILE, JSON.stringify(registryObject, null, 2));
    } catch (error) {
        console.error('Error persisting hash registry:', error);
    }
}

/**
 * Clean up old entries (optional maintenance)
 * @param {number} maxAge - Maximum age in days
 */
export async function cleanupOldHashes(maxAge = 365) {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - maxAge);
    
    let removedCount = 0;
    
    for (const [hash, entry] of hashRegistry.entries()) {
        if (new Date(entry.timestamp) < cutoffDate) {
            hashRegistry.delete(hash);
            removedCount++;
        }
    }
    
    if (removedCount > 0) {
        await persistRegistry();
        console.log(`🧹 Hash Registry: Cleaned up ${removedCount} old entries`);
    }
    
    return removedCount;
}

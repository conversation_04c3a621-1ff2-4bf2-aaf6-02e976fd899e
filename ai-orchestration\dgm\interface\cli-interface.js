/**
 * CLI Interface for DGM Control
 * 
 * Command-line interface for controlling and monitoring the DGM system:
 * - Interactive commands for evolution control
 * - Agent management and inspection
 * - Real-time monitoring and logging
 * - Batch operations and scripting
 */

const readline = require('readline');
const chalk = require('chalk');
const Table = require('cli-table3');
const ora = require('ora');

class CLIInterface {
  constructor(dgmEngine, config) {
    this.dgmEngine = dgmEngine;
    this.config = config;
    this.rl = null;
    this.isRunning = false;
    this.colorOutput = config.get('ui.cli.colorOutput', true);
    this.verboseLogging = config.get('ui.cli.verboseLogging', false);
    this.commands = this.setupCommands();
    this.commandHistory = [];
    
    this.setupEventHandlers();
  }

  /**
   * Start the CLI interface
   */
  async start() {
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
      prompt: this.colorOutput ? chalk.cyan('dgm> ') : 'dgm> '
    });

    this.isRunning = true;
    
    this.displayWelcome();
    this.setupReadlineHandlers();
    this.rl.prompt();
    
    console.log(chalk.green('🖥️  DGM CLI interface started. Type "help" for available commands.'));
  }

  /**
   * Stop the CLI interface
   */
  async stop() {
    if (this.rl) {
      this.rl.close();
    }
    this.isRunning = false;
    console.log(chalk.yellow('🖥️  DGM CLI interface stopped'));
  }

  /**
   * Display welcome message
   */
  displayWelcome() {
    if (!this.colorOutput) return;
    
    console.log(chalk.blue.bold(`
╔══════════════════════════════════════════════════════════════╗
║                Darwin Gödel Machine CLI                     ║
║              Self-Improving AI Orchestration                ║
╚══════════════════════════════════════════════════════════════╝
    `));
  }

  /**
   * Setup readline handlers
   */
  setupReadlineHandlers() {
    this.rl.on('line', async (input) => {
      const trimmedInput = input.trim();
      
      if (trimmedInput) {
        this.commandHistory.push(trimmedInput);
        await this.processCommand(trimmedInput);
      }
      
      this.rl.prompt();
    });

    this.rl.on('close', () => {
      console.log(chalk.yellow('\nGoodbye! 👋'));
      process.exit(0);
    });

    // Handle Ctrl+C gracefully
    this.rl.on('SIGINT', () => {
      console.log(chalk.yellow('\nUse "exit" command to quit gracefully.'));
      this.rl.prompt();
    });
  }

  /**
   * Setup DGM event handlers
   */
  setupEventHandlers() {
    this.dgmEngine.on('generationCompleted', (generation) => {
      this.logEvent(`Generation ${generation.number} completed - Best fitness: ${generation.bestFitness.toFixed(3)}`);
    });

    this.dgmEngine.on('agentCreated', (agent) => {
      if (this.verboseLogging) {
        this.logEvent(`New agent created: ${agent.id.substring(0, 8)} (fitness: ${agent.fitness.toFixed(3)})`);
      }
    });

    this.dgmEngine.on('evolutionStarted', () => {
      this.logEvent('Evolution started', 'success');
    });

    this.dgmEngine.on('evolutionStopped', () => {
      this.logEvent('Evolution stopped', 'warning');
    });

    this.dgmEngine.on('error', (error) => {
      this.logEvent(`Error: ${error.message}`, 'error');
    });
  }

  /**
   * Process user command
   */
  async processCommand(input) {
    const [command, ...args] = input.split(' ');
    const cmd = command.toLowerCase();

    if (this.commands[cmd]) {
      try {
        await this.commands[cmd](args);
      } catch (error) {
        this.logError(`Command failed: ${error.message}`);
      }
    } else {
      this.logError(`Unknown command: ${command}. Type "help" for available commands.`);
    }
  }

  /**
   * Setup available commands
   */
  setupCommands() {
    return {
      help: this.showHelp.bind(this),
      status: this.showStatus.bind(this),
      start: this.startEvolution.bind(this),
      stop: this.stopEvolution.bind(this),
      pause: this.pauseEvolution.bind(this),
      agents: this.listAgents.bind(this),
      agent: this.showAgent.bind(this),
      genealogy: this.showGenealogy.bind(this),
      metrics: this.showMetrics.bind(this),
      history: this.showHistory.bind(this),
      config: this.showConfig.bind(this),
      archive: this.showArchive.bind(this),
      approve: this.approveAgent.bind(this),
      reject: this.rejectAgent.bind(this),
      export: this.exportData.bind(this),
      import: this.importData.bind(this),
      reset: this.resetSystem.bind(this),
      verbose: this.toggleVerbose.bind(this),
      clear: this.clearScreen.bind(this),
      exit: this.exitCLI.bind(this)
    };
  }

  /**
   * Show help information
   */
  async showHelp() {
    const helpTable = new Table({
      head: ['Command', 'Description', 'Usage'],
      colWidths: [15, 40, 25]
    });

    const commands = [
      ['help', 'Show this help message', 'help'],
      ['status', 'Show system status', 'status'],
      ['start', 'Start evolution', 'start [options]'],
      ['stop', 'Stop evolution', 'stop'],
      ['pause', 'Pause evolution', 'pause'],
      ['agents', 'List current agents', 'agents [limit]'],
      ['agent', 'Show agent details', 'agent <id>'],
      ['genealogy', 'Show genealogy stats', 'genealogy'],
      ['metrics', 'Show performance metrics', 'metrics'],
      ['history', 'Show evolution history', 'history [limit]'],
      ['config', 'Show configuration', 'config [key]'],
      ['archive', 'Show archive statistics', 'archive'],
      ['approve', 'Approve agent', 'approve <id>'],
      ['reject', 'Reject agent', 'reject <id>'],
      ['export', 'Export data', 'export <type> [file]'],
      ['import', 'Import data', 'import <file>'],
      ['reset', 'Reset system', 'reset [confirm]'],
      ['verbose', 'Toggle verbose logging', 'verbose'],
      ['clear', 'Clear screen', 'clear'],
      ['exit', 'Exit CLI', 'exit']
    ];

    commands.forEach(([cmd, desc, usage]) => {
      helpTable.push([
        this.colorOutput ? chalk.cyan(cmd) : cmd,
        desc,
        this.colorOutput ? chalk.gray(usage) : usage
      ]);
    });

    console.log('\n' + helpTable.toString() + '\n');
  }

  /**
   * Show system status
   */
  async showStatus() {
    const spinner = ora('Fetching system status...').start();
    
    try {
      const status = {
        isRunning: this.dgmEngine.isRunning,
        currentGeneration: this.dgmEngine.currentGeneration,
        populationSize: this.dgmEngine.agentManager.currentPopulation.length,
        evolutionHistory: this.dgmEngine.evolutionHistory.length,
        uptime: process.uptime(),
        memoryUsage: process.memoryUsage()
      };

      spinner.succeed('Status retrieved');

      const statusTable = new Table();
      statusTable.push(
        ['Status', status.isRunning ? chalk.green('Running') : chalk.red('Stopped')],
        ['Generation', status.currentGeneration],
        ['Population Size', status.populationSize],
        ['Evolution History', status.evolutionHistory],
        ['Uptime', `${Math.floor(status.uptime)}s`],
        ['Memory Usage', `${Math.round(status.memoryUsage.heapUsed / 1024 / 1024)}MB`]
      );

      console.log('\n' + statusTable.toString() + '\n');
      
    } catch (error) {
      spinner.fail('Failed to get status');
      this.logError(error.message);
    }
  }

  /**
   * Start evolution
   */
  async startEvolution(args) {
    if (this.dgmEngine.isRunning) {
      this.logWarning('Evolution is already running');
      return;
    }

    const spinner = ora('Starting evolution...').start();
    
    try {
      const options = this.parseStartOptions(args);
      await this.dgmEngine.startEvolution(options);
      spinner.succeed('Evolution started successfully');
    } catch (error) {
      spinner.fail('Failed to start evolution');
      this.logError(error.message);
    }
  }

  /**
   * Stop evolution
   */
  async stopEvolution() {
    if (!this.dgmEngine.isRunning) {
      this.logWarning('Evolution is not running');
      return;
    }

    const spinner = ora('Stopping evolution...').start();
    
    try {
      this.dgmEngine.isRunning = false;
      spinner.succeed('Evolution stopped');
    } catch (error) {
      spinner.fail('Failed to stop evolution');
      this.logError(error.message);
    }
  }

  /**
   * Pause evolution
   */
  async pauseEvolution() {
    this.logInfo('Pause functionality not yet implemented');
  }

  /**
   * List current agents
   */
  async listAgents(args) {
    const limit = args[0] ? parseInt(args[0]) : 10;
    const spinner = ora('Fetching agents...').start();
    
    try {
      const population = await this.dgmEngine.agentManager.getCurrentPopulation();
      const agents = population.slice(0, limit);
      
      spinner.succeed(`Retrieved ${agents.length} agents`);

      if (agents.length === 0) {
        console.log(chalk.yellow('No agents in current population'));
        return;
      }

      const agentsTable = new Table({
        head: ['ID', 'Generation', 'Type', 'Fitness', 'Created'],
        colWidths: [12, 10, 12, 10, 20]
      });

      agents.forEach(agent => {
        agentsTable.push([
          agent.id.substring(0, 8) + '...',
          agent.generation,
          agent.type,
          agent.fitness.toFixed(3),
          new Date(agent.created).toLocaleString()
        ]);
      });

      console.log('\n' + agentsTable.toString() + '\n');
      
    } catch (error) {
      spinner.fail('Failed to fetch agents');
      this.logError(error.message);
    }
  }

  /**
   * Show agent details
   */
  async showAgent(args) {
    if (!args[0]) {
      this.logError('Agent ID required. Usage: agent <id>');
      return;
    }

    const agentId = args[0];
    const spinner = ora(`Fetching agent ${agentId}...`).start();
    
    try {
      const agent = await this.dgmEngine.archive.retrieveAgent(agentId);
      const lineage = await this.dgmEngine.archive.getAgentLineage(agentId);
      
      spinner.succeed('Agent details retrieved');

      console.log(chalk.blue.bold(`\nAgent: ${agent.id}`));
      console.log(`Generation: ${agent.generation}`);
      console.log(`Type: ${agent.type}`);
      console.log(`Fitness: ${agent.fitness.toFixed(3)}`);
      console.log(`Created: ${new Date(agent.created).toLocaleString()}`);
      console.log(`Parents: ${agent.parentIds.join(', ') || 'None'}`);
      
      if (agent.metrics) {
        console.log('\nMetrics:');
        Object.entries(agent.metrics).forEach(([key, value]) => {
          console.log(`  ${key}: ${typeof value === 'number' ? value.toFixed(3) : value}`);
        });
      }

      if (lineage.ancestors.length > 0) {
        console.log(`\nAncestors: ${lineage.ancestors.length}`);
      }
      
      if (lineage.descendants.length > 0) {
        console.log(`Descendants: ${lineage.descendants.length}`);
      }
      
      console.log();
      
    } catch (error) {
      spinner.fail('Failed to fetch agent details');
      this.logError(error.message);
    }
  }

  /**
   * Show genealogy statistics
   */
  async showGenealogy() {
    const spinner = ora('Fetching genealogy data...').start();
    
    try {
      const stats = this.dgmEngine.archive.genealogy.getGenealogyStats();
      spinner.succeed('Genealogy data retrieved');

      const genealogyTable = new Table();
      genealogyTable.push(
        ['Total Agents', stats.totalAgents],
        ['Founders', stats.founders],
        ['Generations', stats.generations],
        ['Average Children', stats.averageChildren.toFixed(2)],
        ['Max Children', stats.maxChildren],
        ['Lineages', stats.lineages]
      );

      console.log('\n' + genealogyTable.toString() + '\n');
      
    } catch (error) {
      spinner.fail('Failed to fetch genealogy data');
      this.logError(error.message);
    }
  }

  /**
   * Show performance metrics
   */
  async showMetrics() {
    const spinner = ora('Fetching metrics...').start();
    
    try {
      const metricsStats = this.dgmEngine.metricsCollector.getMetricsStats();
      const archiveStats = await this.dgmEngine.archive.getArchiveStats();
      
      spinner.succeed('Metrics retrieved');

      console.log(chalk.blue.bold('\nPerformance Metrics:'));
      
      const metricsTable = new Table();
      metricsTable.push(
        ['Total Agents', metricsStats.totalAgents],
        ['Total Metrics', metricsStats.totalMetrics],
        ['Avg Performance', metricsStats.averagePerformance.toFixed(3)],
        ['Avg Reliability', metricsStats.averageReliability.toFixed(3)],
        ['Avg Functionality', metricsStats.averageFunctionality.toFixed(3)],
        ['Avg Safety', metricsStats.averageSafety.toFixed(3)]
      );

      console.log('\n' + metricsTable.toString());

      console.log(chalk.blue.bold('\nArchive Statistics:'));
      
      const archiveTable = new Table();
      archiveTable.push(
        ['Total Agents', archiveStats.totalAgents],
        ['Generations', archiveStats.generations],
        ['Best Fitness', archiveStats.fitnessDistribution.max.toFixed(3)],
        ['Avg Fitness', archiveStats.fitnessDistribution.average.toFixed(3)],
        ['Min Fitness', archiveStats.fitnessDistribution.min.toFixed(3)]
      );

      console.log('\n' + archiveTable.toString() + '\n');
      
    } catch (error) {
      spinner.fail('Failed to fetch metrics');
      this.logError(error.message);
    }
  }

  /**
   * Show evolution history
   */
  async showHistory(args) {
    const limit = args[0] ? parseInt(args[0]) : 10;
    
    const history = this.dgmEngine.evolutionHistory.slice(-limit);
    
    if (history.length === 0) {
      console.log(chalk.yellow('No evolution history available'));
      return;
    }

    const historyTable = new Table({
      head: ['Generation', 'Best Fitness', 'Avg Fitness', 'Survivors', 'Duration'],
      colWidths: [10, 12, 12, 10, 12]
    });

    history.forEach(gen => {
      historyTable.push([
        gen.number,
        gen.bestFitness.toFixed(3),
        gen.averageFitness.toFixed(3),
        gen.survivors,
        `${gen.duration}ms`
      ]);
    });

    console.log('\n' + historyTable.toString() + '\n');
  }

  /**
   * Parse start options from arguments
   */
  parseStartOptions(args) {
    const options = {};
    
    for (let i = 0; i < args.length; i += 2) {
      const key = args[i];
      const value = args[i + 1];
      
      if (key && value) {
        switch (key) {
          case '--generations':
          case '-g':
            options.maxGenerations = parseInt(value);
            break;
          case '--fitness':
          case '-f':
            options.targetFitness = parseFloat(value);
            break;
        }
      }
    }
    
    return options;
  }

  /**
   * Logging methods
   */
  logEvent(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    let coloredMessage = message;
    
    if (this.colorOutput) {
      switch (type) {
        case 'success':
          coloredMessage = chalk.green(message);
          break;
        case 'warning':
          coloredMessage = chalk.yellow(message);
          break;
        case 'error':
          coloredMessage = chalk.red(message);
          break;
        default:
          coloredMessage = chalk.blue(message);
      }
    }
    
    console.log(`[${timestamp}] ${coloredMessage}`);
  }

  logInfo(message) {
    this.logEvent(message, 'info');
  }

  logWarning(message) {
    this.logEvent(message, 'warning');
  }

  logError(message) {
    this.logEvent(message, 'error');
  }

  logSuccess(message) {
    this.logEvent(message, 'success');
  }

  /**
   * Additional command implementations
   */
  async showConfig(args) {
    const key = args[0];
    
    if (key) {
      const value = this.config.get(key);
      console.log(`${key}: ${JSON.stringify(value, null, 2)}`);
    } else {
      console.log(JSON.stringify(this.config.getAll(), null, 2));
    }
  }

  async showArchive() {
    const stats = await this.dgmEngine.archive.getArchiveStats();
    console.log(JSON.stringify(stats, null, 2));
  }

  async approveAgent(args) {
    if (!args[0]) {
      this.logError('Agent ID required. Usage: approve <id>');
      return;
    }
    
    this.logSuccess(`Agent ${args[0]} approved`);
  }

  async rejectAgent(args) {
    if (!args[0]) {
      this.logError('Agent ID required. Usage: reject <id>');
      return;
    }
    
    this.logWarning(`Agent ${args[0]} rejected`);
  }

  async exportData(args) {
    this.logInfo('Export functionality not yet implemented');
  }

  async importData(args) {
    this.logInfo('Import functionality not yet implemented');
  }

  async resetSystem(args) {
    if (args[0] !== 'confirm') {
      this.logWarning('This will reset the entire DGM system. Use "reset confirm" to proceed.');
      return;
    }
    
    this.logWarning('Reset functionality not yet implemented');
  }

  async toggleVerbose() {
    this.verboseLogging = !this.verboseLogging;
    this.logInfo(`Verbose logging ${this.verboseLogging ? 'enabled' : 'disabled'}`);
  }

  async clearScreen() {
    console.clear();
    this.displayWelcome();
  }

  async exitCLI() {
    console.log(chalk.yellow('Goodbye! 👋'));
    process.exit(0);
  }

  /**
   * Get CLI statistics
   */
  getCLIStats() {
    return {
      isRunning: this.isRunning,
      commandHistory: this.commandHistory.length,
      verboseLogging: this.verboseLogging,
      colorOutput: this.colorOutput
    };
  }
}

module.exports = CLIInterface;

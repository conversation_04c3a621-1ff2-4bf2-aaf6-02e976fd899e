# 🤝 Contributing to EcoStamp

Thank you for your interest in contributing to EcoStamp! We welcome contributions from the community to help make AI more environmentally conscious.

## 🚀 Quick Start

1. **Fork the repository** on GitHub
2. **Clone your fork** locally
3. **Create a feature branch** from `main`
4. **Make your changes**
5. **Test thoroughly**
6. **Submit a pull request**

## 🛠️ Development Setup

### Prerequisites
- Chrome/Edge browser for testing
- Basic knowledge of JavaScript, HTML, CSS
- Familiarity with Chrome extension development

### Local Development
```bash
git clone https://github.com/your-username/ecostamp-extension.git
cd ecostamp-extension

# Load extension in Chrome
# 1. Go to chrome://extensions/
# 2. Enable "Developer mode"
# 3. Click "Load unpacked"
# 4. Select the ecostamp-extension folder
```

### Testing
1. **Load the extension** in Chrome
2. **Visit AI platforms** (ChatGPT, Claude, Gemini, etc.)
3. **Verify EcoStamp footers** appear correctly
4. **Test popup dashboard** functionality
5. **Check browser console** for errors

## 📋 Types of Contributions

### 🐛 Bug Reports
- Use the [bug report template](https://github.com/ecostamp/ecostamp-extension/issues/new?template=bug_report.md)
- Include browser version, extension version, and steps to reproduce
- Provide screenshots if applicable

### ✨ Feature Requests
- Use the [feature request template](https://github.com/ecostamp/ecostamp-extension/issues/new?template=feature_request.md)
- Explain the use case and expected behavior
- Consider environmental impact implications

### 🌐 Platform Support
Help us support more AI platforms:
- Add platform detection in `AI_PLATFORMS` object
- Create platform-specific selectors
- Test thoroughly on the new platform
- Update documentation

### 🎨 UI/UX Improvements
- Improve visual design and accessibility
- Enhance user experience
- Maintain consistency across platforms
- Follow platform-specific design guidelines

### 📚 Documentation
- Improve README, installation guides, or API docs
- Add examples and tutorials
- Fix typos and clarify instructions

## 🔧 Code Guidelines

### JavaScript Style
- Use modern ES6+ syntax
- Follow consistent naming conventions
- Add comments for complex logic
- Handle errors gracefully

### CSS Style
- Use CSS custom properties for theming
- Ensure responsive design
- Support dark mode and accessibility
- Minimize performance impact

### Extension Best Practices
- Minimize permissions requested
- Optimize for performance
- Ensure CSP compatibility
- Follow Chrome extension guidelines

## 🧪 Testing Guidelines

### Manual Testing
- Test on multiple AI platforms
- Verify in different browsers
- Check popup functionality
- Test with various response types

### Platform Testing Checklist
- [ ] ChatGPT (chat.openai.com)
- [ ] Claude (claude.ai)
- [ ] Gemini (gemini.google.com)
- [ ] Perplexity (perplexity.ai)
- [ ] Poe (poe.com)
- [ ] Character.AI (character.ai)
- [ ] You.com (you.com)
- [ ] Hugging Face (huggingface.co)

### Browser Testing
- [ ] Chrome (latest)
- [ ] Edge (latest)
- [ ] Brave Browser
- [ ] Opera

## 📝 Pull Request Process

### Before Submitting
1. **Test thoroughly** on multiple platforms
2. **Update documentation** if needed
3. **Add changelog entry** for significant changes
4. **Ensure no console errors**

### PR Description
- Clearly describe what changes were made
- Reference any related issues
- Include screenshots for UI changes
- List platforms/browsers tested

### Review Process
1. **Automated checks** must pass
2. **Manual review** by maintainers
3. **Testing** on various platforms
4. **Approval** and merge

## 🌱 Environmental Considerations

When contributing, consider:
- **Performance impact** - Minimize CPU/memory usage
- **Accuracy** - Ensure environmental calculations are correct
- **User awareness** - Help users understand their impact
- **Sustainability** - Promote efficient AI usage

## 🏷️ Issue Labels

- `bug` - Something isn't working
- `enhancement` - New feature or improvement
- `platform-support` - Adding support for new AI platforms
- `documentation` - Documentation improvements
- `good-first-issue` - Good for newcomers
- `help-wanted` - Extra attention needed

## 📞 Getting Help

- **[GitHub Discussions](https://github.com/ecostamp/ecostamp-extension/discussions)** - Community support
- **[Issues](https://github.com/ecostamp/ecostamp-extension/issues)** - Bug reports and feature requests
- **[Discord](https://discord.gg/ecostamp)** - Real-time chat (coming soon)

## 🎉 Recognition

Contributors will be:
- Listed in the README contributors section
- Mentioned in release notes
- Invited to the contributors Discord channel
- Eligible for special contributor badges

## 📄 License

By contributing, you agree that your contributions will be licensed under the MIT License.

---

**🌱 Thank you for helping make AI more environmentally conscious!**

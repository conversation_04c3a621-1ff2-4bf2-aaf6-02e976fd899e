/**
 * Feedback Loop Service
 * 
 * Core Gap 6: Continuous learning system that captures execution results,
 * user feedback, and performance metrics to improve future code generation
 * and decision-making processes.
 */

import { EventEmitter } from 'events';
import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger';
import {
  FeedbackType,
  FeedbackSource,
  FeedbackSentiment,
  LearningObjective,
  AdaptationStrategy,
  FeedbackEntry,
  FeedbackContent,
  FeedbackMetadata,
  FeedbackImpact,
  PerformanceMetrics,
  LearningPattern,
  PatternDefinition,
  AdaptationRecommendation,
  ProposedChange,
  ExpectedBenefit,
  LearningSession,
  SessionMetadata,
  LearningConfiguration,
  FeedbackAnalytics,
  TrendAnalysis,
  IssueAnalysis,
  ImprovementArea,
  DataPoint,
  FeedbackQuery,
  LearningMetrics,
  FeedbackError,
  PatternDiscoveryError,
  AdaptationError,
  FEEDBACK_CONSTANTS,
  FeedbackUtils
} from '../../shared/types/FeedbackLoop';

export class FeedbackLoopService extends EventEmitter {
  private prisma: PrismaClient;
  private feedbackStore: Map<string, FeedbackEntry> = new Map();
  private patternStore: Map<string, LearningPattern> = new Map();
  private recommendationStore: Map<string, AdaptationRecommendation> = new Map();
  private activeLearningSession: LearningSession | null = null;
  private patternDiscovery: PatternDiscoveryEngine;
  private recommendationEngine: RecommendationEngine;
  private adaptationEngine: AdaptationEngine;
  private analyticsEngine: AnalyticsEngine;

  constructor(prisma: PrismaClient) {
    super();
    this.prisma = prisma;
    this.patternDiscovery = new PatternDiscoveryEngine();
    this.recommendationEngine = new RecommendationEngine();
    this.adaptationEngine = new AdaptationEngine();
    this.analyticsEngine = new AnalyticsEngine();

    this.setupEventHandlers();
    this.startPeriodicProcessing();
  }

  /**
   * Submit feedback entry
   */
  async submitFeedback(
    type: FeedbackType,
    source: FeedbackSource,
    contextId: string,
    contextType: string,
    content: FeedbackContent,
    metadata?: Partial<FeedbackMetadata>
  ): Promise<FeedbackEntry> {
    const feedbackId = FeedbackUtils.generateFeedbackId();

    const entry: FeedbackEntry = {
      id: feedbackId,
      type,
      source,
      timestamp: new Date(),
      contextId,
      contextType: contextType as any,
      content,
      metadata: {
        confidence: 0.8,
        reliability: 0.9,
        weight: 1.0,
        ...metadata
      },
      processed: false,
      impact: this.calculateFeedbackImpact(type, content, contextType)
    };

    // Validate feedback entry
    if (!FeedbackUtils.validateFeedbackEntry(entry)) {
      throw new FeedbackError('Invalid feedback entry', 'INVALID_FEEDBACK', feedbackId);
    }

    // Store feedback
    this.feedbackStore.set(feedbackId, entry);

    // Emit event
    this.emit('feedbackReceived', {
      feedbackId,
      type,
      source,
      contextId,
      priority: entry.impact.priorityScore
    });

    logger.info('Feedback submitted', {
      feedbackId,
      type,
      source,
      contextId,
      contextType,
      priorityScore: entry.impact.priorityScore
    });

    // Process immediately if high priority
    if (entry.impact.priorityScore > 80) {
      await this.processFeedback(entry);
    }

    return entry;
  }

  /**
   * Process feedback and extract learning insights
   */
  async processFeedback(entry: FeedbackEntry): Promise<void> {
    try {
      // Mark as being processed
      entry.processed = true;
      entry.processedAt = new Date();

      // Extract patterns from feedback
      const patterns = await this.patternDiscovery.analyzeForPatterns(entry);
      
      // Update existing patterns or create new ones
      for (const pattern of patterns) {
        await this.updateOrCreatePattern(pattern);
      }

      // Generate recommendations if applicable
      if (entry.impact.priorityScore > 60) {
        const recommendations = await this.recommendationEngine.generateRecommendations(entry, patterns);
        
        for (const recommendation of recommendations) {
          await this.storeRecommendation(recommendation);
        }
      }

      // Update analytics
      await this.analyticsEngine.updateAnalytics(entry);

      logger.info('Feedback processed', {
        feedbackId: entry.id,
        patternsFound: patterns.length,
        recommendationsGenerated: patterns.length
      });

    } catch (error) {
      logger.error('Failed to process feedback', {
        feedbackId: entry.id,
        error: error.message
      });
      
      entry.processed = false;
      entry.processedAt = undefined;
      
      throw new FeedbackError('Failed to process feedback', 'PROCESSING_FAILED', entry.id, error);
    }
  }

  /**
   * Start a learning session
   */
  async startLearningSession(
    objective: LearningObjective,
    strategy: AdaptationStrategy,
    configuration?: Partial<LearningConfiguration>
  ): Promise<LearningSession> {
    if (this.activeLearningSession && this.activeLearningSession.status === 'RUNNING') {
      throw new FeedbackError('Learning session already active', 'SESSION_ACTIVE');
    }

    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const session: LearningSession = {
      id: sessionId,
      startTime: new Date(),
      objective,
      strategy,
      feedbackProcessed: 0,
      patternsDiscovered: 0,
      recommendationsGenerated: 0,
      adaptationsImplemented: 0,
      performanceBaseline: await this.getPerformanceBaseline(),
      status: 'RUNNING',
      metadata: {
        triggeredBy: 'MANUAL',
        dataRange: {
          startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
          endDate: new Date()
        },
        focusAreas: [],
        constraints: {
          maxProcessingTime: 60,
          maxMemoryUsage: 1024,
          maxCpuUsage: 80,
          minConfidenceThreshold: FEEDBACK_CONSTANTS.MIN_PATTERN_CONFIDENCE,
          maxRiskLevel: 'MEDIUM',
          allowedComponents: [],
          blockedComponents: []
        },
        configuration: {
          enablePatternDiscovery: true,
          enableRecommendationGeneration: true,
          enableAutoImplementation: false,
          batchSize: FEEDBACK_CONSTANTS.DEFAULT_BATCH_SIZE,
          learningRate: FEEDBACK_CONSTANTS.DEFAULT_LEARNING_RATE,
          convergenceThreshold: FEEDBACK_CONSTANTS.CONVERGENCE_THRESHOLD,
          validationSplit: 0.2,
          crossValidationFolds: 5,
          ensembleSize: 3,
          ...configuration
        }
      }
    };

    this.activeLearningSession = session;

    // Start learning process
    this.runLearningSession(session);

    logger.info('Learning session started', {
      sessionId,
      objective,
      strategy
    });

    return session;
  }

  /**
   * Get feedback analytics
   */
  async getFeedbackAnalytics(query?: FeedbackQuery): Promise<FeedbackAnalytics> {
    return await this.analyticsEngine.generateAnalytics(
      Array.from(this.feedbackStore.values()),
      query
    );
  }

  /**
   * Get learning patterns
   */
  getLearningPatterns(objective?: LearningObjective): LearningPattern[] {
    const patterns = Array.from(this.patternStore.values());
    
    if (objective) {
      return patterns.filter(p => p.learningObjective === objective);
    }
    
    return patterns;
  }

  /**
   * Get adaptation recommendations
   */
  getRecommendations(approved?: boolean): AdaptationRecommendation[] {
    const recommendations = Array.from(this.recommendationStore.values());
    
    if (approved !== undefined) {
      return recommendations.filter(r => !!r.approvedBy === approved);
    }
    
    return recommendations;
  }

  /**
   * Approve recommendation for implementation
   */
  async approveRecommendation(recommendationId: string, approvedBy: string): Promise<boolean> {
    const recommendation = this.recommendationStore.get(recommendationId);
    
    if (!recommendation) {
      return false;
    }

    recommendation.approvedBy = approvedBy;
    recommendation.approvedAt = new Date();

    // Auto-implement if configuration allows
    if (recommendation.riskAssessment.overallRisk === 'LOW') {
      await this.implementRecommendation(recommendation);
    }

    logger.info('Recommendation approved', {
      recommendationId,
      approvedBy,
      title: recommendation.title
    });

    return true;
  }

  /**
   * Get learning metrics
   */
  async getLearningMetrics(): Promise<LearningMetrics> {
    const patterns = Array.from(this.patternStore.values());
    const recommendations = Array.from(this.recommendationStore.values());
    const feedback = Array.from(this.feedbackStore.values());

    const activePatterns = patterns.filter(p => p.confidence >= FEEDBACK_CONSTANTS.MIN_PATTERN_CONFIDENCE);
    const approvedRecommendations = recommendations.filter(r => r.approvedBy);
    const implementedRecommendations = recommendations.filter(r => r.implementedAt);

    return {
      totalPatterns: patterns.length,
      activePatterns: activePatterns.length,
      patternAccuracy: activePatterns.length > 0 ? 
        activePatterns.reduce((sum, p) => sum + p.successRate, 0) / activePatterns.length : 0,
      recommendationAcceptanceRate: recommendations.length > 0 ? 
        approvedRecommendations.length / recommendations.length : 0,
      implementationSuccessRate: approvedRecommendations.length > 0 ? 
        implementedRecommendations.length / approvedRecommendations.length : 0,
      overallImprovement: await this.calculateOverallImprovement(),
      learningVelocity: this.calculateLearningVelocity(patterns),
      adaptationLatency: this.calculateAdaptationLatency(feedback, recommendations),
      systemStability: 0.95, // Mock value
      userSatisfactionDelta: await this.calculateSatisfactionDelta()
    };
  }

  /**
   * Private helper methods
   */
  private calculateFeedbackImpact(
    type: FeedbackType,
    content: FeedbackContent,
    contextType: string
  ): FeedbackImpact {
    let priorityScore = 50; // Base priority

    // Adjust based on feedback type
    switch (type) {
      case FeedbackType.ERROR_REPORT:
        priorityScore += 30;
        break;
      case FeedbackType.PERFORMANCE_METRIC:
        priorityScore += 20;
        break;
      case FeedbackType.USER_RATING:
        priorityScore += content.rating ? (10 - content.rating!) * 5 : 0;
        break;
    }

    // Adjust based on severity
    if (content.severity) {
      const severityBonus = { LOW: 0, MEDIUM: 10, HIGH: 20, CRITICAL: 40 };
      priorityScore += severityBonus[content.severity];
    }

    // Determine learning objectives
    const learningObjectives: LearningObjective[] = [];
    
    if (type === FeedbackType.ERROR_REPORT) {
      learningObjectives.push(LearningObjective.ERROR_REDUCTION);
    }
    if (type === FeedbackType.PERFORMANCE_METRIC) {
      learningObjectives.push(LearningObjective.PERFORMANCE_OPTIMIZATION);
    }
    if (type === FeedbackType.USER_RATING) {
      learningObjectives.push(LearningObjective.USER_SATISFACTION);
    }

    return {
      learningObjectives,
      affectedComponents: [contextType],
      priorityScore: Math.min(100, priorityScore),
      expectedImprovement: 0.1, // Default 10% improvement expectation
      implementationComplexity: 'MEDIUM',
      riskLevel: 'LOW'
    };
  }

  private async updateOrCreatePattern(pattern: LearningPattern): Promise<void> {
    const existingPattern = this.patternStore.get(pattern.id);
    
    if (existingPattern) {
      // Update existing pattern
      existingPattern.confidence = pattern.confidence;
      existingPattern.supportingEvidence.push(...pattern.supportingEvidence);
      existingPattern.lastUpdated = new Date();
      existingPattern.usageCount++;
    } else {
      // Create new pattern
      this.patternStore.set(pattern.id, pattern);
    }

    logger.debug('Pattern updated', {
      patternId: pattern.id,
      confidence: pattern.confidence,
      isNew: !existingPattern
    });
  }

  private async storeRecommendation(recommendation: AdaptationRecommendation): Promise<void> {
    this.recommendationStore.set(recommendation.id, recommendation);

    this.emit('recommendationGenerated', {
      recommendationId: recommendation.id,
      title: recommendation.title,
      priority: recommendation.priority,
      type: recommendation.type
    });

    logger.info('Recommendation generated', {
      recommendationId: recommendation.id,
      title: recommendation.title,
      priority: recommendation.priority
    });
  }

  private async implementRecommendation(recommendation: AdaptationRecommendation): Promise<void> {
    try {
      await this.adaptationEngine.implement(recommendation);
      
      recommendation.implementedAt = new Date();

      this.emit('recommendationImplemented', {
        recommendationId: recommendation.id,
        title: recommendation.title,
        implementedAt: recommendation.implementedAt
      });

      logger.info('Recommendation implemented', {
        recommendationId: recommendation.id,
        title: recommendation.title
      });

    } catch (error) {
      logger.error('Failed to implement recommendation', {
        recommendationId: recommendation.id,
        error: error.message
      });
      
      throw new AdaptationError('Failed to implement recommendation', recommendation.id, error);
    }
  }

  private async runLearningSession(session: LearningSession): Promise<void> {
    try {
      const startTime = Date.now();
      const maxTime = session.metadata.constraints.maxProcessingTime * 60 * 1000;

      // Process unprocessed feedback
      const unprocessedFeedback = Array.from(this.feedbackStore.values())
        .filter(f => !f.processed && FeedbackUtils.isRecentFeedback(f.timestamp));

      for (const feedback of unprocessedFeedback) {
        if (Date.now() - startTime > maxTime) {
          logger.warn('Learning session timeout', { sessionId: session.id });
          break;
        }

        await this.processFeedback(feedback);
        session.feedbackProcessed++;
      }

      // Discover new patterns
      const newPatterns = await this.patternDiscovery.discoverPatterns(
        Array.from(this.feedbackStore.values()),
        session.metadata.configuration
      );

      session.patternsDiscovered = newPatterns.length;

      // Generate recommendations
      const newRecommendations = await this.recommendationEngine.generateBatchRecommendations(
        newPatterns,
        session.objective
      );

      session.recommendationsGenerated = newRecommendations.length;

      // Complete session
      session.endTime = new Date();
      session.status = 'COMPLETED';
      session.performanceAfter = await this.getPerformanceBaseline();
      session.improvement = this.calculateSessionImprovement(session);

      this.activeLearningSession = null;

      logger.info('Learning session completed', {
        sessionId: session.id,
        duration: session.endTime.getTime() - session.startTime.getTime(),
        feedbackProcessed: session.feedbackProcessed,
        patternsDiscovered: session.patternsDiscovered,
        recommendationsGenerated: session.recommendationsGenerated
      });

    } catch (error) {
      session.status = 'FAILED';
      session.endTime = new Date();
      this.activeLearningSession = null;

      logger.error('Learning session failed', {
        sessionId: session.id,
        error: error.message
      });
    }
  }

  private async getPerformanceBaseline(): Promise<PerformanceMetrics> {
    // Mock implementation - would gather actual system metrics
    return {
      executionTime: 1000,
      memoryUsage: 512,
      cpuUsage: 25,
      errorRate: 0.05,
      successRate: 0.95,
      throughput: 100,
      latency: 50,
      accuracy: 0.9
    };
  }

  private calculateSessionImprovement(session: LearningSession): number {
    if (!session.performanceBaseline || !session.performanceAfter) {
      return 0;
    }

    // Simple improvement calculation based on error rate reduction
    const baselineError = session.performanceBaseline.errorRate || 0;
    const afterError = session.performanceAfter.errorRate || 0;
    
    if (baselineError === 0) return 0;
    
    return ((baselineError - afterError) / baselineError) * 100;
  }

  private async calculateOverallImprovement(): Promise<number> {
    // Mock implementation - would calculate actual improvement metrics
    return 15.5; // 15.5% improvement
  }

  private calculateLearningVelocity(patterns: LearningPattern[]): number {
    const recentPatterns = patterns.filter(p => 
      Date.now() - p.discoveredAt.getTime() < 24 * 60 * 60 * 1000 // Last 24 hours
    );
    
    return recentPatterns.length;
  }

  private calculateAdaptationLatency(
    feedback: FeedbackEntry[],
    recommendations: AdaptationRecommendation[]
  ): number {
    const implementedRecommendations = recommendations.filter(r => r.implementedAt);
    
    if (implementedRecommendations.length === 0) return 0;

    const totalLatency = implementedRecommendations.reduce((sum, rec) => {
      const basedOnFeedback = feedback.filter(f => rec.basedOnFeedback.includes(f.id));
      if (basedOnFeedback.length === 0) return sum;

      const earliestFeedback = basedOnFeedback.reduce((earliest, f) => 
        f.timestamp < earliest.timestamp ? f : earliest
      );

      const latency = rec.implementedAt!.getTime() - earliestFeedback.timestamp.getTime();
      return sum + latency;
    }, 0);

    return totalLatency / implementedRecommendations.length / (60 * 60 * 1000); // Convert to hours
  }

  private async calculateSatisfactionDelta(): Promise<number> {
    const recentFeedback = Array.from(this.feedbackStore.values())
      .filter(f => f.type === FeedbackType.USER_RATING && FeedbackUtils.isRecentFeedback(f.timestamp, 7));

    const olderFeedback = Array.from(this.feedbackStore.values())
      .filter(f => f.type === FeedbackType.USER_RATING && 
        !FeedbackUtils.isRecentFeedback(f.timestamp, 7) &&
        FeedbackUtils.isRecentFeedback(f.timestamp, 14));

    if (recentFeedback.length === 0 || olderFeedback.length === 0) return 0;

    const recentAvg = recentFeedback.reduce((sum, f) => sum + (f.content.rating || 0), 0) / recentFeedback.length;
    const olderAvg = olderFeedback.reduce((sum, f) => sum + (f.content.rating || 0), 0) / olderFeedback.length;

    return recentAvg - olderAvg;
  }

  private setupEventHandlers(): void {
    // Set up event handlers for feedback processing
    this.on('feedbackReceived', async (event) => {
      // Handle high-priority feedback immediately
      if (event.priority > 80) {
        const feedback = this.feedbackStore.get(event.feedbackId);
        if (feedback && !feedback.processed) {
          await this.processFeedback(feedback);
        }
      }
    });
  }

  private startPeriodicProcessing(): void {
    // Process feedback periodically
    setInterval(async () => {
      const unprocessedFeedback = Array.from(this.feedbackStore.values())
        .filter(f => !f.processed)
        .slice(0, 10); // Process up to 10 at a time

      for (const feedback of unprocessedFeedback) {
        try {
          await this.processFeedback(feedback);
        } catch (error) {
          logger.error('Periodic feedback processing failed', {
            feedbackId: feedback.id,
            error: error.message
          });
        }
      }
    }, FEEDBACK_CONSTANTS.FEEDBACK_PROCESSING_INTERVAL);

    // Discover patterns periodically
    setInterval(async () => {
      if (!this.activeLearningSession) {
        try {
          await this.startLearningSession(
            LearningObjective.PATTERN_RECOGNITION,
            AdaptationStrategy.BATCH_LEARNING
          );
        } catch (error) {
          logger.error('Periodic pattern discovery failed', { error: error.message });
        }
      }
    }, FEEDBACK_CONSTANTS.PATTERN_DISCOVERY_INTERVAL);
  }
}

// Helper classes
class PatternDiscoveryEngine {
  async analyzeForPatterns(feedback: FeedbackEntry): Promise<LearningPattern[]> {
    // Mock implementation - would use actual pattern discovery algorithms
    const patterns: LearningPattern[] = [];

    if (feedback.type === FeedbackType.ERROR_REPORT) {
      patterns.push({
        id: FeedbackUtils.generatePatternId(),
        name: 'Error Pattern',
        description: 'Common error pattern detected',
        pattern: {
          conditions: [],
          outcomes: [],
          correlations: [],
          thresholds: {},
          rules: []
        },
        confidence: 0.8,
        supportingEvidence: [feedback.id],
        contradictingEvidence: [],
        applicableContexts: [feedback.contextType],
        learningObjective: LearningObjective.ERROR_REDUCTION,
        discoveredAt: new Date(),
        lastUpdated: new Date(),
        usageCount: 1,
        successRate: 0.9
      });
    }

    return patterns;
  }

  async discoverPatterns(
    feedback: FeedbackEntry[],
    config: LearningConfiguration
  ): Promise<LearningPattern[]> {
    // Mock implementation - would use machine learning algorithms
    return [];
  }
}

class RecommendationEngine {
  async generateRecommendations(
    feedback: FeedbackEntry,
    patterns: LearningPattern[]
  ): Promise<AdaptationRecommendation[]> {
    // Mock implementation - would generate actual recommendations
    return [];
  }

  async generateBatchRecommendations(
    patterns: LearningPattern[],
    objective: LearningObjective
  ): Promise<AdaptationRecommendation[]> {
    // Mock implementation
    return [];
  }
}

class AdaptationEngine {
  async implement(recommendation: AdaptationRecommendation): Promise<void> {
    // Mock implementation - would implement actual changes
    logger.info('Implementing recommendation', {
      recommendationId: recommendation.id,
      title: recommendation.title
    });
  }
}

class AnalyticsEngine {
  async updateAnalytics(feedback: FeedbackEntry): Promise<void> {
    // Update analytics based on feedback
  }

  async generateAnalytics(
    feedback: FeedbackEntry[],
    query?: FeedbackQuery
  ): Promise<FeedbackAnalytics> {
    // Filter feedback based on query
    let filteredFeedback = feedback;
    
    if (query) {
      filteredFeedback = this.filterFeedback(feedback, query);
    }

    // Calculate analytics
    const totalFeedback = filteredFeedback.length;
    const feedbackByType = this.groupByType(filteredFeedback);
    const feedbackBySource = this.groupBySource(filteredFeedback);
    const feedbackBySentiment = this.groupBySentiment(filteredFeedback);
    const averageRating = this.calculateAverageRating(filteredFeedback);

    return {
      totalFeedback,
      feedbackByType,
      feedbackBySource,
      feedbackBySentiment,
      averageRating,
      trendAnalysis: {
        direction: 'IMPROVING',
        strength: 0.7,
        confidence: 0.8,
        timeframe: '30 days',
        keyFactors: ['Reduced error rate', 'Improved user satisfaction']
      },
      topIssues: [],
      improvementAreas: [],
      userSatisfactionTrend: [],
      performanceTrend: []
    };
  }

  private filterFeedback(feedback: FeedbackEntry[], query: FeedbackQuery): FeedbackEntry[] {
    return feedback.filter(f => {
      if (query.types && !query.types.includes(f.type)) return false;
      if (query.sources && !query.sources.includes(f.source)) return false;
      if (query.processed !== undefined && f.processed !== query.processed) return false;
      // Add more filters as needed
      return true;
    });
  }

  private groupByType(feedback: FeedbackEntry[]): Record<FeedbackType, number> {
    const result = {} as Record<FeedbackType, number>;
    
    for (const f of feedback) {
      result[f.type] = (result[f.type] || 0) + 1;
    }
    
    return result;
  }

  private groupBySource(feedback: FeedbackEntry[]): Record<FeedbackSource, number> {
    const result = {} as Record<FeedbackSource, number>;
    
    for (const f of feedback) {
      result[f.source] = (result[f.source] || 0) + 1;
    }
    
    return result;
  }

  private groupBySentiment(feedback: FeedbackEntry[]): Record<FeedbackSentiment, number> {
    const result = {} as Record<FeedbackSentiment, number>;
    
    for (const f of feedback) {
      if (f.content.sentiment) {
        result[f.content.sentiment] = (result[f.content.sentiment] || 0) + 1;
      }
    }
    
    return result;
  }

  private calculateAverageRating(feedback: FeedbackEntry[]): number {
    const ratingsOnly = feedback.filter(f => f.content.rating !== undefined);
    
    if (ratingsOnly.length === 0) return 0;
    
    const sum = ratingsOnly.reduce((total, f) => total + (f.content.rating || 0), 0);
    return sum / ratingsOnly.length;
  }
}

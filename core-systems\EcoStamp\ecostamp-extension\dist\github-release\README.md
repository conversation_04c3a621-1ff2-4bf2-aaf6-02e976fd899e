# 🌱 EcoStamp - Universal AI Environmental Impact Tracker

**Cross-browser extension for tracking environmental impact across ALL AI platforms!**

[![Chrome Web Store](https://img.shields.io/badge/Chrome-Web%20Store-blue?logo=google-chrome&logoColor=white)](https://chrome.google.com/webstore)
[![Firefox Add-ons](https://img.shields.io/badge/Firefox-Add--ons-orange?logo=firefox&logoColor=white)](https://addons.mozilla.org/)
[![Opera Add-ons](https://img.shields.io/badge/Opera-Add--ons-red?logo=opera&logoColor=white)](https://addons.opera.com/)
[![Edge Add-ons](https://img.shields.io/badge/Edge-Add--ons-blue?logo=microsoft-edge&logoColor=white)](https://microsoftedge.microsoft.com/addons/)
[![GitHub Downloads](https://img.shields.io/github/downloads/ecostamp/ecostamp-extension/total?logo=github&logoColor=white)](https://github.com/ecostamp/ecostamp-extension/releases)
[![Product Hunt](https://img.shields.io/badge/Product%20Hunt-🚀%20Launch-orange?logo=producthunt&logoColor=white)](https://www.producthunt.com/posts/ecostamp)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)

## 🚀 **Instant Download & Use**

### **Option 1: Chrome Web Store (Recommended)**
1. **[Install from Chrome Web Store](https://chrome.google.com/webstore)** ⬅️ **One-click install**
2. **Visit any AI platform** (ChatGPT, Claude, Gemini, etc.)
3. **See EcoStamp footers appear automatically!** ✨

### **Option 2: GitHub Direct Download**
1. **[Download Latest Release](https://github.com/ecostamp/ecostamp-extension/releases/latest)** 📦
2. **Extract ZIP file**
3. **Load in Chrome**: `chrome://extensions/` → Enable Developer Mode → Load Unpacked
4. **Start using immediately!** 🎉

### **Option 3: Product Hunt Launch**
1. **[Get it on Product Hunt](https://www.producthunt.com/posts/ecostamp)** 🏆
2. **Support us with an upvote!** ⬆️
3. **Download and install instantly** ⚡

## 🌍 **Universal AI Platform Support**

EcoStamp works seamlessly with **ALL major AI platforms**:

- ✅ **ChatGPT** (chat.openai.com, chatgpt.com)
- ✅ **Claude** (claude.ai)
- ✅ **Gemini** (gemini.google.com, bard.google.com)
- ✅ **Perplexity** (perplexity.ai)
- ✅ **Poe** (poe.com)
- ✅ **Character.AI** (character.ai)
- ✅ **You.com** (you.com)
- ✅ **Hugging Face** (huggingface.co)
- ✅ **ANY AI Platform** (universal detection)

## ✨ **Key Features**

### 🎯 **Smart Universal Detection**
- **Auto-detects ANY AI platform** using intelligent content analysis
- **Platform-specific optimizations** for major AI services
- **Fallback universal detection** for new/unknown platforms
- **Real-time model detection** (GPT-4, Claude-3, Gemini-Pro, etc.)

### 🌿 **Advanced Eco-Level System**
- **5-leaf rating system** based on response complexity and length
- **Platform efficiency multipliers** (Claude more efficient than ChatGPT)
- **Smart complexity analysis** (code, lists, markdown detection)
- **Real-time environmental impact calculations**

### 📊 **Comprehensive Analytics**
- **Cross-platform statistics** in beautiful popup dashboard
- **Platform usage breakdown** showing your AI preferences
- **Eco-level distribution** to track your efficiency
- **Export functionality** for data analysis
- **Real-time badge updates** showing total interactions

### 🔒 **Privacy & Security**
- **No data collection** - everything stays local
- **CSP-compatible** - works with strict security policies
- **Minimal permissions** - only activeTab and storage
- **Open source** - fully transparent code

## 🎯 **What You'll See**

After each AI response on ANY platform:

```
──────────────────────────────────────────────
🕓 01/02/2025, 03:45:00 UTC  |  🔐 SHA-256: a1b2...c3d4
🌿 Eco-Level: 3/5 Leaves 🌿🌿🌿🍂🍂  (0.45 Wh · 12.8 mL)
Powered by EcoStamp — GitHub              ChatGPT • gpt-4
```

## 📊 **Eco-Level System**

| Level | Leaves | Energy Range | Description          |
| ----- | ------ | ------------ | -------------------- |
| 1     | 🌿🌿🌿🌿🌿  | < 0.15 Wh    | Excellent efficiency |
| 2     | 🌿🌿🌿🌿🍂  | < 0.30 Wh    | Good efficiency      |
| 3     | 🌿🌿🌿🍂🍂  | < 0.50 Wh    | Moderate efficiency  |
| 4     | 🌿🌿🍂🍂🍂  | < 0.70 Wh    | Higher impact        |
| 5     | 🌿🍂🍂🍂🍂  | > 0.70 Wh    | High impact          |

## 🔧 **Advanced Configuration**

Click the EcoStamp extension icon to access:
- **Toggle tracking** on/off
- **View detailed statistics**
- **Export usage data**
- **Reset statistics**
- **Platform breakdown**

## 🛠️ **For Developers**

### **Manual Installation**
```bash
git clone https://github.com/ecostamp/ecostamp-extension.git
cd ecostamp-extension
# Load in Chrome: chrome://extensions/ → Load Unpacked
```

### **Debug Console**
```javascript
// Access EcoStamp functions in browser console
ecoStamp.process()  // Manually trigger processing
ecoStamp.detect()   // Check platform detection
ecoStamp.config     // View current configuration
```

### **Browser Compatibility**
- ✅ Chrome 88+ (Full support)
- ✅ Firefox 88+ (Full support)
- ✅ Edge 88+ (Full support)
- ✅ Opera (Full support)
- ✅ Brave (Full support)
- ⚠️ Safari (Requires conversion)

## 🔍 **Troubleshooting**

**Not seeing footers?**
1. Check if extension is enabled (click icon)
2. Refresh the AI platform page
3. Try asking a longer question
4. Check browser console for debug messages

**Manual trigger:**
Open console and run: `ecoStamp.process()`

**Platform not detected?**
The universal fallback should catch any AI platform. If not, please [report an issue](https://github.com/ecostamp/ecostamp-extension/issues).

## 🌱 **Environmental Impact**

EcoStamp helps you:
- **Track your AI usage** across all platforms
- **Make informed decisions** about AI efficiency
- **Reduce environmental impact** by choosing efficient queries
- **Raise awareness** about AI's environmental cost

## 🤝 **Contributing**

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

## 📄 **License**

MIT License - see [LICENSE](LICENSE) file for details.

## 🔗 **Links**

- **[Chrome Web Store](https://chrome.google.com/webstore)** - Official extension
- **[GitHub Repository](https://github.com/ecostamp/ecostamp-extension)** - Source code
- **[Product Hunt](https://www.producthunt.com/posts/ecostamp)** - Launch page
- **[Documentation](https://ecostamp.github.io/docs)** - Full documentation
- **[Support](https://github.com/ecostamp/ecostamp-extension/issues)** - Report issues

---

**🌱 Making AI conversations environmentally conscious across ALL platforms!**

*EcoStamp - The only AI environmental tracker you'll ever need.*

/**
 * Token counting utilities for different AI platforms
 * Provides accurate token estimation for <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and Gemini
 */

// Approximate token counting based on character/word ratios for each platform
const TOKEN_RATIOS = {
  chatgpt: {
    // GPT models: ~4 characters per token on average
    charsPerToken: 4,
    wordsPerToken: 0.75,
    name: 'Chat<PERSON><PERSON> (GPT-4/3.5)'
  },
  claude: {
    // Claude models: ~3.5 characters per token on average
    charsPerToken: 3.5,
    wordsPerToken: 0.7,
    name: '<PERSON> (Anthropic)'
  },
  gemini: {
    // Gemini models: ~4.2 characters per token on average
    charsPerToken: 4.2,
    wordsPerToken: 0.8,
    name: '<PERSON> (Google)'
  }
};

/**
 * Estimate token count for a given text and platform
 * @param {string} text - The text to count tokens for
 * @param {string} platform - The AI platform (chatgpt, claude, gemini)
 * @param {string} method - Counting method ('chars' or 'words')
 * @returns {number} Estimated token count
 */
export function estimateTokens(text, platform = 'chatgpt', method = 'chars') {
  if (!text || typeof text !== 'string') {
    return 0;
  }

  const ratio = TOKEN_RATIOS[platform] || TOKEN_RATIOS.chatgpt;
  
  if (method === 'words') {
    const wordCount = text.trim().split(/\s+/).length;
    return Math.ceil(wordCount / ratio.wordsPerToken);
  } else {
    // Default to character-based counting
    return Math.ceil(text.length / ratio.charsPerToken);
  }
}

/**
 * Count tokens for both input and output text
 * @param {string} input - User input text
 * @param {string} output - AI response text
 * @param {string} platform - AI platform
 * @returns {object} Token counts and totals
 */
export function countConversationTokens(input, output, platform = 'chatgpt') {
  const inputTokens = estimateTokens(input, platform);
  const outputTokens = estimateTokens(output, platform);
  const totalTokens = inputTokens + outputTokens;

  return {
    inputTokens,
    outputTokens,
    totalTokens,
    platform,
    platformName: TOKEN_RATIOS[platform]?.name || 'Unknown'
  };
}

/**
 * Extract token information from AI platform responses (if available)
 * Some platforms provide actual token counts in their responses
 * @param {object} responseData - Response data from AI platform
 * @param {string} platform - AI platform
 * @returns {object|null} Actual token counts if available
 */
export function extractActualTokens(responseData, platform) {
  try {
    if (!responseData) {
      return null;
    }

    switch (platform) {
      case 'chatgpt':
        // OpenAI API provides usage data
        if (responseData.usage) {
          return {
            inputTokens: responseData.usage.prompt_tokens,
            outputTokens: responseData.usage.completion_tokens,
            totalTokens: responseData.usage.total_tokens,
            source: 'api'
          };
        }
        break;
      
      case 'claude':
        // Anthropic API provides usage data
        if (responseData.usage) {
          return {
            inputTokens: responseData.usage.input_tokens,
            outputTokens: responseData.usage.output_tokens,
            totalTokens: responseData.usage.input_tokens + responseData.usage.output_tokens,
            source: 'api'
          };
        }
        break;
      
      case 'gemini':
        // Google API provides usage metadata
        if (responseData.usageMetadata) {
          return {
            inputTokens: responseData.usageMetadata.promptTokenCount,
            outputTokens: responseData.usageMetadata.candidatesTokenCount,
            totalTokens: responseData.usageMetadata.totalTokenCount,
            source: 'api'
          };
        }
        break;
    }
  } catch (error) {
    console.warn('Error extracting actual tokens:', error);
  }
  
  return null;
}

/**
 * Get comprehensive token analysis
 * @param {string} input - User input
 * @param {string} output - AI output
 * @param {string} platform - AI platform
 * @param {object} responseData - Optional API response data
 * @returns {object} Complete token analysis
 */
export function getTokenAnalysis(input, output, platform, responseData = null) {
  const estimated = countConversationTokens(input, output, platform);
  const actual = extractActualTokens(responseData, platform);
  
  return {
    estimated,
    actual,
    final: actual || estimated, // Use actual if available, otherwise estimated
    timestamp: new Date().toISOString(),
    platform
  };
}

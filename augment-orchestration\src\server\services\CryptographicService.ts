/**
 * Cryptographic Service for Signed Event Packets
 * 
 * Provides cryptographic operations for signing, verification, and hashing
 * of event packets to ensure code provenance and integrity.
 */

import crypto from 'crypto';
import { promisify } from 'util';
import { logger } from '../utils/logger';
import { CryptoService, SignatureData, EVENT_PACKET_CONSTANTS } from '../../shared/types/SignedEventPacket';

export class CryptographicService implements CryptoService {
  private readonly keySize = 2048;
  private readonly hashAlgorithm = 'sha256';
  private readonly signatureAlgorithm = 'RSA-SHA256';

  constructor() {
    logger.info('CryptographicService initialized');
  }

  /**
   * Generate RSA key pair for signing
   */
  async generateKeyPair(): Promise<{ publicKey: string; privateKey: string }> {
    try {
      const { publicKey, privateKey } = crypto.generateKeyPairSync('rsa', {
        modulusLength: this.keySize,
        publicKeyEncoding: {
          type: 'spki',
          format: 'pem'
        },
        privateKeyEncoding: {
          type: 'pkcs8',
          format: 'pem'
        }
      });

      logger.debug('Generated new RSA key pair');
      return { publicKey, privateKey };
    } catch (error) {
      logger.error('Failed to generate key pair:', error);
      throw new Error('Key pair generation failed');
    }
  }

  /**
   * Sign data using private key
   */
  async signData(data: string, privateKey: string): Promise<string> {
    try {
      const sign = crypto.createSign(this.signatureAlgorithm);
      sign.update(data, 'utf8');
      const signature = sign.sign(privateKey, 'base64');
      
      logger.debug('Data signed successfully');
      return signature;
    } catch (error) {
      logger.error('Failed to sign data:', error);
      throw new Error('Data signing failed');
    }
  }

  /**
   * Verify signature using public key
   */
  async verifySignature(data: string, signature: string, publicKey: string): Promise<boolean> {
    try {
      const verify = crypto.createVerify(this.signatureAlgorithm);
      verify.update(data, 'utf8');
      const isValid = verify.verify(publicKey, signature, 'base64');
      
      logger.debug(`Signature verification result: ${isValid}`);
      return isValid;
    } catch (error) {
      logger.error('Failed to verify signature:', error);
      return false;
    }
  }

  /**
   * Generate SHA-256 hash of data
   */
  hashData(data: string): string {
    try {
      const hash = crypto.createHash(this.hashAlgorithm);
      hash.update(data, 'utf8');
      const hashValue = hash.digest('hex');
      
      logger.debug('Data hashed successfully');
      return hashValue;
    } catch (error) {
      logger.error('Failed to hash data:', error);
      throw new Error('Data hashing failed');
    }
  }

  /**
   * Generate cryptographically secure nonce
   */
  generateNonce(): string {
    try {
      const nonce = crypto.randomBytes(32).toString('hex');
      logger.debug('Generated new nonce');
      return nonce;
    } catch (error) {
      logger.error('Failed to generate nonce:', error);
      throw new Error('Nonce generation failed');
    }
  }

  /**
   * Create comprehensive signature data structure
   */
  async createSignatureData(
    payload: string,
    privateKey: string,
    algorithm: 'RSA-SHA256' | 'ECDSA-SHA256' | 'Ed25519' = 'RSA-SHA256'
  ): Promise<SignatureData> {
    try {
      const signature = await this.signData(payload, privateKey);
      const publicKey = this.extractPublicKeyFromPrivate(privateKey);
      
      return {
        payload,
        signature,
        publicKey,
        algorithm,
        timestamp: new Date()
      };
    } catch (error) {
      logger.error('Failed to create signature data:', error);
      throw new Error('Signature data creation failed');
    }
  }

  /**
   * Verify comprehensive signature data structure
   */
  async verifySignatureData(signatureData: SignatureData): Promise<boolean> {
    try {
      const { payload, signature, publicKey, algorithm } = signatureData;
      
      // Verify the signature matches the algorithm
      if (algorithm !== this.signatureAlgorithm) {
        logger.warn(`Unsupported signature algorithm: ${algorithm}`);
        return false;
      }

      return await this.verifySignature(payload, signature, publicKey);
    } catch (error) {
      logger.error('Failed to verify signature data:', error);
      return false;
    }
  }

  /**
   * Extract public key from private key (for RSA)
   */
  private extractPublicKeyFromPrivate(privateKey: string): string {
    try {
      const keyObject = crypto.createPrivateKey(privateKey);
      const publicKey = crypto.createPublicKey(keyObject);
      return publicKey.export({ type: 'spki', format: 'pem' }) as string;
    } catch (error) {
      logger.error('Failed to extract public key:', error);
      throw new Error('Public key extraction failed');
    }
  }

  /**
   * Generate deterministic hash for code content
   */
  generateCodeHash(codeContent: string, metadata?: any): string {
    try {
      const normalizedContent = this.normalizeCodeContent(codeContent);
      const contentWithMetadata = metadata 
        ? `${normalizedContent}${JSON.stringify(metadata, null, 0)}`
        : normalizedContent;
      
      return this.hashData(contentWithMetadata);
    } catch (error) {
      logger.error('Failed to generate code hash:', error);
      throw new Error('Code hash generation failed');
    }
  }

  /**
   * Normalize code content for consistent hashing
   */
  private normalizeCodeContent(content: string): string {
    return content
      .replace(/\r\n/g, '\n')  // Normalize line endings
      .replace(/\s+$/gm, '')   // Remove trailing whitespace
      .trim();                 // Remove leading/trailing whitespace
  }

  /**
   * Create merkle tree hash for multiple code files
   */
  createMerkleHash(codeFiles: Array<{ path: string; content: string }>): string {
    try {
      if (codeFiles.length === 0) {
        return this.hashData('');
      }

      if (codeFiles.length === 1) {
        return this.generateCodeHash(codeFiles[0].content);
      }

      // Sort files by path for deterministic ordering
      const sortedFiles = codeFiles.sort((a, b) => a.path.localeCompare(b.path));
      
      // Create leaf hashes
      let hashes = sortedFiles.map(file => 
        this.hashData(`${file.path}:${this.normalizeCodeContent(file.content)}`)
      );

      // Build merkle tree
      while (hashes.length > 1) {
        const newHashes: string[] = [];
        for (let i = 0; i < hashes.length; i += 2) {
          const left = hashes[i];
          const right = i + 1 < hashes.length ? hashes[i + 1] : left;
          newHashes.push(this.hashData(`${left}${right}`));
        }
        hashes = newHashes;
      }

      return hashes[0];
    } catch (error) {
      logger.error('Failed to create merkle hash:', error);
      throw new Error('Merkle hash creation failed');
    }
  }

  /**
   * Validate hash integrity
   */
  validateHashIntegrity(data: string, expectedHash: string): boolean {
    try {
      const actualHash = this.hashData(data);
      const isValid = actualHash === expectedHash;
      
      if (!isValid) {
        logger.warn('Hash integrity validation failed', {
          expected: expectedHash,
          actual: actualHash
        });
      }

      return isValid;
    } catch (error) {
      logger.error('Failed to validate hash integrity:', error);
      return false;
    }
  }

  /**
   * Generate timestamp-based nonce for replay attack prevention
   */
  generateTimestampNonce(): string {
    const timestamp = Date.now().toString();
    const randomBytes = crypto.randomBytes(16).toString('hex');
    return `${timestamp}-${randomBytes}`;
  }
}

// Singleton instance
export const cryptographicService = new CryptographicService();

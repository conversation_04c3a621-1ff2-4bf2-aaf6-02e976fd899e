import natural from 'natural';
import similarity from 'similarity';
import { config } from '../config/index.js';
import { logger } from '../utils/logger.js';
import OpenAIClient from '../api/openai-client.js';

class SearchEngine {
  constructor() {
    this.openaiClient = new OpenAIClient();
    this.stemmer = natural.PorterStemmer;
    this.tokenizer = new natural.WordTokenizer();
    this.tfidf = new natural.TfIdf();
    this.stopWords = new Set(natural.stopwords);
  }

  async searchThreads(threads, query, options = {}) {
    const startTime = Date.now();
    logger.info('Starting thread search', { 
      query: query.substring(0, 100) + '...',
      threadCount: threads.length 
    });

    try {
      const searchResults = await Promise.all([
        this.semanticSearch(threads, query, options),
        this.keywordSearch(threads, query, options),
        this.contentSearch(threads, query, options)
      ]);

      // Combine and rank results
      const combinedResults = this.combineSearchResults(searchResults, options);
      
      // Apply highlighting
      const highlightedResults = await this.highlightRelevantPassages(
        combinedResults, 
        query, 
        options
      );

      const duration = Date.now() - startTime;
      logger.info('Thread search completed', {
        resultsCount: highlightedResults.length,
        duration: `${duration}ms`
      });

      return highlightedResults;
    } catch (error) {
      logger.error('Thread search failed', { error: error.message });
      throw error;
    }
  }

  async semanticSearch(threads, query, options = {}) {
    logger.info('Performing semantic search');

    try {
      // Get query embedding
      const queryEmbedding = await this.openaiClient.createEmbedding(query);
      
      // Calculate similarity scores for each thread
      const scoredThreads = await Promise.all(
        threads.map(async (thread) => {
          try {
            // Create thread content for embedding
            const threadContent = this.extractThreadContent(thread);
            const threadEmbedding = await this.openaiClient.createEmbedding(threadContent);
            
            // Calculate cosine similarity
            const similarity = this.cosineSimilarity(queryEmbedding, threadEmbedding);
            
            return {
              thread,
              score: similarity,
              method: 'semantic'
            };
          } catch (error) {
            logger.warn(`Failed to process thread ${thread.id} for semantic search`, { 
              error: error.message 
            });
            return {
              thread,
              score: 0,
              method: 'semantic'
            };
          }
        })
      );

      // Filter by threshold and sort
      const threshold = options.semanticThreshold || config.search.similarityThreshold;
      const filteredResults = scoredThreads
        .filter(result => result.score >= threshold)
        .sort((a, b) => b.score - a.score);

      logger.info(`Semantic search found ${filteredResults.length} relevant threads`);
      return filteredResults;
    } catch (error) {
      logger.error('Semantic search failed', { error: error.message });
      return [];
    }
  }

  keywordSearch(threads, query, options = {}) {
    logger.info('Performing keyword search');

    try {
      // Prepare query terms
      const queryTerms = this.preprocessText(query);
      
      // Build TF-IDF index
      threads.forEach(thread => {
        const content = this.extractThreadContent(thread);
        const processedContent = this.preprocessText(content);
        this.tfidf.addDocument(processedContent);
      });

      // Calculate TF-IDF scores
      const scoredThreads = threads.map((thread, index) => {
        let totalScore = 0;
        
        queryTerms.forEach(term => {
          totalScore += this.tfidf.tfidf(term, index);
        });

        return {
          thread,
          score: totalScore,
          method: 'keyword'
        };
      });

      // Sort by score
      const sortedResults = scoredThreads
        .filter(result => result.score > 0)
        .sort((a, b) => b.score - a.score);

      logger.info(`Keyword search found ${sortedResults.length} relevant threads`);
      return sortedResults;
    } catch (error) {
      logger.error('Keyword search failed', { error: error.message });
      return [];
    }
  }

  contentSearch(threads, query, options = {}) {
    logger.info('Performing content search');

    try {
      const queryLower = query.toLowerCase();
      const queryTerms = this.tokenizer.tokenize(queryLower);

      const scoredThreads = threads.map(thread => {
        const content = this.extractThreadContent(thread).toLowerCase();
        let score = 0;

        // Exact phrase matching
        if (content.includes(queryLower)) {
          score += 10;
        }

        // Individual term matching
        queryTerms.forEach(term => {
          const termCount = (content.match(new RegExp(term, 'g')) || []).length;
          score += termCount;
        });

        // Fuzzy matching for individual terms
        queryTerms.forEach(term => {
          const words = content.split(/\s+/);
          words.forEach(word => {
            const sim = similarity(term, word);
            if (sim > 0.8) {
              score += sim;
            }
          });
        });

        return {
          thread,
          score,
          method: 'content'
        };
      });

      const sortedResults = scoredThreads
        .filter(result => result.score > 0)
        .sort((a, b) => b.score - a.score);

      logger.info(`Content search found ${sortedResults.length} relevant threads`);
      return sortedResults;
    } catch (error) {
      logger.error('Content search failed', { error: error.message });
      return [];
    }
  }

  combineSearchResults(searchResults, options = {}) {
    const [semanticResults, keywordResults, contentResults] = searchResults;
    const combinedMap = new Map();

    // Weight the different search methods
    const weights = {
      semantic: options.semanticWeight || 0.5,
      keyword: options.keywordWeight || 0.3,
      content: options.contentWeight || 0.2
    };

    // Combine results with weighted scores
    [semanticResults, keywordResults, contentResults].forEach((results, index) => {
      const method = ['semantic', 'keyword', 'content'][index];
      const weight = weights[method];

      results.forEach(result => {
        const threadId = result.thread.id;
        
        if (combinedMap.has(threadId)) {
          const existing = combinedMap.get(threadId);
          existing.combinedScore += result.score * weight;
          existing.methods.push(method);
          existing.scores[method] = result.score;
        } else {
          combinedMap.set(threadId, {
            thread: result.thread,
            combinedScore: result.score * weight,
            methods: [method],
            scores: { [method]: result.score }
          });
        }
      });
    });

    // Convert to array and sort by combined score
    const combinedResults = Array.from(combinedMap.values())
      .sort((a, b) => b.combinedScore - a.combinedScore);

    // Apply limit
    const limit = options.limit || config.search.maxThreadsToRetrieve;
    return combinedResults.slice(0, limit);
  }

  async highlightRelevantPassages(searchResults, query, options = {}) {
    logger.info('Highlighting relevant passages');

    try {
      const highlightedResults = await Promise.all(
        searchResults.map(async (result) => {
          const highlights = await this.extractHighlights(result.thread, query, options);
          
          return {
            ...result,
            thread: {
              ...result.thread,
              highlights
            }
          };
        })
      );

      return highlightedResults;
    } catch (error) {
      logger.error('Highlighting failed', { error: error.message });
      return searchResults; // Return without highlights if highlighting fails
    }
  }

  async extractHighlights(thread, query, options = {}) {
    const highlights = [];
    const queryTerms = this.preprocessText(query);
    const maxHighlights = options.maxHighlights || 5;
    const contextLength = options.contextLength || 200;

    try {
      thread.messages.forEach((message, messageIndex) => {
        const content = message.content;
        const contentLower = content.toLowerCase();
        const queryLower = query.toLowerCase();

        // Find exact phrase matches
        let index = contentLower.indexOf(queryLower);
        while (index !== -1 && highlights.length < maxHighlights) {
          const start = Math.max(0, index - contextLength / 2);
          const end = Math.min(content.length, index + query.length + contextLength / 2);
          const context = content.substring(start, end);
          
          highlights.push({
            text: context,
            messageIndex,
            type: 'exact_match',
            relevanceScore: 1.0
          });

          index = contentLower.indexOf(queryLower, index + 1);
        }

        // Find individual term matches
        queryTerms.forEach(term => {
          if (highlights.length >= maxHighlights) return;

          const termRegex = new RegExp(`\\b${term}\\b`, 'gi');
          let match;
          
          while ((match = termRegex.exec(content)) !== null && highlights.length < maxHighlights) {
            const start = Math.max(0, match.index - contextLength / 2);
            const end = Math.min(content.length, match.index + term.length + contextLength / 2);
            const context = content.substring(start, end);
            
            highlights.push({
              text: context,
              messageIndex,
              type: 'term_match',
              term,
              relevanceScore: 0.7
            });
          }
        });
      });

      // Sort by relevance and remove duplicates
      return highlights
        .sort((a, b) => b.relevanceScore - a.relevanceScore)
        .slice(0, maxHighlights)
        .filter((highlight, index, array) => 
          array.findIndex(h => h.text === highlight.text) === index
        );

    } catch (error) {
      logger.warn(`Failed to extract highlights for thread ${thread.id}`, { 
        error: error.message 
      });
      return [];
    }
  }

  extractThreadContent(thread) {
    return thread.messages
      .map(message => message.content)
      .join(' ')
      .substring(0, 8000); // Limit content length for embedding
  }

  preprocessText(text) {
    return this.tokenizer.tokenize(text.toLowerCase())
      .filter(token => !this.stopWords.has(token))
      .map(token => this.stemmer.stem(token));
  }

  cosineSimilarity(vecA, vecB) {
    const dotProduct = vecA.reduce((sum, a, i) => sum + a * vecB[i], 0);
    const magnitudeA = Math.sqrt(vecA.reduce((sum, a) => sum + a * a, 0));
    const magnitudeB = Math.sqrt(vecB.reduce((sum, b) => sum + b * b, 0));
    
    return dotProduct / (magnitudeA * magnitudeB);
  }
}

export default SearchEngine;

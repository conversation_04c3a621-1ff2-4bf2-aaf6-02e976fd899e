import type { LoaderFunctionArgs, MetaFunction } from "@remix-run/node";
import { json, redirect } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { motion } from "framer-motion";
import { 
  Upload, 
  Search, 
  BarChart3, 
  Shield, 
  Users, 
  FileText,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle
} from "lucide-react";

import { requireUser } from "~/utils/auth.server";
import { DashboardLayout } from "~/components/layout/DashboardLayout";
import { StatsCard } from "~/components/ui/StatsCard";
import { QuickActionCard } from "~/components/ui/QuickActionCard";
import { RecentActivity } from "~/components/dashboard/RecentActivity";
import { UploadProgress } from "~/components/dashboard/UploadProgress";
import { VerificationStatus } from "~/components/dashboard/VerificationStatus";

export const meta: MetaFunction = () => {
  return [
    { title: "Dashboard - EcoStamp" },
    { name: "description", content: "Your EcoStamp dashboard - manage your digital content and verifications" },
  ];
};

export async function loader({ request }: LoaderFunctionArgs) {
  const user = await requireUser(request);
  
  // In a real app, fetch user's data from database
  const dashboardData = {
    stats: {
      totalUploads: 127,
      verifiedContent: 98,
      pendingVerifications: 5,
      storageUsed: 2.4, // GB
      storageLimit: 10, // GB based on plan
    },
    recentActivity: [
      {
        id: "1",
        type: "upload",
        title: "Product Photography Set",
        timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
        status: "verified",
      },
      {
        id: "2",
        type: "verification",
        title: "Brand Logo Design",
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
        status: "pending",
      },
      {
        id: "3",
        type: "share",
        title: "Marketing Campaign Assets",
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 6), // 6 hours ago
        status: "shared",
      },
    ],
    quickStats: {
      thisMonth: {
        uploads: 23,
        verifications: 18,
        downloads: 45,
      },
      lastMonth: {
        uploads: 19,
        verifications: 15,
        downloads: 32,
      },
    },
  };

  return json({ user, dashboardData });
}

const fadeInUp = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.6 }
};

const staggerContainer = {
  animate: {
    transition: {
      staggerChildren: 0.1
    }
  }
};

export default function DashboardIndex() {
  const { user, dashboardData } = useLoaderData<typeof loader>();

  const calculateTrend = (current: number, previous: number) => {
    if (previous === 0) return "+100%";
    const change = ((current - previous) / previous) * 100;
    return `${change >= 0 ? "+" : ""}${change.toFixed(0)}%`;
  };

  return (
    <DashboardLayout user={user}>
      <div className="space-y-8">
        {/* Welcome Section */}
        <motion.div
          initial="initial"
          animate="animate"
          variants={fadeInUp}
        >
          <div className="bg-gradient-to-r from-primary-600 to-accent-500 rounded-2xl p-8 text-white">
            <h1 className="text-3xl font-bold">
              Welcome back, {user.name || user.email}!
            </h1>
            <p className="mt-2 text-primary-100">
              Here's what's happening with your digital content today.
            </p>
          </div>
        </motion.div>

        {/* Stats Overview */}
        <motion.div
          initial="initial"
          animate="animate"
          variants={staggerContainer}
        >
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
            <StatsCard
              number={dashboardData.stats.totalUploads.toString()}
              label="Total Uploads"
              icon={Upload}
              trend={calculateTrend(
                dashboardData.quickStats.thisMonth.uploads,
                dashboardData.quickStats.lastMonth.uploads
              )}
            />
            <StatsCard
              number={dashboardData.stats.verifiedContent.toString()}
              label="Verified Content"
              icon={CheckCircle}
              trend={calculateTrend(
                dashboardData.quickStats.thisMonth.verifications,
                dashboardData.quickStats.lastMonth.verifications
              )}
            />
            <StatsCard
              number={dashboardData.stats.pendingVerifications.toString()}
              label="Pending Verifications"
              icon={Clock}
              trend="Processing"
            />
            <StatsCard
              number={`${dashboardData.stats.storageUsed}GB`}
              label={`Storage Used (${dashboardData.stats.storageLimit}GB limit)`}
              icon={BarChart3}
              trend={`${((dashboardData.stats.storageUsed / dashboardData.stats.storageLimit) * 100).toFixed(0)}% used`}
            />
          </div>
        </motion.div>

        {/* Quick Actions */}
        <motion.div
          initial="initial"
          animate="animate"
          variants={fadeInUp}
        >
          <h2 className="text-2xl font-bold text-secondary-900 mb-6">Quick Actions</h2>
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
            <QuickActionCard
              title="Upload Content"
              description="Add new digital content for verification"
              icon={Upload}
              href="/dashboard/upload"
              color="primary"
            />
            <QuickActionCard
              title="Search & Manage"
              description="Find and organize your verified content"
              icon={Search}
              href="/dashboard/content"
              color="secondary"
            />
            <QuickActionCard
              title="View Analytics"
              description="Track performance and engagement"
              icon={BarChart3}
              href="/dashboard/analytics"
              color="accent"
            />
            <QuickActionCard
              title="Team Management"
              description="Manage team members and permissions"
              icon={Users}
              href="/dashboard/team"
              color="primary"
              disabled={user.plan === "free"}
            />
            <QuickActionCard
              title="API Documentation"
              description="Integrate EcoStamp into your workflow"
              icon={FileText}
              href="/dashboard/api"
              color="secondary"
              disabled={user.plan === "free"}
            />
            <QuickActionCard
              title="Verification Center"
              description="Monitor verification status and history"
              icon={Shield}
              href="/dashboard/verifications"
              color="accent"
            />
          </div>
        </motion.div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
          {/* Recent Activity */}
          <motion.div
            className="lg:col-span-2"
            initial="initial"
            animate="animate"
            variants={fadeInUp}
          >
            <div className="bg-white rounded-2xl border border-secondary-200 p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-secondary-900">Recent Activity</h3>
                <button className="text-sm text-primary-600 hover:text-primary-700 font-medium">
                  View all
                </button>
              </div>
              <RecentActivity activities={dashboardData.recentActivity} />
            </div>
          </motion.div>

          {/* Sidebar */}
          <motion.div
            className="space-y-6"
            initial="initial"
            animate="animate"
            variants={staggerContainer}
          >
            {/* Upload Progress */}
            <motion.div variants={fadeInUp}>
              <UploadProgress 
                completed={dashboardData.stats.verifiedContent}
                pending={dashboardData.stats.pendingVerifications}
                total={dashboardData.stats.totalUploads}
              />
            </motion.div>

            {/* Verification Status */}
            <motion.div variants={fadeInUp}>
              <VerificationStatus 
                plan={user.plan}
                verificationsUsed={dashboardData.quickStats.thisMonth.verifications}
                verificationsLimit={user.plan === "free" ? 5 : null}
              />
            </motion.div>

            {/* Plan Upgrade CTA */}
            {user.plan === "free" && (
              <motion.div variants={fadeInUp}>
                <div className="bg-gradient-to-br from-primary-50 to-accent-50 rounded-2xl p-6 border border-primary-200">
                  <div className="flex items-center mb-4">
                    <TrendingUp className="h-6 w-6 text-primary-600" />
                    <h4 className="ml-2 text-lg font-semibold text-secondary-900">
                      Upgrade to Pro
                    </h4>
                  </div>
                  <p className="text-sm text-secondary-600 mb-4">
                    Unlock unlimited verifications, advanced analytics, and team collaboration features.
                  </p>
                  <button className="w-full bg-primary-600 text-white rounded-lg px-4 py-2 text-sm font-medium hover:bg-primary-700 transition-colors">
                    Upgrade Now
                  </button>
                </div>
              </motion.div>
            )}
          </motion.div>
        </div>
      </div>
    </DashboardLayout>
  );
}

{"name": "thread-merging-orchestrator", "version": "1.0.0", "description": "Automated Thread-Merging Orchestration System that searches, highlights, and merges relevant threads from ChatGPT and Perplexity, then feeds them to Claude/Gemini for code generation/analysis", "main": "src/index.js", "type": "module", "scripts": {"start": "node src/index.js", "dev": "node --watch src/index.js", "test": "jest", "test:watch": "jest --watch", "setup": "node scripts/setup.js", "web": "node src/web/server.js"}, "keywords": ["ai", "orchestration", "chatgpt", "perplexity", "claude", "gemini", "thread-merging", "automation"], "author": "Your Name", "license": "MIT", "dependencies": {"openai": "^4.20.1", "axios": "^1.6.2", "dotenv": "^16.3.1", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "rate-limiter-flexible": "^3.0.8", "node-cron": "^3.0.3", "winston": "^3.11.0", "cheerio": "^1.0.0-rc.12", "puppeteer": "^21.5.2", "similarity": "^1.2.1", "natural": "^6.8.0", "compromise": "^14.10.0", "markdown-it": "^13.0.2", "turndown": "^7.1.2", "uuid": "^9.0.1", "lodash": "^4.17.21", "moment": "^2.29.4", "commander": "^11.1.0", "@azure/openai": "^1.0.0-beta.8", "replicate": "^0.22.0", "groq-sdk": "^0.3.0"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2", "@types/node": "^20.9.0"}, "jest": {"testEnvironment": "node", "transform": {}, "extensionsToTreatAsEsm": [".js"], "globals": {"NODE_OPTIONS": "--experimental-vm-modules"}}, "engines": {"node": ">=18.0.0"}}
"""
Setup script for Universal Dual-Purpose Feedback Loop Framework
"""

from setuptools import setup, find_packages
from pathlib import Path

# Read README file
readme_file = Path(__file__).parent / "README.md"
long_description = readme_file.read_text(encoding="utf-8") if readme_file.exists() else ""

# Read requirements
requirements_file = Path(__file__).parent / "requirements.txt"
if requirements_file.exists():
    requirements = requirements_file.read_text().strip().split('\n')
    requirements = [req.strip() for req in requirements if req.strip() and not req.startswith('#')]
else:
    requirements = [
        'pyyaml>=6.0',
        'python-dateutil>=2.8.0',
    ]

setup(
    name="feedback-loop-framework",
    version="1.0.0",
    author="AI Orchestration Team",
    author_email="<EMAIL>",
    description="Universal Dual-Purpose Feedback Loop Framework for AI Systems",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/ai-orchestration/feedback-loop-framework",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Software Development :: Libraries :: Python Modules",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: System :: Monitoring",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require={
        'dev': [
            'pytest>=7.0.0',
            'pytest-cov>=4.0.0',
            'black>=22.0.0',
            'flake8>=5.0.0',
            'mypy>=1.0.0',
        ],
        'dashboard': [
            'flask>=2.0.0',
            'plotly>=5.0.0',
            'pandas>=1.3.0',
        ],
        'advanced': [
            'numpy>=1.21.0',
            'scipy>=1.7.0',
            'scikit-learn>=1.0.0',
        ]
    },
    entry_points={
        'console_scripts': [
            'feedback-cli=feedback_loop_framework.cli.feedback_cli:main',
        ],
    },
    include_package_data=True,
    package_data={
        'feedback_loop_framework': [
            'config/*.yaml',
            'examples/*.py',
            'tests/*.py',
        ],
    },
    zip_safe=False,
    keywords=[
        'ai', 'feedback', 'quality-assurance', 'machine-learning', 
        'orchestration', 'validation', 'trust-scoring', 'confidence',
        'drone-ai', 'timestamp-ai', 'continuous-improvement'
    ],
    project_urls={
        'Bug Reports': 'https://github.com/ai-orchestration/feedback-loop-framework/issues',
        'Source': 'https://github.com/ai-orchestration/feedback-loop-framework',
        'Documentation': 'https://feedback-loop-framework.readthedocs.io/',
    },
)

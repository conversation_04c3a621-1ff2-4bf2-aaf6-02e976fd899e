"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.contextApi = exports.auditApi = exports.evolutionApi = exports.tunnelApi = exports.workflowApi = exports.agentApi = exports.orchestratorApi = exports.authApi = void 0;
const axios_1 = __importDefault(require("axios"));
// Create axios instance
const api = axios_1.default.create({
    baseURL: '/api',
    timeout: 10000,
});
// Request interceptor to add auth token
api.interceptors.request.use((config) => {
    const token = localStorage.getItem('token');
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
}, (error) => {
    return Promise.reject(error);
});
// Response interceptor for error handling
api.interceptors.response.use((response) => response, (error) => {
    if (error.response?.status === 401) {
        localStorage.removeItem('token');
        window.location.href = '/login';
    }
    return Promise.reject(error);
});
// Auth API
exports.authApi = {
    login: (email, password) => api.post('/auth/login', { email, password }),
    register: (email, username, password) => api.post('/auth/register', { email, username, password }),
    getCurrentUser: () => api.get('/auth/me'),
    refreshToken: () => api.post('/auth/refresh'),
};
// Orchestrator API
exports.orchestratorApi = {
    getAll: (params) => api.get('/orchestrators', { params }),
    getById: (id) => api.get(`/orchestrators/${id}`),
    create: (data) => api.post('/orchestrators', data),
    update: (id, data) => api.put(`/orchestrators/${id}`, data),
    delete: (id) => api.delete(`/orchestrators/${id}`),
};
// Agent API
exports.agentApi = {
    getAll: (params) => api.get('/agents', { params }),
    getById: (id) => api.get(`/agents/${id}`),
    getRoles: () => api.get('/agents/roles'),
    getRegistry: () => api.get('/agents/registry'),
    create: (data) => api.post('/agents', data),
    update: (id, data) => api.put(`/agents/${id}`, data),
    delete: (id) => api.delete(`/agents/${id}`),
    assign: (criteria) => api.post('/agents/assign', criteria),
    assignRoles: (data) => api.post('/agents/assign-roles', data),
    getRoleRecommendations: (roleId) => api.get(`/agents/recommendations/${roleId}`),
    getCapabilitiesAnalysis: () => api.get('/agents/capabilities-analysis'),
};
// Workflow API
exports.workflowApi = {
    getTemplates: (params) => api.get('/workflows/templates', { params }),
    getExecutions: (params) => api.get('/workflows/executions', { params }),
    getExecutionStatus: (executionId) => api.get(`/workflows/executions/${executionId}`),
    startExecution: (templateId, context) => api.post(`/workflows/${templateId}/execute`, { context }),
    cancelExecution: (executionId) => api.post(`/workflows/executions/${executionId}/cancel`),
    createTemplate: (data) => api.post('/workflows/templates', data),
};
// Tunnel API
exports.tunnelApi = {
    getAll: (params) => api.get('/tunnels', { params }),
    getById: (id) => api.get(`/tunnels/${id}`),
    create: (data) => api.post('/tunnels', data),
    update: (id, data) => api.put(`/tunnels/${id}`, data),
    delete: (id) => api.delete(`/tunnels/${id}`),
    sendData: (id, data) => api.post(`/tunnels/${id}/send`, data),
};
// Evolution API
exports.evolutionApi = {
    getVariants: () => api.get('/evolution/variants'),
    getStatus: () => api.get('/evolution/status'),
    getHistory: (limit) => api.get('/evolution/history', { params: { limit } }),
    startEvolution: (parameters) => api.post('/evolution/start', { parameters }),
    stopEvolution: () => api.post('/evolution/stop'),
};
// Audit API
exports.auditApi = {
    getLogs: (params) => api.get('/audit', { params }),
};
// Context API
exports.contextApi = {
    getAll: () => api.get('/context'),
    create: (data) => api.post('/context', data),
};
exports.default = api;

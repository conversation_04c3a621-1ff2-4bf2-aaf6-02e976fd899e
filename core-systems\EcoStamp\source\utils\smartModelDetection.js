/**
 * Smart Model Detection & Auto Routing System
 * Automatically detects specific AI models and routes to appropriate calculations
 */

// Model detection patterns and signatures
const MODEL_SIGNATURES = {
  chatgpt: {
    'gpt-4-turbo': {
      patterns: [
        /gpt-4-turbo/i,
        /model.*gpt-4.*turbo/i,
        /browsing.*enabled/i,
        /code.*interpreter/i
      ],
      indicators: ['advanced reasoning', 'code execution', 'web browsing'],
      contextSize: 128000,
      capabilities: ['multimodal', 'browsing', 'code']
    },
    'gpt-4': {
      patterns: [
        /gpt-4(?!.*turbo)/i,
        /model.*gpt-4(?!.*turbo)/i
      ],
      indicators: ['detailed analysis', 'complex reasoning'],
      contextSize: 8192,
      capabilities: ['multimodal', 'reasoning']
    },
    'gpt-3.5-turbo': {
      patterns: [
        /gpt-3\.5/i,
        /model.*3\.5/i
      ],
      indicators: ['quick response', 'efficient'],
      contextSize: 4096,
      capabilities: ['text']
    }
  },
  claude: {
    'claude-3-opus': {
      patterns: [
        /claude.*3.*opus/i,
        /opus/i
      ],
      indicators: ['sophisticated analysis', 'creative writing', 'complex reasoning'],
      contextSize: 200000,
      capabilities: ['multimodal', 'analysis', 'creative']
    },
    'claude-3-sonnet': {
      patterns: [
        /claude.*3.*sonnet/i,
        /sonnet/i
      ],
      indicators: ['balanced performance', 'general purpose'],
      contextSize: 200000,
      capabilities: ['multimodal', 'balanced']
    },
    'claude-3-haiku': {
      patterns: [
        /claude.*3.*haiku/i,
        /haiku/i
      ],
      indicators: ['fast response', 'efficient'],
      contextSize: 200000,
      capabilities: ['text', 'fast']
    },
    'claude-instant': {
      patterns: [
        /claude.*instant/i,
        /instant/i
      ],
      indicators: ['quick', 'lightweight'],
      contextSize: 100000,
      capabilities: ['text']
    }
  },
  gemini: {
    'gemini-ultra': {
      patterns: [
        /gemini.*ultra/i,
        /ultra/i
      ],
      indicators: ['advanced capabilities', 'complex tasks'],
      contextSize: 32768,
      capabilities: ['multimodal', 'advanced']
    },
    'gemini-pro': {
      patterns: [
        /gemini.*pro/i,
        /pro/i
      ],
      indicators: ['professional use', 'balanced'],
      contextSize: 32768,
      capabilities: ['multimodal', 'professional']
    },
    'gemini-nano': {
      patterns: [
        /gemini.*nano/i,
        /nano/i
      ],
      indicators: ['lightweight', 'mobile'],
      contextSize: 8192,
      capabilities: ['text', 'mobile']
    }
  }
};

// Response characteristics that help identify models
const RESPONSE_CHARACTERISTICS = {
  'gpt-4-turbo': {
    avgResponseLength: 800,
    complexity: 'high',
    style: 'detailed',
    features: ['step-by-step', 'comprehensive']
  },
  'gpt-4': {
    avgResponseLength: 600,
    complexity: 'high',
    style: 'analytical',
    features: ['thorough', 'nuanced']
  },
  'gpt-3.5-turbo': {
    avgResponseLength: 400,
    complexity: 'medium',
    style: 'concise',
    features: ['direct', 'efficient']
  },
  'claude-3-opus': {
    avgResponseLength: 900,
    complexity: 'very-high',
    style: 'sophisticated',
    features: ['creative', 'philosophical']
  },
  'claude-3-sonnet': {
    avgResponseLength: 650,
    complexity: 'high',
    style: 'balanced',
    features: ['thoughtful', 'structured']
  },
  'claude-3-haiku': {
    avgResponseLength: 300,
    complexity: 'medium',
    style: 'concise',
    features: ['quick', 'focused']
  },
  'gemini-ultra': {
    avgResponseLength: 750,
    complexity: 'high',
    style: 'comprehensive',
    features: ['multimodal', 'detailed']
  },
  'gemini-pro': {
    avgResponseLength: 550,
    complexity: 'medium-high',
    style: 'professional',
    features: ['practical', 'informative']
  }
};

/**
 * Detect the specific AI model being used
 * @param {string} platform - Base platform (chatgpt, claude, gemini)
 * @param {string} input - User input text
 * @param {string} output - AI response text
 * @param {object} context - Additional context (URL, page elements, etc.)
 * @returns {object} Detected model information
 */
export function detectSpecificModel(platform, input, output, context = {}) {
  const platformModels = MODEL_SIGNATURES[platform];
  if (!platformModels) {
    return { platform, model: 'unknown', confidence: 0 };
  }

  let bestMatch = null;
  let highestConfidence = 0;

  for (const [modelName, modelData] of Object.entries(platformModels)) {
    let confidence = 0;

    // Check URL patterns
    if (context.url) {
      for (const pattern of modelData.patterns) {
        if (pattern.test(context.url)) {
          confidence += 30;
        }
      }
    }

    // Check page content for model indicators
    if (context.pageContent) {
      for (const pattern of modelData.patterns) {
        if (pattern.test(context.pageContent)) {
          confidence += 25;
        }
      }
    }

    // Analyze response characteristics
    const responseLength = output.length;
    const expectedLength = RESPONSE_CHARACTERISTICS[modelName]?.avgResponseLength || 500;
    const lengthDiff = Math.abs(responseLength - expectedLength) / expectedLength;
    
    if (lengthDiff < 0.3) {
      confidence += 15;
    }

    // Check for model-specific indicators in response
    const responseText = output.toLowerCase();
    for (const indicator of modelData.indicators) {
      if (responseText.includes(indicator.toLowerCase())) {
        confidence += 10;
      }
    }

    // Analyze complexity based on response structure
    const complexity = analyzeResponseComplexity(output);
    const expectedComplexity = RESPONSE_CHARACTERISTICS[modelName]?.complexity || 'medium';
    
    if (complexity === expectedComplexity) {
      confidence += 20;
    }

    if (confidence > highestConfidence) {
      highestConfidence = confidence;
      bestMatch = {
        platform,
        model: modelName,
        confidence: Math.min(confidence, 100),
        capabilities: modelData.capabilities,
        contextSize: modelData.contextSize,
        characteristics: RESPONSE_CHARACTERISTICS[modelName]
      };
    }
  }

  // Fallback to platform default if confidence is too low
  if (highestConfidence < 30) {
    const defaultModels = {
      chatgpt: 'gpt-4',
      claude: 'claude-3-sonnet',
      gemini: 'gemini-pro'
    };

    return {
      platform,
      model: defaultModels[platform] || 'unknown',
      confidence: 25,
      fallback: true
    };
  }

  return bestMatch;
}

/**
 * Analyze response complexity
 * @param {string} text - Response text
 * @returns {string} Complexity level
 */
function analyzeResponseComplexity(text) {
  const sentences = text.split(/[.!?]+/).length;
  const words = text.split(/\s+/).length;
  const avgWordsPerSentence = words / sentences;
  
  // Check for complex structures
  const hasLists = /[-*•]\s/.test(text) || /\d+\.\s/.test(text);
  const hasCodeBlocks = /```/.test(text) || /`[^`]+`/.test(text);
  const hasComplexPunctuation = /[;:—–]/.test(text);
  
  let complexityScore = 0;
  
  if (avgWordsPerSentence > 20) complexityScore += 2;
  else if (avgWordsPerSentence > 15) complexityScore += 1;
  
  if (hasLists) complexityScore += 1;
  if (hasCodeBlocks) complexityScore += 2;
  if (hasComplexPunctuation) complexityScore += 1;
  if (words > 500) complexityScore += 1;
  
  if (complexityScore >= 5) return 'very-high';
  if (complexityScore >= 3) return 'high';
  if (complexityScore >= 1) return 'medium';
  return 'low';
}

/**
 * Get enhanced context from page
 * @returns {object} Page context information
 */
export function getPageContext() {
  const context = {
    url: window.location.href,
    title: document.title,
    pageContent: '',
    modelIndicators: []
  };

  // Platform-specific context extraction
  const platform = detectBasePlatform();
  
  switch (platform) {
    case 'chatgpt':
      // Look for model indicators in ChatGPT interface
      const modelSelector = document.querySelector('[data-testid="model-switcher"]');
      if (modelSelector) {
        context.modelIndicators.push(modelSelector.textContent);
      }
      
      // Check for GPT-4 indicators
      const gpt4Indicators = document.querySelectorAll('[title*="GPT-4"], [aria-label*="GPT-4"]');
      gpt4Indicators.forEach(el => context.modelIndicators.push(el.textContent || el.title));
      break;
      
    case 'claude':
      // Look for Claude model indicators
      const claudeModel = document.querySelector('.model-name, [data-model]');
      if (claudeModel) {
        context.modelIndicators.push(claudeModel.textContent);
      }
      break;
      
    case 'gemini':
      // Look for Gemini model indicators
      const geminiModel = document.querySelector('[data-model-name], .model-indicator');
      if (geminiModel) {
        context.modelIndicators.push(geminiModel.textContent);
      }
      break;
  }

  context.pageContent = context.modelIndicators.join(' ');
  return context;
}

/**
 * Detect base platform
 * @returns {string} Platform name
 */
function detectBasePlatform() {
  const hostname = window.location.hostname;
  if (hostname.includes('openai.com')) return 'chatgpt';
  if (hostname.includes('claude.ai')) return 'claude';
  if (hostname.includes('gemini.google.com')) return 'gemini';
  return 'unknown';
}

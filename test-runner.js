#!/usr/bin/env node

/**
 * Simple Test Runner for Time Stamp Project
 * Basic version to test project discovery and validation
 */

import fs from 'fs-extra';
import path from 'path';
import { execSync } from 'child_process';

console.log('🚀 Time Stamp Project Test Runner');
console.log('==================================');

const workspaceRoot = process.cwd();
console.log(`Workspace: ${workspaceRoot}`);

// Discover projects
const projectDirs = [
  'core-systems/EcoStamp/source',
  'development-tools',
  'ai-orchestration',
  'core-systems'
];

console.log('\n📦 Discovering Projects:');

for (const dir of projectDirs) {
  const fullPath = path.join(workspaceRoot, dir);
  
  try {
    if (await fs.pathExists(fullPath)) {
      console.log(`✅ Found: ${dir}`);
      
      // Check for package.json
      const packageJsonPath = path.join(fullPath, 'package.json');
      if (await fs.pathExists(packageJsonPath)) {
        const packageJson = await fs.readJson(packageJsonPath);
        console.log(`   📄 package.json: ${packageJson.name || 'unnamed'}`);
        
        if (packageJson.scripts) {
          const scripts = Object.keys(packageJson.scripts);
          console.log(`   📜 Scripts: ${scripts.join(', ')}`);
        }
      }
      
      // Check for Python files
      const setupPyPath = path.join(fullPath, 'setup.py');
      const requirementsPath = path.join(fullPath, 'requirements.txt');
      
      if (await fs.pathExists(setupPyPath)) {
        console.log(`   🐍 Python: setup.py found`);
      }
      
      if (await fs.pathExists(requirementsPath)) {
        console.log(`   🐍 Python: requirements.txt found`);
      }
      
    } else {
      console.log(`❌ Not found: ${dir}`);
    }
  } catch (error) {
    console.log(`❌ Error checking ${dir}: ${error.message}`);
  }
  
  console.log('');
}

console.log('🎯 Quick Test Commands:');
console.log('');
console.log('EcoStamp Backend:');
console.log('  cd core-systems/EcoStamp/source');
console.log('  npm install && npm start');
console.log('');
console.log('Security Scanner:');
console.log('  cd development-tools');
console.log('  npm install && npm run scan');
console.log('');
console.log('AI Orchestration:');
console.log('  cd ai-orchestration');
console.log('  npm install && node orchestrator.js');
console.log('');

console.log('✅ Discovery complete!');

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.tunnelRoutes = void 0;
const express_1 = require("express");
const client_1 = require("@prisma/client");
const zod_1 = require("zod");
const errorHandler_1 = require("../middleware/errorHandler");
const EventBus_1 = require("../services/EventBus");
const logger_1 = require("../utils/logger");
const router = (0, express_1.Router)();
exports.tunnelRoutes = router;
const prisma = new client_1.PrismaClient();
const eventBus = new EventBus_1.EventBus();
// Validation schemas
const createTunnelSchema = zod_1.z.object({
    name: zod_1.z.string().min(1, 'Name is required').max(100, 'Name too long'),
    description: zod_1.z.string().optional(),
    fromAgentId: zod_1.z.string().min(1, 'From agent is required'),
    toAgentId: zod_1.z.string().min(1, 'To agent is required'),
    tunnelType: zod_1.z.enum(['BIDIRECTIONAL', 'UNIDIRECTIONAL']).default('BIDIRECTIONAL'),
    tags: zod_1.z.array(zod_1.z.string()).default([]),
    metadata: zod_1.z.record(zod_1.z.any()).optional(),
    isActive: zod_1.z.boolean().default(true),
});
const updateTunnelSchema = zod_1.z.object({
    name: zod_1.z.string().min(1, 'Name is required').max(100, 'Name too long').optional(),
    description: zod_1.z.string().optional(),
    tunnelType: zod_1.z.enum(['BIDIRECTIONAL', 'UNIDIRECTIONAL']).optional(),
    tags: zod_1.z.array(zod_1.z.string()).optional(),
    metadata: zod_1.z.record(zod_1.z.any()).optional(),
    isActive: zod_1.z.boolean().optional(),
});
const sendDataSchema = zod_1.z.object({
    data: zod_1.z.record(zod_1.z.any()),
    messageType: zod_1.z.string().default('DATA'),
    priority: zod_1.z.number().min(1).max(10).default(5),
    metadata: zod_1.z.record(zod_1.z.any()).optional(),
});
// Get all tunnels
router.get('/', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const search = req.query.search;
    const tunnelType = req.query.tunnelType;
    const isActive = req.query.isActive === 'true' ? true : req.query.isActive === 'false' ? false : undefined;
    const agentId = req.query.agentId;
    const skip = (page - 1) * limit;
    const where = {};
    if (search) {
        where.OR = [
            { name: { contains: search, mode: 'insensitive' } },
            { description: { contains: search, mode: 'insensitive' } },
        ];
    }
    if (tunnelType) {
        where.tunnelType = tunnelType;
    }
    if (isActive !== undefined) {
        where.isActive = isActive;
    }
    if (agentId) {
        where.OR = [
            { fromAgentId: agentId },
            { toAgentId: agentId },
        ];
    }
    const [tunnels, total] = await Promise.all([
        prisma.tunnel.findMany({
            where,
            skip,
            take: limit,
            include: {
                fromAgent: {
                    select: {
                        id: true,
                        agentId: true,
                        name: true,
                        vendor: true,
                    },
                },
                toAgent: {
                    select: {
                        id: true,
                        agentId: true,
                        name: true,
                        vendor: true,
                    },
                },
                _count: {
                    select: {
                        dataFlows: true,
                    },
                },
            },
            orderBy: {
                createdAt: 'desc',
            },
        }),
        prisma.tunnel.count({ where }),
    ]);
    res.json({
        success: true,
        data: tunnels,
        pagination: {
            page,
            limit,
            total,
            totalPages: Math.ceil(total / limit),
        },
    });
}));
// Create new tunnel
router.post('/', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const validatedData = createTunnelSchema.parse(req.body);
    // Validate agents exist
    const [fromAgent, toAgent] = await Promise.all([
        prisma.agent.findUnique({ where: { id: validatedData.fromAgentId } }),
        prisma.agent.findUnique({ where: { id: validatedData.toAgentId } }),
    ]);
    if (!fromAgent) {
        throw new errorHandler_1.AppError('From agent not found', 404);
    }
    if (!toAgent) {
        throw new errorHandler_1.AppError('To agent not found', 404);
    }
    if (validatedData.fromAgentId === validatedData.toAgentId) {
        throw new errorHandler_1.AppError('Cannot create tunnel to the same agent', 400);
    }
    // Check if tunnel already exists
    const existingTunnel = await prisma.tunnel.findFirst({
        where: {
            OR: [
                {
                    fromAgentId: validatedData.fromAgentId,
                    toAgentId: validatedData.toAgentId,
                },
                {
                    fromAgentId: validatedData.toAgentId,
                    toAgentId: validatedData.fromAgentId,
                },
            ],
        },
    });
    if (existingTunnel) {
        throw new errorHandler_1.AppError('Tunnel already exists between these agents', 400);
    }
    const tunnel = await prisma.tunnel.create({
        data: {
            ...validatedData,
            createdBy: req.user.id,
        },
        include: {
            fromAgent: {
                select: {
                    id: true,
                    agentId: true,
                    name: true,
                    vendor: true,
                },
            },
            toAgent: {
                select: {
                    id: true,
                    agentId: true,
                    name: true,
                    vendor: true,
                },
            },
        },
    });
    // Emit event
    eventBus.emit(EventBus_1.EVENT_TYPES.TUNNEL_CREATED, {
        tunnelId: tunnel.id,
        userId: req.user.id,
        data: tunnel,
    });
    logger_1.logger.info(`Tunnel created: ${tunnel.name}`, {
        tunnelId: tunnel.id,
        fromAgent: fromAgent.name,
        toAgent: toAgent.name,
        userId: req.user.id,
    });
    res.status(201).json({
        success: true,
        data: tunnel,
    });
}));
// Update tunnel
router.put('/:id', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const validatedData = updateTunnelSchema.parse(req.body);
    const existingTunnel = await prisma.tunnel.findUnique({
        where: { id },
    });
    if (!existingTunnel) {
        throw new errorHandler_1.AppError('Tunnel not found', 404);
    }
    const tunnel = await prisma.tunnel.update({
        where: { id },
        data: validatedData,
        include: {
            fromAgent: {
                select: {
                    id: true,
                    agentId: true,
                    name: true,
                    vendor: true,
                },
            },
            toAgent: {
                select: {
                    id: true,
                    agentId: true,
                    name: true,
                    vendor: true,
                },
            },
        },
    });
    // Emit event
    eventBus.emit(EventBus_1.EVENT_TYPES.TUNNEL_UPDATED, {
        tunnelId: tunnel.id,
        userId: req.user.id,
        data: tunnel,
    });
    logger_1.logger.info(`Tunnel updated: ${tunnel.name}`, {
        tunnelId: tunnel.id,
        userId: req.user.id,
    });
    res.json({
        success: true,
        data: tunnel,
    });
}));
// Delete tunnel
router.delete('/:id', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const existingTunnel = await prisma.tunnel.findUnique({
        where: { id },
        include: {
            fromAgent: { select: { name: true } },
            toAgent: { select: { name: true } },
        },
    });
    if (!existingTunnel) {
        throw new errorHandler_1.AppError('Tunnel not found', 404);
    }
    await prisma.tunnel.delete({
        where: { id },
    });
    // Emit event
    eventBus.emit(EventBus_1.EVENT_TYPES.TUNNEL_DELETED, {
        tunnelId: id,
        userId: req.user.id,
        data: existingTunnel,
    });
    logger_1.logger.info(`Tunnel deleted: ${existingTunnel.name}`, {
        tunnelId: id,
        fromAgent: existingTunnel.fromAgent.name,
        toAgent: existingTunnel.toAgent.name,
        userId: req.user.id,
    });
    res.json({
        success: true,
        message: 'Tunnel deleted successfully',
    });
}));
// Send data through tunnel
router.post('/:id/send', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const validatedData = sendDataSchema.parse(req.body);
    const tunnel = await prisma.tunnel.findUnique({
        where: { id },
        include: {
            fromAgent: true,
            toAgent: true,
        },
    });
    if (!tunnel) {
        throw new errorHandler_1.AppError('Tunnel not found', 404);
    }
    if (!tunnel.isActive) {
        throw new errorHandler_1.AppError('Tunnel is not active', 400);
    }
    // Create data flow record
    const dataFlow = await prisma.dataFlow.create({
        data: {
            tunnelId: id,
            direction: 'OUTBOUND',
            data: validatedData.data,
            messageType: validatedData.messageType,
            priority: validatedData.priority,
            metadata: validatedData.metadata,
        },
    });
    // Emit real-time event
    eventBus.emit(EventBus_1.EVENT_TYPES.TUNNEL_DATA_SENT, {
        tunnelId: id,
        dataFlowId: dataFlow.id,
        userId: req.user.id,
        data: {
            tunnel,
            dataFlow,
        },
    });
    logger_1.logger.info(`Data sent through tunnel: ${tunnel.name}`, {
        tunnelId: id,
        dataFlowId: dataFlow.id,
        messageType: validatedData.messageType,
        userId: req.user.id,
    });
    res.json({
        success: true,
        data: {
            dataFlowId: dataFlow.id,
            tunnel: {
                id: tunnel.id,
                name: tunnel.name,
                fromAgent: tunnel.fromAgent.name,
                toAgent: tunnel.toAgent.name,
            },
        },
    });
}));

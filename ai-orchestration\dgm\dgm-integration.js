/**
 * DGM Integration Layer
 * 
 * Seamlessly integrates the Darwin Gödel Machine with existing orchestration systems:
 * - AI orchestration system integration
 * - Thread-merging orchestrator integration
 * - Backward compatibility maintenance
 * - Gradual migration support
 */

const path = require('path');
const fs = require('fs').promises;
const chalk = require('chalk');

const DGMEngine = require('./core/dgm-engine');
const WebDashboard = require('./interface/web-dashboard');
const CLIInterface = require('./interface/cli-interface');
const APIServer = require('./interface/api-server');

class DGMIntegration {
  constructor(existingOrchestrator, threadMergingOrchestrator, config = {}) {
    this.existingOrchestrator = existingOrchestrator;
    this.threadMergingOrchestrator = threadMergingOrchestrator;
    this.config = config;
    
    // DGM components
    this.dgmEngine = null;
    this.webDashboard = null;
    this.cliInterface = null;
    this.apiServer = null;
    
    // Integration state
    this.isInitialized = false;
    this.isRunning = false;
    this.integrationMode = config.mode || 'hybrid'; // 'hybrid', 'dgm-only', 'legacy-only'
    this.migrationPhase = 'preparation'; // 'preparation', 'testing', 'gradual', 'complete'
    
    // Performance tracking
    this.performanceComparison = {
      legacy: { requests: 0, successRate: 0, avgTime: 0 },
      dgm: { requests: 0, successRate: 0, avgTime: 0 }
    };
  }

  /**
   * Initialize the DGM integration
   */
  async initialize() {
    console.log(chalk.blue.bold('🧬 Initializing Darwin Gödel Machine Integration...'));
    
    try {
      // Initialize DGM engine
      await this.initializeDGMEngine();
      
      // Initialize interfaces
      await this.initializeInterfaces();
      
      // Setup integration hooks
      await this.setupIntegrationHooks();
      
      // Create backup of existing system
      await this.createSystemBackup();
      
      this.isInitialized = true;
      console.log(chalk.green('✅ DGM Integration initialized successfully'));
      
    } catch (error) {
      console.error(chalk.red(`❌ DGM Integration initialization failed: ${error.message}`));
      throw error;
    }
  }

  /**
   * Initialize the DGM engine
   */
  async initializeDGMEngine() {
    console.log(chalk.blue('🔧 Initializing DGM Engine...'));
    
    this.dgmEngine = new DGMEngine(this.config);
    await this.dgmEngine.initialize();
    
    // Setup event handlers for integration
    this.setupDGMEventHandlers();
    
    console.log(chalk.green('✅ DGM Engine initialized'));
  }

  /**
   * Initialize user interfaces
   */
  async initializeInterfaces() {
    console.log(chalk.blue('🖥️  Initializing DGM Interfaces...'));
    
    // Initialize web dashboard
    if (this.config.ui?.webDashboard?.enabled !== false) {
      this.webDashboard = new WebDashboard(this.dgmEngine, this.config);
      await this.webDashboard.start();
    }
    
    // Initialize CLI interface
    if (this.config.ui?.cli?.enabled !== false) {
      this.cliInterface = new CLIInterface(this.dgmEngine, this.config);
      // CLI will be started on demand
    }
    
    // Initialize API server
    if (this.config.ui?.api?.enabled !== false) {
      this.apiServer = new APIServer(this.dgmEngine, this.config);
      await this.apiServer.start();
    }
    
    console.log(chalk.green('✅ DGM Interfaces initialized'));
  }

  /**
   * Setup integration hooks with existing systems
   */
  async setupIntegrationHooks() {
    console.log(chalk.blue('🔗 Setting up integration hooks...'));
    
    // Hook into existing orchestrator
    if (this.existingOrchestrator) {
      this.hookIntoExistingOrchestrator();
    }
    
    // Hook into thread-merging orchestrator
    if (this.threadMergingOrchestrator) {
      this.hookIntoThreadMergingOrchestrator();
    }
    
    // Setup request routing
    this.setupRequestRouting();
    
    console.log(chalk.green('✅ Integration hooks established'));
  }

  /**
   * Hook into existing AI orchestrator
   */
  hookIntoExistingOrchestrator() {
    const originalExecute = this.existingOrchestrator.execute?.bind(this.existingOrchestrator);
    
    if (originalExecute) {
      this.existingOrchestrator.execute = async (...args) => {
        return this.routeOrchestrationRequest('existing', originalExecute, ...args);
      };
    }
    
    // Hook into cross-flow engine if available
    if (this.existingOrchestrator.crossFlowEngine) {
      const originalCrossFlow = this.existingOrchestrator.crossFlowEngine.execute?.bind(this.existingOrchestrator.crossFlowEngine);
      
      if (originalCrossFlow) {
        this.existingOrchestrator.crossFlowEngine.execute = async (...args) => {
          return this.routeOrchestrationRequest('cross-flow', originalCrossFlow, ...args);
        };
      }
    }
  }

  /**
   * Hook into thread-merging orchestrator
   */
  hookIntoThreadMergingOrchestrator() {
    const originalOrchestrate = this.threadMergingOrchestrator.orchestrate?.bind(this.threadMergingOrchestrator);
    
    if (originalOrchestrate) {
      this.threadMergingOrchestrator.orchestrate = async (...args) => {
        return this.routeThreadMergingRequest(originalOrchestrate, ...args);
      };
    }
  }

  /**
   * Setup request routing logic
   */
  setupRequestRouting() {
    this.requestRouter = {
      shouldUseDGM: (request, context) => {
        switch (this.integrationMode) {
          case 'dgm-only':
            return true;
          case 'legacy-only':
            return false;
          case 'hybrid':
          default:
            return this.determineRoutingStrategy(request, context);
        }
      }
    };
  }

  /**
   * Determine routing strategy for hybrid mode
   */
  determineRoutingStrategy(request, context) {
    // Route based on migration phase
    switch (this.migrationPhase) {
      case 'preparation':
        return false; // Use legacy system
      
      case 'testing':
        // Route 10% of requests to DGM for testing
        return Math.random() < 0.1;
      
      case 'gradual':
        // Gradually increase DGM usage based on performance
        const dgmSuccessRate = this.performanceComparison.dgm.successRate;
        const legacySuccessRate = this.performanceComparison.legacy.successRate;
        
        if (dgmSuccessRate > legacySuccessRate * 1.1) {
          return Math.random() < 0.5; // 50% to DGM if performing better
        } else {
          return Math.random() < 0.2; // 20% to DGM otherwise
        }
      
      case 'complete':
        return true; // Use DGM system
      
      default:
        return false;
    }
  }

  /**
   * Route orchestration requests
   */
  async routeOrchestrationRequest(type, originalFunction, ...args) {
    const startTime = Date.now();
    const request = args[0] || {};
    
    try {
      let result;
      let usedDGM = false;
      
      if (this.requestRouter.shouldUseDGM(request, { type })) {
        // Use DGM system
        result = await this.executeWithDGM(type, request, ...args);
        usedDGM = true;
        
        // Track DGM performance
        this.trackPerformance('dgm', true, Date.now() - startTime);
      } else {
        // Use legacy system
        result = await originalFunction(...args);
        
        // Track legacy performance
        this.trackPerformance('legacy', true, Date.now() - startTime);
      }
      
      // Log routing decision
      this.logRoutingDecision(type, usedDGM, true, Date.now() - startTime);
      
      return result;
      
    } catch (error) {
      const duration = Date.now() - startTime;
      
      // Track failure
      if (this.requestRouter.shouldUseDGM(request, { type })) {
        this.trackPerformance('dgm', false, duration);
      } else {
        this.trackPerformance('legacy', false, duration);
      }
      
      this.logRoutingDecision(type, this.requestRouter.shouldUseDGM(request, { type }), false, duration);
      
      throw error;
    }
  }

  /**
   * Route thread-merging requests
   */
  async routeThreadMergingRequest(originalFunction, ...args) {
    const startTime = Date.now();
    const request = args[0] || {};
    
    try {
      let result;
      let usedDGM = false;
      
      if (this.requestRouter.shouldUseDGM(request, { type: 'thread-merging' })) {
        // Use DGM-enhanced thread merging
        result = await this.executeThreadMergingWithDGM(request, ...args);
        usedDGM = true;
        
        this.trackPerformance('dgm', true, Date.now() - startTime);
      } else {
        // Use legacy thread merging
        result = await originalFunction(...args);
        
        this.trackPerformance('legacy', true, Date.now() - startTime);
      }
      
      this.logRoutingDecision('thread-merging', usedDGM, true, Date.now() - startTime);
      
      return result;
      
    } catch (error) {
      const duration = Date.now() - startTime;
      
      if (this.requestRouter.shouldUseDGM(request, { type: 'thread-merging' })) {
        this.trackPerformance('dgm', false, duration);
      } else {
        this.trackPerformance('legacy', false, duration);
      }
      
      this.logRoutingDecision('thread-merging', this.requestRouter.shouldUseDGM(request, { type: 'thread-merging' }), false, duration);
      
      throw error;
    }
  }

  /**
   * Execute request using DGM system
   */
  async executeWithDGM(type, request, ...args) {
    // Get the best performing agent from current population
    const population = await this.dgmEngine.agentManager.getCurrentPopulation();
    
    if (population.length === 0) {
      throw new Error('No agents available in DGM population');
    }
    
    // Select best agent
    const bestAgent = population.reduce((best, current) => 
      current.fitness > best.fitness ? current : best
    );
    
    // Execute using the best agent
    return await this.dgmEngine.agentManager.executeAgent(bestAgent, { type, request, args });
  }

  /**
   * Execute thread merging with DGM enhancement
   */
  async executeThreadMergingWithDGM(request, ...args) {
    // Use DGM to enhance thread merging process
    const population = await this.dgmEngine.agentManager.getCurrentPopulation();
    
    if (population.length === 0) {
      // Fallback to original function if no DGM agents available
      return await this.threadMergingOrchestrator.orchestrate(...args);
    }
    
    const bestAgent = population.reduce((best, current) => 
      current.fitness > best.fitness ? current : best
    );
    
    // Execute enhanced thread merging
    return await this.dgmEngine.agentManager.executeAgent(bestAgent, { 
      type: 'thread-merging', 
      request, 
      args 
    });
  }

  /**
   * Track performance metrics
   */
  trackPerformance(system, success, duration) {
    const stats = this.performanceComparison[system];
    
    stats.requests++;
    
    if (success) {
      stats.successRate = ((stats.successRate * (stats.requests - 1)) + 1) / stats.requests;
    } else {
      stats.successRate = (stats.successRate * (stats.requests - 1)) / stats.requests;
    }
    
    stats.avgTime = ((stats.avgTime * (stats.requests - 1)) + duration) / stats.requests;
  }

  /**
   * Log routing decisions
   */
  logRoutingDecision(type, usedDGM, success, duration) {
    const system = usedDGM ? 'DGM' : 'Legacy';
    const status = success ? '✅' : '❌';
    
    console.log(chalk.gray(`${status} ${type} request routed to ${system} (${duration}ms)`));
  }

  /**
   * Create system backup
   */
  async createSystemBackup() {
    console.log(chalk.blue('💾 Creating system backup...'));
    
    try {
      const backupDir = path.join(process.cwd(), 'dgm-backup', Date.now().toString());
      await fs.mkdir(backupDir, { recursive: true });
      
      // Backup orchestrator files
      const orchestratorPath = path.join(process.cwd(), 'ai-orchestration', 'orchestrator.js');
      const crossFlowPath = path.join(process.cwd(), 'ai-orchestration', 'workflows', 'cross-flow-engine.js');
      
      if (await this.fileExists(orchestratorPath)) {
        await fs.copyFile(orchestratorPath, path.join(backupDir, 'orchestrator.js'));
      }
      
      if (await this.fileExists(crossFlowPath)) {
        await fs.copyFile(crossFlowPath, path.join(backupDir, 'cross-flow-engine.js'));
      }
      
      // Backup thread-merging orchestrator
      const threadMergingPath = path.join(process.cwd(), 'thread-merging-orchestrator');
      if (await this.fileExists(threadMergingPath)) {
        await this.copyDirectory(threadMergingPath, path.join(backupDir, 'thread-merging-orchestrator'));
      }
      
      console.log(chalk.green(`✅ System backup created: ${backupDir}`));
      
    } catch (error) {
      console.warn(chalk.yellow(`⚠️  Backup creation failed: ${error.message}`));
    }
  }

  /**
   * Setup DGM event handlers
   */
  setupDGMEventHandlers() {
    this.dgmEngine.on('generationCompleted', (generation) => {
      console.log(chalk.blue(`🧬 Generation ${generation.number} completed - Best fitness: ${generation.bestFitness.toFixed(3)}`));
      
      // Auto-advance migration phase based on performance
      this.evaluateMigrationProgress(generation);
    });
    
    this.dgmEngine.on('agentCreated', (agent) => {
      console.log(chalk.green(`🤖 New agent created: ${agent.id.substring(0, 8)} (fitness: ${agent.fitness.toFixed(3)})`));
    });
    
    this.dgmEngine.on('error', (error) => {
      console.error(chalk.red(`🧬 DGM Error: ${error.message}`));
    });
  }

  /**
   * Evaluate migration progress and auto-advance phases
   */
  evaluateMigrationProgress(generation) {
    const dgmPerformance = this.performanceComparison.dgm;
    const legacyPerformance = this.performanceComparison.legacy;
    
    // Auto-advance migration phases based on performance
    if (this.migrationPhase === 'testing' && 
        dgmPerformance.successRate > legacyPerformance.successRate * 0.9 &&
        dgmPerformance.requests > 50) {
      this.migrationPhase = 'gradual';
      console.log(chalk.green('📈 Advanced to gradual migration phase'));
    }
    
    if (this.migrationPhase === 'gradual' && 
        dgmPerformance.successRate > legacyPerformance.successRate * 1.1 &&
        dgmPerformance.requests > 200) {
      this.migrationPhase = 'complete';
      console.log(chalk.green('🎯 Advanced to complete migration phase'));
    }
  }

  /**
   * Start the DGM system
   */
  async start() {
    if (!this.isInitialized) {
      throw new Error('DGM Integration not initialized. Call initialize() first.');
    }
    
    console.log(chalk.blue.bold('🚀 Starting DGM Integration...'));
    
    try {
      // Start CLI interface if requested
      if (this.config.startCLI) {
        await this.cliInterface.start();
      }
      
      this.isRunning = true;
      console.log(chalk.green('✅ DGM Integration started successfully'));
      
      // Display integration status
      this.displayIntegrationStatus();
      
    } catch (error) {
      console.error(chalk.red(`❌ DGM Integration start failed: ${error.message}`));
      throw error;
    }
  }

  /**
   * Stop the DGM system
   */
  async stop() {
    console.log(chalk.yellow('🛑 Stopping DGM Integration...'));
    
    try {
      // Stop interfaces
      if (this.webDashboard) {
        await this.webDashboard.stop();
      }
      
      if (this.cliInterface) {
        await this.cliInterface.stop();
      }
      
      if (this.apiServer) {
        await this.apiServer.stop();
      }
      
      this.isRunning = false;
      console.log(chalk.yellow('✅ DGM Integration stopped'));
      
    } catch (error) {
      console.error(chalk.red(`❌ DGM Integration stop failed: ${error.message}`));
      throw error;
    }
  }

  /**
   * Display integration status
   */
  displayIntegrationStatus() {
    console.log(chalk.blue.bold('\n📊 DGM Integration Status:'));
    console.log(`Mode: ${this.integrationMode}`);
    console.log(`Migration Phase: ${this.migrationPhase}`);
    console.log(`Web Dashboard: ${this.webDashboard ? `http://localhost:${this.webDashboard.port}` : 'Disabled'}`);
    console.log(`API Server: ${this.apiServer ? `http://localhost:${this.apiServer.port}` : 'Disabled'}`);
    console.log(`CLI Interface: ${this.cliInterface ? 'Available' : 'Disabled'}`);
    
    // Performance comparison
    const dgm = this.performanceComparison.dgm;
    const legacy = this.performanceComparison.legacy;
    
    console.log('\n📈 Performance Comparison:');
    console.log(`Legacy: ${legacy.requests} requests, ${(legacy.successRate * 100).toFixed(1)}% success, ${legacy.avgTime.toFixed(0)}ms avg`);
    console.log(`DGM: ${dgm.requests} requests, ${(dgm.successRate * 100).toFixed(1)}% success, ${dgm.avgTime.toFixed(0)}ms avg`);
    console.log();
  }

  /**
   * Get integration statistics
   */
  getIntegrationStats() {
    return {
      isInitialized: this.isInitialized,
      isRunning: this.isRunning,
      integrationMode: this.integrationMode,
      migrationPhase: this.migrationPhase,
      performanceComparison: this.performanceComparison,
      interfaces: {
        webDashboard: this.webDashboard?.getDashboardStats(),
        cliInterface: this.cliInterface?.getCLIStats(),
        apiServer: this.apiServer?.getAPIStats()
      }
    };
  }

  /**
   * Helper methods
   */
  async fileExists(filePath) {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  async copyDirectory(src, dest) {
    await fs.mkdir(dest, { recursive: true });
    const entries = await fs.readdir(src, { withFileTypes: true });
    
    for (const entry of entries) {
      const srcPath = path.join(src, entry.name);
      const destPath = path.join(dest, entry.name);
      
      if (entry.isDirectory()) {
        await this.copyDirectory(srcPath, destPath);
      } else {
        await fs.copyFile(srcPath, destPath);
      }
    }
  }
}

module.exports = DGMIntegration;

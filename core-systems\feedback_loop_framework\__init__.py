"""
Universal Dual-Purpose Feedback Loop Framework

A comprehensive, domain-agnostic feedback loop system that provides continuous QA
and self-improvement across AI domains. Currently supports Drone AI and TimeStamp AI
with extensible architecture for future domains.

Key Features:
- Modular, pluggable architecture
- Support for 'Partially Correct' feedback type
- Real-time and batch processing
- Adaptive confidence scoring
- Trust score evolution
- Developer-only logging and analytics
- Integration with existing AI orchestration systems

Example Usage:
    from feedback_loop_framework import FeedbackEngine, DroneAIDomain, TimeStampAIDomain

    # Initialize the feedback engine
    engine = FeedbackEngine()

    # Register domain modules
    engine.register_domain('drone_ai', DroneAIDomain())
    engine.register_domain('timestamp_ai', TimeStampAIDomain())

    # Process AI output
    result = engine.process_output(
        domain='drone_ai',
        output=sensor_data,
        context={'mission_id': 'M001', 'timestamp': '2025-01-06T10:00:00Z'}
    )

    print(f"Feedback: {result.feedback_type}")
    print(f"Confidence: {result.confidence_score}")
    print(f"Trust Score: {result.trust_score}")
"""

__version__ = "1.0.0"
__author__ = "AI Orchestration Team"
__email__ = "<EMAIL>"
__description__ = "Universal Dual-Purpose Feedback Loop Framework for AI Systems"

# Core exports
from .core.feedback_engine import FeedbackEngine
from .core.enhanced_feedback_engine import EnhancedFeedbackEngine, create_enhanced_engine
from .core.domain_factory import DomainFactory, domain_factory
from .core.generic_domain import GenericDomain
from .core.feedback_types import (
    FeedbackType, FeedbackEntry, ValidationResult,
    ConfidenceLevel, ValidationSeverity, ConfidenceAdjustment, TrustScoreConfig
)
from .core.interfaces import (
    OutputInterpreter, PatternMatcher, OutputValidator,
    ConfidenceModel, TrustScoreCalculator, MemoryStore, BaseDomain
)

# Domain exports
from .domains.drone_ai import DroneAIDomain, SensorDataInterpreter, SensorLogMatcher, DroneValidator
from .domains.timestamp_ai import TimeStampAIDomain, LLMOutputInterpreter, TimestampMatcher, TimeStampValidator
from .domains.search_rescue import SearchRescueDomain, VisualDetectionInterpreter, ReferenceItemMatcher, SearchRescueValidator, SidePocketManager
from .domains.species_tracking import SpeciesTrackingDomain, SpeciesDetectionInterpreter, EcologicalPatternMatcher, EcologicalSurveyValidator
from .domains.mining_ore import MiningOreDomain, OreDetectionInterpreter, GeologicalPatternMatcher, MiningOperationValidator
from .domains.real_estate_construction import RealEstateConstructionDomain, StructuralDetectionInterpreter, ConstructionProgressMatcher, ProjectManagementValidator

# Component exports
from .components.confidence.adaptive_confidence_model import AdaptiveConfidenceModel
from .components.trust.trust_score_calculator import TrustScoreCalculator
from .components.memory.file_memory_store import FileMemoryStore

# Utility functions
def create_default_engine(config=None):
    """
    Create a feedback engine with default configuration.

    Args:
        config: Optional configuration dictionary

    Returns:
        Configured FeedbackEngine instance
    """
    from .components.confidence.adaptive_confidence_model import AdaptiveConfidenceModel
    from .components.trust.trust_score_calculator import TrustScoreCalculator
    from .components.memory.file_memory_store import FileMemoryStore

    config = config or {}

    # Create components with default settings
    confidence_model = AdaptiveConfidenceModel(config.get('confidence_model', {}))
    trust_calculator = TrustScoreCalculator(config.get('trust_calculator', {}))
    memory_store = FileMemoryStore(config.get('memory_store', {}))

    # Create engine
    engine = FeedbackEngine(
        confidence_model=confidence_model,
        trust_calculator=trust_calculator,
        memory_store=memory_store,
        config=config.get('feedback_engine', {})
    )

    # Register default domains
    if config.get('enable_drone_ai', True):
        drone_domain = DroneAIDomain(config.get('drone_ai', {}))
        engine.register_domain('drone_ai', drone_domain)

    if config.get('enable_timestamp_ai', True):
        timestamp_domain = TimeStampAIDomain(config.get('timestamp_ai', {}))
        engine.register_domain('timestamp_ai', timestamp_domain)

    if config.get('enable_search_rescue', True):
        sar_domain = SearchRescueDomain(config.get('search_rescue', {}))
        engine.register_domain('search_rescue', sar_domain)

    if config.get('enable_species_tracking', True):
        species_domain = SpeciesTrackingDomain(config.get('species_tracking', {}))
        engine.register_domain('species_tracking', species_domain)

    if config.get('enable_mining_ore', True):
        mining_domain = MiningOreDomain(config.get('mining_ore', {}))
        engine.register_domain('mining_ore', mining_domain)

    if config.get('enable_real_estate_construction', True):
        construction_domain = RealEstateConstructionDomain(config.get('real_estate_construction', {}))
        engine.register_domain('real_estate_construction', construction_domain)

    return engine


def quick_start(base_path="./feedback_data"):
    """
    Quick start with minimal configuration.

    Args:
        base_path: Base path for data storage

    Returns:
        Ready-to-use FeedbackEngine instance
    """
    config = {
        'memory_store': {
            'base_path': base_path,
            'compression_enabled': True,
            'retention_days': 30
        }
    }

    return create_default_engine(config)


# Package metadata
__all__ = [
    # Core classes
    'FeedbackEngine',
    'EnhancedFeedbackEngine',
    'DomainFactory',
    'domain_factory',
    'GenericDomain',
    'FeedbackType',
    'FeedbackEntry',
    'ValidationResult',
    'ConfidenceLevel',
    'ValidationSeverity',
    'ConfidenceAdjustment',
    'TrustScoreConfig',

    # Interfaces
    'OutputInterpreter',
    'PatternMatcher',
    'OutputValidator',
    'ConfidenceModel',
    'TrustScoreCalculator',
    'MemoryStore',
    'BaseDomain',

    # Domain implementations
    'DroneAIDomain',
    'SensorDataInterpreter',
    'SensorLogMatcher',
    'DroneValidator',
    'TimeStampAIDomain',
    'LLMOutputInterpreter',
    'TimestampMatcher',
    'TimeStampValidator',
    'SearchRescueDomain',
    'VisualDetectionInterpreter',
    'ReferenceItemMatcher',
    'SearchRescueValidator',
    'SidePocketManager',
    'SpeciesTrackingDomain',
    'SpeciesDetectionInterpreter',
    'EcologicalPatternMatcher',
    'EcologicalSurveyValidator',
    'MiningOreDomain',
    'OreDetectionInterpreter',
    'GeologicalPatternMatcher',
    'MiningOperationValidator',
    'RealEstateConstructionDomain',
    'StructuralDetectionInterpreter',
    'ConstructionProgressMatcher',
    'ProjectManagementValidator',

    # Component implementations
    'AdaptiveConfidenceModel',
    'TrustScoreCalculator',
    'FileMemoryStore',

    # Utility functions
    'create_default_engine',
    'create_enhanced_engine',
    'quick_start'
]

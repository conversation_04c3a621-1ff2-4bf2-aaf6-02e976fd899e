"""
Timestamp Matcher for TimeStamp AI

Pattern matching component that analyzes timestamp data, environmental impact
calculations, and LLM outputs against expected patterns and accuracy thresholds.
"""

import re
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import hashlib


class TimestampMatcher:
    """
    Matches timestamp AI outputs against expected patterns and accuracy criteria.
    
    Identifies issues with timestamp accuracy, environmental impact calculations,
    hash verification, and LLM response quality.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.patterns = {}
        self.thresholds = self._initialize_default_thresholds()
        
    def _initialize_default_thresholds(self) -> Dict[str, Any]:
        """Initialize default thresholds for timestamp AI validation."""
        return {
            'timestamp_accuracy': {
                'max_time_drift': 300.0,        # 5 minutes max drift
                'future_tolerance': 60.0,       # 1 minute future tolerance
                'past_tolerance': 86400.0,      # 24 hours past tolerance
                'precision_threshold': 1.0      # 1 second precision
            },
            'environmental_impact': {
                'water_usage_max': 1000.0,      # ml per query
                'electricity_max': 100.0,       # Wh per query
                'carbon_footprint_max': 50.0,   # g CO2 per query
                'efficiency_threshold': 0.8     # 80% efficiency minimum
            },
            'hash_verification': {
                'hash_length': 64,              # SHA-256 length
                'signature_min_length': 128,    # Minimum signature length
                'verification_timeout': 30.0    # 30 seconds max verification time
            },
            'llm_response': {
                'min_confidence': 0.7,          # 70% minimum confidence
                'max_response_time': 30.0,      # 30 seconds max response time
                'min_content_length': 10,       # 10 characters minimum
                'max_token_usage': 4000         # 4000 tokens maximum
            }
        }
    
    def match(self, interpreted_output: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Match interpreted timestamp AI output against expected patterns.
        
        Args:
            interpreted_output: Normalized output from interpreter
            context: Additional context including expected values, model info
            
        Returns:
            Match results with confidence scores and identified patterns
        """
        try:
            match_results = {
                'overall_confidence': 1.0,
                'pattern_matches': [],
                'anomalies': [],
                'warnings': [],
                'metadata': {
                    'match_timestamp': datetime.utcnow().isoformat(),
                    'matcher_version': '1.0.0'
                }
            }
            
            # Check for data quality issues
            self._check_data_quality(interpreted_output, match_results)
            
            # Check output-specific patterns
            output_type = context.get('output_type', 'unknown')
            
            if output_type == 'timestamp_response':
                self._check_timestamp_patterns(interpreted_output, context, match_results)
            
            if output_type == 'impact_calculation':
                self._check_environmental_patterns(interpreted_output, context, match_results)
            
            if output_type == 'hash_verification':
                self._check_hash_patterns(interpreted_output, context, match_results)
            
            if output_type in ['llm_conversation', 'llm_response']:
                self._check_llm_patterns(interpreted_output, context, match_results)
            
            # Check cross-cutting concerns
            self._check_performance_patterns(interpreted_output, context, match_results)
            
            # Calculate overall confidence
            self._calculate_overall_confidence(match_results)
            
            return match_results
            
        except Exception as e:
            self.logger.error(f"Error in timestamp pattern matching: {str(e)}")
            return {
                'overall_confidence': 0.0,
                'pattern_matches': [],
                'anomalies': [{'type': 'matcher_error', 'message': str(e)}],
                'warnings': [],
                'metadata': {'error': True, 'error_message': str(e)}
            }
    
    def _check_data_quality(self, data: Dict[str, Any], results: Dict[str, Any]) -> None:
        """Check basic data quality issues."""
        # Check for interpretation errors
        if data.get('error', False):
            results['anomalies'].append({
                'type': 'data_error',
                'severity': 'high',
                'message': data.get('error_message', 'Unknown data error'),
                'confidence_impact': -0.5
            })
            return
        
        # Check for missing critical fields based on output type
        metadata = data.get('_metadata', {})
        output_type = metadata.get('output_type', 'unknown')
        
        critical_fields = {
            'timestamp_response': ['timestamp'],
            'impact_calculation': ['water_usage', 'electricity_usage'],
            'hash_verification': ['hash'],
            'llm_response': ['content']
        }
        
        if output_type in critical_fields:
            for field in critical_fields[output_type]:
                if field not in data and f'extracted_{field}' not in data:
                    results['warnings'].append({
                        'type': 'missing_critical_field',
                        'field': field,
                        'message': f'Missing critical field: {field}',
                        'confidence_impact': -0.2
                    })
    
    def _check_timestamp_patterns(self, data: Dict[str, Any], context: Dict[str, Any], results: Dict[str, Any]) -> None:
        """Check timestamp-specific patterns and accuracy."""
        ts_thresholds = self.thresholds['timestamp_accuracy']
        
        # Get timestamp value
        timestamp = data.get('timestamp') or data.get('extracted_timestamp')
        if not timestamp:
            results['anomalies'].append({
                'type': 'missing_timestamp',
                'severity': 'high',
                'message': 'No timestamp found in response',
                'confidence_impact': -0.4
            })
            return
        
        # Check timestamp validity
        if not data.get('timestamp_valid', True):
            results['anomalies'].append({
                'type': 'invalid_timestamp_format',
                'severity': 'high',
                'message': data.get('timestamp_error', 'Invalid timestamp format'),
                'confidence_impact': -0.3
            })
            return
        
        # Check timestamp accuracy against current time
        try:
            ts_dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            current_dt = datetime.now(ts_dt.tzinfo)
            time_diff = abs((ts_dt - current_dt).total_seconds())
            
            if time_diff > ts_thresholds['max_time_drift']:
                results['warnings'].append({
                    'type': 'timestamp_drift',
                    'time_difference': time_diff,
                    'threshold': ts_thresholds['max_time_drift'],
                    'message': f'Timestamp drift: {time_diff:.1f}s',
                    'confidence_impact': -0.2
                })
            else:
                results['pattern_matches'].append({
                    'type': 'accurate_timestamp',
                    'confidence': 0.9,
                    'time_difference': time_diff,
                    'message': f'Timestamp accurate within {time_diff:.1f}s'
                })
            
            # Check for future timestamps
            if ts_dt > current_dt + timedelta(seconds=ts_thresholds['future_tolerance']):
                results['warnings'].append({
                    'type': 'future_timestamp',
                    'message': 'Timestamp is in the future',
                    'confidence_impact': -0.15
                })
            
        except ValueError as e:
            results['anomalies'].append({
                'type': 'timestamp_parsing_error',
                'severity': 'medium',
                'message': f'Could not parse timestamp: {str(e)}',
                'confidence_impact': -0.25
            })
        
        # Check hash verification if present
        hash_value = data.get('hash') or data.get('extracted_hash')
        if hash_value:
            if data.get('hash_valid', True):
                results['pattern_matches'].append({
                    'type': 'valid_hash_format',
                    'confidence': 0.8,
                    'message': 'Hash format is valid'
                })
            else:
                results['warnings'].append({
                    'type': 'invalid_hash_format',
                    'message': data.get('hash_error', 'Invalid hash format'),
                    'confidence_impact': -0.2
                })
    
    def _check_environmental_patterns(self, data: Dict[str, Any], context: Dict[str, Any], results: Dict[str, Any]) -> None:
        """Check environmental impact calculation patterns."""
        env_thresholds = self.thresholds['environmental_impact']
        
        # Check water usage
        water_usage = data.get('water_usage')
        if water_usage is not None:
            if water_usage > env_thresholds['water_usage_max']:
                results['warnings'].append({
                    'type': 'high_water_usage',
                    'value': water_usage,
                    'threshold': env_thresholds['water_usage_max'],
                    'message': f'High water usage: {water_usage} ml',
                    'confidence_impact': -0.1
                })
            else:
                results['pattern_matches'].append({
                    'type': 'acceptable_water_usage',
                    'confidence': 0.7,
                    'value': water_usage,
                    'message': f'Water usage within limits: {water_usage} ml'
                })
        
        # Check electricity usage
        electricity = data.get('electricity_usage')
        if electricity is not None:
            if electricity > env_thresholds['electricity_max']:
                results['warnings'].append({
                    'type': 'high_electricity_usage',
                    'value': electricity,
                    'threshold': env_thresholds['electricity_max'],
                    'message': f'High electricity usage: {electricity} Wh',
                    'confidence_impact': -0.1
                })
        
        # Check carbon footprint
        carbon = data.get('carbon_footprint')
        if carbon is not None:
            if carbon > env_thresholds['carbon_footprint_max']:
                results['warnings'].append({
                    'type': 'high_carbon_footprint',
                    'value': carbon,
                    'threshold': env_thresholds['carbon_footprint_max'],
                    'message': f'High carbon footprint: {carbon} g CO2',
                    'confidence_impact': -0.15
                })
        
        # Check for missing impact data
        impact_fields = ['water_usage', 'electricity_usage', 'carbon_footprint']
        missing_fields = [f for f in impact_fields if f not in data]
        if missing_fields:
            results['warnings'].append({
                'type': 'incomplete_impact_data',
                'missing_fields': missing_fields,
                'message': f'Missing impact data: {", ".join(missing_fields)}',
                'confidence_impact': -0.1
            })
    
    def _check_hash_patterns(self, data: Dict[str, Any], context: Dict[str, Any], results: Dict[str, Any]) -> None:
        """Check hash verification patterns."""
        hash_thresholds = self.thresholds['hash_verification']
        
        # Check hash presence and format
        hash_value = data.get('hash') or data.get('extracted_hash')
        if not hash_value:
            results['anomalies'].append({
                'type': 'missing_hash',
                'severity': 'high',
                'message': 'No hash found for verification',
                'confidence_impact': -0.4
            })
            return
        
        # Validate hash format
        if len(hash_value) != hash_thresholds['hash_length']:
            results['warnings'].append({
                'type': 'incorrect_hash_length',
                'expected': hash_thresholds['hash_length'],
                'actual': len(hash_value),
                'message': f'Hash length mismatch: expected {hash_thresholds["hash_length"]}, got {len(hash_value)}',
                'confidence_impact': -0.2
            })
        
        # Check signature if present
        signature = data.get('signature') or data.get('extracted_signature')
        if signature:
            if len(signature) < hash_thresholds['signature_min_length']:
                results['warnings'].append({
                    'type': 'short_signature',
                    'length': len(signature),
                    'min_length': hash_thresholds['signature_min_length'],
                    'message': f'Signature too short: {len(signature)} characters',
                    'confidence_impact': -0.15
                })
        
        # Check verification status
        verification_status = data.get('verification_status')
        if verification_status is not None:
            if verification_status:
                results['pattern_matches'].append({
                    'type': 'successful_verification',
                    'confidence': 0.9,
                    'message': 'Hash verification successful'
                })
            else:
                results['anomalies'].append({
                    'type': 'verification_failed',
                    'severity': 'high',
                    'message': 'Hash verification failed',
                    'confidence_impact': -0.5
                })
    
    def _check_llm_patterns(self, data: Dict[str, Any], context: Dict[str, Any], results: Dict[str, Any]) -> None:
        """Check LLM response patterns."""
        llm_thresholds = self.thresholds['llm_response']
        
        # Check content quality
        content = data.get('content') or data.get('extra_text_content', '')
        if content:
            if len(content) < llm_thresholds['min_content_length']:
                results['warnings'].append({
                    'type': 'short_response',
                    'length': len(content),
                    'min_length': llm_thresholds['min_content_length'],
                    'message': f'Response too short: {len(content)} characters',
                    'confidence_impact': -0.1
                })
        
        # Check confidence score
        confidence = data.get('confidence')
        if confidence is not None:
            if confidence < llm_thresholds['min_confidence']:
                results['warnings'].append({
                    'type': 'low_confidence',
                    'value': confidence,
                    'threshold': llm_thresholds['min_confidence'],
                    'message': f'Low confidence score: {confidence}',
                    'confidence_impact': -0.2
                })
        
        # Check token usage
        tokens = data.get('tokens_used') or data.get('token_count')
        if tokens is not None:
            if tokens > llm_thresholds['max_token_usage']:
                results['warnings'].append({
                    'type': 'high_token_usage',
                    'value': tokens,
                    'threshold': llm_thresholds['max_token_usage'],
                    'message': f'High token usage: {tokens}',
                    'confidence_impact': -0.1
                })
    
    def _check_performance_patterns(self, data: Dict[str, Any], context: Dict[str, Any], results: Dict[str, Any]) -> None:
        """Check performance-related patterns."""
        # Check processing time
        processing_time = data.get('processing_time')
        if processing_time is not None:
            max_time = self.thresholds['llm_response']['max_response_time']
            if processing_time > max_time:
                results['warnings'].append({
                    'type': 'slow_response',
                    'value': processing_time,
                    'threshold': max_time,
                    'message': f'Slow response time: {processing_time}s',
                    'confidence_impact': -0.1
                })
    
    def _calculate_overall_confidence(self, results: Dict[str, Any]) -> None:
        """Calculate overall confidence based on all findings."""
        confidence = 1.0
        
        # Apply confidence impacts from anomalies and warnings
        for anomaly in results['anomalies']:
            impact = anomaly.get('confidence_impact', -0.3)
            confidence += impact
        
        for warning in results['warnings']:
            impact = warning.get('confidence_impact', -0.1)
            confidence += impact
        
        # Boost confidence for positive pattern matches
        for match in results['pattern_matches']:
            boost = match.get('confidence', 0.1) * 0.1
            confidence += boost
        
        # Ensure confidence stays within bounds
        results['overall_confidence'] = max(0.0, min(1.0, confidence))
    
    def add_pattern(self, pattern_id: str, pattern_data: Dict[str, Any]) -> None:
        """Add a new pattern to the matcher."""
        self.patterns[pattern_id] = pattern_data
        self.logger.info(f"Added pattern: {pattern_id}")
    
    def remove_pattern(self, pattern_id: str) -> bool:
        """Remove a pattern from the matcher."""
        if pattern_id in self.patterns:
            del self.patterns[pattern_id]
            self.logger.info(f"Removed pattern: {pattern_id}")
            return True
        return False
    
    def update_thresholds(self, category: str, thresholds: Dict[str, Any]) -> None:
        """Update thresholds for a specific category."""
        if category in self.thresholds:
            self.thresholds[category].update(thresholds)
        else:
            self.thresholds[category] = thresholds
        self.logger.info(f"Updated thresholds for category: {category}")

#!/usr/bin/env node

import ThreadMergingOrchestrator from '../meta-orchestrator/thread-merging-orchestrator/src/orchestrator.js';

async function testRunner() {
  console.log("🚀 Starting test orchestration...");
  const orchestrator = new ThreadMergingOrchestrator();
  try {
    const result = await orchestrator.orchestrate("test query", { maxThreads: 5 });
    console.log('✅ Orchestration result:\n', JSON.stringify(result, null, 2));
  } catch (err) {
    console.error('❌ Orchestration failed:', err);
    process.exit(1);
  }
}

// Only run if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  testRunner();
}

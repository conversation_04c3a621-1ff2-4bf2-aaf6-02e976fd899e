"""
Ecological Pattern Matcher for Species Tracking

Pattern matching component that analyzes species detection patterns against
ecological models, migration patterns, and habitat preferences for
comprehensive biodiversity assessment.
"""

import logging
import math
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from collections import defaultdict


class EcologicalPatternMatcher:
    """
    Matches species detection patterns against ecological models and habitat preferences.

    Analyzes detection patterns, validates against ecological knowledge,
    and identifies potential anomalies or significant findings.
    """

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.patterns = {}
        self.ecological_models = self._initialize_ecological_models()
        self.habitat_preferences = self._initialize_habitat_preferences()

    def _initialize_ecological_models(self) -> Dict[str, Any]:
        """Initialize ecological models and patterns."""
        return {
            'seasonal_patterns': {
                'carolina_chickadee': {
                    'spring': {
                        'activity_level': 0.9,
                        'breeding_behavior': True,
                        'territory_establishment': True,
                        'expected_group_size': [2, 4]  # Pairs with young
                    },
                    'summer': {
                        'activity_level': 0.8,
                        'breeding_behavior': True,
                        'foraging_intensity': 'high',
                        'expected_group_size': [3, 6]  # Family groups
                    },
                    'fall': {
                        'activity_level': 0.7,
                        'breeding_behavior': False,
                        'flock_formation': True,
                        'expected_group_size': [4, 12]  # Mixed flocks
                    },
                    'winter': {
                        'activity_level': 0.6,
                        'breeding_behavior': False,
                        'flock_formation': True,
                        'expected_group_size': [6, 20]  # Large winter flocks
                    }
                }
            },
            'daily_activity_patterns': {
                'chickadee': {
                    'dawn': {'activity': 0.9, 'behavior': 'foraging'},
                    'morning': {'activity': 0.8, 'behavior': 'territorial'},
                    'midday': {'activity': 0.4, 'behavior': 'resting'},
                    'afternoon': {'activity': 0.6, 'behavior': 'foraging'},
                    'dusk': {'activity': 0.8, 'behavior': 'roosting_prep'},
                    'night': {'activity': 0.1, 'behavior': 'roosting'}
                }
            },
            'habitat_associations': {
                'carolina_chickadee': {
                    'preferred_habitats': ['deciduous_forest', 'mixed_forest', 'suburban'],
                    'vegetation_layers': ['canopy', 'understory'],
                    'foraging_zones': ['bark', 'leaves', 'small_branches'],
                    'nesting_requirements': ['tree_cavities', 'nest_boxes'],
                    'water_dependency': 'moderate'
                }
            },
            'population_dynamics': {
                'chickadee': {
                    'territory_size_hectares': [0.5, 2.0],
                    'breeding_density_per_hectare': [0.5, 2.0],
                    'flock_size_range': [4, 20],
                    'dispersal_distance_km': [1, 5]
                }
            }
        }

    def _initialize_habitat_preferences(self) -> Dict[str, Any]:
        """Initialize habitat preference models."""
        return {
            'forest_structure': {
                'canopy_cover': {
                    'chickadee': {'min': 0.3, 'optimal': 0.7, 'max': 0.9}
                },
                'tree_diversity': {
                    'chickadee': {'min': 2, 'optimal': 5, 'max': 10}
                },
                'understory_density': {
                    'chickadee': {'min': 0.2, 'optimal': 0.5, 'max': 0.8}
                }
            },
            'edge_effects': {
                'forest_edge_tolerance': {
                    'chickadee': 0.8  # High tolerance for edge habitats
                },
                'urban_tolerance': {
                    'chickadee': 0.7  # Moderate urban tolerance
                }
            },
            'resource_requirements': {
                'food_sources': {
                    'chickadee': ['insects', 'seeds', 'berries', 'suet']
                },
                'nesting_sites': {
                    'chickadee': ['tree_cavities', 'nest_boxes', 'dense_shrubs']
                },
                'water_sources': {
                    'chickadee': {'distance_tolerance_m': 500}
                }
            }
        }

    def match(self, interpreted_output: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Match interpreted species detections against ecological patterns.

        Args:
            interpreted_output: Normalized species detection data from interpreter
            context: Survey context including habitat, season, target species

        Returns:
            Match results with ecological pattern analysis and anomaly detection
        """
        try:
            match_results = {
                'overall_ecological_fit': 0.0,
                'pattern_matches': [],
                'ecological_anomalies': [],
                'habitat_assessment': {},
                'temporal_analysis': {},
                'population_indicators': {},
                'biodiversity_assessment': {},
                'metadata': {
                    'match_timestamp': datetime.utcnow().isoformat(),
                    'matcher_version': '1.0.0',
                    'survey_id': context.get('survey_id', 'unknown')
                }
            }

            # Extract classifications and environmental data
            classifications = interpreted_output.get('classifications', [])
            environmental_data = interpreted_output.get('environmental_data', {})
            biodiversity_metrics = interpreted_output.get('biodiversity_metrics', {})

            # Perform ecological pattern matching
            self._match_seasonal_patterns(classifications, environmental_data, context, match_results)
            self._match_daily_activity_patterns(classifications, environmental_data, context, match_results)
            self._assess_habitat_suitability(classifications, environmental_data, context, match_results)
            self._analyze_population_indicators(classifications, biodiversity_metrics, context, match_results)
            self._detect_ecological_anomalies(classifications, environmental_data, context, match_results)
            self._assess_biodiversity_patterns(biodiversity_metrics, context, match_results)

            # Calculate overall ecological fit
            self._calculate_overall_ecological_fit(match_results)

            return match_results

        except Exception as e:
            self.logger.error(f"Error in ecological pattern matching: {str(e)}")
            return {
                'overall_ecological_fit': 0.0,
                'pattern_matches': [],
                'ecological_anomalies': [{'type': 'matcher_error', 'message': str(e)}],
                'metadata': {'error': True, 'error_message': str(e)}
            }

    def _match_seasonal_patterns(self, classifications: List[Dict[str, Any]],
                                environmental_data: Dict[str, Any],
                                context: Dict[str, Any],
                                match_results: Dict[str, Any]) -> None:
        """Match detections against seasonal behavior patterns."""
        season = environmental_data.get('temporal_info', {}).get('season', 'unknown')
        target_species = context.get('target_species', 'carolina_chickadee')

        if season == 'unknown':
            return

        # Get seasonal model for target species
        seasonal_model = self.ecological_models['seasonal_patterns'].get(target_species, {}).get(season, {})

        if not seasonal_model:
            return

        # Analyze group sizes
        group_sizes = []
        target_detections = [c for c in classifications if c.get('is_target_species', False)]

        for detection in target_detections:
            group_size = detection.get('features', {}).get('group_size', 1)
            group_sizes.append(group_size)

        if group_sizes:
            avg_group_size = sum(group_sizes) / len(group_sizes)
            expected_range = seasonal_model.get('expected_group_size', [1, 1])

            # Check if group size matches seasonal expectations
            if expected_range[0] <= avg_group_size <= expected_range[1]:
                match_results['pattern_matches'].append({
                    'type': 'seasonal_group_size_match',
                    'season': season,
                    'observed_group_size': avg_group_size,
                    'expected_range': expected_range,
                    'confidence': 0.8,
                    'message': f'Group size matches {season} expectations'
                })
            else:
                match_results['ecological_anomalies'].append({
                    'type': 'unusual_group_size',
                    'season': season,
                    'observed_group_size': avg_group_size,
                    'expected_range': expected_range,
                    'severity': 'medium',
                    'message': f'Group size unusual for {season}'
                })

        # Check breeding behavior indicators
        if seasonal_model.get('breeding_behavior', False):
            breeding_indicators = sum(1 for d in target_detections
                                    if 'nesting' in d.get('behavioral_indicators', []))
            if breeding_indicators > 0:
                match_results['pattern_matches'].append({
                    'type': 'breeding_behavior_match',
                    'season': season,
                    'breeding_indicators': breeding_indicators,
                    'confidence': 0.9,
                    'message': f'Breeding behavior observed during {season}'
                })

    def _match_daily_activity_patterns(self, classifications: List[Dict[str, Any]],
                                     environmental_data: Dict[str, Any],
                                     context: Dict[str, Any],
                                     match_results: Dict[str, Any]) -> None:
        """Match detections against daily activity patterns."""
        time_of_day = environmental_data.get('temporal_info', {}).get('time_of_day', 'unknown')

        if time_of_day == 'unknown':
            return

        # Get activity model for chickadees
        activity_model = self.ecological_models['daily_activity_patterns'].get('chickadee', {}).get(time_of_day, {})

        if not activity_model:
            return

        # Count target species detections
        target_detections = [c for c in classifications if c.get('is_target_species', False)]
        detection_count = len(target_detections)

        expected_activity = activity_model.get('activity', 0.5)

        # Assess if detection count matches expected activity level
        # This is a simplified assessment - in reality would consider survey effort
        if detection_count > 0:
            if expected_activity > 0.7:  # High activity period
                match_results['pattern_matches'].append({
                    'type': 'high_activity_period_match',
                    'time_of_day': time_of_day,
                    'detection_count': detection_count,
                    'expected_activity': expected_activity,
                    'confidence': 0.7,
                    'message': f'Detections during high activity period ({time_of_day})'
                })
            elif expected_activity < 0.3:  # Low activity period
                match_results['ecological_anomalies'].append({
                    'type': 'unexpected_activity',
                    'time_of_day': time_of_day,
                    'detection_count': detection_count,
                    'expected_activity': expected_activity,
                    'severity': 'low',
                    'message': f'Unexpected activity during low activity period ({time_of_day})'
                })

        # Store temporal analysis
        match_results['temporal_analysis'] = {
            'time_of_day': time_of_day,
            'expected_activity_level': expected_activity,
            'observed_detections': detection_count,
            'activity_match': abs(expected_activity - min(detection_count / 5.0, 1.0)) < 0.3
        }

    def _assess_habitat_suitability(self, classifications: List[Dict[str, Any]],
                                  environmental_data: Dict[str, Any],
                                  context: Dict[str, Any],
                                  match_results: Dict[str, Any]) -> None:
        """Assess habitat suitability for detected species."""
        habitat_info = environmental_data.get('habitat_info', {})
        habitat_type = habitat_info.get('habitat_type', 'unknown')
        target_species = context.get('target_species', 'carolina_chickadee')

        # Get habitat associations for target species
        habitat_model = self.ecological_models['habitat_associations'].get(target_species, {})
        preferred_habitats = habitat_model.get('preferred_habitats', [])

        # Assess habitat match
        habitat_suitability = 0.0
        if habitat_type in preferred_habitats:
            habitat_suitability = 1.0
        elif any(pref in habitat_type for pref in preferred_habitats):
            habitat_suitability = 0.7
        else:
            habitat_suitability = 0.3

        # Check vegetation structure if available
        vegetation_density = habitat_info.get('vegetation_density', 'unknown')
        if vegetation_density != 'unknown':
            # Assess vegetation suitability (simplified)
            if vegetation_density in ['medium', 'dense']:
                habitat_suitability *= 1.1  # Boost for good vegetation
            elif vegetation_density == 'sparse':
                habitat_suitability *= 0.8  # Reduce for poor vegetation

        # Water proximity assessment
        water_proximity = habitat_info.get('water_proximity_m', 0)
        water_requirements = self.habitat_preferences['resource_requirements']['water_sources']['chickadee']
        water_tolerance = water_requirements.get('distance_tolerance_m', 500)

        water_suitability = 1.0 if water_proximity <= water_tolerance else 0.7

        # Overall habitat assessment
        overall_suitability = habitat_suitability * water_suitability

        match_results['habitat_assessment'] = {
            'habitat_type': habitat_type,
            'habitat_suitability': habitat_suitability,
            'water_suitability': water_suitability,
            'overall_suitability': overall_suitability,
            'preferred_habitats': preferred_habitats,
            'habitat_match': habitat_type in preferred_habitats
        }

        # Add pattern match if habitat is suitable
        if overall_suitability > 0.7:
            match_results['pattern_matches'].append({
                'type': 'suitable_habitat_match',
                'habitat_type': habitat_type,
                'suitability_score': overall_suitability,
                'confidence': 0.8,
                'message': f'Suitable habitat for {target_species}'
            })

    def _analyze_population_indicators(self, classifications: List[Dict[str, Any]],
                                     biodiversity_metrics: Dict[str, Any],
                                     context: Dict[str, Any],
                                     match_results: Dict[str, Any]) -> None:
        """Analyze population indicators and density estimates."""
        target_species = context.get('target_species', 'carolina_chickadee')
        survey_area = context.get('survey_area_hectares', 1.0)

        # Count target species detections
        target_detections = [c for c in classifications if c.get('is_target_species', False)]
        detection_count = len(target_detections)

        # Estimate density (simplified - assumes perfect detection)
        estimated_density = detection_count / survey_area

        # Get expected density range
        population_model = self.ecological_models['population_dynamics'].get('chickadee', {})
        expected_density_range = population_model.get('breeding_density_per_hectare', [0.5, 2.0])

        # Assess density
        density_assessment = 'normal'
        if estimated_density < expected_density_range[0]:
            density_assessment = 'low'
        elif estimated_density > expected_density_range[1]:
            density_assessment = 'high'

        # Species richness assessment
        species_richness = biodiversity_metrics.get('species_richness', 0)
        richness_assessment = 'low'
        if species_richness >= 5:
            richness_assessment = 'high'
        elif species_richness >= 3:
            richness_assessment = 'moderate'

        match_results['population_indicators'] = {
            'target_species_count': detection_count,
            'estimated_density_per_hectare': estimated_density,
            'expected_density_range': expected_density_range,
            'density_assessment': density_assessment,
            'species_richness': species_richness,
            'richness_assessment': richness_assessment,
            'survey_area_hectares': survey_area
        }

        # Add pattern matches based on population indicators
        if density_assessment == 'normal':
            match_results['pattern_matches'].append({
                'type': 'normal_population_density',
                'estimated_density': estimated_density,
                'expected_range': expected_density_range,
                'confidence': 0.7,
                'message': 'Population density within expected range'
            })
        elif density_assessment in ['low', 'high']:
            match_results['ecological_anomalies'].append({
                'type': f'{density_assessment}_population_density',
                'estimated_density': estimated_density,
                'expected_range': expected_density_range,
                'severity': 'medium',
                'message': f'Population density appears {density_assessment}'
            })

    def _detect_ecological_anomalies(self, classifications: List[Dict[str, Any]],
                                   environmental_data: Dict[str, Any],
                                   context: Dict[str, Any],
                                   match_results: Dict[str, Any]) -> None:
        """Detect ecological anomalies and unusual patterns."""
        # Check for unusual species combinations
        detected_species = [c['species_classification'].get('species', 'unknown') for c in classifications]
        species_set = set(detected_species)

        # Check for rare species
        rare_species = ['rare_warbler', 'uncommon_finch']  # Example rare species
        found_rare = species_set.intersection(rare_species)
        if found_rare:
            match_results['ecological_anomalies'].append({
                'type': 'rare_species_detection',
                'species': list(found_rare),
                'severity': 'high',
                'message': f'Rare species detected: {", ".join(found_rare)}'
            })

        # Check for out-of-season species
        season = environmental_data.get('temporal_info', {}).get('season', 'unknown')
        if season == 'winter':
            # Check for species that should have migrated
            migrant_species = ['warbler', 'flycatcher', 'vireo']
            found_migrants = [s for s in detected_species if any(m in s for m in migrant_species)]
            if found_migrants:
                match_results['ecological_anomalies'].append({
                    'type': 'out_of_season_species',
                    'species': found_migrants,
                    'season': season,
                    'severity': 'medium',
                    'message': f'Potential migrant species in {season}: {", ".join(found_migrants)}'
                })

        # Check for unusual habitat use
        habitat_type = environmental_data.get('habitat_info', {}).get('habitat_type', 'unknown')
        for classification in classifications:
            species = classification['species_classification'].get('species', 'unknown')
            habitat_match = classification.get('habitat_match', 0.5)

            if habitat_match < 0.4:  # Poor habitat match
                match_results['ecological_anomalies'].append({
                    'type': 'unusual_habitat_use',
                    'species': species,
                    'habitat_type': habitat_type,
                    'habitat_match_score': habitat_match,
                    'severity': 'low',
                    'message': f'{species} in unusual habitat: {habitat_type}'
                })

    def _assess_biodiversity_patterns(self, biodiversity_metrics: Dict[str, Any],
                                    context: Dict[str, Any],
                                    match_results: Dict[str, Any]) -> None:
        """Assess biodiversity patterns and ecosystem health indicators."""
        species_richness = biodiversity_metrics.get('species_richness', 0)
        shannon_index = biodiversity_metrics.get('shannon_diversity_index', 0.0)
        dominant_species = biodiversity_metrics.get('dominant_species')

        # Assess ecosystem health based on diversity
        ecosystem_health = 'poor'
        if species_richness >= 8 and shannon_index > 0.8:
            ecosystem_health = 'excellent'
        elif species_richness >= 5 and shannon_index > 0.6:
            ecosystem_health = 'good'
        elif species_richness >= 3 and shannon_index > 0.4:
            ecosystem_health = 'fair'

        # Check for dominance patterns
        dominance_concern = False
        if dominant_species and biodiversity_metrics.get('total_individuals', 0) > 0:
            dominant_count = biodiversity_metrics.get('species_abundance', {}).get(dominant_species, 0)
            total_individuals = biodiversity_metrics.get('total_individuals', 1)
            dominance_ratio = dominant_count / total_individuals

            if dominance_ratio > 0.7:  # One species dominates
                dominance_concern = True

        match_results['biodiversity_assessment'] = {
            'species_richness': species_richness,
            'shannon_diversity_index': shannon_index,
            'ecosystem_health': ecosystem_health,
            'dominant_species': dominant_species,
            'dominance_concern': dominance_concern,
            'diversity_level': 'high' if shannon_index > 0.7 else 'moderate' if shannon_index > 0.4 else 'low'
        }

        # Add pattern matches for biodiversity
        if ecosystem_health in ['good', 'excellent']:
            match_results['pattern_matches'].append({
                'type': 'healthy_ecosystem_indicators',
                'ecosystem_health': ecosystem_health,
                'species_richness': species_richness,
                'diversity_index': shannon_index,
                'confidence': 0.8,
                'message': f'Ecosystem shows {ecosystem_health} health indicators'
            })

        if dominance_concern:
            dominance_ratio = dominant_count / total_individuals
            match_results['ecological_anomalies'].append({
                'type': 'species_dominance_concern',
                'dominant_species': dominant_species,
                'dominance_ratio': dominance_ratio,
                'severity': 'medium',
                'message': f'High dominance by {dominant_species}'
            })

    def _calculate_overall_ecological_fit(self, match_results: Dict[str, Any]) -> None:
        """Calculate overall ecological fit score."""
        fit_score = 0.0

        # Weight pattern matches
        for pattern_match in match_results['pattern_matches']:
            confidence = pattern_match.get('confidence', 0.5)
            fit_score += confidence * 0.2  # Each match contributes up to 0.2

        # Weight habitat assessment
        habitat_suitability = match_results.get('habitat_assessment', {}).get('overall_suitability', 0.5)
        fit_score += habitat_suitability * 0.3

        # Weight biodiversity assessment
        biodiversity = match_results.get('biodiversity_assessment', {})
        diversity_index = biodiversity.get('shannon_diversity_index', 0.0)
        fit_score += min(diversity_index, 1.0) * 0.2

        # Penalize for anomalies
        anomaly_penalty = len(match_results['ecological_anomalies']) * 0.1
        fit_score = max(0.0, fit_score - anomaly_penalty)

        # Normalize to 0-1 range
        match_results['overall_ecological_fit'] = min(1.0, fit_score)

    def add_pattern(self, pattern_id: str, pattern_data: Dict[str, Any]) -> None:
        """Add a new ecological pattern."""
        self.patterns[pattern_id] = pattern_data
        self.logger.info(f"Added ecological pattern: {pattern_id}")

    def update_ecological_model(self, species: str, model_data: Dict[str, Any]) -> None:
        """Update ecological model for a species."""
        if species not in self.ecological_models:
            self.ecological_models[species] = {}
        self.ecological_models[species].update(model_data)
        self.logger.info(f"Updated ecological model for species: {species}")

    def get_ecological_models(self) -> Dict[str, Any]:
        """Get current ecological models."""
        return self.ecological_models.copy()

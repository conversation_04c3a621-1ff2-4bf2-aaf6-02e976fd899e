# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/augment_orchestration"
DB_MAX_CONNECTIONS=10
DB_SSL=false

# Server Configuration
PORT=3001
HOST=localhost
NODE_ENV=development

# CORS Configuration
CORS_ORIGINS="http://localhost:3000,http://localhost:5173"

# JWT Configuration
JWT_SECRET="your-super-secret-jwt-key-change-in-production"
JWT_EXPIRES_IN="24h"

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX=100

# Logging
LOG_LEVEL=debug

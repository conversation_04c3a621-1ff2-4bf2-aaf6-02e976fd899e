"""
Agent Registry and Role Management System

Manages all branded AI coding assistants, their roles, capabilities,
health status, and availability for orchestration tasks.

Key Features:
- Agent registration and discovery
- Role-based capability mapping
- Health monitoring and status tracking
- Load balancing and availability management
- Performance tracking and optimization
"""

import logging
from typing import Dict, Any, List, Optional, Set
from datetime import datetime, timedelta
from enum import Enum
from dataclasses import dataclass, field
import uuid


class AgentRole(Enum):
    """Roles that AI agents can fulfill in the orchestration system."""
    ANALYZER = "analyzer"          # Code analysis and review
    GENERATOR = "generator"        # Code generation and implementation
    COMPLETER = "completer"        # Code completion and suggestions
    VALIDATOR = "validator"        # Testing, verification, and validation
    DOCUMENTER = "documenter"      # Documentation generation
    OPTIMIZER = "optimizer"        # Performance optimization and refactoring
    DEBUGGER = "debugger"         # Debugging and error resolution
    REVIEWER = "reviewer"         # Code review and quality assessment


class AgentStatus(Enum):
    """Agent availability and health status."""
    AVAILABLE = "available"
    BUSY = "busy"
    OFFLINE = "offline"
    ERROR = "error"
    MAINTENANCE = "maintenance"
    RATE_LIMITED = "rate_limited"


class AgentCapability(Enum):
    """Specific capabilities that agents can provide."""
    CODE_GENERATION = "code_generation"
    CODE_COMPLETION = "code_completion"
    CODE_ANALYSIS = "code_analysis"
    CODE_REVIEW = "code_review"
    DOCUMENTATION = "documentation"
    TESTING = "testing"
    DEBUGGING = "debugging"
    REFACTORING = "refactoring"
    OPTIMIZATION = "optimization"
    SECURITY_ANALYSIS = "security_analysis"
    PERFORMANCE_ANALYSIS = "performance_analysis"


@dataclass
class AgentInfo:
    """Information about a registered AI agent."""
    agent_id: str
    vendor: str
    name: str
    version: str
    api_endpoint: str
    supported_roles: List[AgentRole]
    capabilities: List[AgentCapability]
    programming_languages: List[str]
    frameworks: List[str]
    status: AgentStatus = AgentStatus.OFFLINE
    health_score: float = 0.0
    current_load: int = 0
    max_concurrent_tasks: int = 5
    rate_limit_per_minute: int = 60
    rate_limit_per_hour: int = 1000
    last_health_check: Optional[datetime] = None
    registration_time: datetime = field(default_factory=datetime.utcnow)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class AgentPerformance:
    """Performance metrics for an agent."""
    agent_id: str
    total_tasks: int = 0
    successful_tasks: int = 0
    failed_tasks: int = 0
    average_response_time: float = 0.0
    average_quality_score: float = 0.0
    role_performance: Dict[str, Dict[str, float]] = field(default_factory=dict)
    recent_performance: List[float] = field(default_factory=list)
    last_updated: datetime = field(default_factory=datetime.utcnow)


class AgentRegistry:
    """
    Registry for managing all branded AI coding assistants.
    
    Provides agent discovery, health monitoring, role assignment,
    and performance tracking capabilities.
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # Agent storage
        self.agents: Dict[str, AgentInfo] = {}
        self.agent_performance: Dict[str, AgentPerformance] = {}
        self.reserved_agents: Dict[str, str] = {}  # agent_id -> task_id
        
        # Role mappings
        self.role_to_agents: Dict[AgentRole, Set[str]] = {role: set() for role in AgentRole}
        self.capability_to_agents: Dict[AgentCapability, Set[str]] = {cap: set() for cap in AgentCapability}
        
        # Configuration
        self.health_check_interval = self.config.get('health_check_interval', 60)
        self.performance_tracking_enabled = self.config.get('performance_tracking', True)
        self.auto_discovery_enabled = self.config.get('auto_discovery', True)
        
        # Initialize with known agent configurations
        self._initialize_known_agents()
    
    def _initialize_known_agents(self) -> None:
        """Initialize registry with known branded AI assistants."""
        
        # GitHub Copilot
        copilot_agent = AgentInfo(
            agent_id="github_copilot",
            vendor="github_copilot",
            name="GitHub Copilot",
            version="1.0",
            api_endpoint="https://api.github.com/copilot",
            supported_roles=[
                AgentRole.GENERATOR, AgentRole.COMPLETER, AgentRole.ANALYZER
            ],
            capabilities=[
                AgentCapability.CODE_GENERATION, AgentCapability.CODE_COMPLETION,
                AgentCapability.CODE_ANALYSIS, AgentCapability.DOCUMENTATION
            ],
            programming_languages=[
                "python", "javascript", "typescript", "java", "c#", "c++", "go", "rust", "php", "ruby"
            ],
            frameworks=["react", "vue", "angular", "django", "flask", "spring", "express"],
            max_concurrent_tasks=10,
            rate_limit_per_minute=60,
            rate_limit_per_hour=5000
        )
        
        # Tabnine
        tabnine_agent = AgentInfo(
            agent_id="tabnine",
            vendor="tabnine",
            name="Tabnine",
            version="2.0",
            api_endpoint="https://api.tabnine.com",
            supported_roles=[
                AgentRole.COMPLETER, AgentRole.GENERATOR, AgentRole.OPTIMIZER
            ],
            capabilities=[
                AgentCapability.CODE_COMPLETION, AgentCapability.CODE_GENERATION,
                AgentCapability.OPTIMIZATION, AgentCapability.REFACTORING
            ],
            programming_languages=[
                "python", "javascript", "typescript", "java", "c#", "c++", "go", "kotlin", "scala"
            ],
            frameworks=["tensorflow", "pytorch", "react", "angular", "spring"],
            max_concurrent_tasks=15,
            rate_limit_per_minute=100,
            rate_limit_per_hour=10000
        )
        
        # Amazon Q Developer
        amazon_q_agent = AgentInfo(
            agent_id="amazon_q",
            vendor="amazon_q",
            name="Amazon Q Developer",
            version="1.0",
            api_endpoint="https://q.aws.amazon.com/api",
            supported_roles=[
                AgentRole.ANALYZER, AgentRole.VALIDATOR, AgentRole.REVIEWER, AgentRole.DEBUGGER
            ],
            capabilities=[
                AgentCapability.CODE_ANALYSIS, AgentCapability.CODE_REVIEW,
                AgentCapability.SECURITY_ANALYSIS, AgentCapability.DEBUGGING,
                AgentCapability.TESTING
            ],
            programming_languages=[
                "python", "java", "javascript", "typescript", "c#", "go"
            ],
            frameworks=["aws-sdk", "boto3", "lambda", "dynamodb", "s3"],
            max_concurrent_tasks=8,
            rate_limit_per_minute=50,
            rate_limit_per_hour=2000
        )
        
        # Cursor
        cursor_agent = AgentInfo(
            agent_id="cursor",
            vendor="cursor",
            name="Cursor",
            version="1.0",
            api_endpoint="https://api.cursor.sh",
            supported_roles=[
                AgentRole.GENERATOR, AgentRole.ANALYZER, AgentRole.VALIDATOR, AgentRole.DOCUMENTER
            ],
            capabilities=[
                AgentCapability.CODE_GENERATION, AgentCapability.CODE_ANALYSIS,
                AgentCapability.DOCUMENTATION, AgentCapability.REFACTORING
            ],
            programming_languages=[
                "python", "javascript", "typescript", "rust", "go", "c++", "java"
            ],
            frameworks=["react", "nextjs", "svelte", "fastapi", "express"],
            max_concurrent_tasks=6,
            rate_limit_per_minute=30,
            rate_limit_per_hour=1000
        )
        
        # QodoAI
        qodo_agent = AgentInfo(
            agent_id="qodo_ai",
            vendor="qodo_ai",
            name="QodoAI",
            version="1.0",
            api_endpoint="https://api.qodo.ai",
            supported_roles=[
                AgentRole.VALIDATOR, AgentRole.REVIEWER, AgentRole.ANALYZER
            ],
            capabilities=[
                AgentCapability.TESTING, AgentCapability.CODE_REVIEW,
                AgentCapability.SECURITY_ANALYSIS, AgentCapability.PERFORMANCE_ANALYSIS
            ],
            programming_languages=[
                "python", "javascript", "typescript", "java", "c#", "go"
            ],
            frameworks=["pytest", "jest", "junit", "mocha", "cypress"],
            max_concurrent_tasks=5,
            rate_limit_per_minute=40,
            rate_limit_per_hour=1500
        )
        
        # Register all agents
        for agent in [copilot_agent, tabnine_agent, amazon_q_agent, cursor_agent, qodo_agent]:
            self.register_agent(agent)
    
    def register_agent(self, agent_info: AgentInfo) -> bool:
        """
        Register a new AI agent in the registry.
        
        Args:
            agent_info: Information about the agent to register
            
        Returns:
            True if registration successful, False otherwise
        """
        try:
            # Validate agent info
            if not agent_info.agent_id or not agent_info.vendor:
                self.logger.error("Agent ID and vendor are required for registration")
                return False
            
            if agent_info.agent_id in self.agents:
                self.logger.warning(f"Agent {agent_info.agent_id} already registered, updating...")
            
            # Register agent
            self.agents[agent_info.agent_id] = agent_info
            
            # Initialize performance tracking
            if agent_info.agent_id not in self.agent_performance:
                self.agent_performance[agent_info.agent_id] = AgentPerformance(
                    agent_id=agent_info.agent_id
                )
            
            # Update role mappings
            for role in agent_info.supported_roles:
                self.role_to_agents[role].add(agent_info.agent_id)
            
            # Update capability mappings
            for capability in agent_info.capabilities:
                self.capability_to_agents[capability].add(agent_info.agent_id)
            
            self.logger.info(f"Successfully registered agent: {agent_info.agent_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error registering agent {agent_info.agent_id}: {str(e)}")
            return False
    
    def unregister_agent(self, agent_id: str) -> bool:
        """
        Unregister an agent from the registry.
        
        Args:
            agent_id: ID of the agent to unregister
            
        Returns:
            True if unregistration successful, False otherwise
        """
        try:
            if agent_id not in self.agents:
                self.logger.warning(f"Agent {agent_id} not found for unregistration")
                return False
            
            agent_info = self.agents[agent_id]
            
            # Remove from role mappings
            for role in agent_info.supported_roles:
                self.role_to_agents[role].discard(agent_id)
            
            # Remove from capability mappings
            for capability in agent_info.capabilities:
                self.capability_to_agents[capability].discard(agent_id)
            
            # Remove from registry
            del self.agents[agent_id]
            
            # Clean up performance data
            if agent_id in self.agent_performance:
                del self.agent_performance[agent_id]
            
            # Release if reserved
            if agent_id in self.reserved_agents:
                del self.reserved_agents[agent_id]
            
            self.logger.info(f"Successfully unregistered agent: {agent_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error unregistering agent {agent_id}: {str(e)}")
            return False
    
    def get_agent_info(self, agent_id: str) -> Optional[Dict[str, Any]]:
        """Get information about a specific agent."""
        if agent_id not in self.agents:
            return None
        
        agent = self.agents[agent_id]
        performance = self.agent_performance.get(agent_id)
        
        return {
            'agent_id': agent.agent_id,
            'vendor': agent.vendor,
            'name': agent.name,
            'version': agent.version,
            'status': agent.status,
            'health_score': agent.health_score,
            'current_load': agent.current_load,
            'max_concurrent_tasks': agent.max_concurrent_tasks,
            'supported_roles': [role.value for role in agent.supported_roles],
            'capabilities': [cap.value for cap in agent.capabilities],
            'programming_languages': agent.programming_languages,
            'frameworks': agent.frameworks,
            'rate_limits': {
                'per_minute': agent.rate_limit_per_minute,
                'per_hour': agent.rate_limit_per_hour
            },
            'performance': {
                'total_tasks': performance.total_tasks if performance else 0,
                'success_rate': (performance.successful_tasks / performance.total_tasks 
                               if performance and performance.total_tasks > 0 else 0.0),
                'average_response_time': performance.average_response_time if performance else 0.0,
                'average_quality_score': performance.average_quality_score if performance else 0.0
            },
            'last_health_check': agent.last_health_check.isoformat() if agent.last_health_check else None,
            'registration_time': agent.registration_time.isoformat(),
            'reserved': agent_id in self.reserved_agents,
            'reserved_for_task': self.reserved_agents.get(agent_id)
        }
    
    def get_all_agents(self) -> List[Dict[str, Any]]:
        """Get information about all registered agents."""
        return [self.get_agent_info(agent_id) for agent_id in self.agents.keys()]
    
    def get_available_agents(self) -> List[Dict[str, Any]]:
        """Get information about all available agents."""
        available_agents = []
        
        for agent_id, agent in self.agents.items():
            if (agent.status == AgentStatus.AVAILABLE and 
                agent_id not in self.reserved_agents and
                agent.current_load < agent.max_concurrent_tasks):
                available_agents.append(self.get_agent_info(agent_id))
        
        return available_agents
    
    def get_agents_by_role(self, role: AgentRole) -> List[Dict[str, Any]]:
        """Get all agents that support a specific role."""
        agent_ids = self.role_to_agents.get(role, set())
        return [self.get_agent_info(agent_id) for agent_id in agent_ids if agent_id in self.agents]
    
    def get_agents_by_capability(self, capability: AgentCapability) -> List[Dict[str, Any]]:
        """Get all agents that have a specific capability."""
        agent_ids = self.capability_to_agents.get(capability, set())
        return [self.get_agent_info(agent_id) for agent_id in agent_ids if agent_id in self.agents]
    
    def get_agents_by_language(self, language: str) -> List[Dict[str, Any]]:
        """Get all agents that support a specific programming language."""
        matching_agents = []
        
        for agent_id, agent in self.agents.items():
            if language.lower() in [lang.lower() for lang in agent.programming_languages]:
                matching_agents.append(self.get_agent_info(agent_id))
        
        return matching_agents
    
    def update_agent_status(self, agent_id: str, status: AgentStatus, health_score: float = None) -> bool:
        """Update agent status and health score."""
        if agent_id not in self.agents:
            return False
        
        agent = self.agents[agent_id]
        agent.status = status
        agent.last_health_check = datetime.utcnow()
        
        if health_score is not None:
            agent.health_score = max(0.0, min(1.0, health_score))
        
        self.logger.debug(f"Updated agent {agent_id} status to {status.value}")
        return True
    
    def reserve_agent(self, agent_id: str, task_id: str) -> bool:
        """Reserve an agent for a specific task."""
        if agent_id not in self.agents:
            return False
        
        if agent_id in self.reserved_agents:
            self.logger.warning(f"Agent {agent_id} already reserved for task {self.reserved_agents[agent_id]}")
            return False
        
        agent = self.agents[agent_id]
        if agent.status != AgentStatus.AVAILABLE:
            return False
        
        self.reserved_agents[agent_id] = task_id
        agent.current_load += 1
        agent.status = AgentStatus.BUSY if agent.current_load >= agent.max_concurrent_tasks else AgentStatus.AVAILABLE
        
        self.logger.debug(f"Reserved agent {agent_id} for task {task_id}")
        return True
    
    def release_agent(self, agent_id: str) -> bool:
        """Release an agent from reservation."""
        if agent_id not in self.agents:
            return False
        
        if agent_id in self.reserved_agents:
            task_id = self.reserved_agents[agent_id]
            del self.reserved_agents[agent_id]
            self.logger.debug(f"Released agent {agent_id} from task {task_id}")
        
        agent = self.agents[agent_id]
        agent.current_load = max(0, agent.current_load - 1)
        
        # Update status based on current load
        if agent.current_load < agent.max_concurrent_tasks and agent.status == AgentStatus.BUSY:
            agent.status = AgentStatus.AVAILABLE
        
        return True
    
    def is_agent_available(self, agent_id: str) -> bool:
        """Check if an agent is available for new tasks."""
        if agent_id not in self.agents:
            return False
        
        agent = self.agents[agent_id]
        return (agent.status == AgentStatus.AVAILABLE and 
                agent_id not in self.reserved_agents and
                agent.current_load < agent.max_concurrent_tasks)
    
    def update_agent_performance(self, agent_id: str, task_success: bool, 
                               response_time: float, quality_score: float = None) -> None:
        """Update performance metrics for an agent."""
        if not self.performance_tracking_enabled or agent_id not in self.agent_performance:
            return
        
        performance = self.agent_performance[agent_id]
        performance.total_tasks += 1
        
        if task_success:
            performance.successful_tasks += 1
        else:
            performance.failed_tasks += 1
        
        # Update average response time
        total_time = (performance.average_response_time * (performance.total_tasks - 1) + response_time)
        performance.average_response_time = total_time / performance.total_tasks
        
        # Update quality score if provided
        if quality_score is not None:
            if performance.average_quality_score == 0.0:
                performance.average_quality_score = quality_score
            else:
                total_quality = (performance.average_quality_score * (performance.total_tasks - 1) + quality_score)
                performance.average_quality_score = total_quality / performance.total_tasks
            
            # Update recent performance (keep last 20 scores)
            performance.recent_performance.append(quality_score)
            if len(performance.recent_performance) > 20:
                performance.recent_performance = performance.recent_performance[-20:]
        
        performance.last_updated = datetime.utcnow()
    
    def get_registry_statistics(self) -> Dict[str, Any]:
        """Get comprehensive registry statistics."""
        total_agents = len(self.agents)
        available_agents = len(self.get_available_agents())
        
        status_counts = {}
        for status in AgentStatus:
            status_counts[status.value] = sum(1 for agent in self.agents.values() if agent.status == status)
        
        role_counts = {}
        for role in AgentRole:
            role_counts[role.value] = len(self.role_to_agents[role])
        
        capability_counts = {}
        for capability in AgentCapability:
            capability_counts[capability.value] = len(self.capability_to_agents[capability])
        
        return {
            'total_agents': total_agents,
            'available_agents': available_agents,
            'reserved_agents': len(self.reserved_agents),
            'status_distribution': status_counts,
            'role_distribution': role_counts,
            'capability_distribution': capability_counts,
            'average_health_score': sum(agent.health_score for agent in self.agents.values()) / total_agents if total_agents > 0 else 0.0,
            'total_current_load': sum(agent.current_load for agent in self.agents.values()),
            'performance_tracking_enabled': self.performance_tracking_enabled,
            'last_updated': datetime.utcnow().isoformat()
        }

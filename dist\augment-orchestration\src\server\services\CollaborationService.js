"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CollaborationService = void 0;
const client_1 = require("@prisma/client");
const EventBus_1 = require("./EventBus");
const logger_1 = require("../utils/logger");
class CollaborationService {
    constructor(io) {
        this.userPresence = new Map();
        this.socketToUser = new Map();
        this.prisma = new client_1.PrismaClient();
        this.eventBus = new EventBus_1.EventBus();
        this.io = io;
        this.setupSocketHandlers();
        this.setupEventListeners();
    }
    /**
     * Setup Socket.IO event handlers
     */
    setupSocketHandlers() {
        this.io.on('connection', (socket) => {
            socket.on('user:join', async (data) => {
                await this.handleUserJoin(socket.id, data.userId, data.username);
            });
            socket.on('user:presence', async (data) => {
                await this.updateUserPresence(socket.id, data);
            });
            socket.on('comment:create', async (data) => {
                await this.handleCommentCreate(socket.id, data);
            });
            socket.on('comment:react', async (data) => {
                await this.handleCommentReaction(socket.id, data);
            });
            socket.on('disconnect', async () => {
                await this.handleUserDisconnect(socket.id);
            });
        });
    }
    /**
     * Setup event listeners for system events
     */
    setupEventListeners() {
        this.eventBus.on(EventBus_1.EVENT_TYPES.WORKFLOW_STARTED, this.handleWorkflowStarted.bind(this));
        this.eventBus.on(EventBus_1.EVENT_TYPES.AGENT_CREATED, this.handleAgentCreated.bind(this));
        this.eventBus.on(EventBus_1.EVENT_TYPES.ROLE_ASSIGNED, this.handleRoleAssigned.bind(this));
    }
    /**
     * Handle user joining the collaboration session
     */
    async handleUserJoin(socketId, userId, username) {
        try {
            const presence = {
                userId,
                username,
                status: 'online',
                lastSeen: new Date(),
                socketId,
            };
            this.userPresence.set(userId, presence);
            this.socketToUser.set(socketId, userId);
            // Broadcast user joined to all clients
            this.io.emit('user:joined', {
                userId,
                username,
                status: 'online',
            });
            // Send current online users to the new user
            const onlineUsers = Array.from(this.userPresence.values())
                .filter(user => user.status !== 'offline');
            this.io.to(socketId).emit('users:online', onlineUsers);
            logger_1.logger.debug('User joined collaboration session', { userId, username, socketId });
        }
        catch (error) {
            logger_1.logger.error('Failed to handle user join', { error, socketId, userId });
        }
    }
    /**
     * Update user presence information
     */
    async updateUserPresence(socketId, data) {
        try {
            const userId = this.socketToUser.get(socketId);
            if (!userId)
                return;
            const presence = this.userPresence.get(userId);
            if (!presence)
                return;
            presence.status = data.status;
            presence.currentPage = data.currentPage;
            presence.currentResource = data.currentResource;
            presence.lastSeen = new Date();
            this.userPresence.set(userId, presence);
            // Broadcast presence update
            this.io.emit('user:presence', {
                userId,
                status: presence.status,
                currentPage: presence.currentPage,
                currentResource: presence.currentResource,
            });
            logger_1.logger.debug('User presence updated', { userId, status: data.status });
        }
        catch (error) {
            logger_1.logger.error('Failed to update user presence', { error, socketId });
        }
    }
    /**
     * Handle user disconnect
     */
    async handleUserDisconnect(socketId) {
        try {
            const userId = this.socketToUser.get(socketId);
            if (!userId)
                return;
            const presence = this.userPresence.get(userId);
            if (presence) {
                presence.status = 'offline';
                presence.lastSeen = new Date();
                presence.socketId = undefined;
            }
            this.socketToUser.delete(socketId);
            // Broadcast user left
            this.io.emit('user:left', { userId });
            logger_1.logger.debug('User disconnected from collaboration session', { userId, socketId });
        }
        catch (error) {
            logger_1.logger.error('Failed to handle user disconnect', { error, socketId });
        }
    }
    /**
     * Create a new comment
     */
    async createComment(userId, resourceType, resourceId, content, parentId, mentions = []) {
        try {
            const user = await this.prisma.user.findUnique({
                where: { id: userId },
                select: { id: true, username: true },
            });
            if (!user) {
                throw new Error('User not found');
            }
            const comment = await this.prisma.comment.create({
                data: {
                    userId,
                    resourceType,
                    resourceId,
                    content,
                    parentId,
                    mentions,
                    isResolved: false,
                },
            });
            const commentObj = {
                id: comment.id,
                userId: comment.userId,
                username: user.username,
                resourceType: comment.resourceType,
                resourceId: comment.resourceId,
                content: comment.content,
                parentId: comment.parentId,
                mentions: comment.mentions,
                createdAt: comment.createdAt,
                updatedAt: comment.updatedAt,
                isResolved: comment.isResolved,
                reactions: [],
            };
            // Create notifications for mentioned users
            for (const mentionedUserId of mentions) {
                await this.createNotification(mentionedUserId, 'mention', 'You were mentioned in a comment', `${user.username} mentioned you in a comment on ${resourceType}`, resourceType, resourceId);
            }
            // Broadcast new comment
            this.io.emit('comment:created', commentObj);
            // Log activity
            await this.logActivity(userId, user.username, 'commented', resourceType, resourceId, `Added a comment`, { commentId: comment.id });
            logger_1.logger.debug('Comment created', { commentId: comment.id, userId, resourceType, resourceId });
            return commentObj;
        }
        catch (error) {
            logger_1.logger.error('Failed to create comment', { error, userId, resourceType, resourceId });
            throw error;
        }
    }
    /**
     * Handle comment creation from socket
     */
    async handleCommentCreate(socketId, data) {
        try {
            const userId = this.socketToUser.get(socketId);
            if (!userId)
                return;
            await this.createComment(userId, data.resourceType, data.resourceId, data.content, data.parentId, data.mentions);
        }
        catch (error) {
            logger_1.logger.error('Failed to handle comment creation', { error, socketId, data });
        }
    }
    /**
     * Add reaction to comment
     */
    async addCommentReaction(userId, commentId, emoji) {
        try {
            const user = await this.prisma.user.findUnique({
                where: { id: userId },
                select: { id: true, username: true },
            });
            if (!user) {
                throw new Error('User not found');
            }
            // Check if user already reacted with this emoji
            const existingReaction = await this.prisma.commentReaction.findFirst({
                where: { commentId, userId, emoji },
            });
            if (existingReaction) {
                // Remove existing reaction
                await this.prisma.commentReaction.delete({
                    where: { id: existingReaction.id },
                });
            }
            else {
                // Add new reaction
                await this.prisma.commentReaction.create({
                    data: { commentId, userId, emoji },
                });
            }
            // Get updated reactions
            const reactions = await this.prisma.commentReaction.findMany({
                where: { commentId },
                include: { user: { select: { username: true } } },
            });
            const reactionData = reactions.map(r => ({
                userId: r.userId,
                username: r.user.username,
                emoji: r.emoji,
                createdAt: r.createdAt,
            }));
            // Broadcast reaction update
            this.io.emit('comment:reaction', {
                commentId,
                reactions: reactionData,
            });
            logger_1.logger.debug('Comment reaction updated', { commentId, userId, emoji });
        }
        catch (error) {
            logger_1.logger.error('Failed to add comment reaction', { error, userId, commentId, emoji });
            throw error;
        }
    }
    /**
     * Handle comment reaction from socket
     */
    async handleCommentReaction(socketId, data) {
        try {
            const userId = this.socketToUser.get(socketId);
            if (!userId)
                return;
            await this.addCommentReaction(userId, data.commentId, data.emoji);
        }
        catch (error) {
            logger_1.logger.error('Failed to handle comment reaction', { error, socketId, data });
        }
    }
    /**
     * Get comments for a resource
     */
    async getComments(resourceType, resourceId) {
        try {
            const comments = await this.prisma.comment.findMany({
                where: { resourceType, resourceId },
                include: {
                    user: { select: { username: true } },
                    reactions: {
                        include: { user: { select: { username: true } } },
                    },
                },
                orderBy: { createdAt: 'asc' },
            });
            return comments.map(comment => ({
                id: comment.id,
                userId: comment.userId,
                username: comment.user.username,
                resourceType: comment.resourceType,
                resourceId: comment.resourceId,
                content: comment.content,
                parentId: comment.parentId,
                mentions: comment.mentions,
                createdAt: comment.createdAt,
                updatedAt: comment.updatedAt,
                isResolved: comment.isResolved,
                reactions: comment.reactions.map(r => ({
                    userId: r.userId,
                    username: r.user.username,
                    emoji: r.emoji,
                    createdAt: r.createdAt,
                })),
            }));
        }
        catch (error) {
            logger_1.logger.error('Failed to get comments', { error, resourceType, resourceId });
            throw error;
        }
    }
    /**
     * Create notification
     */
    async createNotification(userId, type, title, message, resourceType, resourceId, metadata) {
        try {
            const notification = await this.prisma.notification.create({
                data: {
                    userId,
                    type,
                    title,
                    message,
                    resourceType,
                    resourceId,
                    metadata,
                    isRead: false,
                },
            });
            // Send real-time notification
            const userSocket = Array.from(this.userPresence.values())
                .find(presence => presence.userId === userId)?.socketId;
            if (userSocket) {
                this.io.to(userSocket).emit('notification:new', {
                    id: notification.id,
                    type: notification.type,
                    title: notification.title,
                    message: notification.message,
                    resourceType: notification.resourceType,
                    resourceId: notification.resourceId,
                    createdAt: notification.createdAt,
                    isRead: notification.isRead,
                });
            }
            logger_1.logger.debug('Notification created', { notificationId: notification.id, userId, type });
        }
        catch (error) {
            logger_1.logger.error('Failed to create notification', { error, userId, type });
        }
    }
    /**
     * Get user notifications
     */
    async getUserNotifications(userId, limit = 50) {
        try {
            const notifications = await this.prisma.notification.findMany({
                where: { userId },
                orderBy: { createdAt: 'desc' },
                take: limit,
            });
            return notifications.map(n => ({
                id: n.id,
                userId: n.userId,
                type: n.type,
                title: n.title,
                message: n.message,
                resourceType: n.resourceType,
                resourceId: n.resourceId,
                isRead: n.isRead,
                createdAt: n.createdAt,
                metadata: n.metadata,
            }));
        }
        catch (error) {
            logger_1.logger.error('Failed to get user notifications', { error, userId });
            throw error;
        }
    }
    /**
     * Mark notification as read
     */
    async markNotificationRead(userId, notificationId) {
        try {
            await this.prisma.notification.updateMany({
                where: { id: notificationId, userId },
                data: { isRead: true },
            });
            logger_1.logger.debug('Notification marked as read', { notificationId, userId });
        }
        catch (error) {
            logger_1.logger.error('Failed to mark notification as read', { error, notificationId, userId });
        }
    }
    /**
     * Log user activity
     */
    async logActivity(userId, username, action, resourceType, resourceId, description, metadata) {
        try {
            await this.prisma.activityLog.create({
                data: {
                    userId,
                    username,
                    action,
                    resourceType,
                    resourceId,
                    description,
                    metadata,
                    timestamp: new Date(),
                },
            });
            // Broadcast activity to interested users
            this.io.emit('activity:new', {
                userId,
                username,
                action,
                resourceType,
                resourceId,
                description,
                timestamp: new Date(),
            });
            logger_1.logger.debug('Activity logged', { userId, action, resourceType, resourceId });
        }
        catch (error) {
            logger_1.logger.error('Failed to log activity', { error, userId, action });
        }
    }
    /**
     * Get activity feed
     */
    async getActivityFeed(limit = 100, resourceType) {
        try {
            const where = {};
            if (resourceType)
                where.resourceType = resourceType;
            const activities = await this.prisma.activityLog.findMany({
                where,
                orderBy: { timestamp: 'desc' },
                take: limit,
            });
            return activities.map(activity => ({
                id: activity.id,
                userId: activity.userId,
                username: activity.username,
                action: activity.action,
                resourceType: activity.resourceType,
                resourceId: activity.resourceId,
                resourceName: activity.resourceName || '',
                description: activity.description,
                timestamp: activity.timestamp,
                metadata: activity.metadata,
            }));
        }
        catch (error) {
            logger_1.logger.error('Failed to get activity feed', { error, resourceType });
            throw error;
        }
    }
    /**
     * Get current online users
     */
    getOnlineUsers() {
        return Array.from(this.userPresence.values())
            .filter(user => user.status !== 'offline');
    }
    /**
     * Handle workflow started event
     */
    async handleWorkflowStarted(data) {
        // Create notifications for relevant users
        // This is a simplified example - in practice, you'd determine who should be notified
        const message = `Workflow execution started: ${data.workflowId}`;
        // Broadcast to all online users for now
        this.io.emit('workflow:started', {
            workflowId: data.workflowId,
            executionId: data.executionId,
            message,
        });
    }
    /**
     * Handle agent created event
     */
    async handleAgentCreated(data) {
        const message = `New agent created: ${data.name}`;
        this.io.emit('agent:created', {
            agentId: data.id,
            name: data.name,
            message,
        });
    }
    /**
     * Handle role assigned event
     */
    async handleRoleAssigned(data) {
        await this.createNotification(data.userId, 'assignment', 'Role Assigned', `You have been assigned a new role`, 'role', data.roleId);
    }
}
exports.CollaborationService = CollaborationService;

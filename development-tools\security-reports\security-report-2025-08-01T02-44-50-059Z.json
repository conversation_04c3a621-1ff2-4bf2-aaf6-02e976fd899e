{"timestamp": "2025-08-01T02:44:30.438Z", "projectPath": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools", "projectType": "Node.js", "summary": {"vulnerabilities": {"critical": 0, "high": 0, "medium": 0, "low": 0}, "licenses": {"compliant": 674, "nonCompliant": 0, "unknown": 0}, "dependencies": {"total": 17, "outdated": 13}, "codeIssues": {"security": 0, "quality": 0}}, "details": {"npmAudit": {"auditReportVersion": 2, "vulnerabilities": {"@cyclonedx/cyclonedx-library": {"name": "@cyclonedx/cyclonedx-library", "severity": "critical", "isDirect": false, "via": ["libxmljs2"], "effects": ["@cyclonedx/cyclonedx-npm"], "range": "1.14.0-rc.0 - 8.0.1-alpha.1", "nodes": ["node_modules/@cyclonedx/cyclonedx-library"], "fixAvailable": {"name": "@cyclonedx/cyclonedx-npm", "version": "4.0.0", "isSemVerMajor": true}}, "@cyclonedx/cyclonedx-npm": {"name": "@cyclonedx/cyclonedx-npm", "severity": "critical", "isDirect": true, "via": ["@cyclonedx/cyclonedx-library"], "effects": [], "range": "1.11.0 - 2.1.0", "nodes": ["node_modules/@cyclonedx/cyclonedx-npm"], "fixAvailable": {"name": "@cyclonedx/cyclonedx-npm", "version": "4.0.0", "isSemVerMajor": true}}, "libxmljs2": {"name": "libxmljs2", "severity": "critical", "isDirect": false, "via": [{"source": 1100657, "name": "libxmljs2", "dependency": "libxmljs2", "title": "libxmljs2 vulnerable to type confusion when parsing specially crafted XML", "url": "https://github.com/advisories/GHSA-78h3-pg4x-j8cv", "severity": "critical", "cwe": ["CWE-843"], "cvss": {"score": 8.1, "vectorString": "CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:U/C:H/I:H/A:H"}, "range": "<=0.35.0"}], "effects": ["@cyclonedx/cyclonedx-library"], "range": "<=0.35.0", "nodes": ["node_modules/libxmljs2"], "fixAvailable": {"name": "@cyclonedx/cyclonedx-npm", "version": "4.0.0", "isSemVerMajor": true}}}, "metadata": {"vulnerabilities": {"info": 0, "low": 0, "moderate": 0, "high": 0, "critical": 3, "total": 3}, "dependencies": {"prod": 99, "dev": 716, "optional": 51, "peer": 0, "peerOptional": 0, "total": 815}}}, "licenses": {"@babel/helper-string-parser@7.27.1": {"licenses": "MIT", "repository": "https://github.com/babel/babel", "publisher": "The Babel Team", "url": "https://babel.dev/team", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@babel\\helper-string-parser", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@babel\\helper-string-parser\\LICENSE"}, "@babel/helper-validator-identifier@7.27.1": {"licenses": "MIT", "repository": "https://github.com/babel/babel", "publisher": "The Babel Team", "url": "https://babel.dev/team", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@babel\\helper-validator-identifier", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@babel\\helper-validator-identifier\\LICENSE"}, "@babel/parser@7.28.0": {"licenses": "MIT", "repository": "https://github.com/babel/babel", "publisher": "The Babel Team", "url": "https://babel.dev/team", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@babel\\parser", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@babel\\parser\\LICENSE"}, "@babel/types@7.28.0": {"licenses": "MIT", "repository": "https://github.com/babel/babel", "publisher": "The Babel Team", "url": "https://babel.dev/team", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@babel\\types", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@babel\\types\\LICENSE"}, "@colors/colors@1.5.0": {"licenses": "MIT", "repository": "https://github.com/DABH/colors.js", "publisher": "DABH", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@colors\\colors", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@colors\\colors\\LICENSE"}, "@cyclonedx/cyclonedx-library@6.13.1": {"licenses": "Apache-2.0", "repository": "https://github.com/CycloneDX/cyclonedx-javascript-library", "publisher": "<PERSON>", "url": "https://github.com/jkowalleck", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@cyclonedx\\cyclonedx-library", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@cyclonedx\\cyclonedx-library\\LICENSE", "noticeFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@cyclonedx\\cyclonedx-library\\NOTICE"}, "@cyclonedx/cyclonedx-npm@1.20.0": {"licenses": "Apache-2.0", "repository": "https://github.com/CycloneDX/cyclonedx-node-npm", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@cyclonedx\\cyclonedx-npm", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@cyclonedx\\cyclonedx-npm\\LICENSE", "noticeFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@cyclonedx\\cyclonedx-npm\\NOTICE"}, "@dependents/detective-less@3.0.2": {"licenses": "MIT", "repository": "https://github.com/dependents/node-detective-less", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@dependents\\detective-less", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@dependents\\detective-less\\LICENSE"}, "@eslint-community/eslint-utils@4.7.0": {"licenses": "MIT", "repository": "https://github.com/eslint-community/eslint-utils", "publisher": "<PERSON><PERSON>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@eslint-community\\eslint-utils", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@eslint-community\\eslint-utils\\LICENSE"}, "@eslint-community/regexpp@4.12.1": {"licenses": "MIT", "repository": "https://github.com/eslint-community/regexpp", "publisher": "<PERSON><PERSON>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@eslint-community\\regexpp", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@eslint-community\\regexpp\\LICENSE"}, "@eslint/eslintrc@2.1.4": {"licenses": "MIT", "repository": "https://github.com/eslint/eslintrc", "publisher": "<PERSON>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@eslint\\eslintrc", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@eslint\\eslintrc\\LICENSE"}, "@eslint/js@8.57.1": {"licenses": "MIT", "repository": "https://github.com/eslint/eslint", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@eslint\\js", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@eslint\\js\\LICENSE"}, "@gar/promisify@1.1.3": {"licenses": "MIT", "repository": "https://github.com/wraithgar/gar-promisify", "publisher": "Gar", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@gar\\promisify", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@gar\\promisify\\LICENSE.md"}, "@humanwhocodes/config-array@0.13.0": {"licenses": "Apache-2.0", "repository": "https://github.com/humanwhocodes/config-array", "publisher": "<PERSON>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@humanwhocodes\\config-array", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@humanwhocodes\\config-array\\LICENSE"}, "@humanwhocodes/module-importer@1.0.1": {"licenses": "Apache-2.0", "repository": "https://github.com/humanwhocodes/module-importer", "publisher": "<PERSON>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@humanwhocodes\\module-importer", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@humanwhocodes\\module-importer\\LICENSE"}, "@humanwhocodes/object-schema@2.0.3": {"licenses": "BSD-3-<PERSON><PERSON>", "repository": "https://github.com/humanwhocodes/object-schema", "publisher": "<PERSON>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@humanwhocodes\\object-schema", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@humanwhocodes\\object-schema\\LICENSE"}, "@inquirer/figures@1.0.12": {"licenses": "MIT", "repository": "https://github.com/SBoudrias/Inquirer.js", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@inquirer\\figures", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@inquirer\\figures\\LICENSE"}, "@isaacs/cliui@8.0.2": {"licenses": "ISC", "repository": "https://github.com/yargs/cliui", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@isaacs\\cliui", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@isaacs\\cliui\\LICENSE.txt"}, "@nodelib/fs.scandir@2.1.5": {"licenses": "MIT", "repository": "https://github.com/nodelib/nodelib/tree/master/packages/fs/fs.scandir", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@nodelib\\fs.scandir", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@nodelib\\fs.scandir\\LICENSE"}, "@nodelib/fs.stat@2.0.5": {"licenses": "MIT", "repository": "https://github.com/nodelib/nodelib/tree/master/packages/fs/fs.stat", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@nodelib\\fs.stat", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@nodelib\\fs.stat\\LICENSE"}, "@nodelib/fs.walk@1.2.8": {"licenses": "MIT", "repository": "https://github.com/nodelib/nodelib/tree/master/packages/fs/fs.walk", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@nodelib\\fs.walk", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@nodelib\\fs.walk\\LICENSE"}, "@npmcli/agent@2.2.2": {"licenses": "ISC", "repository": "https://github.com/npm/agent", "publisher": "GitHub Inc.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@npmcli\\agent", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@npmcli\\agent\\README.md"}, "@npmcli/fs@2.1.2": {"licenses": "ISC", "repository": "https://github.com/npm/fs", "publisher": "GitHub Inc.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@npmcli\\run-script\\node_modules\\@npmcli\\fs", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@npmcli\\run-script\\node_modules\\@npmcli\\fs\\LICENSE.md"}, "@npmcli/fs@3.1.1": {"licenses": "ISC", "repository": "https://github.com/npm/fs", "publisher": "GitHub Inc.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@npmcli\\fs", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@npmcli\\fs\\LICENSE.md"}, "@npmcli/git@4.1.0": {"licenses": "ISC", "repository": "https://github.com/npm/git", "publisher": "GitHub Inc.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@npmcli\\git", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@npmcli\\git\\LICENSE"}, "@npmcli/installed-package-contents@2.1.0": {"licenses": "ISC", "repository": "https://github.com/npm/installed-package-contents", "publisher": "GitHub Inc.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@npmcli\\installed-package-contents", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@npmcli\\installed-package-contents\\LICENSE"}, "@npmcli/move-file@2.0.1": {"licenses": "MIT", "repository": "https://github.com/npm/move-file", "publisher": "GitHub Inc.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@npmcli\\move-file", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@npmcli\\move-file\\LICENSE.md"}, "@npmcli/node-gyp@3.0.0": {"licenses": "ISC", "repository": "https://github.com/npm/node-gyp", "publisher": "GitHub Inc.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@npmcli\\node-gyp", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@npmcli\\node-gyp\\README.md"}, "@npmcli/promise-spawn@6.0.2": {"licenses": "ISC", "repository": "https://github.com/npm/promise-spawn", "publisher": "GitHub Inc.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@npmcli\\promise-spawn", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@npmcli\\promise-spawn\\LICENSE"}, "@npmcli/run-script@6.0.2": {"licenses": "ISC", "repository": "https://github.com/npm/run-script", "publisher": "GitHub Inc.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@npmcli\\run-script", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@npmcli\\run-script\\LICENSE"}, "@oozcitak/dom@1.15.10": {"licenses": "MIT", "repository": "https://github.com/oozcitak/dom", "publisher": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@oozcitak\\dom", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@oozcitak\\dom\\LICENSE"}, "@oozcitak/infra@1.0.8": {"licenses": "MIT", "repository": "https://github.com/oozcitak/infra", "publisher": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@oozcitak\\infra", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@oozcitak\\infra\\LICENSE"}, "@oozcitak/url@1.0.4": {"licenses": "MIT", "repository": "https://github.com/oozcitak/url", "publisher": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@oozcitak\\url", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@oozcitak\\url\\LICENSE"}, "@oozcitak/util@8.3.8": {"licenses": "MIT", "repository": "https://github.com/oozcitak/util", "publisher": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@oozcitak\\util", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@oozcitak\\util\\LICENSE"}, "@pkgjs/parseargs@0.11.0": {"licenses": "MIT", "repository": "https://github.com/pkgjs/parseargs", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@pkgjs\\parseargs", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@pkgjs\\parseargs\\LICENSE"}, "@pnpm/config.env-replace@1.1.0": {"licenses": "MIT", "repository": "https://github.com/pnpm/components", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@pnpm\\config.env-replace"}, "@pnpm/network.ca-file@1.0.2": {"licenses": "MIT", "repository": "https://github.com/pnpm/components", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@pnpm\\network.ca-file"}, "@pnpm/npm-conf@2.3.1": {"licenses": "MIT", "repository": "https://github.com/pnpm/npm-conf", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@pnpm\\npm-conf", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@pnpm\\npm-conf\\license"}, "@sentry-internal/tracing@7.120.3": {"licenses": "MIT", "repository": "https://github.com/getsentry/sentry-javascript", "publisher": "Sentry", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@sentry-internal\\tracing", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@sentry-internal\\tracing\\LICENSE"}, "@sentry/core@7.120.3": {"licenses": "MIT", "repository": "https://github.com/getsentry/sentry-javascript", "publisher": "Sentry", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@sentry\\core", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@sentry\\core\\LICENSE"}, "@sentry/integrations@7.120.3": {"licenses": "MIT", "repository": "https://github.com/getsentry/sentry-javascript", "publisher": "Sentry", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@sentry\\integrations", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@sentry\\integrations\\LICENSE"}, "@sentry/node@7.120.3": {"licenses": "MIT", "repository": "https://github.com/getsentry/sentry-javascript", "publisher": "Sentry", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@sentry\\node", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@sentry\\node\\LICENSE"}, "@sentry/types@7.120.3": {"licenses": "MIT", "repository": "https://github.com/getsentry/sentry-javascript", "publisher": "Sentry", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@sentry\\types", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@sentry\\types\\LICENSE"}, "@sentry/utils@7.120.3": {"licenses": "MIT", "repository": "https://github.com/getsentry/sentry-javascript", "publisher": "Sentry", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@sentry\\utils", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@sentry\\utils\\LICENSE"}, "@sigstore/bundle@1.1.0": {"licenses": "Apache-2.0", "repository": "https://github.com/sigstore/sigstore-js", "publisher": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@sigstore\\bundle", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@sigstore\\bundle\\LICENSE"}, "@sigstore/protobuf-specs@0.2.1": {"licenses": "Apache-2.0", "repository": "https://github.com/sigstore/protobuf-specs", "publisher": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@sigstore\\protobuf-specs", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@sigstore\\protobuf-specs\\LICENSE"}, "@sigstore/sign@1.0.0": {"licenses": "Apache-2.0", "repository": "https://github.com/sigstore/sigstore-js", "publisher": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@sigstore\\sign", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@sigstore\\sign\\LICENSE"}, "@sigstore/tuf@1.0.3": {"licenses": "Apache-2.0", "repository": "https://github.com/sigstore/sigstore-js", "publisher": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@sigstore\\tuf", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@sigstore\\tuf\\LICENSE"}, "@sindresorhus/is@5.6.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/is", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@sindresorhus\\is", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@sindresorhus\\is\\license"}, "@szmarczak/http-timer@5.0.1": {"licenses": "MIT", "repository": "https://github.com/szmarczak/http-timer", "publisher": "<PERSON><PERSON><PERSON>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@szmarczak\\http-timer", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@szmarczak\\http-timer\\LICENSE"}, "@tootallnate/once@2.0.0": {"licenses": "MIT", "repository": "https://github.com/TooTallNate/once", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@tootallnate\\once", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@tootallnate\\once\\LICENSE"}, "@tootallnate/quickjs-emscripten@0.23.0": {"licenses": "MIT", "repository": "https://github.com/justjake/quickjs-emscripten", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@tootallnate\\quickjs-emscripten", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@tootallnate\\quickjs-emscripten\\LICENSE"}, "@tufjs/canonical-json@1.0.0": {"licenses": "MIT", "repository": "https://github.com/theupdateframework/tuf-js", "publisher": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@tufjs\\canonical-json", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@tufjs\\canonical-json\\LICENSE"}, "@tufjs/models@1.0.4": {"licenses": "MIT", "repository": "https://github.com/theupdateframework/tuf-js", "publisher": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@tufjs\\models", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@tufjs\\models\\LICENSE"}, "@types/glob@7.2.0": {"licenses": "MIT", "repository": "https://github.com/DefinitelyTyped/DefinitelyTyped", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@types\\glob", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@types\\glob\\LICENSE"}, "@types/http-cache-semantics@4.0.4": {"licenses": "MIT", "repository": "https://github.com/DefinitelyTyped/DefinitelyTyped", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@types\\http-cache-semantics", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@types\\http-cache-semantics\\LICENSE"}, "@types/json5@0.0.29": {"licenses": "MIT", "repository": "https://github.com/DefinitelyTyped/DefinitelyTyped", "publisher": "<PERSON>", "email": "https://jasonswearingen.github.io", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@types\\json5", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@types\\json5\\README.md"}, "@types/minimatch@5.1.2": {"licenses": "MIT", "repository": "https://github.com/DefinitelyTyped/DefinitelyTyped", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@types\\minimatch", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@types\\minimatch\\LICENSE"}, "@types/node@24.0.10": {"licenses": "MIT", "repository": "https://github.com/DefinitelyTyped/DefinitelyTyped", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@types\\node", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@types\\node\\LICENSE"}, "@types/semver-utils@1.1.3": {"licenses": "MIT", "repository": "https://github.com/DefinitelyTyped/DefinitelyTyped", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@types\\semver-utils", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@types\\semver-utils\\LICENSE"}, "@typescript-eslint/types@4.33.0": {"licenses": "MIT", "repository": "https://github.com/typescript-eslint/typescript-eslint", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\precinct\\node_modules\\@typescript-eslint\\types", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\precinct\\node_modules\\@typescript-eslint\\types\\LICENSE"}, "@typescript-eslint/types@5.62.0": {"licenses": "MIT", "repository": "https://github.com/typescript-eslint/typescript-eslint", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@typescript-eslint\\types", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@typescript-eslint\\types\\LICENSE"}, "@typescript-eslint/typescript-estree@4.33.0": {"licenses": "BSD-2-<PERSON><PERSON>", "repository": "https://github.com/typescript-eslint/typescript-eslint", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\precinct\\node_modules\\@typescript-eslint\\typescript-estree", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\precinct\\node_modules\\@typescript-eslint\\typescript-estree\\LICENSE"}, "@typescript-eslint/typescript-estree@5.62.0": {"licenses": "BSD-2-<PERSON><PERSON>", "repository": "https://github.com/typescript-eslint/typescript-eslint", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@typescript-eslint\\typescript-estree", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@typescript-eslint\\typescript-estree\\LICENSE"}, "@typescript-eslint/visitor-keys@4.33.0": {"licenses": "MIT", "repository": "https://github.com/typescript-eslint/typescript-eslint", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\precinct\\node_modules\\@typescript-eslint\\visitor-keys", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\precinct\\node_modules\\@typescript-eslint\\visitor-keys\\LICENSE"}, "@typescript-eslint/visitor-keys@5.62.0": {"licenses": "MIT", "repository": "https://github.com/typescript-eslint/typescript-eslint", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@typescript-eslint\\visitor-keys", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@typescript-eslint\\visitor-keys\\LICENSE"}, "@ungap/structured-clone@1.3.0": {"licenses": "ISC", "repository": "https://github.com/ungap/structured-clone", "publisher": "<PERSON>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@ungap\\structured-clone", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@ungap\\structured-clone\\LICENSE"}, "JSONStream@1.3.5": {"licenses": "(MIT OR Apache-2.0)", "repository": "https://github.com/dominictarr/JSONStream", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://bit.ly/dominictarr", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\JSONStream", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\JSONStream\\LICENSE.APACHE2"}, "abbrev@1.1.1": {"licenses": "ISC", "repository": "https://github.com/isaacs/abbrev-js", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\abbrev", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\abbrev\\LICENSE"}, "abbrev@2.0.0": {"licenses": "ISC", "repository": "https://github.com/npm/abbrev-js", "publisher": "GitHub Inc.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\node-gyp\\node_modules\\abbrev", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\node-gyp\\node_modules\\abbrev\\LICENSE"}, "acorn-jsx@5.3.2": {"licenses": "MIT", "repository": "https://github.com/acornjs/acorn-jsx", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\acorn-jsx", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\acorn-jsx\\LICENSE"}, "acorn-node@1.8.2": {"licenses": "Apache-2.0", "repository": "https://github.com/browserify/acorn-node", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\acorn-node", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\acorn-node\\LICENSE.md"}, "acorn-walk@7.2.0": {"licenses": "MIT", "repository": "https://github.com/acornjs/acorn", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\acorn-walk", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\acorn-walk\\LICENSE"}, "acorn@7.4.1": {"licenses": "MIT", "repository": "https://github.com/acornjs/acorn", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\acorn", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\acorn\\LICENSE"}, "acorn@8.15.0": {"licenses": "MIT", "repository": "https://github.com/acornjs/acorn", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\espree\\node_modules\\acorn", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\espree\\node_modules\\acorn\\LICENSE"}, "agent-base@6.0.2": {"licenses": "MIT", "repository": "https://github.com/TooTallNate/node-agent-base", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\tuf-js\\node_modules\\agent-base", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\tuf-js\\node_modules\\agent-base\\README.md"}, "agent-base@7.1.3": {"licenses": "MIT", "repository": "https://github.com/TooTallNate/proxy-agents", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\agent-base", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\agent-base\\LICENSE"}, "agentkeepalive@4.6.0": {"licenses": "MIT", "repository": "https://github.com/node-modules/agentkeepalive", "publisher": "fengmk2", "email": "<EMAIL>", "url": "https://github.com/fengmk2", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\agentkeepalive", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\agentkeepalive\\LICENSE"}, "aggregate-error@3.1.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/aggregate-error", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\aggregate-error", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\aggregate-error\\license"}, "ajv-formats-draft2019@1.6.1": {"licenses": "MIT", "repository": "https://github.com/luzlab/ajv-formats-draft2019", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\ajv-formats-draft2019", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\ajv-formats-draft2019\\LICENSE"}, "ajv-formats@3.0.1": {"licenses": "MIT", "repository": "https://github.com/ajv-validator/ajv-formats", "publisher": "<PERSON><PERSON><PERSON>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\ajv-formats", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\ajv-formats\\LICENSE"}, "ajv@6.12.6": {"licenses": "MIT", "repository": "https://github.com/ajv-validator/ajv", "publisher": "<PERSON><PERSON><PERSON>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@eslint\\eslintrc\\node_modules\\ajv", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@eslint\\eslintrc\\node_modules\\ajv\\LICENSE"}, "ajv@8.17.1": {"licenses": "MIT", "repository": "https://github.com/ajv-validator/ajv", "publisher": "<PERSON><PERSON><PERSON>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\ajv", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\ajv\\LICENSE"}, "ansi-align@3.0.1": {"licenses": "ISC", "repository": "https://github.com/nexdrew/ansi-align", "publisher": "nexdrew", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\ansi-align", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\ansi-align\\LICENSE"}, "ansi-colors@4.1.3": {"licenses": "MIT", "repository": "https://github.com/doowb/ansi-colors", "publisher": "<PERSON>", "url": "https://github.com/doowb", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\ansi-colors", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\ansi-colors\\LICENSE"}, "ansi-escapes@4.3.2": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/ansi-escapes", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\ansi-escapes", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\ansi-escapes\\license"}, "ansi-regex@5.0.1": {"licenses": "MIT", "repository": "https://github.com/chalk/ansi-regex", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\ansi-regex", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\ansi-regex\\license"}, "ansi-regex@6.1.0": {"licenses": "MIT", "repository": "https://github.com/chalk/ansi-regex", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\ora\\node_modules\\ansi-regex", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\ora\\node_modules\\ansi-regex\\license"}, "ansi-styles@3.2.1": {"licenses": "MIT", "repository": "https://github.com/chalk/ansi-styles", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\license-checker\\node_modules\\ansi-styles", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\license-checker\\node_modules\\ansi-styles\\license"}, "ansi-styles@4.3.0": {"licenses": "MIT", "repository": "https://github.com/chalk/ansi-styles", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\ansi-styles", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\ansi-styles\\license"}, "ansi-styles@6.2.1": {"licenses": "MIT", "repository": "https://github.com/chalk/ansi-styles", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@isaacs\\cliui\\node_modules\\ansi-styles", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@isaacs\\cliui\\node_modules\\ansi-styles\\license"}, "any-promise@1.3.0": {"licenses": "MIT", "repository": "https://github.com/kevinbeaty/any-promise", "publisher": "<PERSON>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\any-promise", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\any-promise\\LICENSE"}, "app-module-path@2.2.0": {"licenses": "BSD-2-<PERSON><PERSON>", "repository": "https://github.com/patrick-steele-idem/app-module-path-node", "publisher": "<PERSON>-<PERSON><PERSON>m", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\app-module-path", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\app-module-path\\README.md"}, "aproba@2.0.0": {"licenses": "ISC", "repository": "https://github.com/iarna/aproba", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\aproba", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\aproba\\LICENSE"}, "are-we-there-yet@3.0.1": {"licenses": "ISC", "repository": "https://github.com/npm/are-we-there-yet", "publisher": "GitHub Inc.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\are-we-there-yet", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\are-we-there-yet\\LICENSE.md"}, "argparse@1.0.10": {"licenses": "MIT", "repository": "https://github.com/nodeca/argparse", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\xmlbuilder2\\node_modules\\argparse", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\xmlbuilder2\\node_modules\\argparse\\LICENSE"}, "argparse@2.0.1": {"licenses": "Python-2.0", "repository": "https://github.com/nodeca/argparse", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\argparse", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\argparse\\LICENSE"}, "array-find-index@1.0.2": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/array-find-index", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\array-find-index", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\array-find-index\\license"}, "array-union@2.1.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/array-union", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\array-union", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\array-union\\license"}, "asap@2.0.6": {"licenses": "MIT", "repository": "https://github.com/kriskowal/asap", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\asap", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\asap\\LICENSE.md"}, "ast-module-types@2.7.1": {"licenses": "MIT", "repository": "https://github.com/mrjoelkemp/node-ast-module-types", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://www.mrjoelkemp.com/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\precinct\\node_modules\\detective-typescript\\node_modules\\ast-module-types", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\precinct\\node_modules\\detective-typescript\\node_modules\\ast-module-types\\Readme.md"}, "ast-module-types@3.0.0": {"licenses": "MIT", "repository": "https://github.com/dependents/node-ast-module-types", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://mrjoelkemp.com/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\module-definition\\node_modules\\ast-module-types", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\module-definition\\node_modules\\ast-module-types\\LICENSE"}, "ast-module-types@4.0.0": {"licenses": "MIT", "repository": "https://github.com/dependents/node-ast-module-types", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://mrjoelkemp.com/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\ast-module-types", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\ast-module-types\\LICENSE"}, "ast-types@0.13.4": {"licenses": "MIT", "repository": "https://github.com/benjamn/ast-types", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\ast-types", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\ast-types\\LICENSE"}, "audit-ci@6.6.1": {"licenses": "Apache-2.0", "repository": "https://github.com/IBM/audit-ci", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\audit-ci", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\audit-ci\\LICENSE"}, "balanced-match@1.0.2": {"licenses": "MIT", "repository": "https://github.com/juliangruber/balanced-match", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\balanced-match", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\balanced-match\\LICENSE.md"}, "base64-js@1.5.1": {"licenses": "MIT", "repository": "https://github.com/beatgammit/base64-js", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\base64-js", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\base64-js\\LICENSE"}, "basic-ftp@5.0.5": {"licenses": "MIT", "repository": "https://github.com/patrickjuchli/basic-ftp", "publisher": "<PERSON>", "email": "pat<PERSON><PERSON><PERSON><PERSON>@gmail.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\basic-ftp", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\basic-ftp\\LICENSE.txt"}, "bindings@1.5.0": {"licenses": "MIT", "repository": "https://github.com/TooTallNate/node-bindings", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://tootallnate.net", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\bindings", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\bindings\\LICENSE.md"}, "bl@4.1.0": {"licenses": "MIT", "repository": "https://github.com/rvagg/bl", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\inquirer\\node_modules\\bl", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\inquirer\\node_modules\\bl\\LICENSE.md"}, "bl@5.1.0": {"licenses": "MIT", "repository": "https://github.com/rvagg/bl", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\bl", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\bl\\LICENSE.md"}, "boolean@3.2.0": {"licenses": "MIT", "repository": "https://github.com/thenativeweb/boolean", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\boolean", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\boolean\\LICENSE.txt"}, "boxen@7.1.1": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/boxen", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\boxen", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\boxen\\license"}, "brace-expansion@1.1.12": {"licenses": "MIT", "repository": "https://github.com/juliangruber/brace-expansion", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\brace-expansion", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\brace-expansion\\LICENSE"}, "brace-expansion@2.0.2": {"licenses": "MIT", "repository": "https://github.com/juliangruber/brace-expansion", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\glob\\node_modules\\brace-expansion", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\glob\\node_modules\\brace-expansion\\LICENSE"}, "braces@3.0.3": {"licenses": "MIT", "repository": "https://github.com/micromatch/braces", "publisher": "<PERSON>", "url": "https://github.com/jonschlinkert", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\braces", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\braces\\LICENSE"}, "buffer-from@1.1.2": {"licenses": "MIT", "repository": "https://github.com/LinusU/buffer-from", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\buffer-from", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\buffer-from\\LICENSE"}, "buffer@5.7.1": {"licenses": "MIT", "repository": "https://github.com/feross/buffer", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://feross.org", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\inquirer\\node_modules\\buffer", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\inquirer\\node_modules\\buffer\\LICENSE"}, "buffer@6.0.3": {"licenses": "MIT", "repository": "https://github.com/feross/buffer", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://feross.org", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\buffer", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\buffer\\LICENSE"}, "cacache@16.1.3": {"licenses": "ISC", "repository": "https://github.com/npm/cacache", "publisher": "GitHub Inc.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@npmcli\\run-script\\node_modules\\cacache", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@npmcli\\run-script\\node_modules\\cacache\\LICENSE.md"}, "cacache@17.1.4": {"licenses": "ISC", "repository": "https://github.com/npm/cacache", "publisher": "GitHub Inc.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\tuf-js\\node_modules\\cacache", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\tuf-js\\node_modules\\cacache\\LICENSE.md"}, "cacache@18.0.4": {"licenses": "ISC", "repository": "https://github.com/npm/cacache", "publisher": "GitHub Inc.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\cacache", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\cacache\\LICENSE.md"}, "cacheable-lookup@7.0.0": {"licenses": "MIT", "repository": "https://github.com/szmarczak/cacheable-lookup", "publisher": "<PERSON><PERSON><PERSON>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\cacheable-lookup", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\cacheable-lookup\\LICENSE"}, "cacheable-request@10.2.14": {"licenses": "MIT", "repository": "https://github.com/jaredwray/cacheable", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://jaredwray.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\cacheable-request", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\cacheable-request\\README.md"}, "callsites@3.1.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/callsites", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\callsites", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\callsites\\license"}, "camelcase@7.0.1": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/camelcase", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\camelcase", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\camelcase\\license"}, "chalk@2.4.2": {"licenses": "MIT", "repository": "https://github.com/chalk/chalk", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\license-checker\\node_modules\\chalk", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\license-checker\\node_modules\\chalk\\license"}, "chalk@4.1.2": {"licenses": "MIT", "repository": "https://github.com/chalk/chalk", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\inquirer\\node_modules\\chalk", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\inquirer\\node_modules\\chalk\\license"}, "chalk@5.4.1": {"licenses": "MIT", "repository": "https://github.com/chalk/chalk", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\chalk", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\chalk\\license"}, "chardet@0.7.0": {"licenses": "MIT", "repository": "https://github.com/runk/node-chardet", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\chardet", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\chardet\\LICENSE"}, "chownr@1.1.4": {"licenses": "ISC", "repository": "https://github.com/isaacs/chownr", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\tar-fs\\node_modules\\chownr", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\tar-fs\\node_modules\\chownr\\LICENSE"}, "chownr@2.0.0": {"licenses": "ISC", "repository": "https://github.com/isaacs/chownr", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\chownr", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\chownr\\LICENSE"}, "ci-info@3.9.0": {"licenses": "MIT", "repository": "https://github.com/watson/ci-info", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://twitter.com/wa7son", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\ci-info", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\ci-info\\LICENSE"}, "clean-stack@2.2.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/clean-stack", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\clean-stack", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\clean-stack\\license"}, "cli-boxes@3.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/cli-boxes", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\cli-boxes", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\cli-boxes\\license"}, "cli-cursor@3.1.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/cli-cursor", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\inquirer\\node_modules\\cli-cursor", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\inquirer\\node_modules\\cli-cursor\\license"}, "cli-cursor@4.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/cli-cursor", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\cli-cursor", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\cli-cursor\\license"}, "cli-spinners@2.9.2": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/cli-spinners", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\cli-spinners", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\cli-spinners\\license"}, "cli-table3@0.6.5": {"licenses": "MIT", "repository": "https://github.com/cli-table/cli-table3", "publisher": "<PERSON>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\cli-table3", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\cli-table3\\LICENSE"}, "cli-width@4.1.0": {"licenses": "ISC", "repository": "https://github.com/knownasilya/cli-width", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\cli-width", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\cli-width\\LICENSE"}, "cliui@8.0.1": {"licenses": "ISC", "repository": "https://github.com/yargs/cliui", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\cliui", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\cliui\\LICENSE.txt"}, "clone@1.0.4": {"licenses": "MIT", "repository": "https://github.com/pvorb/node-clone", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://paul.vorba.ch/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\clone", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\clone\\LICENSE"}, "color-convert@1.9.3": {"licenses": "MIT", "repository": "https://github.com/Qix-/color-convert", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\license-checker\\node_modules\\color-convert", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\license-checker\\node_modules\\color-convert\\LICENSE"}, "color-convert@2.0.1": {"licenses": "MIT", "repository": "https://github.com/Qix-/color-convert", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\color-convert", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\color-convert\\LICENSE"}, "color-name@1.1.3": {"licenses": "MIT", "repository": "https://github.com/dfcreative/color-name", "publisher": "DY", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\license-checker\\node_modules\\color-name", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\license-checker\\node_modules\\color-name\\LICENSE"}, "color-name@1.1.4": {"licenses": "MIT", "repository": "https://github.com/colorjs/color-name", "publisher": "DY", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\color-name", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\color-name\\LICENSE"}, "color-support@1.1.3": {"licenses": "ISC", "repository": "https://github.com/isaacs/color-support", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\color-support", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\color-support\\LICENSE"}, "commander@10.0.1": {"licenses": "MIT", "repository": "https://github.com/tj/commander.js", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\retire\\node_modules\\commander", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\retire\\node_modules\\commander\\LICENSE"}, "commander@11.1.0": {"licenses": "MIT", "repository": "https://github.com/tj/commander.js", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\commander", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\commander\\LICENSE"}, "commander@2.20.3": {"licenses": "MIT", "repository": "https://github.com/tj/commander.js", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\nearley\\node_modules\\commander", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\nearley\\node_modules\\commander\\LICENSE"}, "commander@7.2.0": {"licenses": "MIT", "repository": "https://github.com/tj/commander.js", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\madge\\node_modules\\commander", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\madge\\node_modules\\commander\\LICENSE"}, "commander@9.5.0": {"licenses": "MIT", "repository": "https://github.com/tj/commander.js", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\dependency-tree\\node_modules\\precinct\\node_modules\\commander", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\dependency-tree\\node_modules\\precinct\\node_modules\\commander\\LICENSE"}, "commondir@1.0.1": {"licenses": "MIT", "repository": "https://github.com/substack/node-commondir", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\commondir", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\commondir\\LICENSE"}, "concat-map@0.0.1": {"licenses": "MIT", "repository": "https://github.com/substack/node-concat-map", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\concat-map", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\concat-map\\LICENSE"}, "config-chain@1.1.13": {"licenses": "MIT", "repository": "https://github.com/dominictarr/config-chain", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://dominictarr.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\config-chain", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\config-chain\\LICENCE"}, "configstore@6.0.0": {"licenses": "BSD-2-<PERSON><PERSON>", "repository": "https://github.com/yeoman/configstore", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\configstore", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\configstore\\license"}, "console-control-strings@1.1.0": {"licenses": "ISC", "repository": "https://github.com/iarna/console-control-strings", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\console-control-strings", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\console-control-strings\\LICENSE"}, "cross-spawn@7.0.6": {"licenses": "MIT", "repository": "https://github.com/moxystudio/node-cross-spawn", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\cross-spawn", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\cross-spawn\\LICENSE"}, "crypto-random-string@4.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/crypto-random-string", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\crypto-random-string", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\crypto-random-string\\license"}, "data-uri-to-buffer@6.0.2": {"licenses": "MIT", "repository": "https://github.com/TooTallNate/proxy-agents", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\data-uri-to-buffer", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\data-uri-to-buffer\\LICENSE"}, "debug@3.2.7": {"licenses": "MIT", "repository": "https://github.com/visionmedia/debug", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\license-checker\\node_modules\\debug", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\license-checker\\node_modules\\debug\\LICENSE"}, "debug@4.4.1": {"licenses": "MIT", "repository": "https://github.com/debug-js/debug", "publisher": "<PERSON>", "url": "https://github.com/qix-", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\debug", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\debug\\LICENSE"}, "debuglog@1.0.1": {"licenses": "MIT", "repository": "https://github.com/sam-github/node-debuglog", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\debuglog", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\debuglog\\LICENSE"}, "decompress-response@6.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/decompress-response", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\decompress-response", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\decompress-response\\license"}, "deep-extend@0.6.0": {"licenses": "MIT", "repository": "https://github.com/unclechu/node-deep-extend", "publisher": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\deep-extend", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\deep-extend\\LICENSE"}, "deep-is@0.1.4": {"licenses": "MIT", "repository": "https://github.com/thlorenz/deep-is", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://thlorenz.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\deep-is", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\deep-is\\LICENSE"}, "defaults@1.0.4": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/node-defaults", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\defaults", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\defaults\\LICENSE"}, "defer-to-connect@2.0.1": {"licenses": "MIT", "repository": "https://github.com/szmarczak/defer-to-connect", "publisher": "<PERSON><PERSON><PERSON>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\defer-to-connect", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\defer-to-connect\\LICENSE"}, "define-data-property@1.1.4": {"licenses": "MIT", "repository": "https://github.com/ljharb/define-data-property", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\define-data-property", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\define-data-property\\LICENSE"}, "define-properties@1.2.1": {"licenses": "MIT", "repository": "https://github.com/ljharb/define-properties", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\define-properties", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\define-properties\\LICENSE"}, "defined@1.0.1": {"licenses": "MIT", "repository": "https://github.com/inspect-js/defined", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\defined", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\defined\\LICENSE"}, "degenerator@5.0.1": {"licenses": "MIT", "repository": "https://github.com/TooTallNate/proxy-agents", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\degenerator", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\degenerator\\README.md"}, "delegates@1.0.0": {"licenses": "MIT", "repository": "https://github.com/visionmedia/node-delegates", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\delegates", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\delegates\\License"}, "dependency-check@4.1.0": {"licenses": "BSD-3-<PERSON><PERSON>", "repository": "https://github.com/dependency-check-team/dependency-check", "publisher": "max ogden", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\dependency-check", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\dependency-check\\readme.md"}, "dependency-tree@9.0.0": {"licenses": "MIT", "repository": "https://github.com/mrjoelkemp/node-dependency-tree", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\dependency-tree", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\dependency-tree\\LICENSE"}, "detect-libc@2.0.4": {"licenses": "Apache-2.0", "repository": "https://github.com/lovell/detect-libc", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\detect-libc", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\detect-libc\\LICENSE"}, "detect-node@2.1.0": {"licenses": "MIT", "repository": "https://github.com/iliakan/detect-node", "publisher": "<PERSON><PERSON>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\detect-node", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\detect-node\\LICENSE"}, "detective-amd@3.1.2": {"licenses": "MIT", "repository": "https://github.com/dependents/node-detective-amd", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\precinct\\node_modules\\detective-amd", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\precinct\\node_modules\\detective-amd\\LICENSE"}, "detective-amd@4.2.0": {"licenses": "MIT", "repository": "https://github.com/dependents/node-detective-amd", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\detective-amd", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\detective-amd\\LICENSE"}, "detective-cjs@3.1.3": {"licenses": "MIT", "repository": "https://github.com/dependents/node-detective-cjs", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\precinct\\node_modules\\detective-cjs", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\precinct\\node_modules\\detective-cjs\\LICENSE"}, "detective-cjs@4.1.0": {"licenses": "MIT", "repository": "https://github.com/dependents/node-detective-cjs", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\detective-cjs", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\detective-cjs\\LICENSE"}, "detective-es6@2.2.2": {"licenses": "MIT", "repository": "https://github.com/dependents/node-detective-es6", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\precinct\\node_modules\\detective-es6", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\precinct\\node_modules\\detective-es6\\LICENSE"}, "detective-es6@3.0.1": {"licenses": "MIT", "repository": "https://github.com/dependents/node-detective-es6", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\detective-es6", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\detective-es6\\LICENSE"}, "detective-less@1.0.2": {"licenses": "MIT", "repository": "https://github.com/<PERSON>-Da<PERSON>/node-detective-less", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\detective-less", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\detective-less\\Readme.md"}, "detective-postcss@4.0.0": {"licenses": "Apache-2.0", "repository": "https://github.com/joscha/node-detective-postcss", "publisher": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\precinct\\node_modules\\detective-postcss", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\precinct\\node_modules\\detective-postcss\\LICENSE"}, "detective-postcss@6.1.3": {"licenses": "MIT", "repository": "https://github.com/dependents/node-detective-postcss", "publisher": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\detective-postcss", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\detective-postcss\\LICENSE"}, "detective-sass@3.0.2": {"licenses": "MIT", "repository": "https://github.com/dependents/node-detective-sass", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\precinct\\node_modules\\detective-sass", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\precinct\\node_modules\\detective-sass\\LICENSE"}, "detective-sass@4.1.3": {"licenses": "MIT", "repository": "https://github.com/dependents/node-detective-sass", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\detective-sass", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\detective-sass\\LICENSE"}, "detective-scss@2.0.2": {"licenses": "MIT", "repository": "https://github.com/dependents/node-detective-scss", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\precinct\\node_modules\\detective-scss", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\precinct\\node_modules\\detective-scss\\LICENSE"}, "detective-scss@3.1.1": {"licenses": "MIT", "repository": "https://github.com/dependents/node-detective-scss", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\detective-scss", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\detective-scss\\LICENSE"}, "detective-stylus@1.0.3": {"licenses": "MIT", "repository": "https://github.com/dependents/node-detective-stylus", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://mrjoelkemp.com/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\precinct\\node_modules\\detective-stylus", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\precinct\\node_modules\\detective-stylus\\README.md"}, "detective-stylus@2.0.1": {"licenses": "MIT", "repository": "https://github.com/dependents/node-detective-stylus", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://mrjoelkemp.com/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\detective-stylus", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\detective-stylus\\LICENSE"}, "detective-stylus@3.0.0": {"licenses": "MIT", "repository": "https://github.com/dependents/node-detective-stylus", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\dependency-tree\\node_modules\\detective-stylus", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\dependency-tree\\node_modules\\detective-stylus\\LICENSE"}, "detective-typescript@7.0.2": {"licenses": "MIT", "repository": "https://github.com/pahen/detective-typescript", "publisher": "<PERSON><PERSON>", "email": "patrik.he<PERSON><PERSON>@gmail.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\precinct\\node_modules\\detective-typescript", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\precinct\\node_modules\\detective-typescript\\README.md"}, "detective-typescript@9.1.1": {"licenses": "MIT", "repository": "https://github.com/dependents/detective-typescript", "publisher": "<PERSON><PERSON>", "email": "patrik.he<PERSON><PERSON>@gmail.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\detective-typescript", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\detective-typescript\\README.md"}, "detective@5.2.1": {"licenses": "MIT", "repository": "https://github.com/browserify/detective", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\detective", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\detective\\LICENSE"}, "dezalgo@1.0.4": {"licenses": "ISC", "repository": "https://github.com/npm/dezalgo", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\dezalgo", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\dezalgo\\LICENSE"}, "dir-glob@3.0.1": {"licenses": "MIT", "repository": "https://github.com/kevva/dir-glob", "publisher": "<PERSON>", "email": "kevin<PERSON><PERSON><PERSON>@gmail.com", "url": "github.com/kevva", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\dir-glob", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\dir-glob\\license"}, "discontinuous-range@1.0.0": {"licenses": "MIT", "repository": "https://github.com/dtudury/discontinuous-range", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\discontinuous-range", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\discontinuous-range\\LICENSE"}, "doctrine@3.0.0": {"licenses": "Apache-2.0", "repository": "https://github.com/eslint/doctrine", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\doctrine", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\doctrine\\LICENSE"}, "dot-prop@6.0.1": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/dot-prop", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\dot-prop", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\dot-prop\\license"}, "duplexer@0.1.2": {"licenses": "MIT", "repository": "https://github.com/Raynos/duplexer", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\duplexer", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\duplexer\\LICENCE"}, "eastasianwidth@0.2.0": {"licenses": "MIT", "repository": "https://github.com/komagata/eastasianwidth", "publisher": "<PERSON><PERSON>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\eastasianwidth", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\eastasianwidth\\README.md"}, "emoji-regex@10.4.0": {"licenses": "MIT", "repository": "https://github.com/mathiasbynens/emoji-regex", "publisher": "<PERSON>", "url": "https://mathiasbynens.be/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\ora\\node_modules\\emoji-regex", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\ora\\node_modules\\emoji-regex\\LICENSE-MIT.txt"}, "emoji-regex@8.0.0": {"licenses": "MIT", "repository": "https://github.com/mathiasbynens/emoji-regex", "publisher": "<PERSON>", "url": "https://mathiasbynens.be/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\emoji-regex", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\emoji-regex\\LICENSE-MIT.txt"}, "emoji-regex@9.2.2": {"licenses": "MIT", "repository": "https://github.com/mathiasbynens/emoji-regex", "publisher": "<PERSON>", "url": "https://mathiasbynens.be/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@isaacs\\cliui\\node_modules\\emoji-regex", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@isaacs\\cliui\\node_modules\\emoji-regex\\LICENSE-MIT.txt"}, "encoding@0.1.13": {"licenses": "MIT", "repository": "https://github.com/andris9/encoding", "publisher": "<PERSON><PERSON>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\encoding", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\encoding\\LICENSE"}, "end-of-stream@1.4.5": {"licenses": "MIT", "repository": "https://github.com/mafintosh/end-of-stream", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\end-of-stream", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\end-of-stream\\LICENSE"}, "enhanced-resolve@5.18.2": {"licenses": "MIT", "repository": "https://github.com/webpack/enhanced-resolve", "publisher": "<PERSON> @sokra", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\enhanced-resolve", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\enhanced-resolve\\LICENSE"}, "env-paths@2.2.1": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/env-paths", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\env-paths", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\env-paths\\license"}, "err-code@2.0.3": {"licenses": "MIT", "repository": "https://github.com/IndigoUnited/js-err-code", "publisher": "IndigoUnited", "email": "<EMAIL>", "url": "http://indigounited.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\err-code", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\err-code\\README.md"}, "es-define-property@1.0.1": {"licenses": "MIT", "repository": "https://github.com/ljharb/es-define-property", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\es-define-property", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\es-define-property\\LICENSE"}, "es-errors@1.3.0": {"licenses": "MIT", "repository": "https://github.com/ljharb/es-errors", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\es-errors", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\es-errors\\LICENSE"}, "es6-error@4.1.1": {"licenses": "MIT", "repository": "https://github.com/bjyoungblood/es6-error", "publisher": "<PERSON>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\es6-error", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\es6-error\\LICENSE.md"}, "escalade@3.2.0": {"licenses": "MIT", "repository": "https://github.com/lukeed/escalade", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://lukeed.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\escalade", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\escalade\\license"}, "escape-goat@4.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/escape-goat", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\escape-goat", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\escape-goat\\license"}, "escape-string-regexp@1.0.5": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/escape-string-regexp", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\license-checker\\node_modules\\escape-string-regexp", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\license-checker\\node_modules\\escape-string-regexp\\license"}, "escape-string-regexp@4.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/escape-string-regexp", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\escape-string-regexp", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\escape-string-regexp\\license"}, "escodegen@2.1.0": {"licenses": "BSD-2-<PERSON><PERSON>", "repository": "https://github.com/estools/escodegen", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\escodegen", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\escodegen\\LICENSE.BSD"}, "eslint-plugin-security@1.7.1": {"licenses": "Apache-2.0", "repository": "https://github.com/eslint-community/eslint-plugin-security", "publisher": "Node Security Project", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\eslint-plugin-security", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\eslint-plugin-security\\LICENSE"}, "eslint-scope@7.2.2": {"licenses": "BSD-2-<PERSON><PERSON>", "repository": "https://github.com/eslint/eslint-scope", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\eslint-scope", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\eslint-scope\\LICENSE"}, "eslint-visitor-keys@2.1.0": {"licenses": "Apache-2.0", "repository": "https://github.com/eslint/eslint-visitor-keys", "publisher": "<PERSON><PERSON>", "url": "https://github.com/mysticatea", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\precinct\\node_modules\\eslint-visitor-keys", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\precinct\\node_modules\\eslint-visitor-keys\\LICENSE"}, "eslint-visitor-keys@3.4.3": {"licenses": "Apache-2.0", "repository": "https://github.com/eslint/eslint-visitor-keys", "publisher": "<PERSON><PERSON>", "url": "https://github.com/mysticatea", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\eslint-visitor-keys", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\eslint-visitor-keys\\LICENSE"}, "eslint@8.57.1": {"licenses": "MIT", "repository": "https://github.com/eslint/eslint", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\eslint", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\eslint\\LICENSE"}, "espree@9.6.1": {"licenses": "BSD-2-<PERSON><PERSON>", "repository": "https://github.com/eslint/espree", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\espree", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\espree\\LICENSE"}, "esprima@4.0.1": {"licenses": "BSD-2-<PERSON><PERSON>", "repository": "https://github.com/jquery/esprima", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\esprima", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\esprima\\LICENSE.BSD"}, "esquery@1.6.0": {"licenses": "BSD-3-<PERSON><PERSON>", "repository": "https://github.com/estools/esquery", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\esquery", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\esquery\\license.txt"}, "esrecurse@4.3.0": {"licenses": "BSD-2-<PERSON><PERSON>", "repository": "https://github.com/estools/esrecurse", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\esrecurse", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\esrecurse\\README.md"}, "estraverse@5.3.0": {"licenses": "BSD-2-<PERSON><PERSON>", "repository": "https://github.com/estools/estraverse", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\estraverse", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\estraverse\\LICENSE.BSD"}, "esutils@2.0.3": {"licenses": "BSD-2-<PERSON><PERSON>", "repository": "https://github.com/estools/esutils", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\esutils", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\esutils\\LICENSE.BSD"}, "event-stream@4.0.1": {"licenses": "MIT", "repository": "https://github.com/dominictarr/event-stream", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://bit.ly/dominictarr", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\event-stream", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\event-stream\\LICENCE"}, "expand-template@2.0.3": {"licenses": "(MIT OR WTFPL)", "repository": "https://github.com/ralphtheninja/expand-template", "publisher": "LM", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\expand-template", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\expand-template\\LICENSE"}, "exponential-backoff@3.1.2": {"licenses": "Apache-2.0", "repository": "https://github.com/coveooss/exponential-backoff", "publisher": "<PERSON>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\exponential-backoff", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\exponential-backoff\\LICENSE"}, "extend@3.0.2": {"licenses": "MIT", "repository": "https://github.com/justmoon/node-extend", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://www.justmoon.net", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\extend", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\extend\\LICENSE"}, "external-editor@3.1.0": {"licenses": "MIT", "repository": "https://github.com/mrkmg/node-external-editor", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://mrkmg.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\external-editor", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\external-editor\\LICENSE"}, "fast-deep-equal@3.1.3": {"licenses": "MIT", "repository": "https://github.com/epoberezkin/fast-deep-equal", "publisher": "<PERSON><PERSON><PERSON>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\fast-deep-equal", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\fast-deep-equal\\LICENSE"}, "fast-glob@3.3.3": {"licenses": "MIT", "repository": "https://github.com/mrmlnc/fast-glob", "publisher": "<PERSON>", "url": "https://mrmlnc.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\fast-glob", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\fast-glob\\LICENSE"}, "fast-json-stable-stringify@2.1.0": {"licenses": "MIT", "repository": "https://github.com/epoberezkin/fast-json-stable-stringify", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\fast-json-stable-stringify", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\fast-json-stable-stringify\\LICENSE"}, "fast-levenshtein@2.0.6": {"licenses": "MIT", "repository": "https://github.com/hiddentao/fast-levenshtein", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.hiddentao.com/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\fast-levenshtein", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\fast-levenshtein\\LICENSE.md"}, "fast-memoize@2.5.2": {"licenses": "MIT", "repository": "https://github.com/caiogondim/fast-memoize", "publisher": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://caiogondim.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\fast-memoize", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\fast-memoize\\LICENSE"}, "fast-uri@3.0.6": {"licenses": "BSD-3-<PERSON><PERSON>", "repository": "https://github.com/fastify/fast-uri", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/zekth", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\fast-uri", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\fast-uri\\LICENSE"}, "fastq@1.19.1": {"licenses": "ISC", "repository": "https://github.com/mcollina/fastq", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\fastq", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\fastq\\LICENSE"}, "file-entry-cache@6.0.1": {"licenses": "MIT", "repository": "https://github.com/royriojas/file-entry-cache", "publisher": "<PERSON>", "url": "http://royriojas.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\file-entry-cache", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\file-entry-cache\\LICENSE"}, "file-uri-to-path@1.0.0": {"licenses": "MIT", "repository": "https://github.com/TooTallNate/file-uri-to-path", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\file-uri-to-path", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\file-uri-to-path\\LICENSE"}, "filing-cabinet@3.3.1": {"licenses": "MIT", "repository": "https://github.com/mrjoelkemp/node-filing-cabinet", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://www.mrjoelkemp.com/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\filing-cabinet", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\filing-cabinet\\readme.md"}, "fill-range@7.1.1": {"licenses": "MIT", "repository": "https://github.com/jonschlinkert/fill-range", "publisher": "<PERSON>", "url": "https://github.com/jonschlinkert", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\fill-range", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\fill-range\\LICENSE"}, "find-up@3.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/find-up", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\pkg-up\\node_modules\\find-up", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\pkg-up\\node_modules\\find-up\\license"}, "find-up@5.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/find-up", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\find-up", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\find-up\\license"}, "flat-cache@3.2.0": {"licenses": "MIT", "repository": "https://github.com/jaredwray/flat-cache", "publisher": "<PERSON>", "url": "https://jaredwray.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\flat-cache", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\flat-cache\\LICENSE"}, "flatted@3.3.3": {"licenses": "ISC", "repository": "https://github.com/WebReflection/flatted", "publisher": "<PERSON>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\flatted", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\flatted\\LICENSE"}, "flatten@1.0.3": {"licenses": "MIT", "repository": "https://github.com/mk-pmb/flatten-js", "publisher": "<PERSON>", "email": "josh.hol<PERSON>@gmail.com", "url": "http://jesusabdullah.net", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\flatten", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\flatten\\LICENSE"}, "foreground-child@3.3.1": {"licenses": "ISC", "repository": "https://github.com/tapjs/foreground-child", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\foreground-child", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\foreground-child\\LICENSE"}, "form-data-encoder@2.1.4": {"licenses": "MIT", "repository": "https://github.com/octet-stream/form-data-encoder", "publisher": "<PERSON>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\form-data-encoder", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\form-data-encoder\\license"}, "fp-and-or@0.1.4": {"licenses": "ISC", "repository": "https://github.com/raineorshine/fp-and-or", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/raineorshine", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\fp-and-or", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\fp-and-or\\README.md"}, "from@0.1.7": {"licenses": "MIT", "repository": "https://github.com/dominictarr/from", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\from", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\from\\LICENSE.APACHE2"}, "fs-constants@1.0.0": {"licenses": "MIT", "repository": "https://github.com/mafintosh/fs-constants", "publisher": "<PERSON>", "url": "@mafintosh", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\fs-constants", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\fs-constants\\LICENSE"}, "fs-extra@11.3.0": {"licenses": "MIT", "repository": "https://github.com/jprichardson/node-fs-extra", "publisher": "<PERSON>", "email": "jp<PERSON><PERSON><EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\fs-extra", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\fs-extra\\LICENSE"}, "fs-minipass@2.1.0": {"licenses": "ISC", "repository": "https://github.com/npm/fs-minipass", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\tar\\node_modules\\fs-minipass", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\tar\\node_modules\\fs-minipass\\LICENSE"}, "fs-minipass@3.0.3": {"licenses": "ISC", "repository": "https://github.com/npm/fs-minipass", "publisher": "GitHub Inc.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\fs-minipass", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\fs-minipass\\LICENSE"}, "fs.realpath@1.0.0": {"licenses": "ISC", "repository": "https://github.com/isaacs/fs.realpath", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\fs.realpath", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\fs.realpath\\LICENSE"}, "function-bind@1.1.2": {"licenses": "MIT", "repository": "https://github.com/Raynos/function-bind", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\function-bind", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\function-bind\\LICENSE"}, "gauge@4.0.4": {"licenses": "ISC", "repository": "https://github.com/npm/gauge", "publisher": "GitHub Inc.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\gauge", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\gauge\\LICENSE.md"}, "get-amd-module-type@3.0.2": {"licenses": "MIT", "repository": "https://github.com/dependents/node-get-amd-module-type", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\precinct\\node_modules\\get-amd-module-type", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\precinct\\node_modules\\get-amd-module-type\\LICENSE"}, "get-amd-module-type@4.1.0": {"licenses": "MIT", "repository": "https://github.com/dependents/node-get-amd-module-type", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\get-amd-module-type", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\get-amd-module-type\\LICENSE"}, "get-caller-file@2.0.5": {"licenses": "ISC", "repository": "https://github.com/stefanpenner/get-caller-file", "publisher": "<PERSON>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\get-caller-file", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\get-caller-file\\LICENSE.md"}, "get-own-enumerable-property-symbols@3.0.2": {"licenses": "ISC", "repository": "https://github.com/mightyiam/get-own-enumerable-property-symbols", "publisher": "Shahar Or", "email": "<EMAIL>", "url": "mightyiam", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\get-own-enumerable-property-symbols", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\get-own-enumerable-property-symbols\\LICENSE"}, "get-stdin@8.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/get-stdin", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\get-stdin", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\get-stdin\\license"}, "get-stream@6.0.1": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/get-stream", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\get-stream", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\get-stream\\license"}, "get-uri@6.0.4": {"licenses": "MIT", "repository": "https://github.com/TooTallNate/proxy-agents", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\get-uri", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\get-uri\\LICENSE"}, "github-from-package@0.0.0": {"licenses": "MIT", "repository": "https://github.com/substack/github-from-package", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\github-from-package", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\github-from-package\\LICENSE"}, "glob-parent@5.1.2": {"licenses": "ISC", "repository": "https://github.com/gulpjs/glob-parent", "publisher": "Gulp Team", "email": "<EMAIL>", "url": "https://gulpjs.com/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\fast-glob\\node_modules\\glob-parent", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\fast-glob\\node_modules\\glob-parent\\LICENSE"}, "glob-parent@6.0.2": {"licenses": "ISC", "repository": "https://github.com/gulpjs/glob-parent", "publisher": "Gulp Team", "email": "<EMAIL>", "url": "https://gulpjs.com/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\glob-parent", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\glob-parent\\LICENSE"}, "glob@10.4.5": {"licenses": "ISC", "repository": "https://github.com/isaacs/node-glob", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://blog.izs.me/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\glob", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\glob\\LICENSE"}, "glob@7.2.3": {"licenses": "ISC", "repository": "https://github.com/isaacs/node-glob", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\rimraf\\node_modules\\glob", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\rimraf\\node_modules\\glob\\LICENSE"}, "glob@8.1.0": {"licenses": "ISC", "repository": "https://github.com/isaacs/node-glob", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@npmcli\\run-script\\node_modules\\cacache\\node_modules\\glob", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@npmcli\\run-script\\node_modules\\cacache\\node_modules\\glob\\LICENSE"}, "global-agent@3.0.0": {"licenses": "BSD-3-<PERSON><PERSON>", "repository": "https://github.com/gajus/global-agent", "publisher": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://gajus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\global-agent", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\global-agent\\LICENSE"}, "global-dirs@3.0.1": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/global-dirs", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\global-dirs", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\global-dirs\\license"}, "globals@13.24.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/globals", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\globals", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\globals\\license"}, "globalthis@1.0.4": {"licenses": "MIT", "repository": "https://github.com/ljharb/System.global", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\globalthis", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\globalthis\\LICENSE"}, "globby@10.0.2": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/globby", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\globby", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\globby\\license"}, "globby@11.1.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/globby", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@typescript-eslint\\typescript-estree\\node_modules\\globby", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@typescript-eslint\\typescript-estree\\node_modules\\globby\\license"}, "gonzales-pe@4.3.0": {"licenses": "MIT", "repository": "https://github.com/tonyganch/gonzales-pe", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://tonyganch.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\gonzales-pe", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\gonzales-pe\\README.md"}, "gopd@1.2.0": {"licenses": "MIT", "repository": "https://github.com/ljharb/gopd", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\gopd", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\gopd\\LICENSE"}, "got@12.6.1": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/got", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\got", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\got\\license"}, "graceful-fs@4.2.10": {"licenses": "ISC", "repository": "https://github.com/isaacs/node-graceful-fs", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@pnpm\\network.ca-file\\node_modules\\graceful-fs", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@pnpm\\network.ca-file\\node_modules\\graceful-fs\\LICENSE"}, "graceful-fs@4.2.11": {"licenses": "ISC", "repository": "https://github.com/isaacs/node-graceful-fs", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\graceful-fs", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\graceful-fs\\LICENSE"}, "graphemer@1.4.0": {"licenses": "MIT", "repository": "https://github.com/flmnt/graphemer", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/mattpauldavies", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\graphemer", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\graphemer\\LICENSE"}, "has-flag@3.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/has-flag", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\license-checker\\node_modules\\has-flag", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\license-checker\\node_modules\\has-flag\\license"}, "has-flag@4.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/has-flag", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\has-flag", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\has-flag\\license"}, "has-property-descriptors@1.0.2": {"licenses": "MIT", "repository": "https://github.com/inspect-js/has-property-descriptors", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\has-property-descriptors", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\has-property-descriptors\\LICENSE"}, "has-unicode@2.0.1": {"licenses": "ISC", "repository": "https://github.com/iarna/has-unicode", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\has-unicode", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\has-unicode\\LICENSE"}, "has-yarn@3.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/has-yarn", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\has-yarn", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\has-yarn\\license"}, "hasown@2.0.2": {"licenses": "MIT", "repository": "https://github.com/inspect-js/hasOwn", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\hasown", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\hasown\\LICENSE"}, "hosted-git-info@2.8.9": {"licenses": "ISC", "repository": "https://github.com/npm/hosted-git-info", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\read-package-json\\node_modules\\hosted-git-info", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\read-package-json\\node_modules\\hosted-git-info\\LICENSE"}, "hosted-git-info@5.2.1": {"licenses": "ISC", "repository": "https://github.com/npm/hosted-git-info", "publisher": "GitHub Inc.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\npm-check-updates\\node_modules\\hosted-git-info", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\npm-check-updates\\node_modules\\hosted-git-info\\LICENSE"}, "hosted-git-info@6.1.3": {"licenses": "ISC", "repository": "https://github.com/npm/hosted-git-info", "publisher": "GitHub Inc.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\npm-package-arg\\node_modules\\hosted-git-info", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\npm-package-arg\\node_modules\\hosted-git-info\\LICENSE"}, "hosted-git-info@7.0.2": {"licenses": "ISC", "repository": "https://github.com/npm/hosted-git-info", "publisher": "GitHub Inc.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\hosted-git-info", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\hosted-git-info\\LICENSE"}, "http-cache-semantics@4.2.0": {"licenses": "BSD-2-<PERSON><PERSON>", "repository": "https://github.com/kornelski/http-cache-semantics", "publisher": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://kornel.ski/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\http-cache-semantics", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\http-cache-semantics\\LICENSE"}, "http-proxy-agent@5.0.0": {"licenses": "MIT", "repository": "https://github.com/TooTallNate/node-http-proxy-agent", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\tuf-js\\node_modules\\http-proxy-agent", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\tuf-js\\node_modules\\http-proxy-agent\\README.md"}, "http-proxy-agent@7.0.2": {"licenses": "MIT", "repository": "https://github.com/TooTallNate/proxy-agents", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\http-proxy-agent", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\http-proxy-agent\\LICENSE"}, "http2-wrapper@2.2.1": {"licenses": "MIT", "repository": "https://github.com/szmarczak/http2-wrapper", "publisher": "<PERSON><PERSON><PERSON>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\http2-wrapper", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\http2-wrapper\\LICENSE"}, "https-proxy-agent@5.0.1": {"licenses": "MIT", "repository": "https://github.com/TooTallNate/node-https-proxy-agent", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\tuf-js\\node_modules\\https-proxy-agent", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\tuf-js\\node_modules\\https-proxy-agent\\README.md"}, "https-proxy-agent@7.0.6": {"licenses": "MIT", "repository": "https://github.com/TooTallNate/proxy-agents", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\https-proxy-agent", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\https-proxy-agent\\LICENSE"}, "humanize-ms@1.2.1": {"licenses": "MIT", "repository": "https://github.com/node-modules/humanize-ms", "publisher": "dead-horse", "email": "<EMAIL>", "url": "http://deadhorse.me", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\humanize-ms", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\humanize-ms\\LICENSE"}, "iconv-lite@0.4.24": {"licenses": "MIT", "repository": "https://github.com/ashtuchkin/iconv-lite", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\iconv-lite", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\iconv-lite\\LICENSE"}, "iconv-lite@0.6.3": {"licenses": "MIT", "repository": "https://github.com/ashtuchkin/iconv-lite", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\encoding\\node_modules\\iconv-lite", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\encoding\\node_modules\\iconv-lite\\LICENSE"}, "ieee754@1.2.1": {"licenses": "BSD-3-<PERSON><PERSON>", "repository": "https://github.com/feross/ieee754", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://feross.org", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\ieee754", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\ieee754\\LICENSE"}, "ignore-walk@6.0.5": {"licenses": "ISC", "repository": "https://github.com/npm/ignore-walk", "publisher": "GitHub Inc.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\ignore-walk", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\ignore-walk\\LICENSE"}, "ignore@5.3.2": {"licenses": "MIT", "repository": "https://github.com/kaelzhang/node-ignore", "publisher": "kael", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\ignore", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\ignore\\LICENSE-MIT"}, "immediate@3.0.6": {"licenses": "MIT", "repository": "https://github.com/calvinmetcalf/immediate", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\immediate", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\immediate\\LICENSE.txt"}, "import-fresh@3.3.1": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/import-fresh", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\import-fresh", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\import-fresh\\license"}, "import-lazy@4.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/import-lazy", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\import-lazy", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\import-lazy\\license"}, "imurmurhash@0.1.4": {"licenses": "MIT", "repository": "https://github.com/jensyt/imurmurhash-js", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/homebrewing", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\imurmurhash", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\imurmurhash\\README.md"}, "indent-string@4.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/indent-string", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\indent-string", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\indent-string\\license"}, "indexes-of@1.0.1": {"licenses": "MIT", "repository": "https://github.com/dominictarr/indexes-of", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\indexes-of", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\indexes-of\\LICENSE"}, "infer-owner@1.0.4": {"licenses": "ISC", "repository": "https://github.com/npm/infer-owner", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://izs.me", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\infer-owner", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\infer-owner\\LICENSE"}, "inflight@1.0.6": {"licenses": "ISC", "repository": "https://github.com/npm/inflight", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\inflight", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\inflight\\LICENSE"}, "inherits@2.0.4": {"licenses": "ISC", "repository": "https://github.com/isaacs/inherits", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\inherits", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\inherits\\LICENSE"}, "ini@1.3.8": {"licenses": "ISC", "repository": "https://github.com/isaacs/ini", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\config-chain\\node_modules\\ini", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\config-chain\\node_modules\\ini\\LICENSE"}, "ini@2.0.0": {"licenses": "ISC", "repository": "https://github.com/isaacs/ini", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\global-dirs\\node_modules\\ini", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\global-dirs\\node_modules\\ini\\LICENSE"}, "ini@4.1.3": {"licenses": "ISC", "repository": "https://github.com/npm/ini", "publisher": "GitHub Inc.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\ini", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\ini\\LICENSE"}, "inquirer@9.3.7": {"licenses": "MIT", "repository": "https://github.com/SBoudrias/Inquirer.js", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\inquirer", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\inquirer\\LICENSE"}, "ip-address@9.0.5": {"licenses": "MIT", "repository": "https://github.com/beaugunderson/ip-address", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://beaugunderson.com/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\ip-address", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\ip-address\\LICENSE"}, "is-ci@3.0.1": {"licenses": "MIT", "repository": "https://github.com/watson/is-ci", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://twitter.com/wa7son", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\is-ci", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\is-ci\\LICENSE"}, "is-core-module@2.16.1": {"licenses": "MIT", "repository": "https://github.com/inspect-js/is-core-module", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\is-core-module", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\is-core-module\\LICENSE"}, "is-extglob@2.1.1": {"licenses": "MIT", "repository": "https://github.com/jonschlinkert/is-extglob", "publisher": "<PERSON>", "url": "https://github.com/jonschlinkert", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\is-extglob", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\is-extglob\\LICENSE"}, "is-fullwidth-code-point@3.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/is-fullwidth-code-point", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\is-fullwidth-code-point", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\is-fullwidth-code-point\\license"}, "is-glob@4.0.3": {"licenses": "MIT", "repository": "https://github.com/micromatch/is-glob", "publisher": "<PERSON>", "url": "https://github.com/jonschlinkert", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\is-glob", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\is-glob\\LICENSE"}, "is-installed-globally@0.4.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/is-installed-globally", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\is-installed-globally", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\is-installed-globally\\license"}, "is-interactive@1.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/is-interactive", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\inquirer\\node_modules\\is-interactive", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\inquirer\\node_modules\\is-interactive\\license"}, "is-interactive@2.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/is-interactive", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\is-interactive", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\is-interactive\\license"}, "is-lambda@1.0.1": {"licenses": "MIT", "repository": "https://github.com/watson/is-lambda", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://twitter.com/wa7son", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\is-lambda", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\is-lambda\\LICENSE"}, "is-npm@6.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/is-npm", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\is-npm", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\is-npm\\license"}, "is-number@7.0.0": {"licenses": "MIT", "repository": "https://github.com/jonschlinkert/is-number", "publisher": "<PERSON>", "url": "https://github.com/jonschlinkert", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\is-number", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\is-number\\LICENSE"}, "is-obj@1.0.1": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/is-obj", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\is-obj", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\is-obj\\license"}, "is-obj@2.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/is-obj", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\dot-prop\\node_modules\\is-obj", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\dot-prop\\node_modules\\is-obj\\license"}, "is-path-inside@3.0.3": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/is-path-inside", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\is-path-inside", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\is-path-inside\\license"}, "is-regexp@1.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/is-regexp", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\is-regexp", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\is-regexp\\readme.md"}, "is-relative-path@1.0.2": {"licenses": "MIT", "repository": "https://github.com/mrjoelkemp/is-relative-path", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://www.mrjoelkemp.com/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\is-relative-path", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\is-relative-path\\Readme.md"}, "is-relative@1.0.0": {"licenses": "MIT", "repository": "https://github.com/jonschlinkert/is-relative", "publisher": "<PERSON>", "url": "https://github.com/jonschlinkert", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\is-relative", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\is-relative\\LICENSE"}, "is-typedarray@1.0.0": {"licenses": "MIT", "repository": "https://github.com/hughsk/is-typedarray", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://hughsk.io/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\is-typedarray", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\is-typedarray\\LICENSE.md"}, "is-unc-path@1.0.0": {"licenses": "MIT", "repository": "https://github.com/jonschlinkert/is-unc-path", "publisher": "<PERSON>", "url": "https://github.com/jonschlinkert", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\is-unc-path", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\is-unc-path\\LICENSE"}, "is-unicode-supported@0.1.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/is-unicode-supported", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\inquirer\\node_modules\\is-unicode-supported", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\inquirer\\node_modules\\is-unicode-supported\\license"}, "is-unicode-supported@1.3.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/is-unicode-supported", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\is-unicode-supported", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\is-unicode-supported\\license"}, "is-url-superb@4.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/is-url-superb", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\is-url-superb", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\is-url-superb\\license"}, "is-url@1.2.4": {"licenses": "MIT", "repository": "https://github.com/segmentio/is-url", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\is-url", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\is-url\\LICENSE-MIT"}, "is-yarn-global@0.4.1": {"licenses": "MIT", "repository": "https://github.com/LitoMore/is-yarn-global", "publisher": "LitoMore", "url": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\is-yarn-global", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\is-yarn-global\\LICENSE"}, "isexe@2.0.0": {"licenses": "ISC", "repository": "https://github.com/isaacs/isexe", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\isexe", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\isexe\\LICENSE"}, "isexe@3.1.1": {"licenses": "ISC", "repository": "https://github.com/isaacs/isexe", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\node-gyp\\node_modules\\isexe", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\node-gyp\\node_modules\\isexe\\LICENSE"}, "jackspeak@3.4.3": {"licenses": "BlueOak-1.0.0", "repository": "https://github.com/isaacs/jackspeak", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\jackspeak", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\jackspeak\\LICENSE.md"}, "jju@1.4.0": {"licenses": "MIT", "repository": "https://github.com/rlidwka/jju", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\jju", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\jju\\LICENSE"}, "js-yaml@3.14.1": {"licenses": "MIT", "repository": "https://github.com/nodeca/js-yaml", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\xmlbuilder2\\node_modules\\js-yaml", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\xmlbuilder2\\node_modules\\js-yaml\\LICENSE"}, "js-yaml@4.1.0": {"licenses": "MIT", "repository": "https://github.com/nodeca/js-yaml", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\js-yaml", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\js-yaml\\LICENSE"}, "jsbn@1.1.0": {"licenses": "MIT", "repository": "https://github.com/andyperlitch/jsbn", "publisher": "<PERSON>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\jsbn", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\jsbn\\LICENSE"}, "json-buffer@3.0.1": {"licenses": "MIT", "repository": "https://github.com/dominictarr/json-buffer", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://dominictarr.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\json-buffer", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\json-buffer\\LICENSE"}, "json-parse-even-better-errors@2.3.1": {"licenses": "MIT", "repository": "https://github.com/npm/json-parse-even-better-errors", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\json-parse-even-better-errors", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\json-parse-even-better-errors\\LICENSE.md"}, "json-parse-even-better-errors@3.0.2": {"licenses": "MIT", "repository": "https://github.com/npm/json-parse-even-better-errors", "publisher": "GitHub Inc.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\read-package-json-fast\\node_modules\\json-parse-even-better-errors", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\read-package-json-fast\\node_modules\\json-parse-even-better-errors\\LICENSE.md"}, "json-parse-helpfulerror@1.0.3": {"licenses": "MIT", "repository": "https://github.com/smikes/json-parse-helpfulerror", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\json-parse-helpfulerror", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\json-parse-helpfulerror\\LICENSE"}, "json-schema-traverse@0.4.1": {"licenses": "MIT", "repository": "https://github.com/epoberezkin/json-schema-traverse", "publisher": "<PERSON><PERSON><PERSON>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@eslint\\eslintrc\\node_modules\\json-schema-traverse", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@eslint\\eslintrc\\node_modules\\json-schema-traverse\\LICENSE"}, "json-schema-traverse@1.0.0": {"licenses": "MIT", "repository": "https://github.com/epoberezkin/json-schema-traverse", "publisher": "<PERSON><PERSON><PERSON>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\json-schema-traverse", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\json-schema-traverse\\LICENSE"}, "json-stable-stringify-without-jsonify@1.0.1": {"licenses": "MIT", "repository": "https://github.com/samn/json-stable-stringify", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\json-stable-stringify-without-jsonify", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\json-stable-stringify-without-jsonify\\LICENSE"}, "json-stringify-safe@5.0.1": {"licenses": "ISC", "repository": "https://github.com/isaacs/json-stringify-safe", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\json-stringify-safe", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\json-stringify-safe\\LICENSE"}, "json5@1.0.2": {"licenses": "MIT", "repository": "https://github.com/json5/json5", "publisher": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\tsconfig-paths\\node_modules\\json5", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\tsconfig-paths\\node_modules\\json5\\LICENSE.md"}, "json5@2.2.3": {"licenses": "MIT", "repository": "https://github.com/json5/json5", "publisher": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\json5", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\json5\\LICENSE.md"}, "jsonfile@6.1.0": {"licenses": "MIT", "repository": "https://github.com/jprichardson/node-jsonfile", "publisher": "<PERSON>", "email": "jp<PERSON><PERSON><EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\jsonfile", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\jsonfile\\LICENSE"}, "jsonlines@0.1.1": {"licenses": "MIT", "repository": "https://github.com/LinusU/node-jsonlines", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\jsonlines", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\jsonlines\\README.md"}, "jsonparse@1.3.1": {"licenses": "MIT", "repository": "https://github.com/creationix/jsonparse", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\jsonparse", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\jsonparse\\LICENSE"}, "keyv@4.5.4": {"licenses": "MIT", "repository": "https://github.com/jaredwray/keyv", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://jaredwray.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\keyv", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\keyv\\README.md"}, "kleur@4.1.5": {"licenses": "MIT", "repository": "https://github.com/lukeed/kleur", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://lukeed.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\kleur", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\kleur\\license"}, "latest-version@7.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/latest-version", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\latest-version", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\latest-version\\license"}, "levn@0.4.1": {"licenses": "MIT", "repository": "https://github.com/gkz/levn", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\levn", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\levn\\LICENSE"}, "libxmljs2@0.35.0": {"licenses": "MIT", "repository": "https://github.com/marudor/libxmljs2", "publisher": "marudor", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\libxmljs2", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\libxmljs2\\LICENSE"}, "license-checker@25.0.1": {"licenses": "BSD-3-<PERSON><PERSON>", "repository": "https://github.com/davglass/license-checker", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\license-checker", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\license-checker\\LICENSE"}, "lie@3.1.1": {"licenses": "MIT", "repository": "https://github.com/calvinmetcalf/lie", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\lie", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\lie\\license.md"}, "localforage@1.10.0": {"licenses": "Apache-2.0", "repository": "https://github.com/localForage/localForage", "publisher": "Mozilla", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\localforage", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\localforage\\LICENSE"}, "locate-path@3.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/locate-path", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\pkg-up\\node_modules\\locate-path", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\pkg-up\\node_modules\\locate-path\\license"}, "locate-path@6.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/locate-path", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\locate-path", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\locate-path\\license"}, "lodash.merge@4.6.2": {"licenses": "MIT", "repository": "https://github.com/lodash/lodash", "publisher": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\lodash.merge", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\lodash.merge\\LICENSE"}, "lodash@4.17.21": {"licenses": "MIT", "repository": "https://github.com/lodash/lodash", "publisher": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\lodash", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\lodash\\LICENSE"}, "log-symbols@4.1.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/log-symbols", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\inquirer\\node_modules\\log-symbols", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\inquirer\\node_modules\\log-symbols\\license"}, "log-symbols@5.1.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/log-symbols", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\log-symbols", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\log-symbols\\license"}, "lowercase-keys@3.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/lowercase-keys", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\lowercase-keys", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\lowercase-keys\\license"}, "lru-cache@10.4.3": {"licenses": "ISC", "repository": "https://github.com/isaacs/node-lru-cache", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\lru-cache", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\lru-cache\\LICENSE"}, "lru-cache@7.18.3": {"licenses": "ISC", "repository": "https://github.com/isaacs/node-lru-cache", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\tuf-js\\node_modules\\lru-cache", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\tuf-js\\node_modules\\lru-cache\\LICENSE"}, "madge@6.1.0": {"licenses": "MIT", "repository": "https://github.com/pahen/madge", "publisher": "<PERSON><PERSON>", "email": "patrik.he<PERSON><PERSON>@gmail.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\madge", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\madge\\LICENSE"}, "make-fetch-happen@10.2.1": {"licenses": "ISC", "repository": "https://github.com/npm/make-fetch-happen", "publisher": "GitHub Inc.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@npmcli\\run-script\\node_modules\\make-fetch-happen", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@npmcli\\run-script\\node_modules\\make-fetch-happen\\LICENSE"}, "make-fetch-happen@11.1.1": {"licenses": "ISC", "repository": "https://github.com/npm/make-fetch-happen", "publisher": "GitHub Inc.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\tuf-js\\node_modules\\make-fetch-happen", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\tuf-js\\node_modules\\make-fetch-happen\\LICENSE"}, "make-fetch-happen@13.0.1": {"licenses": "ISC", "repository": "https://github.com/npm/make-fetch-happen", "publisher": "GitHub Inc.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\make-fetch-happen", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\make-fetch-happen\\LICENSE"}, "map-stream@0.0.7": {"licenses": "MIT", "repository": "https://github.com/dominictarr/map-stream", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://dominictarr.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\map-stream", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\map-stream\\LICENCE"}, "matcher@3.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/matcher", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\matcher", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\matcher\\license"}, "merge2@1.4.1": {"licenses": "MIT", "repository": "https://github.com/teambition/merge2", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\merge2", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\merge2\\LICENSE"}, "micromatch@4.0.8": {"licenses": "MIT", "repository": "https://github.com/micromatch/micromatch", "publisher": "<PERSON>", "url": "https://github.com/jonschlinkert", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\micromatch", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\micromatch\\LICENSE"}, "mimic-fn@2.1.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/mimic-fn", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\mimic-fn", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\mimic-fn\\license"}, "mimic-response@3.1.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/mimic-response", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\mimic-response", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\mimic-response\\license"}, "mimic-response@4.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/mimic-response", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\cacheable-request\\node_modules\\mimic-response", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\cacheable-request\\node_modules\\mimic-response\\license"}, "minimatch@3.1.2": {"licenses": "ISC", "repository": "https://github.com/isaacs/minimatch", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\minimatch", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\minimatch\\LICENSE"}, "minimatch@5.1.6": {"licenses": "ISC", "repository": "https://github.com/isaacs/minimatch", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@npmcli\\run-script\\node_modules\\cacache\\node_modules\\minimatch", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@npmcli\\run-script\\node_modules\\cacache\\node_modules\\minimatch\\LICENSE"}, "minimatch@9.0.5": {"licenses": "ISC", "repository": "https://github.com/isaacs/minimatch", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\glob\\node_modules\\minimatch", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\glob\\node_modules\\minimatch\\LICENSE"}, "minimist@1.2.8": {"licenses": "MIT", "repository": "https://github.com/minimistjs/minimist", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\minimist", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\minimist\\LICENSE"}, "minipass-collect@1.0.2": {"licenses": "ISC", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://izs.me", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\tuf-js\\node_modules\\minipass-collect", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\tuf-js\\node_modules\\minipass-collect\\LICENSE"}, "minipass-collect@2.0.1": {"licenses": "ISC", "repository": "https://github.com/isaacs/minipass-collect", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://izs.me", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\minipass-collect", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\minipass-collect\\LICENSE"}, "minipass-fetch@2.1.2": {"licenses": "MIT", "repository": "https://github.com/npm/minipass-fetch", "publisher": "GitHub Inc.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@npmcli\\run-script\\node_modules\\minipass-fetch", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@npmcli\\run-script\\node_modules\\minipass-fetch\\LICENSE"}, "minipass-fetch@3.0.5": {"licenses": "MIT", "repository": "https://github.com/npm/minipass-fetch", "publisher": "GitHub Inc.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\minipass-fetch", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\minipass-fetch\\LICENSE"}, "minipass-flush@1.0.5": {"licenses": "ISC", "repository": "https://github.com/isaacs/minipass-flush", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://izs.me", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\minipass-flush", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\minipass-flush\\LICENSE"}, "minipass-json-stream@1.0.2": {"licenses": "MIT", "repository": "https://github.com/isaacs/minipass-json-stream", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://izs.me", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\minipass-json-stream", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\minipass-json-stream\\LICENSE"}, "minipass-pipeline@1.2.4": {"licenses": "ISC", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://izs.me", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\minipass-pipeline", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\minipass-pipeline\\LICENSE"}, "minipass-sized@1.0.3": {"licenses": "ISC", "repository": "https://github.com/isaacs/minipass-sized", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://izs.me", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\minipass-sized", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\minipass-sized\\LICENSE"}, "minipass@3.3.6": {"licenses": "ISC", "repository": "https://github.com/isaacs/minipass", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\tuf-js\\node_modules\\minipass-collect\\node_modules\\minipass", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\tuf-js\\node_modules\\minipass-collect\\node_modules\\minipass\\LICENSE"}, "minipass@5.0.0": {"licenses": "ISC", "repository": "https://github.com/isaacs/minipass", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\tar\\node_modules\\minipass", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\tar\\node_modules\\minipass\\LICENSE"}, "minipass@7.1.2": {"licenses": "ISC", "repository": "https://github.com/isaacs/minipass", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\minipass", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\minipass\\LICENSE"}, "minizlib@2.1.2": {"licenses": "MIT", "repository": "https://github.com/isaacs/minizlib", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\minizlib", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\minizlib\\LICENSE"}, "mkdirp-classic@0.5.3": {"licenses": "MIT", "repository": "https://github.com/mafintosh/mkdirp-classic", "publisher": "<PERSON>", "url": "@mafintosh", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\mkdirp-classic", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\mkdirp-classic\\LICENSE"}, "mkdirp@0.5.6": {"licenses": "MIT", "repository": "https://github.com/substack/node-mkdirp", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\mkdirp", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\mkdirp\\LICENSE"}, "mkdirp@1.0.4": {"licenses": "MIT", "repository": "https://github.com/isaacs/node-mkdirp", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\tar\\node_modules\\mkdirp", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\tar\\node_modules\\mkdirp\\LICENSE"}, "module-definition@3.4.0": {"licenses": "MIT", "repository": "https://github.com/dependents/module-definition", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://mrjoelkemp.com/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\module-definition", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\module-definition\\LICENSE"}, "module-definition@4.1.0": {"licenses": "MIT", "repository": "https://github.com/dependents/module-definition", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://mrjoelkemp.com/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\dependency-tree\\node_modules\\module-definition", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\dependency-tree\\node_modules\\module-definition\\LICENSE"}, "module-lookup-amd@7.0.1": {"licenses": "MIT", "repository": "https://github.com/mrjoelkemp/node-module-lookup-amd", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\module-lookup-amd", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\module-lookup-amd\\Readme.md"}, "moo@0.5.2": {"licenses": "BSD-3-<PERSON><PERSON>", "repository": "https://github.com/tjvr/moo", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\moo", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\moo\\LICENSE"}, "ms@2.1.3": {"licenses": "MIT", "repository": "https://github.com/vercel/ms", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\ms", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\ms\\license.md"}, "mute-stream@1.0.0": {"licenses": "ISC", "repository": "https://github.com/npm/mute-stream", "publisher": "GitHub Inc.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\mute-stream", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\mute-stream\\LICENSE"}, "nan@2.20.0": {"licenses": "MIT", "repository": "https://github.com/nodejs/nan", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\nan", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\nan\\LICENSE.md"}, "nanoid@3.3.11": {"licenses": "MIT", "repository": "https://github.com/ai/nanoid", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\nanoid", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\nanoid\\LICENSE"}, "napi-build-utils@2.0.0": {"licenses": "MIT", "repository": "https://github.com/inspiredware/napi-build-utils", "publisher": "<PERSON>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\napi-build-utils", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\napi-build-utils\\LICENSE"}, "natural-compare@1.4.0": {"licenses": "MIT", "repository": "https://github.com/litejs/natural-compare-lite", "publisher": "<PERSON><PERSON>", "url": "https://github.com/litejs/natural-compare-lite", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\natural-compare", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\natural-compare\\README.md"}, "nearley@2.20.1": {"licenses": "MIT", "repository": "https://github.com/hardmath123/nearley", "publisher": "Hardmath123", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\nearley", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\nearley\\LICENSE.txt"}, "negotiator@0.6.4": {"licenses": "MIT", "repository": "https://github.com/jshttp/negotiator", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\negotiator", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\negotiator\\LICENSE"}, "netmask@2.0.2": {"licenses": "MIT", "repository": "https://github.com/rs/node-netmask", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\netmask", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\netmask\\README.md"}, "node-abi@3.75.0": {"licenses": "MIT", "repository": "https://github.com/electron/node-abi", "publisher": "<PERSON><PERSON>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\node-abi", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\node-abi\\LICENSE"}, "node-gyp@10.3.1": {"licenses": "MIT", "repository": "https://github.com/nodejs/node-gyp", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://tootallnate.net", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\node-gyp", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\node-gyp\\LICENSE"}, "node-gyp@9.4.1": {"licenses": "MIT", "repository": "https://github.com/nodejs/node-gyp", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://tootallnate.net", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@npmcli\\run-script\\node_modules\\node-gyp", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@npmcli\\run-script\\node_modules\\node-gyp\\LICENSE"}, "node-source-walk@4.3.0": {"licenses": "MIT", "repository": "https://github.com/dependents/node-source-walk", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://mrjoelkemp.com/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\detective-less\\node_modules\\node-source-walk", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\detective-less\\node_modules\\node-source-walk\\LICENSE"}, "node-source-walk@5.0.2": {"licenses": "MIT", "repository": "https://github.com/dependents/node-source-walk", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\node-source-walk", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\node-source-walk\\LICENSE"}, "nopt@4.0.3": {"licenses": "ISC", "repository": "https://github.com/npm/nopt", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\nopt", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\nopt\\LICENSE"}, "nopt@6.0.0": {"licenses": "ISC", "repository": "https://github.com/npm/nopt", "publisher": "GitHub Inc.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@npmcli\\run-script\\node_modules\\nopt", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@npmcli\\run-script\\node_modules\\nopt\\LICENSE"}, "nopt@7.2.1": {"licenses": "ISC", "repository": "https://github.com/npm/nopt", "publisher": "GitHub Inc.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\node-gyp\\node_modules\\nopt", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\node-gyp\\node_modules\\nopt\\LICENSE"}, "normalize-package-data@2.5.0": {"licenses": "BSD-2-<PERSON><PERSON>", "repository": "https://github.com/npm/normalize-package-data", "publisher": "Meryn Stol", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\read-package-json\\node_modules\\normalize-package-data", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\read-package-json\\node_modules\\normalize-package-data\\LICENSE"}, "normalize-package-data@5.0.0": {"licenses": "BSD-2-<PERSON><PERSON>", "repository": "https://github.com/npm/normalize-package-data", "publisher": "GitHub Inc.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\pacote\\node_modules\\normalize-package-data", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\pacote\\node_modules\\normalize-package-data\\LICENSE"}, "normalize-package-data@6.0.2": {"licenses": "BSD-2-<PERSON><PERSON>", "repository": "https://github.com/npm/normalize-package-data", "publisher": "GitHub Inc.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\normalize-package-data", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\normalize-package-data\\LICENSE"}, "normalize-url@8.0.2": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/normalize-url", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\normalize-url", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\normalize-url\\license"}, "npm-bundled@3.0.1": {"licenses": "ISC", "repository": "https://github.com/npm/npm-bundled", "publisher": "GitHub Inc.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\npm-bundled", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\npm-bundled\\LICENSE"}, "npm-check-updates@16.14.20": {"licenses": "Apache-2.0", "repository": "https://github.com/raineorshine/npm-check-updates", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\npm-check-updates", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\npm-check-updates\\LICENSE"}, "npm-install-checks@6.3.0": {"licenses": "BSD-2-<PERSON><PERSON>", "repository": "https://github.com/npm/npm-install-checks", "publisher": "GitHub Inc.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\npm-install-checks", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\npm-install-checks\\LICENSE"}, "npm-normalize-package-bin@1.0.1": {"licenses": "ISC", "repository": "https://github.com/npm/npm-normalize-package-bin", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://izs.me", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\read-package-json\\node_modules\\npm-normalize-package-bin", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\read-package-json\\node_modules\\npm-normalize-package-bin\\LICENSE"}, "npm-normalize-package-bin@3.0.1": {"licenses": "ISC", "repository": "https://github.com/npm/npm-normalize-package-bin", "publisher": "GitHub Inc.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\npm-normalize-package-bin", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\npm-normalize-package-bin\\LICENSE"}, "npm-package-arg@10.1.0": {"licenses": "ISC", "repository": "https://github.com/npm/npm-package-arg", "publisher": "GitHub Inc.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\npm-package-arg", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\npm-package-arg\\LICENSE"}, "npm-packlist@7.0.4": {"licenses": "ISC", "repository": "https://github.com/npm/npm-packlist", "publisher": "GitHub Inc.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\npm-packlist", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\npm-packlist\\LICENSE"}, "npm-pick-manifest@8.0.2": {"licenses": "ISC", "repository": "https://github.com/npm/npm-pick-manifest", "publisher": "GitHub Inc.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\npm-pick-manifest", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\npm-pick-manifest\\LICENSE.md"}, "npm-registry-fetch@14.0.5": {"licenses": "ISC", "repository": "https://github.com/npm/npm-registry-fetch", "publisher": "GitHub Inc.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\npm-registry-fetch", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\npm-registry-fetch\\LICENSE.md"}, "npmlog@6.0.2": {"licenses": "ISC", "repository": "https://github.com/npm/npmlog", "publisher": "GitHub Inc.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\npmlog", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\npmlog\\LICENSE.md"}, "object-keys@1.1.1": {"licenses": "MIT", "repository": "https://github.com/ljharb/object-keys", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\object-keys", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\object-keys\\LICENSE"}, "once@1.4.0": {"licenses": "ISC", "repository": "https://github.com/isaacs/once", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\once", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\once\\LICENSE"}, "onetime@5.1.2": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/onetime", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\onetime", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\onetime\\license"}, "optionator@0.9.4": {"licenses": "MIT", "repository": "https://github.com/gkz/optionator", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\optionator", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\optionator\\LICENSE"}, "ora@5.4.1": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/ora", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\inquirer\\node_modules\\ora", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\inquirer\\node_modules\\ora\\license"}, "ora@7.0.1": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/ora", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\ora", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\ora\\license"}, "os-homedir@1.0.2": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/os-homedir", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\os-homedir", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\os-homedir\\license"}, "os-tmpdir@1.0.2": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/os-tmpdir", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\os-tmpdir", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\os-tmpdir\\license"}, "osenv@0.1.5": {"licenses": "ISC", "repository": "https://github.com/npm/osenv", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\osenv", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\osenv\\LICENSE"}, "p-cancelable@3.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/p-cancelable", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\p-cancelable", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\p-cancelable\\license"}, "p-limit@2.3.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/p-limit", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\pkg-up\\node_modules\\p-limit", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\pkg-up\\node_modules\\p-limit\\license"}, "p-limit@3.1.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/p-limit", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\p-limit", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\p-limit\\license"}, "p-locate@3.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/p-locate", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\pkg-up\\node_modules\\p-locate", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\pkg-up\\node_modules\\p-locate\\license"}, "p-locate@5.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/p-locate", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\p-locate", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\p-locate\\license"}, "p-map@4.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/p-map", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\p-map", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\p-map\\license"}, "p-try@2.2.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/p-try", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\p-try", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\p-try\\license"}, "pac-proxy-agent@7.2.0": {"licenses": "MIT", "repository": "https://github.com/TooTallNate/proxy-agents", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\pac-proxy-agent", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\pac-proxy-agent\\LICENSE"}, "pac-resolver@7.0.1": {"licenses": "MIT", "repository": "https://github.com/TooTallNate/proxy-agents", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\pac-resolver", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\pac-resolver\\LICENSE"}, "package-json-from-dist@1.0.1": {"licenses": "BlueOak-1.0.0", "repository": "https://github.com/isaacs/package-json-from-dist", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://izs.me", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\package-json-from-dist", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\package-json-from-dist\\LICENSE.md"}, "package-json@8.1.1": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/package-json", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\package-json", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\package-json\\license"}, "packageurl-js@1.2.1": {"licenses": "MIT", "repository": "https://github.com/package-url/packageurl-js", "publisher": "the purl authors", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\packageurl-js", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\packageurl-js\\LICENSE"}, "pacote@15.2.0": {"licenses": "ISC", "repository": "https://github.com/npm/pacote", "publisher": "GitHub Inc.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\pacote", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\pacote\\LICENSE"}, "parent-module@1.0.1": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/parent-module", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\parent-module", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\parent-module\\license"}, "parse-github-url@1.0.3": {"licenses": "MIT", "repository": "https://github.com/jonschlinkert/parse-github-url", "publisher": "<PERSON>", "url": "https://github.com/jonschlinkert", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\parse-github-url", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\parse-github-url\\LICENSE"}, "parse-ms@2.1.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/parse-ms", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\parse-ms", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\parse-ms\\license"}, "path-exists@3.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/path-exists", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\pkg-up\\node_modules\\path-exists", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\pkg-up\\node_modules\\path-exists\\license"}, "path-exists@4.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/path-exists", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\path-exists", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\path-exists\\license"}, "path-is-absolute@1.0.1": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/path-is-absolute", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\path-is-absolute", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\path-is-absolute\\license"}, "path-key@3.1.1": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/path-key", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\path-key", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\path-key\\license"}, "path-parse@1.0.7": {"licenses": "MIT", "repository": "https://github.com/jbgu<PERSON>rez/path-parse", "publisher": "<PERSON>", "email": "http://jbgutierrez.info", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\path-parse", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\path-parse\\LICENSE"}, "path-scurry@1.11.1": {"licenses": "BlueOak-1.0.0", "repository": "https://github.com/isaacs/path-scurry", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://blog.izs.me", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\path-scurry", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\path-scurry\\LICENSE.md"}, "path-type@4.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/path-type", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\path-type", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\path-type\\license"}, "pause-stream@0.0.11": {"licenses": ["MIT", "Apache2"], "repository": "https://github.com/dominictarr/pause-stream", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\pause-stream", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\pause-stream\\LICENSE"}, "picocolors@1.1.1": {"licenses": "ISC", "repository": "https://github.com/alexeyraspopov/picocolors", "publisher": "<PERSON><PERSON>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\picocolors", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\picocolors\\LICENSE"}, "picomatch@2.3.1": {"licenses": "MIT", "repository": "https://github.com/micromatch/picomatch", "publisher": "<PERSON>", "url": "https://github.com/jonschlinkert", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\picomatch", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\picomatch\\LICENSE"}, "pkg-up@3.1.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/pkg-up", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\pkg-up", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\pkg-up\\license"}, "pluralize@8.0.0": {"licenses": "MIT", "repository": "https://github.com/blakeembrey/pluralize", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blakeembrey.me", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\pluralize", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\pluralize\\LICENSE"}, "postcss-values-parser@2.0.1": {"licenses": "MIT", "repository": "https://github.com/lesshint/postcss-values-parser", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "shellscape", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\precinct\\node_modules\\postcss-values-parser", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\precinct\\node_modules\\postcss-values-parser\\LICENSE"}, "postcss-values-parser@6.0.2": {"licenses": "MPL-2.0", "repository": "https://github.com/shellscape/postcss-values-parser", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "shellscape", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\postcss-values-parser", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\postcss-values-parser\\LICENSE"}, "postcss@8.5.6": {"licenses": "MIT", "repository": "https://github.com/postcss/postcss", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\postcss", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\postcss\\LICENSE"}, "prebuild-install@7.1.3": {"licenses": "MIT", "repository": "https://github.com/prebuild/prebuild-install", "publisher": "<PERSON>", "url": "@mafintosh", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\prebuild-install", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\prebuild-install\\LICENSE"}, "precinct@8.3.1": {"licenses": "MIT", "repository": "https://github.com/dependents/node-precinct", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\precinct", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\precinct\\Readme.md"}, "precinct@9.2.1": {"licenses": "MIT", "repository": "https://github.com/dependents/node-precinct", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\dependency-tree\\node_modules\\precinct", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\dependency-tree\\node_modules\\precinct\\LICENSE"}, "prelude-ls@1.2.1": {"licenses": "MIT", "repository": "https://github.com/gkz/prelude-ls", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\prelude-ls", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\prelude-ls\\LICENSE"}, "pretty-ms@7.0.1": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/pretty-ms", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\pretty-ms", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\pretty-ms\\license"}, "proc-log@3.0.0": {"licenses": "ISC", "repository": "https://github.com/npm/proc-log", "publisher": "GitHub Inc.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\npm-package-arg\\node_modules\\proc-log", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\npm-package-arg\\node_modules\\proc-log\\LICENSE"}, "proc-log@4.2.0": {"licenses": "ISC", "repository": "https://github.com/npm/proc-log", "publisher": "GitHub Inc.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\proc-log", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\proc-log\\LICENSE"}, "progress@2.0.3": {"licenses": "MIT", "repository": "https://github.com/visionmedia/node-progress", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\progress", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\progress\\LICENSE"}, "promise-inflight@1.0.1": {"licenses": "ISC", "repository": "https://github.com/iarna/promise-inflight", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\promise-inflight", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\promise-inflight\\LICENSE"}, "promise-retry@2.0.1": {"licenses": "MIT", "repository": "https://github.com/IndigoUnited/node-promise-retry", "publisher": "IndigoUnited", "email": "<EMAIL>", "url": "http://indigounited.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\promise-retry", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\promise-retry\\LICENSE"}, "prompts-ncu@3.0.2": {"licenses": "MIT", "repository": "https://github.com/raineorshine/prompts/tree/ncu", "publisher": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://terkel.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\prompts-ncu", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\prompts-ncu\\license"}, "proto-list@1.2.4": {"licenses": "ISC", "repository": "https://github.com/isaacs/proto-list", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\proto-list", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\proto-list\\LICENSE"}, "proxy-agent@6.5.0": {"licenses": "MIT", "repository": "https://github.com/TooTallNate/proxy-agents", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\proxy-agent", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\proxy-agent\\LICENSE"}, "proxy-from-env@1.1.0": {"licenses": "MIT", "repository": "https://github.com/Rob--W/proxy-from-env", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://robwu.nl/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\proxy-from-env", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\proxy-from-env\\LICENSE"}, "pump@3.0.3": {"licenses": "MIT", "repository": "https://github.com/mafintosh/pump", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\pump", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\pump\\LICENSE"}, "punycode@2.3.1": {"licenses": "MIT", "repository": "https://github.com/mathiasbynens/punycode.js", "publisher": "<PERSON>", "url": "https://mathiasbynens.be/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\punycode", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\punycode\\LICENSE-MIT.txt"}, "pupa@3.1.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/pupa", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\pupa", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\pupa\\license"}, "queue-microtask@1.2.3": {"licenses": "MIT", "repository": "https://github.com/feross/queue-microtask", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://feross.org", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\queue-microtask", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\queue-microtask\\LICENSE"}, "quick-lru@5.1.1": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/quick-lru", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\quick-lru", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\quick-lru\\license"}, "quote-unquote@1.0.0": {"licenses": "MIT", "repository": "https://github.com/dominictarr/quote-unquote", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://dominictarr.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\quote-unquote", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\quote-unquote\\LICENSE"}, "railroad-diagrams@1.0.0": {"licenses": "CC0-1.0", "repository": "https://github.com/tabatkins/railroad-diagrams", "publisher": "<PERSON><PERSON> Jr.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\railroad-diagrams", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\railroad-diagrams\\README.md"}, "randexp@0.4.6": {"licenses": "MIT", "repository": "https://github.com/fent/randexp.js", "publisher": "<PERSON><PERSON>", "url": "https://github.com/fent", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\randexp", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\randexp\\LICENSE"}, "rc-config-loader@4.1.3": {"licenses": "MIT", "repository": "https://github.com/azu/rc-config-loader", "publisher": "azu", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\rc-config-loader", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\rc-config-loader\\LICENSE"}, "rc@1.2.8": {"licenses": "(BSD-2-<PERSON><PERSON> OR MIT OR Apache-2.0)", "repository": "https://github.com/dominictarr/rc", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\rc", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\rc\\LICENSE.APACHE2"}, "read-installed@4.0.3": {"licenses": "ISC", "repository": "https://github.com/isaacs/read-installed", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\read-installed", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\read-installed\\LICENSE"}, "read-package-json-fast@3.0.2": {"licenses": "ISC", "repository": "https://github.com/npm/read-package-json-fast", "publisher": "GitHub Inc.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\read-package-json-fast", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\read-package-json-fast\\LICENSE"}, "read-package-json@2.1.2": {"licenses": "ISC", "repository": "https://github.com/npm/read-package-json", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\read-package-json", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\read-package-json\\LICENSE"}, "read-package-json@6.0.4": {"licenses": "ISC", "repository": "https://github.com/npm/read-package-json", "publisher": "GitHub Inc.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\pacote\\node_modules\\read-package-json", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\pacote\\node_modules\\read-package-json\\LICENSE"}, "readable-stream@3.6.2": {"licenses": "MIT", "repository": "https://github.com/nodejs/readable-stream", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\readable-stream", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\readable-stream\\LICENSE"}, "readdir-scoped-modules@1.1.0": {"licenses": "ISC", "repository": "https://github.com/npm/readdir-scoped-modules", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\readdir-scoped-modules", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\readdir-scoped-modules\\LICENSE"}, "readline-transform@1.0.0": {"licenses": "MIT", "repository": "https://github.com/tilfin/readline-transform", "publisher": "<PERSON><PERSON><PERSON><PERSON>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\readline-transform", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\readline-transform\\LICENSE"}, "regexp-tree@0.1.27": {"licenses": "MIT", "repository": "https://github.com/Dmitry<PERSON>osh<PERSON>/regexp-tree", "publisher": "<PERSON>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\regexp-tree", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\regexp-tree\\LICENSE"}, "registry-auth-token@5.1.0": {"licenses": "MIT", "repository": "https://github.com/rexxars/registry-auth-token", "publisher": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\registry-auth-token", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\registry-auth-token\\LICENSE"}, "registry-url@6.0.1": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/registry-url", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\registry-url", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\registry-url\\license"}, "remote-git-tags@3.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/remote-git-tags", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\remote-git-tags", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\remote-git-tags\\license"}, "require-directory@2.1.1": {"licenses": "MIT", "repository": "https://github.com/troygoode/node-require-directory", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/troygoode/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\require-directory", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\require-directory\\LICENSE"}, "require-from-string@2.0.2": {"licenses": "MIT", "repository": "https://github.com/floatdrop/require-from-string", "publisher": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "github.com/floatdrop", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\require-from-string", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\require-from-string\\license"}, "requirejs-config-file@4.0.0": {"licenses": "MIT", "repository": "https://github.com/webforge-labs/requirejs-config-file", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\requirejs-config-file", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\requirejs-config-file\\LICENSE"}, "requirejs@2.3.7": {"licenses": "MIT", "repository": "https://github.com/jrburke/r.js", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/jrburke", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\requirejs", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\requirejs\\README.md"}, "resolve-alpn@1.2.1": {"licenses": "MIT", "repository": "https://github.com/szmarczak/resolve-alpn", "publisher": "<PERSON><PERSON><PERSON>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\resolve-alpn", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\resolve-alpn\\LICENSE"}, "resolve-dependency-path@2.0.0": {"licenses": "MIT", "repository": "https://github.com/mrjoelkemp/node-resolve-dependency-path", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://www.mrjoelkemp.com/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\resolve-dependency-path", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\resolve-dependency-path\\Readme.md"}, "resolve-from@4.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/resolve-from", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\resolve-from", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\resolve-from\\license"}, "resolve@1.22.10": {"licenses": "MIT", "repository": "https://github.com/browserify/resolve", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\resolve", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\resolve\\LICENSE"}, "responselike@3.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/responselike", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://lukechilds.co.uk", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\responselike", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\responselike\\license"}, "restore-cursor@3.1.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/restore-cursor", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\inquirer\\node_modules\\restore-cursor", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\inquirer\\node_modules\\restore-cursor\\license"}, "restore-cursor@4.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/restore-cursor", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\restore-cursor", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\restore-cursor\\license"}, "ret@0.1.15": {"licenses": "MIT", "repository": "https://github.com/fent/ret.js", "publisher": "<PERSON><PERSON>", "url": "https://github.com/fent", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\ret", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\ret\\LICENSE"}, "retire@4.4.4": {"licenses": "Apache-2.0", "repository": "https://github.com/RetireJS/retire.js", "publisher": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\retire", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\retire\\README.md"}, "retry@0.12.0": {"licenses": "MIT", "repository": "https://github.com/tim-kos/node-retry", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\retry", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\retry\\License"}, "reusify@1.1.0": {"licenses": "MIT", "repository": "https://github.com/mcollina/reusify", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\reusify", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\reusify\\LICENSE"}, "rimraf@3.0.2": {"licenses": "ISC", "repository": "https://github.com/isaacs/rimraf", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\rimraf", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\rimraf\\LICENSE"}, "rimraf@5.0.10": {"licenses": "ISC", "repository": "https://github.com/isaacs/rimraf", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\npm-check-updates\\node_modules\\rimraf", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\npm-check-updates\\node_modules\\rimraf\\LICENSE"}, "roarr@2.15.4": {"licenses": "BSD-3-<PERSON><PERSON>", "repository": "https://github.com/gajus/roarr", "publisher": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://gajus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\roarr", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\roarr\\LICENSE"}, "run-async@3.0.0": {"licenses": "MIT", "repository": "https://github.com/SBoudrias/run-async", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\run-async", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\run-async\\LICENSE"}, "run-parallel@1.2.0": {"licenses": "MIT", "repository": "https://github.com/feross/run-parallel", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://feross.org", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\run-parallel", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\run-parallel\\LICENSE"}, "rxjs@7.8.2": {"licenses": "Apache-2.0", "repository": "https://github.com/reactivex/rxjs", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\rxjs", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\rxjs\\LICENSE.txt"}, "safe-buffer@5.2.1": {"licenses": "MIT", "repository": "https://github.com/feross/safe-buffer", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://feross.org", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\safe-buffer", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\safe-buffer\\LICENSE"}, "safe-regex@2.1.1": {"licenses": "MIT", "repository": "https://github.com/davisjam/safe-regex", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "<PERSON>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\safe-regex", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\safe-regex\\LICENSE"}, "safer-buffer@2.1.2": {"licenses": "MIT", "repository": "https://github.com/ChALkeR/safer-buffer", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/ChALkeR", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\safer-buffer", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\safer-buffer\\LICENSE"}, "sass-lookup@3.0.0": {"licenses": "MIT", "repository": "https://github.com/mrjoelkemp/node-sass-lookup", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://www.mrjoelkemp.com/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\sass-lookup", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\sass-lookup\\Readme.md"}, "schemes@1.4.0": {"licenses": "MIT", "repository": "https://github.com/Munter/schemes", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\schemes", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\schemes\\README.md"}, "semver-compare@1.0.0": {"licenses": "MIT", "repository": "https://github.com/substack/semver-compare", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\semver-compare", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\semver-compare\\LICENSE"}, "semver-diff@4.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/semver-diff", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\semver-diff", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\semver-diff\\license"}, "semver-utils@1.1.4": {"licenses": "MIT*", "repository": "git://git.coolaj86.com/coolaj86/semver-utils.js", "publisher": "AJ <PERSON>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\semver-utils", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\semver-utils\\LICENSE"}, "semver@5.7.2": {"licenses": "ISC", "repository": "https://github.com/npm/node-semver", "publisher": "GitHub Inc.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\read-package-json\\node_modules\\semver", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\read-package-json\\node_modules\\semver\\LICENSE"}, "semver@7.7.2": {"licenses": "ISC", "repository": "https://github.com/npm/node-semver", "publisher": "GitHub Inc.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\semver", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\semver\\LICENSE"}, "serialize-error@7.0.1": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/serialize-error", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\serialize-error", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\serialize-error\\license"}, "set-blocking@2.0.0": {"licenses": "ISC", "repository": "https://github.com/yargs/set-blocking", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\set-blocking", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\set-blocking\\LICENSE.txt"}, "shebang-command@2.0.0": {"licenses": "MIT", "repository": "https://github.com/kevva/shebang-command", "publisher": "<PERSON>", "email": "kevin<PERSON><PERSON><PERSON>@gmail.com", "url": "github.com/kevva", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\shebang-command", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\shebang-command\\license"}, "shebang-regex@3.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/shebang-regex", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\shebang-regex", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\shebang-regex\\license"}, "signal-exit@3.0.7": {"licenses": "ISC", "repository": "https://github.com/tapjs/signal-exit", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\restore-cursor\\node_modules\\signal-exit", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\restore-cursor\\node_modules\\signal-exit\\LICENSE.txt"}, "signal-exit@4.1.0": {"licenses": "ISC", "repository": "https://github.com/tapjs/signal-exit", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\signal-exit", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\signal-exit\\LICENSE.txt"}, "sigstore@1.9.0": {"licenses": "Apache-2.0", "repository": "https://github.com/sigstore/sigstore-js", "publisher": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\sigstore", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\sigstore\\LICENSE"}, "simple-concat@1.0.1": {"licenses": "MIT", "repository": "https://github.com/feross/simple-concat", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://feross.org", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\simple-concat", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\simple-concat\\LICENSE"}, "simple-get@4.0.1": {"licenses": "MIT", "repository": "https://github.com/feross/simple-get", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://feross.org", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\simple-get", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\simple-get\\LICENSE"}, "sisteransi@1.0.5": {"licenses": "MIT", "repository": "https://github.com/terkelg/sisteransi", "publisher": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://terkel.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\sisteransi", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\sisteransi\\license"}, "slash@3.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/slash", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\slash", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\slash\\license"}, "slide@1.1.6": {"licenses": "ISC", "repository": "https://github.com/isaacs/slide-flow-control", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\slide", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\slide\\LICENSE"}, "smart-buffer@4.2.0": {"licenses": "MIT", "repository": "https://github.com/JoshGlazebrook/smart-buffer", "publisher": "<PERSON>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\smart-buffer", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\smart-buffer\\LICENSE"}, "smtp-address-parser@1.1.0": {"licenses": "MIT", "repository": "https://github.com/gene-hightower/smtp-address-parser", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://digilicious.com/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\smtp-address-parser", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\smtp-address-parser\\LICENSE"}, "snyk@1.1297.3": {"licenses": "Apache-2.0", "repository": "https://github.com/snyk/snyk", "publisher": "snyk.io", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\snyk", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\snyk\\LICENSE"}, "socks-proxy-agent@7.0.0": {"licenses": "MIT", "repository": "https://github.com/TooTallNate/node-socks-proxy-agent", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\tuf-js\\node_modules\\socks-proxy-agent", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\tuf-js\\node_modules\\socks-proxy-agent\\README.md"}, "socks-proxy-agent@8.0.5": {"licenses": "MIT", "repository": "https://github.com/TooTallNate/proxy-agents", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\socks-proxy-agent", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\socks-proxy-agent\\LICENSE"}, "socks@2.8.5": {"licenses": "MIT", "repository": "https://github.com/Josh<PERSON>lazebrook/socks", "publisher": "<PERSON>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\socks", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\socks\\LICENSE"}, "source-map-js@1.2.1": {"licenses": "BSD-3-<PERSON><PERSON>", "repository": "https://github.com/7rulnik/source-map-js", "publisher": "Valentin 7rulnik Semirulnik", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\source-map-js", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\source-map-js\\LICENSE"}, "source-map-support@0.5.21": {"licenses": "MIT", "repository": "https://github.com/evanw/node-source-map-support", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\source-map-support", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\source-map-support\\LICENSE.md"}, "source-map@0.6.1": {"licenses": "BSD-3-<PERSON><PERSON>", "repository": "https://github.com/mozilla/source-map", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\source-map", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\source-map\\LICENSE"}, "spawn-please@2.0.2": {"licenses": "ISC", "repository": "https://github.com/raineorshine/spawn-please", "publisher": "<PERSON><PERSON>", "url": "https://github.com/raineorshine", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\spawn-please", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\spawn-please\\LICENSE"}, "spdx-compare@1.0.0": {"licenses": "MIT", "repository": "https://github.com/kemitchell/spdx-compare.js", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://kemitchell.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\spdx-compare", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\spdx-compare\\LICENSE.md"}, "spdx-correct@3.2.0": {"licenses": "Apache-2.0", "repository": "https://github.com/jslicense/spdx-correct.js", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\spdx-correct", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\spdx-correct\\LICENSE"}, "spdx-exceptions@2.5.0": {"licenses": "CC-BY-3.0", "repository": "https://github.com/kemitchell/spdx-exceptions.json", "publisher": "The Linux Foundation", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\spdx-exceptions", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\spdx-exceptions\\README.md"}, "spdx-expression-parse@3.0.1": {"licenses": "MIT", "repository": "https://github.com/jslicense/spdx-expression-parse.js", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://kemitchell.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\spdx-correct\\node_modules\\spdx-expression-parse", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\spdx-correct\\node_modules\\spdx-expression-parse\\LICENSE"}, "spdx-expression-parse@4.0.0": {"licenses": "MIT", "repository": "https://github.com/jslicense/spdx-expression-parse.js", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://kemitchell.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\spdx-expression-parse", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\spdx-expression-parse\\LICENSE"}, "spdx-license-ids@3.0.21": {"licenses": "CC0-1.0", "repository": "https://github.com/jslicense/spdx-license-ids", "publisher": "<PERSON><PERSON><PERSON>", "url": "https://github.com/shinnn", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\spdx-license-ids", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\spdx-license-ids\\README.md"}, "spdx-ranges@2.1.1": {"licenses": "(MIT AND CC-BY-3.0)", "repository": "https://github.com/kemitchell/spdx-ranges.js", "publisher": "The Linux Foundation", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\spdx-ranges", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\spdx-ranges\\LICENSE.md"}, "spdx-satisfies@4.0.1": {"licenses": "MIT", "repository": "https://github.com/kemitchell/spdx-satisfies.js", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://kemitchell.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\spdx-satisfies", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\spdx-satisfies\\LICENSE"}, "split@1.0.1": {"licenses": "MIT", "repository": "https://github.com/dominictarr/split", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://bit.ly/dominictarr", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\split", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\split\\LICENCE"}, "sprintf-js@1.0.3": {"licenses": "BSD-3-<PERSON><PERSON>", "repository": "https://github.com/alexei/sprintf.js", "publisher": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://alexei.ro/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\xmlbuilder2\\node_modules\\sprintf-js", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\xmlbuilder2\\node_modules\\sprintf-js\\LICENSE"}, "sprintf-js@1.1.3": {"licenses": "BSD-3-<PERSON><PERSON>", "repository": "https://github.com/alexei/sprintf.js", "publisher": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\sprintf-js", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\sprintf-js\\LICENSE"}, "ssri@10.0.6": {"licenses": "ISC", "repository": "https://github.com/npm/ssri", "publisher": "GitHub Inc.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\ssri", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\ssri\\LICENSE.md"}, "ssri@9.0.1": {"licenses": "ISC", "repository": "https://github.com/npm/ssri", "publisher": "GitHub Inc.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@npmcli\\run-script\\node_modules\\ssri", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@npmcli\\run-script\\node_modules\\ssri\\LICENSE.md"}, "stdin-discarder@0.1.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/stdin-discarder", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\stdin-discarder", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\stdin-discarder\\license"}, "stream-combiner@0.2.2": {"licenses": "MIT", "repository": "https://github.com/dominictarr/stream-combiner", "publisher": "'<PERSON>'", "email": "<EMAIL>", "url": "http://dominictarr.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\stream-combiner", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\stream-combiner\\LICENSE"}, "stream-to-array@2.3.0": {"licenses": "MIT", "repository": "https://github.com/stream-utils/stream-to-array", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\stream-to-array", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\stream-to-array\\LICENSE"}, "string-width@4.2.3": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/string-width", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\string-width", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\string-width\\license"}, "string-width@5.1.2": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/string-width", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@isaacs\\cliui\\node_modules\\string-width", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@isaacs\\cliui\\node_modules\\string-width\\license"}, "string-width@6.1.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/string-width", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\ora\\node_modules\\string-width", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\ora\\node_modules\\string-width\\license"}, "string_decoder@1.3.0": {"licenses": "MIT", "repository": "https://github.com/nodejs/string_decoder", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\string_decoder", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\string_decoder\\LICENSE"}, "stringify-object@3.3.0": {"licenses": "BSD-2-<PERSON><PERSON>", "repository": "https://github.com/yeoman/stringify-object", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\stringify-object", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\stringify-object\\LICENSE"}, "strip-ansi@6.0.1": {"licenses": "MIT", "repository": "https://github.com/chalk/strip-ansi", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\strip-ansi", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\strip-ansi\\license"}, "strip-ansi@7.1.0": {"licenses": "MIT", "repository": "https://github.com/chalk/strip-ansi", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\ora\\node_modules\\strip-ansi", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\ora\\node_modules\\strip-ansi\\license"}, "strip-bom@3.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/strip-bom", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\strip-bom", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\strip-bom\\license"}, "strip-json-comments@2.0.1": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/strip-json-comments", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\rc\\node_modules\\strip-json-comments", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\rc\\node_modules\\strip-json-comments\\license"}, "strip-json-comments@3.1.1": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/strip-json-comments", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\strip-json-comments", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\strip-json-comments\\license"}, "strip-json-comments@5.0.2": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/strip-json-comments", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\npm-check-updates\\node_modules\\strip-json-comments", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\npm-check-updates\\node_modules\\strip-json-comments\\license"}, "stylus-lookup@3.0.2": {"licenses": "MIT", "repository": "https://github.com/mrjoelkemp/node-stylus-lookup", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://www.mrjoelkemp.com/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\stylus-lookup", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\stylus-lookup\\Readme.md"}, "supports-color@5.5.0": {"licenses": "MIT", "repository": "https://github.com/chalk/supports-color", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\license-checker\\node_modules\\supports-color", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\license-checker\\node_modules\\supports-color\\license"}, "supports-color@7.2.0": {"licenses": "MIT", "repository": "https://github.com/chalk/supports-color", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\supports-color", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\supports-color\\license"}, "supports-preserve-symlinks-flag@1.0.0": {"licenses": "MIT", "repository": "https://github.com/inspect-js/node-supports-preserve-symlinks-flag", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\supports-preserve-symlinks-flag", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\supports-preserve-symlinks-flag\\LICENSE"}, "tapable@2.2.2": {"licenses": "MIT", "repository": "https://github.com/webpack/tapable", "publisher": "<PERSON> @sokra", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\tapable", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\tapable\\LICENSE"}, "tar-fs@2.1.3": {"licenses": "MIT", "repository": "https://github.com/mafintosh/tar-fs", "publisher": "<PERSON>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\tar-fs", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\tar-fs\\LICENSE"}, "tar-stream@2.2.0": {"licenses": "MIT", "repository": "https://github.com/mafintosh/tar-stream", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\tar-stream", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\tar-stream\\LICENSE"}, "tar@6.2.1": {"licenses": "ISC", "repository": "https://github.com/isaacs/node-tar", "publisher": "GitHub Inc.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\tar", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\tar\\LICENSE"}, "text-table@0.2.0": {"licenses": "MIT", "repository": "https://github.com/substack/text-table", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\text-table", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\text-table\\LICENSE"}, "through@2.3.8": {"licenses": "MIT", "repository": "https://github.com/dominictarr/through", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\through", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\through\\LICENSE.APACHE2"}, "tmp@0.0.33": {"licenses": "MIT", "repository": "https://github.com/raszi/node-tmp", "publisher": "KARASZI István", "email": "<EMAIL>", "url": "http://raszi.hu/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\tmp", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\tmp\\LICENSE"}, "to-regex-range@5.0.1": {"licenses": "MIT", "repository": "https://github.com/micromatch/to-regex-range", "publisher": "<PERSON>", "url": "https://github.com/jonschlinkert", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\to-regex-range", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\to-regex-range\\LICENSE"}, "treeify@1.1.0": {"licenses": "MIT", "repository": "https://github.com/notatestuser/treeify", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\treeify", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\treeify\\LICENSE"}, "ts-graphviz@1.8.2": {"licenses": "MIT", "repository": "https://github.com/ts-graphviz/ts-graphviz", "publisher": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\ts-graphviz", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\ts-graphviz\\LICENSE"}, "tsconfig-paths@3.15.0": {"licenses": "MIT", "repository": "https://github.com/dividab/tsconfig-paths", "publisher": "<PERSON>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\tsconfig-paths", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\tsconfig-paths\\LICENSE"}, "tslib@1.14.1": {"licenses": "0BSD", "repository": "https://github.com/Microsoft/tslib", "publisher": "Microsoft Corp.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\tsutils\\node_modules\\tslib", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\tsutils\\node_modules\\tslib\\LICENSE.txt"}, "tslib@2.8.1": {"licenses": "0BSD", "repository": "https://github.com/Microsoft/tslib", "publisher": "Microsoft Corp.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\tslib", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\tslib\\LICENSE.txt"}, "tsutils@3.21.0": {"licenses": "MIT", "repository": "https://github.com/ajafff/tsutils", "publisher": "<PERSON>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\tsutils", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\tsutils\\LICENSE"}, "tuf-js@1.1.7": {"licenses": "MIT", "repository": "https://github.com/theupdateframework/tuf-js", "publisher": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\tuf-js", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\tuf-js\\LICENSE"}, "tunnel-agent@0.6.0": {"licenses": "Apache-2.0", "repository": "https://github.com/mikeal/tunnel-agent", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.futurealoof.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\tunnel-agent", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\tunnel-agent\\LICENSE"}, "type-check@0.4.0": {"licenses": "MIT", "repository": "https://github.com/gkz/type-check", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\type-check", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\type-check\\LICENSE"}, "type-fest@0.13.1": {"licenses": "(MIT OR CC0-1.0)", "repository": "https://github.com/sindresorhus/type-fest", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\serialize-error\\node_modules\\type-fest", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\serialize-error\\node_modules\\type-fest\\license"}, "type-fest@0.20.2": {"licenses": "(MIT OR CC0-1.0)", "repository": "https://github.com/sindresorhus/type-fest", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\type-fest", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\type-fest\\license"}, "type-fest@0.21.3": {"licenses": "(MIT OR CC0-1.0)", "repository": "https://github.com/sindresorhus/type-fest", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\ansi-escapes\\node_modules\\type-fest", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\ansi-escapes\\node_modules\\type-fest\\license"}, "type-fest@1.4.0": {"licenses": "(MIT OR CC0-1.0)", "repository": "https://github.com/sindresorhus/type-fest", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\crypto-random-string\\node_modules\\type-fest", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\crypto-random-string\\node_modules\\type-fest\\license"}, "type-fest@2.19.0": {"licenses": "(MIT OR CC0-1.0)", "repository": "https://github.com/sindresorhus/type-fest", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\boxen\\node_modules\\type-fest", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\boxen\\node_modules\\type-fest\\readme.md"}, "typedarray-to-buffer@3.1.5": {"licenses": "MIT", "repository": "https://github.com/feross/typedarray-to-buffer", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\typedarray-to-buffer", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\typedarray-to-buffer\\LICENSE"}, "typescript@3.9.10": {"licenses": "Apache-2.0", "repository": "https://github.com/Microsoft/TypeScript", "publisher": "Microsoft Corp.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\filing-cabinet\\node_modules\\typescript", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\filing-cabinet\\node_modules\\typescript\\LICENSE.txt"}, "typescript@4.9.5": {"licenses": "Apache-2.0", "repository": "https://github.com/Microsoft/TypeScript", "publisher": "Microsoft Corp.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\typescript", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\typescript\\LICENSE.txt"}, "unc-path-regex@0.1.2": {"licenses": "MIT", "repository": "https://github.com/regexhq/unc-path-regex", "publisher": "<PERSON>", "url": "https://github.com/jonschlinkert", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\unc-path-regex", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\unc-path-regex\\LICENSE"}, "undici-types@7.8.0": {"licenses": "MIT", "repository": "https://github.com/nodejs/undici", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\undici-types", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\undici-types\\LICENSE"}, "uniq@1.0.1": {"licenses": "MIT", "repository": "https://github.com/mi<PERSON><PERSON><PERSON><PERSON>/uniq", "publisher": "<PERSON><PERSON><PERSON>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\uniq", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\uniq\\LICENSE"}, "unique-filename@2.0.1": {"licenses": "ISC", "repository": "https://github.com/npm/unique-filename", "publisher": "GitHub Inc.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@npmcli\\run-script\\node_modules\\unique-filename", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@npmcli\\run-script\\node_modules\\unique-filename\\LICENSE"}, "unique-filename@3.0.0": {"licenses": "ISC", "repository": "https://github.com/npm/unique-filename", "publisher": "GitHub Inc.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\unique-filename", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\unique-filename\\LICENSE"}, "unique-slug@3.0.0": {"licenses": "ISC", "repository": "https://github.com/npm/unique-slug", "publisher": "GitHub Inc.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@npmcli\\run-script\\node_modules\\unique-slug", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@npmcli\\run-script\\node_modules\\unique-slug\\LICENSE"}, "unique-slug@4.0.0": {"licenses": "ISC", "repository": "https://github.com/npm/unique-slug", "publisher": "GitHub Inc.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\unique-slug", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\unique-slug\\LICENSE"}, "unique-string@3.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/unique-string", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\unique-string", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\unique-string\\license"}, "universalify@2.0.1": {"licenses": "MIT", "repository": "https://github.com/RyanZim/universalify", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\universalify", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\universalify\\LICENSE"}, "untildify@4.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/untildify", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\untildify", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\untildify\\license"}, "update-notifier@6.0.2": {"licenses": "BSD-2-<PERSON><PERSON>", "repository": "https://github.com/yeoman/update-notifier", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\update-notifier", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\update-notifier\\license"}, "uri-js@4.4.1": {"licenses": "BSD-2-<PERSON><PERSON>", "repository": "https://github.com/garycourt/uri-js", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\uri-js", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\uri-js\\LICENSE"}, "util-deprecate@1.0.2": {"licenses": "MIT", "repository": "https://github.com/TooTallNate/util-deprecate", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\util-deprecate", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\util-deprecate\\LICENSE"}, "util-extend@1.0.3": {"licenses": "MIT", "repository": "https://github.com/isaacs/util-extend", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\util-extend", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\util-extend\\LICENSE"}, "uuid@9.0.1": {"licenses": "MIT", "repository": "https://github.com/uuidjs/uuid", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\uuid", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\uuid\\LICENSE.md"}, "validate-npm-package-license@3.0.4": {"licenses": "Apache-2.0", "repository": "https://github.com/kemitchell/validate-npm-package-license.js", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://kemitchell.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\validate-npm-package-license", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\validate-npm-package-license\\LICENSE"}, "validate-npm-package-name@5.0.1": {"licenses": "ISC", "repository": "https://github.com/npm/validate-npm-package-name", "publisher": "GitHub Inc.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\validate-npm-package-name", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\validate-npm-package-name\\LICENSE"}, "vscode-universal-security-scanner@1.0.0": {"licenses": "MIT", "publisher": "<PERSON> <PERSON> Solo Developer Security Solutions", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\README.md"}, "walkdir@0.4.1": {"licenses": "MIT", "repository": "https://github.com/soldair/node-walkdir", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\walkdir", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\walkdir\\license"}, "wcwidth@1.0.1": {"licenses": "MIT", "repository": "https://github.com/timoxley/wcwidth", "publisher": "<PERSON>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\wcwidth", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\wcwidth\\LICENSE"}, "which@2.0.2": {"licenses": "ISC", "repository": "https://github.com/isaacs/node-which", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\which", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\which\\LICENSE"}, "which@3.0.1": {"licenses": "ISC", "repository": "https://github.com/npm/node-which", "publisher": "GitHub Inc.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@npmcli\\promise-spawn\\node_modules\\which", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@npmcli\\promise-spawn\\node_modules\\which\\LICENSE"}, "which@4.0.0": {"licenses": "ISC", "repository": "https://github.com/npm/node-which", "publisher": "GitHub Inc.", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\node-gyp\\node_modules\\which", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\node-gyp\\node_modules\\which\\LICENSE"}, "wide-align@1.1.5": {"licenses": "ISC", "repository": "https://github.com/iarna/wide-align", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\wide-align", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\wide-align\\LICENSE"}, "widest-line@4.0.1": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/widest-line", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\widest-line", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\widest-line\\license"}, "word-wrap@1.2.5": {"licenses": "MIT", "repository": "https://github.com/jonschlinkert/word-wrap", "publisher": "<PERSON>", "url": "https://github.com/jonschlinkert", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\word-wrap", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\word-wrap\\LICENSE"}, "wrap-ansi@6.2.0": {"licenses": "MIT", "repository": "https://github.com/chalk/wrap-ansi", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\wrap-ansi", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\wrap-ansi\\license"}, "wrap-ansi@7.0.0": {"licenses": "MIT", "repository": "https://github.com/chalk/wrap-ansi", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\wrap-ansi-cjs", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\wrap-ansi-cjs\\license"}, "wrap-ansi@8.1.0": {"licenses": "MIT", "repository": "https://github.com/chalk/wrap-ansi", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@isaacs\\cliui\\node_modules\\wrap-ansi", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\@isaacs\\cliui\\node_modules\\wrap-ansi\\license"}, "wrappy@1.0.2": {"licenses": "ISC", "repository": "https://github.com/npm/wrappy", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\wrappy", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\wrappy\\LICENSE"}, "write-file-atomic@3.0.3": {"licenses": "ISC", "repository": "https://github.com/npm/write-file-atomic", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\write-file-atomic", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\write-file-atomic\\LICENSE"}, "xdg-basedir@5.1.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/xdg-basedir", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\xdg-basedir", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\xdg-basedir\\license"}, "xmlbuilder2@3.1.1": {"licenses": "MIT", "repository": "https://github.com/oozcitak/xmlbuilder2", "publisher": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\xmlbuilder2", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\xmlbuilder2\\LICENSE"}, "xtend@4.0.2": {"licenses": "MIT", "repository": "https://github.com/Raynos/xtend", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\xtend", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\xtend\\LICENSE"}, "y18n@5.0.8": {"licenses": "ISC", "repository": "https://github.com/yargs/y18n", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\y18n", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\y18n\\LICENSE"}, "yallist@4.0.0": {"licenses": "ISC", "repository": "https://github.com/isaacs/yallist", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\yallist", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\yallist\\LICENSE"}, "yargs-parser@21.1.1": {"licenses": "ISC", "repository": "https://github.com/yargs/yargs-parser", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\yargs-parser", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\yargs-parser\\LICENSE.txt"}, "yargs@17.7.2": {"licenses": "MIT", "repository": "https://github.com/yargs/yargs", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\yargs", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\yargs\\LICENSE"}, "yocto-queue@0.1.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/yocto-queue", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\yocto-queue", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\yocto-queue\\license"}, "yoctocolors-cjs@2.1.2": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/yoctocolors", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\yoctocolors-cjs", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\yoctocolors-cjs\\license"}, "zod@3.25.74": {"licenses": "MIT", "repository": "https://github.com/colinhacks/zod", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\zod", "licenseFile": "C:\\Users\\<USER>\\Time_Stamp_Project\\development-tools\\node_modules\\zod\\LICENSE"}}, "sbom": {"format": "CycloneDX", "components": 553, "generated": true}}, "recommendations": []}
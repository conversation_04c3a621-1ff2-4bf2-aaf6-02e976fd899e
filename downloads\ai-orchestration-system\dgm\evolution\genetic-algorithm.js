/**
 * Genetic Algorithm Implementation for DGM
 * 
 * Implements evolutionary operations for agent populations:
 * - Parent selection strategies
 * - Genetic operations (mutation, crossover)
 * - Survivor selection
 * - Diversity maintenance
 */

const chalk = require('chalk');

class GeneticAlgorithm {
  constructor(config) {
    this.config = config;
    this.selectionMethod = config.get('genetics.selectionMethod', 'tournament');
    this.tournamentSize = config.get('genetics.tournamentSize', 3);
    this.mutationRate = config.get('evolution.mutationRate', 0.3);
    this.crossoverRate = config.get('evolution.crossoverRate', 0.7);
    this.elitismRate = config.get('evolution.elitismRate', 0.1);
    this.diversityThreshold = config.get('evolution.diversityThreshold', 0.8);
  }

  /**
   * Select parents for reproduction
   */
  selectParents(population) {
    const parentCount = Math.floor(population.length * 0.5); // Select 50% as parents
    const parents = [];

    switch (this.selectionMethod) {
      case 'tournament':
        parents.push(...this.tournamentSelection(population, parentCount));
        break;
      case 'roulette':
        parents.push(...this.rouletteSelection(population, parentCount));
        break;
      case 'rank':
        parents.push(...this.rankSelection(population, parentCount));
        break;
      default:
        throw new Error(`Unknown selection method: ${this.selectionMethod}`);
    }

    console.log(chalk.blue(`🎯 Selected ${parents.length} parents using ${this.selectionMethod} selection`));
    return parents;
  }

  /**
   * Tournament selection
   */
  tournamentSelection(population, count) {
    const parents = [];
    
    for (let i = 0; i < count; i++) {
      const tournament = [];
      
      // Select random individuals for tournament
      for (let j = 0; j < this.tournamentSize; j++) {
        const randomIndex = Math.floor(Math.random() * population.length);
        tournament.push(population[randomIndex]);
      }
      
      // Select the best from tournament
      const winner = tournament.reduce((best, current) => 
        current.fitness > best.fitness ? current : best
      );
      
      parents.push(winner);
    }
    
    return parents;
  }

  /**
   * Roulette wheel selection
   */
  rouletteSelection(population, count) {
    const parents = [];
    const totalFitness = population.reduce((sum, agent) => sum + agent.fitness, 0);
    
    if (totalFitness === 0) {
      // If all fitness is 0, select randomly
      return this.randomSelection(population, count);
    }
    
    for (let i = 0; i < count; i++) {
      const randomValue = Math.random() * totalFitness;
      let cumulativeFitness = 0;
      
      for (const agent of population) {
        cumulativeFitness += agent.fitness;
        if (cumulativeFitness >= randomValue) {
          parents.push(agent);
          break;
        }
      }
    }
    
    return parents;
  }

  /**
   * Rank-based selection
   */
  rankSelection(population, count) {
    // Sort population by fitness
    const sortedPopulation = [...population].sort((a, b) => b.fitness - a.fitness);
    
    // Assign ranks (higher rank = better fitness)
    const totalRank = (sortedPopulation.length * (sortedPopulation.length + 1)) / 2;
    const parents = [];
    
    for (let i = 0; i < count; i++) {
      const randomValue = Math.random() * totalRank;
      let cumulativeRank = 0;
      
      for (let j = 0; j < sortedPopulation.length; j++) {
        cumulativeRank += (sortedPopulation.length - j);
        if (cumulativeRank >= randomValue) {
          parents.push(sortedPopulation[j]);
          break;
        }
      }
    }
    
    return parents;
  }

  /**
   * Random selection (fallback)
   */
  randomSelection(population, count) {
    const parents = [];
    for (let i = 0; i < count; i++) {
      const randomIndex = Math.floor(Math.random() * population.length);
      parents.push(population[randomIndex]);
    }
    return parents;
  }

  /**
   * Select genetic operation (mutation or crossover)
   */
  selectGeneticOperation() {
    const random = Math.random();
    
    if (random < this.mutationRate) {
      return 'mutation';
    } else if (random < this.mutationRate + this.crossoverRate) {
      return 'crossover';
    } else {
      return 'mutation'; // Default to mutation
    }
  }

  /**
   * Select random parent from population
   */
  selectRandomParent(parents) {
    const randomIndex = Math.floor(Math.random() * parents.length);
    return parents[randomIndex];
  }

  /**
   * Select pair of parents for crossover
   */
  selectParentPair(parents) {
    if (parents.length < 2) {
      throw new Error('Need at least 2 parents for crossover');
    }
    
    const parent1Index = Math.floor(Math.random() * parents.length);
    let parent2Index;
    
    // Ensure different parents
    do {
      parent2Index = Math.floor(Math.random() * parents.length);
    } while (parent2Index === parent1Index);
    
    return [parents[parent1Index], parents[parent2Index]];
  }

  /**
   * Generate crossover instructions
   */
  generateCrossoverInstructions(parent1, parent2) {
    const strategies = this.config.get('genetics.crossoverStrategies', [
      'single_point',
      'two_point',
      'uniform',
      'semantic'
    ]);
    
    const strategy = strategies[Math.floor(Math.random() * strategies.length)];
    
    switch (strategy) {
      case 'single_point':
        return this.generateSinglePointCrossover(parent1, parent2);
      case 'two_point':
        return this.generateTwoPointCrossover(parent1, parent2);
      case 'uniform':
        return this.generateUniformCrossover(parent1, parent2);
      case 'semantic':
        return this.generateSemanticCrossover(parent1, parent2);
      default:
        return this.generateSinglePointCrossover(parent1, parent2);
    }
  }

  /**
   * Single-point crossover
   */
  generateSinglePointCrossover(parent1, parent2) {
    const crossoverPoint = Math.random();
    
    return {
      strategy: 'single_point',
      crossoverPoint,
      orchestratorFrom: crossoverPoint < 0.5 ? 'parent1' : 'parent2',
      crossFlowFrom: crossoverPoint < 0.5 ? 'parent1' : 'parent2',
      workflowsFrom: crossoverPoint < 0.5 ? 'parent1' : 'parent2'
    };
  }

  /**
   * Two-point crossover
   */
  generateTwoPointCrossover(parent1, parent2) {
    const point1 = Math.random();
    const point2 = Math.random();
    const [start, end] = [Math.min(point1, point2), Math.max(point1, point2)];
    
    return {
      strategy: 'two_point',
      startPoint: start,
      endPoint: end,
      orchestratorFrom: start < 0.33 && end > 0.33 ? 'parent2' : 'parent1',
      crossFlowFrom: start < 0.66 && end > 0.66 ? 'parent2' : 'parent1',
      workflowsFrom: start < 1.0 && end > 1.0 ? 'parent2' : 'parent1'
    };
  }

  /**
   * Uniform crossover
   */
  generateUniformCrossover(parent1, parent2) {
    return {
      strategy: 'uniform',
      orchestratorFrom: Math.random() < 0.5 ? 'parent1' : 'parent2',
      crossFlowFrom: Math.random() < 0.5 ? 'parent1' : 'parent2',
      workflowsFrom: Math.random() < 0.5 ? 'parent1' : 'parent2'
    };
  }

  /**
   * Semantic crossover (based on performance characteristics)
   */
  generateSemanticCrossover(parent1, parent2) {
    const instructions = {
      strategy: 'semantic'
    };
    
    // Choose components based on which parent performs better in each area
    if (parent1.metrics.performance > parent2.metrics.performance) {
      instructions.orchestratorFrom = 'parent1';
    } else {
      instructions.orchestratorFrom = 'parent2';
    }
    
    if (parent1.metrics.reliability > parent2.metrics.reliability) {
      instructions.crossFlowFrom = 'parent1';
    } else {
      instructions.crossFlowFrom = 'parent2';
    }
    
    if (parent1.metrics.functionality > parent2.metrics.functionality) {
      instructions.workflowsFrom = 'parent1';
    } else {
      instructions.workflowsFrom = 'parent2';
    }
    
    return instructions;
  }

  /**
   * Select survivors for next generation
   */
  selectSurvivors(evaluatedAgents) {
    const populationSize = this.config.get('evolution.populationSize', 20);
    const eliteCount = Math.floor(populationSize * this.elitismRate);
    
    // Sort by fitness (descending)
    const sortedAgents = [...evaluatedAgents].sort((a, b) => b.fitness - a.fitness);
    
    // Select elite agents
    const survivors = sortedAgents.slice(0, eliteCount);
    
    // Fill remaining spots with diverse selection
    const remaining = populationSize - eliteCount;
    const diverseAgents = this.selectDiverseAgents(
      sortedAgents.slice(eliteCount),
      remaining
    );
    
    survivors.push(...diverseAgents);
    
    console.log(chalk.green(`🏆 Selected ${survivors.length} survivors (${eliteCount} elite, ${diverseAgents.length} diverse)`));
    
    return survivors;
  }

  /**
   * Select diverse agents to maintain population diversity
   */
  selectDiverseAgents(candidates, count) {
    if (candidates.length <= count) {
      return candidates;
    }
    
    const selected = [];
    const remaining = [...candidates];
    
    // Select first agent randomly from top performers
    const firstIndex = Math.floor(Math.random() * Math.min(5, remaining.length));
    selected.push(remaining.splice(firstIndex, 1)[0]);
    
    // Select remaining agents based on diversity
    while (selected.length < count && remaining.length > 0) {
      let bestCandidate = null;
      let bestDiversityScore = -1;
      let bestIndex = -1;
      
      for (let i = 0; i < remaining.length; i++) {
        const candidate = remaining[i];
        const diversityScore = this.calculateDiversityScore(candidate, selected);
        
        if (diversityScore > bestDiversityScore) {
          bestDiversityScore = diversityScore;
          bestCandidate = candidate;
          bestIndex = i;
        }
      }
      
      if (bestCandidate) {
        selected.push(bestCandidate);
        remaining.splice(bestIndex, 1);
      } else {
        // Fallback: select randomly
        const randomIndex = Math.floor(Math.random() * remaining.length);
        selected.push(remaining.splice(randomIndex, 1)[0]);
      }
    }
    
    return selected;
  }

  /**
   * Calculate diversity score for an agent compared to selected agents
   */
  calculateDiversityScore(candidate, selectedAgents) {
    if (selectedAgents.length === 0) {
      return 1.0;
    }
    
    let totalDistance = 0;
    
    for (const selected of selectedAgents) {
      const distance = this.calculateAgentDistance(candidate, selected);
      totalDistance += distance;
    }
    
    return totalDistance / selectedAgents.length;
  }

  /**
   * Calculate distance between two agents
   */
  calculateAgentDistance(agent1, agent2) {
    // Calculate distance based on multiple factors
    let distance = 0;
    
    // Fitness distance
    const fitnessDistance = Math.abs(agent1.fitness - agent2.fitness);
    distance += fitnessDistance * 0.3;
    
    // Generation distance
    const generationDistance = Math.abs(agent1.generation - agent2.generation) / 10;
    distance += generationDistance * 0.2;
    
    // Type distance
    const typeDistance = agent1.type === agent2.type ? 0 : 1;
    distance += typeDistance * 0.2;
    
    // Metrics distance
    if (agent1.metrics && agent2.metrics) {
      const metricsDistance = Math.sqrt(
        Math.pow(agent1.metrics.performance - agent2.metrics.performance, 2) +
        Math.pow(agent1.metrics.reliability - agent2.metrics.reliability, 2) +
        Math.pow(agent1.metrics.functionality - agent2.metrics.functionality, 2) +
        Math.pow(agent1.metrics.safety - agent2.metrics.safety, 2)
      );
      distance += metricsDistance * 0.3;
    }
    
    return distance;
  }

  /**
   * Check if population has sufficient diversity
   */
  checkPopulationDiversity(population) {
    if (population.length < 2) {
      return true; // Can't measure diversity with less than 2 agents
    }
    
    let totalDistance = 0;
    let comparisons = 0;
    
    for (let i = 0; i < population.length; i++) {
      for (let j = i + 1; j < population.length; j++) {
        totalDistance += this.calculateAgentDistance(population[i], population[j]);
        comparisons++;
      }
    }
    
    const averageDistance = totalDistance / comparisons;
    const diversityScore = Math.min(1.0, averageDistance);
    
    console.log(chalk.blue(`📊 Population diversity score: ${diversityScore.toFixed(3)}`));
    
    return diversityScore >= this.diversityThreshold;
  }

  /**
   * Get genetic algorithm statistics
   */
  getGeneticStats(population) {
    const stats = {
      populationSize: population.length,
      averageFitness: 0,
      bestFitness: 0,
      worstFitness: 1,
      fitnessStdDev: 0,
      diversityScore: 0,
      generationSpread: 0
    };
    
    if (population.length === 0) {
      return stats;
    }
    
    // Calculate fitness statistics
    const fitnessValues = population.map(agent => agent.fitness);
    stats.averageFitness = fitnessValues.reduce((sum, f) => sum + f, 0) / fitnessValues.length;
    stats.bestFitness = Math.max(...fitnessValues);
    stats.worstFitness = Math.min(...fitnessValues);
    
    // Calculate standard deviation
    const variance = fitnessValues.reduce((sum, f) => sum + Math.pow(f - stats.averageFitness, 2), 0) / fitnessValues.length;
    stats.fitnessStdDev = Math.sqrt(variance);
    
    // Calculate diversity
    stats.diversityScore = this.checkPopulationDiversity(population) ? 1 : 0;
    
    // Calculate generation spread
    const generations = population.map(agent => agent.generation);
    stats.generationSpread = Math.max(...generations) - Math.min(...generations);
    
    return stats;
  }
}

module.exports = GeneticAlgorithm;

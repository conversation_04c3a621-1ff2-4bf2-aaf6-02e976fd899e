/**
 * Real-Time Conflict Resolution Service
 * 
 * Advanced conflict detection for overlapping functions/classes with intelligent
 * merging algorithms and trust-based prioritization.
 */

import { EventEmitter } from 'events';
import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger';
import {
  ConflictType,
  ConflictSeverity,
  ResolutionStrategy,
  ConflictStatus,
  ConflictDetection,
  ConflictingElement,
  ConflictContext,
  ResolutionResult,
  IntelligentMerger,
  MergeAlgorithm,
  MergeStrategy,
  MergeResult,
  TrustBasedPrioritization,
  AgentTrustScore,
  ConflictResolutionEngine,
  ConflictAnalytics,
  ConflictResolutionError,
  MergeError,
  ValidationError,
  CONFLICT_RESOLUTION_CONSTANTS,
  ConflictResolutionUtils
} from '../../shared/types/ConflictResolution';

export class ConflictResolutionService extends EventEmitter {
  private prisma: PrismaClient;
  private activeConflicts: Map<string, ConflictDetection> = new Map();
  private resolutionEngine: ConflictResolutionEngine;
  private intelligentMerger: IntelligentMergerEngine;
  private trustManager: TrustManager;
  private conflictDetector: ConflictDetector;
  private validationEngine: ValidationEngine;

  constructor(prisma: PrismaClient) {
    super();
    this.prisma = prisma;
    this.resolutionEngine = this.initializeResolutionEngine();
    this.intelligentMerger = new IntelligentMergerEngine();
    this.trustManager = new TrustManager();
    this.conflictDetector = new ConflictDetector();
    this.validationEngine = new ValidationEngine();

    this.setupEventHandlers();
    this.startRealTimeDetection();
  }

  /**
   * Detect conflicts in real-time
   */
  async detectConflicts(
    codeChanges: CodeChange[],
    context: ConflictContext
  ): Promise<ConflictDetection[]> {
    const conflicts: ConflictDetection[] = [];

    try {
      // Analyze code changes for potential conflicts
      const potentialConflicts = await this.conflictDetector.analyzeChanges(codeChanges, context);

      for (const potentialConflict of potentialConflicts) {
        // Validate if it's a real conflict
        const isRealConflict = await this.validateConflict(potentialConflict);
        
        if (isRealConflict) {
          const conflict = await this.createConflictDetection(potentialConflict, context);
          conflicts.push(conflict);
          
          // Store active conflict
          this.activeConflicts.set(conflict.id, conflict);
          
          // Emit conflict detected event
          this.emit('conflictDetected', {
            conflictId: conflict.id,
            type: conflict.type,
            severity: conflict.severity,
            involvedAgents: conflict.involvedAgents
          });

          logger.warn('Conflict detected', {
            conflictId: conflict.id,
            type: conflict.type,
            severity: conflict.severity,
            involvedAgents: conflict.involvedAgents
          });
        }
      }

      return conflicts;

    } catch (error) {
      logger.error('Conflict detection failed', { error: error.message });
      throw new ConflictResolutionError('Failed to detect conflicts', 'DETECTION_FAILED', undefined, error);
    }
  }

  /**
   * Resolve conflict using intelligent algorithms
   */
  async resolveConflict(conflictId: string): Promise<ResolutionResult> {
    const conflict = this.activeConflicts.get(conflictId);
    
    if (!conflict) {
      throw new ConflictResolutionError('Conflict not found', 'CONFLICT_NOT_FOUND', conflictId);
    }

    try {
      // Update conflict status
      conflict.status = ConflictStatus.ANALYZING;
      
      // Determine resolution strategy
      const strategy = await this.determineResolutionStrategy(conflict);
      conflict.resolutionStrategy = strategy;

      // Update status to resolving
      conflict.status = ConflictStatus.RESOLVING;

      let resolutionResult: ResolutionResult;

      // Apply resolution strategy
      switch (strategy) {
        case ResolutionStrategy.TRUST_BASED:
          resolutionResult = await this.resolveTrustBased(conflict);
          break;
        case ResolutionStrategy.MERGE_INTELLIGENT:
          resolutionResult = await this.resolveIntelligentMerge(conflict);
          break;
        case ResolutionStrategy.PERFORMANCE_BASED:
          resolutionResult = await this.resolvePerformanceBased(conflict);
          break;
        case ResolutionStrategy.VERSION_LATEST:
          resolutionResult = await this.resolveVersionLatest(conflict);
          break;
        case ResolutionStrategy.MANUAL_REVIEW:
          resolutionResult = await this.scheduleManualReview(conflict);
          break;
        default:
          throw new ConflictResolutionError('Unknown resolution strategy', 'UNKNOWN_STRATEGY', conflictId);
      }

      // Validate resolution
      const validationResults = await this.validationEngine.validateResolution(resolutionResult);
      resolutionResult.testResults = validationResults.testResults;

      // Update conflict with resolution
      conflict.resolutionResult = resolutionResult;
      conflict.status = ConflictStatus.RESOLVED;
      conflict.resolvedAt = new Date();

      // Update trust scores based on resolution outcome
      await this.trustManager.updateTrustScores(conflict, resolutionResult);

      // Emit resolution event
      this.emit('conflictResolved', {
        conflictId,
        strategy,
        success: true,
        resolutionTime: conflict.resolvedAt.getTime() - conflict.detectedAt.getTime()
      });

      logger.info('Conflict resolved', {
        conflictId,
        strategy,
        resolutionTime: conflict.resolvedAt.getTime() - conflict.detectedAt.getTime()
      });

      return resolutionResult;

    } catch (error) {
      conflict.status = ConflictStatus.FAILED;
      
      logger.error('Conflict resolution failed', {
        conflictId,
        error: error.message
      });

      throw new ConflictResolutionError('Failed to resolve conflict', 'RESOLUTION_FAILED', conflictId, error);
    }
  }

  /**
   * Get active conflicts
   */
  getActiveConflicts(): ConflictDetection[] {
    return Array.from(this.activeConflicts.values());
  }

  /**
   * Get conflict analytics
   */
  async getConflictAnalytics(timeRange?: { startDate: Date; endDate: Date }): Promise<ConflictAnalytics> {
    const conflicts = Array.from(this.activeConflicts.values());
    
    // Filter by time range if provided
    const filteredConflicts = timeRange 
      ? conflicts.filter(c => c.detectedAt >= timeRange.startDate && c.detectedAt <= timeRange.endDate)
      : conflicts;

    const analytics: ConflictAnalytics = {
      timeRange: timeRange || {
        startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
        endDate: new Date()
      },
      totalConflicts: filteredConflicts.length,
      conflictsByType: this.groupByType(filteredConflicts),
      conflictsBySeverity: this.groupBySeverity(filteredConflicts),
      resolutionsByStrategy: this.groupByStrategy(filteredConflicts),
      averageResolutionTime: this.calculateAverageResolutionTime(filteredConflicts),
      successRate: this.calculateSuccessRate(filteredConflicts),
      topConflictingAgents: await this.getTopConflictingAgents(filteredConflicts),
      conflictTrends: this.calculateConflictTrends(filteredConflicts),
      hotspots: this.identifyConflictHotspots(filteredConflicts)
    };

    return analytics;
  }

  /**
   * Update agent trust scores
   */
  async updateAgentTrustScore(agentId: string, metrics: Partial<AgentTrustScore>): Promise<void> {
    await this.trustManager.updateAgentTrustScore(agentId, metrics);
  }

  /**
   * Private helper methods
   */
  private async createConflictDetection(
    potentialConflict: any,
    context: ConflictContext
  ): Promise<ConflictDetection> {
    const conflictId = ConflictResolutionUtils.generateConflictId();

    const conflict: ConflictDetection = {
      id: conflictId,
      type: potentialConflict.type,
      severity: ConflictResolutionUtils.assessConflictSeverity(
        potentialConflict.similarity,
        context.impactRadius,
        context.businessCriticality
      ),
      status: ConflictStatus.DETECTED,
      detectedAt: new Date(),
      involvedAgents: potentialConflict.involvedAgents,
      conflictingElements: potentialConflict.elements,
      contextInfo: context,
      metadata: {
        detectionMethod: 'STATIC_ANALYSIS',
        confidence: potentialConflict.confidence,
        similarityScore: potentialConflict.similarity,
        complexityScore: potentialConflict.complexity,
        riskAssessment: await this.assessRisk(potentialConflict),
        historicalData: await this.getHistoricalData(potentialConflict.involvedAgents)
      }
    };

    return conflict;
  }

  private async determineResolutionStrategy(conflict: ConflictDetection): Promise<ResolutionStrategy> {
    // Get trust scores for involved agents
    const trustScores: Record<string, number> = {};
    for (const agentId of conflict.involvedAgents) {
      const trustScore = await this.trustManager.getAgentTrustScore(agentId);
      trustScores[agentId] = trustScore.overallTrust;
    }

    return ConflictResolutionUtils.selectResolutionStrategy(
      conflict.type,
      conflict.severity,
      trustScores
    );
  }

  private async resolveTrustBased(conflict: ConflictDetection): Promise<ResolutionResult> {
    const prioritization = await this.trustManager.prioritizeByTrust(conflict.involvedAgents);
    const selectedAgent = prioritization.selectedAgent;
    
    // Find the element from the most trusted agent
    const selectedElement = conflict.conflictingElements.find(e => e.sourceAgent === selectedAgent);
    
    if (!selectedElement) {
      throw new ConflictResolutionError('No element found for selected agent', 'ELEMENT_NOT_FOUND', conflict.id);
    }

    return {
      strategy: ResolutionStrategy.TRUST_BASED,
      selectedVersion: selectedElement.elementId,
      modifications: [{
        file: selectedElement.sourceFile,
        operation: 'MODIFY',
        lineStart: selectedElement.lineNumber,
        lineEnd: selectedElement.lineNumber,
        originalCode: '',
        modifiedCode: selectedElement.codeSnippet,
        reason: `Selected based on trust score: ${prioritization.finalScores[selectedAgent]}`,
        confidence: prioritization.confidence
      }],
      testResults: [],
      performanceMetrics: {
        executionTime: 0,
        memoryUsage: 0,
        cpuUsage: 0,
        throughput: 0,
        latency: 0,
        resourceEfficiency: 0
      },
      qualityMetrics: {
        codeComplexity: 0,
        maintainabilityIndex: 0,
        technicalDebt: 0,
        duplicateCode: 0,
        testCoverage: 0,
        documentationCoverage: 0
      },
      approvalRequired: conflict.severity === ConflictSeverity.CRITICAL,
      rollbackPlan: {
        rollbackId: `rollback_${conflict.id}`,
        steps: [],
        estimatedTime: 30000,
        riskLevel: 'LOW',
        dependencies: [],
        validationChecks: []
      }
    };
  }

  private async resolveIntelligentMerge(conflict: ConflictDetection): Promise<ResolutionResult> {
    const mergeResult = await this.intelligentMerger.mergeConflictingElements(
      conflict.conflictingElements,
      MergeAlgorithm.SEMANTIC_MERGE,
      MergeStrategy.COMBINE_FEATURES
    );

    if (!mergeResult.success) {
      throw new MergeError('Intelligent merge failed', conflict.id, mergeResult);
    }

    return {
      strategy: ResolutionStrategy.MERGE_INTELLIGENT,
      mergedCode: mergeResult.mergedCode,
      modifications: [{
        file: conflict.conflictingElements[0].sourceFile,
        operation: 'MODIFY',
        lineStart: conflict.conflictingElements[0].lineNumber,
        lineEnd: conflict.conflictingElements[0].lineNumber,
        originalCode: conflict.conflictingElements[0].codeSnippet,
        modifiedCode: mergeResult.mergedCode,
        reason: 'Intelligent merge of conflicting elements',
        confidence: mergeResult.qualityScore
      }],
      testResults: [],
      performanceMetrics: {
        executionTime: 0,
        memoryUsage: 0,
        cpuUsage: 0,
        throughput: 0,
        latency: 0,
        resourceEfficiency: 0
      },
      qualityMetrics: {
        codeComplexity: 0,
        maintainabilityIndex: 0,
        technicalDebt: 0,
        duplicateCode: 0,
        testCoverage: 0,
        documentationCoverage: 0
      },
      approvalRequired: mergeResult.qualityScore < 0.8,
      rollbackPlan: {
        rollbackId: `rollback_${conflict.id}`,
        steps: [],
        estimatedTime: 60000,
        riskLevel: 'MEDIUM',
        dependencies: [],
        validationChecks: []
      }
    };
  }

  private async resolvePerformanceBased(conflict: ConflictDetection): Promise<ResolutionResult> {
    // Mock implementation - would run performance tests on each conflicting element
    const performanceResults = await this.runPerformanceTests(conflict.conflictingElements);
    const bestPerforming = performanceResults.reduce((best, current) => 
      current.score > best.score ? current : best
    );

    return {
      strategy: ResolutionStrategy.PERFORMANCE_BASED,
      selectedVersion: bestPerforming.elementId,
      modifications: [],
      testResults: [],
      performanceMetrics: bestPerforming.metrics,
      qualityMetrics: {
        codeComplexity: 0,
        maintainabilityIndex: 0,
        technicalDebt: 0,
        duplicateCode: 0,
        testCoverage: 0,
        documentationCoverage: 0
      },
      approvalRequired: false,
      rollbackPlan: {
        rollbackId: `rollback_${conflict.id}`,
        steps: [],
        estimatedTime: 30000,
        riskLevel: 'LOW',
        dependencies: [],
        validationChecks: []
      }
    };
  }

  private async resolveVersionLatest(conflict: ConflictDetection): Promise<ResolutionResult> {
    // Select the most recently modified element
    const latestElement = conflict.conflictingElements.reduce((latest, current) => 
      current.lastModified > latest.lastModified ? current : latest
    );

    return {
      strategy: ResolutionStrategy.VERSION_LATEST,
      selectedVersion: latestElement.elementId,
      modifications: [],
      testResults: [],
      performanceMetrics: {
        executionTime: 0,
        memoryUsage: 0,
        cpuUsage: 0,
        throughput: 0,
        latency: 0,
        resourceEfficiency: 0
      },
      qualityMetrics: {
        codeComplexity: 0,
        maintainabilityIndex: 0,
        technicalDebt: 0,
        duplicateCode: 0,
        testCoverage: 0,
        documentationCoverage: 0
      },
      approvalRequired: false,
      rollbackPlan: {
        rollbackId: `rollback_${conflict.id}`,
        steps: [],
        estimatedTime: 15000,
        riskLevel: 'LOW',
        dependencies: [],
        validationChecks: []
      }
    };
  }

  private async scheduleManualReview(conflict: ConflictDetection): Promise<ResolutionResult> {
    // Schedule for manual review
    conflict.status = ConflictStatus.MANUAL_INTERVENTION;

    return {
      strategy: ResolutionStrategy.MANUAL_REVIEW,
      modifications: [],
      testResults: [],
      performanceMetrics: {
        executionTime: 0,
        memoryUsage: 0,
        cpuUsage: 0,
        throughput: 0,
        latency: 0,
        resourceEfficiency: 0
      },
      qualityMetrics: {
        codeComplexity: 0,
        maintainabilityIndex: 0,
        technicalDebt: 0,
        duplicateCode: 0,
        testCoverage: 0,
        documentationCoverage: 0
      },
      approvalRequired: true,
      rollbackPlan: {
        rollbackId: `rollback_${conflict.id}`,
        steps: [],
        estimatedTime: 0,
        riskLevel: 'HIGH',
        dependencies: [],
        validationChecks: []
      }
    };
  }

  private async validateConflict(potentialConflict: any): Promise<boolean> {
    // Implement conflict validation logic
    return potentialConflict.confidence > 0.7;
  }

  private async assessRisk(potentialConflict: any): Promise<any> {
    // Mock risk assessment
    return {
      breakingChangeRisk: 0.3,
      performanceRisk: 0.2,
      securityRisk: 0.1,
      maintainabilityRisk: 0.4,
      overallRisk: 0.25,
      riskFactors: ['Code complexity', 'Multiple agents involved'],
      mitigationSuggestions: ['Add comprehensive tests', 'Code review required']
    };
  }

  private async getHistoricalData(agentIds: string[]): Promise<any> {
    // Mock historical data
    return {
      previousConflicts: 5,
      resolutionSuccessRate: 0.85,
      averageResolutionTime: 120000,
      commonPatterns: ['Function naming conflicts', 'Parameter type mismatches'],
      agentReliability: agentIds.reduce((acc, id) => ({ ...acc, [id]: 0.8 }), {})
    };
  }

  private async runPerformanceTests(elements: ConflictingElement[]): Promise<any[]> {
    // Mock performance testing
    return elements.map(element => ({
      elementId: element.elementId,
      score: Math.random() * 100,
      metrics: {
        executionTime: Math.random() * 1000,
        memoryUsage: Math.random() * 100,
        cpuUsage: Math.random() * 50,
        throughput: Math.random() * 1000,
        latency: Math.random() * 100,
        resourceEfficiency: Math.random()
      }
    }));
  }

  private groupByType(conflicts: ConflictDetection[]): Record<ConflictType, number> {
    const result = {} as Record<ConflictType, number>;
    for (const conflict of conflicts) {
      result[conflict.type] = (result[conflict.type] || 0) + 1;
    }
    return result;
  }

  private groupBySeverity(conflicts: ConflictDetection[]): Record<ConflictSeverity, number> {
    const result = {} as Record<ConflictSeverity, number>;
    for (const conflict of conflicts) {
      result[conflict.severity] = (result[conflict.severity] || 0) + 1;
    }
    return result;
  }

  private groupByStrategy(conflicts: ConflictDetection[]): Record<ResolutionStrategy, number> {
    const result = {} as Record<ResolutionStrategy, number>;
    for (const conflict of conflicts) {
      if (conflict.resolutionStrategy) {
        result[conflict.resolutionStrategy] = (result[conflict.resolutionStrategy] || 0) + 1;
      }
    }
    return result;
  }

  private calculateAverageResolutionTime(conflicts: ConflictDetection[]): number {
    const resolvedConflicts = conflicts.filter(c => c.resolvedAt);
    if (resolvedConflicts.length === 0) return 0;

    const totalTime = resolvedConflicts.reduce((sum, conflict) => 
      sum + (conflict.resolvedAt!.getTime() - conflict.detectedAt.getTime()), 0
    );

    return totalTime / resolvedConflicts.length;
  }

  private calculateSuccessRate(conflicts: ConflictDetection[]): number {
    if (conflicts.length === 0) return 0;
    const resolvedConflicts = conflicts.filter(c => c.status === ConflictStatus.RESOLVED);
    return resolvedConflicts.length / conflicts.length;
  }

  private async getTopConflictingAgents(conflicts: ConflictDetection[]): Promise<any[]> {
    // Mock implementation
    return [];
  }

  private calculateConflictTrends(conflicts: ConflictDetection[]): any[] {
    // Mock implementation
    return [];
  }

  private identifyConflictHotspots(conflicts: ConflictDetection[]): any[] {
    // Mock implementation
    return [];
  }

  private initializeResolutionEngine(): ConflictResolutionEngine {
    return {
      engineId: 'conflict_resolution_engine_v1',
      version: '1.0.0',
      capabilities: ['real_time_detection', 'intelligent_merging', 'trust_based_prioritization'],
      supportedLanguages: ['javascript', 'typescript', 'python', 'java', 'go'],
      algorithms: [MergeAlgorithm.SEMANTIC_MERGE, MergeAlgorithm.STRUCTURAL_MERGE, MergeAlgorithm.TRUST_WEIGHTED_MERGE],
      strategies: Object.values(ResolutionStrategy),
      configuration: {
        enableRealTimeDetection: true,
        enableIntelligentMerging: true,
        enableTrustBasedPrioritization: true,
        conflictThreshold: 0.7,
        trustThreshold: 0.6,
        qualityThreshold: 0.7,
        autoResolutionEnabled: true,
        manualReviewRequired: false,
        rollbackOnFailure: true,
        maxResolutionTime: CONFLICT_RESOLUTION_CONSTANTS.MAX_RESOLUTION_TIME
      },
      performance: {
        totalConflictsDetected: 0,
        totalConflictsResolved: 0,
        averageResolutionTime: 0,
        successRate: 0,
        falsePositiveRate: 0,
        falseNegativeRate: 0,
        userSatisfactionScore: 0,
        systemStabilityImpact: 0
      }
    };
  }

  private setupEventHandlers(): void {
    this.on('conflictDetected', (event) => {
      // Auto-resolve if configuration allows
      if (this.resolutionEngine.configuration.autoResolutionEnabled) {
        setTimeout(() => {
          this.resolveConflict(event.conflictId).catch(error => {
            logger.error('Auto-resolution failed', { conflictId: event.conflictId, error: error.message });
          });
        }, 1000); // 1 second delay
      }
    });
  }

  private startRealTimeDetection(): void {
    // Start real-time conflict detection
    setInterval(() => {
      // This would monitor for code changes and detect conflicts
      // Implementation would integrate with version control systems
    }, CONFLICT_RESOLUTION_CONSTANTS.CONFLICT_DETECTION_INTERVAL);
  }
}

// Helper classes
interface CodeChange {
  file: string;
  operation: 'ADD' | 'MODIFY' | 'DELETE';
  content: string;
  author: string;
  timestamp: Date;
}

class ConflictDetector {
  async analyzeChanges(changes: CodeChange[], context: ConflictContext): Promise<any[]> {
    // Mock implementation - would use AST analysis, semantic analysis, etc.
    return [];
  }
}

class IntelligentMergerEngine {
  async mergeConflictingElements(
    elements: ConflictingElement[],
    algorithm: MergeAlgorithm,
    strategy: MergeStrategy
  ): Promise<MergeResult> {
    // Mock implementation - would use sophisticated merging algorithms
    return {
      success: true,
      mergedCode: elements.map(e => e.codeSnippet).join('\n'),
      conflictsResolved: elements.length,
      conflictsRemaining: 0,
      qualityScore: 0.85,
      testCompatibility: true,
      performanceImpact: 0.05,
      warnings: [],
      recommendations: ['Add unit tests for merged code']
    };
  }
}

class TrustManager {
  private trustScores: Map<string, AgentTrustScore> = new Map();

  async getAgentTrustScore(agentId: string): Promise<AgentTrustScore> {
    return this.trustScores.get(agentId) || {
      agentId,
      overallTrust: 0.7,
      codeQuality: 0.7,
      reliability: 0.8,
      expertise: 0.6,
      collaboration: 0.7,
      recentPerformance: 0.7,
      domainExpertise: {},
      lastUpdated: new Date()
    };
  }

  async updateAgentTrustScore(agentId: string, metrics: Partial<AgentTrustScore>): Promise<void> {
    const existing = await this.getAgentTrustScore(agentId);
    const updated = { ...existing, ...metrics, lastUpdated: new Date() };
    this.trustScores.set(agentId, updated);
  }

  async prioritizeByTrust(agentIds: string[]): Promise<TrustBasedPrioritization> {
    const trustScores: Record<string, AgentTrustScore> = {};
    const finalScores: Record<string, number> = {};

    for (const agentId of agentIds) {
      const trustScore = await this.getAgentTrustScore(agentId);
      trustScores[agentId] = trustScore;
      finalScores[agentId] = trustScore.overallTrust;
    }

    const selectedAgent = Object.entries(finalScores).reduce((best, [agentId, score]) => 
      score > finalScores[best] ? agentId : best, agentIds[0]
    );

    return {
      agentTrustScores: trustScores,
      codeQualityMetrics: {},
      historicalPerformance: {},
      priorityMatrix: {
        trustWeight: 0.4,
        qualityWeight: 0.3,
        performanceWeight: 0.2,
        recentActivityWeight: 0.1,
        domainExpertiseWeight: 0.0,
        collaborationWeight: 0.0,
        finalScores,
        selectedAgent,
        confidence: 0.8
      },
      decisionRationale: `Selected ${selectedAgent} based on highest trust score: ${finalScores[selectedAgent]}`
    };
  }

  async updateTrustScores(conflict: ConflictDetection, result: ResolutionResult): Promise<void> {
    // Update trust scores based on resolution outcome
    for (const agentId of conflict.involvedAgents) {
      const currentScore = await this.getAgentTrustScore(agentId);
      
      // Adjust trust score based on resolution success
      const adjustment = result.strategy === ResolutionStrategy.TRUST_BASED ? 0.05 : 0.02;
      const newOverallTrust = Math.min(1.0, currentScore.overallTrust + adjustment);
      
      await this.updateAgentTrustScore(agentId, {
        overallTrust: newOverallTrust,
        recentPerformance: newOverallTrust
      });
    }
  }
}

class ValidationEngine {
  async validateResolution(result: ResolutionResult): Promise<any> {
    // Mock validation
    return {
      testResults: [],
      validationPassed: true
    };
  }
}

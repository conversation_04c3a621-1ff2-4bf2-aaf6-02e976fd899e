import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Card,
  CardContent,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  Chip,
  Alert,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Tooltip,
  Tabs,
  Tab,
  Badge,
  Avatar,
  Switch,
  FormControlLabel,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import {
  Security,
  Shield,
  Person,
  Group,
  Assignment,
  Visibility,
  Edit,
  Delete,
  Add,
  Refresh,
  Download,
  Warning,
  CheckCircle,
  Error,
  Info,
  ExpandMore,
  Timeline,
  Notifications,
  Comment,
  Activity,
} from '@mui/icons-material';

interface SecurityDashboardProps {
  onCreateRole?: (data: any) => void;
  onUpdateRole?: (id: string, data: any) => void;
  onDeleteRole?: (id: string) => void;
  onAssignRole?: (userId: string, roleId: string) => void;
  onRemoveRole?: (userId: string, roleId: string) => void;
  onExportAuditLog?: (filters: any) => void;
  onRefresh?: () => void;
}

interface Role {
  id: string;
  name: string;
  description: string;
  permissions: Permission[];
  hierarchy: number;
  isSystemRole: boolean;
  userCount?: number;
}

interface Permission {
  id: string;
  name: string;
  resource: string;
  action: string;
  conditions?: Record<string, any>;
}

interface User {
  id: string;
  username: string;
  email: string;
  roles: Role[];
  lastLogin?: Date;
  isActive: boolean;
}

interface AuditLogEntry {
  id: string;
  userId: string;
  username?: string;
  action: string;
  resource: string;
  resourceId?: string;
  result: 'GRANTED' | 'DENIED';
  reason?: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`security-tabpanel-${index}`}
      aria-labelledby={`security-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

export const SecurityDashboard: React.FC<SecurityDashboardProps> = ({
  onCreateRole,
  onUpdateRole,
  onDeleteRole,
  onAssignRole,
  onRemoveRole,
  onExportAuditLog,
  onRefresh,
}) => {
  const [tabValue, setTabValue] = useState(0);
  const [roles, setRoles] = useState<Role[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [auditLogs, setAuditLogs] = useState<AuditLogEntry[]>([]);
  const [statistics, setStatistics] = useState<any>(null);
  
  const [createRoleDialog, setCreateRoleDialog] = useState(false);
  const [editRoleDialog, setEditRoleDialog] = useState(false);
  const [assignRoleDialog, setAssignRoleDialog] = useState(false);
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const [newRole, setNewRole] = useState({
    name: '',
    description: '',
    hierarchy: 50,
    permissions: [] as Permission[],
  });

  const [auditFilters, setAuditFilters] = useState({
    userId: '',
    resource: '',
    action: '',
    result: '',
    startDate: '',
    endDate: '',
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setIsLoading(true);
      
      const [rolesResponse, usersResponse, auditResponse, statsResponse] = await Promise.all([
        fetch('/api/security/roles'),
        fetch('/api/security/users'),
        fetch('/api/security/audit-logs'),
        fetch('/api/security/statistics'),
      ]);

      if (rolesResponse.ok && usersResponse.ok && auditResponse.ok && statsResponse.ok) {
        const rolesData = await rolesResponse.json();
        const usersData = await usersResponse.json();
        const auditData = await auditResponse.json();
        const statsData = await statsResponse.json();
        
        setRoles(rolesData.data);
        setUsers(usersData.data);
        setAuditLogs(auditData.data);
        setStatistics(statsData.data);
        setError(null);
      } else {
        throw new Error('Failed to load security data');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to load data');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateRole = async () => {
    try {
      await onCreateRole?.(newRole);
      setCreateRoleDialog(false);
      setNewRole({
        name: '',
        description: '',
        hierarchy: 50,
        permissions: [],
      });
      loadData();
    } catch (err: any) {
      setError(err.message || 'Failed to create role');
    }
  };

  const handleUpdateRole = async () => {
    if (!selectedRole) return;

    try {
      await onUpdateRole?.(selectedRole.id, newRole);
      setEditRoleDialog(false);
      setSelectedRole(null);
      loadData();
    } catch (err: any) {
      setError(err.message || 'Failed to update role');
    }
  };

  const handleDeleteRole = async (roleId: string) => {
    if (window.confirm('Are you sure you want to delete this role?')) {
      try {
        await onDeleteRole?.(roleId);
        loadData();
      } catch (err: any) {
        setError(err.message || 'Failed to delete role');
      }
    }
  };

  const handleAssignRole = async () => {
    if (!selectedUser || !selectedRole) return;

    try {
      await onAssignRole?.(selectedUser.id, selectedRole.id);
      setAssignRoleDialog(false);
      setSelectedUser(null);
      setSelectedRole(null);
      loadData();
    } catch (err: any) {
      setError(err.message || 'Failed to assign role');
    }
  };

  const handleRemoveRole = async (userId: string, roleId: string) => {
    if (window.confirm('Are you sure you want to remove this role from the user?')) {
      try {
        await onRemoveRole?.(userId, roleId);
        loadData();
      } catch (err: any) {
        setError(err.message || 'Failed to remove role');
      }
    }
  };

  const openEditRoleDialog = (role: Role) => {
    setSelectedRole(role);
    setNewRole({
      name: role.name,
      description: role.description,
      hierarchy: role.hierarchy,
      permissions: role.permissions,
    });
    setEditRoleDialog(true);
  };

  const getResultColor = (result: string) => {
    return result === 'GRANTED' ? 'success' : 'error';
  };

  const getResultIcon = (result: string) => {
    return result === 'GRANTED' ? <CheckCircle /> : <Error />;
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleExportAuditLog = () => {
    onExportAuditLog?.(auditFilters);
  };

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h5">Security & Compliance</Typography>
        <Box>
          <Button
            startIcon={<Refresh />}
            onClick={onRefresh}
            sx={{ mr: 1 }}
          >
            Refresh
          </Button>
          <Button
            startIcon={<Download />}
            variant="outlined"
            onClick={handleExportAuditLog}
            sx={{ mr: 1 }}
          >
            Export Audit Log
          </Button>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Security Statistics */}
      {statistics && (
        <Grid container spacing={3} mb={3}>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <Shield color="primary" sx={{ mr: 2 }} />
                  <Box>
                    <Typography variant="h6">Total Roles</Typography>
                    <Typography variant="h4" color="primary">
                      {statistics.totalRoles}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <Group color="primary" sx={{ mr: 2 }} />
                  <Box>
                    <Typography variant="h6">Active Users</Typography>
                    <Typography variant="h4" color="primary">
                      {statistics.activeUsers}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <CheckCircle color="success" sx={{ mr: 2 }} />
                  <Box>
                    <Typography variant="h6">Access Granted</Typography>
                    <Typography variant="h4" color="success.main">
                      {statistics.accessGranted}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <Error color="error" sx={{ mr: 2 }} />
                  <Box>
                    <Typography variant="h6">Access Denied</Typography>
                    <Typography variant="h4" color="error.main">
                      {statistics.accessDenied}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs value={tabValue} onChange={handleTabChange} aria-label="security tabs">
          <Tab label="Roles & Permissions" icon={<Shield />} />
          <Tab label="User Management" icon={<Group />} />
          <Tab label="Audit Logs" icon={<Timeline />} />
          <Tab label="Compliance" icon={<Security />} />
        </Tabs>
      </Paper>

      {/* Roles & Permissions Tab */}
      <TabPanel value={tabValue} index={0}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Typography variant="h6">Roles & Permissions</Typography>
          <Button
            startIcon={<Add />}
            variant="contained"
            onClick={() => setCreateRoleDialog(true)}
          >
            Create Role
          </Button>
        </Box>

        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Role Name</TableCell>
                <TableCell>Description</TableCell>
                <TableCell>Hierarchy</TableCell>
                <TableCell>Permissions</TableCell>
                <TableCell>Users</TableCell>
                <TableCell>System Role</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {roles.map((role) => (
                <TableRow key={role.id}>
                  <TableCell>
                    <Box display="flex" alignItems="center">
                      <Shield sx={{ mr: 1 }} />
                      <Typography variant="body2">{role.name}</Typography>
                    </Box>
                  </TableCell>
                  <TableCell>{role.description}</TableCell>
                  <TableCell>
                    <Chip label={role.hierarchy} size="small" />
                  </TableCell>
                  <TableCell>
                    <Chip label={`${role.permissions.length} permissions`} size="small" />
                  </TableCell>
                  <TableCell>
                    <Badge badgeContent={role.userCount || 0} color="primary">
                      <Group />
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {role.isSystemRole ? (
                      <Chip label="System" color="primary" size="small" />
                    ) : (
                      <Chip label="Custom" color="default" size="small" />
                    )}
                  </TableCell>
                  <TableCell>
                    <Tooltip title="View Details">
                      <IconButton size="small">
                        <Visibility />
                      </IconButton>
                    </Tooltip>
                    {!role.isSystemRole && (
                      <>
                        <Tooltip title="Edit">
                          <IconButton size="small" onClick={() => openEditRoleDialog(role)}>
                            <Edit />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Delete">
                          <IconButton size="small" onClick={() => handleDeleteRole(role.id)}>
                            <Delete />
                          </IconButton>
                        </Tooltip>
                      </>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </TabPanel>

      {/* User Management Tab */}
      <TabPanel value={tabValue} index={1}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Typography variant="h6">User Management</Typography>
          <Button
            startIcon={<Assignment />}
            variant="contained"
            onClick={() => setAssignRoleDialog(true)}
          >
            Assign Role
          </Button>
        </Box>

        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>User</TableCell>
                <TableCell>Email</TableCell>
                <TableCell>Roles</TableCell>
                <TableCell>Last Login</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {users.map((user) => (
                <TableRow key={user.id}>
                  <TableCell>
                    <Box display="flex" alignItems="center">
                      <Avatar sx={{ mr: 2, width: 32, height: 32 }}>
                        {user.username.charAt(0).toUpperCase()}
                      </Avatar>
                      <Typography variant="body2">{user.username}</Typography>
                    </Box>
                  </TableCell>
                  <TableCell>{user.email}</TableCell>
                  <TableCell>
                    <Box display="flex" gap={0.5} flexWrap="wrap">
                      {user.roles.slice(0, 2).map((role) => (
                        <Chip
                          key={role.id}
                          label={role.name}
                          size="small"
                          onDelete={() => handleRemoveRole(user.id, role.id)}
                        />
                      ))}
                      {user.roles.length > 2 && (
                        <Chip label={`+${user.roles.length - 2}`} size="small" />
                      )}
                    </Box>
                  </TableCell>
                  <TableCell>
                    {user.lastLogin ? new Date(user.lastLogin).toLocaleDateString() : 'Never'}
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={user.isActive ? 'Active' : 'Inactive'}
                      color={user.isActive ? 'success' : 'default'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Tooltip title="View Details">
                      <IconButton size="small">
                        <Visibility />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Edit">
                      <IconButton size="small">
                        <Edit />
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </TabPanel>

      {/* Audit Logs Tab */}
      <TabPanel value={tabValue} index={2}>
        <Typography variant="h6" mb={3}>Audit Logs</Typography>

        {/* Audit Filters */}
        <Paper sx={{ p: 2, mb: 3 }}>
          <Grid container spacing={2}>
            <Grid item xs={12} md={2}>
              <TextField
                fullWidth
                label="User ID"
                value={auditFilters.userId}
                onChange={(e) => setAuditFilters(prev => ({ ...prev, userId: e.target.value }))}
                size="small"
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <TextField
                fullWidth
                label="Resource"
                value={auditFilters.resource}
                onChange={(e) => setAuditFilters(prev => ({ ...prev, resource: e.target.value }))}
                size="small"
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <TextField
                fullWidth
                label="Action"
                value={auditFilters.action}
                onChange={(e) => setAuditFilters(prev => ({ ...prev, action: e.target.value }))}
                size="small"
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>Result</InputLabel>
                <Select
                  value={auditFilters.result}
                  label="Result"
                  onChange={(e) => setAuditFilters(prev => ({ ...prev, result: e.target.value }))}
                >
                  <MenuItem value="">All</MenuItem>
                  <MenuItem value="GRANTED">Granted</MenuItem>
                  <MenuItem value="DENIED">Denied</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <TextField
                fullWidth
                label="Start Date"
                type="date"
                value={auditFilters.startDate}
                onChange={(e) => setAuditFilters(prev => ({ ...prev, startDate: e.target.value }))}
                size="small"
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <TextField
                fullWidth
                label="End Date"
                type="date"
                value={auditFilters.endDate}
                onChange={(e) => setAuditFilters(prev => ({ ...prev, endDate: e.target.value }))}
                size="small"
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
          </Grid>
        </Paper>

        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Timestamp</TableCell>
                <TableCell>User</TableCell>
                <TableCell>Action</TableCell>
                <TableCell>Resource</TableCell>
                <TableCell>Result</TableCell>
                <TableCell>Reason</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {auditLogs.map((log) => (
                <TableRow key={log.id}>
                  <TableCell>
                    {new Date(log.timestamp).toLocaleString()}
                  </TableCell>
                  <TableCell>{log.username || log.userId}</TableCell>
                  <TableCell>{log.action}</TableCell>
                  <TableCell>
                    {log.resource}
                    {log.resourceId && (
                      <Typography variant="caption" display="block" color="text.secondary">
                        ID: {log.resourceId}
                      </Typography>
                    )}
                  </TableCell>
                  <TableCell>
                    <Box display="flex" alignItems="center">
                      {getResultIcon(log.result)}
                      <Chip
                        label={log.result}
                        color={getResultColor(log.result)}
                        size="small"
                        sx={{ ml: 1 }}
                      />
                    </Box>
                  </TableCell>
                  <TableCell>{log.reason || '-'}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </TabPanel>

      {/* Compliance Tab */}
      <TabPanel value={tabValue} index={3}>
        <Typography variant="h6" mb={3}>Compliance Overview</Typography>
        
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Security Policies
                </Typography>
                <List>
                  <ListItem>
                    <ListItemIcon>
                      <CheckCircle color="success" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Role-Based Access Control"
                      secondary="RBAC system is active and configured"
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <CheckCircle color="success" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Audit Logging"
                      secondary="All access attempts are logged"
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <Warning color="warning" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Password Policy"
                      secondary="Consider implementing stronger password requirements"
                    />
                  </ListItem>
                </List>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Data Protection
                </Typography>
                <List>
                  <ListItem>
                    <ListItemIcon>
                      <CheckCircle color="success" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Data Encryption"
                      secondary="Sensitive data is encrypted at rest"
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <CheckCircle color="success" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Access Monitoring"
                      secondary="Real-time access monitoring is enabled"
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <Info color="info" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Data Retention"
                      secondary="Review data retention policies regularly"
                    />
                  </ListItem>
                </List>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      {/* Create Role Dialog */}
      <Dialog open={createRoleDialog} onClose={() => setCreateRoleDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Create New Role</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Role Name"
                value={newRole.name}
                onChange={(e) => setNewRole(prev => ({ ...prev, name: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Hierarchy Level"
                type="number"
                value={newRole.hierarchy}
                onChange={(e) => setNewRole(prev => ({ ...prev, hierarchy: parseInt(e.target.value) }))}
                helperText="Higher number = more privileged"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                multiline
                rows={3}
                value={newRole.description}
                onChange={(e) => setNewRole(prev => ({ ...prev, description: e.target.value }))}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateRoleDialog(false)}>Cancel</Button>
          <Button onClick={handleCreateRole} variant="contained">Create</Button>
        </DialogActions>
      </Dialog>

      {/* Edit Role Dialog */}
      <Dialog open={editRoleDialog} onClose={() => setEditRoleDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Edit Role</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Role Name"
                value={newRole.name}
                onChange={(e) => setNewRole(prev => ({ ...prev, name: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Hierarchy Level"
                type="number"
                value={newRole.hierarchy}
                onChange={(e) => setNewRole(prev => ({ ...prev, hierarchy: parseInt(e.target.value) }))}
                helperText="Higher number = more privileged"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                multiline
                rows={3}
                value={newRole.description}
                onChange={(e) => setNewRole(prev => ({ ...prev, description: e.target.value }))}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditRoleDialog(false)}>Cancel</Button>
          <Button onClick={handleUpdateRole} variant="contained">Update</Button>
        </DialogActions>
      </Dialog>

      {/* Assign Role Dialog */}
      <Dialog open={assignRoleDialog} onClose={() => setAssignRoleDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Assign Role to User</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>Select User</InputLabel>
                <Select
                  value={selectedUser?.id || ''}
                  label="Select User"
                  onChange={(e) => {
                    const user = users.find(u => u.id === e.target.value);
                    setSelectedUser(user || null);
                  }}
                >
                  {users.map((user) => (
                    <MenuItem key={user.id} value={user.id}>
                      {user.username} ({user.email})
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>Select Role</InputLabel>
                <Select
                  value={selectedRole?.id || ''}
                  label="Select Role"
                  onChange={(e) => {
                    const role = roles.find(r => r.id === e.target.value);
                    setSelectedRole(role || null);
                  }}
                >
                  {roles.map((role) => (
                    <MenuItem key={role.id} value={role.id}>
                      {role.name} (Level {role.hierarchy})
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAssignRoleDialog(false)}>Cancel</Button>
          <Button onClick={handleAssignRole} variant="contained">Assign</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default SecurityDashboard;

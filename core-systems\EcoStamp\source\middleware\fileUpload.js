/**
 * File Upload Middleware
 * Handles document uploads with security and validation
 */

import multer from 'multer';
import path from 'path';
import fs from 'fs/promises';
import crypto from 'crypto';

// Allowed file types and their MIME types
const ALLOWED_FILE_TYPES = {
  // Documents
  'application/pdf': '.pdf',
  'application/msword': '.doc',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '.docx',
  'text/plain': '.txt',
  'text/markdown': '.md',
  'application/rtf': '.rtf',
  
  // Spreadsheets
  'application/vnd.ms-excel': '.xls',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': '.xlsx',
  'text/csv': '.csv',
  
  // Presentations
  'application/vnd.ms-powerpoint': '.ppt',
  'application/vnd.openxmlformats-officedocument.presentationml.presentation': '.pptx',
  
  // Images (for OCR processing)
  'image/jpeg': '.jpg',
  'image/png': '.png',
  'image/gif': '.gif',
  'image/webp': '.webp',
  
  // Code files
  'application/json': '.json',
  'application/xml': '.xml',
  'text/html': '.html',
  'text/css': '.css',
  'text/javascript': '.js',
  'application/javascript': '.js'
};

// File size limits (in bytes)
const FILE_SIZE_LIMITS = {
  document: 10 * 1024 * 1024,  // 10MB for documents
  image: 5 * 1024 * 1024,      // 5MB for images
  code: 1 * 1024 * 1024,       // 1MB for code files
  default: 10 * 1024 * 1024    // 10MB default
};

// Configure multer storage
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    const uploadDir = path.join(process.cwd(), 'uploads', 'temp');
    try {
      await fs.mkdir(uploadDir, { recursive: true });
      cb(null, uploadDir);
    } catch (error) {
      cb(error);
    }
  },
  filename: (req, file, cb) => {
    // Generate secure filename
    const timestamp = Date.now();
    const randomBytes = crypto.randomBytes(8).toString('hex');
    const extension = ALLOWED_FILE_TYPES[file.mimetype] || '.bin';
    const filename = `${timestamp}-${randomBytes}${extension}`;
    cb(null, filename);
  }
});

// File filter function
const fileFilter = (req, file, cb) => {
  // Check if file type is allowed
  if (ALLOWED_FILE_TYPES[file.mimetype]) {
    cb(null, true);
  } else {
    cb(new Error(`File type ${file.mimetype} not allowed`), false);
  }
};

// Configure multer
const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: FILE_SIZE_LIMITS.default,
    files: 5, // Maximum 5 files per request
    fields: 10 // Maximum 10 form fields
  }
});

/**
 * Validate uploaded file
 * @param {object} file - Multer file object
 * @returns {object} Validation result
 */
export function validateUploadedFile(file) {
  const validation = {
    valid: true,
    errors: [],
    warnings: [],
    metadata: {}
  };

  // Check file size based on type
  const fileType = getFileCategory(file.mimetype);
  const maxSize = FILE_SIZE_LIMITS[fileType] || FILE_SIZE_LIMITS.default;
  
  if (file.size > maxSize) {
    validation.valid = false;
    validation.errors.push(`File size ${(file.size / 1024 / 1024).toFixed(2)}MB exceeds limit of ${(maxSize / 1024 / 1024).toFixed(2)}MB`);
  }

  // Check filename for security
  if (file.originalname.includes('..') || file.originalname.includes('/') || file.originalname.includes('\\')) {
    validation.valid = false;
    validation.errors.push('Invalid filename characters detected');
  }

  // Add metadata
  validation.metadata = {
    originalName: file.originalname,
    mimeType: file.mimetype,
    size: file.size,
    category: fileType,
    uploadedAt: new Date().toISOString(),
    hash: null // Will be calculated later
  };

  return validation;
}

/**
 * Calculate file hash
 * @param {string} filePath - Path to file
 * @returns {Promise<string>} SHA-256 hash
 */
export async function calculateFileHash(filePath) {
  const fileBuffer = await fs.readFile(filePath);
  return crypto.createHash('sha256').update(fileBuffer).digest('hex');
}

/**
 * Process uploaded file
 * @param {object} file - Multer file object
 * @returns {Promise<object>} Processing result
 */
export async function processUploadedFile(file) {
  const validation = validateUploadedFile(file);
  
  if (!validation.valid) {
    // Clean up invalid file
    try {
      await fs.unlink(file.path);
    } catch (error) {
      console.warn('Failed to clean up invalid file:', error);
    }
    return { success: false, ...validation };
  }

  try {
    // Calculate file hash
    const fileHash = await calculateFileHash(file.path);
    validation.metadata.hash = fileHash;

    // Extract text content based on file type
    const textContent = await extractTextContent(file);
    validation.metadata.textLength = textContent.length;
    validation.metadata.wordCount = textContent.split(/\s+/).filter(word => word.length > 0).length;

    // Move file to permanent storage
    const permanentPath = await moveToPermStorage(file, fileHash);
    validation.metadata.storagePath = permanentPath;

    // Clean up temp file
    await fs.unlink(file.path);

    return {
      success: true,
      file: validation.metadata,
      textContent,
      hash: fileHash
    };

  } catch (error) {
    // Clean up on error
    try {
      await fs.unlink(file.path);
    } catch (cleanupError) {
      console.warn('Failed to clean up file after error:', cleanupError);
    }
    
    return {
      success: false,
      error: 'File processing failed',
      message: error.message
    };
  }
}

/**
 * Extract text content from file
 * @param {object} file - Multer file object
 * @returns {Promise<string>} Extracted text
 */
async function extractTextContent(file) {
  const category = getFileCategory(file.mimetype);
  
  switch (category) {
    case 'document':
      return await extractDocumentText(file);
    case 'image':
      return await extractImageText(file);
    case 'code':
      return await fs.readFile(file.path, 'utf8');
    default:
      // Try to read as text
      try {
        return await fs.readFile(file.path, 'utf8');
      } catch (error) {
        return `[Binary file: ${file.originalname}]`;
      }
  }
}

/**
 * Extract text from document files
 * @param {object} file - Multer file object
 * @returns {Promise<string>} Extracted text
 */
async function extractDocumentText(file) {
  // For now, return placeholder
  // In production, use libraries like pdf-parse, mammoth, etc.
  const content = await fs.readFile(file.path);
  return `[Document content: ${file.originalname}, ${content.length} bytes]`;
}

/**
 * Extract text from images using OCR
 * @param {object} file - Multer file object
 * @returns {Promise<string>} Extracted text
 */
async function extractImageText(file) {
  // For now, return placeholder
  // In production, use OCR libraries like tesseract.js
  return `[Image file: ${file.originalname}]`;
}

/**
 * Move file to permanent storage
 * @param {object} file - Multer file object
 * @param {string} hash - File hash
 * @returns {Promise<string>} Permanent file path
 */
async function moveToPermStorage(file, hash) {
  const permDir = path.join(process.cwd(), 'uploads', 'files');
  await fs.mkdir(permDir, { recursive: true });
  
  const extension = path.extname(file.originalname);
  const permPath = path.join(permDir, `${hash}${extension}`);
  
  await fs.copyFile(file.path, permPath);
  return permPath;
}

/**
 * Get file category based on MIME type
 * @param {string} mimeType - File MIME type
 * @returns {string} File category
 */
function getFileCategory(mimeType) {
  if (mimeType.startsWith('image/')) return 'image';
  if (mimeType.startsWith('text/') || mimeType.includes('json') || mimeType.includes('xml')) return 'code';
  return 'document';
}

// Export configured multer instance
export const uploadMiddleware = upload;

// Export single file upload
export const uploadSingle = upload.single('file');

// Export multiple files upload
export const uploadMultiple = upload.array('files', 5);

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stamply Hash Verification</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .search-section {
            padding: 30px;
            border-bottom: 1px solid #eee;
        }
        
        .search-box {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .search-input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            font-family: 'Monaco', 'Menlo', monospace;
        }
        
        .search-btn {
            padding: 12px 24px;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: background 0.2s;
        }
        
        .search-btn:hover {
            background: #2980b9;
        }
        
        .upload-section {
            padding: 30px;
            border-bottom: 1px solid #eee;
        }
        
        .upload-area {
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .upload-area:hover {
            border-color: #3498db;
            background: #f8f9fa;
        }
        
        .upload-area.dragover {
            border-color: #27ae60;
            background: #d5f4e6;
        }
        
        .file-input {
            display: none;
        }
        
        .results {
            padding: 30px;
        }
        
        .result-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #3498db;
        }
        
        .result-card.verified {
            border-left-color: #27ae60;
            background: #d5f4e6;
        }
        
        .result-card.invalid {
            border-left-color: #e74c3c;
            background: #fadbd8;
        }
        
        .result-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .result-status {
            font-weight: 600;
            font-size: 1.1em;
        }
        
        .result-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .detail-item {
            background: white;
            padding: 10px;
            border-radius: 6px;
        }
        
        .detail-label {
            font-size: 0.9em;
            color: #666;
            margin-bottom: 5px;
        }
        
        .detail-value {
            font-weight: 600;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 0.9em;
        }
        
        .hash-display {
            background: #2c3e50;
            color: white;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Monaco', 'Menlo', monospace;
            word-break: break-all;
            margin: 10px 0;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
        }
        
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .eco-level {
            display: inline-flex;
            align-items: center;
            gap: 5px;
            background: #27ae60;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 600;
        }
        
        .platform-badge {
            display: inline-block;
            background: #3498db;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Stamply Public Verification</h1>
            <p>Verify SHA-256 hashes and search the Stamply registry</p>
            <p style="font-size: 0.9em; opacity: 0.9;">
                🌐 Public access - No login required | 🔒 Privacy-focused | ⚡ Real-time verification
            </p>
        </div>
        
        <div class="search-section">
            <h3>Search by Hash</h3>
            <div class="search-box">
                <input type="text" class="search-input" id="hashInput" 
                       placeholder="Enter full or partial SHA-256 hash (minimum 8 characters)">
                <button class="search-btn" onclick="searchHash()">🔍 Search</button>
            </div>
            <p style="color: #666; font-size: 0.9em;">
                Enter at least 8 characters of a SHA-256 hash to search the registry.
            </p>
        </div>
        
        <div class="upload-section">
            <h3>Verify File Content</h3>
            <div class="upload-area" onclick="document.getElementById('fileInput').click()">
                <input type="file" id="fileInput" class="file-input" onchange="handleFileUpload(event)">
                <div>
                    <p style="font-size: 1.2em; margin-bottom: 10px;">📁 Drop a file here or click to browse</p>
                    <p style="color: #666;">Upload a file to calculate its hash and verify against the registry</p>
                </div>
            </div>
        </div>
        
        <div class="results" id="results">
            <p style="color: #666; text-align: center;">Enter a hash or upload a file to begin verification</p>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000/api';
        
        async function searchHash() {
            const hash = document.getElementById('hashInput').value.trim();
            
            if (hash.length < 8) {
                showError('Please enter at least 8 characters');
                return;
            }
            
            showLoading();
            
            try {
                const response = await fetch(`${API_BASE}/search/${hash}`);
                const result = await response.json();
                
                if (result.found) {
                    showVerificationResult(result.entry, hash);
                } else {
                    showNotFound(hash);
                }
            } catch (error) {
                showError('Search failed: ' + error.message);
            }
        }
        
        async function handleFileUpload(event) {
            const file = event.target.files[0];
            if (!file) return;
            
            showLoading();
            
            try {
                // Calculate file hash
                const hash = await calculateFileHash(file);
                
                // Search for hash in registry
                const response = await fetch(`${API_BASE}/search/${hash}`);
                const result = await response.json();
                
                if (result.found) {
                    showVerificationResult(result.entry, hash, file);
                } else {
                    showFileNotInRegistry(hash, file);
                }
            } catch (error) {
                showError('File verification failed: ' + error.message);
            }
        }
        
        async function calculateFileHash(file) {
            const arrayBuffer = await file.arrayBuffer();
            const hashBuffer = await crypto.subtle.digest('SHA-256', arrayBuffer);
            const hashArray = Array.from(new Uint8Array(hashBuffer));
            return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
        }
        
        function showVerificationResult(entry, searchHash, file = null) {
            const resultsDiv = document.getElementById('results');
            
            const timestamp = new Date(entry.timestamp).toLocaleString();
            const ecoLevel = entry.ecoLevel || 3;
            const leaves = '🌿'.repeat(Math.max(0, 6 - ecoLevel)) + '🍂'.repeat(Math.max(0, ecoLevel - 1));
            
            resultsDiv.innerHTML = `
                <div class="result-card verified">
                    <div class="result-header">
                        <div class="result-status">✅ Hash Verified</div>
                        <div style="display: flex; gap: 10px; align-items: center;">
                            <div class="eco-level">${ecoLevel}/5 ${leaves}</div>
                            <button onclick="shareVerification('${entry.hash}')" style="padding: 4px 8px; background: #3498db; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.8em;">📤 Share</button>
                            <button onclick="generateQRCode('${entry.hash}')" style="padding: 4px 8px; background: #9b59b6; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.8em;">📱 QR</button>
                        </div>
                    </div>
                    
                    <div class="hash-display">${entry.hash}</div>
                    
                    ${file ? `<p><strong>File:</strong> ${file.name} (${(file.size / 1024).toFixed(1)} KB)</p>` : ''}
                    
                    <div class="result-details">
                        <div class="detail-item">
                            <div class="detail-label">Timestamp</div>
                            <div class="detail-value">${timestamp}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Platform</div>
                            <div class="detail-value">
                                <span class="platform-badge">${entry.platform}</span>
                            </div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Model</div>
                            <div class="detail-value">${entry.model}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Energy Usage</div>
                            <div class="detail-value">${entry.energy.toFixed(4)} Wh</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Water Usage</div>
                            <div class="detail-value">${entry.water.toFixed(2)} mL</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Content Length</div>
                            <div class="detail-value">${entry.inputLength + entry.outputLength} chars</div>
                        </div>
                    </div>
                </div>
            `;
        }
        
        function showNotFound(hash) {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = `
                <div class="result-card invalid">
                    <div class="result-header">
                        <div class="result-status">❌ Hash Not Found</div>
                    </div>
                    <p>The hash "${hash}" was not found in the Stamply registry.</p>
                    <p style="margin-top: 10px; color: #666;">This could mean:</p>
                    <ul style="margin: 10px 0 0 20px; color: #666;">
                        <li>The content was not processed by Stamply</li>
                        <li>The hash is incorrect or incomplete</li>
                        <li>The entry has been removed from the registry</li>
                    </ul>
                </div>
            `;
        }
        
        function showFileNotInRegistry(hash, file) {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = `
                <div class="result-card invalid">
                    <div class="result-header">
                        <div class="result-status">📄 File Not in Registry</div>
                    </div>
                    <p><strong>File:</strong> ${file.name}</p>
                    <div class="hash-display">${hash}</div>
                    <p>This file has not been processed by Stamply or is not in the registry.</p>
                    <p style="margin-top: 10px;">
                        <button class="search-btn" onclick="uploadToStamply('${hash}', '${file.name}')">
                            📤 Process with Stamply
                        </button>
                    </p>
                </div>
            `;
        }
        
        function showLoading() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                    <p>Searching registry...</p>
                </div>
            `;
        }
        
        function showError(message) {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = `
                <div class="result-card invalid">
                    <div class="result-header">
                        <div class="result-status">❌ Error</div>
                    </div>
                    <p>${message}</p>
                </div>
            `;
        }
        
        async function uploadToStamply(hash, filename) {
            alert('File upload to Stamply coming soon! Hash: ' + hash);
        }
        
        // Drag and drop functionality
        const uploadArea = document.querySelector('.upload-area');
        
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                document.getElementById('fileInput').files = files;
                handleFileUpload({ target: { files } });
            }
        });
        
        // Enter key support for search
        document.getElementById('hashInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                searchHash();
            }
        });

        // Check for hash parameter in URL and auto-search
        document.addEventListener('DOMContentLoaded', () => {
            const urlParams = new URLSearchParams(window.location.search);
            const hashParam = urlParams.get('hash');

            if (hashParam) {
                document.getElementById('hashInput').value = hashParam;
                searchHash();
            }
        });

        // Share functionality
        function shareVerification(hash) {
            const shareUrl = `${window.location.origin}/verify.html?hash=${hash}`;

            if (navigator.share) {
                navigator.share({
                    title: 'Stamply Hash Verification',
                    text: `Verify this SHA-256 hash: ${hash.substring(0, 8)}...`,
                    url: shareUrl
                });
            } else {
                // Fallback: copy to clipboard
                navigator.clipboard.writeText(shareUrl).then(() => {
                    alert('Verification URL copied to clipboard!');
                }).catch(() => {
                    prompt('Copy this verification URL:', shareUrl);
                });
            }
        }

        // Generate QR code for verification URL
        function generateQRCode(hash) {
            const qrUrl = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(window.location.origin + '/verify.html?hash=' + hash)}`;

            const qrWindow = window.open('', '_blank', 'width=250,height=250');
            qrWindow.document.write(`
                <html>
                    <head><title>QR Code - Hash Verification</title></head>
                    <body style="margin:0; padding:20px; text-align:center; font-family:Arial;">
                        <h3>Scan to Verify Hash</h3>
                        <img src="${qrUrl}" alt="QR Code" style="border:1px solid #ddd;">
                        <p style="font-size:12px; color:#666; word-break:break-all;">${hash}</p>
                    </body>
                </html>
            `);
        }
    </script>
</body>
</html>

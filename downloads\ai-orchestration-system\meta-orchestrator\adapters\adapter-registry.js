/**
 * Adapter Registry
 * 
 * Manages standardized adapters for all major AI assistants:
 * - <PERSON><PERSON><PERSON>, Qodo, Cursor, Tabnine, Cline, SuperAGI, AutoGen, Ollama, LM Studio
 * - Normalizes API calls, handles authentication, standardizes input/output
 * - Provides unified interface for all assistants
 * - Manages adapter lifecycle and health monitoring
 */

const EventEmitter = require('events');
const path = require('path');
const fs = require('fs').promises;
const chalk = require('chalk');

// Import all adapter implementations
const AugmentCodeAdapter = require('./implementations/augment-code-adapter');
const CursorAdapter = require('./implementations/cursor-adapter');
const WindsurfAdapter = require('./implementations/windsurf-adapter');
const TabnineAdapter = require('./implementations/tabnine-adapter');
const GitHubCopilotAdapter = require('./implementations/github-copilot-adapter');
const QodoAdapter = require('./implementations/qodo-adapter');
const ClineAdapter = require('./implementations/cline-adapter');
const ContinueAdapter = require('./implementations/continue-adapter');
const AiderAdapter = require('./implementations/aider-adapter');
const OllamaAdapter = require('./implementations/ollama-adapter');
const LMStudioAdapter = require('./implementations/lm-studio-adapter');
const SuperAGIAdapter = require('./implementations/superagi-adapter');
const AutoGenAdapter = require('./implementations/autogen-adapter');

class AdapterRegistry extends EventEmitter {
  constructor(metaOrchestrator) {
    super();
    this.metaOrchestrator = metaOrchestrator;
    
    // Registry of all adapters
    this.adapters = new Map();
    
    // Adapter configurations
    this.adapterConfigs = new Map();
    
    // Adapter health status
    this.adapterHealth = new Map();
    
    // Available adapter classes
    this.adapterClasses = {
      'augment-code': AugmentCodeAdapter,
      'cursor': CursorAdapter,
      'windsurf': WindsurfAdapter,
      'tabnine': TabnineAdapter,
      'github-copilot': GitHubCopilotAdapter,
      'qodo': QodoAdapter,
      'cline': ClineAdapter,
      'continue': ContinueAdapter,
      'aider': AiderAdapter,
      'ollama': OllamaAdapter,
      'lm-studio': LMStudioAdapter,
      'superagi': SuperAGIAdapter,
      'autogen': AutoGenAdapter
    };
    
    // Default configurations for each adapter
    this.defaultConfigs = {
      'augment-code': {
        enabled: true,
        apiEndpoint: 'http://localhost:8080',
        timeout: 30000,
        retries: 3,
        features: ['analysis', 'context', 'search', 'validation']
      },
      
      'cursor': {
        enabled: true,
        apiKey: process.env.CURSOR_API_KEY,
        baseUrl: 'https://api.cursor.sh',
        model: 'claude-3.5-sonnet',
        timeout: 60000,
        retries: 3,
        features: ['generation', 'completion', 'refactoring']
      },
      
      'windsurf': {
        enabled: true,
        apiKey: process.env.WINDSURF_API_KEY,
        baseUrl: 'https://api.codeium.com',
        model: 'claude-3.5-sonnet',
        timeout: 60000,
        retries: 3,
        features: ['multi-file-edit', 'architecture', 'refactoring']
      },
      
      'tabnine': {
        enabled: true,
        timeout: 5000,
        retries: 2,
        features: ['inline-completion', 'suggestions', 'auto-import']
      },
      
      'github-copilot': {
        enabled: true,
        apiKey: process.env.GITHUB_TOKEN,
        timeout: 30000,
        retries: 3,
        features: ['generation', 'completion', 'documentation']
      },
      
      'qodo': {
        enabled: true,
        apiKey: process.env.QODO_API_KEY,
        baseUrl: 'https://api.qodo.ai',
        timeout: 45000,
        retries: 3,
        features: ['validation', 'testing', 'quality-analysis']
      },
      
      'cline': {
        enabled: true,
        timeout: 30000,
        retries: 2,
        features: ['generation', 'completion', 'local-execution']
      },
      
      'continue': {
        enabled: true,
        timeout: 30000,
        retries: 2,
        features: ['generation', 'completion', 'analysis']
      },
      
      'aider': {
        enabled: true,
        timeout: 45000,
        retries: 2,
        features: ['generation', 'refactoring', 'git-integration']
      },
      
      'ollama': {
        enabled: true,
        baseUrl: 'http://localhost:11434',
        timeout: 60000,
        retries: 2,
        features: ['generation', 'completion', 'analysis', 'documentation']
      },
      
      'lm-studio': {
        enabled: true,
        baseUrl: 'http://localhost:1234',
        timeout: 60000,
        retries: 2,
        features: ['generation', 'completion', 'analysis', 'documentation']
      },
      
      'superagi': {
        enabled: true,
        apiKey: process.env.SUPERAGI_API_KEY,
        baseUrl: 'https://api.superagi.com',
        timeout: 90000,
        retries: 3,
        features: ['analysis', 'generation', 'validation', 'orchestration']
      },
      
      'autogen': {
        enabled: true,
        timeout: 60000,
        retries: 2,
        features: ['analysis', 'generation', 'validation', 'collaboration']
      }
    };
  }
  
  async initializeAdapters() {
    try {
      console.log(chalk.blue('🔌 Initializing AI assistant adapters...'));
      
      // Load adapter configurations
      await this.loadAdapterConfigs();
      
      // Initialize each enabled adapter
      for (const [adapterId, AdapterClass] of Object.entries(this.adapterClasses)) {
        const config = this.adapterConfigs.get(adapterId);
        
        if (config && config.enabled) {
          try {
            await this.initializeAdapter(adapterId, AdapterClass, config);
          } catch (error) {
            console.error(chalk.red(`❌ Failed to initialize ${adapterId} adapter:`, error.message));
            this.adapterHealth.set(adapterId, {
              status: 'error',
              error: error.message,
              lastCheck: Date.now()
            });
          }
        } else {
          console.log(chalk.gray(`⏭️ Skipping disabled adapter: ${adapterId}`));
        }
      }
      
      console.log(chalk.green(`✅ Initialized ${this.adapters.size} adapters`));
      
    } catch (error) {
      console.error(chalk.red('❌ Failed to initialize adapters:'), error);
      throw error;
    }
  }
  
  async loadAdapterConfigs() {
    try {
      // Try to load from configuration file
      const configPath = path.join(process.cwd(), 'ai-orchestration', 'config', 'adapters.json');
      
      try {
        const configData = await fs.readFile(configPath, 'utf8');
        const userConfigs = JSON.parse(configData);
        
        // Merge with defaults
        for (const [adapterId, defaultConfig] of Object.entries(this.defaultConfigs)) {
          const userConfig = userConfigs[adapterId] || {};
          const mergedConfig = { ...defaultConfig, ...userConfig };
          this.adapterConfigs.set(adapterId, mergedConfig);
        }
        
        console.log(chalk.green('📋 Loaded adapter configurations from file'));
        
      } catch (fileError) {
        // Use default configurations
        for (const [adapterId, defaultConfig] of Object.entries(this.defaultConfigs)) {
          this.adapterConfigs.set(adapterId, { ...defaultConfig });
        }
        
        console.log(chalk.yellow('📋 Using default adapter configurations'));
      }
      
    } catch (error) {
      console.error(chalk.red('❌ Failed to load adapter configurations:'), error);
      throw error;
    }
  }
  
  async initializeAdapter(adapterId, AdapterClass, config) {
    try {
      console.log(chalk.cyan(`🔌 Initializing ${adapterId} adapter...`));
      
      // Create adapter instance
      const adapter = new AdapterClass(config, this.metaOrchestrator);
      
      // Initialize the adapter
      await adapter.initialize();
      
      // Test availability
      const isAvailable = await adapter.checkAvailability();
      
      if (isAvailable) {
        // Store adapter
        this.adapters.set(adapterId, adapter);
        
        // Update health status
        this.adapterHealth.set(adapterId, {
          status: 'healthy',
          lastCheck: Date.now(),
          responseTime: adapter.lastResponseTime || 0
        });
        
        console.log(chalk.green(`✅ ${adapterId} adapter initialized and available`));
        
      } else {
        this.adapterHealth.set(adapterId, {
          status: 'unavailable',
          lastCheck: Date.now(),
          error: 'Availability check failed'
        });
        
        console.log(chalk.yellow(`⚠️ ${adapterId} adapter initialized but not available`));
      }
      
    } catch (error) {
      console.error(chalk.red(`❌ Failed to initialize ${adapterId} adapter:`, error.message));
      
      this.adapterHealth.set(adapterId, {
        status: 'error',
        error: error.message,
        lastCheck: Date.now()
      });
      
      throw error;
    }
  }
  
  getAdapter(adapterId) {
    return this.adapters.get(adapterId);
  }
  
  getAllAdapters() {
    return Object.fromEntries(this.adapters);
  }
  
  getAdapterHealth(adapterId) {
    return this.adapterHealth.get(adapterId);
  }
  
  getAllAdapterHealth() {
    return Object.fromEntries(this.adapterHealth);
  }
  
  async checkAdapterAvailability(adapterId) {
    const adapter = this.adapters.get(adapterId);
    
    if (!adapter) {
      return false;
    }
    
    try {
      const startTime = Date.now();
      const isAvailable = await adapter.checkAvailability();
      const responseTime = Date.now() - startTime;
      
      // Update health status
      this.adapterHealth.set(adapterId, {
        status: isAvailable ? 'healthy' : 'unavailable',
        lastCheck: Date.now(),
        responseTime
      });
      
      return isAvailable;
      
    } catch (error) {
      this.adapterHealth.set(adapterId, {
        status: 'error',
        error: error.message,
        lastCheck: Date.now()
      });
      
      return false;
    }
  }
  
  async checkAllAdapterAvailability() {
    const results = new Map();
    
    for (const adapterId of this.adapters.keys()) {
      const isAvailable = await this.checkAdapterAvailability(adapterId);
      results.set(adapterId, isAvailable);
    }
    
    return results;
  }
  
  async executeWithAdapter(adapterId, task, context) {
    const adapter = this.adapters.get(adapterId);
    
    if (!adapter) {
      throw new Error(`Adapter not found: ${adapterId}`);
    }
    
    try {
      const startTime = Date.now();
      const result = await adapter.execute(task, context);
      const responseTime = Date.now() - startTime;
      
      // Update health status on successful execution
      this.adapterHealth.set(adapterId, {
        status: 'healthy',
        lastCheck: Date.now(),
        responseTime
      });
      
      return result;
      
    } catch (error) {
      // Update health status on error
      this.adapterHealth.set(adapterId, {
        status: 'error',
        error: error.message,
        lastCheck: Date.now()
      });
      
      throw error;
    }
  }
  
  async reloadAdapter(adapterId) {
    try {
      console.log(chalk.blue(`🔄 Reloading ${adapterId} adapter...`));
      
      // Shutdown existing adapter if it exists
      const existingAdapter = this.adapters.get(adapterId);
      if (existingAdapter && typeof existingAdapter.shutdown === 'function') {
        await existingAdapter.shutdown();
      }
      
      // Remove from registry
      this.adapters.delete(adapterId);
      
      // Reload configuration
      await this.loadAdapterConfigs();
      
      // Reinitialize adapter
      const AdapterClass = this.adapterClasses[adapterId];
      const config = this.adapterConfigs.get(adapterId);
      
      if (AdapterClass && config && config.enabled) {
        await this.initializeAdapter(adapterId, AdapterClass, config);
        console.log(chalk.green(`✅ ${adapterId} adapter reloaded successfully`));
      } else {
        console.log(chalk.yellow(`⚠️ ${adapterId} adapter not reloaded (disabled or not found)`));
      }
      
    } catch (error) {
      console.error(chalk.red(`❌ Failed to reload ${adapterId} adapter:`, error.message));
      throw error;
    }
  }
  
  async updateAdapterConfig(adapterId, newConfig) {
    try {
      // Update configuration
      const currentConfig = this.adapterConfigs.get(adapterId) || {};
      const mergedConfig = { ...currentConfig, ...newConfig };
      this.adapterConfigs.set(adapterId, mergedConfig);
      
      // Save to file
      await this.saveAdapterConfigs();
      
      // Reload adapter with new configuration
      await this.reloadAdapter(adapterId);
      
      this.emit('adapterConfigUpdated', { adapterId, config: mergedConfig });
      
    } catch (error) {
      console.error(chalk.red(`❌ Failed to update ${adapterId} adapter config:`, error.message));
      throw error;
    }
  }
  
  async saveAdapterConfigs() {
    try {
      const configPath = path.join(process.cwd(), 'ai-orchestration', 'config', 'adapters.json');
      const configData = Object.fromEntries(this.adapterConfigs);
      
      await fs.writeFile(configPath, JSON.stringify(configData, null, 2));
      
    } catch (error) {
      console.error(chalk.red('❌ Failed to save adapter configurations:'), error);
      throw error;
    }
  }
  
  getAdaptersByRole(role) {
    const adaptersForRole = [];
    
    for (const [adapterId, adapter] of this.adapters) {
      if (adapter.supportsRole && adapter.supportsRole(role)) {
        adaptersForRole.push({
          adapterId,
          adapter,
          health: this.adapterHealth.get(adapterId)
        });
      }
    }
    
    return adaptersForRole;
  }
  
  getAdaptersByCapability(capability) {
    const adaptersWithCapability = [];
    
    for (const [adapterId, adapter] of this.adapters) {
      if (adapter.hasCapability && adapter.hasCapability(capability)) {
        adaptersWithCapability.push({
          adapterId,
          adapter,
          health: this.adapterHealth.get(adapterId)
        });
      }
    }
    
    return adaptersWithCapability;
  }
  
  async shutdownAll() {
    console.log(chalk.blue('🛑 Shutting down all adapters...'));
    
    const shutdownPromises = [];
    
    for (const [adapterId, adapter] of this.adapters) {
      if (typeof adapter.shutdown === 'function') {
        shutdownPromises.push(
          adapter.shutdown().catch(error => {
            console.error(chalk.red(`❌ Error shutting down ${adapterId}:`, error.message));
          })
        );
      }
    }
    
    await Promise.all(shutdownPromises);
    
    this.adapters.clear();
    this.adapterHealth.clear();
    
    console.log(chalk.green('✅ All adapters shut down'));
  }
  
  getRegistryStats() {
    const totalAdapters = this.adapters.size;
    const healthyAdapters = Array.from(this.adapterHealth.values())
      .filter(health => health.status === 'healthy').length;
    const errorAdapters = Array.from(this.adapterHealth.values())
      .filter(health => health.status === 'error').length;
    const unavailableAdapters = Array.from(this.adapterHealth.values())
      .filter(health => health.status === 'unavailable').length;
    
    return {
      total: totalAdapters,
      healthy: healthyAdapters,
      error: errorAdapters,
      unavailable: unavailableAdapters,
      healthRate: totalAdapters > 0 ? healthyAdapters / totalAdapters : 0
    };
  }
}

module.exports = AdapterRegistry;

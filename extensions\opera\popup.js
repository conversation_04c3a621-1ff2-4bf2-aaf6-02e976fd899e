/**
 * EcoStamp Popup Script
 * Handles popup interface and user interactions
 */

class EcoStampPopup {
  constructor() {
    this.apiUrl = 'http://localhost:3000';
    this.init();
  }
  
  async init() {
    await this.loadSettings();
    await this.loadStats();
    this.setupEventListeners();
    this.checkConnection();
    
    // Refresh stats every 10 seconds
    setInterval(() => {
      this.loadStats();
    }, 10000);
  }
  
  async loadSettings() {
    try {
      const response = await chrome.runtime.sendMessage({
        type: 'GET_SETTINGS'
      });
      
      if (response.success) {
        const settings = response.data;
        
        // Update toggles
        this.updateToggle('tracking-toggle', settings.trackingEnabled !== false);
        this.updateToggle('widget-toggle', settings.showWidget !== false);
        this.updateToggle('verify-toggle', settings.autoVerify !== false);
        
        // Update API URL if different
        if (settings.apiUrl) {
          this.apiUrl = settings.apiUrl;
        }
      }
    } catch (error) {
      console.error('Failed to load settings:', error);
    }
  }
  
  async loadStats() {
    try {
      const response = await chrome.runtime.sendMessage({
        type: 'GET_STATS'
      });
      
      if (response.success) {
        const stats = response.data;
        this.updateStats(stats);
        this.updateConnectionStatus(true);
      } else {
        this.updateConnectionStatus(false);
      }
    } catch (error) {
      console.error('Failed to load stats:', error);
      this.updateConnectionStatus(false);
    }
  }
  
  updateStats(stats) {
    document.getElementById('energy-stat').textContent = (stats.totalEnergy || 0).toFixed(2);
    document.getElementById('water-stat').textContent = (stats.totalWater || 0).toFixed(1);
    document.getElementById('conversations-stat').textContent = stats.conversations || 0;
    document.getElementById('hashes-stat').textContent = stats.totalHashes || 0;
    
    // Update eco level based on recent activity
    const ecoLevel = this.calculateEcoLevel(stats);
    this.updateEcoLevel(ecoLevel);
  }
  
  calculateEcoLevel(stats) {
    const energy = stats.totalEnergy || 0;
    const conversations = stats.conversations || 0;
    
    if (conversations === 0) return 5;
    
    const avgEnergy = energy / conversations;
    
    if (avgEnergy < 0.001) return 5;
    if (avgEnergy < 0.005) return 4;
    if (avgEnergy < 0.01) return 3;
    if (avgEnergy < 0.05) return 2;
    return 1;
  }
  
  updateEcoLevel(level) {
    const leaves = '🌱'.repeat(level) + '🍂'.repeat(5 - level);
    document.getElementById('eco-leaves').textContent = leaves;
  }
  
  updateConnectionStatus(connected) {
    const indicator = document.getElementById('status-indicator');
    const statusText = document.getElementById('status-text');
    
    if (connected) {
      indicator.classList.remove('offline');
      statusText.textContent = 'Connected to EcoStamp server';
    } else {
      indicator.classList.add('offline');
      statusText.textContent = 'Offline mode - data will sync when connected';
    }
  }
  
  updateToggle(toggleId, active) {
    const toggle = document.getElementById(toggleId);
    if (active) {
      toggle.classList.add('active');
    } else {
      toggle.classList.remove('active');
    }
  }
  
  setupEventListeners() {
    // Action buttons
    document.getElementById('verify-btn').addEventListener('click', (e) => {
      e.preventDefault();
      this.openVerifyPage();
    });
    
    document.getElementById('dashboard-btn').addEventListener('click', (e) => {
      e.preventDefault();
      this.openDashboard();
    });
    
    document.getElementById('github-btn').addEventListener('click', (e) => {
      e.preventDefault();
      this.openGitHub();
    });
    
    // Setting toggles
    document.getElementById('tracking-toggle').addEventListener('click', () => {
      this.toggleSetting('trackingEnabled', 'tracking-toggle');
    });
    
    document.getElementById('widget-toggle').addEventListener('click', () => {
      this.toggleSetting('showWidget', 'widget-toggle');
    });
    
    document.getElementById('verify-toggle').addEventListener('click', () => {
      this.toggleSetting('autoVerify', 'verify-toggle');
    });
  }
  
  async toggleSetting(settingKey, toggleId) {
    const toggle = document.getElementById(toggleId);
    const isActive = toggle.classList.contains('active');
    const newValue = !isActive;
    
    try {
      const response = await chrome.runtime.sendMessage({
        type: 'UPDATE_SETTINGS',
        data: { [settingKey]: newValue }
      });
      
      if (response.success) {
        this.updateToggle(toggleId, newValue);
        
        // Reload current tab if widget setting changed
        if (settingKey === 'showWidget' || settingKey === 'trackingEnabled') {
          this.reloadCurrentTab();
        }
      }
    } catch (error) {
      console.error('Failed to update setting:', error);
      this.showError('Failed to update setting');
    }
  }
  
  async reloadCurrentTab() {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tab && this.isAIWebsite(tab.url)) {
        chrome.tabs.reload(tab.id);
      }
    } catch (error) {
      console.error('Failed to reload tab:', error);
    }
  }
  
  isAIWebsite(url) {
    const aiDomains = [
      'chat.openai.com',
      'claude.ai',
      'gemini.google.com',
      'bard.google.com',
      'copilot.microsoft.com',
      'you.com',
      'perplexity.ai',
      'poe.com'
    ];
    
    return aiDomains.some(domain => url.includes(domain));
  }
  
  openVerifyPage() {
    chrome.tabs.create({
      url: `${this.apiUrl}/verify.html`
    });
  }
  
  openDashboard() {
    chrome.tabs.create({
      url: `${this.apiUrl}/dashboard.html`
    });
  }
  
  openGitHub() {
    chrome.tabs.create({
      url: 'https://github.com/chris-ai-dev/Time_Stamp_Project'
    });
  }
  
  async checkConnection() {
    try {
      const response = await fetch(`${this.apiUrl}/api/health`);
      this.updateConnectionStatus(response.ok);
    } catch (error) {
      this.updateConnectionStatus(false);
    }
  }
  
  showError(message) {
    const errorElement = document.getElementById('error-message');
    errorElement.textContent = message;
    errorElement.style.display = 'block';
    
    setTimeout(() => {
      errorElement.style.display = 'none';
    }, 5000);
  }
}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new EcoStampPopup();
});

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.config = void 0;
const config = {
    server: {
        port: parseInt(process.env.PORT || '3001', 10),
        host: process.env.HOST || 'localhost',
        cors: {
            origin: process.env.CORS_ORIGINS?.split(',') || ['http://localhost:3000'],
            credentials: true,
        },
        rateLimit: {
            windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000', 10), // 15 minutes
            max: parseInt(process.env.RATE_LIMIT_MAX || '100', 10), // limit each IP to 100 requests per windowMs
        },
    },
    socket: {
        cors: {
            origin: process.env.CORS_ORIGINS?.split(',') || ['http://localhost:3000'],
            credentials: true,
        },
        transports: ['websocket', 'polling'],
    },
    database: {
        url: process.env.DATABASE_URL || 'postgresql://localhost:5432/augment_orchestration',
        maxConnections: parseInt(process.env.DB_MAX_CONNECTIONS || '10', 10),
        ssl: process.env.DB_SSL === 'true',
    },
    jwt: {
        secret: process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production',
        expiresIn: process.env.JWT_EXPIRES_IN || '24h',
    },
    environment: process.env.NODE_ENV || 'development',
};
exports.config = config;
// Validate required environment variables
const requiredEnvVars = ['DATABASE_URL'];
if (config.environment === 'production') {
    requiredEnvVars.push('JWT_SECRET');
}
for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
        throw new Error(`Required environment variable ${envVar} is not set`);
    }
}

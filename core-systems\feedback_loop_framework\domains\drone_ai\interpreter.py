"""
Sensor Data Interpreter for Drone AI

Interprets and normalizes various types of sensor data and drone outputs
into standardized formats for feedback processing.
"""

import json
import logging
from typing import Any, Dict, List
from datetime import datetime


class SensorDataInterpreter:
    """
    Interprets sensor data and drone AI outputs into standardized format.
    
    Supports various sensor types including GPS, IMU, camera, lidar, and
    environmental sensors commonly found in drone systems.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.supported_formats = [
            'json', 'csv', 'binary_sensor', 'mavlink', 'custom_drone'
        ]
        
        # Standard field mappings for different sensor types
        self.field_mappings = {
            'gps': {
                'latitude': ['lat', 'latitude', 'gps_lat'],
                'longitude': ['lon', 'lng', 'longitude', 'gps_lon'],
                'altitude': ['alt', 'altitude', 'height', 'gps_alt'],
                'accuracy': ['acc', 'accuracy', 'hdop', 'precision']
            },
            'imu': {
                'acceleration_x': ['accel_x', 'ax', 'acc_x'],
                'acceleration_y': ['accel_y', 'ay', 'acc_y'],
                'acceleration_z': ['accel_z', 'az', 'acc_z'],
                'gyro_x': ['gyro_x', 'gx', 'angular_vel_x'],
                'gyro_y': ['gyro_y', 'gy', 'angular_vel_y'],
                'gyro_z': ['gyro_z', 'gz', 'angular_vel_z']
            },
            'environmental': {
                'temperature': ['temp', 'temperature', 'ambient_temp'],
                'humidity': ['humidity', 'rh', 'relative_humidity'],
                'pressure': ['pressure', 'barometric_pressure', 'atm_pressure'],
                'wind_speed': ['wind_speed', 'wind_vel', 'wind_magnitude'],
                'wind_direction': ['wind_dir', 'wind_direction', 'wind_bearing']
            }
        }
    
    def interpret(self, raw_output: Any, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Interpret and normalize raw sensor data or drone output.
        
        Args:
            raw_output: Raw sensor data or drone AI output
            context: Additional context including sensor type, mission info
            
        Returns:
            Normalized output dictionary with standardized fields
        """
        try:
            # Determine data type and format
            data_type = context.get('data_type', 'unknown')
            sensor_type = context.get('sensor_type', 'mixed')
            
            # Parse raw output based on format
            parsed_data = self._parse_raw_output(raw_output, context)
            
            # Normalize based on sensor type
            normalized_data = self._normalize_sensor_data(parsed_data, sensor_type)
            
            # Add metadata
            normalized_data['_metadata'] = {
                'original_format': type(raw_output).__name__,
                'data_type': data_type,
                'sensor_type': sensor_type,
                'interpretation_timestamp': datetime.utcnow().isoformat(),
                'interpreter_version': '1.0.0'
            }
            
            # Validate required fields
            self._validate_normalized_data(normalized_data, sensor_type)
            
            return normalized_data
            
        except Exception as e:
            self.logger.error(f"Error interpreting sensor data: {str(e)}")
            return {
                'error': True,
                'error_message': str(e),
                'raw_output': str(raw_output)[:1000],  # Truncate for logging
                '_metadata': {
                    'interpretation_failed': True,
                    'error_timestamp': datetime.utcnow().isoformat()
                }
            }
    
    def _parse_raw_output(self, raw_output: Any, context: Dict[str, Any]) -> Dict[str, Any]:
        """Parse raw output based on its format."""
        if isinstance(raw_output, dict):
            return raw_output
        
        if isinstance(raw_output, str):
            # Try to parse as JSON
            try:
                return json.loads(raw_output)
            except json.JSONDecodeError:
                # Treat as CSV or other structured text
                return self._parse_text_data(raw_output, context)
        
        if isinstance(raw_output, (list, tuple)):
            # Convert to dictionary with indexed keys
            return {f'value_{i}': val for i, val in enumerate(raw_output)}
        
        # For other types, create a wrapper
        return {'raw_value': raw_output}
    
    def _parse_text_data(self, text_data: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Parse text-based sensor data (CSV, custom formats)."""
        lines = text_data.strip().split('\n')
        
        if len(lines) == 1:
            # Single line - try comma-separated values
            values = lines[0].split(',')
            return {f'field_{i}': val.strip() for i, val in enumerate(values)}
        
        # Multi-line - try to detect headers
        if len(lines) >= 2:
            headers = lines[0].split(',')
            values = lines[1].split(',')
            
            if len(headers) == len(values):
                return {header.strip(): value.strip() for header, value in zip(headers, values)}
        
        # Fallback: return as lines
        return {f'line_{i}': line for i, line in enumerate(lines)}
    
    def _normalize_sensor_data(self, parsed_data: Dict[str, Any], sensor_type: str) -> Dict[str, Any]:
        """Normalize sensor data using field mappings."""
        normalized = {}
        
        if sensor_type in self.field_mappings:
            mappings = self.field_mappings[sensor_type]
            
            for standard_field, possible_names in mappings.items():
                value = None
                source_field = None
                
                # Find the first matching field
                for field_name in possible_names:
                    if field_name in parsed_data:
                        value = parsed_data[field_name]
                        source_field = field_name
                        break
                
                if value is not None:
                    # Convert to appropriate type
                    normalized[standard_field] = self._convert_value(value, standard_field)
                    normalized[f'{standard_field}_source'] = source_field
        
        # Copy any unmapped fields
        for key, value in parsed_data.items():
            if key not in [name for names in self.field_mappings.get(sensor_type, {}).values() for name in names]:
                normalized[f'extra_{key}'] = value
        
        return normalized
    
    def _convert_value(self, value: Any, field_type: str) -> Any:
        """Convert value to appropriate type based on field."""
        if value is None:
            return None
        
        # Numeric fields
        numeric_fields = [
            'latitude', 'longitude', 'altitude', 'accuracy',
            'acceleration_x', 'acceleration_y', 'acceleration_z',
            'gyro_x', 'gyro_y', 'gyro_z',
            'temperature', 'humidity', 'pressure', 'wind_speed', 'wind_direction'
        ]
        
        if field_type in numeric_fields:
            try:
                return float(value)
            except (ValueError, TypeError):
                return value
        
        return value
    
    def _validate_normalized_data(self, data: Dict[str, Any], sensor_type: str) -> None:
        """Validate that normalized data has required fields."""
        required_fields = {
            'gps': ['latitude', 'longitude'],
            'imu': ['acceleration_x', 'acceleration_y', 'acceleration_z'],
            'environmental': ['temperature']
        }
        
        if sensor_type in required_fields:
            missing_fields = []
            for field in required_fields[sensor_type]:
                if field not in data:
                    missing_fields.append(field)
            
            if missing_fields:
                self.logger.warning(f"Missing required fields for {sensor_type}: {missing_fields}")
    
    def validate_input(self, raw_output: Any) -> bool:
        """
        Validate that the raw output can be interpreted.
        
        Args:
            raw_output: The raw output to validate
            
        Returns:
            True if the output can be interpreted, False otherwise
        """
        if raw_output is None:
            return False
        
        # Accept most common data types
        accepted_types = (dict, list, tuple, str, int, float, bool)
        return isinstance(raw_output, accepted_types)
    
    def get_supported_formats(self) -> List[str]:
        """
        Get list of supported output formats.
        
        Returns:
            List of format identifiers this interpreter supports
        """
        return self.supported_formats.copy()
    
    def add_field_mapping(self, sensor_type: str, standard_field: str, field_names: List[str]) -> None:
        """
        Add custom field mapping for a sensor type.
        
        Args:
            sensor_type: Type of sensor (e.g., 'custom_lidar')
            standard_field: Standard field name
            field_names: List of possible field names in raw data
        """
        if sensor_type not in self.field_mappings:
            self.field_mappings[sensor_type] = {}
        
        self.field_mappings[sensor_type][standard_field] = field_names
        self.logger.info(f"Added field mapping for {sensor_type}.{standard_field}: {field_names}")
    
    def get_field_mappings(self, sensor_type: str) -> Dict[str, List[str]]:
        """
        Get field mappings for a specific sensor type.
        
        Args:
            sensor_type: Type of sensor
            
        Returns:
            Dictionary of field mappings
        """
        return self.field_mappings.get(sensor_type, {}).copy()

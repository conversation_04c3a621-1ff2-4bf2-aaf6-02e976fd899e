"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.clearError = exports.fetchAgentRegistry = exports.fetchAgents = void 0;
const toolkit_1 = require("@reduxjs/toolkit");
const api_1 = require("../../services/api");
const initialState = {
    agents: [],
    roles: [],
    registry: null,
    isLoading: false,
    error: null,
};
exports.fetchAgents = (0, toolkit_1.createAsyncThunk)('agent/fetchAll', async () => {
    const response = await api_1.agentApi.getAll();
    return response.data;
});
exports.fetchAgentRegistry = (0, toolkit_1.createAsyncThunk)('agent/fetchRegistry', async () => {
    const response = await api_1.agentApi.getRegistry();
    return response.data;
});
const agentSlice = (0, toolkit_1.createSlice)({
    name: 'agent',
    initialState,
    reducers: {
        clearError: (state) => {
            state.error = null;
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(exports.fetchAgents.pending, (state) => {
            state.isLoading = true;
            state.error = null;
        })
            .addCase(exports.fetchAgents.fulfilled, (state, action) => {
            state.isLoading = false;
            state.agents = action.payload;
        })
            .addCase(exports.fetchAgents.rejected, (state, action) => {
            state.isLoading = false;
            state.error = action.error.message || 'Failed to fetch agents';
        })
            .addCase(exports.fetchAgentRegistry.fulfilled, (state, action) => {
            state.registry = action.payload;
            state.roles = action.payload.roles || [];
        });
    },
});
exports.clearError = agentSlice.actions.clearError;
exports.default = agentSlice.reducer;

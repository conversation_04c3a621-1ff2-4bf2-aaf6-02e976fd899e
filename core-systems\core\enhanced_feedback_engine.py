"""
Enhanced Feedback Engine with Dynamic Domain Registration

Extends the base feedback engine with automatic domain detection,
dynamic domain registration, and intelligent routing for unlimited
industry domain expansion.
"""

import logging
from typing import Dict, Any, List, Optional, Union
from datetime import datetime

from .feedback_engine import FeedbackEngine
from .domain_factory import domain_factory
from .feedback_types import ValidationResult, FeedbackType


class EnhancedFeedbackEngine(FeedbackEngine):
    """
    Enhanced feedback engine with dynamic domain registration capabilities.
    
    Automatically detects appropriate domains for new data types and
    creates domain instances on-demand for unlimited industry expansion.
    """
    
    def __init__(self, confidence_model=None, trust_calculator=None, memory_store=None, config=None):
        super().__init__(confidence_model, trust_calculator, memory_store, config)
        
        self.logger = logging.getLogger(__name__)
        self.domain_factory = domain_factory
        
        # Enhanced configuration
        self.enhanced_config = config or {}
        self.auto_domain_creation = self.enhanced_config.get('auto_domain_creation', True)
        self.domain_learning_enabled = self.enhanced_config.get('domain_learning_enabled', True)
        
        # Dynamic domain tracking
        self.dynamic_domains = {}
        self.domain_usage_stats = {}
        self.cross_domain_insights = {}
        
        # Initialize unified domains
        self._initialize_unified_domains()
    
    def _initialize_unified_domains(self) -> None:
        """Initialize unified Drone AI and TimeStamp AI domains."""
        try:
            # Initialize Unified Drone AI Domain
            from ..domains.drone_ai.unified_domain import UnifiedDroneAIDomain
            drone_config = self.enhanced_config.get('drone_ai', {})
            unified_drone_domain = UnifiedDroneAIDomain(drone_config)
            self.register_domain('drone_ai', unified_drone_domain)
            
            # Initialize Unified TimeStamp AI Domain
            from ..domains.timestamp_ai.unified_domain import UnifiedTimeStampAIDomain
            timestamp_config = self.enhanced_config.get('timestamp_ai', {})
            unified_timestamp_domain = UnifiedTimeStampAIDomain(timestamp_config)
            self.register_domain('timestamp_ai', unified_timestamp_domain)
            
            self.logger.info("Initialized unified domains: drone_ai, timestamp_ai")
            
        except Exception as e:
            self.logger.error(f"Error initializing unified domains: {str(e)}")
    
    def process_output(self, domain: Optional[str], raw_output: Any, 
                      context: Dict[str, Any], agent_id: str = None) -> ValidationResult:
        """
        Enhanced process_output with automatic domain detection and creation.
        
        Args:
            domain: Optional domain name (if None, will auto-detect)
            raw_output: Raw output data
            context: Processing context
            agent_id: Optional agent identifier
            
        Returns:
            ValidationResult with enhanced metadata
        """
        try:
            start_time = datetime.utcnow()
            
            # Auto-detect domain if not specified
            if domain is None:
                domain = self._auto_detect_domain(raw_output, context)
                if domain is None:
                    return self._create_error_result("Could not detect appropriate domain for input data")
            
            # Get or create domain instance
            domain_instance = self._get_or_create_domain(domain, raw_output, context)
            if domain_instance is None:
                return self._create_error_result(f"Could not create or find domain: {domain}")
            
            # Process through domain
            result = domain_instance.process_output(raw_output, context)
            
            # Enhance result with cross-domain metadata
            self._enhance_result_metadata(result, domain, agent_id, start_time)
            
            # Update domain usage statistics
            self._update_domain_usage_stats(domain, result, start_time)
            
            # Apply cross-domain learning if enabled
            if self.domain_learning_enabled:
                self._apply_cross_domain_learning(domain, result, context)
            
            # Store in memory if available
            if self.memory_store:
                self.memory_store.store_entry(result, domain, agent_id)
            
            # Update confidence and trust models
            if self.confidence_model:
                self.confidence_model.update_confidence(agent_id, result.feedback_type, domain)
            
            if self.trust_calculator:
                self.trust_calculator.update_trust(agent_id, result.feedback_type, result.confidence_score, domain)
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error in enhanced process_output: {str(e)}")
            return self._create_error_result(f"Enhanced processing failed: {str(e)}")
    
    def _auto_detect_domain(self, raw_output: Any, context: Dict[str, Any]) -> Optional[str]:
        """Automatically detect the appropriate domain for the input data."""
        try:
            detection_result = self.domain_factory.detect_domain_type(raw_output, context)
            if detection_result:
                domain_type = detection_result['domain_type']
                self.logger.info(f"Auto-detected domain: {domain_type} (confidence: {detection_result.get('confidence', 0):.2f})")
                return domain_type
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error in auto-detection: {str(e)}")
            return None
    
    def _get_or_create_domain(self, domain: str, raw_output: Any, context: Dict[str, Any]):
        """Get existing domain or create new one dynamically."""
        # Check if domain already exists
        if domain in self.domains:
            return self.domains[domain]
        
        # Check if it's a dynamic domain we've created before
        if domain in self.dynamic_domains:
            return self.dynamic_domains[domain]
        
        # Auto-create domain if enabled
        if self.auto_domain_creation:
            domain_instance = self.domain_factory.auto_detect_and_create(
                raw_output, context, self.enhanced_config.get(domain, {})
            )
            
            if domain_instance:
                self.dynamic_domains[domain] = domain_instance
                self.logger.info(f"Dynamically created domain: {domain}")
                return domain_instance
        
        return None
    
    def _enhance_result_metadata(self, result: ValidationResult, domain: str, 
                               agent_id: str, start_time: datetime) -> None:
        """Enhance result with cross-domain metadata."""
        processing_time = (datetime.utcnow() - start_time).total_seconds() * 1000
        
        enhanced_metadata = {
            'enhanced_engine': True,
            'auto_detected_domain': domain not in self.domains,
            'dynamic_domain': domain in self.dynamic_domains,
            'processing_time_ms': processing_time,
            'agent_id': agent_id,
            'cross_domain_learning_enabled': self.domain_learning_enabled,
            'total_registered_domains': len(self.domains) + len(self.dynamic_domains),
            'engine_version': 'enhanced_v1.0'
        }
        
        if result.metadata:
            result.metadata.update(enhanced_metadata)
        else:
            result.metadata = enhanced_metadata
    
    def _update_domain_usage_stats(self, domain: str, result: ValidationResult, start_time: datetime) -> None:
        """Update domain usage statistics."""
        if domain not in self.domain_usage_stats:
            self.domain_usage_stats[domain] = {
                'total_uses': 0,
                'success_rate': 0.0,
                'avg_confidence': 0.0,
                'avg_processing_time': 0.0,
                'first_used': start_time.isoformat(),
                'last_used': start_time.isoformat()
            }
        
        stats = self.domain_usage_stats[domain]
        stats['total_uses'] += 1
        stats['last_used'] = start_time.isoformat()
        
        # Update success rate
        is_success = result.feedback_type in [FeedbackType.CORRECT, FeedbackType.PARTIALLY_CORRECT]
        total_success = stats['success_rate'] * (stats['total_uses'] - 1) + (1 if is_success else 0)
        stats['success_rate'] = total_success / stats['total_uses']
        
        # Update average confidence
        total_confidence = stats['avg_confidence'] * (stats['total_uses'] - 1) + result.confidence_score
        stats['avg_confidence'] = total_confidence / stats['total_uses']
        
        # Update average processing time
        processing_time = result.metadata.get('processing_time_ms', 0)
        total_time = stats['avg_processing_time'] * (stats['total_uses'] - 1) + processing_time
        stats['avg_processing_time'] = total_time / stats['total_uses']
    
    def _apply_cross_domain_learning(self, domain: str, result: ValidationResult, context: Dict[str, Any]) -> None:
        """Apply learning insights across domains."""
        try:
            # Track patterns that could benefit other domains
            if result.confidence_score > 0.8:
                # High confidence result - extract learnable patterns
                pattern_key = f"{domain}_high_confidence"
                if pattern_key not in self.cross_domain_insights:
                    self.cross_domain_insights[pattern_key] = []
                
                self.cross_domain_insights[pattern_key].append({
                    'timestamp': datetime.utcnow().isoformat(),
                    'confidence': result.confidence_score,
                    'context_keys': list(context.keys()) if isinstance(context, dict) else [],
                    'feedback_type': result.feedback_type.value
                })
                
                # Keep only recent insights
                if len(self.cross_domain_insights[pattern_key]) > 50:
                    self.cross_domain_insights[pattern_key] = self.cross_domain_insights[pattern_key][-50:]
            
        except Exception as e:
            self.logger.error(f"Error in cross-domain learning: {str(e)}")
    
    def _create_error_result(self, error_message: str) -> ValidationResult:
        """Create standardized error result."""
        error_result = ValidationResult()
        error_result.is_valid = False
        error_result.confidence_score = 0.0
        error_result.feedback_type = FeedbackType.INCORRECT
        error_result.issues = [{
            'type': 'enhanced_engine_error',
            'severity': 'critical',
            'message': error_message,
            'details': {'timestamp': datetime.utcnow().isoformat()}
        }]
        error_result.metadata = {
            'enhanced_engine': True,
            'error': True,
            'error_message': error_message
        }
        
        return error_result
    
    def register_custom_domain(self, domain_name: str, domain_class, config: Dict[str, Any] = None) -> None:
        """Register a custom domain class."""
        try:
            # Register with domain factory
            self.domain_factory.register_domain_class(domain_name, domain_class)
            
            # Create and register instance
            domain_instance = domain_class(config or {})
            self.register_domain(domain_name, domain_instance)
            
            self.logger.info(f"Registered custom domain: {domain_name}")
            
        except Exception as e:
            self.logger.error(f"Error registering custom domain {domain_name}: {str(e)}")
    
    def add_industry_pattern(self, pattern_name: str, indicators: List[str], 
                           domain_type: str, scenario_type: str) -> None:
        """Add a new industry pattern for automatic detection."""
        self.domain_factory.add_data_pattern(pattern_name, indicators, domain_type, scenario_type)
        self.logger.info(f"Added industry pattern: {pattern_name} -> {domain_type}.{scenario_type}")
    
    def scan_for_new_domains(self, data_samples: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Scan data samples to identify potential new domain types."""
        return self.domain_factory.scan_for_new_domains(data_samples)
    
    def get_enhanced_statistics(self) -> Dict[str, Any]:
        """Get comprehensive enhanced engine statistics."""
        base_stats = self.get_statistics()
        
        enhanced_stats = {
            'base_statistics': base_stats,
            'domain_usage_stats': self.domain_usage_stats.copy(),
            'dynamic_domains': list(self.dynamic_domains.keys()),
            'cross_domain_insights_count': len(self.cross_domain_insights),
            'auto_domain_creation_enabled': self.auto_domain_creation,
            'domain_learning_enabled': self.domain_learning_enabled,
            'supported_domains': self.domain_factory.get_supported_domains(),
            'total_domains': {
                'registered': len(self.domains),
                'dynamic': len(self.dynamic_domains),
                'total': len(self.domains) + len(self.dynamic_domains)
            }
        }
        
        return enhanced_stats
    
    def get_domain_recommendations(self, raw_output: Any, context: Dict[str, Any]) -> Dict[str, Any]:
        """Get domain recommendations for input data."""
        detection_result = self.domain_factory.detect_domain_type(raw_output, context)
        
        recommendations = {
            'primary_recommendation': detection_result,
            'supported_domains': self.domain_factory.get_supported_domains(),
            'can_auto_create': self.auto_domain_creation,
            'similar_patterns': []
        }
        
        # Find similar patterns
        if detection_result:
            domain_type = detection_result['domain_type']
            for pattern_name, pattern_info in self.domain_factory.data_type_patterns.items():
                if pattern_info['domain_type'] == domain_type:
                    recommendations['similar_patterns'].append({
                        'pattern_name': pattern_name,
                        'scenario_type': pattern_info['scenario_type']
                    })
        
        return recommendations
    
    def health_check(self) -> Dict[str, Any]:
        """Enhanced health check including dynamic domains."""
        base_health = super().health_check()
        
        enhanced_health = {
            'base_health': base_health,
            'domain_factory': 'operational',
            'auto_domain_creation': 'enabled' if self.auto_domain_creation else 'disabled',
            'domain_learning': 'enabled' if self.domain_learning_enabled else 'disabled',
            'dynamic_domains': {
                'count': len(self.dynamic_domains),
                'domains': list(self.dynamic_domains.keys())
            },
            'unified_domains': {
                'drone_ai': 'drone_ai' in self.domains,
                'timestamp_ai': 'timestamp_ai' in self.domains
            }
        }
        
        return enhanced_health


def create_enhanced_engine(config: Dict[str, Any] = None) -> EnhancedFeedbackEngine:
    """
    Create an enhanced feedback engine with all capabilities enabled.
    
    Args:
        config: Optional configuration dictionary
        
    Returns:
        Configured EnhancedFeedbackEngine instance
    """
    from .confidence_models import AdaptiveConfidenceModel
    from .trust_models import TrustScoreCalculator
    from ..components.memory.file_memory_store import FileMemoryStore
    
    # Default enhanced configuration
    default_config = {
        'auto_domain_creation': True,
        'domain_learning_enabled': True,
        'drone_ai': {
            'search_rescue': {'enabled': True},
            'species_tracking': {'enabled': True},
            'mining_ore': {'enabled': True},
            'real_estate_construction': {'enabled': True}
        },
        'timestamp_ai': {
            'llm_validation': {'enabled': True},
            'environmental_impact': {'enabled': True},
            'ecostamp': {'enabled': True}
        }
    }
    
    # Merge with provided config
    if config:
        default_config.update(config)
    
    # Create components
    confidence_model = AdaptiveConfidenceModel()
    trust_calculator = TrustScoreCalculator()
    memory_store = FileMemoryStore({'base_path': './enhanced_feedback_data'})
    
    # Create enhanced engine
    engine = EnhancedFeedbackEngine(
        confidence_model=confidence_model,
        trust_calculator=trust_calculator,
        memory_store=memory_store,
        config=default_config
    )
    
    return engine
